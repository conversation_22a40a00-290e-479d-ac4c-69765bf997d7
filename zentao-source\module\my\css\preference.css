.preference .table-form > tbody > tr > th {font-weight: 400; color: #0B0F18;}
.preference .chosen-container-single .chosen-single > span {color: #313C52;}
.tip {margin-top: 10px;}

.preference > .preference-border {display: flex; padding: 5px 16px; border: 1px solid #EDEEF2;}
.preference-border > .preference-img {flex: 0 0 80px;}
.preference.picker-option-active > .preference-border {box-shadow: 0px 0px 1px 1px #2E7FFF;}
.preference.picker-option-active {background: unset !important;}
.preference.picker-option-active > .preference-border {background: rgba(230,240,255, 0.4) !important;}
.picker-option.picker-option-selected.preference {background: unset !important;}
.picker-option.picker-option-selected.preference > .preference-border {background: rgba(230,240,255, 0.4) !important;}
.preference-text {display: flex; flex-direction: column; justify-content: space-around; padding-left: 5px;}
.preference-text > .title {color: #0B0F18; font-size: 13px;}
.preference-text > .context {color: #838A9D; font-size: 12px; overflow: hidden; white-space: normal;}

.picker-option.picker-option-active,
.picker-single .picker-option.picker-option-selected.picker-option-active.option-ursr,
.picker-option.picker-option-selected.option-ursr {background: unset !important; color: unset;}
.picker-option.picker-option-active > .border,
.picker-single .picker-option.picker-option-selected.picker-option-active.option-ursr > .border,
.picker-single .picker-option.picker-option-selected.picker-option-active.option-ursr > .border {background: rgba(230,240,255, 0.4) !important;}
#pickerDropMenu-pk_URSR > .picker-option-list {display: flex; flex-wrap: wrap;}
.option-ursr {flex: 1 1 50%;}
.option-ursr > .border  {height: 46px; display: flex; padding: 5px 16px; border: 1px solid #EDEEF2; align-items: center;}
.option-ursr > .border > .value {margin-right: 20px; width: 20px; height: 20px; border-radius: 100%; background: rgba(230,240,255, 0.4);}
.option-ursr > .border > .value > p {line-height: 20px; text-align: center;}
