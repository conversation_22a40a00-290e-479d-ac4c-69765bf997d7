import{d as c,r as n,o as _,c as r,w as t,e as l,z as p}from"./index.js";const d=p("span",null,"\u8FD4\u56DE",-1),b=c({__name:"index",setup(u){const o=()=>{var e;(e=window.backBrowse)==null||e.call(window)};return(e,i)=>{const s=n("n-button"),a=n("n-space");return _(),r(a,{class:"header-left-btn",size:25},{default:t(()=>[l(s,{class:"btn-back",ghost:"",onClick:o},{default:t(()=>[d]),_:1})]),_:1})}}});export{b as default};
