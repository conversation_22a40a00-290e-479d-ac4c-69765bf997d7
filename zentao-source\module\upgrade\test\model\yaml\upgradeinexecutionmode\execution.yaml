title: table zt_execution
author: <PERSON>
version: "1.0"
fields:
  - field: id
    range: 101-700
  - field: name
    note: "名称"
    fields:
    - field: name1
      range: 迭代{30}
    - field: name2
      range: 1-10000
  - field: project
    range: 0
  - field: model
    range: []
  - field: type
    range: sprint{30}
  - field: budget
    range: 800000-1:100
  - field: status
    range: closed{10},wait{10},doing{10}
  - field: percent
    range: 0
  - field: milestone
    range: 0
  - field: auth
    range: "extend"
  - field: desc
    range: 1-10000
    prefix: "迭代描述"
  - field: begin
    range: '`2020-01-01`{5},`2020-06-01`{5},`2021-01-01`{5},`2021-06-01`{5},`2022-01-01`{5},`2022-06-01`{5}'
  - field: end
    range: '`2020-05-31`{5},`2020-12-31`{5},`2021-05-31`{5},`2021-12-31`{5},`2022-05-31`{5},`2022-12-31`{5}'
  - field: grade
    range: 1
  - field: parent
    range: 11-40
  - field: path
    fields:
      - field: path1
        prefix: ","
        range: 11-100
      - field: path2
        prefix: ","
        range: 101-700
        postfix: ","
  - field: acl
    range: open{4},private{4}
  - field: openedVersion
    range: "16.5"
  - field: whitelist
    froms:
      - from: common.user.v1.yaml
        use: empty{8}
      - from: common.user.v1.yaml
        use: empty{8}
        prefix: ","
      - from: common.user.v1.yaml
        use: one{8}
        prefix: ","
      - from: common.user.v1.yaml
        use: two{8}
        prefix: ","
      - from: common.user.v1.yaml
        use: three{8}
        prefix: ","
  - field: openedDate
    range: '`2020-01-01`{10},`2021-01-01`{10},`2022-01-01`{5}'
