import{V as a}from"./useSync.hook-14394fcc.js";import{p as n,d as r,f as t,o,h as s,c,L as _,a9 as i}from"./index.js";import"./plugin-37914809.js";import"./icon-bb3d09e7.js";import"./tables_list-f613fa36.js";const p={key:0,class:"go-chart-configurations-setting"},m=r({__name:"index",setup(d){const{targetData:e}=a();return(u,l)=>t(e)?(o(),s("div",p,[(o(),c(_(t(e).chartConfig.conKey),{optionData:t(e).option,componentID:t(e).id},null,8,["optionData","componentID"]))])):i("",!0)}});var h=n(m,[["__scopeId","data-v-04372b24"]]);export{h as default};
