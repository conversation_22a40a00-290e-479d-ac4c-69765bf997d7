#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端字段显示功能
验证AI更新界面是否正确显示所有字段
"""

import asyncio
import aiohttp
import json
import os

# 设置测试模式环境变量
os.environ["PMO_TEST_MODE"] = "true"

BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"
TEST_PROJECT_CODE = "C202500012"

async def test_frontend_fields():
    """测试前端字段显示"""
    async with aiohttp.ClientSession() as session:
        print("🎯 测试前端AI更新字段显示功能")
        print("=" * 60)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test_token"
        }
        
        # 1. 触发AI更新分析
        print("📤 触发AI更新分析...")
        async with session.post(f"{BASE_URL}/api/v1/project/{TEST_PROJECT_CODE}/ai-update", 
                               headers=headers) as response:
            if response.status == 200:
                result = await response.json()
                print("✅ AI更新分析成功")
                
                if result.get("code") == 200:
                    data = result.get("data", {})
                    steps = data.get("steps", {})
                    
                    # 检查步骤6的数据
                    step6 = steps.get("step6_user_confirm", {})
                    if step6.get("status") == "ready":
                        step6_data = step6.get("data", {})
                        ai_response = step6_data.get("ai_response_markdown", "")
                        
                        print(f"\n📋 AI分析结果:")
                        print(f"  - AI回答长度: {len(ai_response):,} 字符")
                        
                        # 解析AI建议的字段
                        ai_fields = parse_ai_response(ai_response)
                        print(f"  - AI建议字段数: {len(ai_fields)}")
                        
                        # 显示AI建议的字段
                        print(f"\n🤖 AI建议的字段:")
                        for i, field in enumerate(ai_fields, 1):
                            print(f"  {i:2d}. {field['field_key']:30} - {field['field_label']}")
                            print(f"      当前值: {field['current_value'][:50]}...")
                            print(f"      建议值: {field['suggested_value'][:50]}...")
                            print(f"      原因: {field['reason'][:80]}...")
                            print()
                        
                        # 验证前端应该显示的所有字段
                        print(f"\n📊 前端应该显示的字段验证:")
                        expected_fields = get_expected_fields()
                        print(f"  - 预期显示字段总数: {len(expected_fields)}")
                        print(f"  - AI建议字段数: {len(ai_fields)}")
                        print(f"  - 其他可编辑字段数: {len(expected_fields) - len(ai_fields)}")
                        
                        print(f"\n✅ 前端界面应该显示:")
                        print(f"  1. AI建议的 {len(ai_fields)} 个字段（默认选中，带'AI建议'标签）")
                        print(f"  2. 其他 {len(expected_fields) - len(ai_fields)} 个可编辑字段（默认未选中）")
                        print(f"  3. 每个字段都有对应的编辑控件（日期选择器、下拉框、文本框等）")
                        print(f"  4. AI建议的字段显示建议原因")
                        
                        return True
                    else:
                        print("❌ 步骤6未准备就绪")
                        return False
                else:
                    print(f"❌ AI更新失败: {result.get('message')}")
                    return False
            else:
                print(f"❌ 请求失败: HTTP {response.status}")
                return False

def parse_ai_response(ai_response):
    """解析AI回答中的字段建议"""
    fields = []
    lines = ai_response.split('\n')
    
    for line in lines:
        if line.startswith('|') and line.endswith('|'):
            cells = line.split('|')[1:-1]  # 去掉首尾空元素
            cells = [cell.strip() for cell in cells]
            
            if len(cells) >= 4 and cells[0] and cells[1] and cells[2]:
                # 跳过表头和分隔行
                if not cells[0].startswith('-') and '字段名称' not in cells[0]:
                    fields.append({
                        'field_key': cells[0],
                        'field_label': get_field_label(cells[0]),
                        'current_value': cells[1],
                        'suggested_value': cells[2],
                        'reason': cells[3] if len(cells) > 3 else ''
                    })
    
    return fields

def get_field_label(field_key):
    """获取字段标签"""
    field_labels = {
        'project_name': '项目名称',
        'project_establishment_name': '项目立项名称',
        'current_progress': '当前进度',
        'responsible_person': '责任人',
        'responsible_department': '负责部门',
        'construction_content': '建设内容',
        'project_overview': '项目概述',
        'start_time': '开始时间',
        'acceptance_time': '验收时间',
        'project_establishment_time': '项目立项时间',
        'project_implementation_time': '项目实施时间',
        'annual_investment_plan': '年度投资计划',
        'budget': '预算',
        'project_planned_total_investment': '项目计划总投资',
        'investment_type': '投资类型',
        'is_hardware': '是否硬件项目',
        'next_steps': '下一步工作',
        'developer_1': '开发人员1',
        'itbp_team_member': 'ITBP团队成员',
        'remarks': '备注'
    }
    return field_labels.get(field_key, field_key)

def get_expected_fields():
    """获取前端应该显示的所有字段"""
    return [
        'project_name', 'project_establishment_name', 'investment_entity',
        'construction_content', 'project_overview', 'current_progress',
        'next_steps', 'issues_to_be_coordinated_resolved', 'responsible_person',
        'responsible_department', 'start_time', 'acceptance_time',
        'project_establishment_time', 'project_implementation_time',
        'annual_investment_plan', 'budget', 'project_planned_total_investment',
        'project_category', 'investment_type', 'is_hardware',
        'is_non_indigenous_innovation', 'developer_1', 'developer_2',
        'itbp_team_member', 'remarks', 'business_number'
    ]

async def main():
    """主函数"""
    success = await test_frontend_fields()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 前端字段显示功能验证成功！")
        print("\n📋 使用说明:")
        print("1. 打开浏览器访问: http://localhost:3000")
        print("2. 进入项目管理页面")
        print("3. 点击项目的'AI更新'按钮")
        print("4. 在步骤6中查看所有可编辑字段")
        print("5. AI建议的字段会有'AI建议'标签并默认选中")
        print("6. 可以选择任意字段进行编辑")
        print("7. 根据字段类型提供不同的编辑控件")
    else:
        print("\n❌ 前端字段显示功能验证失败")

if __name__ == "__main__":
    asyncio.run(main())
