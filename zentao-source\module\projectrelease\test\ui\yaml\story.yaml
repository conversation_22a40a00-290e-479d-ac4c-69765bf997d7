title: table zt_story
desc: "需求"
author: <PERSON>
version: "1.0"
fields:
  - field: product
    note: "所属产品"
    range: 1
  - field: grade
    note: "需求层级"
    range: 1
    format: ""
  - field: title
    note: "需求名称"
  - field: title
    range: 需求001,需求002,需求003,需求004,需求005
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "需求类型"
    range: story
  - field: pri
    note: "优先级"
    range: 1-4
  - field: status
    note: "当前状态"
    range: active
    prefix: ""
    postfix: ""
    loop: 0
  - field: stage
    note: "所处阶段"
    range: "projected,[],developing,[],developed,[],testing,[],tested,[]"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
