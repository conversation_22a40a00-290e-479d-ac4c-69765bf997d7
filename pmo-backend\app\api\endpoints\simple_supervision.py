#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的督办管理API
参考人员管理页面的实现方式
"""

from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import pymysql
import os
from dotenv import load_dotenv
import logging
import re
import pandas as pd
import io
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

# 加载环境变量
load_dotenv()

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'database': os.getenv('DB_NAME'),
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(**DB_CONFIG)

def execute_query(query: str, params: tuple = None, fetch_one: bool = False):
    """执行数据库查询"""
    connection = get_db_connection()
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            cursor.execute(query, params)
            if fetch_one:
                return cursor.fetchone()
            return cursor.fetchall()
    finally:
        connection.close()

def execute_update(query: str, params: tuple = None):
    """执行数据库更新操作"""
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            cursor.execute(query, params)
            connection.commit()
            return cursor.rowcount
    finally:
        connection.close()

# ==================== 数据模型 ====================

class SupervisionItem(BaseModel):
    """督办事项模型"""
    id: Optional[int] = None
    sequence_number: int
    work_dimension: str
    work_theme: str
    supervision_source: str
    work_content: str
    is_annual_assessment: str = '否'
    completion_deadline: str
    overall_progress: str = 'X 未启动'
    财险_status: str = 'X'
    寿险_status: str = 'X'
    金租_status: str = 'X'
    资管_status: str = 'X'
    广租_status: str = 'X'
    通盛_status: str = 'X'
    担保_status: str = 'X'
    小贷_status: str = 'X'
    保理_status: str = 'X'
    不动产_status: str = 'X'
    征信_status: str = 'X'
    金服_status: str = 'X'
    本部_status: str = 'X'

class ProgressDetail(BaseModel):
    """进度详情模型"""
    work_theme: str
    company_name: str
    progress_description: Optional[str] = None
    existing_problems: Optional[str] = None
    next_plan: Optional[str] = None

class StatusUpdate(BaseModel):
    """状态更新模型"""
    work_theme: str
    company_name: str
    status: str

class ProgressUpdate(BaseModel):
    """进度更新模型（包含状态和详细信息）"""
    work_theme: str
    company_name: str
    status: str
    progress_description: Optional[str] = None
    existing_problems: Optional[str] = None
    next_plan: Optional[str] = None

class CompanyColumn(BaseModel):
    """公司列管理模型"""
    company_name: str
    display_order: Optional[int] = 0

# ==================== API接口 ====================

@router.get("/items")
async def get_supervision_items():
    """获取督办事项列表，每次都重新计算整体进度"""
    try:
        query = """
        SELECT * FROM supervision_items
        ORDER BY sequence_number ASC
        """
        items = execute_query(query)

        # 获取所有公司列表
        companies = get_all_companies()

        # 为每个督办事项重新计算整体进度
        updated_items = []
        for item in items:
            # 动态构建公司状态字典
            company_statuses = {}
            for company in companies:
                status_field = f"{company}_status"
                company_statuses[company] = item.get(status_field, 'X')

            # 计算新的整体进度
            calculated_progress = calculate_overall_progress(company_statuses)

            # 如果计算出的进度与数据库中的不同，更新数据库
            if calculated_progress != item.get('overall_progress'):
                update_query = """
                UPDATE supervision_items
                SET overall_progress = %s, updated_at = CURRENT_TIMESTAMP
                WHERE work_theme = %s
                """
                execute_update(update_query, (calculated_progress, item['work_theme']))
                item['overall_progress'] = calculated_progress

            updated_items.append(item)

        return {
            "success": True,
            "message": "获取督办事项成功",
            "data": updated_items
        }

    except Exception as e:
        logger.error(f"获取督办事项失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取督办事项失败: {str(e)}")

@router.post("/items")
async def create_supervision_item(item: SupervisionItem):
    """创建督办事项"""
    try:
        query = """
        INSERT INTO supervision_items 
        (sequence_number, work_dimension, work_theme, supervision_source, work_content,
         is_annual_assessment, completion_deadline, overall_progress,
         财险_status, 寿险_status, 金租_status, 资管_status, 广租_status, 通盛_status,
         担保_status, 小贷_status, 保理_status, 不动产_status, 征信_status, 金服_status, 本部_status)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        params = (
            item.sequence_number, item.work_dimension, item.work_theme, item.supervision_source,
            item.work_content, item.is_annual_assessment, item.completion_deadline, item.overall_progress,
            item.财险_status, item.寿险_status, item.金租_status, item.资管_status, item.广租_status, item.通盛_status,
            item.担保_status, item.小贷_status, item.保理_status, item.不动产_status, item.征信_status, item.金服_status, item.本部_status
        )
        
        execute_update(query, params)
        
        return {
            "success": True,
            "message": "创建督办事项成功"
        }
        
    except Exception as e:
        logger.error(f"创建督办事项失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建督办事项失败: {str(e)}")

@router.put("/items/{item_id}")
async def update_supervision_item(item_id: int, item: SupervisionItem):
    """更新督办事项"""
    try:
        query = """
        UPDATE supervision_items SET
        sequence_number = %s, work_dimension = %s, work_theme = %s, supervision_source = %s,
        work_content = %s, is_annual_assessment = %s, completion_deadline = %s, overall_progress = %s,
        财险_status = %s, 寿险_status = %s, 金租_status = %s, 资管_status = %s, 广租_status = %s, 通盛_status = %s,
        担保_status = %s, 小贷_status = %s, 保理_status = %s, 不动产_status = %s, 征信_status = %s, 金服_status = %s, 本部_status = %s
        WHERE id = %s
        """
        
        params = (
            item.sequence_number, item.work_dimension, item.work_theme, item.supervision_source,
            item.work_content, item.is_annual_assessment, item.completion_deadline, item.overall_progress,
            item.财险_status, item.寿险_status, item.金租_status, item.资管_status, item.广租_status, item.通盛_status,
            item.担保_status, item.小贷_status, item.保理_status, item.不动产_status, item.征信_status, item.金服_status, item.本部_status,
            item_id
        )
        
        execute_update(query, params)
        
        return {
            "success": True,
            "message": "更新督办事项成功"
        }
        
    except Exception as e:
        logger.error(f"更新督办事项失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新督办事项失败: {str(e)}")

@router.delete("/items/{item_id}")
async def delete_supervision_item(item_id: int):
    """删除督办事项"""
    try:
        # 先获取工作主题
        item = execute_query("SELECT work_theme FROM supervision_items WHERE id = %s", (item_id,), fetch_one=True)
        if not item:
            raise HTTPException(status_code=404, detail="督办事项不存在")
        
        work_theme = item['work_theme']
        
        # 删除相关的详情记录
        execute_update("DELETE FROM company_progress_details WHERE work_theme = %s", (work_theme,))
        
        # 删除主记录
        execute_update("DELETE FROM supervision_items WHERE id = %s", (item_id,))
        
        return {
            "success": True,
            "message": "删除督办事项成功"
        }
        
    except Exception as e:
        logger.error(f"删除督办事项失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除督办事项失败: {str(e)}")

@router.put("/status")
async def update_company_status(status_update: StatusUpdate):
    """更新公司状态"""
    try:
        # 更新主表中的状态字段
        status_field = f"{status_update.company_name}_status"
        query = f"UPDATE supervision_items SET {status_field} = %s WHERE work_theme = %s"

        execute_update(query, (status_update.status, status_update.work_theme))

        # 重新计算并更新整体进度
        new_overall_progress = update_item_overall_progress(status_update.work_theme)

        return {
            "success": True,
            "message": "状态更新成功",
            "new_overall_progress": new_overall_progress
        }

    except Exception as e:
        logger.error(f"更新状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新状态失败: {str(e)}")

@router.get("/progress-detail/{work_theme}/{company_name}")
async def get_progress_detail(work_theme: str, company_name: str):
    """获取进度详情"""
    try:
        query = """
        SELECT progress_description, existing_problems, next_plan, updated_at
        FROM company_progress_details
        WHERE work_theme = %s AND company_name = %s
        """
        
        result = execute_query(query, (work_theme, company_name), fetch_one=True)
        
        if result:
            return {
                "success": True,
                "message": "获取进度详情成功",
                "data": result
            }
        else:
            return {
                "success": True,
                "message": "暂无进度记录",
                "data": {
                    "progress_description": "",
                    "existing_problems": "",
                    "next_plan": "",
                    "updated_at": None
                }
            }
            
    except Exception as e:
        logger.error(f"获取进度详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取进度详情失败: {str(e)}")

@router.put("/progress-detail")
async def update_progress_detail(detail: ProgressDetail):
    """更新进度详情"""
    try:
        query = """
        INSERT INTO company_progress_details 
        (work_theme, company_name, progress_description, existing_problems, next_plan)
        VALUES (%s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        progress_description = VALUES(progress_description),
        existing_problems = VALUES(existing_problems),
        next_plan = VALUES(next_plan)
        """
        
        params = (
            detail.work_theme, detail.company_name, 
            detail.progress_description, detail.existing_problems, detail.next_plan
        )
        
        execute_update(query, params)
        
        return {
            "success": True,
            "message": "更新进度详情成功"
        }

    except Exception as e:
        logger.error(f"更新进度详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新进度详情失败: {str(e)}")

@router.put("/progress")
async def update_progress_with_details(progress_update: ProgressUpdate):
    """更新进度状态和详细信息（双击编辑时调用）"""
    try:
        # 1. 更新主表中的状态字段
        status_field = f"{progress_update.company_name}_status"
        main_query = f"UPDATE supervision_items SET {status_field} = %s WHERE work_theme = %s"
        execute_update(main_query, (progress_update.status, progress_update.work_theme))

        # 2. 更新或插入详情表中的进度信息
        detail_query = """
        INSERT INTO company_progress_details
        (work_theme, company_name, progress_description, existing_problems, next_plan)
        VALUES (%s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        progress_description = VALUES(progress_description),
        existing_problems = VALUES(existing_problems),
        next_plan = VALUES(next_plan),
        updated_at = CURRENT_TIMESTAMP
        """

        execute_update(detail_query, (
            progress_update.work_theme,
            progress_update.company_name,
            progress_update.progress_description,
            progress_update.existing_problems,
            progress_update.next_plan
        ))

        # 3. 重新计算并更新整体进度
        update_item_overall_progress(progress_update.work_theme)

        return {
            "success": True,
            "message": "更新进度成功"
        }

    except Exception as e:
        logger.error(f"更新进度失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新进度失败: {str(e)}")

@router.get("/export")
async def export_to_excel():
    """导出督办事项到Excel"""
    try:
        # 获取所有督办事项
        query = """
        SELECT * FROM supervision_items
        ORDER BY sequence_number ASC
        """
        items = execute_query(query)

        if not items:
            raise HTTPException(status_code=404, detail="没有数据可导出")

        # 转换为DataFrame
        df = pd.DataFrame(items)

        # 重命名列为中文
        column_mapping = {
            'id': 'ID',
            'sequence_number': '序号',
            'work_dimension': '工作维度',
            'work_theme': '工作主题',
            'supervision_source': '督办来源',
            'work_content': '工作内容',
            'is_annual_assessment': '是否年度考核',
            'completion_deadline': '完成时限',
            'overall_progress': '整体进度',
            '财险_status': '财险',
            '寿险_status': '寿险',
            '金租_status': '金租',
            '资管_status': '资管',
            '广租_status': '广租',
            '通盛_status': '通盛',
            '担保_status': '担保',
            '小贷_status': '小贷',
            '保理_status': '保理',
            '不动产_status': '不动产',
            '征信_status': '征信',
            '金服_status': '金服',
            '本部_status': '本部',
            'created_at': '创建时间',
            'updated_at': '更新时间'
        }

        df = df.rename(columns=column_mapping)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='督办事项', index=False)

            # 获取工作表并设置列宽
            worksheet = writer.sheets['督办事项']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        output.seek(0)

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"supervision_items_{timestamp}.xlsx"

        return StreamingResponse(
            io.BytesIO(output.read()),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        logger.error(f"导出Excel失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出Excel失败: {str(e)}")

@router.post("/import")
async def import_from_excel(file: UploadFile = File(...)):
    """从Excel导入督办事项（覆盖原有数据）"""
    try:
        # 检查文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(status_code=400, detail="请上传Excel文件(.xlsx或.xls)")

        # 读取Excel文件
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))

        logger.info(f"读取Excel文件成功，共 {len(df)} 行数据")
        logger.info(f"Excel列名: {list(df.columns)}")

        # 列名映射（中文到英文）
        reverse_column_mapping = {
            'ID': 'id',
            '序号': 'sequence_number',
            '工作维度': 'work_dimension',
            '工作主题': 'work_theme',
            '督办来源': 'supervision_source',
            '工作内容': 'work_content',
            '是否年度考核': 'is_annual_assessment',
            '完成时限': 'completion_deadline',
            '整体进度': 'overall_progress',
            '财险': '财险_status',
            '寿险': '寿险_status',
            '金租': '金租_status',
            '资管': '资管_status',
            '广租': '广租_status',
            '通盛': '通盛_status',
            '担保': '担保_status',
            '小贷': '小贷_status',
            '保理': '保理_status',
            '不动产': '不动产_status',
            '征信': '征信_status',
            '金服': '金服_status',
            '本部': '本部_status',
            '创建时间': 'created_at',
            '更新时间': 'updated_at'
        }

        # 重命名列
        df = df.rename(columns=reverse_column_mapping)

        # 数据清理和验证
        required_columns = ['sequence_number', 'work_dimension', 'work_theme', 'supervision_source', 'work_content', 'completion_deadline']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise HTTPException(status_code=400, detail=f"Excel文件缺少必要列: {missing_columns}")

        # 填充默认值
        df = df.fillna({
            'is_annual_assessment': '否',
            'overall_progress': 'X 未启动',
            '财险_status': 'X',
            '寿险_status': 'X',
            '金租_status': 'X',
            '资管_status': 'X',
            '广租_status': 'X',
            '通盛_status': 'X',
            '担保_status': 'X',
            '小贷_status': 'X',
            '保理_status': 'X',
            '不动产_status': 'X',
            '征信_status': 'X',
            '金服_status': 'X',
            '本部_status': 'X'
        })

        # 开始数据库操作
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 清空现有数据
                logger.info("清空现有数据...")
                cursor.execute("DELETE FROM company_progress_details")
                cursor.execute("DELETE FROM supervision_items")

                # 插入新数据
                logger.info(f"插入 {len(df)} 条新数据...")
                for index, row in df.iterrows():
                    insert_sql = """
                    INSERT INTO supervision_items
                    (sequence_number, work_dimension, work_theme, supervision_source, work_content,
                     is_annual_assessment, completion_deadline, overall_progress,
                     财险_status, 寿险_status, 金租_status, 资管_status, 广租_status, 通盛_status,
                     担保_status, 小贷_status, 保理_status, 不动产_status, 征信_status, 金服_status, 本部_status)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """

                    params = (
                        int(row['sequence_number']) if pd.notna(row['sequence_number']) else index + 1,
                        str(row['work_dimension']),
                        str(row['work_theme']),
                        str(row['supervision_source']),
                        str(row['work_content']),
                        str(row['is_annual_assessment']),
                        str(row['completion_deadline']),
                        str(row['overall_progress']),
                        str(row['财险_status']),
                        str(row['寿险_status']),
                        str(row['金租_status']),
                        str(row['资管_status']),
                        str(row['广租_status']),
                        str(row['通盛_status']),
                        str(row['担保_status']),
                        str(row['小贷_status']),
                        str(row['保理_status']),
                        str(row['不动产_status']),
                        str(row['征信_status']),
                        str(row['金服_status']),
                        str(row['本部_status'])
                    )

                    cursor.execute(insert_sql, params)

                connection.commit()
                logger.info("数据导入成功")

        finally:
            connection.close()

        return {
            "success": True,
            "message": f"成功导入 {len(df)} 条督办事项数据"
        }

    except Exception as e:
        logger.error(f"导入Excel失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导入Excel失败: {str(e)}")

def excel_date_to_python_date(excel_date):
    """
    将Excel日期序列号转换为Python日期对象
    """
    try:
        if isinstance(excel_date, str):
            # 如果已经是YYYY-MM-DD格式，直接返回
            if re.match(r'^\d{4}-\d{2}-\d{2}$', excel_date):
                return excel_date
            # 尝试转换为数字
            excel_date = float(excel_date)

        # Excel的基准日期是1899年12月30日
        from datetime import datetime, timedelta
        base_date = datetime(1899, 12, 30)
        target_date = base_date + timedelta(days=excel_date)

        return target_date.strftime('%Y-%m-%d')
    except:
        return None

@router.post("/import-data")
async def import_data_from_frontend(data: Dict[str, Any]):
    """从前端导入解析后的数据"""
    try:
        items = data.get('items', [])
        if not items:
            raise HTTPException(status_code=400, detail="没有数据可导入")

        logger.info(f"接收到前端数据，共 {len(items)} 条")

        # 开始数据库操作
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 清空现有数据
                logger.info("清空现有数据...")
                cursor.execute("DELETE FROM company_progress_details")
                cursor.execute("DELETE FROM supervision_items")

                # 插入新数据
                logger.info(f"插入 {len(items)} 条新数据...")
                for item in items:
                    # 转换完成时限为日期格式
                    completion_deadline = item.get('completion_deadline', '')
                    if completion_deadline:
                        converted_date = excel_date_to_python_date(completion_deadline)
                        if converted_date:
                            completion_deadline = converted_date
                        else:
                            logger.warning(f"无法转换日期: {completion_deadline}")
                            completion_deadline = None
                    else:
                        completion_deadline = None

                    insert_sql = """
                    INSERT INTO supervision_items
                    (sequence_number, work_dimension, work_theme, supervision_source, work_content,
                     is_annual_assessment, completion_deadline, overall_progress,
                     财险_status, 寿险_status, 金租_status, 资管_status, 广租_status, 通盛_status,
                     担保_status, 小贷_status, 保理_status, 不动产_status, 征信_status, 金服_status, 本部_status)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """

                    params = (
                        int(item.get('sequence_number', 0)),
                        str(item.get('work_dimension', '')),
                        str(item.get('work_theme', '')),
                        str(item.get('supervision_source', '')),
                        str(item.get('work_content', '')),
                        str(item.get('is_annual_assessment', '否')),
                        completion_deadline,
                        str(item.get('overall_progress', 'X 未启动')),
                        str(item.get('财险_status', 'X')),
                        str(item.get('寿险_status', 'X')),
                        str(item.get('金租_status', 'X')),
                        str(item.get('资管_status', 'X')),
                        str(item.get('广租_status', 'X')),
                        str(item.get('通盛_status', 'X')),
                        str(item.get('担保_status', 'X')),
                        str(item.get('小贷_status', 'X')),
                        str(item.get('保理_status', 'X')),
                        str(item.get('不动产_status', 'X')),
                        str(item.get('征信_status', 'X')),
                        str(item.get('金服_status', 'X')),
                        str(item.get('本部_status', 'X'))
                    )

                    cursor.execute(insert_sql, params)

                connection.commit()
                logger.info("数据导入成功")

        finally:
            connection.close()

        return {
            "success": True,
            "message": f"成功导入 {len(items)} 条督办事项数据"
        }

    except Exception as e:
        logger.error(f"导入数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导入数据失败: {str(e)}")

def get_all_companies():
    """获取所有公司名称列表"""
    try:
        query = """
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'supervision_items'
        AND COLUMN_NAME LIKE '%_status'
        ORDER BY ORDINAL_POSITION
        """

        result = execute_query(query)
        companies = []

        for row in result:
            column_name = row['COLUMN_NAME']
            company_name = column_name.replace('_status', '')
            companies.append(company_name)

        return companies
    except Exception as e:
        logger.error(f"获取公司列表失败: {str(e)}")
        return []

def calculate_overall_progress(company_statuses: Dict[str, str]) -> str:
    """
    根据各公司状态计算整体进度
    参考原督办管理页面的计算逻辑
    """
    if not company_statuses:
        return 'X 未启动'

    # 获取所有有效状态（排除"不需要执行"）
    valid_statuses = [status for status in company_statuses.values() if status != '—']

    if not valid_statuses:
        return '— 不需要执行'

    # 统计各状态数量
    status_counts = {
        '√': valid_statuses.count('√'),  # 已完成
        'O': valid_statuses.count('O'),  # 进行中
        '！': valid_statuses.count('！'), # 逾期
        'X': valid_statuses.count('X')   # 未启动
    }

    total_valid = len(valid_statuses)

    # 判定整体状态（按优先级）
    if status_counts['！'] > 0:
        return '！逾期'
    elif status_counts['√'] == total_valid:
        return '√ 已完成'
    elif status_counts['O'] > 0:
        return 'O 进行中'
    else:
        return 'X 未启动'

def update_item_overall_progress(work_theme: str):
    """更新督办事项的整体进度"""
    try:
        # 获取所有公司列表
        companies = get_all_companies()

        # 动态构建查询字段
        status_fields = [f"{company}_status" for company in companies]
        query = f"""
        SELECT {', '.join(status_fields)}
        FROM supervision_items
        WHERE work_theme = %s
        """
        result = execute_query(query, (work_theme,), fetch_one=True)

        if result:
            # 动态构建公司状态字典
            company_statuses = {}
            for company in companies:
                status_field = f"{company}_status"
                company_statuses[company] = result.get(status_field) or 'X'

            # 计算新的整体进度
            new_overall_progress = calculate_overall_progress(company_statuses)

            # 更新数据库
            update_query = """
            UPDATE supervision_items
            SET overall_progress = %s, updated_at = CURRENT_TIMESTAMP
            WHERE work_theme = %s
            """
            execute_update(update_query, (new_overall_progress, work_theme))

            logger.info(f"更新督办事项 '{work_theme}' 的整体进度为: {new_overall_progress}")
            return new_overall_progress

        return None

    except Exception as e:
        logger.error(f"更新整体进度失败: {str(e)}")
        return None

# ==================== 公司列管理API ====================

@router.get("/companies")
async def get_companies():
    """获取所有公司列表"""
    try:
        # 从数据库表结构中获取公司列信息
        query = """
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'supervision_items'
        AND COLUMN_NAME LIKE '%_status'
        ORDER BY ORDINAL_POSITION
        """

        result = execute_query(query)
        companies = []

        for i, row in enumerate(result):
            column_name = row['COLUMN_NAME']
            company_name = column_name.replace('_status', '')
            companies.append({
                'company_name': company_name,
                'display_order': i + 1,
                'column_name': column_name
            })

        return {
            "success": True,
            "data": companies,
            "message": f"获取到 {len(companies)} 个公司"
        }

    except Exception as e:
        logger.error(f"获取公司列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取公司列表失败: {str(e)}")

@router.post("/companies")
async def add_company(company: CompanyColumn):
    """添加新公司列"""
    try:
        company_name = company.company_name.strip()
        if not company_name:
            raise HTTPException(status_code=400, detail="公司名称不能为空")

        # 检查公司名称是否已存在
        column_name = f"{company_name}_status"
        check_query = """
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'supervision_items'
        AND COLUMN_NAME = %s
        """

        existing = execute_query(check_query, (column_name,))
        if existing:
            raise HTTPException(status_code=400, detail=f"公司 '{company_name}' 已存在")

        # 添加新的状态列到数据库表
        alter_query = f"""
        ALTER TABLE supervision_items
        ADD COLUMN {column_name} VARCHAR(10) DEFAULT 'X' COMMENT '{company_name}状态'
        """

        execute_update(alter_query)

        logger.info(f"成功添加公司列: {company_name}")

        return {
            "success": True,
            "message": f"成功添加公司 '{company_name}'"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加公司失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加公司失败: {str(e)}")

@router.delete("/companies/{company_name}")
async def delete_company(company_name: str):
    """删除公司列"""
    try:
        company_name = company_name.strip()
        if not company_name:
            raise HTTPException(status_code=400, detail="公司名称不能为空")

        # 检查公司是否存在
        column_name = f"{company_name}_status"
        check_query = """
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'supervision_items'
        AND COLUMN_NAME = %s
        """

        existing = execute_query(check_query, (column_name,))
        if not existing:
            raise HTTPException(status_code=404, detail=f"公司 '{company_name}' 不存在")

        # 从数据库表中删除列
        alter_query = f"ALTER TABLE supervision_items DROP COLUMN {column_name}"
        execute_update(alter_query)

        logger.info(f"成功删除公司列: {company_name}")

        return {
            "success": True,
            "message": f"成功删除公司 '{company_name}'"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除公司失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除公司失败: {str(e)}")
