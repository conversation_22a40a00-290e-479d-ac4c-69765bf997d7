<?php
$config->upgrade = new stdclass();
$config->upgrade->maxVersion = array();
$config->upgrade->maxVersion['max2_0_rc1']     = '15_0_rc3';
$config->upgrade->maxVersion['max2_0']         = '15_0';
$config->upgrade->maxVersion['max2_1']         = '15_3';
$config->upgrade->maxVersion['max2_2']         = '15_5';
$config->upgrade->maxVersion['max2_3']         = '15_7';
$config->upgrade->maxVersion['max2_3_1']       = '15_7_1';
$config->upgrade->maxVersion['max2_4_beta1']   = '16_0_beta1';
$config->upgrade->maxVersion['max2_4']         = '16_0';
$config->upgrade->maxVersion['max2_5']         = '16_1';
$config->upgrade->maxVersion['max2_6']         = '16_2';
$config->upgrade->maxVersion['max2_7']         = '16_3';
$config->upgrade->maxVersion['max2_8']         = '16_4';
$config->upgrade->maxVersion['max3_0_beta1']   = '16_5_beta1';
$config->upgrade->maxVersion['max3_0']         = '16_5';
$config->upgrade->maxVersion['max3_1_beta1']   = '17_0_beta1';
$config->upgrade->maxVersion['max3_1_beta2']   = '17_0_beta2';
$config->upgrade->maxVersion['max3_1']         = '17_0';
$config->upgrade->maxVersion['max3_2']         = '17_1';
$config->upgrade->maxVersion['max3_3']         = '17_2';
$config->upgrade->maxVersion['max3_4']         = '17_3';
$config->upgrade->maxVersion['max3_5']         = '17_4';
$config->upgrade->maxVersion['max3_6']         = '17_5';
$config->upgrade->maxVersion['max3_6_1']       = '17_6';
$config->upgrade->maxVersion['max3_6_2']       = '17_6_1';
$config->upgrade->maxVersion['max3_6_3']       = '17_6_2';
$config->upgrade->maxVersion['max3_7']         = '17_7';
$config->upgrade->maxVersion['max3_8']         = '17_8';
$config->upgrade->maxVersion['max4_0_beta1']   = '18_0_beta1';
$config->upgrade->maxVersion['max4_0_beta2']   = '18_0_beta2';
$config->upgrade->maxVersion['max4_0_beta3']   = '18_0_beta3';
$config->upgrade->maxVersion['max4_0']         = '18_0';
$config->upgrade->maxVersion['max4_1']         = '18_1';
$config->upgrade->maxVersion['max4_2']         = '18_2';
$config->upgrade->maxVersion['max4_3']         = '18_3';
$config->upgrade->maxVersion['max4_4_alpha1']  = '18_4_alpha1';
$config->upgrade->maxVersion['max4_4_beta1']   = '18_4_beta1';
$config->upgrade->maxVersion['max4_4']         = '18_4';
$config->upgrade->maxVersion['max4_5']         = '18_5';
$config->upgrade->maxVersion['max4_6']         = '18_6';
$config->upgrade->maxVersion['max4_7']         = '18_7';
$config->upgrade->maxVersion['max4_8']         = '18_8';
$config->upgrade->maxVersion['max4_9']         = '18_9';
$config->upgrade->maxVersion['max4_10']        = '18_10';
$config->upgrade->maxVersion['max4_10_1']      = '18_10_1';
$config->upgrade->maxVersion['max4_11']        = '18_11';
$config->upgrade->maxVersion['max4_12']        = '18_12';
$config->upgrade->maxVersion['max5_0_0_beta1'] = '20_1_0';
$config->upgrade->maxVersion['max5_0_0']       = '20_1_1';
$config->upgrade->maxVersion['max5_1_0']       = '20_2_0';
$config->upgrade->maxVersion['max5_2_0']       = '20_3_0';
$config->upgrade->maxVersion['max5_3']         = '20_4';
$config->upgrade->maxVersion['max4_13']        = '18_13';
$config->upgrade->maxVersion['max5_4']         = '20_5';
$config->upgrade->maxVersion['max5_5']         = '20_6';
$config->upgrade->maxVersion['max5_6']         = '20_7';
$config->upgrade->maxVersion['max5_6_1']       = '20_7_1';
$config->upgrade->maxVersion['max5_7']         = '20_8';
$config->upgrade->maxVersion['max6_0']         = '21_0';
$config->upgrade->maxVersion['max6_1']         = '21_1';
$config->upgrade->maxVersion['max6_2']         = '21_2';
$config->upgrade->maxVersion['max6_3']         = '21_3';
$config->upgrade->maxVersion['max6_4']         = '21_4';
$config->upgrade->maxVersion['max6_5']         = '21_5';
$config->upgrade->maxVersion['max6_6_beta']    = '21_6_beta';
$config->upgrade->maxVersion['max6_6']         = '21_6';
$config->upgrade->maxVersion['max6_6_1']       = '21_6_1';
$config->upgrade->maxVersion['max6_7']         = '21_7';
$config->upgrade->maxVersion['max7_0']         = '21_7_1'; // max insert position.

$config->upgrade->bizVersion = array();
$config->upgrade->bizVersion['biz1_0']          = '9_5_1';
$config->upgrade->bizVersion['biz1_1']          = '9_6_3';
$config->upgrade->bizVersion['biz1_1_1']        = '9_8';
$config->upgrade->bizVersion['biz1_1_2']        = '9_8_1';
$config->upgrade->bizVersion['biz1_1_3']        = '9_8_2';
$config->upgrade->bizVersion['biz1_1_4']        = '9_8_3';
$config->upgrade->bizVersion['biz2_0_beta']     = '10_0';
$config->upgrade->bizVersion['biz2_1']          = '10_3';
$config->upgrade->bizVersion['biz2_2']          = '10_4';
$config->upgrade->bizVersion['biz2_3']          = '10_5';
$config->upgrade->bizVersion['biz2_3_1']        = '10_6';
$config->upgrade->bizVersion['biz2_4']          = '10_6';
$config->upgrade->bizVersion['biz3_0']          = '11_1';
$config->upgrade->bizVersion['biz3_1']          = '11_2';
$config->upgrade->bizVersion['biz3_2']          = '11_3';
$config->upgrade->bizVersion['biz3_2_1']        = '11_4_1';
$config->upgrade->bizVersion['biz3_3']          = '11_5';
$config->upgrade->bizVersion['biz3_4']          = '11_6';
$config->upgrade->bizVersion['biz3_5_alpha']    = '11_6';
$config->upgrade->bizVersion['biz3_5_beta']     = '11_6_1';
$config->upgrade->bizVersion['biz3_5']          = '11_6_5';
$config->upgrade->bizVersion['biz3_5_1']        = '11_7';
$config->upgrade->bizVersion['biz3_6']          = '12_0';
$config->upgrade->bizVersion['biz3_6_1']        = '12_0_1';
$config->upgrade->bizVersion['biz3_7']          = '12_2';
$config->upgrade->bizVersion['biz3_7_1']        = '12_3_2';
$config->upgrade->bizVersion['biz3_7_2']        = '12_3_3';
$config->upgrade->bizVersion['biz4_0']          = '12_4';
$config->upgrade->bizVersion['biz4_0_1']        = '12_4_1';
$config->upgrade->bizVersion['biz4_0_2']        = '12_4_2';
$config->upgrade->bizVersion['biz4_0_3']        = '12_4_3';
$config->upgrade->bizVersion['biz4_0_4']        = '12_4_4';
$config->upgrade->bizVersion['biz4_1']          = '12_5';
$config->upgrade->bizVersion['biz4_1_1']        = '12_5_1';
$config->upgrade->bizVersion['biz4_1_2']        = '12_5_2';
$config->upgrade->bizVersion['biz4_1_3']        = '12_5_3';
$config->upgrade->bizVersion['biz5_0_rc1']      = '15_0_rc3';
$config->upgrade->bizVersion['biz5_0']          = '15_0';
$config->upgrade->bizVersion['biz5_0_1']        = '15_0_3';
$config->upgrade->bizVersion['biz5_1']          = '15_3';
$config->upgrade->bizVersion['biz5_2']          = '15_5';
$config->upgrade->bizVersion['biz5_3']          = '15_7';
$config->upgrade->bizVersion['biz5_3_1']        = '15_7_1';
$config->upgrade->bizVersion['biz6_0_beta1']    = '16_0_beta1';
$config->upgrade->bizVersion['biz6_0']          = '16_0';
$config->upgrade->bizVersion['biz6_1']          = '16_1';
$config->upgrade->bizVersion['biz6_2']          = '16_2';
$config->upgrade->bizVersion['biz6_3']          = '16_3';
$config->upgrade->bizVersion['biz6_4']          = '16_4';
$config->upgrade->bizVersion['biz6_5_beta1']    = '16_5_beta1';
$config->upgrade->bizVersion['biz6_5']          = '16_5';
$config->upgrade->bizVersion['biz7_0_beta1']    = '17_0_beta1';
$config->upgrade->bizVersion['biz7_0_beta2']    = '17_0_beta2';
$config->upgrade->bizVersion['biz7_0']          = '17_0';
$config->upgrade->bizVersion['biz7_1']          = '17_1';
$config->upgrade->bizVersion['biz7_2']          = '17_2';
$config->upgrade->bizVersion['biz7_3']          = '17_3';
$config->upgrade->bizVersion['biz7_4']          = '17_4';
$config->upgrade->bizVersion['biz7_5']          = '17_5';
$config->upgrade->bizVersion['biz7_6']          = '17_6';
$config->upgrade->bizVersion['biz7_6_1']        = '17_6_1';
$config->upgrade->bizVersion['biz7_6_2']        = '17_6_2';
$config->upgrade->bizVersion['biz7_7']          = '17_7';
$config->upgrade->bizVersion['biz7_8']          = '17_8';
$config->upgrade->bizVersion['biz8_0_beta1']    = '18_0_beta1';
$config->upgrade->bizVersion['biz8_0_beta2']    = '18_0_beta2';
$config->upgrade->bizVersion['biz8_0_beta3']    = '18_0_beta3';
$config->upgrade->bizVersion['biz8_0']          = '18_0';
$config->upgrade->bizVersion['biz8_1']          = '18_1';
$config->upgrade->bizVersion['biz8_2']          = '18_2';
$config->upgrade->bizVersion['biz8_3']          = '18_3';
$config->upgrade->bizVersion['biz8_4_alpha1']   = '18_4_alpha1';
$config->upgrade->bizVersion['biz8_4_beta1']    = '18_4_beta1';
$config->upgrade->bizVersion['biz8_4']          = '18_4';
$config->upgrade->bizVersion['biz8_5']          = '18_5';
$config->upgrade->bizVersion['biz8_6']          = '18_6';
$config->upgrade->bizVersion['biz8_7']          = '18_7';
$config->upgrade->bizVersion['biz8_8']          = '18_8';
$config->upgrade->bizVersion['biz8_9']          = '18_9';
$config->upgrade->bizVersion['biz8_10']         = '18_10';
$config->upgrade->bizVersion['biz8_10_1']       = '18_10_1';
$config->upgrade->bizVersion['biz8_11']         = '18_11';
$config->upgrade->bizVersion['biz8_12']         = '18_12';
$config->upgrade->bizVersion['biz10_0_0_beta1'] = '20_1_0';
$config->upgrade->bizVersion['biz10_0_0']       = '20_1_1';
$config->upgrade->bizVersion['biz10_1_0']       = '20_2_0';
$config->upgrade->bizVersion['biz10_2_0']       = '20_3_0';
$config->upgrade->bizVersion['biz10_3']         = '20_4';
$config->upgrade->bizVersion['biz8_13']         = '18_13';
$config->upgrade->bizVersion['biz10_4']         = '20_5';
$config->upgrade->bizVersion['biz10_5']         = '20_6';
$config->upgrade->bizVersion['biz10_6']         = '20_7';
$config->upgrade->bizVersion['biz10_6_1']       = '20_7_1';
$config->upgrade->bizVersion['biz10_7']         = '20_8';
$config->upgrade->bizVersion['biz11_0']         = '21_0';
$config->upgrade->bizVersion['biz11_1']         = '21_1';
$config->upgrade->bizVersion['biz11_2']         = '21_2';
$config->upgrade->bizVersion['biz11_3']         = '21_3';
$config->upgrade->bizVersion['biz11_4']         = '21_4';
$config->upgrade->bizVersion['biz11_5']         = '21_5';
$config->upgrade->bizVersion['biz11_6_beta']    = '21_6_beta';
$config->upgrade->bizVersion['biz11_6']         = '21_6';
$config->upgrade->bizVersion['biz11_6_1']       = '21_6_1';
$config->upgrade->bizVersion['biz11_7']         = '21_7';
$config->upgrade->bizVersion['biz12_0']         = '21_7_1'; // biz insert position.

$config->upgrade->proVersion = array();
$config->upgrade->proVersion['pro1_0']        = '3_1';
$config->upgrade->proVersion['pro1_1']        = '3_2';
$config->upgrade->proVersion['pro1_1_1']      = '3_2_1';
$config->upgrade->proVersion['pro1_2']        = '3_3';
$config->upgrade->proVersion['pro1_3']        = '3_3';
$config->upgrade->proVersion['pro2_0']        = '4_0';
$config->upgrade->proVersion['pro2_0_1']      = '4_0_1';
$config->upgrade->proVersion['pro2_1']        = '4_1';
$config->upgrade->proVersion['pro2_2_beta']   = '4_2_beta';
$config->upgrade->proVersion['pro2_3_beta']   = '4_3_beta';
$config->upgrade->proVersion['pro3_0_beta1']  = '5_0_beta1';
$config->upgrade->proVersion['pro3_0_beta1']  = '5_0_beta2';
$config->upgrade->proVersion['pro3_0']        = '5_0';
$config->upgrade->proVersion['pro3_1']        = '5_1';
$config->upgrade->proVersion['pro3_2']        = '5_2';
$config->upgrade->proVersion['pro3_2_1']      = '5_2_1';
$config->upgrade->proVersion['pro3_3']        = '5_3';
$config->upgrade->proVersion['pro4_0_beta1']  = '6_0';
$config->upgrade->proVersion['pro4_0']        = '6_1';
$config->upgrade->proVersion['pro4_1_beta']   = '6_2';
$config->upgrade->proVersion['pro4_2']        = '6_3';
$config->upgrade->proVersion['pro4_3']        = '6_3';
$config->upgrade->proVersion['pro4_4']        = '7_0';
$config->upgrade->proVersion['pro4_5']        = '7_1';
$config->upgrade->proVersion['pro4_6']        = '7_2_4';
$config->upgrade->proVersion['pro4_7']        = '7_2_5';
$config->upgrade->proVersion['pro4_7_1']      = '7_3';
$config->upgrade->proVersion['pro5_0']        = '8_0';
$config->upgrade->proVersion['pro5_0_1']      = '8_0_1';
$config->upgrade->proVersion['pro5_1']        = '8_1';
$config->upgrade->proVersion['pro5_1_3']      = '8_1_3';
$config->upgrade->proVersion['pro5_2']        = '8_2';
$config->upgrade->proVersion['pro5_2_1']      = '8_2_1';
$config->upgrade->proVersion['pro5_3']        = '8_2_3';
$config->upgrade->proVersion['pro5_3_1']      = '8_2_4';
$config->upgrade->proVersion['pro5_3_2']      = '8_2_5';
$config->upgrade->proVersion['pro5_3_3']      = '8_2_6';
$config->upgrade->proVersion['pro5_4']        = '8_3';
$config->upgrade->proVersion['pro5_4_1']      = '8_3_1';
$config->upgrade->proVersion['pro5_5']        = '8_4';
$config->upgrade->proVersion['pro5_5_1']      = '8_4_1';
$config->upgrade->proVersion['pro6_0_beta']   = '9_0_beta';
$config->upgrade->proVersion['pro6_0']        = '9_0';
$config->upgrade->proVersion['pro6_0_1']      = '9_0_1';
$config->upgrade->proVersion['pro6_1']        = '9_1';
$config->upgrade->proVersion['pro6_2']        = '9_1_2';
$config->upgrade->proVersion['pro6_3']        = '9_2';
$config->upgrade->proVersion['pro6_3_1']      = '9_2_1';
$config->upgrade->proVersion['pro6_4']        = '9_4';
$config->upgrade->proVersion['pro6_5']        = '9_5';
$config->upgrade->proVersion['pro6_5_1']      = '9_5_1';
$config->upgrade->proVersion['pro6_6']        = '9_6_3';
$config->upgrade->proVersion['pro6_6_1']      = '9_7';
$config->upgrade->proVersion['pro6_7']        = '9_8';
$config->upgrade->proVersion['pro6_7_1']      = '9_8_1';
$config->upgrade->proVersion['pro6_7_2']      = '9_8_2';
$config->upgrade->proVersion['pro6_7_3']      = '9_8_3';
$config->upgrade->proVersion['pro7_0_beta']   = '10_0';
$config->upgrade->proVersion['pro7_1']        = '10_1';
$config->upgrade->proVersion['pro7_2']        = '10_3';
$config->upgrade->proVersion['pro7_3']        = '10_4';
$config->upgrade->proVersion['pro7_4']        = '10_5';
$config->upgrade->proVersion['pro7_5']        = '10_5_1';
$config->upgrade->proVersion['pro7_5_1']      = '10_6';
$config->upgrade->proVersion['pro8_0']        = '11_1';
$config->upgrade->proVersion['pro8_1']        = '11_2';
$config->upgrade->proVersion['pro8_2']        = '11_3';
$config->upgrade->proVersion['pro8_3']        = '11_4_1';
$config->upgrade->proVersion['pro8_3_1']      = '11_4_1';
$config->upgrade->proVersion['pro8_4']        = '11_5';
$config->upgrade->proVersion['pro8_5']        = '11_6';
$config->upgrade->proVersion['pro8_5_1']      = '11_6_1';
$config->upgrade->proVersion['pro8_5_2']      = '11_6_3';
$config->upgrade->proVersion['pro8_5_3']      = '11_6_5';
$config->upgrade->proVersion['pro8_6']        = '11_7';
$config->upgrade->proVersion['pro8_7']        = '12_0';
$config->upgrade->proVersion['pro8_7_1']      = '12_0_1';
$config->upgrade->proVersion['pro8_8']        = '12_2';
$config->upgrade->proVersion['pro8_8_1']      = '12_3_1';
$config->upgrade->proVersion['pro8_8_2']      = '12_3_2';
$config->upgrade->proVersion['pro8_8_3']      = '12_3_3';
$config->upgrade->proVersion['pro8_9']        = '12_4';
$config->upgrade->proVersion['pro8_9_1']      = '12_4_1';
$config->upgrade->proVersion['pro8_9_2']      = '12_4_2';
$config->upgrade->proVersion['pro8_9_3']      = '12_4_3';
$config->upgrade->proVersion['pro8_9_4']      = '12_4_4';
$config->upgrade->proVersion['pro9_0']        = '12_5';
$config->upgrade->proVersion['pro9_0_1']      = '12_5_1';
$config->upgrade->proVersion['pro9_0_2']      = '12_5_2';
$config->upgrade->proVersion['pro9_0_3']      = '12_5_3';
$config->upgrade->proVersion['pro10_0_rc1']   = '15_0_rc3';
$config->upgrade->proVersion['pro10_0']       = '15_0';
$config->upgrade->proVersion['pro10_0_1']     = '15_0_2';
$config->upgrade->proVersion['pro10_0_2']     = '15_0_3';
$config->upgrade->proVersion['pro10_1']       = '15_3';
$config->upgrade->proVersion['pro10_2']       = '15_5';
$config->upgrade->proVersion['pro10_3']       = '15_7';
$config->upgrade->proVersion['pro10_3_1']     = '15_7_1';
$config->upgrade->proVersion['pro11_0_beta1'] = '16_0_beta1';
$config->upgrade->proVersion['pro11_0']       = '16_0';

$config->upgrade->liteVersion = array();
$config->upgrade->liteVersion['lite1_0'] = '16_5_beta1';
$config->upgrade->liteVersion['lite1_1'] = '16_5';
$config->upgrade->liteVersion['lite1_2'] = '17_1';

$config->upgrade->liteVersion['liteVIP1_1'] = 'biz6_5';
$config->upgrade->liteVersion['liteVIP1_2'] = 'biz7_1';

$config->upgrade->ipdVersion = array();
$config->upgrade->ipdVersion['ipd1_0_beta1']   = '18_4_alpha1';
$config->upgrade->ipdVersion['ipd1_0']         = '18_5';
$config->upgrade->ipdVersion['ipd1_0_1']       = '18_6';
$config->upgrade->ipdVersion['ipd1_0_2']       = '18_7';
$config->upgrade->ipdVersion['ipd1_1']         = '18_8';
$config->upgrade->ipdVersion['ipd1_1_1']       = '18_9';
$config->upgrade->ipdVersion['ipd1_1_2']       = '18_10';
$config->upgrade->ipdVersion['ipd1_2']         = '18_10_1';
$config->upgrade->ipdVersion['ipd1_3']         = '18_11';
$config->upgrade->ipdVersion['ipd1_4']         = '18_12';
$config->upgrade->ipdVersion['ipd2_0_0_beta1'] = '20_1_0';
$config->upgrade->ipdVersion['ipd2_0_0']       = '20_1_1';
$config->upgrade->ipdVersion['ipd2_1_0']       = '20_2_0';
$config->upgrade->ipdVersion['ipd2_2_0']       = '20_3_0';
$config->upgrade->ipdVersion['ipd2_3']         = '20_4';
$config->upgrade->ipdVersion['ipd1_5']         = '18_13';
$config->upgrade->ipdVersion['ipd2_4']         = '20_5';
$config->upgrade->ipdVersion['ipd2_5']         = '20_6';
$config->upgrade->ipdVersion['ipd2_6']         = '20_7';
$config->upgrade->ipdVersion['ipd2_6_1']       = '20_7_1';
$config->upgrade->ipdVersion['ipd2_7']         = '20_8';
$config->upgrade->ipdVersion['ipd3_0']         = '21_0';
$config->upgrade->ipdVersion['ipd3_1']         = '21_1';
$config->upgrade->ipdVersion['ipd3_2']         = '21_2';
$config->upgrade->ipdVersion['ipd3_3']         = '21_3';
$config->upgrade->ipdVersion['ipd3_4']         = '21_4';
$config->upgrade->ipdVersion['ipd3_5']         = '21_5';
$config->upgrade->ipdVersion['ipd3_6_beta']    = '21_6_beta';
$config->upgrade->ipdVersion['ipd3_6']         = '21_6';
$config->upgrade->ipdVersion['ipd3_6_1']       = '21_6_1';
$config->upgrade->ipdVersion['ipd3_7']         = '21_7';
$config->upgrade->ipdVersion['ipd4_0']         = '21_7_1'; // ipd insert position.

$config->upgrade->lowerTables = array();
$config->upgrade->lowerTables[$config->db->prefix . 'caseStep']       = $config->db->prefix . 'casestep';
$config->upgrade->lowerTables[$config->db->prefix . 'docLib']         = $config->db->prefix . 'doclib';
$config->upgrade->lowerTables[$config->db->prefix . 'groupPriv']      = $config->db->prefix . 'grouppriv';
$config->upgrade->lowerTables[$config->db->prefix . 'productPlan']    = $config->db->prefix . 'productplan';
$config->upgrade->lowerTables[$config->db->prefix . 'projectProduct'] = $config->db->prefix . 'projectproduct';
$config->upgrade->lowerTables[$config->db->prefix . 'projectStory']   = $config->db->prefix . 'projectstory';
$config->upgrade->lowerTables[$config->db->prefix . 'storySpec']      = $config->db->prefix . 'storyspec';
$config->upgrade->lowerTables[$config->db->prefix . 'taskEstimate']   = $config->db->prefix . 'taskestimate';
$config->upgrade->lowerTables[$config->db->prefix . 'testResult']     = $config->db->prefix . 'testresult';
$config->upgrade->lowerTables[$config->db->prefix . 'testRun']        = $config->db->prefix . 'testrun';
$config->upgrade->lowerTables[$config->db->prefix . 'testTask']       = $config->db->prefix . 'testtask';
$config->upgrade->lowerTables[$config->db->prefix . 'userContact']    = $config->db->prefix . 'usercontact';
$config->upgrade->lowerTables[$config->db->prefix . 'userGroup']      = $config->db->prefix . 'usergroup';
$config->upgrade->lowerTables[$config->db->prefix . 'userQuery']      = $config->db->prefix . 'userquery';
$config->upgrade->lowerTables[$config->db->prefix . 'userTPL']        = $config->db->prefix . 'usertpl';

$config->upgrade->bearychat = array();
$config->upgrade->bearychat['zh-cn'] = '倍洽';
$config->upgrade->bearychat['zh-tw'] = '倍洽';
$config->upgrade->bearychat['en']    = 'Bearychat';
$config->upgrade->bearychat['de']    = 'Bearychat';

$config->upgrade->discardedBugTypes['de']['interface']    = 'UI Optimierung';
$config->upgrade->discardedBugTypes['de']['newfeature']   = 'Neues Feature';
$config->upgrade->discardedBugTypes['de']['designchange'] = 'Design Änderung';
$config->upgrade->discardedBugTypes['de']['trackthings']  = 'Arbeit Verfolgen';

$config->upgrade->discardedBugTypes['en']['interface']    = 'Interface';
$config->upgrade->discardedBugTypes['en']['designchange'] = 'DesignChange';
$config->upgrade->discardedBugTypes['en']['newfeature']   = 'NewFeature';
$config->upgrade->discardedBugTypes['en']['trackthings']  = 'Tracking';

$config->upgrade->discardedBugTypes['fr']['interface']    = 'Interface';
$config->upgrade->discardedBugTypes['fr']['designchange'] = 'Design Change';
$config->upgrade->discardedBugTypes['fr']['newfeature']   = 'Nouvelle fonctionnalité';
$config->upgrade->discardedBugTypes['fr']['trackthings']  = 'Tracking';

$config->upgrade->discardedBugTypes['zh-cn']['interface']    = '界面优化';
$config->upgrade->discardedBugTypes['zh-cn']['designchange'] = '设计变更';
$config->upgrade->discardedBugTypes['zh-cn']['newfeature']   = "新增需求";
$config->upgrade->discardedBugTypes['zh-cn']['trackthings']  = '事务跟踪';

$config->upgrade->discardedBugTypes['zh-tw']['interface']    = '界面優化';
$config->upgrade->discardedBugTypes['zh-tw']['designchange'] = '設計變更';
$config->upgrade->discardedBugTypes['zh-tw']['newfeature']   = "新增需求";
$config->upgrade->discardedBugTypes['zh-tw']['trackthings']  = '事務跟蹤';

$config->delete['10.6'][]   = 'module/chat/ext/control/extensions.php';
$config->delete['12.4.2'][] = 'www/js/ueditor';

$config->delete['16_5_beta1'][] = 'module/my/ext/model/hook/setMenu.effort.php';
$config->delete['16_5_beta1'][] = 'module/my/ext/lang/de/bizext.php';
$config->delete['16_5_beta1'][] = 'module/my/ext/lang/vi/bizext.php';
$config->delete['16_5_beta1'][] = 'module/my/ext/lang/fr/bizext.php';
$config->delete['16_5_beta1'][] = 'module/common/ext/model/search.php';
$config->delete['16_5_beta1'][] = 'module/common/ext/lang/de/search.php';
$config->delete['16_5_beta1'][] = 'module/common/ext/lang/vi/search.php';
$config->delete['16_5_beta1'][] = 'module/common/ext/lang/fr/search.php';
$config->delete['16_5_beta1'][] = 'module/common/ext/view/footer.search.html.hook.php';
$config->delete['16_5_beta1'][] = 'module/common/ext/view/header.search.html.hook.php';
$config->delete['16_5_beta1'][] = 'module/story/ext/model/bizext.php';
$config->delete['16_5_beta1'][] = 'module/misc/ext/config/config2.php';
$config->delete['16_5_beta1'][] = 'module/misc/ext/config/config1.php';
$config->delete['16_5_beta1'][] = 'module/misc/ext/model/hook/hello2.start.php';
$config->delete['16_5_beta1'][] = 'module/misc/ext/model/hook/hello.test.php';
$config->delete['16_5_beta1'][] = 'module/misc/ext/model/hook/hello.test2.php';
$config->delete['16_5_beta1'][] = 'module/misc/ext/model/foo.php';
$config->delete['16_5_beta1'][] = 'module/misc/ext/model/class/test.class.php';
$config->delete['16_5_beta1'][] = 'module/misc/ext/model/ext.php';
$config->delete['16_5_beta1'][] = 'module/misc/ext/lang/zh-cn2.php';
$config->delete['16_5_beta1'][] = 'module/misc/ext/lang/zh-cn1.php';
$config->delete['16_5_beta1'][] = 'module/misc/ext/view/getsid.html.php';
$config->delete['16_5_beta1'][] = 'module/misc/ext/view/getsid.color.html.hook.php';
$config->delete['16_5_beta1'][] = 'module/todo/ext/model/hook/getTodos4Side.zentaobiz.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/config/gantt.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/model/flow.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/model/gantt.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/model/calendar.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/lang/en/gantt.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/lang/en/calendar.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/lang/de/flow.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/lang/de/gantt.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/lang/de/calendar.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/lang/vi/flow.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/lang/vi/gantt.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/lang/vi/calendar.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/lang/zh-tw/flow.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/lang/zh-tw/calendar.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/lang/zh-cn/calendar.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/lang/fr/flow.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/lang/fr/gantt.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/lang/fr/calendar.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/view/effort.calendar.html.hook.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/view/effort.html.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/view/taskeffort.html.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/view/ajaxkanbansetting.flow.html.hook.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/view/featurebar.calendar.html.hook.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/view/featurebar.html.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/view/task.excel.html.hook.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/view/gantt.html.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/view/maintainrelation.html.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/view/effortcalendar.html.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/view/featurebar.gantt.html.hook.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/view/calendar.html.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/view/relation.html.php';
$config->delete['16_5_beta1'][] = 'module/product/ext/model/web.php';
$config->delete['16_5_beta1'][] = 'module/product/ext/lang/en/bizext.php';
$config->delete['16_5_beta1'][] = 'module/product/ext/lang/de/bizext.php';
$config->delete['16_5_beta1'][] = 'module/product/ext/lang/vi/bizext.php';
$config->delete['16_5_beta1'][] = 'module/product/ext/lang/zh-tw/bizext.php';
$config->delete['16_5_beta1'][] = 'module/product/ext/lang/zh-cn/bizext.php';
$config->delete['16_5_beta1'][] = 'module/product/ext/lang/fr/bizext.php';
$config->delete['16_5_beta1'][] = 'module/project/ext/css/relation/gantt.css';
$config->delete['16_5_beta1'][] = 'module/project/ext/css/ajaxkanbansetting/flow.css';
$config->delete['16_5_beta1'][] = 'module/project/ext/css/taskeffort/taskeffort.css';
$config->delete['16_5_beta1'][] = 'module/project/ext/css/calendar/calendar.css';
$config->delete['16_5_beta1'][] = 'module/project/ext/css/effort/effort.css';
$config->delete['16_5_beta1'][] = 'module/project/ext/css/task/execl.css';
$config->delete['16_5_beta1'][] = 'module/project/ext/js/taskeffort/taskeffort.js';
$config->delete['16_5_beta1'][] = 'module/project/ext/js/effort/effort.js';

$config->delete['17_0_beta1'][] = 'extension/biz/flow/ext/control/export.php';
$config->delete['17_0_beta1'][] = 'extension/biz/flow/ext/control/exporttemplate.php';
$config->delete['17_0_beta1'][] = 'extension/biz/flow/ext/view/autoimport.html.php';
$config->delete['17_0_beta1'][] = 'extension/biz/flow/ext/view/browse.html.php';
$config->delete['17_0_beta1'][] = 'extension/biz/workflow/ext/control/flowchart.php';
$config->delete['17_0_beta1'][] = 'extension/biz/workflowcondition/ext/view/browse.html.php';
$config->delete['17_0_beta1'][] = 'extension/biz/workflowhook/ext/view/browse.html.php';
$config->delete['17_0_beta1'][] = 'extension/biz/group/ext/lang/zh-cn/aflow.php';
$config->delete['17_0_beta1'][] = 'extension/biz/group/ext/lang/zh-cn/zflow.php';
$config->delete['17_0_beta1'][] = 'extension/biz/group/ext/model/flow.php';
$config->delete['17_0_beta1'][] = 'extension/biz/user/ext/model/bizext.php';
$config->delete['17_0_beta1'][] = 'extension/biz/sso/ext/model/bizext.php';
$config->delete['17_0_beta1'][] = 'extension/max/flow/ext/control/export.php';
$config->delete['17_0_beta1'][] = 'extension/max/flow/ext/control/exporttemplate.php';
$config->delete['17_0_beta1'][] = 'extension/max/flow/ext/view/autoimport.html.php';
$config->delete['17_0_beta1'][] = 'extension/max/flow/ext/view/browse.html.php';
$config->delete['17_0_beta1'][] = 'extension/max/workflow/ext/control/flowchart.php';
$config->delete['17_0_beta1'][] = 'extension/max/workflowcondition/ext/view/browse.html.php';
$config->delete['17_0_beta1'][] = 'extension/max/workflowhook/ext/view/browse.html.php';
$config->delete['17_0_beta1'][] = 'extension/max/group/ext/lang/zh-cn/aflow.php';
$config->delete['17_0_beta1'][] = 'extension/max/group/ext/lang/zh-cn/zflow.php';
$config->delete['17_0_beta1'][] = 'extension/max/group/ext/model/flow.php';
$config->delete['17_0_beta1'][] = 'extension/max/user/ext/model/bizext.php';
$config->delete['17_0_beta1'][] = 'extension/max/sso/ext/model/bizext.php';

$config->delete['17_2'][] = 'extension/biz/my/ext/view/todo.calendar.html.hook.php';
$config->delete['17_2'][] = 'extension/max/my/ext/view/todo.calendar.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/attend/ext/view/stat.oa.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/flow/ext/view/browse.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/flow/ext/view/create.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflow/ext/view/browsedb.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflow/ext/view/browseflow.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflow/ext/view/edit.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflow/ext/view/flowchart.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflow/ext/view/release.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflow/ext/view/setcss.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflow/ext/view/setjs.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflowaction/ext/view/browse.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflowaction/ext/view/edit.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflowaction/ext/view/setnotice.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflowdatasource/ext/view/browse.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflowdatasource/ext/view/edit.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflowfield/ext/view/browse.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflowhook/ext/view/create.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflowhook/ext/view/edit.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflowlabel/ext/view/browse.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflowlayout/ext/view/admin.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/workflowrelation/ext/view/admin.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/extension/lite/workflowrule/ext/view/browse.flow.html.hook.php';
$config->delete['17_2'][] = 'extension/lite/extension/lite/workflowrule/ext/view/view.flow.html.hook.php';
$config->delete['17_8'][] = 'extension/biz/common/ext/lang/zh-cn/crystal.php';
$config->delete['17_8'][] = 'extension/biz/common/ext/lang/zh-tw/crystal.php';
$config->delete['17_8'][] = 'extension/biz/common/ext/lang/en/crystal.php';
$config->delete['17_8'][] = 'extension/biz/common/ext/lang/de/crystal.php';
$config->delete['17_8'][] = 'extension/biz/common/ext/lang/fr/crystal.php';
$config->delete['17_8'][] = 'extension/biz/common/ext/lang/vi/crystal.php';
$config->delete['17_8'][] = 'extension/max/common/ext/lang/zh-cn/crystal.php';
$config->delete['17_8'][] = 'extension/max/common/ext/lang/zh-tw/crystal.php';
$config->delete['17_8'][] = 'extension/max/common/ext/lang/en/crystal.php';
$config->delete['17_8'][] = 'extension/max/common/ext/lang/de/crystal.php';
$config->delete['17_8'][] = 'extension/max/common/ext/lang/fr/crystal.php';
$config->delete['17_8'][] = 'extension/max/common/ext/lang/vi/crystal.php';
$config->delete['18_0'][] = 'extension/max/custom/ext/view/mode.zentaomax.html.hook.php';
$config->delete['18_0'][] = 'extension/max/my/ext/view/audit.html.php';
$config->delete['18_0'][] = 'extension/biz/repo/ext/control/ajaxgetcommitinfo.php';
$config->delete['18_0'][] = 'extension/biz/repo/ext/control/ajaxgetbranchesandtags.php';
$config->delete['18_0'][] = 'extension/biz/repo/ext/control/ajaxgetbranchesandtags.php';
$config->delete['18_0'][] = 'extension/biz/repo/ext/control/editbug.php';
$config->delete['18_0'][] = 'extension/biz/repo/ext/view/ajaxgetcommitinfo.html.php';
$config->delete['18_0'][] = 'extension/max/repo/ext/control/ajaxgetcommitinfo.php';
$config->delete['18_0'][] = 'extension/max/repo/ext/control/ajaxgetbranchesandtags.php';
$config->delete['18_0'][] = 'extension/max/repo/ext/control/ajaxgetbranchesandtags.php';
$config->delete['18_0'][] = 'extension/max/repo/ext/control/editbug.php';
$config->delete['18_0'][] = 'extension/max/repo/ext/view/ajaxgetcommitinfo.html.php';

$config->delete['18_4_alpha1'][] = 'extension/biz/action/ext/lang/de/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/action/ext/lang/en/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/action/ext/lang/fr/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/action/ext/lang/vi/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/action/ext/lang/zh-cn/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/action/ext/lang/zh-tw/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/chart/css/design.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/chart/js/create.js';
$config->delete['18_4_alpha1'][] = 'extension/biz/chart/js/design.js';
$config->delete['18_4_alpha1'][] = 'extension/biz/chart/lang/de.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/chart/lang/en.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/chart/lang/fr.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/chart/lang/zh-cn.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/chart/lang/zh-tw.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/chart/view/browse.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/chart/view/create.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/chart/view/design.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/chart/view/edit.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/chart/config.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/chart/control.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/chart/model.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/pivot/ext/control/ajaxgetpivot.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/pivot/ext/control/ajaxgetsysoptions.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/pivot/ext/css/preview/zentaobiz.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/pivot/ext/css/common/zentaobiz.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/pivot/ext/js/preview/zentaobiz.js';
$config->delete['18_4_alpha1'][] = 'extension/biz/pivot/ext/js/common/zentaobiz.js';
$config->delete['18_4_alpha1'][] = 'extension/biz/pivot/ext/view/show.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/common/ext/lang/de/report.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/common/ext/lang/en/report.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/common/ext/lang/fr/report.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/common/ext/lang/vi/report.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/common/ext/lang/zh-cn/report.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/common/ext/lang/zh-tw/report.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/dev/ext/lang/de/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/dev/ext/lang/en/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/dev/ext/lang/fr/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/dev/ext/lang/vi/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/dev/ext/lang/zh-cn/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/dev/ext/lang/zh-tw/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/group/ext/lang/de/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/group/ext/lang/en/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/group/ext/lang/fr/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/group/ext/lang/vi/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/group/ext/lang/vi/export.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/group/ext/lang/zh-cn/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/group/ext/lang/zh-tw/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/config/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/ajaxcheckvar.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/ajaxsetlang.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/browsereport.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/bugassignsummary.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/bugsummary.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/build.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/casesrun.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/crystalexport.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/custom.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/deletereport.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/editreport.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/productinvest.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/roadmap.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/savereport.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/show.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/storylinkedbug.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/testcase.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/usereport.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/workassignsummary.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/control/worksummary.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/css/custom/crystal.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/css/roadmap/report.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/css/show/crystal.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/css/show/crystal.en.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/css/show/crystal.zh-cn.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/css/show/crystal.zh-tw.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/css/workassignsummary/report.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/css/workassignsummary/report.en.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/css/workassignsummary/report.zh-cn.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/css/workassignsummary/report.zh-tw.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/css/worksummary/report.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/css/worksummary/report.en.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/css/worksummary/report.zh-cn.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/css/worksummary/report.zh-tw.css';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/js/ajaxsetlang/crystal.js';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/js/bugassignsummary/report.js';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/js/bugsummary/report.js';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/js/custom/crystal.js';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/js/productinvest/report.js';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/js/roadmap/report.js';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/js/show/crystal.js';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/js/workassignsummary/worksummary.js';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/js/worksummary/worksummary.js';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/lang/de/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/lang/de/report.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/lang/en/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/lang/en/report.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/lang/fr/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/lang/fr/report.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/lang/vi/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/lang/vi/report.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/lang/zh-cn/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/lang/zh-cn/report.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/lang/zh-tw/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/lang/zh-tw/report.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/model/class/crystal.class.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/model/class/report.class.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/model/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/model/report.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/ajaxsetlang.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/blockcondition.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/blockdata.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/blocksql.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/browsereport.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/bugassignsummary.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/bugsummary.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/build.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/casesrun.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/crystalexport.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/custom.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/editreport.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/productinvest.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/reportdata.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/roadmap.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/savereport.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/setvarvalues.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/show.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/storylinkedbug.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/testcase.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/usereport.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/workassignsummary.html.php';
$config->delete['18_4_alpha1'][] = 'extension/biz/report/ext/view/worksummary.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/chart/css/design.css';
$config->delete['18_4_alpha1'][] = 'extension/max/chart/js/create.js';
$config->delete['18_4_alpha1'][] = 'extension/max/chart/js/design.js';
$config->delete['18_4_alpha1'][] = 'extension/max/chart/lang/de.php';
$config->delete['18_4_alpha1'][] = 'extension/max/chart/lang/en.php';
$config->delete['18_4_alpha1'][] = 'extension/max/chart/lang/fr.php';
$config->delete['18_4_alpha1'][] = 'extension/max/chart/lang/zh-cn.php';
$config->delete['18_4_alpha1'][] = 'extension/max/chart/lang/zh-tw.php';
$config->delete['18_4_alpha1'][] = 'extension/max/chart/view/browse.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/chart/view/create.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/chart/view/design.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/chart/view/edit.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/chart/config.php';
$config->delete['18_4_alpha1'][] = 'extension/max/chart/control.php';
$config->delete['18_4_alpha1'][] = 'extension/max/chart/model.php';
$config->delete['18_4_alpha1'][] = 'extension/max/pivot/ext/control/ajaxgetpivot.php';
$config->delete['18_4_alpha1'][] = 'extension/max/pivot/ext/control/ajaxgetsysoptions.php';
$config->delete['18_4_alpha1'][] = 'extension/max/pivot/ext/css/preview/zentaobiz.css';
$config->delete['18_4_alpha1'][] = 'extension/max/pivot/ext/css/common/zentaobiz.css';
$config->delete['18_4_alpha1'][] = 'extension/max/pivot/ext/js/preview/zentaobiz.js';
$config->delete['18_4_alpha1'][] = 'extension/max/pivot/ext/js/common/zentaobiz.js';
$config->delete['18_4_alpha1'][] = 'extension/max/pivot/ext/view/show.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/common/ext/lang/de/report.php';
$config->delete['18_4_alpha1'][] = 'extension/max/common/ext/lang/en/report.php';
$config->delete['18_4_alpha1'][] = 'extension/max/common/ext/lang/fr/report.php';
$config->delete['18_4_alpha1'][] = 'extension/max/common/ext/lang/vi/report.php';
$config->delete['18_4_alpha1'][] = 'extension/max/common/ext/lang/zh-cn/report.php';
$config->delete['18_4_alpha1'][] = 'extension/max/common/ext/lang/zh-tw/report.php';
$config->delete['18_4_alpha1'][] = 'extension/max/group/ext/lang/vi/crystal.php';
$config->delete['18_4_alpha1'][] = 'extension/max/group/ext/lang/vi/export.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/control/bugassignsummary.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/control/bugsummary.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/control/build.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/control/casesrun.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/control/instancetemplate.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/control/productinvest.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/control/roadmap.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/control/storylinkedbug.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/control/testcase.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/control/workassignsummary.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/control/worksummary.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/css/roadmap/report.css';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/css/workassignsummary/report.css';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/css/workassignsummary/report.en.css';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/css/workassignsummary/report.zh-cn.css';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/css/workassignsummary/report.zh-tw.css';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/css/worksummary/report.css';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/css/worksummary/report.en.css';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/css/worksummary/report.zh-cn.css';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/css/worksummary/report.zh-tw.css';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/js/bugassignsummary/report.js';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/js/bugsummary/report.js';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/js/productinvest/report.js';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/js/roadmap/report.js';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/js/workassignsummary/worksummary.js';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/js/worksummary/worksummary.js';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/lang/de/report.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/lang/en/report.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/lang/fr/report.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/lang/vi/report.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/lang/zh-cn/report.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/lang/zh-tw/report.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/model/class/report.class.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/model/report.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/view/bugassignsummary.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/view/bugsummary.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/view/build.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/view/casesrun.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/view/instancetemplate.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/view/productinvest.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/view/roadmap.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/view/storylinkedbug.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/view/testcase.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/view/workassignsummary.html.php';
$config->delete['18_4_alpha1'][] = 'extension/max/report/ext/view/worksummary.html.php';
$config->delete['18_4_alpha1'][] = 'module/report/css/bugcreate.css';
$config->delete['18_4_alpha1'][] = 'module/report/css/common.css';
$config->delete['18_4_alpha1'][] = 'module/report/css/productsummary.css';
$config->delete['18_4_alpha1'][] = 'module/report/css/projectdeviation.css';
$config->delete['18_4_alpha1'][] = 'module/report/css/workload.css';
$config->delete['18_4_alpha1'][] = 'module/report/js/bugassign.js';
$config->delete['18_4_alpha1'][] = 'module/report/js/bugcreate.js';
$config->delete['18_4_alpha1'][] = 'module/report/js/productsummary.js';
$config->delete['18_4_alpha1'][] = 'module/report/js/projectdeviation.js';
$config->delete['18_4_alpha1'][] = 'module/report/js/workload.js';
$config->delete['18_4_alpha1'][] = 'module/report/view/blockreportlist.html.php';
$config->delete['18_4_alpha1'][] = 'module/report/view/bugassign.html.php';
$config->delete['18_4_alpha1'][] = 'module/report/view/bugcreate.html.php';
$config->delete['18_4_alpha1'][] = 'module/report/view/preview.html.php';
$config->delete['18_4_alpha1'][] = 'module/report/view/productsummary.html.php';
$config->delete['18_4_alpha1'][] = 'module/report/view/projectdeviation.html.php';
$config->delete['18_4_alpha1'][] = 'module/report/view/workload.html.php';

$config->delete['18_4'][] = 'extension/biz/dataview/config.php';
$config->delete['18_4'][] = 'extension/biz/dataview/control.php';
$config->delete['18_4'][] = 'extension/biz/dataview/model.php';
$config->delete['18_4'][] = 'extension/biz/dataview/lang/de.php';
$config->delete['18_4'][] = 'extension/biz/dataview/lang/en.php';
$config->delete['18_4'][] = 'extension/biz/dataview/lang/fr.php';
$config->delete['18_4'][] = 'extension/biz/dataview/lang/zh-cn.php';
$config->delete['18_4'][] = 'extension/biz/dataview/css/browse.css';
$config->delete['18_4'][] = 'extension/biz/dataview/css/browse.zh-cn.css';
$config->delete['18_4'][] = 'extension/biz/dataview/css/browse.zh-cn.css';
$config->delete['18_4'][] = 'extension/biz/dataview/css/create.css';
$config->delete['18_4'][] = 'extension/biz/dataview/css/edit.css';
$config->delete['18_4'][] = 'extension/biz/dataview/css/query.css';
$config->delete['18_4'][] = 'extension/biz/dataview/css/querybase.css';
$config->delete['18_4'][] = 'extension/biz/dataview/js/browse.js';
$config->delete['18_4'][] = 'extension/biz/dataview/js/common.js';
$config->delete['18_4'][] = 'extension/biz/dataview/js/create.js';
$config->delete['18_4'][] = 'extension/biz/dataview/js/datastorage.js';
$config->delete['18_4'][] = 'extension/biz/dataview/js/edit.js';
$config->delete['18_4'][] = 'extension/biz/dataview/js/query.js';
$config->delete['18_4'][] = 'extension/biz/dataview/js/querybase.js';
$config->delete['18_4'][] = 'extension/biz/dataview/view/browse.html.php';
$config->delete['18_4'][] = 'extension/biz/dataview/view/create.html.php';
$config->delete['18_4'][] = 'extension/biz/dataview/view/edit.html.php';
$config->delete['18_4'][] = 'extension/biz/dataview/view/query.html.php';
$config->delete['18_4'][] = 'extension/biz/dataview/view/querybase.html.php';
$config->delete['18_4'][] = 'extension/max/dataview/config.php';
$config->delete['18_4'][] = 'extension/max/dataview/control.php';
$config->delete['18_4'][] = 'extension/max/dataview/model.php';
$config->delete['18_4'][] = 'extension/max/dataview/lang/de.php';
$config->delete['18_4'][] = 'extension/max/dataview/lang/en.php';
$config->delete['18_4'][] = 'extension/max/dataview/lang/fr.php';
$config->delete['18_4'][] = 'extension/max/dataview/lang/zh-cn.php';
$config->delete['18_4'][] = 'extension/max/dataview/css/browse.css';
$config->delete['18_4'][] = 'extension/max/dataview/css/browse.zh-cn.css';
$config->delete['18_4'][] = 'extension/max/dataview/css/browse.zh-cn.css';
$config->delete['18_4'][] = 'extension/max/dataview/css/create.css';
$config->delete['18_4'][] = 'extension/max/dataview/css/edit.css';
$config->delete['18_4'][] = 'extension/max/dataview/css/query.css';
$config->delete['18_4'][] = 'extension/max/dataview/css/querybase.css';
$config->delete['18_4'][] = 'extension/max/dataview/js/browse.js';
$config->delete['18_4'][] = 'extension/max/dataview/js/common.js';
$config->delete['18_4'][] = 'extension/max/dataview/js/create.js';
$config->delete['18_4'][] = 'extension/max/dataview/js/datastorage.js';
$config->delete['18_4'][] = 'extension/max/dataview/js/edit.js';
$config->delete['18_4'][] = 'extension/max/dataview/js/query.js';
$config->delete['18_4'][] = 'extension/max/dataview/js/querybase.js';
$config->delete['18_4'][] = 'extension/max/dataview/view/browse.html.php';
$config->delete['18_4'][] = 'extension/max/dataview/view/create.html.php';
$config->delete['18_4'][] = 'extension/max/dataview/view/edit.html.php';
$config->delete['18_4'][] = 'extension/max/dataview/view/query.html.php';
$config->delete['18_4'][] = 'extension/max/dataview/view/querybase.html.php';

$config->delete['18_6'][] = 'extension/biz/host/view/changestatus.html.php';
$config->delete['18_6'][] = 'extension/biz/host/view/create.html.php';
$config->delete['18_6'][] = 'extension/biz/host/view/m.browse.html.php';
$config->delete['18_6'][] = 'extension/biz/host/view/treemap.html.php';
$config->delete['18_6'][] = 'extension/biz/host/view/m.view.html.php';
$config->delete['18_6'][] = 'extension/biz/host/view/view.html.php';
$config->delete['18_6'][] = 'extension/biz/host/view/browse.html.php';
$config->delete['18_6'][] = 'extension/biz/host/view/edit.html.php';
$config->delete['18_6'][] = 'extension/biz/host/config.php';
$config->delete['18_6'][] = 'extension/biz/host/css/enew.vi.css';
$config->delete['18_6'][] = 'extension/biz/host/css/create.css';
$config->delete['18_6'][] = 'extension/biz/host/css/view.zh-cn.css';
$config->delete['18_6'][] = 'extension/biz/host/css/view.en.css';
$config->delete['18_6'][] = 'extension/biz/host/css/view.css';
$config->delete['18_6'][] = 'extension/biz/host/css/view.zh-tw.css';
$config->delete['18_6'][] = 'extension/biz/host/css/edit.css';
$config->delete['18_6'][] = 'extension/biz/host/css/treemap.css';
$config->delete['18_6'][] = 'extension/biz/host/model.php';
$config->delete['18_6'][] = 'extension/biz/host/lang/de.php';
$config->delete['18_6'][] = 'extension/biz/host/lang/zh-cn.php';
$config->delete['18_6'][] = 'extension/biz/host/lang/fr.php';
$config->delete['18_6'][] = 'extension/biz/host/lang/zh-tw.php';
$config->delete['18_6'][] = 'extension/biz/host/lang/vi.php';
$config->delete['18_6'][] = 'extension/biz/host/lang/en.php';
$config->delete['18_6'][] = 'extension/biz/host/control.php';
$config->delete['18_6'][] = 'extension/biz/host/js/create.js';
$config->delete['18_6'][] = 'extension/biz/host/js/edit.js';
$config->delete['18_6'][] = 'extension/biz/account/view/create.html.php';
$config->delete['18_6'][] = 'extension/biz/account/view/view.html.php';
$config->delete['18_6'][] = 'extension/biz/account/view/browse.html.php';
$config->delete['18_6'][] = 'extension/biz/account/view/edit.html.php';
$config->delete['18_6'][] = 'extension/biz/account/config.php';
$config->delete['18_6'][] = 'extension/biz/account/model.php';
$config->delete['18_6'][] = 'extension/biz/account/lang/de.php';
$config->delete['18_6'][] = 'extension/biz/account/lang/zh-cn.php';
$config->delete['18_6'][] = 'extension/biz/account/lang/fr.php';
$config->delete['18_6'][] = 'extension/biz/account/lang/zh-tw.php';
$config->delete['18_6'][] = 'extension/biz/account/lang/en.php';
$config->delete['18_6'][] = 'extension/biz/account/control.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/view/create.html.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/view/m.browse.html.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/view/m.view.html.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/view/view.html.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/view/browse.html.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/view/edit.html.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/config.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/css/browse.css';
$config->delete['18_6'][] = 'extension/biz/serverroom/model.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/lang/de.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/lang/zh-cn.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/lang/fr.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/lang/zh-tw.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/lang/vi.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/lang/en.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/control.php';
$config->delete['18_6'][] = 'extension/biz/serverroom/js/custom.js';
$config->delete['18_6'][] = 'extension/biz/ops/view/setting.html.php';
$config->delete['18_6'][] = 'extension/biz/ops/lang/de.php';
$config->delete['18_6'][] = 'extension/biz/ops/lang/zh-cn.php';
$config->delete['18_6'][] = 'extension/biz/ops/lang/fr.php';
$config->delete['18_6'][] = 'extension/biz/ops/lang/zh-tw.php';
$config->delete['18_6'][] = 'extension/biz/ops/lang/vi.php';
$config->delete['18_6'][] = 'extension/biz/ops/lang/en.php';
$config->delete['18_6'][] = 'extension/biz/ops/control.php';
$config->delete['18_6'][] = 'extension/biz/ops/js/setting.js';
$config->delete['18_6'][] = 'extension/max/host/view/changestatus.html.php';
$config->delete['18_6'][] = 'extension/max/host/view/create.html.php';
$config->delete['18_6'][] = 'extension/max/host/view/m.browse.html.php';
$config->delete['18_6'][] = 'extension/max/host/view/treemap.html.php';
$config->delete['18_6'][] = 'extension/max/host/view/m.view.html.php';
$config->delete['18_6'][] = 'extension/max/host/view/view.html.php';
$config->delete['18_6'][] = 'extension/max/host/view/browse.html.php';
$config->delete['18_6'][] = 'extension/max/host/view/edit.html.php';
$config->delete['18_6'][] = 'extension/max/host/config.php';
$config->delete['18_6'][] = 'extension/max/host/css/enew.vi.css';
$config->delete['18_6'][] = 'extension/max/host/css/create.css';
$config->delete['18_6'][] = 'extension/max/host/css/view.zh-cn.css';
$config->delete['18_6'][] = 'extension/max/host/css/view.en.css';
$config->delete['18_6'][] = 'extension/max/host/css/view.css';
$config->delete['18_6'][] = 'extension/max/host/css/view.zh-tw.css';
$config->delete['18_6'][] = 'extension/max/host/css/edit.css';
$config->delete['18_6'][] = 'extension/max/host/css/treemap.css';
$config->delete['18_6'][] = 'extension/max/host/model.php';
$config->delete['18_6'][] = 'extension/max/host/lang/de.php';
$config->delete['18_6'][] = 'extension/max/host/lang/zh-cn.php';
$config->delete['18_6'][] = 'extension/max/host/lang/fr.php';
$config->delete['18_6'][] = 'extension/max/host/lang/zh-tw.php';
$config->delete['18_6'][] = 'extension/max/host/lang/vi.php';
$config->delete['18_6'][] = 'extension/max/host/lang/en.php';
$config->delete['18_6'][] = 'extension/max/host/control.php';
$config->delete['18_6'][] = 'extension/max/host/js/create.js';
$config->delete['18_6'][] = 'extension/max/host/js/edit.js';
$config->delete['18_6'][] = 'extension/max/account/view/create.html.php';
$config->delete['18_6'][] = 'extension/max/account/view/view.html.php';
$config->delete['18_6'][] = 'extension/max/account/view/browse.html.php';
$config->delete['18_6'][] = 'extension/max/account/view/edit.html.php';
$config->delete['18_6'][] = 'extension/max/account/config.php';
$config->delete['18_6'][] = 'extension/max/account/model.php';
$config->delete['18_6'][] = 'extension/max/account/lang/de.php';
$config->delete['18_6'][] = 'extension/max/account/lang/zh-cn.php';
$config->delete['18_6'][] = 'extension/max/account/lang/fr.php';
$config->delete['18_6'][] = 'extension/max/account/lang/zh-tw.php';
$config->delete['18_6'][] = 'extension/max/account/lang/en.php';
$config->delete['18_6'][] = 'extension/max/account/control.php';
$config->delete['18_6'][] = 'extension/max/serverroom/view/create.html.php';
$config->delete['18_6'][] = 'extension/max/serverroom/view/m.browse.html.php';
$config->delete['18_6'][] = 'extension/max/serverroom/view/m.view.html.php';
$config->delete['18_6'][] = 'extension/max/serverroom/view/view.html.php';
$config->delete['18_6'][] = 'extension/max/serverroom/view/browse.html.php';
$config->delete['18_6'][] = 'extension/max/serverroom/view/edit.html.php';
$config->delete['18_6'][] = 'extension/max/serverroom/config.php';
$config->delete['18_6'][] = 'extension/max/serverroom/css/browse.css';
$config->delete['18_6'][] = 'extension/max/serverroom/model.php';
$config->delete['18_6'][] = 'extension/max/serverroom/lang/de.php';
$config->delete['18_6'][] = 'extension/max/serverroom/lang/zh-cn.php';
$config->delete['18_6'][] = 'extension/max/serverroom/lang/fr.php';
$config->delete['18_6'][] = 'extension/max/serverroom/lang/zh-tw.php';
$config->delete['18_6'][] = 'extension/max/serverroom/lang/vi.php';
$config->delete['18_6'][] = 'extension/max/serverroom/lang/en.php';
$config->delete['18_6'][] = 'extension/max/serverroom/control.php';
$config->delete['18_6'][] = 'extension/max/serverroom/js/custom.js';
$config->delete['18_6'][] = 'extension/max/ops/view/setting.html.php';
$config->delete['18_6'][] = 'extension/max/ops/lang/de.php';
$config->delete['18_6'][] = 'extension/max/ops/lang/zh-cn.php';
$config->delete['18_6'][] = 'extension/max/ops/lang/fr.php';
$config->delete['18_6'][] = 'extension/max/ops/lang/zh-tw.php';
$config->delete['18_6'][] = 'extension/max/ops/lang/vi.php';
$config->delete['18_6'][] = 'extension/max/ops/lang/en.php';
$config->delete['18_6'][] = 'extension/max/ops/control.php';
$config->delete['18_6'][] = 'extension/max/ops/js/setting.js';
$config->delete['18_6'][] = 'extension/ipd/host/view/changestatus.html.php';
$config->delete['18_6'][] = 'extension/ipd/host/view/create.html.php';
$config->delete['18_6'][] = 'extension/ipd/host/view/m.browse.html.php';
$config->delete['18_6'][] = 'extension/ipd/host/view/treemap.html.php';
$config->delete['18_6'][] = 'extension/ipd/host/view/m.view.html.php';
$config->delete['18_6'][] = 'extension/ipd/host/view/view.html.php';
$config->delete['18_6'][] = 'extension/ipd/host/view/browse.html.php';
$config->delete['18_6'][] = 'extension/ipd/host/view/edit.html.php';
$config->delete['18_6'][] = 'extension/ipd/host/config.php';
$config->delete['18_6'][] = 'extension/ipd/host/css/enew.vi.css';
$config->delete['18_6'][] = 'extension/ipd/host/css/create.css';
$config->delete['18_6'][] = 'extension/ipd/host/css/view.zh-cn.css';
$config->delete['18_6'][] = 'extension/ipd/host/css/view.en.css';
$config->delete['18_6'][] = 'extension/ipd/host/css/view.css';
$config->delete['18_6'][] = 'extension/ipd/host/css/view.zh-tw.css';
$config->delete['18_6'][] = 'extension/ipd/host/css/edit.css';
$config->delete['18_6'][] = 'extension/ipd/host/css/treemap.css';
$config->delete['18_6'][] = 'extension/ipd/host/model.php';
$config->delete['18_6'][] = 'extension/ipd/host/lang/de.php';
$config->delete['18_6'][] = 'extension/ipd/host/lang/zh-cn.php';
$config->delete['18_6'][] = 'extension/ipd/host/lang/fr.php';
$config->delete['18_6'][] = 'extension/ipd/host/lang/zh-tw.php';
$config->delete['18_6'][] = 'extension/ipd/host/lang/vi.php';
$config->delete['18_6'][] = 'extension/ipd/host/lang/en.php';
$config->delete['18_6'][] = 'extension/ipd/host/control.php';
$config->delete['18_6'][] = 'extension/ipd/host/js/create.js';
$config->delete['18_6'][] = 'extension/ipd/host/js/edit.js';
$config->delete['18_6'][] = 'extension/ipd/account/view/create.html.php';
$config->delete['18_6'][] = 'extension/ipd/account/view/view.html.php';
$config->delete['18_6'][] = 'extension/ipd/account/view/browse.html.php';
$config->delete['18_6'][] = 'extension/ipd/account/view/edit.html.php';
$config->delete['18_6'][] = 'extension/ipd/account/config.php';
$config->delete['18_6'][] = 'extension/ipd/account/model.php';
$config->delete['18_6'][] = 'extension/ipd/account/lang/de.php';
$config->delete['18_6'][] = 'extension/ipd/account/lang/zh-cn.php';
$config->delete['18_6'][] = 'extension/ipd/account/lang/fr.php';
$config->delete['18_6'][] = 'extension/ipd/account/lang/zh-tw.php';
$config->delete['18_6'][] = 'extension/ipd/account/lang/en.php';
$config->delete['18_6'][] = 'extension/ipd/account/control.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/view/create.html.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/view/m.browse.html.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/view/m.view.html.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/view/view.html.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/view/browse.html.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/view/edit.html.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/config.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/css/browse.css';
$config->delete['18_6'][] = 'extension/ipd/serverroom/model.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/lang/de.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/lang/zh-cn.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/lang/fr.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/lang/zh-tw.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/lang/vi.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/lang/en.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/control.php';
$config->delete['18_6'][] = 'extension/ipd/serverroom/js/custom.js';
$config->delete['18_6'][] = 'extension/ipd/ops/view/setting.html.php';
$config->delete['18_6'][] = 'extension/ipd/ops/lang/de.php';
$config->delete['18_6'][] = 'extension/ipd/ops/lang/zh-cn.php';
$config->delete['18_6'][] = 'extension/ipd/ops/lang/fr.php';
$config->delete['18_6'][] = 'extension/ipd/ops/lang/zh-tw.php';
$config->delete['18_6'][] = 'extension/ipd/ops/lang/vi.php';
$config->delete['18_6'][] = 'extension/ipd/ops/lang/en.php';
$config->delete['18_6'][] = 'extension/ipd/ops/control.php';
$config->delete['18_6'][] = 'extension/ipd/ops/js/setting.js';
$config->delete['18_6'][] = 'module/gitlab/js/browseproject.js';
$config->delete['18_8'][] = 'extension/biz/screen/ext/control/ajaxgetchart.php';
$config->delete['18_8'][] = 'extension/max/screen/ext/control/ajaxgetchart.php';
$config->delete['18_8'][] = 'extension/ipd/screen/ext/control/ajaxgetchart.php';
$config->delete['18_9'][] = 'module/automation/control.php';
$config->delete['18_9'][] = 'module/automation/model.php';
$config->delete['18_9'][] = 'module/automation/config.php';
$config->delete['18_9'][] = 'module/automation/css/browse.css';
$config->delete['18_9'][] = 'module/automation/css/browse.en.css';
$config->delete['18_9'][] = 'module/automation/lang/zh-cn.php';
$config->delete['18_9'][] = 'module/automation/lang/fr.php';
$config->delete['18_9'][] = 'module/automation/lang/zh-tw.php';
$config->delete['18_9'][] = 'module/automation/lang/de.php';
$config->delete['18_9'][] = 'module/automation/lang/en.php';
$config->delete['18_9'][] = 'module/automation/view/browse.html.php';

$config->delete['18_12'][] = 'extension/ipd/project/ext/model/ipd.php';

$config->delete['20_0_beta1'][] = 'extension/lite/kanban/ext/model/lite.php';
$config->delete['20_0_beta1'][] = 'extension/biz/my/ext/model/class/effort.class.php';
$config->delete['20_0_beta1'][] = 'extension/max/my/ext/model/class/effort.class.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/my/ext/model/class/effort.class.php';
$config->delete['20_0_beta1'][] = 'extension/biz/todo/ext/control/batchcreate.php';
$config->delete['20_0_beta1'][] = 'extension/max/todo/ext/control/batchcreate.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/todo/ext/control/batchcreate.php';
$config->delete['20_0_beta1'][] = 'extension/biz/bug/ext/control/export.php';
$config->delete['20_0_beta1'][] = 'extension/max/bug/ext/control/export.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/bug/ext/control/export.php';
$config->delete['20_0_beta1'][] = 'extension/biz/block/ext/view/calendarblock.html.php';
$config->delete['20_0_beta1'][] = 'extension/max/block/ext/view/calendarblock.html.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/block/ext/view/calendarblock.html.php';
$config->delete['20_0_beta1'][] = 'extension/biz/block/ext/view/calendarblock.html.php';
$config->delete['20_0_beta1'][] = 'extension/max/block/ext/view/calendarblock.html.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/block/ext/view/calendarblock.html.php';
$config->delete['20_0_beta1'][] = 'extension/biz/block/ext/config/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/max/block/ext/config/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/block/ext/config/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/biz/block/ext/lang/zh-cn/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/max/block/ext/lang/zh-cn/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/block/ext/lang/zh-cn/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/biz/block/ext/lang/zh-tw/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/max/block/ext/lang/zh-tw/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/block/ext/lang/zh-tw/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/biz/block/ext/lang/de/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/max/block/ext/lang/de/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/block/ext/lang/de/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/biz/block/ext/lang/en/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/max/block/ext/lang/en/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/block/ext/lang/en/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/biz/block/ext/lang/fr/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/max/block/ext/lang/fr/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/block/ext/lang/fr/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/biz/block/ext/lang/vi/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/max/block/ext/lang/vi/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/block/ext/lang/vi/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/biz/block/ext/model/class/calendar.class.php';
$config->delete['20_0_beta1'][] = 'extension/max/block/ext/model/class/calendar.class.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/block/ext/model/class/calendar.class.php';
$config->delete['20_0_beta1'][] = 'extension/biz/block/ext/model/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/max/block/ext/model/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/block/ext/model/calendar.php';
$config->delete['20_0_beta1'][] = 'extension/max/admin/ext/config/repo.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/admin/ext/config/repo.php';
$config->delete['20_0_beta1'][] = 'extension/biz/admin/ext/config/repo.php';
$config->delete['20_0_beta1'][] = 'extension/biz/common/view/flowchart.html.php';
$config->delete['20_0_beta1'][] = 'extension/max/common/view/flowchart.html.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/common/view/flowchart.html.php';
$config->delete['20_0_beta1'][] = 'extension/biz/workflow/css/flowchart.css';
$config->delete['20_0_beta1'][] = 'extension/max/workflow/css/flowchart.css';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflow/css/flowchart.css';
$config->delete['20_0_beta1'][] = 'extension/biz/workflow/js/flowchart.js';
$config->delete['20_0_beta1'][] = 'extension/max/workflow/js/flowchart.js';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflow/js/flowchart.js';
$config->delete['20_0_beta1'][] = 'extension/biz/workflow/view/flowchart.html.php';
$config->delete['20_0_beta1'][] = 'extension/max/workflow/view/flowchart.html.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflow/view/flowchart.html.php';
$config->delete['20_0_beta1'][] = 'extension/biz/workflow/ext/control/delete.php';
$config->delete['20_0_beta1'][] = 'extension/max/workflow/ext/control/delete.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflow/ext/control/delete.php';
$config->delete['20_0_beta1'][] = 'extension/biz/workflow/ext/control/browseflow.php';
$config->delete['20_0_beta1'][] = 'extension/max/workflow/ext/control/browseflow.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflow/ext/control/browseflow.php';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowrule/ext/css/browse/flow.css';
$config->delete['20_0_beta1'][] = 'extension/max/workflowrule/ext/css/browse/flow.css';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowrule/ext/css/browse/flow.css';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowrule/ext/css/create/flow.css';
$config->delete['20_0_beta1'][] = 'extension/max/workflowrule/ext/css/create/flow.css';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowrule/ext/css/create/flow.css';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowrule/ext/css/edit/flow.css';
$config->delete['20_0_beta1'][] = 'extension/max/workflowrule/ext/css/edit/flow.css';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowrule/ext/css/edit/flow.css';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowrule/ext/model/flow.php';
$config->delete['20_0_beta1'][] = 'extension/max/workflowrule/ext/model/flow.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowrule/ext/model/flow.php';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowrule/ext/model/class/flow.class.php';
$config->delete['20_0_beta1'][] = 'extension/max/workflowrule/ext/model/class/flow.class.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowrule/ext/model/class/flow.class.php';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowrule/ext/view/browse.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/workflowrule/ext/view/browse.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowrule/ext/view/browse.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowrule/ext/view/create.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/workflowrule/ext/view/create.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowrule/ext/view/create.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowrule/ext/view/edit.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/workflowrule/ext/view/edit.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowrule/ext/view/edit.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/my/ext/view/effort.calendar.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/my/ext/view/effort.calendar.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/my/ext/view/effort.calendar.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/task/ext/view/view.effort.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/task/ext/view/view.effort.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/task/ext/view/view.effort.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/todo/ext/view/batchcreate.calendar.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/todo/ext/view/batchcreate.calendar.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/todo/ext/view/batchcreate.calendar.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/doc/ext/view/view.effort.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/doc/ext/view/view.effort.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/doc/ext/view/view.effort.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/execution/ext/view/effort.calendar.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/execution/ext/view/effort.calendar.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/execution/ext/view/effort.calendar.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/execution/ext/view/featurebar.calendar.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/execution/ext/view/featurebar.calendar.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/execution/ext/view/featurebar.calendar.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/effort/view/footer.html.php';
$config->delete['20_0_beta1'][] = 'extension/max/effort/view/footer.html.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/effort/view/footer.html.php';
$config->delete['20_0_beta1'][] = 'extension/biz/effort/view/effortsforobject.html.php';
$config->delete['20_0_beta1'][] = 'extension/max/effort/view/effortsforobject.html.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/effort/view/effortsforobject.html.php';
$config->delete['20_0_beta1'][] = 'extension/biz/user/ext/view/featurebar.calendar.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/user/ext/view/featurebar.calendar.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/user/ext/view/featurebar.calendar.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/bug/ext/view/import.html.php';
$config->delete['20_0_beta1'][] = 'extension/max/bug/ext/view/import.html.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/bug/ext/view/import.html.php';
$config->delete['20_0_beta1'][] = 'extension/biz/testcase/ext/browse.excel.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/testcase/ext/browse.excel.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/testcase/ext/browse.excel.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/custom/ext/view/set.feedback.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/custom/ext/view/set.feedback.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/custom/ext/view/set.feedback.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowdatasource/ext/css/browse/flow.css';
$config->delete['20_0_beta1'][] = 'extension/max/workflowdatasource/ext/css/browse/flow.css';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowdatasource/ext/css/browse/flow.css';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowdatasource/ext/css/create/flow.css';
$config->delete['20_0_beta1'][] = 'extension/max/workflowdatasource/ext/css/create/flow.css';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowdatasource/ext/css/create/flow.css';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowdatasource/ext/css/edit/flow.css';
$config->delete['20_0_beta1'][] = 'extension/max/workflowdatasource/ext/css/edit/flow.css';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowdatasource/ext/css/edit/flow.css';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowdatasource/ext/model/class/flow.class.php';
$config->delete['20_0_beta1'][] = 'extension/max/workflowdatasource/ext/model/class/flow.class.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowdatasource/ext/model/class/flow.class.php';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowdatasource/ext/model/flow.php';
$config->delete['20_0_beta1'][] = 'extension/max/workflowdatasource/ext/model/flow.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowdatasource/ext/model/flow.php';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowdatasource/ext/view/browse.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/workflowdatasource/ext/view/browse.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowdatasource/ext/view/browse.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowdatasource/ext/view/create.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/workflowdatasource/ext/view/create.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowdatasource/ext/view/create.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/workflowdatasource/ext/view/edit.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/workflowdatasource/ext/view/edit.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/workflowdatasource/ext/view/edit.flow.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/action/ext/model/hook/undelete.bizext.php';
$config->delete['20_0_beta1'][] = 'extension/max/action/ext/model/hook/undelete.bizext.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/action/ext/model/hook/undelete.bizext.php';
$config->delete['20_0_beta1'][] = 'extension/biz/admin/ext/view/index.bizext.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/admin/ext/view/index.bizext.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/admin/ext/view/index.bizext.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/misc/ext/view/about.bizext.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/misc/ext/view/about.bizext.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/misc/ext/view/about.bizext.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/report/ext/js/common/bizext.js';
$config->delete['20_0_beta1'][] = 'extension/max/report/ext/js/common/bizext.js';
$config->delete['20_0_beta1'][] = 'extension/ipd/report/ext/js/common/bizext.js';
$config->delete['20_0_beta1'][] = 'extension/biz/sso/ext/model/class/bizext.class.php';
$config->delete['20_0_beta1'][] = 'extension/max/sso/ext/model/class/bizext.class.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/sso/ext/model/class/bizext.class.php';
$config->delete['20_0_beta1'][] = 'extension/biz/task/ext/view/edit.bizext.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/task/ext/view/edit.bizext.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/task/ext/view/edit.bizext.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/upgrade/ext/view/afterexec.bizext.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/upgrade/ext/view/afterexec.bizext.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/upgrade/ext/view/afterexec.bizext.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/biz/upgrade/ext/view/execute.bizext.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/max/upgrade/ext/view/execute.bizext.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/ipd/upgrade/ext/view/execute.bizext.html.hook.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/config.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/control.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/model.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/activate.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/affected.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/assignto.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/batchcreate.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/browse.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/change.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/close.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/create.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/distribute.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/editdraft.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/edit.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/editnormal.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/export.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/retract.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/review.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/sendmail.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/submitreview.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/view/view.html.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/css/batchcreate.css';
$config->delete['20_0_beta1'][] = 'extension/or/demand/css/browse.css';
$config->delete['20_0_beta1'][] = 'extension/or/demand/css/close.en.css';
$config->delete['20_0_beta1'][] = 'extension/or/demand/css/create.css';
$config->delete['20_0_beta1'][] = 'extension/or/demand/css/distribute.css';
$config->delete['20_0_beta1'][] = 'extension/or/demand/css/edit.css';
$config->delete['20_0_beta1'][] = 'extension/or/demand/css/review.en.css';
$config->delete['20_0_beta1'][] = 'extension/or/demand/css/submitreview.css';
$config->delete['20_0_beta1'][] = 'extension/or/demand/css/view.css';
$config->delete['20_0_beta1'][] = 'extension/or/demand/js/batchcreate.js';
$config->delete['20_0_beta1'][] = 'extension/or/demand/js/browse.js';
$config->delete['20_0_beta1'][] = 'extension/or/demand/js/change.js';
$config->delete['20_0_beta1'][] = 'extension/or/demand/js/common.js';
$config->delete['20_0_beta1'][] = 'extension/or/demand/js/create.js';
$config->delete['20_0_beta1'][] = 'extension/or/demand/js/distribute.js';
$config->delete['20_0_beta1'][] = 'extension/or/demand/js/edit.js';
$config->delete['20_0_beta1'][] = 'extension/or/demand/js/review.js';
$config->delete['20_0_beta1'][] = 'extension/or/demand/js/submitreview.js';
$config->delete['20_0_beta1'][] = 'extension/or/demand/lang/de.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/lang/en.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/lang/fr.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/lang/zh-cn.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/ext/model/class/feedback.class.php';
$config->delete['20_0_beta1'][] = 'extension/or/demand/ext/model/feedback.php';
$config->delete['20_0_beta1'][] = 'module/metric/css/browse.ui.css';
$config->delete['20_0_beta1'][] = 'module/metric/css/create.ui.css';
$config->delete['20_0_beta1'][] = 'module/metric/css/edit.ui.css';
$config->delete['20_0_beta1'][] = 'module/metric/css/implement.ui.css';
$config->delete['20_0_beta1'][] = 'module/metric/css/view.ui.css';
$config->delete['20_0_beta1'][] = 'module/metric/js/browse.ui.js';
$config->delete['20_0_beta1'][] = 'module/metric/js/create.ui.js';
$config->delete['20_0_beta1'][] = 'module/metric/js/implement.ui.js';
$config->delete['20_0_beta1'][] = 'module/metric/js/view.ui.js';
$config->delete['20_0_beta1'][] = 'module/metric/template/metric.php.tmp';
$config->delete['20_0_beta1'][] = 'module/metric/ui/browse.html.php';
$config->delete['20_0_beta1'][] = 'module/metric/ui/create.html.php';
$config->delete['20_0_beta1'][] = 'module/metric/ui/edit.html.php';
$config->delete['20_0_beta1'][] = 'module/metric/ui/implement.html.php';
$config->delete['20_0_beta1'][] = 'module/metric/ui/view.html.php';
$config->delete['20_0_beta1'][] = 'module/metric/ui/viewphpmetric.html.php';
$config->delete['20_0_beta1'][] = 'module/metric/ui/viewsqlmetric.html.php';

$config->delete['20_0_beta2'][] = 'module/install/css/showtableprogress.ui.css';
$config->delete['20_0_beta2'][] = 'module/install/css/step1.ui.css';
$config->delete['20_0_beta2'][] = 'module/install/css/step2.ui.css';
$config->delete['20_0_beta2'][] = 'module/install/css/step5.ui.css';
$config->delete['20_0_beta2'][] = 'module/dimension/view/ajaxgetdropmenu.html.php';
$config->delete['20_0_beta2'][] = 'extension/lite/story/ext/ui/view.html.php';

$config->delete['20_0'][] = 'extension/biz/task/ext/control/create.php';
$config->delete['20_0'][] = 'extension/max/task/ext/control/create.php';
$config->delete['20_0'][] = 'extension/ipd/task/ext/control/create.php';
$config->delete['20_0'][] = 'extension/biz/story/ext/view/relation.html.php';
$config->delete['20_0'][] = 'extension/max/story/ext/view/relation.html.php';
$config->delete['20_0'][] = 'extension/ipd/story/ext/view/relation.html.php';
$config->delete['20_0'][] = 'extension/biz/attend/ext/control/browse.php';
$config->delete['20_0'][] = 'extension/max/attend/ext/control/browse.php';
$config->delete['20_0'][] = 'extension/ipd/attend/ext/control/browse.php';
$config->delete['20_0'][] = 'extension/biz/attend/ext/control/browsereview.php';
$config->delete['20_0'][] = 'extension/max/attend/ext/control/browsereview.php';
$config->delete['20_0'][] = 'extension/ipd/attend/ext/control/browsereview.php';
$config->delete['20_0'][] = 'extension/biz/attend/ext/control/detail.php';
$config->delete['20_0'][] = 'extension/max/attend/ext/control/detail.php';
$config->delete['20_0'][] = 'extension/ipd/attend/ext/control/detail.php';
$config->delete['20_0'][] = 'extension/biz/attend/ext/control/exportdetail.php';
$config->delete['20_0'][] = 'extension/max/attend/ext/control/exportdetail.php';
$config->delete['20_0'][] = 'extension/ipd/attend/ext/control/exportdetail.php';
$config->delete['20_0'][] = 'extension/biz/attend/ext/control/exportstat.php';
$config->delete['20_0'][] = 'extension/max/attend/ext/control/exportstat.php';
$config->delete['20_0'][] = 'extension/ipd/attend/ext/control/exportstat.php';
$config->delete['20_0'][] = 'extension/biz/attend/ext/control/personal.php';
$config->delete['20_0'][] = 'extension/max/attend/ext/control/personal.php';
$config->delete['20_0'][] = 'extension/ipd/attend/ext/control/personal.php';
$config->delete['20_0'][] = 'extension/biz/attend/ext/control/review.php';
$config->delete['20_0'][] = 'extension/max/attend/ext/control/review.php';
$config->delete['20_0'][] = 'extension/ipd/attend/ext/control/review.php';
$config->delete['20_0'][] = 'extension/biz/attend/ext/control/stat.php';
$config->delete['20_0'][] = 'extension/max/attend/ext/control/stat.php';
$config->delete['20_0'][] = 'extension/ipd/attend/ext/control/stat.php';
$config->delete['20_0'][] = 'extension/biz/feedback/ext/ui/admin.excel.html.hook.php';
$config->delete['20_0'][] = 'extension/max/feedback/ext/ui/admin.excel.html.hook.php';
$config->delete['20_0'][] = 'extension/ipd/feedback/ext/ui/admin.excel.html.hook.php';
$config->delete['20_0'][] = 'module/story/ui/linkstories.html.php';
$config->delete['20_0'][] = 'extension/or/product/ext/control/batchedit.php';

$config->delete['20_1'][] = 'module/gitfox';
$config->delete['20_1'][] = 'module/artifactrepo';
$config->delete['20_1'][] = 'execution/biz/common/ext/lang/zh-tw/report.php';
$config->delete['20_1'][] = 'execution/biz/common/ext/lang/zh-cn/report.php';
$config->delete['20_1'][] = 'execution/biz/common/ext/lang/en/report.php';
$config->delete['20_1'][] = 'execution/biz/common/ext/lang/fr/report.php';
$config->delete['20_1'][] = 'execution/biz/common/ext/lang/de/report.php';
$config->delete['20_1'][] = 'execution/max/common/ext/lang/zh-tw/report.php';
$config->delete['20_1'][] = 'execution/max/common/ext/lang/zh-cn/report.php';
$config->delete['20_1'][] = 'execution/max/common/ext/lang/en/report.php';
$config->delete['20_1'][] = 'execution/max/common/ext/lang/fr/report.php';
$config->delete['20_1'][] = 'execution/max/common/ext/lang/de/report.php';
$config->delete['20_1'][] = 'execution/ipd/common/ext/lang/zh-tw/report.php';
$config->delete['20_1'][] = 'execution/ipd/common/ext/lang/zh-cn/report.php';
$config->delete['20_1'][] = 'execution/ipd/common/ext/lang/en/report.php';
$config->delete['20_1'][] = 'execution/ipd/common/ext/lang/fr/report.php';
$config->delete['20_1'][] = 'execution/ipd/common/ext/lang/de/report.php';
$config->delete['20_1'][] = 'execution/biz/bug/ext/ui/view.effort.html.hook.php';
$config->delete['20_1'][] = 'execution/biz/story/ext/ui/view.effort.html.hook.php';
$config->delete['20_1'][] = 'execution/biz/testcase/ext/ui/view.effort.html.hook.php';
$config->delete['20_1'][] = 'execution/max/bug/ext/ui/view.effort.html.hook.php';
$config->delete['20_1'][] = 'execution/max/story/ext/ui/view.effort.html.hook.php';
$config->delete['20_1'][] = 'execution/max/testcase/ext/ui/view.effort.html.hook.php';
$config->delete['20_1'][] = 'execution/ipd/bug/ext/ui/view.effort.html.hook.php';
$config->delete['20_1'][] = 'execution/ipd/story/ext/ui/view.effort.html.hook.php';
$config->delete['20_1'][] = 'execution/ipd/testcase/ext/ui/view.effort.html.hook.php';

$config->delete['20_1_1'][] = 'extension/max/design/ext/ui/browse.zentamax.html.hook.php';
$config->delete['20_1_1'][] = 'extension/max/doc/ext/ui/create.zentamax.html.hook.php';
$config->delete['20_1_1'][] = 'extension/max/doc/ext/view/create.zentamax.html.hook.php';

$config->delete['20_3_0'][] = 'extension/ipd/story/ext/tao/class/ipd.class.php';
$config->delete['20_3_0'][] = 'extension/ipd/story/ext/tao/ipd.php';
$config->delete['20_3_0'][] = 'extension/ipd/project/ext/js/create/ipd.ui.js';
$config->delete['20_3_0'][] = 'extension/ipd/project/ext/js/edit/ipd.ui.js';

$config->delete['20_4'][] = 'extension/max/repo/ext/config/repo.php';
$config->delete['20_4'][] = 'extension/max/repo/ext/lang/de/zentaomax.php';
$config->delete['20_4'][] = 'extension/max/repo/ext/lang/en/zentaomax.php';
$config->delete['20_4'][] = 'extension/max/repo/ext/lang/fr/zentaomax.php';
$config->delete['20_4'][] = 'extension/max/repo/ext/lang/zh-cn/zentaomax.php';
$config->delete['20_4'][] = 'extension/max/repo/ext/model/zentaomax.php';
$config->delete['20_4'][] = 'extension/max/repo/ext/view/log.html.php';
$config->delete['20_4'][] = 'extension/ipd/repo/ext/config/repo.php';
$config->delete['20_4'][] = 'extension/ipd/repo/ext/lang/de/zentaomax.php';
$config->delete['20_4'][] = 'extension/ipd/repo/ext/lang/en/zentaomax.php';
$config->delete['20_4'][] = 'extension/ipd/repo/ext/lang/fr/zentaomax.php';
$config->delete['20_4'][] = 'extension/ipd/repo/ext/lang/zh-cn/zentaomax.php';
$config->delete['20_4'][] = 'extension/ipd/repo/ext/model/zentaomax.php';
$config->delete['20_4'][] = 'extension/ipd/repo/ext/view/log.html.php';
$config->delete['20_4'][] = 'extension/xuan/index/ext/config/xuan.php';

$config->delete['20_5'][] = 'extension/custom/my/ext/control/preference.php';
$config->delete['20_5'][] = 'module/design/js/browse.ui.js';

$config->delete['20_6'][] = 'extension/biz/pivot/ext/zen/pivot.php';
$config->delete['20_6'][] = 'extension/max/pivot/ext/zen/pivot.php';
$config->delete['20_6'][] = 'extension/ipd/pivot/ext/zen/pivot.php';
$config->delete['20_6'][] = 'extension/biz/dataview/table';
$config->delete['20_6'][] = 'extension/max/dataview/table';
$config->delete['20_6'][] = 'extension/ipd/dataview/table';

$config->delete['20_7'][] = 'extension/biz/user/ext/model/hook/create.bizext.php';
$config->delete['20_7'][] = 'extension/max/user/ext/model/hook/create.bizext.php';
$config->delete['20_7'][] = 'extension/ipd/user/ext/model/hook/create.bizext.php';

$config->delete['20_8'][] = 'extension/biz/pivot/ext/js/design/step1.ui.js';
$config->delete['20_8'][] = 'extension/biz/pivot/ext/js/design/step2.ui.js';
$config->delete['20_8'][] = 'extension/biz/pivot/ext/js/design/step3.ui.js';
$config->delete['20_8'][] = 'extension/biz/pivot/ext/js/design/step4.ui.js';
$config->delete['20_8'][] = 'extension/biz/pivot/ext/js/design/step5.ui.js';
$config->delete['20_8'][] = 'extension/biz/pivot/ext/ui/block/step1.html.php';
$config->delete['20_8'][] = 'extension/biz/pivot/ext/ui/block/step2.html.php';
$config->delete['20_8'][] = 'extension/biz/pivot/ext/ui/block/step3.html.php';
$config->delete['20_8'][] = 'extension/biz/pivot/ext/ui/block/step4.html.php';
$config->delete['20_8'][] = 'extension/biz/pivot/ext/ui/block/step5.html.php';
$config->delete['20_8'][] = 'extension/max/pivot/ext/js/design/step1.ui.js';
$config->delete['20_8'][] = 'extension/max/pivot/ext/js/design/step2.ui.js';
$config->delete['20_8'][] = 'extension/max/pivot/ext/js/design/step3.ui.js';
$config->delete['20_8'][] = 'extension/max/pivot/ext/js/design/step4.ui.js';
$config->delete['20_8'][] = 'extension/max/pivot/ext/js/design/step5.ui.js';
$config->delete['20_8'][] = 'extension/max/pivot/ext/ui/block/step1.html.php';
$config->delete['20_8'][] = 'extension/max/pivot/ext/ui/block/step2.html.php';
$config->delete['20_8'][] = 'extension/max/pivot/ext/ui/block/step3.html.php';
$config->delete['20_8'][] = 'extension/max/pivot/ext/ui/block/step4.html.php';
$config->delete['20_8'][] = 'extension/max/pivot/ext/ui/block/step5.html.php';
$config->delete['20_8'][] = 'extension/ipd/pivot/ext/js/design/step1.ui.js';
$config->delete['20_8'][] = 'extension/ipd/pivot/ext/js/design/step2.ui.js';
$config->delete['20_8'][] = 'extension/ipd/pivot/ext/js/design/step3.ui.js';
$config->delete['20_8'][] = 'extension/ipd/pivot/ext/js/design/step4.ui.js';
$config->delete['20_8'][] = 'extension/ipd/pivot/ext/js/design/step5.ui.js';
$config->delete['20_8'][] = 'extension/ipd/pivot/ext/ui/block/step1.html.php';
$config->delete['20_8'][] = 'extension/ipd/pivot/ext/ui/block/step2.html.php';
$config->delete['20_8'][] = 'extension/ipd/pivot/ext/ui/block/step3.html.php';
$config->delete['20_8'][] = 'extension/ipd/pivot/ext/ui/block/step4.html.php';
$config->delete['20_8'][] = 'extension/ipd/pivot/ext/ui/block/step5.html.php';
$config->delete['20_8'][] = 'extension/max/git';
$config->delete['20_8'][] = 'extension/ipd/git';
$config->delete['20_8'][] = 'extension/max/svn';
$config->delete['20_8'][] = 'extension/ipd/svn';
$config->delete['20_8'][] = 'extension/biz/workflow/ext/model/flow.php';
$config->delete['20_8'][] = 'extension/biz/workflow/ext/model/class/flow.class.php';
$config->delete['20_8'][] = 'extension/max/workflow/ext/model/flow.php';
$config->delete['20_8'][] = 'extension/max/workflow/ext/model/class/flow.class.php';
$config->delete['20_8'][] = 'extension/ipd/workflow/ext/model/flow.php';
$config->delete['20_8'][] = 'extension/ipd/workflow/ext/model/class/flow.class.php';
$config->delete['20_8'][] = 'extension/biz/workflow/ext/control/ajaxgetapps.php';
$config->delete['20_8'][] = 'extension/max/workflow/ext/control/ajaxgetapps.php';
$config->delete['20_8'][] = 'extension/ipd/workflow/ext/control/ajaxgetapps.php';
$config->delete['20_9'][] = 'extension/biz/index/ext/config/repo.php';
$config->delete['20_9'][] = 'extension/max/index/ext/config/repo.php';
$config->delete['20_9'][] = 'extension/ipd/index/ext/config/repo.php';
$config->delete['20_9'][] = 'extension/biz/common/ext/lang/zh-cn/ops.php';
$config->delete['20_9'][] = 'extension/max/common/ext/lang/zh-cn/ops.php';
$config->delete['20_9'][] = 'extension/ipd/common/ext/lang/zh-cn/ops.php';
$config->delete['20_9'][] = 'extension/biz/common/ext/lang/zh-tw/ops.php';
$config->delete['20_9'][] = 'extension/max/common/ext/lang/zh-tw/ops.php';
$config->delete['20_9'][] = 'extension/ipd/common/ext/lang/zh-tw/ops.php';
$config->delete['20_9'][] = 'extension/biz/common/ext/lang/en/ops.php';
$config->delete['20_9'][] = 'extension/max/common/ext/lang/en/ops.php';
$config->delete['20_9'][] = 'extension/ipd/common/ext/lang/en/ops.php';
$config->delete['20_9'][] = 'extension/biz/common/ext/lang/de/ops.php';
$config->delete['20_9'][] = 'extension/max/common/ext/lang/de/ops.php';
$config->delete['20_9'][] = 'extension/ipd/common/ext/lang/de/ops.php';
$config->delete['20_9'][] = 'extension/biz/common/ext/lang/fr/ops.php';
$config->delete['20_9'][] = 'extension/max/common/ext/lang/fr/ops.php';
$config->delete['20_9'][] = 'extension/ipd/common/ext/lang/fr/ops.php';
$config->delete['20_9'][] = 'extension/biz/deploy/js/steps.ui.js';
$config->delete['20_9'][] = 'extension/max/deploy/js/steps.ui.js';
$config->delete['20_9'][] = 'extension/ipd/deploy/js/steps.ui.js';
$config->delete['20_9'][] = 'extension/biz/deploy/ui/scope.html.php';
$config->delete['20_9'][] = 'extension/max/deploy/ui/scope.html.php';
$config->delete['20_9'][] = 'extension/ipd/deploy/ui/scope.html.php';
$config->delete['20_9'][] = 'extension/biz/deploy/ui/scope.html.php';
$config->delete['20_9'][] = 'extension/max/deploy/ui/managescope.html.php';
$config->delete['20_9'][] = 'extension/ipd/deploy/ui/managescope.html.php';
$config->delete['20_9'][] = 'extension/biz/deploy/js/managescope.ui.js';
$config->delete['20_9'][] = 'extension/max/deploy/js/managescope.ui.js';
$config->delete['20_9'][] = 'extension/ipd/deploy/js/managescope.ui.js';
$config->delete['20_9'][] = 'extension/biz/deploy/js/finish.ui.js';
$config->delete['20_9'][] = 'extension/max/deploy/js/finish.ui.js';
$config->delete['20_9'][] = 'extension/ipd/deploy/js/finish.ui.js';
$config->delete['20_9'][] = 'module/account/';
$config->delete['20_9'][] = 'module/host/js/create.ui.js';
$config->delete['20_9'][] = 'module/host/js/edit.ui.js';
$config->delete['21_1'][] = 'module/admin/ui/cache.html.php';
$config->delete['21_2'][] = 'extension/biz/service/';
$config->delete['21_2'][] = 'extension/max/service/';
$config->delete['21_2'][] = 'extension/biz/host/';
$config->delete['21_2'][] = 'extension/max/host/';
$config->delete['21_2'][] = 'extension/ipd/host/';
$config->delete['21_2'][] = 'extension/biz/domain/';
$config->delete['21_2'][] = 'extension/max/domain/';
$config->delete['21_2'][] = 'extension/ipd/domain/';
$config->delete['21_2'][] = 'module/ops/';

$config->delete['21_3'][] = 'extension/ipd/project/ext/ui/view.ipd.html.hook.php';
$config->delete['21_3'][] = 'extension/or/story/ext/zen/story.php';
$config->delete['21_3'][] = 'extension/biz/group/ext/lang/de/sqlbuilder.php';
$config->delete['21_3'][] = 'extension/biz/group/ext/lang/en/sqlbuilder.php';
$config->delete['21_3'][] = 'extension/biz/group/ext/lang/fr/sqlbuilder.php';
$config->delete['21_3'][] = 'extension/biz/group/ext/lang/zh-cn/sqlbuilder.php';
$config->delete['21_3'][] = 'extension/max/group/ext/lang/de/sqlbuilder.php';
$config->delete['21_3'][] = 'extension/max/group/ext/lang/en/sqlbuilder.php';
$config->delete['21_3'][] = 'extension/max/group/ext/lang/fr/sqlbuilder.php';
$config->delete['21_3'][] = 'extension/max/group/ext/lang/zh-cn/sqlbuilder.php';
$config->delete['21_3'][] = 'extension/ipd/group/ext/lang/de/sqlbuilder.php';
$config->delete['21_3'][] = 'extension/ipd/group/ext/lang/en/sqlbuilder.php';
$config->delete['21_3'][] = 'extension/ipd/group/ext/lang/fr/sqlbuilder.php';
$config->delete['21_3'][] = 'extension/ipd/group/ext/lang/zh-cn/sqlbuilder.php';
$config->delete['21_3'][] = 'extension/biz/bug/ext/control/view.php';
$config->delete['21_3'][] = 'extension/max/bug/ext/control/view.php';
$config->delete['21_3'][] = 'extension/ipd/bug/ext/control/view.php';

$config->delete['21_6_beta'][] = 'extension/biz/doc/ext/control/create.php';
$config->delete['21_6_beta'][] = 'extension/max/doc/ext/control/create.php';
$config->delete['21_6_beta'][] = 'extension/ipd/doc/ext/control/create.php';
$config->delete['21_6_beta'][] = 'extension/biz/common/ext/lang/de/effort.php';
$config->delete['21_6_beta'][] = 'extension/biz/common/ext/lang/en/effort.php';
$config->delete['21_6_beta'][] = 'extension/biz/common/ext/lang/fr/effort.php';
$config->delete['21_6_beta'][] = 'extension/biz/common/ext/lang/zh-cn/effort.php';
$config->delete['21_6_beta'][] = 'extension/max/common/ext/lang/de/effort.php';
$config->delete['21_6_beta'][] = 'extension/max/common/ext/lang/en/effort.php';
$config->delete['21_6_beta'][] = 'extension/max/common/ext/lang/fr/effort.php';
$config->delete['21_6_beta'][] = 'extension/max/common/ext/lang/zh-cn/effort.php';
$config->delete['21_6_beta'][] = 'extension/ipd/common/ext/lang/de/effort.php';
$config->delete['21_6_beta'][] = 'extension/ipd/common/ext/lang/en/effort.php';
$config->delete['21_6_beta'][] = 'extension/ipd/common/ext/lang/fr/effort.php';
$config->delete['21_6_beta'][] = 'extension/ipd/common/ext/lang/zh-cn/effort.php';

$config->delete['21_6_beta'][] = 'extension/ipd/project/ext/model/class/zentaoipd.class.php';
$config->delete['21_6_beta'][] = 'extension/max/project/ext/model/class/zentaoipd.class.php';
$config->delete['21_6_beta'][] = 'extension/biz/project/ext/model/class/zentaoipd.class.php';
$config->delete['21_6_beta'][] = 'extension/ipd/project/ext/model/zentaoipd.php';
$config->delete['21_6_beta'][] = 'extension/max/project/ext/model/zentaoipd.php';
$config->delete['21_6_beta'][] = 'extension/biz/project/ext/model/zentaoipd.php';

$config->upgrade->openModules  = array('action', 'admin', 'ai', 'bi', 'aiapp', 'api', 'automation', 'backup', 'block', 'branch', 'budget', 'bug', 'build', 'cache', 'caselib', 'chart', 'ci', 'client', 'common', 'company', 'compile', 'convert', 'cron', 'custom', 'datatable', 'dataview', 'dept', 'design', 'dev', 'dimension', 'doc', 'durationestimation', 'entry', 'execution', 'extension', 'file', 'git', 'gitlab', 'group', 'holiday', 'im', 'index', 'index.html', 'install', 'issue', 'jenkins', 'job', 'kanban', 'license', 'mail', 'message', 'metric', 'misc', 'mr', 'my', 'personnel', 'pipeline', 'product', 'productplan', 'productset', 'program', 'programplan', 'project', 'projectbuild', 'projectplan', 'projectrelease', 'projectstory', 'pivot', 'qa', 'release', 'repo', 'report', 'risk', 'score', 'screen', 'search', 'setting', 'sonarqube', 'sso', 'stage', 'stakeholder', 'story', 'subject', 'svn', 'task', 'testcase', 'testreport', 'testsuite', 'testtask', 'todo', 'tree', 'tutorial', 'upgrade', 'user', 'webhook', 'weekly', 'workestimation', 'gitea', 'gogs', 'transfer', 'zahost', 'zanode', 'editor', 'charter', 'roadmap', 'account', 'cne', 'host', 'instance', 'ops', 'serverroom', 'space', 'store', 'system', 'solution', 'demand', 'gitfox', 'epic', 'requirement', 'mark');
$config->upgrade->unsetModules = array('design', 'program', 'programplan', 'projectbuild', 'projectrelease', 'stage', 'stakeholder', 'product', 'branch', 'productplan', 'release', 'build', 'qa', 'bug', 'testcase', 'testtask', 'testreport', 'testsuite', 'caselib', 'automation', 'repo', 'ci', 'compile', 'jenkins', 'job', 'svn', 'gitlab', 'sonarqube', 'mr', 'git', 'report', 'sqlbuilder', 'feedback', 'faq', 'attend', 'holiday', 'leave', 'makeup', 'overtime', 'lieu', 'ops', 'host', 'serverroom', 'account', 'domain', 'service', 'deploy', 'conference', 'traincourse', 'pssp', 'baseline', 'classify', 'cm', 'cmcl', 'auditcl', 'reviewcl', 'process', 'activity', 'zoutput', 'auditplan', 'nc', 'subject', 'weekly', 'workestimation', 'issue', 'durationestimation', 'risk', 'opportunity', 'trainplan', 'gapanalysis', 'researchplan', 'researchreport', 'meeting', 'meetingroom', 'budget', 'reviewissue', 'reviewsetting', 'review', 'milestone', 'measurement', 'measrecord', 'assetlib', 'setting', 'im', 'client', 'ldap', 'dev', 'api', 'gitea', 'gogs', 'zanode', 'zahost');

global $lang;
$config->upgrade->defaultActions = array();
$config->upgrade->defaultActions['type']          = 'single';
$config->upgrade->defaultActions['extensionType'] = 'none';
$config->upgrade->defaultActions['batchMode']     = 'different';
$config->upgrade->defaultActions['layout']        = 'normal';
$config->upgrade->defaultActions['show']          = 'direct';
$config->upgrade->defaultActions['order']         = 0;
$config->upgrade->defaultActions['buildin']       = 1;
$config->upgrade->defaultActions['role']          = 'buildin';
$config->upgrade->defaultActions['virtual']       = 0;
$config->upgrade->defaultActions['status']        = 'enable';
$config->upgrade->defaultActions['vision']        = 'rnd';

$config->upgrade->recoveryActions = new stdclass();
$config->upgrade->recoveryActions->feedback = new stdclass();
$config->upgrade->recoveryActions->feedback->review = array();
$config->upgrade->recoveryActions->feedback->review['action']   = 'review';
$config->upgrade->recoveryActions->feedback->review['module']   = 'feedback';
$config->upgrade->recoveryActions->feedback->review['name']     = $lang->upgrade->recoveryActions->review;
$config->upgrade->recoveryActions->feedback->review['method']   = 'operate';
$config->upgrade->recoveryActions->feedback->review['open']     = 'modal';
$config->upgrade->recoveryActions->feedback->review['position'] = 'browseandview';
$config->upgrade->recoveryActions->feedback->review['hasLite']  = true;

$config->upgrade->recoveryActions->task = new stdclass();
$config->upgrade->recoveryActions->task->review = array();
$config->upgrade->recoveryActions->task->review['action']   = 'cancel';
$config->upgrade->recoveryActions->task->review['module']   = 'task';
$config->upgrade->recoveryActions->task->review['name']     = $lang->upgrade->recoveryActions->cancel;
$config->upgrade->recoveryActions->task->review['method']   = 'operate';
$config->upgrade->recoveryActions->task->review['open']     = 'modal';
$config->upgrade->recoveryActions->task->review['position'] = 'view';
$config->upgrade->recoveryActions->task->review['hasLite']  = true;

$config->upgrade->recoveryActions->story = new stdclass();
$config->upgrade->recoveryActions->story->review = array();
$config->upgrade->recoveryActions->story->review['action']   = 'review';
$config->upgrade->recoveryActions->story->review['module']   = 'story';
$config->upgrade->recoveryActions->story->review['name']     = $lang->upgrade->recoveryActions->review;
$config->upgrade->recoveryActions->story->review['method']   = 'operate';
$config->upgrade->recoveryActions->story->review['open']     = 'normal';
$config->upgrade->recoveryActions->story->review['position'] = 'browseandview';

$config->upgrade->recoveryActions->testcase = new stdclass();
$config->upgrade->recoveryActions->testcase->review = array();
$config->upgrade->recoveryActions->testcase->review['action']   = 'review';
$config->upgrade->recoveryActions->testcase->review['module']   = 'testcase';
$config->upgrade->recoveryActions->testcase->review['name']     = $lang->upgrade->recoveryActions->review;
$config->upgrade->recoveryActions->testcase->review['method']   = 'operate';
$config->upgrade->recoveryActions->testcase->review['open']     = 'normal';
$config->upgrade->recoveryActions->testcase->review['position'] = 'browseandview';

$config->upgrade->missedFlowFieldVersions = array('max4_4_alpha1', 'max4_4_beta1', 'max4_4', 'max4_5', 'biz8_4_alpha1', 'biz8_4_beta1', 'biz8_4', 'biz8_5');

$config->upgrade->dbFieldLengths['int']      = array('tinyint' => 4, 'smallint' => 6, 'mediumint' => 9, 'int' => 11, 'integer' => 11, 'bigint' => 20);
$config->upgrade->dbFieldLengths['unsigned'] = array('tinyint' => 3, 'smallint' => 5, 'mediumint' => 8, 'int' => 10, 'integer' => 10, 'bigint' => 20);
$config->upgrade->dbFieldLengths['text']     = array('tinytext' => 8, 'text' => 16, 'mediumtext' => 24, 'longtext' => 32);

$config->upgrade->editors['doc']         = array('table' => TABLE_DOCCONTENT,  'fields' => 'doc,`content`,`digest`');
$config->upgrade->editors['project']     = array('table' => TABLE_PROJECT,     'fields' => 'id,`desc`');
$config->upgrade->editors['bug']         = array('table' => TABLE_BUG,         'fields' => 'id,`steps`');
$config->upgrade->editors['release']     = array('table' => TABLE_RELEASE,     'fields' => 'id,`desc`');
$config->upgrade->editors['productplan'] = array('table' => TABLE_PRODUCTPLAN, 'fields' => 'id,`desc`');
$config->upgrade->editors['product']     = array('table' => TABLE_PRODUCT,     'fields' => 'id,`desc`');
$config->upgrade->editors['story']       = array('table' => TABLE_STORYSPEC,   'fields' => 'story,`spec`,`verify`');
$config->upgrade->editors['testtask']    = array('table' => TABLE_TESTTASK,    'fields' => 'id,`desc`,`report`');
$config->upgrade->editors['todo']        = array('table' => TABLE_TODO,        'fields' => 'id,`desc`');
$config->upgrade->editors['task']        = array('table' => TABLE_TASK,        'fields' => 'id,`desc`');
$config->upgrade->editors['build']       = array('table' => TABLE_BUILD,       'fields' => 'id,`desc`');
