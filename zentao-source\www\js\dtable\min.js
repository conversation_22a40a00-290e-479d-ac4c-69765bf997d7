var Zr=Object.defineProperty;var ei=(D,A,z)=>A in D?Zr(D,A,{enumerable:!0,configurable:!0,writable:!0,value:z}):D[A]=z;var U=(D,A,z)=>(ei(D,typeof A!="symbol"?A+"":A,z),z),_o=(D,A,z)=>{if(!A.has(D))throw TypeError("Cannot "+z)};var g=(D,A,z)=>(_o(D,A,"read from private field"),z?z.call(D):A.get(D)),k=(D,A,z)=>{if(A.has(D))throw TypeError("Cannot add the same private member more than once");A instanceof WeakSet?A.add(D):A.set(D,z)},M=(D,A,z,_e)=>(_o(D,A,"write to private field"),_e?_e.call(D,z):A.set(D,z),z);var B=(D,A,z)=>(_o(D,A,"access private method"),z);(function(D,A){typeof exports=="object"&&typeof module<"u"?A(exports):typeof define=="function"&&define.amd?define(["exports"],A):(D=typeof globalThis<"u"?globalThis:D||self,A(D.zui={}))})(this,function(D){var te,oe,Ke,ge,et,Qn,ne,ke,X,ye,me,mt,be,xe,ue,Q,de,F,Y,Z,Ee,Fe,Be,se,Re,Se,bt,as,wt,fs,vt,hs,Ct,us,Ve,go,kt,xt,Ge,Xe,Et,Rt,St,ds,Mt,ps,$t,_s,Ye,pe,ee,Me,$e,It;"use strict";const A=`:root{--color-gray-50:#f8fafc;--color-gray-100:#f1f5f9;--color-gray-200:#e2e8f0;--color-gray-300:#cbd5e1;--color-gray-400:#94a3b8;--color-gray-500:#64748b;--color-gray-600:#475569;--color-gray-700:#334155;--color-gray-800:#1e293b;--color-gray-900:#0f172a;--color-gray-50-rgb:248,250,252;--color-gray-100-rgb:241,245,249;--color-gray-200-rgb:226,232,240;--color-gray-300-rgb:203,213,225;--color-gray-400-rgb:148,163,184;--color-gray-500-rgb:100,116,139;--color-gray-600-rgb:71,85,105;--color-gray-700-rgb:51,65,85;--color-gray-800-rgb:30,41,59;--color-gray-900-rgb:15,23,42;--color-primary-50:#eff6ff;--color-primary-100:#dbeafe;--color-primary-200:#bfdbfe;--color-primary-300:#93c5fd;--color-primary-400:#60a5fa;--color-primary-500:#3b82f6;--color-primary-600:#2563eb;--color-primary-700:#1d4ed8;--color-primary-800:#1e40af;--color-primary-900:#1e3a8a;--color-primary-50-rgb:239,246,255;--color-primary-100-rgb:219,234,254;--color-primary-200-rgb:191,219,254;--color-primary-300-rgb:147,197,253;--color-primary-400-rgb:96,165,250;--color-primary-500-rgb:59,130,246;--color-primary-600-rgb:37,99,235;--color-primary-700-rgb:29,78,216;--color-primary-800-rgb:30,64,175;--color-primary-900-rgb:30,58,138;--color-secondary-50:#f0f9ff;--color-secondary-100:#e0f2fe;--color-secondary-200:#bae6fd;--color-secondary-300:#7dd3fc;--color-secondary-400:#38bdf8;--color-secondary-500:#0ea5e9;--color-secondary-600:#0284c7;--color-secondary-700:#0369a1;--color-secondary-800:#075985;--color-secondary-900:#0c4a6e;--color-secondary-50-rgb:240,249,255;--color-secondary-100-rgb:224,242,254;--color-secondary-200-rgb:186,230,253;--color-secondary-300-rgb:125,211,252;--color-secondary-400-rgb:56,189,248;--color-secondary-500-rgb:14,165,233;--color-secondary-600-rgb:2,132,199;--color-secondary-700-rgb:3,105,161;--color-secondary-800-rgb:7,89,133;--color-secondary-900-rgb:12,74,110;--color-success-50:#f0fdf4;--color-success-100:#dcfce7;--color-success-200:#bbf7d0;--color-success-300:#86efac;--color-success-400:#4ade80;--color-success-500:#22c55e;--color-success-600:#16a34a;--color-success-700:#15803d;--color-success-800:#166534;--color-success-900:#14532d;--color-success-50-rgb:240,253,244;--color-success-100-rgb:220,252,231;--color-success-200-rgb:187,247,208;--color-success-300-rgb:134,239,172;--color-success-400-rgb:74,222,128;--color-success-500-rgb:34,197,94;--color-success-600-rgb:22,163,74;--color-success-700-rgb:21,128,61;--color-success-800-rgb:22,101,52;--color-success-900-rgb:20,83,45;--color-warning-50:#fffbeb;--color-warning-100:#fef3c7;--color-warning-200:#fde68a;--color-warning-300:#fcd34d;--color-warning-400:#fbbf24;--color-warning-500:#f59e0b;--color-warning-600:#d97706;--color-warning-700:#b45309;--color-warning-800:#92400e;--color-warning-900:#78350f;--color-warning-50-rgb:255,251,235;--color-warning-100-rgb:254,243,199;--color-warning-200-rgb:253,230,138;--color-warning-300-rgb:252,211,77;--color-warning-400-rgb:251,191,36;--color-warning-500-rgb:245,158,11;--color-warning-600-rgb:217,119,6;--color-warning-700-rgb:180,83,9;--color-warning-800-rgb:146,64,14;--color-warning-900-rgb:120,53,15;--color-danger-50:#fef2f2;--color-danger-100:#fee2e2;--color-danger-200:#fecaca;--color-danger-300:#fca5a5;--color-danger-400:#f87171;--color-danger-500:#ef4444;--color-danger-600:#dc2626;--color-danger-700:#b91c1c;--color-danger-800:#991b1b;--color-danger-900:#7f1d1d;--color-danger-50-rgb:254,242,242;--color-danger-100-rgb:254,226,226;--color-danger-200-rgb:254,202,202;--color-danger-300-rgb:252,165,165;--color-danger-400-rgb:248,113,113;--color-danger-500-rgb:239,68,68;--color-danger-600-rgb:220,38,38;--color-danger-700-rgb:185,28,28;--color-danger-800-rgb:153,27,27;--color-danger-900-rgb:127,29,29;--color-important-50:#fdf2f8;--color-important-100:#fce7f3;--color-important-200:#fbcfe8;--color-important-300:#f9a8d4;--color-important-400:#f472b6;--color-important-500:#ec4899;--color-important-600:#db2777;--color-important-700:#be185d;--color-important-800:#9d174d;--color-important-900:#831843;--color-important-50-rgb:253,242,248;--color-important-100-rgb:252,231,243;--color-important-200-rgb:251,207,232;--color-important-300-rgb:249,168,212;--color-important-400-rgb:244,114,182;--color-important-500-rgb:236,72,153;--color-important-600-rgb:219,39,119;--color-important-700-rgb:190,24,93;--color-important-800-rgb:157,23,77;--color-important-900-rgb:131,24,67;--color-special-50:#faf5ff;--color-special-100:#f3e8ff;--color-special-200:#e9d5ff;--color-special-300:#d8b4fe;--color-special-400:#c084fc;--color-special-500:#a855f7;--color-special-600:#9333ea;--color-special-700:#7e22ce;--color-special-800:#6b21a8;--color-special-900:#581c87;--color-special-50-rgb:250,245,255;--color-special-100-rgb:243,232,255;--color-special-200-rgb:233,213,255;--color-special-300-rgb:216,180,254;--color-special-400-rgb:192,132,252;--color-special-500-rgb:168,85,247;--color-special-600-rgb:147,51,234;--color-special-700-rgb:126,34,206;--color-special-800-rgb:107,33,168;--color-special-900-rgb:88,28,135;--color-inherit:inherit;--color-transparent:transparent;--color-current:currentColor;--color-black:#000;--color-white:#fff;--color-canvas:#fff;--color-inverse:#000;--color-surface:#f1f5f9;--color-fore:#334155;--color-focus:#bfdbfe;--color-link:#3b82f6;--color-link-hover:#2563eb;--color-link-visited:#1d4ed8;--color-border:#e2e8f0;--color-border-strong:#cbd5e1;--color-border-light:#f1f5f9;--color-black-rgb:0,0,0;--color-white-rgb:255,255,255;--color-canvas-rgb:255,255,255;--color-inverse-rgb:0,0,0;--color-surface-rgb:241,245,249;--color-fore-rgb:51,65,85;--color-focus-rgb:191,219,254;--color-link-rgb:59,130,246;--color-link-hover-rgb:37,99,235;--color-link-visited-rgb:29,78,216;--color-border-rgb:226,232,240;--color-border-strong-rgb:203,213,225;--color-border-light-rgb:241,245,249;--radius-none:0px;--radius-sm:2px;--radius:4px;--radius-md:6px;--radius-lg:8px;--radius-xl:12px;--radius-2xl:16px;--radius-3xl:24px;--radius-full:9999px;--shadow-sm:0 1px 2px 0 rgba(0,0,0,.05);--shadow:0 1px 3px 0 rgba(0,0,0,.1),0 1px 2px -1px rgba(0,0,0,.1);--shadow-md:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--shadow-lg:0 10px 15px -3px rgba(0,0,0,.1),0 4px 6px -4px rgba(0,0,0,.1);--shadow-xl:0 20px 25px -5px rgba(0,0,0,.1),0 8px 10px -6px rgba(0,0,0,.1);--shadow-2xl:0 25px 50px -12px rgba(0,0,0,.25);--shadow-inner:inset 0 2px 4px 0 rgba(0,0,0,.05);--shadow-none:none;--space:4px;--root-font-size:16px}*,:after,:before{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(59,130,246,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(59,130,246,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }
`;function z(e,t){function o(n,...r){return $(this).each(function(){var c;const s=$(this),l=s.data(e.KEY);l?typeof n=="string"&&((c=l[n])==null||c.call(l,...r)):(typeof n=="string"&&(n={}),s.data(e.KEY,new e(this,{...s.data(),...n})))})}$.extend(!0,$,{zui3:{[t??e.NAME]:e},fn:{[(t??e.NAME).toLowerCase()]:o}})}var _e,H,yo,mo,De,bo,tt={},wo=[],gs=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function ce(e,t){for(var o in t)e[o]=t[o];return e}function vo(e){var t=e.parentNode;t&&t.removeChild(e)}function Co(e,t,o){var n,r,s,l={};for(s in t)s=="key"?n=t[s]:s=="ref"?r=t[s]:l[s]=t[s];if(arguments.length>2&&(l.children=arguments.length>3?_e.call(arguments,2):o),typeof e=="function"&&e.defaultProps!=null)for(s in e.defaultProps)l[s]===void 0&&(l[s]=e.defaultProps[s]);return ot(e,l,n,r,null)}function ot(e,t,o,n,r){var s={type:e,props:t,key:o,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:r??++yo};return r==null&&H.vnode!=null&&H.vnode(s),s}function ys(){return{current:null}}function nt(e){return e.children}function st(e,t){this.props=e,this.context=t}function Ie(e,t){if(t==null)return e.__?Ie(e.__,e.__.__k.indexOf(e)+1):null;for(var o;t<e.__k.length;t++)if((o=e.__k[t])!=null&&o.__e!=null)return o.__e;return typeof e.type=="function"?Ie(e):null}function ko(e){var t,o;if((e=e.__)!=null&&e.__c!=null){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if((o=e.__k[t])!=null&&o.__e!=null){e.__e=e.__c.base=o.__e;break}return ko(e)}}function xo(e){(!e.__d&&(e.__d=!0)&&De.push(e)&&!rt.__r++||bo!==H.debounceRendering)&&((bo=H.debounceRendering)||setTimeout)(rt)}function rt(){for(var e;rt.__r=De.length;)e=De.sort(function(t,o){return t.__v.__b-o.__v.__b}),De=[],e.some(function(t){var o,n,r,s,l,c;t.__d&&(l=(s=(o=t).__v).__e,(c=o.__P)&&(n=[],(r=ce({},s)).__v=s.__v+1,At(c,s,r,o.__n,c.ownerSVGElement!==void 0,s.__h!=null?[l]:null,n,l??Ie(s),s.__h),Io(n,s),s.__e!=l&&ko(s)))})}function Eo(e,t,o,n,r,s,l,c,f,u){var i,p,d,a,h,m,_,y=n&&n.__k||wo,b=y.length;for(o.__k=[],i=0;i<t.length;i++)if((a=o.__k[i]=(a=t[i])==null||typeof a=="boolean"?null:typeof a=="string"||typeof a=="number"||typeof a=="bigint"?ot(null,a,null,null,a):Array.isArray(a)?ot(nt,{children:a},null,null,null):a.__b>0?ot(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a)!=null){if(a.__=o,a.__b=o.__b+1,(d=y[i])===null||d&&a.key==d.key&&a.type===d.type)y[i]=void 0;else for(p=0;p<b;p++){if((d=y[p])&&a.key==d.key&&a.type===d.type){y[p]=void 0;break}d=null}At(e,a,d=d||tt,r,s,l,c,f,u),h=a.__e,(p=a.ref)&&d.ref!=p&&(_||(_=[]),d.ref&&_.push(d.ref,null,a),_.push(p,a.__c||h,a)),h!=null?(m==null&&(m=h),typeof a.type=="function"&&a.__k===d.__k?a.__d=f=Ro(a,f,e):f=So(e,a,d,y,h,f),typeof o.type=="function"&&(o.__d=f)):f&&d.__e==f&&f.parentNode!=e&&(f=Ie(d))}for(o.__e=m,i=b;i--;)y[i]!=null&&No(y[i],y[i]);if(_)for(i=0;i<_.length;i++)Ao(_[i],_[++i],_[++i])}function Ro(e,t,o){for(var n,r=e.__k,s=0;r&&s<r.length;s++)(n=r[s])&&(n.__=e,t=typeof n.type=="function"?Ro(n,t,o):So(o,n,n,r,n.__e,t));return t}function So(e,t,o,n,r,s){var l,c,f;if(t.__d!==void 0)l=t.__d,t.__d=void 0;else if(o==null||r!=s||r.parentNode==null)e:if(s==null||s.parentNode!==e)e.appendChild(r),l=null;else{for(c=s,f=0;(c=c.nextSibling)&&f<n.length;f+=2)if(c==r)break e;e.insertBefore(r,s),l=s}return l!==void 0?l:r.nextSibling}function ms(e,t,o,n,r){var s;for(s in o)s==="children"||s==="key"||s in t||it(e,s,null,o[s],n);for(s in t)r&&typeof t[s]!="function"||s==="children"||s==="key"||s==="value"||s==="checked"||o[s]===t[s]||it(e,s,t[s],o[s],n)}function Mo(e,t,o){t[0]==="-"?e.setProperty(t,o):e[t]=o==null?"":typeof o!="number"||gs.test(t)?o:o+"px"}function it(e,t,o,n,r){var s;e:if(t==="style")if(typeof o=="string")e.style.cssText=o;else{if(typeof n=="string"&&(e.style.cssText=n=""),n)for(t in n)o&&t in o||Mo(e.style,t,"");if(o)for(t in o)n&&o[t]===n[t]||Mo(e.style,t,o[t])}else if(t[0]==="o"&&t[1]==="n")s=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+s]=o,o?n||e.addEventListener(t,s?Do:$o,s):e.removeEventListener(t,s?Do:$o,s);else if(t!=="dangerouslySetInnerHTML"){if(r)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(t!=="href"&&t!=="list"&&t!=="form"&&t!=="tabIndex"&&t!=="download"&&t in e)try{e[t]=o??"";break e}catch{}typeof o=="function"||(o==null||o===!1&&t.indexOf("-")==-1?e.removeAttribute(t):e.setAttribute(t,o))}}function $o(e){this.l[e.type+!1](H.event?H.event(e):e)}function Do(e){this.l[e.type+!0](H.event?H.event(e):e)}function At(e,t,o,n,r,s,l,c,f){var u,i,p,d,a,h,m,_,y,b,C,E,L,S,I,v=t.type;if(t.constructor!==void 0)return null;o.__h!=null&&(f=o.__h,c=t.__e=o.__e,t.__h=null,s=[c]),(u=H.__b)&&u(t);try{e:if(typeof v=="function"){if(_=t.props,y=(u=v.contextType)&&n[u.__c],b=u?y?y.props.value:u.__:n,o.__c?m=(i=t.__c=o.__c).__=i.__E:("prototype"in v&&v.prototype.render?t.__c=i=new v(_,b):(t.__c=i=new st(_,b),i.constructor=v,i.render=ws),y&&y.sub(i),i.props=_,i.state||(i.state={}),i.context=b,i.__n=n,p=i.__d=!0,i.__h=[],i._sb=[]),i.__s==null&&(i.__s=i.state),v.getDerivedStateFromProps!=null&&(i.__s==i.state&&(i.__s=ce({},i.__s)),ce(i.__s,v.getDerivedStateFromProps(_,i.__s))),d=i.props,a=i.state,p)v.getDerivedStateFromProps==null&&i.componentWillMount!=null&&i.componentWillMount(),i.componentDidMount!=null&&i.__h.push(i.componentDidMount);else{if(v.getDerivedStateFromProps==null&&_!==d&&i.componentWillReceiveProps!=null&&i.componentWillReceiveProps(_,b),!i.__e&&i.shouldComponentUpdate!=null&&i.shouldComponentUpdate(_,i.__s,b)===!1||t.__v===o.__v){for(i.props=_,i.state=i.__s,t.__v!==o.__v&&(i.__d=!1),i.__v=t,t.__e=o.__e,t.__k=o.__k,t.__k.forEach(function(w){w&&(w.__=t)}),C=0;C<i._sb.length;C++)i.__h.push(i._sb[C]);i._sb=[],i.__h.length&&l.push(i);break e}i.componentWillUpdate!=null&&i.componentWillUpdate(_,i.__s,b),i.componentDidUpdate!=null&&i.__h.push(function(){i.componentDidUpdate(d,a,h)})}if(i.context=b,i.props=_,i.__v=t,i.__P=e,E=H.__r,L=0,"prototype"in v&&v.prototype.render){for(i.state=i.__s,i.__d=!1,E&&E(t),u=i.render(i.props,i.state,i.context),S=0;S<i._sb.length;S++)i.__h.push(i._sb[S]);i._sb=[]}else do i.__d=!1,E&&E(t),u=i.render(i.props,i.state,i.context),i.state=i.__s;while(i.__d&&++L<25);i.state=i.__s,i.getChildContext!=null&&(n=ce(ce({},n),i.getChildContext())),p||i.getSnapshotBeforeUpdate==null||(h=i.getSnapshotBeforeUpdate(d,a)),I=u!=null&&u.type===nt&&u.key==null?u.props.children:u,Eo(e,Array.isArray(I)?I:[I],t,o,n,r,s,l,c,f),i.base=t.__e,t.__h=null,i.__h.length&&l.push(i),m&&(i.__E=i.__=null),i.__e=!1}else s==null&&t.__v===o.__v?(t.__k=o.__k,t.__e=o.__e):t.__e=bs(o.__e,t,o,n,r,s,l,f);(u=H.diffed)&&u(t)}catch(w){t.__v=null,(f||s!=null)&&(t.__e=c,t.__h=!!f,s[s.indexOf(c)]=null),H.__e(w,t,o)}}function Io(e,t){H.__c&&H.__c(t,e),e.some(function(o){try{e=o.__h,o.__h=[],e.some(function(n){n.call(o)})}catch(n){H.__e(n,o.__v)}})}function bs(e,t,o,n,r,s,l,c){var f,u,i,p=o.props,d=t.props,a=t.type,h=0;if(a==="svg"&&(r=!0),s!=null){for(;h<s.length;h++)if((f=s[h])&&"setAttribute"in f==!!a&&(a?f.localName===a:f.nodeType===3)){e=f,s[h]=null;break}}if(e==null){if(a===null)return document.createTextNode(d);e=r?document.createElementNS("http://www.w3.org/2000/svg",a):document.createElement(a,d.is&&d),s=null,c=!1}if(a===null)p===d||c&&e.data===d||(e.data=d);else{if(s=s&&_e.call(e.childNodes),u=(p=o.props||tt).dangerouslySetInnerHTML,i=d.dangerouslySetInnerHTML,!c){if(s!=null)for(p={},h=0;h<e.attributes.length;h++)p[e.attributes[h].name]=e.attributes[h].value;(i||u)&&(i&&(u&&i.__html==u.__html||i.__html===e.innerHTML)||(e.innerHTML=i&&i.__html||""))}if(ms(e,d,p,r,c),i)t.__k=[];else if(h=t.props.children,Eo(e,Array.isArray(h)?h:[h],t,o,n,r&&a!=="foreignObject",s,l,s?s[0]:o.__k&&Ie(o,0),c),s!=null)for(h=s.length;h--;)s[h]!=null&&vo(s[h]);c||("value"in d&&(h=d.value)!==void 0&&(h!==e.value||a==="progress"&&!h||a==="option"&&h!==p.value)&&it(e,"value",h,p.value,!1),"checked"in d&&(h=d.checked)!==void 0&&h!==e.checked&&it(e,"checked",h,p.checked,!1))}return e}function Ao(e,t,o){try{typeof e=="function"?e(t):e.current=t}catch(n){H.__e(n,o)}}function No(e,t,o){var n,r;if(H.unmount&&H.unmount(e),(n=e.ref)&&(n.current&&n.current!==e.__e||Ao(n,null,t)),(n=e.__c)!=null){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(s){H.__e(s,t)}n.base=n.__P=null,e.__c=void 0}if(n=e.__k)for(r=0;r<n.length;r++)n[r]&&No(n[r],t,o||typeof e.type!="function");o||e.__e==null||vo(e.__e),e.__=e.__e=e.__d=void 0}function ws(e,t,o){return this.constructor(e,o)}function vs(e,t,o){var n,r,s;H.__&&H.__(e,t),r=(n=typeof o=="function")?null:o&&o.__k||t.__k,s=[],At(t,e=(!n&&o||t).__k=Co(nt,null,[e]),r||tt,tt,t.ownerSVGElement!==void 0,!n&&o?[o]:r?null:t.firstChild?_e.call(t.childNodes):null,s,!n&&o?o:r?r.__e:t.firstChild,n),Io(s,e)}_e=wo.slice,H={__e:function(e,t,o,n){for(var r,s,l;t=t.__;)if((r=t.__c)&&!r.__)try{if((s=r.constructor)&&s.getDerivedStateFromError!=null&&(r.setState(s.getDerivedStateFromError(e)),l=r.__d),r.componentDidCatch!=null&&(r.componentDidCatch(e,n||{}),l=r.__d),l)return r.__E=r}catch(c){e=c}throw e}},yo=0,mo=function(e){return e!=null&&e.constructor===void 0},st.prototype.setState=function(e,t){var o;o=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=ce({},this.state),typeof e=="function"&&(e=e(ce({},o),this.props)),e&&ce(o,e),e!=null&&this.__v&&(t&&this._sb.push(t),xo(this))},st.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),xo(this))},st.prototype.render=nt,De=[],rt.__r=0;var Cs=0;function To(e,t,o,n,r){var s,l,c={};for(l in t)l=="ref"?s=t[l]:c[l]=t[l];var f={type:e,props:c,key:o,ref:s,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--Cs,__source:r,__self:n};if(typeof e=="function"&&(s=e.defaultProps))for(l in s)c[l]===void 0&&(c[l]=s[l]);return H.vnode&&H.vnode(f),f}class ks{constructor(t=""){k(this,te,void 0);typeof t=="object"?M(this,te,t):M(this,te,document.appendChild(document.createComment(t)))}on(t,o,n){g(this,te).addEventListener(t,o,n)}once(t,o,n){g(this,te).addEventListener(t,o,{once:!0,...n})}off(t,o,n){g(this,te).removeEventListener(t,o,n)}emit(t){return g(this,te).dispatchEvent(t),t}}te=new WeakMap;const Nt=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);class Tt extends ks{on(t,o,n){super.on(t,o,n)}off(t,o,n){super.off(t,o,n)}once(t,o,n){super.once(t,o,n)}emit(t,o){return typeof t=="string"&&(Nt.has(t)?(t=new Event(t),Object.assign(t,{detail:o})):t=new CustomEvent(t,{detail:o})),super.emit(Tt.createEvent(t,o))}static createEvent(t,o){return typeof t=="string"&&(Nt.has(t)?(t=new Event(t),Object.assign(t,{detail:o})):t=new CustomEvent(t,{detail:o})),t}}class Ho extends Tt{constructor(o="",n){super(o);k(this,ge);k(this,oe,new Map);k(this,Ke,void 0);M(this,Ke,n==null?void 0:n.customEventSuffix)}on(o,n,r){o=B(this,ge,et).call(this,o),super.on(o,n,r),g(this,oe).set(n,[o,r])}off(o,n,r){o=B(this,ge,et).call(this,o),super.off(o,n,r),g(this,oe).delete(n)}once(o,n,r){o=B(this,ge,et).call(this,o);const s=l=>{n(l),g(this,oe).delete(s)};super.once(o,s,r),g(this,oe).set(s,[o,r])}emit(o,n){return typeof o=="string"&&(o=B(this,ge,et).call(this,o)),super.emit(o,n)}offAll(){Array.from(g(this,oe).entries()).forEach(([o,[n,r]])=>{super.off(n,o,r)}),g(this,oe).clear()}}oe=new WeakMap,Ke=new WeakMap,ge=new WeakSet,et=function(o){const n=g(this,Ke);return Nt.has(o)||typeof n!="string"||o.endsWith(n)?o:`${o}${n}`};function xs(e,t){if(e==null)return[e,void 0];typeof t=="string"&&(t=t.split("."));const o=t.join(".");let n=e;const r=[n];for(;typeof n=="object"&&n!==null&&t.length;){let s=t.shift(),l;const c=s.indexOf("[");if(c>0&&c<s.length-1&&s.endsWith("]")&&(l=s.substring(c+1,s.length-1),s=s.substring(0,c)),n=n[s],r.push(n),l!==void 0)if(typeof n=="object"&&n!==null)n instanceof Map?n=n.get(l):n=n[l],r.push(n);else throw new Error(`Cannot access property "${s}[${l}]", the full path is "${o}".`)}if(t.length)throw new Error(`Cannot access property with rest path "${t.join(".")}", the full path is "${o}".`);return r}function Es(e,t,o){const n=xs(e,t),r=n[n.length-1];return r===void 0?o:r}function Ht(e){return!!e&&typeof e=="object"&&!Array.isArray(e)}function Pt(e,...t){if(!t.length)return e;const o=t.shift();if(Ht(e)&&Ht(o))for(const n in o)Ht(o[n])?(e[n]||Object.assign(e,{[n]:{}}),Pt(e[n],o[n])):Object.assign(e,{[n]:o[n]});return Pt(e,...t)}function Ae(e,...t){if(t.length===0)return e;if(t.length===1&&typeof t[0]=="object"&&t[0]){const o=t[0];return Object.keys(o).forEach(n=>{const r=o[n]??0;e=e.replace(new RegExp(`\\{${n}\\}`,"g"),`${r}`)}),e}for(let o=0;o<t.length;o++){const n=t[o]??"";e=e.replace(new RegExp(`\\{${o}\\}`,"g"),`${n}`)}return e}let Ot=((Qn=document.documentElement.getAttribute("lang"))==null?void 0:Qn.toLowerCase())??"zh_cn",ae;function Rs(){return Ot}function Ss(e){Ot=e.toLowerCase()}function Ms(e,t){ae||(ae={}),typeof e=="string"&&(e={[e]:t??{}}),Pt(ae,e)}function Ne(e,t,o,n,r,s){Array.isArray(e)?ae&&e.unshift(ae):e=ae?[ae,e]:[e],typeof o=="string"&&(s=r,r=n,n=o,o=void 0);const l=r||Ot;let c;for(const f of e){if(!f)continue;const u=f[l];if(!u)continue;const i=s&&f===ae?`${s}.${t}`:t;if(c=Es(u,i),c!==void 0)break}return c===void 0?n:o?Ae(c,...Array.isArray(o)?o:[o]):c}Ne.addLang=Ms,Ne.getCode=Rs,Ne.setCode=Ss;function $s(e){return Object.fromEntries(Object.entries(e).map(([t,o])=>{if(typeof o=="string")try{o=JSON.parse(o)}catch{}return[t,o]}))}const zt=new Map;class jt{constructor(t,o){k(this,ne,void 0);k(this,ke,void 0);k(this,X,void 0);t=typeof t=="string"?document.querySelector(t):t,this.constructor.EVENTS&&M(this,X,new Ho(t,{customEventSuffix:`.${this.constructor.KEY}`})),M(this,ne,{...this.constructor.DEFAULT}),this.setOptions({...t instanceof HTMLElement?$s(t.dataset):null,...o}),this.constructor.all.set(t,this),M(this,ke,t),this.init(),requestAnimationFrame(()=>{this.afterInit(),this.emit("inited",this)})}get options(){return g(this,ne)}get element(){return g(this,ke)}get events(){return g(this,X)}init(){}afterInit(){}setOptions(t){return t&&Object.assign(g(this,ne),t),g(this,ne)}render(t){this.setOptions(t)}destroy(){this.constructor.all.delete(g(this,ke)),g(this,X)&&(this.emit("destroyed",this),g(this,X).offAll())}on(t,o,n){var r;(r=g(this,X))==null||r.on(t,o,n)}once(t,o,n){var r;(r=g(this,X))==null||r.once(t,o,n)}off(t,o,n){var r;(r=g(this,X))==null||r.off(t,o,n)}emit(t,o){var l;let n=Ho.createEvent(t,o);const r=`on${t[0].toUpperCase()}${t.substring(1)}`,s=g(this,ne)[r];return s&&s(n)===!1&&(n.preventDefault(),n.stopPropagation()),n=(l=g(this,X))==null?void 0:l.emit(t,o),n}i18n(t,o,n){return Ne(g(this,ne).i18n,t,o,n,this.options.lang,this.constructor.NAME)??`{i18n:${t}}`}static get NAME(){throw new Error(`static NAME should be override in class ${this.name}`)}static get KEY(){return`zui.${this.NAME}`}static get all(){const t=this.NAME;if(zt.has(t))return zt.get(t);const o=new Map;return zt.set(t,o),o}static getAll(){return this.all}static get(t){return this.all.get(t)}static ensure(t,o){return this.get(t)||new this(t,o)}}ne=new WeakMap,ke=new WeakMap,X=new WeakMap,U(jt,"EVENTS",!1),U(jt,"DEFAULT",{});class Po extends jt{constructor(){super(...arguments);U(this,"ref",ys())}get $(){return this.ref.current}init(){requestAnimationFrame(()=>this.render())}destroy(){super.destroy(),this.element.innerHTML=""}render(o){const n=this.constructor.Component;vs(To(n,{ref:this.ref,...this.setOptions(o)}),this.element)}}U(Po,"Component");var Lt,P,Oo,zo,Te,jo,Lo={},Wo=[],Ds=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function fe(e,t){for(var o in t)e[o]=t[o];return e}function Uo(e){var t=e.parentNode;t&&t.removeChild(e)}function Wt(e,t,o){var n,r,s,l={};for(s in t)s=="key"?n=t[s]:s=="ref"?r=t[s]:l[s]=t[s];if(arguments.length>2&&(l.children=arguments.length>3?Lt.call(arguments,2):o),typeof e=="function"&&e.defaultProps!=null)for(s in e.defaultProps)l[s]===void 0&&(l[s]=e.defaultProps[s]);return lt(e,l,n,r,null)}function lt(e,t,o,n,r){var s={type:e,props:t,key:o,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:r??++Oo};return r==null&&P.vnode!=null&&P.vnode(s),s}function Ko(){return{current:null}}function Ut(e){return e.children}function He(e,t){this.props=e,this.context=t}function Pe(e,t){if(t==null)return e.__?Pe(e.__,e.__.__k.indexOf(e)+1):null;for(var o;t<e.__k.length;t++)if((o=e.__k[t])!=null&&o.__e!=null)return o.__e;return typeof e.type=="function"?Pe(e):null}function Fo(e){var t,o;if((e=e.__)!=null&&e.__c!=null){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if((o=e.__k[t])!=null&&o.__e!=null){e.__e=e.__c.base=o.__e;break}return Fo(e)}}function Bo(e){(!e.__d&&(e.__d=!0)&&Te.push(e)&&!ct.__r++||jo!==P.debounceRendering)&&((jo=P.debounceRendering)||setTimeout)(ct)}function ct(){for(var e;ct.__r=Te.length;)e=Te.sort(function(t,o){return t.__v.__b-o.__v.__b}),Te=[],e.some(function(t){var o,n,r,s,l,c;t.__d&&(l=(s=(o=t).__v).__e,(c=o.__P)&&(n=[],(r=fe({},s)).__v=s.__v+1,Qo(c,s,r,o.__n,c.ownerSVGElement!==void 0,s.__h!=null?[l]:null,n,l??Pe(s),s.__h),As(n,s),s.__e!=l&&Fo(s)))})}function Vo(e,t,o,n,r,s,l,c,f,u){var i,p,d,a,h,m,_,y=n&&n.__k||Wo,b=y.length;for(o.__k=[],i=0;i<t.length;i++)if((a=o.__k[i]=(a=t[i])==null||typeof a=="boolean"?null:typeof a=="string"||typeof a=="number"||typeof a=="bigint"?lt(null,a,null,null,a):Array.isArray(a)?lt(Ut,{children:a},null,null,null):a.__b>0?lt(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a)!=null){if(a.__=o,a.__b=o.__b+1,(d=y[i])===null||d&&a.key==d.key&&a.type===d.type)y[i]=void 0;else for(p=0;p<b;p++){if((d=y[p])&&a.key==d.key&&a.type===d.type){y[p]=void 0;break}d=null}Qo(e,a,d=d||Lo,r,s,l,c,f,u),h=a.__e,(p=a.ref)&&d.ref!=p&&(_||(_=[]),d.ref&&_.push(d.ref,null,a),_.push(p,a.__c||h,a)),h!=null?(m==null&&(m=h),typeof a.type=="function"&&a.__k===d.__k?a.__d=f=Go(a,f,e):f=Xo(e,a,d,y,h,f),typeof o.type=="function"&&(o.__d=f)):f&&d.__e==f&&f.parentNode!=e&&(f=Pe(d))}for(o.__e=m,i=b;i--;)y[i]!=null&&en(y[i],y[i]);if(_)for(i=0;i<_.length;i++)Zo(_[i],_[++i],_[++i])}function Go(e,t,o){for(var n,r=e.__k,s=0;r&&s<r.length;s++)(n=r[s])&&(n.__=e,t=typeof n.type=="function"?Go(n,t,o):Xo(o,n,n,r,n.__e,t));return t}function Xo(e,t,o,n,r,s){var l,c,f;if(t.__d!==void 0)l=t.__d,t.__d=void 0;else if(o==null||r!=s||r.parentNode==null)e:if(s==null||s.parentNode!==e)e.appendChild(r),l=null;else{for(c=s,f=0;(c=c.nextSibling)&&f<n.length;f+=2)if(c==r)break e;e.insertBefore(r,s),l=s}return l!==void 0?l:r.nextSibling}function Is(e,t,o,n,r){var s;for(s in o)s==="children"||s==="key"||s in t||at(e,s,null,o[s],n);for(s in t)r&&typeof t[s]!="function"||s==="children"||s==="key"||s==="value"||s==="checked"||o[s]===t[s]||at(e,s,t[s],o[s],n)}function Yo(e,t,o){t[0]==="-"?e.setProperty(t,o):e[t]=o==null?"":typeof o!="number"||Ds.test(t)?o:o+"px"}function at(e,t,o,n,r){var s;e:if(t==="style")if(typeof o=="string")e.style.cssText=o;else{if(typeof n=="string"&&(e.style.cssText=n=""),n)for(t in n)o&&t in o||Yo(e.style,t,"");if(o)for(t in o)n&&o[t]===n[t]||Yo(e.style,t,o[t])}else if(t[0]==="o"&&t[1]==="n")s=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+s]=o,o?n||e.addEventListener(t,s?Jo:qo,s):e.removeEventListener(t,s?Jo:qo,s);else if(t!=="dangerouslySetInnerHTML"){if(r)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(t!=="href"&&t!=="list"&&t!=="form"&&t!=="tabIndex"&&t!=="download"&&t in e)try{e[t]=o??"";break e}catch{}typeof o=="function"||(o==null||o===!1&&t.indexOf("-")==-1?e.removeAttribute(t):e.setAttribute(t,o))}}function qo(e){this.l[e.type+!1](P.event?P.event(e):e)}function Jo(e){this.l[e.type+!0](P.event?P.event(e):e)}function Qo(e,t,o,n,r,s,l,c,f){var u,i,p,d,a,h,m,_,y,b,C,E,L,S,I,v=t.type;if(t.constructor!==void 0)return null;o.__h!=null&&(f=o.__h,c=t.__e=o.__e,t.__h=null,s=[c]),(u=P.__b)&&u(t);try{e:if(typeof v=="function"){if(_=t.props,y=(u=v.contextType)&&n[u.__c],b=u?y?y.props.value:u.__:n,o.__c?m=(i=t.__c=o.__c).__=i.__E:("prototype"in v&&v.prototype.render?t.__c=i=new v(_,b):(t.__c=i=new He(_,b),i.constructor=v,i.render=Ts),y&&y.sub(i),i.props=_,i.state||(i.state={}),i.context=b,i.__n=n,p=i.__d=!0,i.__h=[],i._sb=[]),i.__s==null&&(i.__s=i.state),v.getDerivedStateFromProps!=null&&(i.__s==i.state&&(i.__s=fe({},i.__s)),fe(i.__s,v.getDerivedStateFromProps(_,i.__s))),d=i.props,a=i.state,p)v.getDerivedStateFromProps==null&&i.componentWillMount!=null&&i.componentWillMount(),i.componentDidMount!=null&&i.__h.push(i.componentDidMount);else{if(v.getDerivedStateFromProps==null&&_!==d&&i.componentWillReceiveProps!=null&&i.componentWillReceiveProps(_,b),!i.__e&&i.shouldComponentUpdate!=null&&i.shouldComponentUpdate(_,i.__s,b)===!1||t.__v===o.__v){for(i.props=_,i.state=i.__s,t.__v!==o.__v&&(i.__d=!1),i.__v=t,t.__e=o.__e,t.__k=o.__k,t.__k.forEach(function(w){w&&(w.__=t)}),C=0;C<i._sb.length;C++)i.__h.push(i._sb[C]);i._sb=[],i.__h.length&&l.push(i);break e}i.componentWillUpdate!=null&&i.componentWillUpdate(_,i.__s,b),i.componentDidUpdate!=null&&i.__h.push(function(){i.componentDidUpdate(d,a,h)})}if(i.context=b,i.props=_,i.__v=t,i.__P=e,E=P.__r,L=0,"prototype"in v&&v.prototype.render){for(i.state=i.__s,i.__d=!1,E&&E(t),u=i.render(i.props,i.state,i.context),S=0;S<i._sb.length;S++)i.__h.push(i._sb[S]);i._sb=[]}else do i.__d=!1,E&&E(t),u=i.render(i.props,i.state,i.context),i.state=i.__s;while(i.__d&&++L<25);i.state=i.__s,i.getChildContext!=null&&(n=fe(fe({},n),i.getChildContext())),p||i.getSnapshotBeforeUpdate==null||(h=i.getSnapshotBeforeUpdate(d,a)),I=u!=null&&u.type===Ut&&u.key==null?u.props.children:u,Vo(e,Array.isArray(I)?I:[I],t,o,n,r,s,l,c,f),i.base=t.__e,t.__h=null,i.__h.length&&l.push(i),m&&(i.__E=i.__=null),i.__e=!1}else s==null&&t.__v===o.__v?(t.__k=o.__k,t.__e=o.__e):t.__e=Ns(o.__e,t,o,n,r,s,l,f);(u=P.diffed)&&u(t)}catch(w){t.__v=null,(f||s!=null)&&(t.__e=c,t.__h=!!f,s[s.indexOf(c)]=null),P.__e(w,t,o)}}function As(e,t){P.__c&&P.__c(t,e),e.some(function(o){try{e=o.__h,o.__h=[],e.some(function(n){n.call(o)})}catch(n){P.__e(n,o.__v)}})}function Ns(e,t,o,n,r,s,l,c){var f,u,i,p=o.props,d=t.props,a=t.type,h=0;if(a==="svg"&&(r=!0),s!=null){for(;h<s.length;h++)if((f=s[h])&&"setAttribute"in f==!!a&&(a?f.localName===a:f.nodeType===3)){e=f,s[h]=null;break}}if(e==null){if(a===null)return document.createTextNode(d);e=r?document.createElementNS("http://www.w3.org/2000/svg",a):document.createElement(a,d.is&&d),s=null,c=!1}if(a===null)p===d||c&&e.data===d||(e.data=d);else{if(s=s&&Lt.call(e.childNodes),u=(p=o.props||Lo).dangerouslySetInnerHTML,i=d.dangerouslySetInnerHTML,!c){if(s!=null)for(p={},h=0;h<e.attributes.length;h++)p[e.attributes[h].name]=e.attributes[h].value;(i||u)&&(i&&(u&&i.__html==u.__html||i.__html===e.innerHTML)||(e.innerHTML=i&&i.__html||""))}if(Is(e,d,p,r,c),i)t.__k=[];else if(h=t.props.children,Vo(e,Array.isArray(h)?h:[h],t,o,n,r&&a!=="foreignObject",s,l,s?s[0]:o.__k&&Pe(o,0),c),s!=null)for(h=s.length;h--;)s[h]!=null&&Uo(s[h]);c||("value"in d&&(h=d.value)!==void 0&&(h!==e.value||a==="progress"&&!h||a==="option"&&h!==p.value)&&at(e,"value",h,p.value,!1),"checked"in d&&(h=d.checked)!==void 0&&h!==e.checked&&at(e,"checked",h,p.checked,!1))}return e}function Zo(e,t,o){try{typeof e=="function"?e(t):e.current=t}catch(n){P.__e(n,o)}}function en(e,t,o){var n,r;if(P.unmount&&P.unmount(e),(n=e.ref)&&(n.current&&n.current!==e.__e||Zo(n,null,t)),(n=e.__c)!=null){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(s){P.__e(s,t)}n.base=n.__P=null,e.__c=void 0}if(n=e.__k)for(r=0;r<n.length;r++)n[r]&&en(n[r],t,o||typeof e.type!="function");o||e.__e==null||Uo(e.__e),e.__=e.__e=e.__d=void 0}function Ts(e,t,o){return this.constructor(e,o)}Lt=Wo.slice,P={__e:function(e,t,o,n){for(var r,s,l;t=t.__;)if((r=t.__c)&&!r.__)try{if((s=r.constructor)&&s.getDerivedStateFromError!=null&&(r.setState(s.getDerivedStateFromError(e)),l=r.__d),r.componentDidCatch!=null&&(r.componentDidCatch(e,n||{}),l=r.__d),l)return r.__E=r}catch(c){e=c}throw e}},Oo=0,zo=function(e){return e!=null&&e.constructor===void 0},He.prototype.setState=function(e,t){var o;o=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=fe({},this.state),typeof e=="function"&&(e=e(fe({},o),this.props)),e&&fe(o,e),e!=null&&this.__v&&(t&&this._sb.push(t),Bo(this))},He.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),Bo(this))},He.prototype.render=Ut,Te=[],ct.__r=0;var Hs=0;function x(e,t,o,n,r){var s,l,c={};for(l in t)l=="ref"?s=t[l]:c[l]=t[l];var f={type:e,props:c,key:o,ref:s,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--Hs,__source:r,__self:n};if(typeof e=="function"&&(s=e.defaultProps))for(l in s)c[l]===void 0&&(c[l]=s[l]);return P.vnode&&P.vnode(f),f}let Ps=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce((t,o)=>(o&=63,o<36?t+=o.toString(36):o<62?t+=(o-26).toString(36).toUpperCase():o>62?t+="-":t+="_",t),"");function tn(...e){const t=[],o=new Map,n=(r,s)=>{if(Array.isArray(r)&&(s=r[1],r=r[0]),!r.length)return;const l=o.get(r);typeof l=="number"?t[l][1]=!!s:(o.set(r,t.length),t.push([r,!!s]))};return e.forEach(r=>{typeof r=="function"&&(r=r()),Array.isArray(r)?tn(...r).forEach(n):r&&typeof r=="object"?Object.entries(r).forEach(n):typeof r=="string"&&r.split(" ").forEach(s=>n(s,!0))}),t.sort((r,s)=>(o.get(r[0])||0)-(o.get(s[0])||0))}const G=(...e)=>tn(...e).reduce((t,[o,n])=>(n&&t.push(o),t),[]).join(" ");var on,O,nn,Oe,sn,rn={},ln=[],Os=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function he(e,t){for(var o in t)e[o]=t[o];return e}function cn(e){var t=e.parentNode;t&&t.removeChild(e)}function Kt(e,t,o,n,r){var s={type:e,props:t,key:o,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:r??++nn};return r==null&&O.vnode!=null&&O.vnode(s),s}function Ft(e){return e.children}function ze(e,t){this.props=e,this.context=t}function je(e,t){if(t==null)return e.__?je(e.__,e.__.__k.indexOf(e)+1):null;for(var o;t<e.__k.length;t++)if((o=e.__k[t])!=null&&o.__e!=null)return o.__e;return typeof e.type=="function"?je(e):null}function an(e){var t,o;if((e=e.__)!=null&&e.__c!=null){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if((o=e.__k[t])!=null&&o.__e!=null){e.__e=e.__c.base=o.__e;break}return an(e)}}function fn(e){(!e.__d&&(e.__d=!0)&&Oe.push(e)&&!ft.__r++||sn!==O.debounceRendering)&&((sn=O.debounceRendering)||setTimeout)(ft)}function ft(){for(var e;ft.__r=Oe.length;)e=Oe.sort(function(t,o){return t.__v.__b-o.__v.__b}),Oe=[],e.some(function(t){var o,n,r,s,l,c;t.__d&&(l=(s=(o=t).__v).__e,(c=o.__P)&&(n=[],(r=he({},s)).__v=s.__v+1,yn(c,s,r,o.__n,c.ownerSVGElement!==void 0,s.__h!=null?[l]:null,n,l??je(s),s.__h),js(n,s),s.__e!=l&&an(s)))})}function hn(e,t,o,n,r,s,l,c,f,u){var i,p,d,a,h,m,_,y=n&&n.__k||ln,b=y.length;for(o.__k=[],i=0;i<t.length;i++)if((a=o.__k[i]=(a=t[i])==null||typeof a=="boolean"?null:typeof a=="string"||typeof a=="number"||typeof a=="bigint"?Kt(null,a,null,null,a):Array.isArray(a)?Kt(Ft,{children:a},null,null,null):a.__b>0?Kt(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a)!=null){if(a.__=o,a.__b=o.__b+1,(d=y[i])===null||d&&a.key==d.key&&a.type===d.type)y[i]=void 0;else for(p=0;p<b;p++){if((d=y[p])&&a.key==d.key&&a.type===d.type){y[p]=void 0;break}d=null}yn(e,a,d=d||rn,r,s,l,c,f,u),h=a.__e,(p=a.ref)&&d.ref!=p&&(_||(_=[]),d.ref&&_.push(d.ref,null,a),_.push(p,a.__c||h,a)),h!=null?(m==null&&(m=h),typeof a.type=="function"&&a.__k===d.__k?a.__d=f=un(a,f,e):f=dn(e,a,d,y,h,f),typeof o.type=="function"&&(o.__d=f)):f&&d.__e==f&&f.parentNode!=e&&(f=je(d))}for(o.__e=m,i=b;i--;)y[i]!=null&&bn(y[i],y[i]);if(_)for(i=0;i<_.length;i++)mn(_[i],_[++i],_[++i])}function un(e,t,o){for(var n,r=e.__k,s=0;r&&s<r.length;s++)(n=r[s])&&(n.__=e,t=typeof n.type=="function"?un(n,t,o):dn(o,n,n,r,n.__e,t));return t}function dn(e,t,o,n,r,s){var l,c,f;if(t.__d!==void 0)l=t.__d,t.__d=void 0;else if(o==null||r!=s||r.parentNode==null)e:if(s==null||s.parentNode!==e)e.appendChild(r),l=null;else{for(c=s,f=0;(c=c.nextSibling)&&f<n.length;f+=2)if(c==r)break e;e.insertBefore(r,s),l=s}return l!==void 0?l:r.nextSibling}function zs(e,t,o,n,r){var s;for(s in o)s==="children"||s==="key"||s in t||ht(e,s,null,o[s],n);for(s in t)r&&typeof t[s]!="function"||s==="children"||s==="key"||s==="value"||s==="checked"||o[s]===t[s]||ht(e,s,t[s],o[s],n)}function pn(e,t,o){t[0]==="-"?e.setProperty(t,o):e[t]=o==null?"":typeof o!="number"||Os.test(t)?o:o+"px"}function ht(e,t,o,n,r){var s;e:if(t==="style")if(typeof o=="string")e.style.cssText=o;else{if(typeof n=="string"&&(e.style.cssText=n=""),n)for(t in n)o&&t in o||pn(e.style,t,"");if(o)for(t in o)n&&o[t]===n[t]||pn(e.style,t,o[t])}else if(t[0]==="o"&&t[1]==="n")s=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+s]=o,o?n||e.addEventListener(t,s?gn:_n,s):e.removeEventListener(t,s?gn:_n,s);else if(t!=="dangerouslySetInnerHTML"){if(r)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(t!=="href"&&t!=="list"&&t!=="form"&&t!=="tabIndex"&&t!=="download"&&t in e)try{e[t]=o??"";break e}catch{}typeof o=="function"||(o==null||o===!1&&t.indexOf("-")==-1?e.removeAttribute(t):e.setAttribute(t,o))}}function _n(e){this.l[e.type+!1](O.event?O.event(e):e)}function gn(e){this.l[e.type+!0](O.event?O.event(e):e)}function yn(e,t,o,n,r,s,l,c,f){var u,i,p,d,a,h,m,_,y,b,C,E,L,S,I,v=t.type;if(t.constructor!==void 0)return null;o.__h!=null&&(f=o.__h,c=t.__e=o.__e,t.__h=null,s=[c]),(u=O.__b)&&u(t);try{e:if(typeof v=="function"){if(_=t.props,y=(u=v.contextType)&&n[u.__c],b=u?y?y.props.value:u.__:n,o.__c?m=(i=t.__c=o.__c).__=i.__E:("prototype"in v&&v.prototype.render?t.__c=i=new v(_,b):(t.__c=i=new ze(_,b),i.constructor=v,i.render=Ws),y&&y.sub(i),i.props=_,i.state||(i.state={}),i.context=b,i.__n=n,p=i.__d=!0,i.__h=[],i._sb=[]),i.__s==null&&(i.__s=i.state),v.getDerivedStateFromProps!=null&&(i.__s==i.state&&(i.__s=he({},i.__s)),he(i.__s,v.getDerivedStateFromProps(_,i.__s))),d=i.props,a=i.state,p)v.getDerivedStateFromProps==null&&i.componentWillMount!=null&&i.componentWillMount(),i.componentDidMount!=null&&i.__h.push(i.componentDidMount);else{if(v.getDerivedStateFromProps==null&&_!==d&&i.componentWillReceiveProps!=null&&i.componentWillReceiveProps(_,b),!i.__e&&i.shouldComponentUpdate!=null&&i.shouldComponentUpdate(_,i.__s,b)===!1||t.__v===o.__v){for(i.props=_,i.state=i.__s,t.__v!==o.__v&&(i.__d=!1),i.__v=t,t.__e=o.__e,t.__k=o.__k,t.__k.forEach(function(w){w&&(w.__=t)}),C=0;C<i._sb.length;C++)i.__h.push(i._sb[C]);i._sb=[],i.__h.length&&l.push(i);break e}i.componentWillUpdate!=null&&i.componentWillUpdate(_,i.__s,b),i.componentDidUpdate!=null&&i.__h.push(function(){i.componentDidUpdate(d,a,h)})}if(i.context=b,i.props=_,i.__v=t,i.__P=e,E=O.__r,L=0,"prototype"in v&&v.prototype.render){for(i.state=i.__s,i.__d=!1,E&&E(t),u=i.render(i.props,i.state,i.context),S=0;S<i._sb.length;S++)i.__h.push(i._sb[S]);i._sb=[]}else do i.__d=!1,E&&E(t),u=i.render(i.props,i.state,i.context),i.state=i.__s;while(i.__d&&++L<25);i.state=i.__s,i.getChildContext!=null&&(n=he(he({},n),i.getChildContext())),p||i.getSnapshotBeforeUpdate==null||(h=i.getSnapshotBeforeUpdate(d,a)),I=u!=null&&u.type===Ft&&u.key==null?u.props.children:u,hn(e,Array.isArray(I)?I:[I],t,o,n,r,s,l,c,f),i.base=t.__e,t.__h=null,i.__h.length&&l.push(i),m&&(i.__E=i.__=null),i.__e=!1}else s==null&&t.__v===o.__v?(t.__k=o.__k,t.__e=o.__e):t.__e=Ls(o.__e,t,o,n,r,s,l,f);(u=O.diffed)&&u(t)}catch(w){t.__v=null,(f||s!=null)&&(t.__e=c,t.__h=!!f,s[s.indexOf(c)]=null),O.__e(w,t,o)}}function js(e,t){O.__c&&O.__c(t,e),e.some(function(o){try{e=o.__h,o.__h=[],e.some(function(n){n.call(o)})}catch(n){O.__e(n,o.__v)}})}function Ls(e,t,o,n,r,s,l,c){var f,u,i,p=o.props,d=t.props,a=t.type,h=0;if(a==="svg"&&(r=!0),s!=null){for(;h<s.length;h++)if((f=s[h])&&"setAttribute"in f==!!a&&(a?f.localName===a:f.nodeType===3)){e=f,s[h]=null;break}}if(e==null){if(a===null)return document.createTextNode(d);e=r?document.createElementNS("http://www.w3.org/2000/svg",a):document.createElement(a,d.is&&d),s=null,c=!1}if(a===null)p===d||c&&e.data===d||(e.data=d);else{if(s=s&&on.call(e.childNodes),u=(p=o.props||rn).dangerouslySetInnerHTML,i=d.dangerouslySetInnerHTML,!c){if(s!=null)for(p={},h=0;h<e.attributes.length;h++)p[e.attributes[h].name]=e.attributes[h].value;(i||u)&&(i&&(u&&i.__html==u.__html||i.__html===e.innerHTML)||(e.innerHTML=i&&i.__html||""))}if(zs(e,d,p,r,c),i)t.__k=[];else if(h=t.props.children,hn(e,Array.isArray(h)?h:[h],t,o,n,r&&a!=="foreignObject",s,l,s?s[0]:o.__k&&je(o,0),c),s!=null)for(h=s.length;h--;)s[h]!=null&&cn(s[h]);c||("value"in d&&(h=d.value)!==void 0&&(h!==e.value||a==="progress"&&!h||a==="option"&&h!==p.value)&&ht(e,"value",h,p.value,!1),"checked"in d&&(h=d.checked)!==void 0&&h!==e.checked&&ht(e,"checked",h,p.checked,!1))}return e}function mn(e,t,o){try{typeof e=="function"?e(t):e.current=t}catch(n){O.__e(n,o)}}function bn(e,t,o){var n,r;if(O.unmount&&O.unmount(e),(n=e.ref)&&(n.current&&n.current!==e.__e||mn(n,null,t)),(n=e.__c)!=null){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(s){O.__e(s,t)}n.base=n.__P=null,e.__c=void 0}if(n=e.__k)for(r=0;r<n.length;r++)n[r]&&bn(n[r],t,o||typeof e.type!="function");o||e.__e==null||cn(e.__e),e.__=e.__e=e.__d=void 0}function Ws(e,t,o){return this.constructor(e,o)}on=ln.slice,O={__e:function(e,t,o,n){for(var r,s,l;t=t.__;)if((r=t.__c)&&!r.__)try{if((s=r.constructor)&&s.getDerivedStateFromError!=null&&(r.setState(s.getDerivedStateFromError(e)),l=r.__d),r.componentDidCatch!=null&&(r.componentDidCatch(e,n||{}),l=r.__d),l)return r.__E=r}catch(c){e=c}throw e}},nn=0,ze.prototype.setState=function(e,t){var o;o=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=he({},this.state),typeof e=="function"&&(e=e(he({},o),this.props)),e&&he(o,e),e!=null&&this.__v&&(t&&this._sb.push(t),fn(this))},ze.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),fn(this))},ze.prototype.render=Ft,Oe=[],ft.__r=0;var Us=0;function wn(e,t,o,n,r){var s,l,c={};for(l in t)l=="ref"?s=t[l]:c[l]=t[l];var f={type:e,props:c,key:o,ref:s,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--Us,__source:r,__self:n};if(typeof e=="function"&&(s=e.defaultProps))for(l in s)c[l]===void 0&&(c[l]=s[l]);return O.vnode&&O.vnode(f),f}const ti="";class vn extends ze{constructor(o){super(o);k(this,ye,0);k(this,me,null);U(this,"_handleWheel",o=>{const{wheelContainer:n}=this.props,r=o.target;if(!(!r||!n)&&(typeof n=="string"&&r.closest(n)||typeof n=="object")){const s=(this.props.type==="horz"?o.deltaX:o.deltaY)*(this.props.wheelSpeed??1);this.scrollOffset(s)&&o.preventDefault()}});U(this,"_handleMouseMove",o=>{const{dragStart:n}=this.state;n&&(g(this,ye)&&cancelAnimationFrame(g(this,ye)),M(this,ye,requestAnimationFrame(()=>{const r=this.props.type==="horz"?o.clientX-n.x:o.clientY-n.y;this.scroll(n.offset+r*this.props.scrollSize/this.props.clientSize),M(this,ye,0)})),o.preventDefault())});U(this,"_handleMouseUp",()=>{this.state.dragStart&&this.setState({dragStart:!1})});U(this,"_handleMouseDown",o=>{this.state.dragStart||this.setState({dragStart:{x:o.clientX,y:o.clientY,offset:this.scrollPos}}),o.stopPropagation()});U(this,"_handleClick",o=>{const n=o.currentTarget;if(!n)return;const r=n.getBoundingClientRect(),{type:s,clientSize:l,scrollSize:c}=this.props,f=(s==="horz"?o.clientX-r.left:o.clientY-r.top)-this.barSize/2;this.scroll(f*c/l),o.preventDefault()});this.state={scrollPos:this.props.defaultScrollPos??0,dragStart:!1}}get scrollPos(){return this.props.scrollPos??this.state.scrollPos}get controlled(){return this.props.scrollPos!==void 0}get maxScrollPos(){const{scrollSize:o,clientSize:n}=this.props;return Math.max(0,o-n)}get barSize(){const{clientSize:o,scrollSize:n,size:r=12,minBarSize:s=3*r}=this.props;return Math.max(Math.round(o*o/n),s)}componentDidMount(){document.addEventListener("mousemove",this._handleMouseMove),document.addEventListener("mouseup",this._handleMouseUp);const{wheelContainer:o}=this.props;o&&(M(this,me,typeof o=="string"?document:o.current),g(this,me).addEventListener("wheel",this._handleWheel,{passive:!1}))}componentWillUnmount(){document.removeEventListener("mousemove",this._handleMouseMove),document.removeEventListener("mouseup",this._handleMouseUp),g(this,me)&&g(this,me).removeEventListener("wheel",this._handleWheel)}scroll(o){return o=Math.max(0,Math.min(Math.round(o),this.maxScrollPos)),o===this.scrollPos?!1:(this.controlled?this._afterScroll(o):this.setState({scrollPos:o},this._afterScroll.bind(this,o)),!0)}scrollOffset(o){return this.scroll(this.scrollPos+o)}_afterScroll(o){const{onScroll:n}=this.props;n&&n(o,this.props.type??"vert")}render(){const{clientSize:o,type:n,size:r=12,className:s,style:l,left:c,top:f,bottom:u,right:i}=this.props,{maxScrollPos:p,scrollPos:d}=this,{dragStart:a}=this.state,h={left:c,top:f,bottom:u,right:i,...l},m={};return n==="horz"?(h.height=r,h.width=o,m.width=this.barSize,m.left=Math.round(Math.min(p,d)*(o-m.width)/p)):(h.width=r,h.height=o,m.height=this.barSize,m.top=Math.round(Math.min(p,d)*(o-m.height)/p)),wn("div",{className:G("scrollbar",s,{"is-vert":n==="vert","is-horz":n==="horz","is-dragging":a}),style:h,onMouseDown:this._handleClick,children:wn("div",{className:"scrollbar-bar",style:m,onMouseDown:this._handleMouseDown})})}}ye=new WeakMap,me=new WeakMap;function Bt(e,t,o){return e&&(t&&(e=Math.max(t,e)),o&&(e=Math.min(o,e))),e}function Cn({col:e,className:t,height:o,row:n,onRenderCell:r,style:s,outerStyle:l,children:c,outerClass:f,...u}){var v;const i={left:e.left,width:e.realWidth,height:o,...l},{align:p,border:d}=e.setting,a={justifyContent:p?p==="left"?"start":p==="right"?"end":p:void 0,...e.setting.cellStyle,...s},h=["dtable-cell",f,e.setting.className,{"has-border-left":d===!0||d==="left","has-border-right":d===!0||d==="right"}],m=["dtable-cell-content",t],_=[c??((v=n.data)==null?void 0:v[e.name])??""],y=r?r(_,{row:n,col:e},Wt):_,b=[],C=[],E={},L={};let S="div";y==null||y.forEach(w=>{if(typeof w=="object"&&w&&!zo(w)&&("html"in w||"className"in w||"style"in w||"attrs"in w||"children"in w||"tagName"in w)){const qe=w.outer?b:C;w.html?qe.push(x("div",{className:G("dtable-cell-html",w.className),style:w.style,dangerouslySetInnerHTML:{__html:w.html},...w.attrs??{}})):(w.style&&Object.assign(w.outer?i:a,w.style),w.className&&(w.outer?h:m).push(w.className),w.children&&qe.push(w.children),w.attrs&&Object.assign(w.outer?E:L,w.attrs)),w.tagName&&!w.outer&&(S=w.tagName)}else C.push(w)});const I=S;return x("div",{className:G(h),style:i,"data-col":e.name,...u,...E,children:[C.length>0&&x(I,{className:G(m),style:a,...L,children:C}),b]})}function Vt({row:e,className:t,top:o=0,left:n=0,width:r,height:s,cols:l,CellComponent:c=Cn,onRenderCell:f}){return x("div",{className:G("dtable-cells",t),style:{top:o,left:n,width:r,height:s},children:l.map(u=>u.visible?x(c,{col:u,row:e,onRenderCell:f},u.name):null)})}function kn({row:e,className:t,top:o,height:n,fixedLeftCols:r,fixedRightCols:s,scrollCols:l,fixedLeftWidth:c,scrollWidth:f,scrollColsWidth:u,fixedRightWidth:i,scrollLeft:p,CellComponent:d=Cn,onRenderCell:a,style:h,...m}){let _=null;r!=null&&r.length&&(_=x(Vt,{className:"dtable-fixed-left",cols:r,width:c,row:e,CellComponent:d,onRenderCell:a}));let y=null;l!=null&&l.length&&(y=x(Vt,{className:"dtable-flexable",cols:l,left:c-p,width:Math.max(f,u),row:e,CellComponent:d,onRenderCell:a}));let b=null;s!=null&&s.length&&(b=x(Vt,{className:"dtable-fixed-right",cols:s,left:c+f,width:i,row:e,CellComponent:d,onRenderCell:a}));const C={top:o,height:n,lineHeight:`${n-2}px`,...h};return x("div",{className:G("dtable-row",t),style:C,"data-id":e.id,...m,children:[_,y,b]})}function Ks({height:e,onRenderRow:t,...o}){const n={height:e,...o,row:{id:"HEADER",index:-1,top:0},className:"dtable-in-header",top:0};if(t){const r=t({props:n},Wt);r&&Object.assign(n,r)}return x("div",{className:"dtable-header",style:{height:e},children:x(kn,{...n})})}function Fs({className:e,style:t,top:o,rows:n,height:r,rowHeight:s,scrollTop:l,onRenderRow:c,...f}){return t={...t,top:o,height:r},x("div",{className:G("dtable-rows",e),style:t,children:n.map(u=>{const i={className:`dtable-row-${u.index%2?"odd":"even"}`,row:u,top:u.top-l,height:s,...f},p=c==null?void 0:c({props:i,row:u},Wt);return p&&Object.assign(i,p),x(kn,{...i})})})}const ut=new Map,dt=[];function xn(e,t){const{name:o}=e;if(!(t!=null&&t.override)&&ut.has(o))throw new Error(`DTable: Plugin with name ${o} already exists`);ut.set(o,e),t!=null&&t.buildIn&&!dt.includes(o)&&dt.push(o)}function V(e,t){xn(e,t);const o=n=>{if(!n)return e;const{defaultOptions:r,...s}=e;return{...s,defaultOptions:{...r,...n}}};return o.plugin=e,o}function En(e){return ut.delete(e)}function Bs(e){if(typeof e=="string"){const t=ut.get(e);return t||console.warn(`DTable: Cannot found plugin "${e}"`),t}if(typeof e=="function"&&"plugin"in e)return e.plugin;if(typeof e=="object")return e;console.warn("DTable: Invalid plugin",e)}function Rn(e,t,o){return t.forEach(n=>{var s;if(!n)return;const r=Bs(n);r&&(o.has(r.name)||((s=r.plugins)!=null&&s.length&&Rn(e,r.plugins,o),e.push(r),o.add(r.name)))}),e}function Vs(e=[],t=!0){return t&&dt.length&&e.unshift(...dt),e!=null&&e.length?Rn([],e,new Set):[]}function Sn(){return{cols:[],data:[],rowKey:"id",width:"100%",height:"auto",rowHeight:35,defaultColWidth:80,minColWidth:20,maxColWidth:9999,header:!0,footer:!1,headerHeight:0,footerHeight:0,rowHover:!0,colHover:!1,cellHover:!1,bordered:!1,striped:!0,responsive:!1,scrollbarHover:!0,horzScrollbarPos:"outside"}}const oi="";function Gs(e){const{tag:t,className:o,style:n,renders:r,generateArgs:s=[],generatorThis:l,generators:c,onGenerate:f,onRenderItem:u,...i}=e,p=[o],d={...n},a=[],h=[];return r.forEach(m=>{const _=[];typeof m=="string"&&c&&c[m]&&(m=c[m]),typeof m=="function"?f?_.push(...f.call(l,m,a,...s)):_.push(...m.call(l,a,...s)??[]):_.push(m),_.forEach(y=>{y!=null&&(typeof y=="object"&&!mo(y)&&("html"in y||"__html"in y||"className"in y||"style"in y||"attrs"in y||"children"in y)?y.html?a.push(To("div",{className:G(y.className),style:y.style,dangerouslySetInnerHTML:{__html:y.html},...y.attrs??{}})):y.__html?h.push(y.__html):(y.style&&Object.assign(d,y.style),y.className&&p.push(y.className),y.children&&a.push(y.children),y.attrs&&Object.assign(i,y.attrs)):a.push(y))})}),h.length&&Object.assign(i,{dangerouslySetInnerHTML:{__html:h}}),[{className:G(p),style:d,...i},a]}function Mn({tag:e="div",...t}){const[o,n]=Gs(t);return Co(e,o,...n)}let Xs=(mt=class extends He{constructor(o){super(o);k(this,bt);k(this,wt);k(this,vt);k(this,Ct);k(this,Ve);k(this,St);k(this,Mt);k(this,$t);U(this,"ref",Ko());k(this,be,0);k(this,xe,void 0);k(this,ue,!1);k(this,Q,void 0);k(this,de,void 0);k(this,F,[]);k(this,Y,void 0);k(this,Z,new Map);k(this,Ee,{});k(this,Fe,void 0);k(this,Be,[]);U(this,"updateLayout",()=>{g(this,be)&&cancelAnimationFrame(g(this,be)),M(this,be,requestAnimationFrame(()=>{M(this,Y,void 0),this.forceUpdate(),M(this,be,0)}))});k(this,se,(o,n)=>{n=n||o.type;const r=g(this,Z).get(n);if(r!=null&&r.length){for(const s of r)if(s.call(this,o)===!1){o.stopPropagation(),o.preventDefault();break}}});k(this,Re,o=>{g(this,se).call(this,o,`window_${o.type}`)});k(this,Se,o=>{g(this,se).call(this,o,`document_${o.type}`)});k(this,kt,(o,n)=>{if(this.options.onRenderRow){const r=this.options.onRenderRow.call(this,o,n);r&&Object.assign(o.props,r)}return g(this,F).forEach(r=>{if(r.onRenderRow){const s=r.onRenderRow.call(this,o,n);s&&Object.assign(o.props,s)}}),o.props});k(this,xt,(o,n)=>(this.options.onRenderHeaderRow&&(o.props=this.options.onRenderHeaderRow.call(this,o,n)),g(this,F).forEach(r=>{r.onRenderHeaderRow&&(o.props=r.onRenderHeaderRow.call(this,o,n))}),o.props));k(this,Ge,(o,n,r)=>{const{row:s,col:l}=n;o[0]=this.getCellValue(s,l);const c=s.id==="HEADER"?"onRenderHeaderCell":"onRenderCell";return l.setting[c]&&(o=l.setting[c].call(this,o,n,r)),this.options[c]&&(o=this.options[c].call(this,o,n,r)),g(this,F).forEach(f=>{f[c]&&(o=f[c].call(this,o,n,r))}),o});k(this,Xe,(o,n)=>{n==="horz"?this.scroll({scrollLeft:o}):this.scroll({scrollTop:o})});k(this,Et,o=>{var c,f,u,i,p;const n=this.getPointerInfo(o);if(!n)return;const{rowID:r,colName:s,cellElement:l}=n;if(r==="HEADER")l&&((c=this.options.onHeaderCellClick)==null||c.call(this,o,{colName:s,element:l}),g(this,F).forEach(d=>{var a;(a=d.onHeaderCellClick)==null||a.call(this,o,{colName:s,element:l})}));else{const{rowElement:d}=n,a=this.layout.visibleRows.find(h=>h.id===r);if(l){if(((f=this.options.onCellClick)==null?void 0:f.call(this,o,{colName:s,rowID:r,rowInfo:a,element:l,rowElement:d}))===!0)return;for(const h of g(this,F))if(((u=h.onCellClick)==null?void 0:u.call(this,o,{colName:s,rowID:r,rowInfo:a,element:l,rowElement:d}))===!0)return}if(((i=this.options.onRowClick)==null?void 0:i.call(this,o,{rowID:r,rowInfo:a,element:d}))===!0)return;for(const h of g(this,F))if(((p=h.onRowClick)==null?void 0:p.call(this,o,{rowID:r,rowInfo:a,element:d}))===!0)return}});k(this,Rt,o=>{const n=o.key.toLowerCase();if(["pageup","pagedown","home","end"].includes(n))return!this.scroll({to:n.replace("page","")})});M(this,xe,o.id??`dtable-${Ps(10)}`),this.state={scrollTop:0,scrollLeft:0,renderCount:0},M(this,de,Object.freeze(Vs(o.plugins))),g(this,de).forEach(n=>{var c;const{methods:r,data:s,state:l}=n;r&&Object.entries(r).forEach(([f,u])=>{typeof u=="function"&&Object.assign(this,{[f]:u.bind(this)})}),s&&Object.assign(g(this,Ee),s.call(this)),l&&Object.assign(this.state,l.call(this)),(c=n.onCreate)==null||c.call(this,n)})}get options(){var o;return((o=g(this,Y))==null?void 0:o.options)||g(this,Q)||Sn()}get plugins(){return g(this,F)}get layout(){return g(this,Y)}get id(){return g(this,xe)}get data(){return g(this,Ee)}get parent(){var o;return this.props.parent??((o=this.ref.current)==null?void 0:o.parentElement)}componentWillReceiveProps(){M(this,Q,void 0)}componentDidMount(){if(g(this,ue)?this.forceUpdate():B(this,Ve,go).call(this),g(this,F).forEach(o=>{let{events:n}=o;n&&(typeof n=="function"&&(n=n.call(this)),Object.entries(n).forEach(([r,s])=>{s&&this.on(r,s)}))}),this.on("click",g(this,Et)),this.on("keydown",g(this,Rt)),this.options.responsive){if(typeof ResizeObserver<"u"){const{parent:o}=this;if(o){const n=new ResizeObserver(this.updateLayout);n.observe(o),M(this,Fe,n)}}this.on("window_resize",this.updateLayout)}g(this,F).forEach(o=>{var n;(n=o.onMounted)==null||n.call(this)})}componentDidUpdate(){g(this,ue)?B(this,Ve,go).call(this):g(this,F).forEach(o=>{var n;(n=o.onUpdated)==null||n.call(this)})}componentWillUnmount(){var n;(n=g(this,Fe))==null||n.disconnect();const{current:o}=this.ref;if(o)for(const r of g(this,Z).keys())r.startsWith("window_")?window.removeEventListener(r.replace("window_",""),g(this,Re)):r.startsWith("document_")?document.removeEventListener(r.replace("document_",""),g(this,Se)):o.removeEventListener(r,g(this,se));g(this,F).forEach(r=>{var s;(s=r.onUnmounted)==null||s.call(this)}),g(this,de).forEach(r=>{var s;(s=r.onDestory)==null||s.call(this)}),M(this,Ee,{}),g(this,Z).clear()}on(o,n,r){var l;r&&(o=`${r}_${o}`);const s=g(this,Z).get(o);s?s.push(n):(g(this,Z).set(o,[n]),o.startsWith("window_")?window.addEventListener(o.replace("window_",""),g(this,Re)):o.startsWith("document_")?document.addEventListener(o.replace("document_",""),g(this,Se)):(l=this.ref.current)==null||l.addEventListener(o,g(this,se)))}off(o,n,r){var c;r&&(o=`${r}_${o}`);const s=g(this,Z).get(o);if(!s)return;const l=s.indexOf(n);l>=0&&s.splice(l,1),s.length||(g(this,Z).delete(o),o.startsWith("window_")?window.removeEventListener(o.replace("window_",""),g(this,Re)):o.startsWith("document_")?document.removeEventListener(o.replace("document_",""),g(this,Se)):(c=this.ref.current)==null||c.removeEventListener(o,g(this,se)))}emitCustomEvent(o,n){g(this,se).call(this,n instanceof Event?n:new CustomEvent(o,{detail:n}),o)}scroll(o,n){const{scrollLeft:r,scrollTop:s,rowsHeightTotal:l,rowsHeight:c,rowHeight:f,colsInfo:{scrollWidth:u,scrollColsWidth:i}}=this.layout,{to:p}=o;let{scrollLeft:d,scrollTop:a}=o;if(p==="up"||p==="down")a=s+(p==="down"?1:-1)*Math.floor(c/f)*f;else if(p==="left"||p==="right")d=r+(p==="right"?1:-1)*u;else if(p==="home")a=0;else if(p==="end")a=l-c;else if(p==="left-begin")d=0;else if(p==="right-end")d=i-u;else{const{offsetLeft:m,offsetTop:_}=o;typeof m=="number"&&(d=r+m),typeof _=="number"&&(d=s+_)}const h={};return typeof d=="number"&&(d=Math.max(0,Math.min(d,i-u)),d!==r&&(h.scrollLeft=d)),typeof a=="number"&&(a=Math.max(0,Math.min(a,l-c)),a!==s&&(h.scrollTop=a)),Object.keys(h).length?(this.setState(h,()=>{var m;(m=this.options.onScroll)==null||m.call(this,h),n==null||n.call(this,!0)}),!0):(n==null||n.call(this,!1),!1)}getColInfo(o){if(o===void 0)return;if(typeof o=="object")return o;const{colsMap:n,colsList:r}=this.layout;return typeof o=="number"?r[o]:n[o]}getRowInfo(o){if(o===void 0)return;if(typeof o=="object")return o;if(o===-1||o==="HEADER")return{id:"HEADER",index:-1,top:0};const{rows:n,rowsMap:r}=this.layout;return typeof o=="number"?n[o]:r[o]}getCellValue(o,n){var f;const r=typeof o=="object"?o:this.getRowInfo(o);if(!r)return;const s=typeof n=="object"?n:this.getColInfo(n);if(!s)return;let l=r.id==="HEADER"?s.setting.title??s.setting.name:(f=r.data)==null?void 0:f[s.name];const{cellValueGetter:c}=this.options;return c&&(l=c.call(this,r,s,l)),l}getRowInfoByIndex(o){return this.layout.rows[o]}update(o={},n){if(!g(this,Q))return;typeof o=="function"&&(n=o,o={});const{dirtyType:r,state:s}=o;if(r==="layout")M(this,Y,void 0);else if(r==="options"){if(M(this,Q,void 0),!g(this,Y))return;M(this,Y,void 0)}this.setState(s??(l=>({renderCount:l.renderCount+1})),n)}getPointerInfo(o){const n=o.target;if(!n||n.closest(".no-cell-event"))return;const r=n.closest(".dtable-cell");if(!r)return;const s=r.closest(".dtable-row");if(!s)return;const l=r==null?void 0:r.getAttribute("data-col"),c=s==null?void 0:s.getAttribute("data-id");if(!(typeof l!="string"||typeof c!="string"))return{cellElement:r,rowElement:s,colName:l,rowID:c,target:n}}i18n(o,n,r){return Ne(g(this,Be),o,n,r,this.options.lang)??`{i18n:${o}}`}render(){const o=B(this,$t,_s).call(this),{className:n,rowHover:r,colHover:s,cellHover:l,bordered:c,striped:f,scrollbarHover:u}=this.options,i={width:o==null?void 0:o.width,height:o==null?void 0:o.height},p=["dtable",n,{"dtable-hover-row":r,"dtable-hover-col":s,"dtable-hover-cell":l,"dtable-bordered":c,"dtable-striped":f,"dtable-scrolled-down":((o==null?void 0:o.scrollTop)??0)>0,"scrollbar-hover":u}],d=[];return o&&g(this,F).forEach(a=>{var m;const h=(m=a.onRender)==null?void 0:m.call(this,o);h&&(h.style&&Object.assign(i,h.style),h.className&&p.push(h.className),h.children&&d.push(h.children))}),x("div",{id:g(this,xe),className:G(p),style:i,ref:this.ref,tabIndex:-1,children:[o&&B(this,bt,as).call(this,o),o&&B(this,wt,fs).call(this,o),o&&B(this,vt,hs).call(this,o),o&&B(this,Ct,us).call(this,o)]})}},be=new WeakMap,xe=new WeakMap,ue=new WeakMap,Q=new WeakMap,de=new WeakMap,F=new WeakMap,Y=new WeakMap,Z=new WeakMap,Ee=new WeakMap,Fe=new WeakMap,Be=new WeakMap,se=new WeakMap,Re=new WeakMap,Se=new WeakMap,bt=new WeakSet,as=function(o){const{header:n,colsInfo:r,headerHeight:s,scrollLeft:l}=o;if(!n)return null;if(n===!0)return x(Ks,{scrollLeft:l,height:s,onRenderCell:g(this,Ge),onRenderRow:g(this,xt),...r});const c=Array.isArray(n)?n:[n];return x(Mn,{className:"dtable-header",style:{height:s},renders:c,generateArgs:[o],generatorThis:this})},wt=new WeakSet,fs=function(o){const{headerHeight:n,rowsHeight:r,visibleRows:s,rowHeight:l,colsInfo:c,scrollLeft:f,scrollTop:u}=o;return x(Fs,{top:n,height:r,rows:s,rowHeight:l,scrollLeft:f,scrollTop:u,onRenderCell:g(this,Ge),onRenderRow:g(this,kt),...c})},vt=new WeakSet,hs=function(o){const{footer:n}=o;if(!n)return null;const r=typeof n=="function"?n.call(this,o):Array.isArray(n)?n:[n];return x(Mn,{className:"dtable-footer",style:{height:o.footerHeight,top:o.rowsHeight+o.headerHeight},renders:r,generateArgs:[o],generatorThis:this,generators:o.footerGenerators})},Ct=new WeakSet,us=function(o){const n=[],{scrollLeft:r,colsInfo:s,scrollTop:l,rowsHeight:c,rowsHeightTotal:f,footerHeight:u}=o,{scrollColsWidth:i,scrollWidth:p}=s,{scrollbarSize:d=12,horzScrollbarPos:a}=this.options;return i>p&&n.push(x(vn,{type:"horz",scrollPos:r,scrollSize:i,clientSize:p,onScroll:g(this,Xe),left:s.fixedLeftWidth,bottom:(a==="inside"?0:-d)+u,size:d,wheelContainer:this.ref},"horz")),f>c&&n.push(x(vn,{type:"vert",scrollPos:l,scrollSize:f,clientSize:c,onScroll:g(this,Xe),right:0,size:d,top:o.headerHeight,wheelContainer:this.ref},"vert")),n.length?n:null},Ve=new WeakSet,go=function(){var o;M(this,ue,!1),(o=this.options.afterRender)==null||o.call(this),g(this,F).forEach(n=>{var r;return(r=n.afterRender)==null?void 0:r.call(this)})},kt=new WeakMap,xt=new WeakMap,Ge=new WeakMap,Xe=new WeakMap,Et=new WeakMap,Rt=new WeakMap,St=new WeakSet,ds=function(){if(g(this,Q))return!1;const n={...Sn(),...g(this,de).reduce((r,s)=>{const{defaultOptions:l}=s;return l&&Object.assign(r,l),r},{}),...this.props};return M(this,Q,n),M(this,F,g(this,de).reduce((r,s)=>{const{when:l,options:c}=s;return(!l||l(n))&&(r.push(s),c&&Object.assign(n,typeof c=="function"?c.call(this,n):c)),r},[])),M(this,Be,[this.options.i18n,...this.plugins.map(r=>r.i18n)].filter(Boolean)),!0},Mt=new WeakSet,ps=function(){var ns,ss;const{plugins:o}=this;let n=g(this,Q);const r={flex:x("div",{style:"flex:auto"}),divider:x("div",{style:"width:1px;margin:var(--space);background:var(--color-border);height:50%"})};o.forEach(R=>{var ie;const W=(ie=R.beforeLayout)==null?void 0:ie.call(this,n);W&&(n={...n,...W}),Object.assign(r,R.footer)});const{defaultColWidth:s,minColWidth:l,maxColWidth:c}=n,f=[],u=[],i=[],p={},d=[],a=[];let h=0,m=0,_=0;n.cols.forEach(R=>{if(R.hidden)return;const{name:W,type:ie="",fixed:le=!1,flex:ve=!1,width:Qe=s,minWidth:Ze=l,maxWidth:po=c,...Qr}=R,T={name:W,type:ie,setting:{name:W,type:ie,fixed:le,flex:ve,width:Qe,minWidth:Ze,maxWidth:po,...Qr},flex:le?0:ve===!0?1:typeof ve=="number"?ve:0,left:0,width:Bt(Qe,Ze,po),realWidth:0,visible:!0,index:d.length};o.forEach(rs=>{var is,ls;const Dt=(is=rs.colTypes)==null?void 0:is[ie];if(Dt){const cs=typeof Dt=="function"?Dt(T):Dt;cs&&Object.assign(T.setting,cs)}(ls=rs.onAddCol)==null||ls.call(this,T)}),T.width=Bt(T.setting.width??T.width,T.setting.minWidth??Ze,T.setting.maxWidth??po),T.realWidth=T.realWidth||T.width,le==="left"?(T.left=h,h+=T.width,f.push(T)):le==="right"?(T.left=m,m+=T.width,u.push(T)):(T.left=_,_+=T.width,i.push(T)),T.flex&&a.push(T),d.push(T),p[T.name]=T});let y=n.width,b=0;const C=h+_+m;if(typeof y=="function"&&(y=y.call(this,C)),y==="auto")b=C;else if(y==="100%"){const{parent:R}=this;if(R)b=R.clientWidth;else{b=0,M(this,ue,!0);return}}else b=y??0;const{data:E,rowKey:L="id",rowHeight:S}=n,I=[],v=(R,W,ie)=>{var ve,Qe;const le={data:ie??{[L]:R},id:R,index:I.length,top:0};if(ie||(le.lazy=!0),I.push(le),((ve=n.onAddRow)==null?void 0:ve.call(this,le,W))!==!1){for(const Ze of o)if(((Qe=Ze.onAddRow)==null?void 0:Qe.call(this,le,W))===!1)return}};if(typeof E=="number")for(let R=0;R<E;R++)v(`${R}`,R);else Array.isArray(E)&&E.forEach((R,W)=>{typeof R=="object"?v(`${R[L]??""}`,W,R):v(`${R??""}`,W)});let w=I;const qe={};if(n.onAddRows){const R=n.onAddRows.call(this,w);R&&(w=R)}for(const R of o){const W=(ns=R.onAddRows)==null?void 0:ns.call(this,w);W&&(w=W)}w.forEach((R,W)=>{qe[R.id]=R,R.index=W,R.top=R.index*S});const{header:Zn,footer:es}=n,fo=Zn?n.headerHeight||S:0,ho=es?n.footerHeight||S:0;let re=n.height,we=0;const ts=w.length*S,uo=fo+ho+ts;if(typeof re=="function"&&(re=re.call(this,uo)),re==="auto")we=uo;else if(typeof re=="object")we=Math.min(re.max,Math.max(re.min,uo));else if(re==="100%"){const{parent:R}=this;if(R)we=R.clientHeight;else{we=0,M(this,ue,!0);return}}else we=re;const qr=we-fo-ho,Jr=b-h-m,Je={options:n,allRows:I,width:b,height:we,rows:w,rowsMap:qe,rowHeight:S,rowsHeight:qr,rowsHeightTotal:ts,header:Zn,footer:es,footerGenerators:r,headerHeight:fo,footerHeight:ho,colsMap:p,colsList:d,flexCols:a,colsInfo:{fixedLeftCols:f,fixedRightCols:u,scrollCols:i,fixedLeftWidth:h,scrollWidth:Jr,scrollColsWidth:_,fixedRightWidth:m}},os=(ss=n.onLayout)==null?void 0:ss.call(this,Je);os&&Object.assign(Je,os),o.forEach(R=>{if(R.onLayout){const W=R.onLayout.call(this,Je);W&&Object.assign(Je,W)}}),M(this,Y,Je)},$t=new WeakSet,_s=function(){(B(this,St,ds).call(this)||!g(this,Y))&&B(this,Mt,ps).call(this);const{layout:o}=this;if(!o)return;let{scrollLeft:n}=this.state;const{flexCols:r,colsInfo:{scrollCols:s,scrollWidth:l,scrollColsWidth:c}}=o;if(r.length){const C=l-c;if(C>0){const E=r.reduce((S,I)=>S+I.flex,0);let L=0;r.forEach(S=>{const I=Math.min(C-L,Math.ceil(C*(S.flex/E)));S.realWidth=I+S.width,L+=S.realWidth})}else r.forEach(E=>{E.realWidth=E.width})}n=Math.min(Math.max(0,c-l),n);let f=0;s.forEach(C=>{C.left=f,f+=C.realWidth,C.visible=C.left+C.realWidth>=n&&C.left<=n+l});const{rowsHeightTotal:u,rowsHeight:i,rows:p,rowHeight:d}=o,a=Math.min(Math.max(0,u-i),this.state.scrollTop),h=Math.floor(a/d),m=a+i,_=Math.min(p.length,Math.ceil(m/d)),y=[],{rowDataGetter:b}=this.options;for(let C=h;C<_;C++){const E=p[C];E.lazy&&b&&(E.data=b([E.id])[0],E.lazy=!1),y.push(E)}return o.visibleRows=y,o.scrollTop=a,o.scrollLeft=n,o},U(mt,"addPlugin",xn),U(mt,"removePlugin",En),mt);function $n(e,t){t!==void 0?e.data.hoverCol=t:t=e.data.hoverCol;const{current:o}=e.ref;if(!o)return;const n="dtable-col-hover";o.querySelectorAll(`.${n}`).forEach(r=>r.classList.remove(n)),typeof t=="string"&&t.length&&o.querySelectorAll(`.dtable-cell[data-col="${t}"]`).forEach(r=>r.classList.add(n))}const Ys=V({name:"col-hover",defaultOptions:{colHover:!1},when:e=>!!e.colHover,events:{mouseover(e){var r;const{colHover:t}=this.options;if(!t)return;const o=(r=e.target)==null?void 0:r.closest(".dtable-cell");if(!o||t==="header"&&!o.closest(".dtable-header"))return;const n=(o==null?void 0:o.getAttribute("data-col"))??!1;$n(this,n)},mouseleave(){$n(this,!1)}}},{buildIn:!0}),si="";function qs(e,t){var l,c;typeof e=="boolean"&&(t=e,e=void 0);const o=this.state.checkedRows,n={},{canRowCheckable:r}=this.options,s=(f,u)=>{r&&!r.call(this,f)||!!o[f]===u||(u?o[f]=!0:delete o[f],n[f]=u)};if(e===void 0?(t===void 0&&(t=!Dn.call(this)),(l=this.layout)==null||l.allRows.forEach(({id:f})=>{s(f,!!t)})):(Array.isArray(e)||(e=[e]),e.forEach(f=>{s(f,t??!o[f])})),Object.keys(n).length){const f=(c=this.options.beforeCheckRows)==null?void 0:c.call(this,e,n,o);f&&Object.keys(f).forEach(u=>{f[u]?o[u]=!0:delete o[u]}),this.setState({checkedRows:{...o}},()=>{var u;(u=this.options.onCheckChange)==null||u.call(this,n)})}return n}function Js(e){return this.state.checkedRows[e]??!1}function Dn(){var o,n;const e=this.getChecks().length,{canRowCheckable:t}=this.options;return t?e===((o=this.layout)==null?void 0:o.allRows.reduce((r,s)=>r+(t.call(this,s.id)?1:0),0)):e===((n=this.layout)==null?void 0:n.allRows.length)}function Qs(){return Object.keys(this.state.checkedRows)}const Zs=V({name:"checkable",defaultOptions:{checkable:!0},when:e=>!!e.checkable,state(){return{checkedRows:{}}},methods:{toggleCheckRows:qs,isRowChecked:Js,isAllRowChecked:Dn,getChecks:Qs},i18n:{zh_cn:{checkedCountInfo:"已选择 {selected} 项",totalCountInfo:"共 {total} 项"},en:{checkedCountInfo:"Selected {selected} items",totalCountInfo:"Total {total} items"}},footer:{checkbox(){const e=this.isAllRowChecked();return[x("div",{style:{padding:"0 calc(3 * var(--space))",display:"flex",alignItems:"center"},onClick:()=>this.toggleCheckRows(),children:x("input",{type:"checkbox",checked:e})})]},checkedInfo(e,t){const o=this.getChecks().length,n=[];return o&&n.push(this.i18n("checkedCountInfo",{selected:o})),n.push(this.i18n("totalCountInfo",{total:t.allRows.length})),[x("div",{children:n.join(", ")})]}},onRenderCell(e,{row:t,col:o}){var c;const{id:n}=t,{canRowCheckable:r}=this.options;if(r&&!r.call(this,n))return e;const{checkbox:s}=o.setting;if(typeof s=="function"?s.call(this,n):s){const f=this.isRowChecked(n),u=((c=this.options.checkboxRender)==null?void 0:c.call(this,f,n))??x("input",{type:"checkbox",checked:f});e.unshift(u),e.push({className:"has-checkbox"})}return e},onRenderHeaderCell(e,{row:t,col:o}){var l;const{id:n}=t,{checkbox:r}=o.setting;if(typeof r=="function"?r.call(this,n):r){const c=this.isAllRowChecked(),f=((l=this.options.checkboxRender)==null?void 0:l.call(this,c,n))??x("input",{type:"checkbox",checked:c});e.unshift(f),e.push({className:"has-checkbox"})}return e},onRenderRow({props:e,row:t}){if(this.isRowChecked(t.id))return{className:G(e.className,"is-checked")}},onHeaderCellClick(e){const t=e.target;if(!t)return;const o=t.closest('input[type="checkbox"],.dtable-checkbox');o&&(this.toggleCheckRows(o.checked),e.stopPropagation())},onRowClick(e,{rowID:t}){const o=e.target;if(!o)return;(o.closest('input[type="checkbox"],.dtable-checkbox')||this.options.checkOnClickRow)&&this.toggleCheckRows(t)}}),ii="",li="";var In=(e=>(e.unknown="",e.collapsed="collapsed",e.expanded="expanded",e.hidden="hidden",e.normal="normal",e))(In||{});function Gt(e){const t=this.data.nestedMap.get(e);if(!t||t.state!=="")return t??{state:"normal",level:-1};if(!t.parent&&!t.children)return t.state="normal",t;const o=this.state.collapsedRows,n=t.children&&o&&o[e];let r=!1,{parent:s}=t;for(;s;){const l=Gt.call(this,s);if(l.state!=="expanded"){r=!0;break}s=l.parent}return t.state=r?"hidden":n?"collapsed":t.children?"expanded":"normal",t.level=t.parent?Gt.call(this,t.parent).level+1:0,t}function er(e,t){let o=this.state.collapsedRows??{};const{nestedMap:n}=this.data;if(e==="HEADER")if(t===void 0&&(t=!An.call(this)),t){const r=n.entries();for(const[s,l]of r)l.state==="expanded"&&(o[s]=!0)}else o={};else{const r=Array.isArray(e)?e:[e];t===void 0&&(t=!o[r[0]]),r.forEach(s=>{const l=n.get(s);t&&(l!=null&&l.children)?o[s]=!0:delete o[s]})}this.update({dirtyType:"layout",state:{collapsedRows:{...o}}},()=>{var r;(r=this.options.onNestedChange)==null||r.call(this)})}function An(){const e=this.data.nestedMap.values();for(const t of e)if(t.state==="expanded")return!1;return!0}function Nn(e,t=0,o,n=0){var r;o||(o=[...e.keys()]);for(const s of o){const l=e.get(s);l&&(l.level===n&&(l.order=t++),(r=l.children)!=null&&r.length&&(t=Nn(e,t,l.children,n+1)))}return t}function Tn(e,t,o,n){const r=e.getNestedRowInfo(t);return!r||r.state===""||!r.children||r.children.forEach(s=>{n[s]=o,Tn(e,s,o,n)}),r}function Hn(e,t,o,n,r){var c;const s=e.getNestedRowInfo(t);if(!s||s.state==="")return;((c=s.children)==null?void 0:c.every(f=>{const u=!!(n[f]!==void 0?n[f]:r[f]);return o===u}))&&(n[t]=o),s.parent&&Hn(e,s.parent,o,n,r)}const tr=V({name:"nested",defaultOptions:{nested:!0,nestedParentKey:"parent",asParentKey:"asParent",nestedIndent:20,canSortTo(e,t){const{nestedMap:o}=this.data,n=o.get(e.id),r=o.get(t.id);return(n==null?void 0:n.parent)===(r==null?void 0:r.parent)},beforeCheckRows(e,t,o){if(!this.options.checkable||!(e!=null&&e.length))return;const n={};return Object.entries(t).forEach(([r,s])=>{const l=Tn(this,r,s,n);l!=null&&l.parent&&Hn(this,l.parent,s,n,o)}),n}},when:e=>!!e.nested,data(){return{nestedMap:new Map}},methods:{toggleRow:er,isAllCollapsed:An,getNestedRowInfo:Gt},beforeLayout(){this.data.nestedMap.clear()},onAddRow(e){var r,s;const{nestedMap:t}=this.data,o=(r=e.data)==null?void 0:r[this.options.nestedParentKey??"parent"],n=t.get(e.id)??{state:"",level:0};if(n.parent=o,(s=e.data)!=null&&s[this.options.asParentKey??"asParent"]&&(n.children=[]),t.set(e.id,n),o){let l=t.get(o);l||(l={state:"",level:0},t.set(o,l)),l.children||(l.children=[]),l.children.push(e.id)}},onAddRows(e){return e=e.filter(t=>this.getNestedRowInfo(t.id).state!=="hidden"),Nn(this.data.nestedMap),e.sort((t,o)=>{const n=this.getNestedRowInfo(t.id),r=this.getNestedRowInfo(o.id),s=(n.order??0)-(r.order??0);return s===0?t.index-o.index:s}),e},onRenderCell(e,{col:t,row:o}){var c;const{id:n,data:r}=o,{nestedToggle:s}=t.setting,l=this.getNestedRowInfo(n);if(s&&(l.children||l.parent)&&e.unshift(((c=this.options.onRenderNestedToggle)==null?void 0:c.call(this,l,n,t,r))??x("a",{role:"button",className:`dtable-nested-toggle state${l.children?"":" is-no-child"}`,children:x("span",{className:"toggle-icon"})})),l.level){let{nestedIndent:f=s}=t.setting;f&&(f===!0&&(f=this.options.nestedIndent??12),e.unshift(x("div",{className:"dtable-nested-indent",style:{width:f*l.level+"px"}})))}return e},onRenderHeaderCell(e,{row:t,col:o}){var r;const{id:n}=t;return o.setting.nestedToggle&&e.unshift(((r=this.options.onRenderNestedToggle)==null?void 0:r.call(this,void 0,n,o,void 0))??x("a",{type:"button",className:"dtable-nested-toggle state",children:x("span",{className:"toggle-icon"})})),e},onRenderRow({props:e,row:t}){const o=this.getNestedRowInfo(t.id);return{className:G(e.className,`is-${o.state}`),"data-parent":o.parent}},onRenderHeaderRow({props:e}){return e.className=G(e.className,`is-${this.isAllCollapsed()?"collapsed":"expanded"}`),e},onHeaderCellClick(e){const t=e.target;if(!(!t||!t.closest(".dtable-nested-toggle")))return this.toggleRow("HEADER"),!0},onCellClick(e,{rowID:t}){const o=e.target;if(!(!o||!this.getNestedRowInfo(t).children||!o.closest(".dtable-nested-toggle")))return this.toggleRow(t),!0}}),or=e=>e?(e instanceof Date||(typeof e=="string"&&(e=e.trim(),/^\d+$/.test(e)&&(e=Number.parseInt(e,10))),typeof e=="number"&&e<1e10&&(e*=1e3),e=new Date(e)),e):new Date,nr=(e,t="yyyy-MM-dd hh:mm")=>{e=or(e);const o={"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours(),"H+":e.getHours()%12,"m+":e.getMinutes(),"s+":e.getSeconds(),"S+":e.getMilliseconds()};return/(y+)/i.test(t)&&(t=t.replace(RegExp.$1,`${e.getFullYear()}`.substring(4-RegExp.$1.length))),Object.keys(o).forEach(n=>{if(new RegExp(`(${n})`).test(t)){const r=`${o[n]}`;t=t.replace(RegExp.$1,RegExp.$1.length===1?r:`00${r}`.substring(r.length))}}),t},ai="",sr=V({name:"rich",colTypes:{html:{onRenderCell(e){return e[0]={html:e[0]},e}},link:{onRenderCell(e,{col:t,row:o}){const{linkTemplate:n="",linkProps:r}=t.setting,s=Ae(n,o.data);return e[0]=x("a",{href:s,...r,children:e[0]}),e}},avatar:{onRenderCell(e,{col:t,row:o}){const{data:n}=o,{avatarWithName:r,avatarClass:s="size-xs circle",avatarKey:l=`${t.name}Avatar`}=t.setting,c=x("div",{className:`avatar ${s} flex-none`,children:x("img",{src:n?n[l]:""})});return r?e.unshift(c):e[0]=c,e}},circleProgress:{align:"center",onRenderCell(e,{col:t}){const{circleSize:o=24,circleBorderSize:n=1,circleBgColor:r="var(--color-border)",circleColor:s="var(--color-success-500)"}=t.setting,l=(o-n)/2,c=o/2,f=e[0];return e[0]=x("svg",{width:o,height:o,children:[x("circle",{cx:c,cy:c,r:l,"stroke-width":n,stroke:r,fill:"transparent"}),x("circle",{cx:c,cy:c,r:l,"stroke-width":n,stroke:s,fill:"transparent","stroke-linecap":"round","stroke-dasharray":Math.PI*l*2,"stroke-dashoffset":Math.PI*l*2*(100-f)/100,style:{transformOrigin:"center",transform:"rotate(-90deg)"}}),x("text",{x:c,y:c+n,"dominant-baseline":"middle","text-anchor":"middle",style:{fontSize:`${l}px`},children:Math.round(f)})]}),e}},actionButtons:{onRenderCell(e,{col:t,row:o}){var c;const n=(c=o.data)==null?void 0:c[t.name];if(!n)return e;const{actionBtnTemplate:r='<button type="button" data-action="{action}" title="{title}" class="{className}"><i class="icon icon-{icon}"></i></button>',actionBtnData:s={},actionBtnClass:l="btn text-primary square size-sm ghost"}=t.setting;return[{html:n.map(f=>{typeof f=="string"&&(f={action:f});const u=s[f.action];return u&&(f={className:l,...u,...f}),Ae(r,f)}).join(" ")}]}},format:{onRenderCell(e,{col:t}){let{format:o}=t.setting;if(!o)return e;typeof o=="string"&&(o={type:"text",format:o});const{format:n,type:r}=o,s=e[0];return typeof n=="function"?e[0]=r==="html"?{html:n(s)}:n(s):r==="datetime"?e[0]=nr(s,n):r==="html"?e[0]={html:Ae(n,s)}:e[0]=Ae(n,s),e}}}},{buildIn:!0}),rr=V({name:"sort-type",onRenderHeaderCell(e,{col:t}){const{sortType:o}=t.setting;if(o){const{sortLink:n=this.options.sortLink,sortAttrs:r}=t.setting,s=o===!0?"none":o;if(e.push(x("div",{className:`dtable-sort dtable-sort-${s}`}),{outer:!0,attrs:{"data-sort":s}}),n){const l=typeof n=="function"?n.call(this,t,s):n;e.push({tagName:"a",attrs:{href:l,...r}})}}return e}},{buildIn:!0}),ir=Object.freeze(Object.defineProperty({__proto__:null,NestedRowState:In,checkable:Zs,colHover:Ys,nested:tr,rich:sr,sortType:rr},Symbol.toStringTag,{value:"Module"}));class Ce extends Po{}U(Ce,"NAME","dtable"),U(Ce,"Component",Xs),U(Ce,"definePlugin",V),U(Ce,"removePlugin",En),U(Ce,"plugins",ir),z(Ce,"DTable");function Xt(e,t){return Object.entries(t).reduce((o,[n,r])=>{const s=o[n];return s?r===void 0?delete o[n]:Object.keys(r).forEach(l=>{const c=r[l];c===void 0?delete s[l]:s[l]=c}):r!==void 0&&(o[n]=r),o},e)}function Le(e){return Object.entries(e).reduce((t,[o,n])=>(t&&typeof n=="object"&&(t[o]={...n}),t),{})}const Pn=V({name:"draft",defaultOptions:{draft:!0,selectAllOnFocus:!0,history:20},when:e=>!!e.draft,state(){return{stagingDraft:{},appliedDraft:{}}},methods:{getCellDraftValue(e,t){const o=this.getRowInfo(e);if(!o)return;const n=this.getColInfo(t);if(!n)return;const{id:r}=o,{name:s}=n,l=this.state.stagingDraft[r];if(l&&s in l)return l[s];const c=this.state.appliedDraft[r];return c&&s in c?c[s]:this.getCellValue(o,n)},stageDraft(e,t){var s;const{stagingDraft:o}=this.state;if(((s=this.options.onStageDraft)==null?void 0:s.call(this,e,o))===!1)return;const n=Le(o);Xt(o,e);const r=()=>{var l,c;(l=t==null?void 0:t.callback)==null||l.call(t,e),(c=this.options.afterStageDraft)==null||c.call(this,e,this.state.stagingDraft,n)};t!=null&&t.skipUpdate?r():this.update(r)},applyDraft(e,t){const{stagingDraft:o,appliedDraft:n}=this.state,r=Le(n);Object.entries(e).forEach(([l,c])=>{const f=o[l];f&&typeof c=="object"&&(c===null?delete o[l]:Object.keys(c).forEach(i=>{f[i]===c[i]&&delete f[i]}),Object.keys(f).length||delete o[l]);const u=n[l];u?c===null?delete n[l]:Object.assign(u,c):c!==null&&(n[l]=c)});const s=()=>{var l,c;(l=t==null?void 0:t.callback)==null||l.call(t,e),(c=this.options.afterApplyDraft)==null||c.call(this,e,this.state.appliedDraft,r)};t!=null&&t.skipUpdate?s():this.forceUpdate(s)},renderDraftCell(e,{col:t,row:o}){const n=`${this.getCellDraftValue(o,t)??""}`;return e[0]={children:n,attrs:{title:n}},e}},onRenderCell(e,...t){return this.options.skipRenderDraftCell?e:this.renderDraftCell(e,...t)},onRenderHeaderCell(e,...t){return this.options.skipRenderDraftCell?e:this.renderDraftCell(e,...t)}}),di="",lr=V({name:"editable",plugins:[Pn],defaultOptions:{editable:!0,selectAllOnFocus:!0,history:20,skipRenderDraftCell:!0},when:e=>!!e.editable,events:{dblclick(e){this.editCell(this.getPointerInfo(e))}},data(){return{editingInputRef:Ko()}},methods:{isCellEditing(e,t){const{editingCell:o}=this.state;return!!o&&o.rowID===e&&o.colName===t},editCell(e){const{editable:t,headerEditable:o}=this.options;e&&(typeof t=="function"&&!t(e.rowID,e.colName)||!o&&e.rowID==="HEADER")&&(e=void 0),this.data.editingInputRef.current=null,this.data.needAutoFocus=!0,this.setState({editingCell:e?{colName:e.colName,rowID:e.rowID}:void 0})},deleteCells(e,t=null){const o={};return e.forEach(n=>{const r=o[n.rowID];r?r[n.colName]=t:o[n.rowID]={[n.colName]:t}}),Object.keys(o).length?(this.stageDraft(o),!0):!1},handleEditingInputChange(){var s,l;const e=(s=this.data.editingInputRef.current)==null?void 0:s.value,{editingCell:t}=this.state;if(typeof e!="string"||!t)return;const{rowID:o,colName:n}=t,r=this.getCellDraftValue(o,n);r!==e&&((l=this.options.onEditCell)==null?void 0:l.call(this,{rowID:o,colName:n,value:e,oldValue:r}))!==!1&&this.stageDraft({[o]:{[n]:e}})},handleEditingInputBlur(){this.editCell()},handleEditingKeyDown(e){var t;e.key==="Enter"?(t=this.data.editingInputRef.current)==null||t.blur():e.key==="Esc"&&this.editCell()},renderEditableCell(e,{col:t,row:o},n){const{id:r}=o;if(this.isCellEditing(r,t.name)){const s=`${this.getCellDraftValue(r,t.name)??""}`;return[{outer:!0,className:"is-editing",children:x("input",{ref:this.data.editingInputRef,class:"dtable-editing-input",type:"text",defaultValue:s,style:{textAlign:t.setting.align},onChange:this.handleEditingInputChange,onBlur:this.handleEditingInputBlur,onKeyDown:this.handleEditingKeyDown,onPaste:this.options.onPasteToCell})}]}return this.renderDraftCell(e,{row:o,col:t},n)}},onUpdated(){const e=this.data.editingInputRef.current;e&&this.data.needAutoFocus&&(this.data.needAutoFocus=!1,e.focus({preventScroll:!0}),this.options.selectAllOnFocus&&e.select())},onRender(){return{className:{"dtable-editable":this.options.editable,"dtable-editing":this.state.editingCell}}},onRenderCell(...e){return this.renderEditableCell(...e)},onRenderHeaderCell(...e){return this.renderEditableCell(...e)}}),Yt=V({name:"mousemove",events:{mousemove(e){this.data.mmRafID&&(cancelAnimationFrame(this.data.mmRafID),this.data.mmRafID=0),this.data.mmRafID=requestAnimationFrame(()=>{this.emitCustomEvent("mousemovesmooth",e)}),e.preventDefault()},document_mousemove(e){this.data.dmmRafID&&(cancelAnimationFrame(this.data.dmmRafID),this.data.dmmRafID=0),this.data.dmmRafID=requestAnimationFrame(()=>{this.emitCustomEvent("document_mousemovesmooth",e)}),e.preventDefault()}}}),gi="";function On(e,t,o){const{colResizing:n}=e.state;if(!n)return;const r=Math.round(t.clientX-n.startX);if(!r&&!o)return;const s={};o&&(s.colResizing=void 0);const{colsSizes:l}=e.state,{colName:c}=n;l[n.colName]=n.startSize+r,s.colsSizes={...l},e.update({dirtyType:"layout",state:s},o?()=>{var u;const f=e.getColInfo(c);f&&((u=e.options.onColResize)==null||u.call(e,c,f.realWidth))}:void 0),t.stopPropagation(),t.preventDefault()}const cr=V({name:"resize",defaultOptions:{colResize:!0},when:e=>!!e.colResize,plugins:[Yt],state(){return{colsSizes:{}}},events:{mousedown(e){var n;const t=e.target.closest(".dtable-col-splitter");if(!t)return;const o=(n=t.closest(".dtable-cell"))==null?void 0:n.dataset.col;if(o)return this.setState({colResizing:{colName:o,startSize:this.state.colsSizes[o]??0,startX:e.clientX}}),e.stopPropagation(),!1},dblclick(e){var r;const t=e.target.closest(".dtable-col-splitter");if(!t)return;const o=(r=t.closest(".dtable-cell"))==null?void 0:r.dataset.col;if(!o)return;const{colsSizes:n}=this.state;n[o]&&(delete n[o],this.update({dirtyType:"layout",state:{colResizing:void 0,colsSizes:{...n}}},()=>{var s;(s=this.options.onColResize)==null||s.call(this,o,0)}))},document_mouseup(e){if(this.state.colResizing)return On(this,e,!0),!1},document_mousemovesmooth(e){if(this.state.colResizing)return On(this,e),!1}},onRenderHeaderCell(e,{col:t}){var n;const{colResize:o}=this.options;return!t.flex&&(typeof o!="function"||o.call(this,t.name)!==!1)&&(e.push({className:"has-col-splitter",children:x("div",{className:"dtable-col-splitter no-cell-event"}),outer:!0}),((n=this.state.colResizing)==null?void 0:n.colName)===t.name&&e.push({className:"has-col-resizing",outer:!0})),e},onAddCol(e){const t=this.state.colsSizes[e.name];typeof t=="number"&&(e.realWidth=Bt(e.width+t,e.setting.minWidth,e.setting.maxWidth))},onRender(){if(this.state.colResizing)return{className:"has-col-resizing"}}}),mi="",ar=/C(\d+)R(\d+)/i,fr=/(?:C(\d+))?(?:R(\d+))?/i;function zn(e){const t=ar.exec(e);if(!t||t.length<3)return;const[,o,n]=t;return{col:+o,row:+n}}function qt(e){const t=fr.exec(e);if(!t)return;const[,o=-1,n=-1]=t,r=+o,s=+n;return{col:r,row:s}}function Jt(e){const t=[],o=qt(e);if(!o)return t;const{col:n,row:r}=o;if(n>=0)if(r>=0)t.push({col:n,row:r});else{const s=this.layout.rows.length;for(let l=0;l<s;l++)t.push({col:n,row:l})}else if(r>=0){const{colsInfo:s}=this.layout,l=s.fixedLeftCols.length+s.scrollCols.length+s.fixedRightCols.length;for(let c=0;c<l;c++)t.push({col:c,row:r})}return t}function jn(e){const[t,o]=e.split(":");if(t){if(!o)return Jt.call(this,t)}else return o?Jt.call(this,o):[];const n=qt(t),r=qt(o);if(!n||!r)return[];const s=Math.min(n.col,r.col),l=Math.max(n.col,r.col),c=Math.min(n.row,r.row),f=Math.max(n.row,r.row),u=[],i=Object.keys(this.layout.colsMap).length;for(let p=s;p<=l;p++)if(c<0||f<0){const d=this.layout.rows.length;for(let a=0;a<d;a++)u.push({col:p,row:a})}else for(let d=c;d<=f;d++)if(p<0)for(let a=0;a<i;a++)u.push({col:a,row:d});else u.push({col:p,row:d});return u}function hr(e){return e.reduce((t,o)=>(typeof o=="object"?t.push(o):o.includes(":")?t.push(...jn.call(this,o)):t.push(...Jt.call(this,o)),t),[])}function Qt(e,t){const{col:o,row:n}=e,r=[];if(o>=0&&r.push(`C${o}`),n>=0&&r.push(`R${n}`),t){const s=Qt(t);s&&r.push(":",s)}return r.join("")}function Zt(e,t){const{selectable:o}=e.options;return typeof o=="function"?o.call(e,t):!!o}function eo(e,t={}){var d;Array.isArray(e)||(e=[e]);let o=hr.call(this,e);const n=(d=this.options.beforeSelectCells)==null?void 0:d.call(this,o);n&&(o=n);const{clearBefore:r=!0,deselect:s,selecting:l,callback:c}=t,{selectingMap:f,selectedMap:u}=this.state,i=l?f:u;f.clear(),r&&u.clear();let p=!1;return s?o.forEach(a=>{if(!Zt(this,a))return;const{col:h,row:m}=a,_=i.get(h);_&&(_.delete(m),_.size||i.delete(h)),p=!0}):o.forEach(a=>{if(!Zt(this,a))return;const{col:h,row:m}=a,_=i.get(h);_?_.add(m):i.set(h,new Set([m])),p=!0}),p?(this.update({},()=>{var a;c==null||c.call(this,o),(a=this.options.onSelectCells)==null||a.call(this,o)}),o):[]}function ur(e){const{selectedMap:t}=this.state;let o=-1,n=-1;for(const[r,s]of t.entries()){n=n<0?r:Math.min(r,n);const l=Math.min(...s);o=o<0?l:Math.min(l,o)}if(!(o<0||n<0)&&(e==="down"?o++:e==="up"?o--:e==="left"?n--:n++,o>=0&&n>=0&&o<this.layout.rows.length&&n<this.layout.colsList.length)){const r={col:n,row:o};if(Zt(this,r))return this.scrollTo(r),this.selectCells([r]),r}}function dr(e,t){return eo.call(this,e,{...t,selecting:!0})}function pr(e,t){return eo.call(this,e,{...t,deselect:!0})}function _r(){const{colsInfo:e}=this.layout,t=e.fixedLeftCols.length+e.scrollCols.length+e.fixedRightCols.length,o=this.layout.rows.length,{selectedMap:n}=this.state,r=typeof this.options.selectable=="function"?this.options.selectable:!1;for(let s=0;s<t;s++){let l=n.get(s);l||(l=new Set,n.set(s,l));for(let c=0;c<o;c++)r&&!r({row:c,col:s})||l.add(c)}this.forceUpdate()}function gr(){const{selectedMap:e}=this.state;return e.size?(e.clear(),this.forceUpdate(),!0):!1}function yr(e){var o;const t=typeof e=="string"?zn(e):e;return t?((o=this.state.selectedMap.get(t.col))==null?void 0:o.has(t.row))??!1:!1}function Ln(e,t){for(const[,o]of e.state.selectedMap.entries())if(o.has(t))return!0;for(const[,o]of e.state.selectingMap.entries())if(o.has(t))return!0;return!1}function mr(e,t){var o,n;return!!((o=e.state.selectedMap.get(t))!=null&&o.size||(n=e.state.selectingMap.get(t))!=null&&n.size)}function br(e){var o;const t=typeof e=="string"?zn(e):e;return t?((o=this.state.selectingMap.get(t.col))==null?void 0:o.has(t.row))??!1:!1}function wr(){const e=[];for(const[t,o]of this.state.selectedMap.entries())for(const n of o)e.push({col:t,row:n});return e}function vr(){let e=0;for(const t of this.state.selectedMap.values())e+=t.size;return e}function to(e,t,o){var u,i;const n=e.getPointerInfo(t);if(!n||n.target.closest("input,textarea,[contenteditable]"))return;const{rowID:r,colName:s}=n,l=((u=e.getColInfo(s))==null?void 0:u.index)??-1;if(l<0)return;const c=r==="HEADER";if(c&&(o!=null&&o.ignoreHeaderCell))return;const f=c?-1:((i=e.getRowInfo(r))==null?void 0:i.index)??-1;return{col:l,row:f}}const Cr=V({name:"selectable",defaultOptions:{selectable:!0,markSelectRange:!0},when:e=>!!e.selectable,plugins:[Yt],state(){return{selectedMap:new Map,selectingMap:new Map}},methods:{selectCells:eo,selectNextCell:ur,selectingCells:dr,deselectCells:pr,isCellSelected:yr,isCellSelecting:br,getSelectedCells:wr,selectAllCells:_r,deselectAllCells:gr,getSelectedCellsSize:vr},events:{mousedown(e){if(this.data.disableSelectable)return;const t=to(this,e);e.button!==0&&(!t||this.isCellSelected(t))||(this.data.selectingStart=t,this.startScrollToMouse(),t&&e.stopPropagation())},document_mouseup(e){const{selectingStart:t}=this.data;if(this.stopScrollToMouse(),!t)return;this.data.selectingStart=void 0;const o=to(this,e);if(o){const n=Qt(t,o);n&&(this.selectCells(n),e.stopPropagation())}},document_click(e){const t=e.target;if(!t)return;const{ignoreDeselectOn:o}=this.options;t.closest(`#${this.id}${o?`,${o}`:""}`)||this.deselectAllCells()},mousemovesmooth(e){const t=this,{selectingStart:o}=t.data;if(!o)return;const n=to(t,e);if(!n)return;const r=Qt(o,n);r&&(t.selectingCells(r),e.preventDefault(),e.stopPropagation())}},onRender(){if(this.options.selectable)return{className:"dtable-selectable"}},onRenderRow({props:e,row:t}){if(Ln(this,t.index))return{className:G(e.className,"has-cell-select")}},onRenderCell(e,{row:t,col:o}){const n=this.getRowInfo(t.id);if(!n)return e;const r={col:o.index,row:n.index};return this.isCellSelecting(r)?e.push({outer:!0,className:"is-select is-selecting"}):this.isCellSelected(r)&&e.push({outer:!0,className:"is-select is-selected"}),this.options.markSelectRange&&o.name==="INDEX"&&Ln(this,n.index)&&e.push({outer:!0,className:"has-cell-selected"}),e},onRenderHeaderCell(e,{col:t}){return this.options.markSelectRange&&t.name!=="INDEX"&&mr(this,t.index)&&e.push({outer:!0,className:"has-cell-selected"}),e}});var oo=typeof navigator<"u"?navigator.userAgent.toLowerCase().indexOf("firefox")>0:!1;function no(e,t,o,n){e.addEventListener?e.addEventListener(t,o,n):e.attachEvent&&e.attachEvent("on".concat(t),function(){o(window.event)})}function Wn(e,t){for(var o=t.slice(0,t.length-1),n=0;n<o.length;n++)o[n]=e[o[n].toLowerCase()];return o}function Un(e){typeof e!="string"&&(e=""),e=e.replace(/\s/g,"");for(var t=e.split(","),o=t.lastIndexOf("");o>=0;)t[o-1]+=",",t.splice(o,1),o=t.lastIndexOf("");return t}function kr(e,t){for(var o=e.length>=t.length?e:t,n=e.length>=t.length?t:e,r=!0,s=0;s<o.length;s++)n.indexOf(o[s])===-1&&(r=!1);return r}for(var We={backspace:8,"⌫":8,tab:9,clear:12,enter:13,"↩":13,return:13,esc:27,escape:27,space:32,left:37,up:38,right:39,down:40,del:46,delete:46,ins:45,insert:45,home:36,end:35,pageup:33,pagedown:34,capslock:20,num_0:96,num_1:97,num_2:98,num_3:99,num_4:100,num_5:101,num_6:102,num_7:103,num_8:104,num_9:105,num_multiply:106,num_add:107,num_enter:108,num_subtract:109,num_decimal:110,num_divide:111,"⇪":20,",":188,".":190,"/":191,"`":192,"-":oo?173:189,"=":oo?61:187,";":oo?59:186,"'":222,"[":219,"]":221,"\\":220},q={"⇧":16,shift:16,"⌥":18,alt:18,option:18,"⌃":17,ctrl:17,control:17,"⌘":91,cmd:91,command:91},so={16:"shiftKey",18:"altKey",17:"ctrlKey",91:"metaKey",shiftKey:16,ctrlKey:17,altKey:18,metaKey:91},K={16:!1,18:!1,17:!1,91:!1},j={},pt=1;pt<20;pt++)We["f".concat(pt)]=111+pt;var N=[],Kn=!1,Fn="all",Bn=[],_t=function(t){return We[t.toLowerCase()]||q[t.toLowerCase()]||t.toUpperCase().charCodeAt(0)},xr=function(t){return Object.keys(We).find(function(o){return We[o]===t})},Er=function(t){return Object.keys(q).find(function(o){return q[o]===t})};function Vn(e){Fn=e||"all"}function Ue(){return Fn||"all"}function Rr(){return N.slice(0)}function Sr(){return N.map(function(e){return xr(e)||Er(e)||String.fromCharCode(e)})}function Mr(e){var t=e.target||e.srcElement,o=t.tagName,n=!0;return(t.isContentEditable||(o==="INPUT"||o==="TEXTAREA"||o==="SELECT")&&!t.readOnly)&&(n=!1),n}function $r(e){return typeof e=="string"&&(e=_t(e)),N.indexOf(e)!==-1}function Dr(e,t){var o,n;e||(e=Ue());for(var r in j)if(Object.prototype.hasOwnProperty.call(j,r))for(o=j[r],n=0;n<o.length;)o[n].scope===e?o.splice(n,1):n++;Ue()===e&&Vn(t||"all")}function Ir(e){var t=e.keyCode||e.which||e.charCode,o=N.indexOf(t);if(o>=0&&N.splice(o,1),e.key&&e.key.toLowerCase()==="meta"&&N.splice(0,N.length),(t===93||t===224)&&(t=91),t in K){K[t]=!1;for(var n in q)q[n]===t&&(J[n]=!1)}}function Ar(e){if(typeof e>"u")Object.keys(j).forEach(function(l){return delete j[l]});else if(Array.isArray(e))e.forEach(function(l){l.key&&ro(l)});else if(typeof e=="object")e.key&&ro(e);else if(typeof e=="string"){for(var t=arguments.length,o=new Array(t>1?t-1:0),n=1;n<t;n++)o[n-1]=arguments[n];var r=o[0],s=o[1];typeof r=="function"&&(s=r,r=""),ro({key:e,scope:r,method:s,splitKey:"+"})}}var ro=function(t){var o=t.key,n=t.scope,r=t.method,s=t.splitKey,l=s===void 0?"+":s,c=Un(o);c.forEach(function(f){var u=f.split(l),i=u.length,p=u[i-1],d=p==="*"?"*":_t(p);if(j[d]){n||(n=Ue());var a=i>1?Wn(q,u):[];j[d]=j[d].filter(function(h){var m=r?h.method===r:!0;return!(m&&h.scope===n&&kr(h.mods,a))})}})};function Gn(e,t,o,n){if(t.element===n){var r;if(t.scope===o||t.scope==="all"){r=t.mods.length>0;for(var s in K)Object.prototype.hasOwnProperty.call(K,s)&&(!K[s]&&t.mods.indexOf(+s)>-1||K[s]&&t.mods.indexOf(+s)===-1)&&(r=!1);(t.mods.length===0&&!K[16]&&!K[18]&&!K[17]&&!K[91]||r||t.shortcut==="*")&&t.method(e,t)===!1&&(e.preventDefault?e.preventDefault():e.returnValue=!1,e.stopPropagation&&e.stopPropagation(),e.cancelBubble&&(e.cancelBubble=!0))}}}function Xn(e,t){var o=j["*"],n=e.keyCode||e.which||e.charCode;if(J.filter.call(this,e)){if((n===93||n===224)&&(n=91),N.indexOf(n)===-1&&n!==229&&N.push(n),["ctrlKey","altKey","shiftKey","metaKey"].forEach(function(h){var m=so[h];e[h]&&N.indexOf(m)===-1?N.push(m):!e[h]&&N.indexOf(m)>-1?N.splice(N.indexOf(m),1):h==="metaKey"&&e[h]&&N.length===3&&(e.ctrlKey||e.shiftKey||e.altKey||(N=N.slice(N.indexOf(m))))}),n in K){K[n]=!0;for(var r in q)q[r]===n&&(J[r]=!0);if(!o)return}for(var s in K)Object.prototype.hasOwnProperty.call(K,s)&&(K[s]=e[so[s]]);e.getModifierState&&!(e.altKey&&!e.ctrlKey)&&e.getModifierState("AltGraph")&&(N.indexOf(17)===-1&&N.push(17),N.indexOf(18)===-1&&N.push(18),K[17]=!0,K[18]=!0);var l=Ue();if(o)for(var c=0;c<o.length;c++)o[c].scope===l&&(e.type==="keydown"&&o[c].keydown||e.type==="keyup"&&o[c].keyup)&&Gn(e,o[c],l,t);if(n in j){for(var f=0;f<j[n].length;f++)if((e.type==="keydown"&&j[n][f].keydown||e.type==="keyup"&&j[n][f].keyup)&&j[n][f].key){for(var u=j[n][f],i=u.splitKey,p=u.key.split(i),d=[],a=0;a<p.length;a++)d.push(_t(p[a]));d.sort().join("")===N.sort().join("")&&Gn(e,u,l,t)}}}}function Nr(e){return Bn.indexOf(e)>-1}function J(e,t,o){N=[];var n=Un(e),r=[],s="all",l=document,c=0,f=!1,u=!0,i="+",p=!1;for(o===void 0&&typeof t=="function"&&(o=t),Object.prototype.toString.call(t)==="[object Object]"&&(t.scope&&(s=t.scope),t.element&&(l=t.element),t.keyup&&(f=t.keyup),t.keydown!==void 0&&(u=t.keydown),t.capture!==void 0&&(p=t.capture),typeof t.splitKey=="string"&&(i=t.splitKey)),typeof t=="string"&&(s=t);c<n.length;c++)e=n[c].split(i),r=[],e.length>1&&(r=Wn(q,e)),e=e[e.length-1],e=e==="*"?"*":_t(e),e in j||(j[e]=[]),j[e].push({keyup:f,keydown:u,scope:s,mods:r,shortcut:n[c],method:o,key:n[c],splitKey:i,element:l});typeof l<"u"&&!Nr(l)&&window&&(Bn.push(l),no(l,"keydown",function(d){Xn(d,l)},p),Kn||(Kn=!0,no(window,"focus",function(){N=[]},p)),no(l,"keyup",function(d){Xn(d,l),Ir(d)},p))}function Tr(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"all";Object.keys(j).forEach(function(o){var n=j[o].find(function(r){return r.scope===t&&r.shortcut===e});n&&n.method&&n.method()})}var io={getPressedKeyString:Sr,setScope:Vn,getScope:Ue,deleteScope:Dr,getPressedKeyCodes:Rr,isPressed:$r,filter:Mr,trigger:Tr,unbind:Ar,keyMap:We,modifier:q,modifierMap:so};for(var lo in io)Object.prototype.hasOwnProperty.call(io,lo)&&(J[lo]=io[lo]);if(typeof window<"u"){var Hr=window.hotkeys;J.noConflict=function(e){return e&&window.hotkeys===J&&(window.hotkeys=Hr),J},window.hotkeys=J}const Pr=V({name:"hotkey",data(){return{hotkeys:new Map}},when:e=>!!e.hotkeys,methods:{hotkeyHandler(e,t){var o;(o=this.data.hotkeys.get(t.key))==null||o.call(this,e,t)}},onMounted(){const{hotkeys:e}=this.options;if(!e)return;const t=new Map;Object.keys(e).forEach(n=>{const r=e[n];n.split(",").forEach(s=>{s=s.trim(),s.length&&t.set(s,r)})});const o=[...t.keys()];o.length&&(this.data.keys=o.join(","),J(this.data.keys,{element:this.ref.current},this.hotkeyHandler),this.data.hotkeys=t)},onUnmounted(){this.data.keys&&J.unbind(this.data.keys,this.hotkeyHandler)}});let Or=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce((t,o)=>(o&=63,o<36?t+=o.toString(36):o<62?t+=(o-26).toString(36).toUpperCase():o>62?t+="-":t+="_",t),"");const ao=class{constructor(t,o="local"){k(this,$e);k(this,Ye,void 0);k(this,pe,void 0);k(this,ee,void 0);k(this,Me,void 0);M(this,Ye,o),M(this,pe,`ZUI_STORE:${t??Or()}`),M(this,ee,o==="local"?localStorage:sessionStorage)}get type(){return g(this,Ye)}get session(){return this.type==="session"?this:(g(this,Me)||M(this,Me,new ao(g(this,pe),"session")),g(this,Me))}get(t,o){const n=g(this,ee).getItem(B(this,$e,It).call(this,t));return typeof n=="string"?JSON.parse(n):n??o}set(t,o){if(o==null)return this.remove(t);g(this,ee).setItem(B(this,$e,It).call(this,t),JSON.stringify(o))}remove(t){g(this,ee).removeItem(B(this,$e,It).call(this,t))}each(t){for(let o=0;o<g(this,ee).length;o++){const n=g(this,ee).key(o);if(n!=null&&n.startsWith(g(this,pe))){const r=g(this,ee).getItem(n);typeof r=="string"&&t(n.substring(g(this,pe).length+1),JSON.parse(r))}}}getAll(){const t={};return this.each((o,n)=>{t[o]=n}),t}};let co=ao;Ye=new WeakMap,pe=new WeakMap,ee=new WeakMap,Me=new WeakMap,$e=new WeakSet,It=function(t){return`${g(this,pe)}:${t}`};const zr=V({name:"store",defaultOptions:{store:!0},when:e=>!!e.store,data(){return{store:new co(this.id)}}});function gt(e,t){const o=new Set([...Object.keys(e),...Object.keys(t)]);return Array.from(o).reduce((n,r)=>{const s=e[r]||{},l=t[r]||{};if(s===l)return n;const c=new Set([...Object.keys(s),...Object.keys(l)]),f={};let u=!1;return c.forEach(i=>{const p=s[i],d=l[i];p!==d&&(f[i]=p,u=!0)}),u&&(n[r]=f),n},{})}const jr=V({name:"history",plugins:[zr,Pn],defaultOptions:{history:!0,historyTarget:"staging",historyThreshold:10},when:e=>!!e.history,options(e){const{afterApplyDraft:t,afterStageDraft:o,historyTarget:n,history:r}=e;return{afterApplyDraft:r&&n==="applied"?(s,l,c)=>{t&&t.call(this,s,l,c),this.addHistory({after:gt(l,c),before:gt(c,l)})}:t,afterStageDraft:r&&n!=="applied"?(s,l,c)=>{o&&o.call(this,s,l,c),this.addHistory({after:gt(l,c),before:gt(c,l)})}:o}},data(){return{historyIdx:1}},state(){return{historyCursor:0,historyItems:[]}},methods:{addHistory(e){let{historyCursor:t,historyItems:o}=this.state;t>0&&(o.splice(0,t).forEach(s=>{typeof s=="number"&&this.data.store.session.remove(`HISTORY:${this.id}:${s}`)}),t=0),o=[e,...o];const{historyThreshold:n}=this.options;if(n&&o.length>n)for(let r=n;r<o.length;r++){const s=o[r];if(typeof s=="number")break;const l=this.data.historyIdx++;this.data.store.session.set(`HISTORY:${this.id}:${l}`,s),o[r]=l}this.setState({historyCursor:t,historyItems:o})},getHistory(e){if(typeof e=="object")return e;const{historyCursor:t,historyItems:o}=this.state;e===void 0&&(e=t);const n=o[e];return typeof n=="number"?this.data.store.session.get(`HISTORY:${this.id}:${n}`):n},applyHistory(e,t,o){const n={historyCursor:e},r=this.options.historyTarget==="applied",{appliedDraft:s,stagingDraft:l}=this.state,c=Le((r?s:l)??{});r?n.appliedDraft=Xt(s,t):n.stagingDraft=Xt(l,t),this.setState(n,()=>{var u;o==null||o();const f=Object.entries(t).reduce((i,[p,d])=>{if(d){const a={};Object.entries(d).forEach(([h,m])=>{a[h]=m===void 0?this.getCellDraftValue(p,h):m}),i[p]=a}return i},{});(u=this.options.onHistoryApplied)==null||u.call(this,f,r?s:l??{},c)})},undoHistory(e){if(!this.canUndoHistory())return!1;const{historyCursor:t}=this.state,o=this.getHistory(t);return o?(this.applyHistory(t+1,Le(o.before),e),!0):!1},redoHistory(e){if(!this.canRedoHistory())return!1;const{historyCursor:t}=this.state,o=this.getHistory(t-1);return o?(this.applyHistory(t-1,Le(o.after),e),!0):!1},canUndoHistory(){const{historyCursor:e,historyItems:t}=this.state;return t.length>0&&e<t.length},canRedoHistory(){return this.state.historyCursor>0}}}),ki="";function Lr(){var y,b;const{scrollToMouse:e}=this.data;if(!e)return this.stopScrollToMouse();const{position:t,startTime:o,delay:n}=e;if(!t||Date.now()-o<n)return;const r=(b=(y=this.ref.current)==null?void 0:y.querySelector(".dtable-rows"))==null?void 0:b.getBoundingClientRect();if(!r)return;const{maxStep:s,detectPadding:l,speed:c}=e,{x:f,y:u}=t,{left:i,top:p,right:d,bottom:a}=r;let h=0;f<i-l?h=-Math.max(s,i-l-f):f>d-l&&(h=Math.max(s,f-(d-l)));let m=0;u<p-l?m=-Math.max(s,p-l-u):u>a-l&&(m=Math.max(s,u-(a-l)));const _={};h!==0&&(_.scrollLeft=this.layout.scrollLeft+c*h),m!==0&&(_.scrollTop=this.layout.scrollTop+c*m),Object.keys(_).length&&this.scroll(_)}const Wr=V({name:"autoscroll",plugins:[Yt],events:{document_mousemovesmooth(e){if(!this.data.scrollToMouse)return;const{clientX:t,clientY:o}=e;this.data.scrollToMouse.position={x:t,y:o}}},methods:{scrollTo({col:e,row:t,extra:o=2}){const n=this.getColInfo(e),r=this.getRowInfo(t);if(!n&&!r)return!1;const s={},{layout:l}=this;if(n){const{scrollLeft:c,colsInfo:f}=l,u=n.left+n.realWidth;n.left<c?s.scrollLeft=n.left-o:u>f.scrollWidth+c&&(s.scrollLeft=u-f.scrollWidth+o)}if(r){const{scrollTop:c,rowHeight:f,rowsHeight:u}=l,i=r.top+f;r.top<c?s.scrollTop=r.top-o:i>u+c&&(s.scrollTop=i-u+o)}return this.scroll(s),!0},startScrollToMouse(e){const t={interval:60,speed:.2,delay:200,maxStep:this.options.rowHeight,onlyInside:!1,detectPadding:30,startTime:Date.now(),...e};this.data.scrollToMouse=t,clearInterval(this.data.scrollToTimer),this.data.scrollToTimer=window.setInterval(Lr.bind(this),t.interval)},stopScrollToMouse(){clearInterval(this.data.scrollToTimer),this.data.scrollToMouse=void 0}},onUnmounted(){clearInterval(this.data.scrollToTimer)}});function Ur(e,t){const{colsCount:o,rowsCount:n}=e.getGridSize(),{cols:r=[]}=t,{showRowIndex:s,defaultColWidth:l}=e.options,c=[];s!==!1&&c.push({width:`${n+1}`.length*8+24,name:"INDEX",fixed:"left",align:"right",title:"#",...r.find(u=>u.name==="INDEX")});const f={...e.state.appliedDraft.HEADER,...e.state.stagingDraft.HEADER};for(let u=0;u<o;++u){const i=`C${u+1}`;if(f[i]===!1)continue;const p={name:i,width:l,...r.find(d=>d.name===i)};c.push(p)}return{cols:c,data:n}}function Kr(e,t,o){var n,r;return t.name==="INDEX"?e.id==="HEADER"?o:e.index+1:e.id!=="HEADER"?(r=(n=this.options.datasource.data)==null?void 0:n[+e.id])==null?void 0:r[+t.name.replace("C","")-1]:o}function yt(e,t,o){e.selectNextCell(o)&&(t.preventDefault(),t.stopPropagation())}const Fr={delete(){this.deleteSelections()},cut(){this.cutSelections()},selectAll(e){this.selectAllCells(),e.preventDefault()},paste(){this.pasteToSelection()},copy(e){this.copySelections(),e.preventDefault()},focus(){const e=this.getSelectedCells()[0];if(!e)return;const t=this.getColInfo(e.col),o=this.getRowInfo(e.row);!t||!o||this.editCell({rowID:o.id,colName:t.name})},cancel(){this.deselectAllCells()},undo(){this.undoHistory()},redo(){this.redoHistory()},selectRight(e){yt(this,e,"right")},selectLeft(e){yt(this,e,"left")},selectDown(e){yt(this,e,"down")},selectUp(e){yt(this,e,"up")}};function Yn(e,t){Array.isArray(e)||(e=[e]);let o=0,n=0;const r=t==null?void 0:t.ignoreEmptyCell;return e.forEach(s=>{Object.entries(s).forEach(([l,c])=>{l!=="HEADER"&&(o=Math.max(o,+l+1),n=Math.max(n,...Object.keys(c).map(f=>r&&Xr(c[f])||f[0]!=="C"?0:+f.replace("C",""))))})}),{maxRow:o,maxCol:n}}function Br(){const{minRows:e=1,minCols:t=1,extraRows:o=0,extraCols:n=0,datasource:r}=this.options,{data:s=[],cols:l=[]}=r,{stagingDraft:c,appliedDraft:f}=this.state,{maxRow:u,maxCol:i}=Yn([c,f]);return{rowsCount:Math.max(u,s.length+o,e,this.data.rowsCount??0),colsCount:Math.max(i,t,l.length,s.reduce((p,d)=>Math.max(p,d.length),0)+n,this.data.colsCount??0)}}function Vr(e=1,t){const{rowsCount:o}=this.getGridSize();let n=o;return typeof e=="number"?n=o+Math.max(e,0):Array.isArray(e)&&(n=o+Math.max(e.length,0),this.stageDraft(e.reduce((r,s,l)=>(Array.isArray(s)&&(s=s.reduce((c,f,u)=>(c[`C${u+1}`]=f,c),{})),r[o+l]=s,r),{}),{skipUpdate:!0})),n>o?(this.data.rowsCount=n,t!=null&&t.skipUpdate||this.update({dirtyType:"options"},()=>{(t==null?void 0:t.select)!==!1&&this.selectCells(`R${o}:R${n-1}`),(t==null?void 0:t.autoScroll)!==!1&&this.scrollTo({row:n-1,col:0})}),!0):!1}function Gr(e=1,t){const{colsCount:o}=this.getGridSize();let n=o;const{showRowIndex:r}=this.options;return typeof e=="number"?n=o+Math.max(e,0):Array.isArray(e)&&(n=o+Math.max(e.length,0),this.stageDraft(e.reduce((s,l,c)=>(l.forEach((f,u)=>{const i=s[u],p=`C${o+c+(r?1:0)}`;i?i[p]=f:s[u]={[p]:f}}),s),{}),{skipUpdate:!0})),n>o?(this.data.colsCount=n,t!=null&&t.skipUpdate||this.update({dirtyType:"options"},()=>{(t==null?void 0:t.select)!==!1&&this.selectCells(`C${o+(r?1:0)}:C${n-1+(r?1:0)}`),(t==null?void 0:t.autoScroll)!==!1&&this.scrollTo({col:n-(r?0:1),row:0})}),!0):!1}function qn(e,t){const o=this.getGridSize(),n=Math.max(0,(e.rowsCount??0)-o.rowsCount),r=Math.max(0,(e.colsCount??0)-o.colsCount);return n&&this.appendRows(n,{skipUpdate:!0,select:!1}),r&&this.appendCols(r,{skipUpdate:!0,select:!1}),n||r?(t!=null&&t.skipUpdate||this.update({dirtyType:"options"}),!0):!1}function Xr(e){return e==null||typeof e=="string"&&!e.length}const Jn={name:"datagrid",plugins:[lr,Cr,Pr,cr,jr,Wr],defaultOptions:{defaultColWidth:80,headerEditable:!0,minRows:20,minCols:10,extraRows:5,extraCols:5,showRowIndex:!0,rowHover:!1,colHover:!1,cellHover:!0,bordered:!0,striped:!1,datagridHotkeys:{},emptyCellValue:"",cellValueSplitter:"	",cellValueGetter:Kr,hotkeys:{},autoExpandGrid:!0,deletedKey:"deleted"},options(e){const{datagridHotkeys:t,datasource:o,hotkeys:n,editable:r,selectable:s,beforeSelectCells:l,showRowIndex:c,colResize:f,onPasteToCell:u,afterStageDraft:i}=e,p={delete:"delete,backspace",selectAll:"ctrl+a,command+a",paste:"ctrl+v,command+v",copy:"ctrl+c,command+c",focus:"enter",cancel:"esc",cut:"ctrl+x,command+x",redo:"ctrl+shift+z,command+shift+z",undo:"ctrl+z,command+z",selectRight:"tab,right",selectLeft:"left",selectDown:"down",selectUp:"up"};return{hotkeys:{...n,...Object.entries({...p,...t}).reduce((a,[h,m])=>{var _;return m&&(a[m===!0?p[h]:m]=(_=Fr[h])==null?void 0:_.bind(this)),a},{})},colResize:f?a=>(typeof f!="function"||f.call(this,a))&&a!=="INDEX":!1,editable:r?(a,h)=>typeof r=="function"&&!r(a,h)?!1:h!=="INDEX":!1,selectable:s?a=>typeof s=="function"&&!s(a)?!1:a.col>=(c?1:0):!1,beforeSelectCells:c?a=>(a.every(h=>h.col===0)&&(a=jn.call(this,`R${Math.min(...a.map(h=>h.row))}:R${Math.max(...a.map(h=>h.row))}`)),l?l.call(this,a):a):l,...Ur(this,o),onPasteToCell:a=>{var m;const h=(m=a.clipboardData)==null?void 0:m.getData("text");typeof h=="string"&&(h.includes("	")||h.includes(`
`))&&this.state.editingCell&&(this.pasteCells(this.state.editingCell,{data:h}),a.preventDefault()),u==null||u.call(this,a)},afterStageDraft:(a,h,m)=>{i==null||i.call(this,a,h,m);const{autoExpandGrid:_}=this.options;if(!_)return;const{maxCol:y,maxRow:b}=Yn(h,{ignoreEmptyCell:!0}),{extraCols:C=1,extraRows:E=1}=this.options;this.expandGridSize({rowsCount:b+(typeof _=="number"?_:E),colsCount:y+(typeof _=="number"?_:C)})}}},methods:{deleteSelections(){const e=[];for(const[t,o]of this.state.selectedMap.entries()){const n=this.getColInfo(t);if(n)for(const r of o){const s=this.getRowInfo(r);s&&e.push({colName:n.name,rowID:s.id})}}return this.deleteCells(e,this.options.emptyCellValue)},deleteRows(e){const t={},{deletedKey:o="deleted"}=this.options;for(const n of e)this.getRowInfo(n)&&(t[n]={[o]:!0});return Object.keys(t).length?(this.stageDraft(t,{skipUpdate:!0}),this.update({dirtyType:"options"}),!0):!1},deleteCols(e){const t={};for(const o of e)this.getColInfo(o)&&(t[o]=!1);return Object.keys(t).length?(this.stageDraft({HEADER:t},{skipUpdate:!0}),this.update({dirtyType:"options"}),!0):!1},copySelections(){const e=this.getSelectedCells();if(!e.length)return!1;let t=Number.MAX_SAFE_INTEGER,o=Number.MAX_SAFE_INTEGER;e.forEach(s=>{t=Math.min(s.col,t),o=Math.min(s.row,o)});const n=[];e.forEach(s=>{const l=this.getCellDraftValue(s.row,s.col);let c=n[s.row-o];c||(c=[],n[s.row-o]=c),c[s.col-t]=l});const r=n.map(s=>s.join("	")).join(`
`);return navigator.clipboard.writeText(r),!0},cutSelections(){return this.copySelections(),this.deleteSelections()},async pasteCells(e,t){var d,a;let o=-1,n=-1;if("colName"in e){const h=this.getColInfo(e.colName),m=this.getRowInfo(e.rowID);if(!h||!m)return!1;o=h.index,n=m.index}else o=e.col,n=e.row;let r=t==null?void 0:t.data;if(r===void 0)try{const h="clipboard-read";if((await navigator.permissions.query({name:h})).state==="denied")return(d=this.options.onReadClipboardFail)==null||d.call(this),!1;r=await navigator.clipboard.readText()}catch{return(a=this.options.onReadClipboardFail)==null||a.call(this),!1}if(!r.length)return!1;const s={},l=[],c=(t==null?void 0:t.expandCells)!==!1;let f=!1,u=0,i=0;const p=r.split(/\r?\n/);return p.forEach((h,m)=>{var C;if(!h.trim().length&&m===p.length-1)return;const _=m+n;let y=(C=this.getRowInfo(_))==null?void 0:C.id;if(y===void 0){if(!c)return;f=!0,y=`${_}`}let b=s[y];b||(b={},s[y]=b),h.split("	").forEach((E,L)=>{var v;const S=L+o;let I=(v=this.getColInfo(S))==null?void 0:v.name;if(I===void 0){if(!c)return;f=!0,I=`C${S}`}b[I]=E,l.push({col:S,row:_}),u=Math.max(u,_),i=Math.max(i,S)})}),this.stageDraft(s,{skipUpdate:!0}),(t==null?void 0:t.select)!==!1&&this.selectCells(l),this.update({dirtyType:f?"options":void 0},()=>{(t==null?void 0:t.autoscroll)!==!1&&this.scrollTo({col:i,row:u})}),!0},pasteToSelection(){const e=this.getSelectedCells();if(!e.length)return Promise.resolve(!1);const t=e[0];return this.pasteCells(t)},getGridSize:Br,appendRows:Vr,appendCols:Gr,expandGridSize:qn},onRender(){return{className:"dtable-datagrid"}},onAddRows(e){const t=new Set;if(Object.entries(this.state.appliedDraft).forEach(([o,{deleted:n}])=>{n&&t.add(o)}),Object.entries(this.state.stagingDraft).forEach(([o,{deleted:n}])=>{n?t.add(o):n===!1&&t.delete(o)}),t.size)return e.filter(o=>!t.has(o.id))}},Yr=V(Jn);D.datagrid=Yr,D.datagridPlugin=Jn,D.expandGridSize=qn,Object.defineProperty(D,Symbol.toStringTag,{value:"Module"})});
