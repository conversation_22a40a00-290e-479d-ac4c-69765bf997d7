#TRAction .icon {padding: 0px 6px; font-size: 14px}

.has-error .form-control {border-color: #ff4268!important;}
.has-error .form-control:focus {box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 6px #f5b2a5!important;}
.has-error .form-control::placeholder {color: #ff4268!important;}

.label.text-red {background: #ff4268 !important; color: #ffffff;}

.dropdown-menu > li {padding: 0 10px;}
.dropdown-menu > li > a > .icon {position: relative; left: -5px;}

#kanbanContainer {padding-bottom: 0; margin-bottom: 0; border: unset;}
#kanbanContainer.fullscreen {overflow: auto;}

.region {border: 0px solid #dcdcdc; background-color: #fff;}
.region + .region {margin-top: 10px;}
.region .region-header {padding: 10px;}
.region .region-header strong {font-size: 14px; color: #3C4353; float: left;}
.region .region-header label {color: #999; background: transparent; border: 1px solid #ddd; margin-left: 10px; margin-right: 10px}
.region .region-header .action {float: right}
.region .region-actions {float: right; position: relative; top: -6px; right: -6px;}
.region .region-header .icon-double-angle-up,.icon-double-angle-down {cursor: pointer;}
.region .kanban-header-sub-cols {margin-left: -2px;}
.region .kanban-header-sub-cols .kanban-header-col > .title {max-width: 100% !important;min-width:200px;}
.region .sort .region-header {cursor: move;}
.region .kanban.kanban-dragging {overflow: hidden;}

.region .kanban {padding: 0px 0px 10px 0px; min-height: unset; overflow:auto; width: 100%}
.region .kanban .form-actions {margin: 0;}
.region .kanban .form-actions .btn {margin-right: 10px; min-width: 50px;}

.region .kanban-board + .kanban-board {margin-top: 20px;}
.region .kanban-board > .kanban-header > .kanban-group-header {padding: 7px 0; width: 20px; text-align: center;}
.region .kanban-board > .kanban-header > .kanban-group-header:hover {cursor: move;}
.region .kanban-board.sort > .kanban-header {padding-left: 0;}

.region .kanban-header {border-bottom: none!important; min-width: max-content; min-width: -moz-max-content;}
.region .kanban-header-col > .title {max-width: 80% !important;}
.region .kanban-header-col > .title {margin: 0}
.region .kanban-header-col > .title > span {display: inline-block; overflow: hidden; padding-right:2px; position: initial; vertical-align: middle;}
.region .kanban-header-col > .title > .text-grey {opacity: .5; font-weight: bold; color: #8b91a2;}
.region .kanban-header-col > .title > .count {opacity: .5; font-weight: bold; color: #8b91a2;}
.region .kanban-header-col > .title > .error {color: #333333; padding-left: 2px; font-size: 10px; padding-top: 1px; padding-right: 2px;}
.region .kanban-header-col > .actions {z-index: 999; top: inherit;}
.kanban-affixed .kanban-header-col > .title > .text {color: #fff!important}
.region .kanban-header-parent-col > .actions {position: absolute; right: 0px; top: 0px !important;}
.region .kanban-header-sub-cols .actions {position: absolute; right: 0px; top: 0px !important;}

.kanban-affixed .kanban-header-col > .title > .count {opacity:1; color: #fff!important;}
.kanban-affixed .kanban-header-col > .title > .text-grey {opacity:1; color: #fff!important;}
.kanban-affixed .kanban-header-col > .actions > .btn > .icon {opacity:1; color: #fff!important;}

.region .kanban-lane {position: relative; border-bottom: none!important; min-height: 200px !important}

.region .kanban-lane > .kanban-lane-name > .text {margin: auto 0;}
.region .kanban-lane > .kanban-lane-name > .actions {position: absolute; top: 0; left: 0; right: 0; opacity: 0;}
.region .kanban-lane > .kanban-lane-name > .actions > a {display: block; width: 20px; height: 20px; line-height: 20px; text-align: center; opacity: .7; color: #fff;}
.region .kanban-lane > .kanban-lane-name > .actions > a:hover {background-color: rgba(0,0,0,.2); opacity: 1;}
.region .kanban-lane > .kanban-lane-name:hover > .actions {opacity: 1;}
.region .kanban-lane.sort > .kanban-lane-name {cursor: move;}

.region .kanban-lane-col {max-height: unset !important; overflow: auto; position: relative;}
.region .kanban-lane-col.has-scrollbar > .kanban-lane-actions {position: absolute; background-color: inherit; bottom: 0; left: 0; right: 0;}

.kanban-item {position: relative}
.kanban-item > .kanban-card > .title {display: block; line-height: 18px;}
.kanban-item > .kanban-card > .title:hover {color: #2272eb}
.kanban-item > .kanban-card > .info {margin-top: 4px; position: relative;}
.kanban-item > .kanban-card > .info > .pri {height: 14px; border-width: 1px; font-size: 12px; line-height: 12px; min-width: 14px; padding: 0 1px;}
.kanban-item > .kanban-card > .info > .estimate{margin-left: 4px; color: #999; background-color: rgb(255, 255, 255); padding: 3px 0px}
.kanban-item > .kanban-card > .info > .time {margin-left: 4px; font-size: 12px; background: rgb(242, 242, 242)}
.kanban-item > .kanban-card > .info > .user {position: absolute; right: 0; top: -2px}
.kanban-item > .kanban-card > .info > .user > .avatar {display: inline-block; width: 24px; height: 24px; line-height: 24px; margin-right: 0px; margin-left: 2px}
.kanban-item > .kanban-card > .info > .user > span {display: inline-block; color: transparent; width: 2px; height: 2px; background-color: #8990a2; position: sticky; border-radius: 50%; top: 3px; margin: 0 7px;}
.kanban-item > .kanban-card > .info > .user > span:before,
.kanban-item > .kanban-card > .info > .user > span:after {content: ''; display: block; position: absolute; width: 2px; height: 2px; background-color: #8990a2; top: 0; border-radius: 50%}
.kanban-item > .kanban-card > .info > .user > span:before {left: -4px;}
.kanban-item > .kanban-card > .info > .user > span:after {right: -4px;}
.kanban-item > .kanban-card > .actions > a {display: block; float: left; width: 20px; height: 20px; line-height: 20px; text-align: center; border-radius: 4px; opacity: .7;}
.kanban-item > .kanban-card > .actions > a:hover {background-color: rgba(0,0,0,.075); opacity: 1;}
.kanban-item:hover > .actions {opacity: 1;}
.kanban-item .has-color .title,
.kanban-item .has-color .actions .icon-more-v,
.kanban-item .has-color .info > .label-pri,
.kanban-item .has-color .info > .estimate,
.kanban-item .has-color .info > .user .label-teamSumCount,
.kanban-item .has-color .info > .label-light {color: #FFFFFF;}
.kanban-item .has-color .info > .label-pri {border-color: #FFFFFF;}
.kanban-item .has-color .progress-box > .progress-number {color: #FFFFFF;}
.kanban-item .item-more {position: absolute; right: 6px; top: 0px;}

.kanban-card {height: auto !important; padding: 8px 14px !important; min-height: 60px;}
.kanban-card > .title, .kanban-card > .productplanTitle, .kanban-card > .releaseTitle, .kanban-card > .buildTitle {word-break: break-all;}
.kanban-card > .info {position: relative; margin-top: 5px}
.kanban-card > .info > .info + .info {margin-left: 8px}
.kanban-card:hover > .actions {opacity: 1;}
.kanban-card > .actions {position: absolute; top: 4px; right: 4px; opacity: 0;}
.kanban-card > .actions > a {display: block; float: left; width: 20px; height: 20px; line-height: 20px; text-align: center; border-radius: 4px; opacity: .7;}
.kanban-card > .actions > a:hover {background-color: rgba(0,0,0,.075); opacity: 1;}
.kanban-card .productplanDesc, .kanban-card .buildDesc {color: #838a9d; overflow: hidden; white-space:nowrap; text-overflow: clip;}

.cardcolor {width:40px; height: 14px; float: left; margin-left: 5px; margin-right: 5px; margin-top: 2px; border-radius: 2px;}
#cardcolormenu {padding:2px 2px; width: 100px;}

.region-header {margin-top: -6px;}
.region-header > .icon{padding: 6px; margin-left:2px;}
.kanban-col[data-type=ADD] {display: none;}
.kanban-col[data-type=EMPTY] {display: none;}
.kanban-col {padding: 0px !important;}
.kanban-col.drag-shadow {background-color: #ededed !important; border-color: #ededed!important; box-shadow: 0 4px 10px 0 rgb(0,0,0, 0.05), 0 4px 20px 0 rgb(0,0,0, .3); min-height: 30px!important;}
.kanban-col.drag-shadow .kanban-header-col {min-height: 30px!important;}
.kanban-header-has-parent .kanban-header-cols > .kanban-col.drag-shadow,
.kanban-col.kanban-header-parent-col.drag-shadow {min-height: 64px!important;}
.kanban-col.drag-shadow > .actions {visibility: hidden!important;}
.kanban-item .title {padding-right: 10px;}
.gray .actions {display:none;}
.kanban-cols-sorting .kanban-item {visibility: hidden!important;}

.kanban-card .header .executionName {display: flex; width: 100%; float: left;}
.kanban-card .header .executionName .title {padding-right: 0px; margin-right: 5px; overflow: hidden; white-space: nowrap;}
.kanban-card .header .executionName > span {flex: none;}
.execInfo, .releaseInfo, .productplanInfo {margin-top: 22px;}
.label-wait {background: #EFEFEF !important; color: #838A9D;}
.label-doing {background: #f8d2d2 !important; color: #e64b4c;}
.label-suspended {background: #fff3d9 !important; color: #ff9800;}
.label-future {background: #EFEFEF !important; color: #838A9D; border-radius: 9px}
.label-done {background: #dfe9d8 !important; color: #429b16;}
.label-closed, .label-terminate {background: #e5e5e5 !important; color: #b8b8b8;}
.label-deleted {background: #ff5d5d !important; color: #fff;}
.label-normal {background: #e1e8d9 !important; color: #9abe76;}
.kanban-item > .kanban-card > .execInfo > .user, .kanban-item > .kanban-card > .releaseInfo > .user, .kanban-item > .kanban-card > .productplanInfo > .user {float:right}
.kanban-item > .kanban-card > .execInfo > .user > .avatar, .kanban-item > .kanban-card > .releaseInfo > .user > .avatar, .kanban-item > .kanban-card > .productplanInfo > .user > .avatar {display: inline-block; width: 24px; height: 24px; line-height: 24px; margin-right: 0px; margin-left: 2px}
.kanban-card .releaseTitle .icon, .kanban-card .title .icon, .kanban-card .productplanTitle .icon, .kanban-card .title .icon, .kanban-card .buildTitle .icon{padding-right: 5px;}
.kanban-card .label-finish {margin-right: 7px; margin-top: -1px; padding: 3px 5px; float: left; background-color:#2a5f29;}
.kanban-card .releaseTitle, .kanban-card .productplanTitle {width: 100%; float: left;}
.kanban-card .productplanDesc {min-width: 100%;}

.progress-box {width:100%; display: flex; flex-direction: row; margin-top: 10px;}
.progress {flex: auto; margin: 5px auto 5px;}
.progress-number {padding-left: 5px;}
.region .region-header .action {float: left;}

@-moz-document url-prefix(){ .region .kanban-lane-items{scrollbar-width:thin; padding:5px 0px 5px 5px !important;} .region .kanban-card .info .user{right: -6px !important;}}
#archivedCards .table-empty-tip {border-style: hidden;}
#archivedColumns .table-empty-tip {border-style: hidden;}

#main > .container {padding: 0px; margin-top: -5px; margin-right: auto; margin-left: auto;}
@media screen and (max-width: 1366px){#main > .container {padding: 0px; margin: -10px 0px 0px 0px;}}
.panel-body {padding: 0px;}
#kanbanContainer {background: #efefef; box-shadow: none;}

.region .kanban-board .kanban-header-col.left {padding-left: 5px;}
.region .kanban-board .kanban-header-col.left > .title, .region .kanban-board .kanban-header-parent-col > .kanban-header-col.left {justify-content: flex-start;}
.region .kanban-board .kanban-header-col > .title > span {max-width: calc(100% - 100px) !important;}
.region .kanban-board .kanban-header-parent-col > .kanban-header-col > .title {max-width: 100% !important;}
.region .kanban-board .kanban-header-sub-cols .kanban-header-col > .title > span {max-width: calc(100% - 165px) !important;}
.region .kanban-board .kanban-header-sub-cols .kanban-header-col.left > .title > span {max-width: calc(100% - 120px) !important;}
.executionName .delayed {margin-right: 10px;}
.region .kanban-board .kanban-header-sub-cols .kanban-header-col > .title > .text {max-width: calc(100% - 172px) !important;}

#kanbanBox {position: relative;}
#regionTabs {background-color: #efefef; z-index: 100; display: flex; align-items: center; gap: 1.5rem; justify-content: space-between;}
#regionTabs .leftBtn.disabled  i, #regionTabs .rightBtn.disabled i{color: #c2c2c2;}
#regionTabs .leftBtn  i, #regionTabs .rightBtn i{vertical-align: 1px;}
#regionTabs .icon-angle-left, #regionTabs .icon-angle-right {font-size: 20px; vertical-align: -webkit-baseline-middle;}
#regionNavTabs {display: inline-flex; overflow: hidden;}
.leftBtn.hidden + #regionNavTabs {padding-left: 10px;}
#regionNavTabs > ul {display: inline-flex; border-bottom: none; margin-bottom: -3px;}
#regionNavTabs > ul > li {padding: 0; text-align: center;}
#regionNavTabs > ul > li > a {min-width: 134px; overflow: hidden; white-space: nowrap; z-index: 0; position: relative; padding: 1.7rem; margin-left: -20px; background: none; outline: 0; cursor: pointer !important;}
#regionNavTabs > ul > li:first-child > a {margin-left: 0;}
#regionNavTabs > ul > li.active > a {z-index: 1;}
#regionNavTabs > ul > li.active > a::before {background-color: #fff;}
#regionNavTabs > ul > li > a:before {content: ""; position: absolute; top: -1rem; left: 0; background: #f8f8f8; height: 100%; width: 100%; z-index: -1; border-top-left-radius: 1rem; border-top-right-radius: 1rem; transform: perspective(0.5rem) rotateX(2deg); transform-origin: bottom;}
#regionNavTabs > ul > li > a > span {vertical-align: bottom; overflow: hidden; max-width: 6em; white-space: nowrap; display: inline-block;}
#regionNavTabs > ul > li.active > a > span {color: #2b80ff!important;}
#regionTabs.affixed {margin-bottom: 5px; position: fixed; top: 0px;}
#regionTabs.affixed + #kanbanContainer .kanban-board.kanban-affixed > .kanban-header {margin-top: 43px;}
#regionTabs.affixed + #kanbanContainer .kanban-board.kanban-affixed > .kanban-header + .kanban-lane {margin-top: 38px;}
#regionTabs.affixed ul {margin-bottom: -10px;}
#regionTabs.affixed ul > li {margin-bottom: 0px !important;}
#regionTabs .region-actions .dropdown-menu {top: 25px;}
#region-tab-actions {display: none;}
#region-tab-actions.active {display: flex;}
.region {display: none;}
.region.active {display: block;}
.region.active.notAll {margin-top: -5px;}
.region.active.notAll .region-header {display: none;}
.region.active.notAll .kanban, .region.active.notAll .kanban .kanban-header {background-color: #efefef;}
.kanban-card a[disabled] {color: #313c52;}
