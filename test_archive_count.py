#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试档案数量功能
"""

import requests
import json

def test_archive_count_api():
    """测试档案数量API"""
    print("🧪 开始测试档案数量功能...")
    
    # 测试后端API
    api_url = "http://localhost:8000/api/v1/project/all"
    
    try:
        print(f"📡 请求API: {api_url}")
        response = requests.get(api_url)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API请求成功")
            print(f"📊 返回状态: {data.get('success', False)}")
            print(f"📝 消息: {data.get('message', '')}")
            
            projects = data.get('data', {}).get('projects', [])
            print(f"📋 项目数量: {len(projects)}")
            
            # 查找TEST_001项目
            test_project = None
            for project in projects:
                if project.get('project_code') == 'TEST_001':
                    test_project = project
                    break
            
            if test_project:
                print("\n🎯 找到测试项目 TEST_001:")
                print(f"   项目名称: {test_project.get('project_name', 'N/A')}")
                print(f"   当前阶段: {test_project.get('current_progress', 'N/A')}")
                
                archive_count = test_project.get('archive_count')
                if archive_count:
                    print("📁 档案数量信息:")
                    print(f"   总文件数: {archive_count.get('total_files', 0)}")
                    print(f"   Markdown文件数: {archive_count.get('markdown_files', 0)}")
                    print(f"   其他文件数: {archive_count.get('other_files', 0)}")
                    print(f"   档案摘要: {archive_count.get('archive_summary', 'N/A')}")
                    
                    if archive_count.get('markdown_files', 0) > 0:
                        print("✅ Markdown文件统计正常")
                    else:
                        print("⚠️  未检测到Markdown文件")
                else:
                    print("❌ 未找到档案数量信息")
            else:
                print("⚠️  未找到TEST_001项目")
                print("📋 现有项目列表:")
                for project in projects[:5]:  # 只显示前5个项目
                    print(f"   - {project.get('project_code', 'N/A')}: {project.get('project_name', 'N/A')}")
                
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务，请确保后端服务已启动")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")

def test_archive_count_function():
    """直接测试档案统计函数"""
    print("\n🔧 直接测试档案统计函数...")
    
    try:
        import sys
        import os
        sys.path.append('pmo-backend')
        
        from pmo_backend.app.api.endpoints.project_archive import get_project_archive_count
        
        # 测试TEST_001项目
        result = get_project_archive_count('TEST_001')
        print("📁 TEST_001项目档案统计结果:")
        print(f"   总文件数: {result.get('total_files', 0)}")
        print(f"   Markdown文件数: {result.get('markdown_files', 0)}")
        print(f"   其他文件数: {result.get('other_files', 0)}")
        print(f"   档案摘要: {result.get('archive_summary', 'N/A')}")
        
        if result.get('markdown_files', 0) > 0:
            print("✅ 档案统计函数工作正常")
        else:
            print("⚠️  档案统计函数未检测到Markdown文件")
            
    except ImportError as e:
        print(f"⚠️  无法导入档案统计函数: {str(e)}")
    except Exception as e:
        print(f"❌ 测试档案统计函数时发生错误: {str(e)}")

if __name__ == "__main__":
    print("🚀 档案数量功能测试开始")
    print("=" * 50)
    
    # 测试API
    test_archive_count_api()
    
    # 测试函数
    test_archive_count_function()
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")
