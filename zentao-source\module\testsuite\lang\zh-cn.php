<?php
/**
 * The testsuite module zh-cn file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     testsuite
 * @version     $Id: zh-cn.php 4490 2013-02-27 03:27:05Z <EMAIL> $
 * @link        https://www.zentao.net
 */
$lang->testsuite->create           = "建套件";
$lang->testsuite->delete           = "删除套件";
$lang->testsuite->view             = "概况";
$lang->testsuite->edit             = "编辑套件";
$lang->testsuite->browse           = "套件列表";
$lang->testsuite->linkCase         = "关联用例";
$lang->testsuite->linkVersion      = "版本";
$lang->testsuite->unlinkCase       = "移除";
$lang->testsuite->unlinkCaseAction = "移除用例";
$lang->testsuite->batchUnlinkCases = "批量移除用例";
$lang->testsuite->deleted          = '已删除';
$lang->testsuite->successSaved     = '保存成功';

$lang->testsuite->id             = '编号';
$lang->testsuite->pri            = '优先级';
$lang->testsuite->common         = '套件';
$lang->testsuite->project        = '所属' . $lang->projectCommon;
$lang->testsuite->product        = '所属' . $lang->productCommon;
$lang->testsuite->name           = '套件名称';
$lang->testsuite->type           = '类型';
$lang->testsuite->desc           = '描述';
$lang->testsuite->mailto         = '抄送给';
$lang->testsuite->author         = '访问权限';
$lang->testsuite->addedBy        = '创建者';
$lang->testsuite->addedDate      = '创建日期';
$lang->testsuite->addedTime      = '创建时间';
$lang->testsuite->lastEditedBy   = '最后编辑人';
$lang->testsuite->lastEditedDate = '最后编辑时间';

$lang->testsuite->legendDesc      = '描述';
$lang->testsuite->legendBasicInfo = '基本信息';

$lang->testsuite->unlinkedCases = '未关联';

$lang->testsuite->confirmDelete     = '您确认要删除该套件吗？';
$lang->testsuite->confirmUnlinkCase = '您确认要移除该用例吗？';
$lang->testsuite->noticeNone        = '您还没有创建套件';
$lang->testsuite->noModule          = '<div>您现在还没有模块信息</div><div>请维护用例库模块</div>';
$lang->testsuite->noTestsuite       = '暂时没有套件。';
$lang->testsuite->summary           = "本页共 <strong>%d</strong> 个套件，公开 <strong>%d</strong> 个，私有 <strong>%d</strong> 个。";

$lang->testsuite->lblCases      = '用例列表';
$lang->testsuite->lblUnlinkCase = '移除用例';

$lang->testsuite->authorList['private'] = '私有';
$lang->testsuite->authorList['public']  = '公开';

$lang->testsuite->featureBar['browse']['all'] = '全部';
