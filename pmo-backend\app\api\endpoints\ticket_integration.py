#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工单系统集成API端点
提供工单数据查询、分析和集成功能
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Dict, Any, Optional
import mysql.connector
from datetime import datetime, timedelta
import json
import time
from decimal import Decimal

from app.core.security import get_current_user
from app.utils.response_utils import success_response, error_response

router = APIRouter()

class TicketSystemConnector:
    """工单系统数据库连接器"""
    
    def __init__(self):
        self.config = {
            'host': '**********',
            'user': 'qyuser',
            'password': 'C~w9d4kaWS',
            'database': 'ticket',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
    
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = mysql.connector.connect(**self.config)
            return True
        except Exception as e:
            print(f"工单系统数据库连接失败: {e}")
            return False
    
    def execute_query(self, sql: str, params: tuple = None) -> List[Dict]:
        """执行查询SQL"""
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return []
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(sql, params or ())
            result = cursor.fetchall()
            cursor.close()
            
            # 处理Decimal类型
            for row in result:
                for key, value in row.items():
                    if isinstance(value, Decimal):
                        row[key] = float(value)
            
            return result
        except Exception as e:
            print(f"SQL执行失败: {e}")
            return []
    
    def close(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()

@router.get("/projects", summary="获取工单系统项目列表")
async def get_ticket_projects(
    limit: int = Query(10, description="返回数量限制"),
    current_user: dict = Depends(get_current_user)
):
    """获取工单系统中的项目列表"""
    connector = TicketSystemConnector()
    
    try:
        # 获取项目基本信息，包含用户和部门名称
        projects_sql = """
        SELECT
            p.feelec_project_id,
            p.feelec_name,
            p.feelec_manager_id,
            p.feelec_department_id,
            p.feelec_creator_id,
            p.feelec_content,
            p.create_time,
            p.complete_time,
            p.feelec_archive,
            u1.feelec_name as manager_name,
            u2.feelec_name as creator_name,
            d.feelec_name as department_name
        FROM feelec_project p
        LEFT JOIN feelec_user u1 ON p.feelec_manager_id = u1.feelec_user_id AND u1.delete_time = 0
        LEFT JOIN feelec_user u2 ON p.feelec_creator_id = u2.feelec_user_id AND u2.delete_time = 0
        LEFT JOIN feelec_member_department d ON p.feelec_department_id = d.feelec_department_id AND d.delete_time = 0
        WHERE p.feelec_delete = 20
        ORDER BY p.create_time DESC
        LIMIT %s
        """
        
        projects = connector.execute_query(projects_sql, (limit,))
        
        # 获取每个项目的工单统计
        for project in projects:
            project_id = project['feelec_project_id']
            
            # 工单统计
            tickets_sql = """
            SELECT 
                COUNT(*) as total_tickets,
                SUM(CASE WHEN complete_time > 0 THEN 1 ELSE 0 END) as completed_tickets,
                SUM(CASE WHEN feelec_priority = 1 THEN 1 ELSE 0 END) as urgent_tickets,
                SUM(CASE WHEN deadlines > 0 AND deadlines < UNIX_TIMESTAMP() AND complete_time = 0 THEN 1 ELSE 0 END) as overdue_tickets,
                AVG(CASE WHEN complete_time > 0 AND first_process_time > 0 
                    THEN complete_time - first_process_time ELSE NULL END) as avg_completion_time
            FROM feelec_ticket 
            WHERE feelec_project_id = %s AND feelec_delete = 20
            """
            
            ticket_stats = connector.execute_query(tickets_sql, (project_id,))
            if ticket_stats:
                project.update(ticket_stats[0])
                
                # 计算完成率
                if project['total_tickets'] > 0:
                    project['completion_rate'] = round((project['completed_tickets'] / project['total_tickets']) * 100, 2)
                else:
                    project['completion_rate'] = 0
            
            # 格式化时间
            project['create_time_formatted'] = datetime.fromtimestamp(project['create_time']).strftime('%Y-%m-%d %H:%M:%S')
            if project['complete_time'] and project['complete_time'] > 0:
                project['complete_time_formatted'] = datetime.fromtimestamp(project['complete_time']).strftime('%Y-%m-%d %H:%M:%S')
                project['is_completed'] = True
            else:
                project['complete_time_formatted'] = '进行中'
                project['is_completed'] = False
        
        return success_response(data={
            'projects': projects,
            'total_count': len(projects)
        })
        
    except Exception as e:
        return error_response(message=f"获取项目列表失败: {str(e)}")
    finally:
        connector.close()

@router.get("/tickets/by-status", summary="按状态获取工单列表")
async def get_tickets_by_status(
    status: str = Query(..., description="工单状态：all(全部), completed(已完成), urgent(紧急)"),
    limit: int = Query(50, description="返回数量限制"),
    current_user: dict = Depends(get_current_user)
):
    """按状态获取工单列表 - 完全按照项目工单列表实现"""
    connector = TicketSystemConnector()

    try:
        # 构建查询条件
        where_conditions = ["t.feelec_delete = 20"]
        params = []

        if status == "completed":
            where_conditions.append("s.feelec_name = '已完成'")
        elif status == "urgent":
            where_conditions.append("t.feelec_priority = 1")
        # status == "all" 时不添加额外条件

        # 完全按照项目工单列表的SQL查询 - 包含工单表的所有字段
        tickets_sql = f"""
        SELECT
            t.*,
            s.feelec_name as status_name,
            u1.feelec_name as publisher_name,
            u2.feelec_name as processor_name,
            d.feelec_name as department_name,
            c.feelec_name as company_name,
            tt.feelec_name as template_name,
            p.feelec_name as project_name
        FROM feelec_ticket t
        LEFT JOIN feelec_ticket_status s ON t.feelec_status_id = s.feelec_status_id AND s.delete_time = 0
        LEFT JOIN feelec_user u1 ON t.feelec_publisher_id = u1.feelec_user_id AND u1.delete_time = 0
        LEFT JOIN feelec_user u2 ON t.feelec_processor_id = u2.feelec_user_id AND u2.delete_time = 0
        LEFT JOIN feelec_member_department d ON t.feelec_department_id = d.feelec_department_id AND d.delete_time = 0
        LEFT JOIN feelec_company c ON t.feelec_company_id = c.feelec_company_id AND c.delete_time = 0
        LEFT JOIN feelec_ticket_template tt ON t.feelec_template_id = tt.feelec_template_id
        LEFT JOIN feelec_project p ON t.feelec_project_id = p.feelec_project_id AND p.feelec_delete = 20
        WHERE {' AND '.join(where_conditions)}
        ORDER BY t.create_time DESC
        LIMIT %s
        """

        params.append(limit)
        tickets = connector.execute_query(tickets_sql, tuple(params))

        # 完全按照项目工单列表的格式化逻辑
        for ticket in tickets:
            # 优先级映射
            priority_map = {1: '紧急', 2: '高', 3: '一般', 4: '低'}
            ticket['priority_text'] = priority_map.get(ticket['feelec_priority'], '未知')
            ticket['priority_color'] = {1: 'danger', 2: 'warning', 3: 'primary', 4: 'info'}.get(ticket['feelec_priority'], 'info')

            # 来源映射
            source_map = {1: '邮件', 2: '电话', 3: '网页', 4: '系统', 5: '其他'}
            ticket['source_text'] = source_map.get(ticket['feelec_source'], '未知')

            # 时间格式化
            ticket['create_time_formatted'] = datetime.fromtimestamp(ticket['create_time']).strftime('%Y-%m-%d %H:%M:%S')

            # 首次分配时间
            if ticket['first_assign_time'] and ticket['first_assign_time'] > 0:
                ticket['first_assign_time_formatted'] = datetime.fromtimestamp(ticket['first_assign_time']).strftime('%Y-%m-%d %H:%M:%S')
            else:
                ticket['first_assign_time_formatted'] = '未分配'

            # 首次处理时间
            if ticket['first_process_time'] and ticket['first_process_time'] > 0:
                ticket['first_process_time_formatted'] = datetime.fromtimestamp(ticket['first_process_time']).strftime('%Y-%m-%d %H:%M:%S')
            else:
                ticket['first_process_time_formatted'] = '未处理'

            if ticket['complete_time'] and ticket['complete_time'] > 0:
                ticket['complete_time_formatted'] = datetime.fromtimestamp(ticket['complete_time']).strftime('%Y-%m-%d %H:%M:%S')
                ticket['is_completed'] = True
            else:
                ticket['complete_time_formatted'] = '未完成'
                ticket['is_completed'] = False

            # 截止时间
            if ticket['deadlines'] and ticket['deadlines'] > 0:
                ticket['deadline_formatted'] = datetime.fromtimestamp(ticket['deadlines']).strftime('%Y-%m-%d %H:%M:%S')
                # 检查是否逾期
                if ticket['deadlines'] < datetime.now().timestamp() and not ticket['is_completed']:
                    ticket['is_overdue'] = True
                    ticket['overdue_days'] = int((datetime.now().timestamp() - ticket['deadlines']) / 86400)
                else:
                    ticket['is_overdue'] = False
            else:
                ticket['deadline_formatted'] = '无期限'
                ticket['is_overdue'] = False

            # 计算处理时长
            if ticket['first_process_time'] and ticket['complete_time'] and ticket['complete_time'] > 0:
                process_duration = ticket['complete_time'] - ticket['first_process_time']
                duration_days = process_duration // 86400
                duration_hours = (process_duration % 86400) // 3600
                if duration_days > 0:
                    ticket['process_duration_text'] = f"{int(duration_days)}天{int(duration_hours)}小时"
                elif duration_hours > 0:
                    ticket['process_duration_text'] = f"{int(duration_hours)}小时"
                else:
                    ticket['process_duration_text'] = "1小时内"
                ticket['process_duration_hours'] = round(process_duration / 3600, 2)
            else:
                ticket['process_duration_text'] = '处理中' if not ticket['is_completed'] else '未知'
                ticket['process_duration_hours'] = None

            # 添加工单内容预览（使用工单标题作为内容预览）
            ticket['feelec_content'] = f"工单：{ticket['feelec_title']}"

            # 设置优先级颜色
            ticket['priority_color'] = {1: 'danger', 2: 'warning', 3: 'primary', 4: 'info'}.get(ticket['feelec_priority'], 'info')

        return success_response(data={
            'tickets': tickets,
            'total_count': len(tickets),
            'status_filter': status
        })

    except Exception as e:
        return error_response(message=f"获取工单列表失败: {str(e)}")
    finally:
        connector.close()

@router.get("/users", summary="获取用户列表")
async def get_users_list(
    limit: int = Query(50, description="返回数量限制"),
    current_user: dict = Depends(get_current_user)
):
    """获取用户列表及其工单统计"""
    connector = TicketSystemConnector()

    try:
        users_sql = """
        SELECT
            u.feelec_user_id,
            u.feelec_name,
            u.feelec_mobile,
            u.feelec_email,
            u.feelec_company_id,
            c.feelec_name as company_name,
            COUNT(t.feelec_ticket_id) as total_tickets,
            SUM(CASE WHEN ts.feelec_name = '已完成' THEN 1 ELSE 0 END) as completed_tickets,
            SUM(CASE WHEN t.feelec_priority = 1 THEN 1 ELSE 0 END) as urgent_tickets
        FROM feelec_user u
        LEFT JOIN feelec_company c ON u.feelec_company_id = c.feelec_company_id AND c.delete_time = 0
        LEFT JOIN feelec_ticket t ON u.feelec_user_id = t.feelec_processor_id AND t.feelec_delete = 20
        LEFT JOIN feelec_ticket_status ts ON t.feelec_status_id = ts.feelec_status_id AND ts.delete_time = 0
        WHERE u.delete_time = 0
        GROUP BY u.feelec_user_id
        HAVING total_tickets > 0
        ORDER BY total_tickets DESC
        LIMIT %s
        """

        users = connector.execute_query(users_sql, (limit,))

        # 计算完成率
        for user in users:
            if user['total_tickets'] > 0:
                user['completion_rate'] = round((user['completed_tickets'] / user['total_tickets']) * 100, 2)
            else:
                user['completion_rate'] = 0

        return success_response(data={
            'users': users,
            'total_count': len(users)
        })

    except Exception as e:
        return error_response(message=f"获取用户列表失败: {str(e)}")
    finally:
        connector.close()

@router.get("/companies", summary="获取主体列表")
async def get_companies_list(
    limit: int = Query(50, description="返回数量限制"),
    current_user: dict = Depends(get_current_user)
):
    """获取主体列表及其工单统计"""
    connector = TicketSystemConnector()

    try:
        companies_sql = """
        SELECT
            c.feelec_company_id,
            c.feelec_name,
            c.feelec_contact,
            c.feelec_phone,
            c.feelec_address,
            COUNT(t.feelec_ticket_id) as total_tickets,
            SUM(CASE WHEN ts.feelec_name = '已完成' THEN 1 ELSE 0 END) as completed_tickets,
            SUM(CASE WHEN t.feelec_priority = 1 THEN 1 ELSE 0 END) as urgent_tickets
        FROM feelec_company c
        LEFT JOIN feelec_ticket t ON c.feelec_company_id = t.feelec_company_id AND t.feelec_delete = 20
        LEFT JOIN feelec_ticket_status ts ON t.feelec_status_id = ts.feelec_status_id AND ts.delete_time = 0
        WHERE c.delete_time = 0
        GROUP BY c.feelec_company_id
        HAVING total_tickets > 0
        ORDER BY total_tickets DESC
        LIMIT %s
        """

        companies = connector.execute_query(companies_sql, (limit,))

        # 计算完成率
        for company in companies:
            if company['total_tickets'] > 0:
                company['completion_rate'] = round((company['completed_tickets'] / company['total_tickets']) * 100, 2)
            else:
                company['completion_rate'] = 0

        return success_response(data={
            'companies': companies,
            'total_count': len(companies)
        })

    except Exception as e:
        return error_response(message=f"获取主体列表失败: {str(e)}")
    finally:
        connector.close()

@router.get("/users/{user_id}/tickets", summary="获取用户工单列表")
async def get_user_tickets(
    user_id: str,
    current_user: dict = Depends(get_current_user)
):
    """获取指定用户的工单列表"""
    connector = TicketSystemConnector()

    try:
        tickets_sql = """
        SELECT
            t.feelec_ticket_id,
            t.feelec_ticket_no,
            t.feelec_title,
            t.feelec_priority,
            t.create_time,
            t.complete_time,
            ts.feelec_name as status_name,
            u.feelec_name as processor_name,
            CASE t.feelec_priority
                WHEN 1 THEN '紧急'
                WHEN 2 THEN '高'
                WHEN 3 THEN '中'
                WHEN 4 THEN '低'
                ELSE '未知'
            END as priority_text
        FROM feelec_ticket t
        LEFT JOIN feelec_ticket_status ts ON t.feelec_status_id = ts.feelec_status_id AND ts.delete_time = 0
        LEFT JOIN feelec_user u ON t.feelec_processor_id = u.feelec_user_id AND u.delete_time = 0
        WHERE t.feelec_processor_id = %s AND t.feelec_delete = 20
        ORDER BY t.create_time DESC
        """

        tickets = connector.execute_query(tickets_sql, (user_id,))

        # 格式化数据
        for ticket in tickets:
            ticket['create_time_formatted'] = datetime.fromtimestamp(ticket['create_time']).strftime('%Y-%m-%d %H:%M:%S')
            if ticket['complete_time'] and ticket['complete_time'] > 0:
                ticket['complete_time_formatted'] = datetime.fromtimestamp(ticket['complete_time']).strftime('%Y-%m-%d %H:%M:%S')
                ticket['is_completed'] = True
            else:
                ticket['complete_time_formatted'] = '进行中'
                ticket['is_completed'] = False

            # 设置优先级颜色
            ticket['priority_color'] = {1: 'danger', 2: 'warning', 3: 'primary', 4: 'info'}.get(ticket['feelec_priority'], 'info')

        return success_response(data={
            'tickets': tickets,
            'total_count': len(tickets),
            'user_id': user_id
        })

    except Exception as e:
        return error_response(message=f"获取用户工单失败: {str(e)}")
    finally:
        connector.close()

@router.get("/companies/{company_id}/tickets", summary="获取主体工单列表")
async def get_company_tickets(
    company_id: str,
    current_user: dict = Depends(get_current_user)
):
    """获取指定主体的工单列表"""
    connector = TicketSystemConnector()

    try:
        tickets_sql = """
        SELECT
            t.feelec_ticket_id,
            t.feelec_ticket_no,
            t.feelec_title,
            t.feelec_priority,
            t.create_time,
            t.complete_time,
            ts.feelec_name as status_name,
            u1.feelec_name as publisher_name,
            u2.feelec_name as processor_name,
            c.feelec_name as company_name,
            CASE t.feelec_priority
                WHEN 1 THEN '紧急'
                WHEN 2 THEN '高'
                WHEN 3 THEN '中'
                WHEN 4 THEN '低'
                ELSE '未知'
            END as priority_text
        FROM feelec_ticket t
        LEFT JOIN feelec_ticket_status ts ON t.feelec_status_id = ts.feelec_status_id AND ts.delete_time = 0
        LEFT JOIN feelec_user u1 ON t.feelec_publisher_id = u1.feelec_user_id AND u1.delete_time = 0
        LEFT JOIN feelec_user u2 ON t.feelec_processor_id = u2.feelec_user_id AND u2.delete_time = 0
        LEFT JOIN feelec_company c ON t.feelec_company_id = c.feelec_company_id AND c.delete_time = 0
        WHERE t.feelec_company_id = %s AND t.feelec_delete = 20
        ORDER BY t.create_time DESC
        """

        tickets = connector.execute_query(tickets_sql, (company_id,))

        # 格式化数据
        for ticket in tickets:
            ticket['create_time_formatted'] = datetime.fromtimestamp(ticket['create_time']).strftime('%Y-%m-%d %H:%M:%S')
            if ticket['complete_time'] and ticket['complete_time'] > 0:
                ticket['complete_time_formatted'] = datetime.fromtimestamp(ticket['complete_time']).strftime('%Y-%m-%d %H:%M:%S')
                ticket['is_completed'] = True
            else:
                ticket['complete_time_formatted'] = '进行中'
                ticket['is_completed'] = False

            # 设置优先级颜色
            ticket['priority_color'] = {1: 'danger', 2: 'warning', 3: 'primary', 4: 'info'}.get(ticket['feelec_priority'], 'info')

        return success_response(data={
            'tickets': tickets,
            'total_count': len(tickets),
            'company_id': company_id
        })

    except Exception as e:
        return error_response(message=f"获取主体工单失败: {str(e)}")
    finally:
        connector.close()

@router.get("/projects/{project_id}/tickets", summary="获取项目工单详情")
async def get_project_tickets(
    project_id: str,
    current_user: dict = Depends(get_current_user)
):
    """获取指定项目的工单详情"""
    connector = TicketSystemConnector()
    
    try:
        # 获取项目基本信息
        project_sql = """
        SELECT feelec_project_id, feelec_name, feelec_content, feelec_manager_id
        FROM feelec_project 
        WHERE feelec_project_id = %s AND feelec_delete = 20
        """
        
        project_info = connector.execute_query(project_sql, (project_id,))
        if not project_info:
            return error_response(message="项目不存在", code=404)
        
        # 获取项目工单列表 - 包含工单表的所有字段
        tickets_sql = """
        SELECT
            t.*,
            s.feelec_name as status_name,
            u1.feelec_name as publisher_name,
            u2.feelec_name as processor_name,
            d.feelec_name as department_name,
            c.feelec_name as company_name,
            tt.feelec_name as template_name,
            p.feelec_name as project_name
        FROM feelec_ticket t
        LEFT JOIN feelec_ticket_status s ON t.feelec_status_id = s.feelec_status_id AND s.delete_time = 0
        LEFT JOIN feelec_user u1 ON t.feelec_publisher_id = u1.feelec_user_id AND u1.delete_time = 0
        LEFT JOIN feelec_user u2 ON t.feelec_processor_id = u2.feelec_user_id AND u2.delete_time = 0
        LEFT JOIN feelec_member_department d ON t.feelec_department_id = d.feelec_department_id AND d.delete_time = 0
        LEFT JOIN feelec_company c ON t.feelec_company_id = c.feelec_company_id AND c.delete_time = 0
        LEFT JOIN feelec_ticket_template tt ON t.feelec_template_id = tt.feelec_template_id
        LEFT JOIN feelec_project p ON t.feelec_project_id = p.feelec_project_id AND p.feelec_delete = 20
        WHERE t.feelec_project_id = %s AND t.feelec_delete = 20
        ORDER BY t.create_time DESC
        """
        
        tickets = connector.execute_query(tickets_sql, (project_id,))
        
        # 格式化工单数据
        for ticket in tickets:
            # 优先级映射
            priority_map = {1: '紧急', 2: '高', 3: '一般', 4: '低'}
            ticket['priority_text'] = priority_map.get(ticket['feelec_priority'], '未知')
            ticket['priority_color'] = {1: 'danger', 2: 'warning', 3: 'primary', 4: 'info'}.get(ticket['feelec_priority'], 'info')

            # 来源映射
            source_map = {1: '邮件', 2: '电话', 3: '网页', 4: '系统', 5: '其他'}
            ticket['source_text'] = source_map.get(ticket['feelec_source'], '未知')

            # 时间格式化
            ticket['create_time_formatted'] = datetime.fromtimestamp(ticket['create_time']).strftime('%Y-%m-%d %H:%M:%S')

            # 首次分配时间
            if ticket['first_assign_time'] and ticket['first_assign_time'] > 0:
                ticket['first_assign_time_formatted'] = datetime.fromtimestamp(ticket['first_assign_time']).strftime('%Y-%m-%d %H:%M:%S')
            else:
                ticket['first_assign_time_formatted'] = '未分配'

            # 首次处理时间
            if ticket['first_process_time'] and ticket['first_process_time'] > 0:
                ticket['first_process_time_formatted'] = datetime.fromtimestamp(ticket['first_process_time']).strftime('%Y-%m-%d %H:%M:%S')
            else:
                ticket['first_process_time_formatted'] = '未处理'
            
            if ticket['complete_time'] and ticket['complete_time'] > 0:
                ticket['complete_time_formatted'] = datetime.fromtimestamp(ticket['complete_time']).strftime('%Y-%m-%d %H:%M:%S')
                ticket['is_completed'] = True
            else:
                ticket['complete_time_formatted'] = '未完成'
                ticket['is_completed'] = False
            
            # 截止时间
            if ticket['deadlines'] and ticket['deadlines'] > 0:
                ticket['deadline_formatted'] = datetime.fromtimestamp(ticket['deadlines']).strftime('%Y-%m-%d %H:%M:%S')
                # 检查是否逾期
                if ticket['deadlines'] < datetime.now().timestamp() and not ticket['is_completed']:
                    ticket['is_overdue'] = True
                    ticket['overdue_days'] = int((datetime.now().timestamp() - ticket['deadlines']) / 86400)
                else:
                    ticket['is_overdue'] = False
            else:
                ticket['deadline_formatted'] = '无期限'
                ticket['is_overdue'] = False
            
            # 计算处理时长
            if ticket['first_process_time'] and ticket['complete_time'] and ticket['complete_time'] > 0:
                process_duration = ticket['complete_time'] - ticket['first_process_time']
                duration_days = process_duration // 86400
                duration_hours = (process_duration % 86400) // 3600
                if duration_days > 0:
                    ticket['process_duration_text'] = f"{int(duration_days)}天{int(duration_hours)}小时"
                elif duration_hours > 0:
                    ticket['process_duration_text'] = f"{int(duration_hours)}小时"
                else:
                    ticket['process_duration_text'] = "1小时内"
                ticket['process_duration_hours'] = round(process_duration / 3600, 2)
            else:
                ticket['process_duration_text'] = '处理中' if not ticket['is_completed'] else '未知'
                ticket['process_duration_hours'] = None

            # 添加工单内容预览（使用工单标题作为内容预览）
            ticket['feelec_content'] = f"工单：{ticket['feelec_title']}"
        
        return success_response(data={
            'project': project_info[0],
            'tickets': tickets,
            'total_tickets': len(tickets)
        })
        
    except Exception as e:
        return error_response(message=f"获取项目工单失败: {str(e)}")
    finally:
        connector.close()

@router.get("/workload-analysis", summary="团队工作负荷分析")
async def get_workload_analysis(
    department_id: Optional[str] = Query(None, description="部门ID"),
    days: int = Query(30, description="分析天数"),
    current_user: dict = Depends(get_current_user)
):
    """获取团队工作负荷分析"""
    connector = TicketSystemConnector()
    
    try:
        # 计算开始时间
        start_timestamp = int((datetime.now() - timedelta(days=days)).timestamp())
        
        # 构建SQL查询
        base_sql = """
        SELECT
            t.feelec_processor_id,
            t.feelec_department_id,
            u.feelec_name as processor_name,
            d.feelec_name as department_name,
            COUNT(*) as total_tickets,
            SUM(CASE WHEN t.complete_time > 0 THEN 1 ELSE 0 END) as completed_tickets,
            AVG(CASE WHEN t.complete_time > 0 AND t.first_process_time > 0
                THEN t.complete_time - t.first_process_time ELSE NULL END) as avg_process_time,
            COUNT(CASE WHEN t.feelec_priority = 1 THEN 1 END) as urgent_tickets,
            COUNT(CASE WHEN t.deadlines > 0 AND t.deadlines < UNIX_TIMESTAMP() AND t.complete_time = 0 THEN 1 END) as overdue_tickets
        FROM feelec_ticket t
        LEFT JOIN feelec_user u ON t.feelec_processor_id = u.feelec_user_id AND u.delete_time = 0
        LEFT JOIN feelec_member_department d ON t.feelec_department_id = d.feelec_department_id AND d.delete_time = 0
        WHERE t.feelec_delete = 20
        AND t.create_time >= %s
        """
        
        params = [start_timestamp]
        if department_id:
            base_sql += " AND t.feelec_department_id = %s"
            params.append(department_id)
        
        base_sql += """
        GROUP BY t.feelec_processor_id, t.feelec_department_id, u.feelec_name, d.feelec_name
        HAVING total_tickets > 0
        ORDER BY total_tickets DESC
        """
        
        workload_data = connector.execute_query(base_sql, tuple(params))
        
        # 计算统计数据
        for item in workload_data:
            if item['total_tickets'] > 0:
                item['completion_rate'] = round((item['completed_tickets'] / item['total_tickets']) * 100, 2)
            else:
                item['completion_rate'] = 0
            
            if item['avg_process_time']:
                item['avg_process_hours'] = round(item['avg_process_time'] / 3600, 2)
            else:
                item['avg_process_hours'] = 0
            
            # 工作负荷等级
            if item['total_tickets'] >= 30:
                item['workload_level'] = 'high'
                item['workload_text'] = '高负荷'
            elif item['total_tickets'] >= 15:
                item['workload_level'] = 'medium'
                item['workload_text'] = '中等负荷'
            else:
                item['workload_level'] = 'low'
                item['workload_text'] = '低负荷'
        
        # 计算总体统计
        total_stats = {
            'total_processors': len(workload_data),
            'total_tickets': sum(item['total_tickets'] for item in workload_data),
            'total_completed': sum(item['completed_tickets'] for item in workload_data),
            'high_workload_count': len([item for item in workload_data if item['workload_level'] == 'high']),
            'analysis_period': f'最近{days}天'
        }
        
        if total_stats['total_tickets'] > 0:
            total_stats['overall_completion_rate'] = round((total_stats['total_completed'] / total_stats['total_tickets']) * 100, 2)
        else:
            total_stats['overall_completion_rate'] = 0
        
        return success_response(data={
            'workload_data': workload_data,
            'total_stats': total_stats
        })
        
    except Exception as e:
        return error_response(message=f"获取工作负荷分析失败: {str(e)}")
    finally:
        connector.close()

@router.get("/dashboard-stats", summary="工单系统仪表盘统计")
async def get_dashboard_stats(
    current_user: dict = Depends(get_current_user)
):
    """获取工单系统仪表盘统计数据"""
    connector = TicketSystemConnector()
    
    try:
        # 项目统计
        project_stats_sql = """
        SELECT 
            COUNT(*) as total_projects,
            SUM(CASE WHEN complete_time = 0 THEN 1 ELSE 0 END) as active_projects,
            SUM(CASE WHEN complete_time > 0 THEN 1 ELSE 0 END) as completed_projects
        FROM feelec_project 
        WHERE feelec_delete = 20
        """
        
        project_stats = connector.execute_query(project_stats_sql)
        
        # 工单统计
        ticket_stats_sql = """
        SELECT 
            COUNT(*) as total_tickets,
            SUM(CASE WHEN complete_time > 0 THEN 1 ELSE 0 END) as completed_tickets,
            SUM(CASE WHEN feelec_priority = 1 THEN 1 ELSE 0 END) as urgent_tickets,
            SUM(CASE WHEN deadlines > 0 AND deadlines < UNIX_TIMESTAMP() AND complete_time = 0 THEN 1 ELSE 0 END) as overdue_tickets
        FROM feelec_ticket 
        WHERE feelec_delete = 20
        """
        
        ticket_stats = connector.execute_query(ticket_stats_sql)
        
        # 最近7天工单趋势
        trend_sql = """
        SELECT 
            DATE(FROM_UNIXTIME(create_time)) as date,
            COUNT(*) as created_count,
            SUM(CASE WHEN complete_time > 0 THEN 1 ELSE 0 END) as completed_count
        FROM feelec_ticket 
        WHERE feelec_delete = 20 
        AND create_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY))
        GROUP BY DATE(FROM_UNIXTIME(create_time))
        ORDER BY date
        """
        
        trend_data = connector.execute_query(trend_sql)
        
        # 状态分布
        status_sql = """
        SELECT 
            s.feelec_name as status_name,
            COUNT(t.feelec_ticket_id) as count
        FROM feelec_ticket_status s
        LEFT JOIN feelec_ticket t ON s.feelec_status_id = t.feelec_status_id AND t.feelec_delete = 20
        WHERE s.delete_time = 0
        GROUP BY s.feelec_status_id, s.feelec_name
        HAVING count > 0
        ORDER BY count DESC
        """
        
        status_distribution = connector.execute_query(status_sql)
        
        return success_response(data={
            'project_stats': project_stats[0] if project_stats else {},
            'ticket_stats': ticket_stats[0] if ticket_stats else {},
            'trend_data': trend_data,
            'status_distribution': status_distribution
        })
        
    except Exception as e:
        return error_response(message=f"获取仪表盘统计失败: {str(e)}")
    finally:
        connector.close()

@router.get("/integration-suggestions", summary="获取集成建议")
async def get_integration_suggestions(
    current_user: dict = Depends(get_current_user)
):
    """获取工单系统集成建议"""
    connector = TicketSystemConnector()
    
    try:
        # 获取基础统计数据
        stats_sql = """
        SELECT 
            (SELECT COUNT(*) FROM feelec_project WHERE feelec_delete = 20 AND complete_time = 0) as active_projects,
            (SELECT COUNT(*) FROM feelec_ticket WHERE feelec_delete = 20) as total_tickets,
            (SELECT COUNT(DISTINCT feelec_processor_id) FROM feelec_ticket WHERE feelec_delete = 20 AND feelec_processor_id != '') as active_processors
        """
        
        stats = connector.execute_query(stats_sql)
        
        # 生成集成建议
        suggestions = []
        
        if stats and stats[0]:
            data = stats[0]
            
            if data['active_projects'] > 0:
                suggestions.append({
                    'category': '项目管理',
                    'title': '项目进度跟踪集成',
                    'description': f'发现 {data["active_projects"]} 个活跃项目，建议集成工单系统的项目进度跟踪功能',
                    'priority': 'high',
                    'benefits': ['实时项目进度监控', '工单完成率统计', '项目风险预警']
                })
            
            if data['active_processors'] > 10:
                suggestions.append({
                    'category': '团队管理',
                    'title': '工作负荷优化',
                    'description': f'发现 {data["active_processors"]} 个活跃处理人，建议集成工单分配优化功能',
                    'priority': 'medium',
                    'benefits': ['团队负荷均衡', '工作效率提升', '资源合理分配']
                })
            
            if data['total_tickets'] > 1000:
                suggestions.append({
                    'category': '数据分析',
                    'title': '深度数据挖掘',
                    'description': f'工单数据量达到 {data["total_tickets"]} 条，建议进行深度数据分析',
                    'priority': 'medium',
                    'benefits': ['历史趋势分析', '性能指标优化', '预测性维护']
                })
        
        # 通用建议
        suggestions.extend([
            {
                'category': '数据同步',
                'title': '实时数据同步',
                'description': '建议实现项目任务与工单的双向同步，确保数据一致性',
                'priority': 'high',
                'benefits': ['数据一致性保证', '减少重复录入', '提高工作效率']
            },
            {
                'category': '状态管理',
                'title': '统一状态管理',
                'description': '集成工单状态管理，实现任务状态的自动更新',
                'priority': 'high',
                'benefits': ['状态自动同步', '流程标准化', '进度透明化']
            },
            {
                'category': '报表分析',
                'title': '增强报表功能',
                'description': '利用工单系统的丰富数据，增强项目报表和分析功能',
                'priority': 'medium',
                'benefits': ['多维度分析', '可视化报表', '决策支持']
            }
        ])
        
        return success_response(data={
            'suggestions': suggestions,
            'total_count': len(suggestions)
        })
        
    except Exception as e:
        return error_response(message=f"获取集成建议失败: {str(e)}")
    finally:
        connector.close()

@router.get("/tickets/{ticket_id}/full-content", summary="获取工单完整内容")
async def get_ticket_full_content(
    ticket_id: int,
    current_user: dict = Depends(get_current_user)
):
    """获取工单的完整详细内容"""
    connector = TicketSystemConnector()

    try:
        if not connector.connect():
            return error_response(message="数据库连接失败")

        # 获取工单基本信息 - 只包含实际存在的字段
        ticket_sql = """
        SELECT
            t.feelec_ticket_id,
            t.feelec_title,
            t.feelec_ticket_no,
            t.feelec_project_id,
            t.feelec_publisher_id,
            t.feelec_processor_id,
            t.feelec_department_id,
            t.feelec_company_id,
            t.feelec_priority,
            t.feelec_status_id,
            t.feelec_source,
            t.first_assign_time,
            t.first_process_time,
            t.deadlines,
            t.complete_time,
            t.create_time,
            t.feelec_template_id,
            t.feelec_delete,
            p.feelec_name as project_name,
            s.feelec_name as status_name,
            u1.feelec_name as publisher_name,
            u2.feelec_name as processor_name,
            d.feelec_name as department_name,
            c.feelec_name as company_name,
            tt.feelec_name as template_name
        FROM feelec_ticket t
        LEFT JOIN feelec_project p ON t.feelec_project_id = p.feelec_project_id AND p.feelec_delete = 20
        LEFT JOIN feelec_ticket_status s ON t.feelec_status_id = s.feelec_status_id
        LEFT JOIN feelec_user u1 ON t.feelec_publisher_id = u1.feelec_user_id AND u1.delete_time = 0
        LEFT JOIN feelec_user u2 ON t.feelec_processor_id = u2.feelec_user_id AND u2.delete_time = 0
        LEFT JOIN feelec_member_department d ON t.feelec_department_id = d.feelec_department_id AND d.delete_time = 0
        LEFT JOIN feelec_company c ON t.feelec_company_id = c.feelec_company_id AND c.delete_time = 0
        LEFT JOIN feelec_ticket_template tt ON t.feelec_template_id = tt.feelec_template_id AND tt.delete_time = 0
        WHERE t.feelec_ticket_id = %s AND t.feelec_delete = 20
        """

        tickets = connector.execute_query(ticket_sql, (ticket_id,))

        if not tickets:
            return error_response(message="工单不存在")

        ticket = tickets[0]

        # 处理优先级
        priority_map = {1: '紧急', 2: '高', 3: '中', 4: '低'}
        priority_color_map = {1: 'danger', 2: 'warning', 3: 'primary', 4: 'info'}
        ticket['priority_text'] = priority_map.get(ticket['feelec_priority'], '普通')
        ticket['priority_color'] = priority_color_map.get(ticket['feelec_priority'], 'info')

        # 处理来源
        source_map = {1: '电话', 2: '邮件', 3: '系统', 4: '其他'}
        ticket['source_text'] = source_map.get(ticket['feelec_source'], '未知')

        # 处理时间格式化
        def format_timestamp(timestamp):
            if timestamp and timestamp > 0:
                return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
            return '无'

        ticket['create_time_formatted'] = format_timestamp(ticket['create_time'])
        ticket['first_assign_time_formatted'] = format_timestamp(ticket['first_assign_time'])
        ticket['first_process_time_formatted'] = format_timestamp(ticket['first_process_time'])
        ticket['complete_time_formatted'] = format_timestamp(ticket['complete_time'])
        ticket['deadline_formatted'] = format_timestamp(ticket['deadlines']) if ticket['deadlines'] else '无期限'

        # 获取工单详细字段内容
        detail_fields_sql = """
        SELECT
            td.feelec_field_id,
            td.feelec_content,
            tf.feelec_name as field_name,
            tf.feelec_type as field_type
        FROM feelec_ticket_detail td
        LEFT JOIN feelec_ticket_template_field tf ON td.feelec_field_id = tf.feelec_field_id
        WHERE td.feelec_ticket_id = %s
        ORDER BY td.feelec_sort ASC
        """

        detail_fields = connector.execute_query(detail_fields_sql, (ticket_id,))

        # 将详细字段转换为字典格式
        ticket_detail_fields = {}
        for field in detail_fields:
            field_name = field.get('field_name') or f"字段{field['feelec_field_id']}"
            ticket_detail_fields[field_name] = field.get('feelec_content', '')

        ticket['detail_fields'] = ticket_detail_fields

        # 计算处理时长
        if ticket['complete_time'] and ticket['complete_time'] > 0 and ticket['first_process_time'] and ticket['first_process_time'] > 0:
            duration_seconds = ticket['complete_time'] - ticket['first_process_time']
            duration_hours = round(duration_seconds / 3600, 2)
            ticket['process_duration_hours'] = duration_hours
            ticket['process_duration_text'] = f"{duration_hours}小时"
        else:
            ticket['process_duration_hours'] = None
            ticket['process_duration_text'] = '未完成'

        # 判断是否完成
        ticket['is_completed'] = ticket['complete_time'] and ticket['complete_time'] > 0

        # 判断是否逾期
        current_time = datetime.now().timestamp()
        if ticket['deadlines'] and ticket['deadlines'] > 0 and not ticket['is_completed']:
            if current_time > ticket['deadlines']:
                ticket['is_overdue'] = True
                ticket['overdue_days'] = int((current_time - ticket['deadlines']) / 86400)
            else:
                ticket['is_overdue'] = False
                ticket['overdue_days'] = 0
        else:
            ticket['is_overdue'] = False
            ticket['overdue_days'] = 0

        # 获取处理记录
        process_sql = """
        SELECT
            tp.feelec_process_id,
            tp.feelec_processor_id,
            tp.feelec_action_id,
            tp.feelec_content,
            tp.create_time,
            ta.feelec_name as action_name,
            u.feelec_name as processor_name
        FROM feelec_ticket_process tp
        LEFT JOIN feelec_ticket_action ta ON tp.feelec_action_id = ta.feelec_action_id
        LEFT JOIN feelec_user u ON tp.feelec_processor_id = u.feelec_user_id AND u.delete_time = 0
        WHERE tp.feelec_ticket_id = %s AND tp.feelec_delete = 20
        ORDER BY tp.create_time ASC
        """

        process_records = connector.execute_query(process_sql, (ticket_id,))

        # 格式化处理记录
        for record in process_records:
            record['create_time_formatted'] = format_timestamp(record['create_time'])
            # processor_name 已经从SQL中获取，如果为空则显示ID
            if not record.get('processor_name'):
                record['processor_name'] = record['feelec_processor_id'] or '未知'

        ticket['process_records'] = process_records

        # 获取工单详细内容
        detail_sql = """
        SELECT
            form_identify,
            form_type,
            form_content,
            create_time
        FROM feelec_ticket_detail
        WHERE feelec_ticket_id = %s
        ORDER BY create_time ASC
        """

        detail_result = connector.execute_query(detail_sql, (ticket_id,))

        # 处理详细内容
        ticket_content = {}
        for detail in detail_result:
            if detail['form_identify'] == 'title':
                ticket_content['title'] = detail['form_content']
            elif detail['form_identify'] == 'content':
                ticket_content['content'] = detail['form_content']
            else:
                # 其他自定义字段
                ticket_content[detail['form_identify']] = detail['form_content']

        ticket['ticket_content'] = ticket_content

        # 获取发布人信息（子公司需求来源）
        publisher_info = None
        if ticket['feelec_publisher_id']:
            publisher_sql = """
            SELECT
                feelec_user_id,
                feelec_name,
                feelec_mobile,
                feelec_email,
                feelec_company_id
            FROM feelec_user
            WHERE feelec_user_id = %s
            """

            publisher_result = connector.execute_query(publisher_sql, (ticket['feelec_publisher_id'],))
            if publisher_result:
                publisher_info = publisher_result[0]

                # 获取公司信息
                if publisher_info['feelec_company_id']:
                    company_sql = """
                    SELECT
                        feelec_company_id,
                        feelec_name as company_name
                    FROM feelec_company
                    WHERE feelec_company_id = %s
                    """

                    company_result = connector.execute_query(company_sql, (publisher_info['feelec_company_id'],))
                    if company_result:
                        publisher_info['company_info'] = company_result[0]

        ticket['publisher_info'] = publisher_info

        # 设置工单内容（从详细内容中获取）
        if ticket_content.get('content'):
            ticket['feelec_content'] = ticket_content['content']
        else:
            ticket['feelec_content'] = '暂无详细内容'

        return success_response(data=ticket)

    except Exception as e:
        return error_response(message=f"获取工单详情失败: {str(e)}")
    finally:
        connector.close()
