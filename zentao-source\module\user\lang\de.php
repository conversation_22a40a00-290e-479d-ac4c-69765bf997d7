<?php
/**
 * The user module English file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     user
 * @version     $Id: en.php 5053 2013-07-06 08:17:37Z <EMAIL> $
 * @link        https://www.zentao.net
 */
$lang->user->common           = 'Benutzer';
$lang->user->id               = 'ID';
$lang->user->inside           = 'Inside Members';
$lang->user->outside          = 'Outside Members';
$lang->user->company          = 'Unternehmen';
$lang->user->dept             = 'Abteilung';
$lang->user->account          = 'Konto';
$lang->user->password         = 'Passwort';
$lang->user->passwordfield    = 'Passwort';
$lang->user->password1        = 'Passwort';
$lang->user->password2        = 'Passwort wiederholen';
$lang->user->role             = 'Rolle';
$lang->user->group            = 'Gruppe';
$lang->user->realname         = 'Name';
$lang->user->nickname         = 'Spitzname';
$lang->user->commiter         = 'SCM User';
$lang->user->birthyear        = 'Geburtsdatum';
$lang->user->gender           = 'Geschlecht';
$lang->user->email            = 'Email';
$lang->user->basicInfo        = 'Basis Info';
$lang->user->accountInfo      = 'Konto';
$lang->user->verify           = 'Bestätigen';
$lang->user->contactInfo      = 'Kontakt';
$lang->user->skype            = 'Skype';
$lang->user->qq               = 'QQ';
$lang->user->mobile           = 'Mobil';
$lang->user->phone            = 'Telefon';
$lang->user->weixin           = 'WeChat';
$lang->user->dingding         = 'DingDing';
$lang->user->slack            = 'Slack';
$lang->user->whatsapp         = 'WhatsApp';
$lang->user->address          = 'Adresse';
$lang->user->zipcode          = 'PLZ';
$lang->user->join             = 'Beigetreten am';
$lang->user->priv             = 'Gruppe';
$lang->user->visits           = 'Besuche';
$lang->user->visions          = 'Version Type';
$lang->user->ip               = 'Letzte IP';
$lang->user->last             = 'Letztes Login';
$lang->user->ranzhi           = 'Zdoo Konto';
$lang->user->ditto            = 'Dito';
$lang->user->originalPassword = 'Altes Passwort';
$lang->user->newPassword      = 'New Password';
$lang->user->verifyPassword   = 'Ihr Login Passwort';
$lang->user->forgetPassword   = 'Passwort vergessen?';
$lang->user->score            = 'Score';
$lang->user->name             = 'Name';
$lang->user->type             = 'User Type';
$lang->user->cropAvatar       = 'Crop Avatar';
$lang->user->cropAvatarTip    = 'Drag and drop the box to select the image clipping range.';
$lang->user->cropImageTip     = 'The image used is too small, the recommended image size is at least 48x48, the current image size is %s';
$lang->user->captcha          = 'Captcha';
$lang->user->avatar           = 'Avatar';
$lang->user->birthday         = 'Birthday';
$lang->user->nature           = 'Nature';
$lang->user->analysis         = 'Analysis';
$lang->user->strategy         = 'Strategy';
$lang->user->fails            = 'number of failures';
$lang->user->locked           = 'Lock Time';
$lang->user->scoreLevel       = 'Score Level';
$lang->user->clientStatus     = 'Client Status';
$lang->user->clientLang       = 'Client Language';
$lang->user->programs         = 'Program';
$lang->user->products         = $lang->productCommon;
$lang->user->projects         = $lang->projectCommon;
$lang->user->sprints          = $lang->execution->common;
$lang->user->identity         = 'Identity';
$lang->user->switchVision     = 'Switch to %s';
$lang->user->submit           = 'Submit';
$lang->user->resetPWD         = 'Reset Password';
$lang->user->resetPwdByAdmin  = 'Reset password via admin';
$lang->user->resetPwdByMail   = 'Reset password via email';

$lang->user->abbr = new stdclass();
$lang->user->abbr->id        = 'ID';
$lang->user->abbr->password2 = 'Passwort wiederholen';
$lang->user->abbr->address   = 'Adresse';
$lang->user->abbr->join      = 'Induktion';

$lang->user->legendBasic        = 'Basic Information';
$lang->user->legendContribution = 'Contribution';

$lang->user->index         = "Home";
$lang->user->view          = "Übersicht";
$lang->user->create        = "Benutzer erstellen";
$lang->user->batchCreate   = "Mehrere erstellen";
$lang->user->edit          = "Bearbeiten";
$lang->user->batchEdit     = "Mehrere bearbeiten";
$lang->user->unlock        = "Entsperren";
$lang->user->delete        = "Löschen";
$lang->user->unbind        = "Zdoo verbindung aufheben";
$lang->user->login         = "Anmelden";
$lang->user->bind          = "Bind User";
$lang->user->oauthRegister = "Register a new account";
$lang->user->mobileLogin   = "Mobil";
$lang->user->editProfile   = "Bearbeiten";
$lang->user->deny          = "Zugriff verweigert.";
$lang->user->confirmDelete = "Möchten Sie diesen Benutzer löschen?";
$lang->user->confirmUnlock = "Möchten Sie diese Benutzer entsperren?";
$lang->user->confirmUnbind = "Möchten Sie dei Verknüpfung zu Zdoo aufheben?";
$lang->user->relogin       = "Erneut anmelden";
$lang->user->asGuest       = "Gast";
$lang->user->goback        = "Zurück";
$lang->user->deleted       = '(Gelöscht)';
$lang->user->search        = 'Suche';
$lang->user->else          = 'Else';

$lang->user->saveTemplate          = 'Save as Template';
$lang->user->setPublic             = 'Set as Public Template';
$lang->user->deleteTemplate        = 'Delete Template';
$lang->user->setTemplateTitle      = 'Please enter the title of template.';
$lang->user->applyTemplate         = 'Templates';
$lang->user->confirmDeleteTemplate = 'Do you want to delete this template?';
$lang->user->setPublicTemplate     = 'Set as Public Template';
$lang->user->tplContentNotEmpty    = 'Vorlageninhalt darf nicht leer sein!';
$lang->user->sendEmailSuccess      = 'An email has been sent to your mailbox. Please check it.';
$lang->user->linkExpired           = 'The link has expired, please apply again.';

$lang->user->profile   = 'Profil';
$lang->user->project   = $lang->executionCommon;
$lang->user->execution = 'Execution';
$lang->user->task      = 'Aufgaben';
$lang->user->bug       = 'Bugs';
$lang->user->test      = 'Tests';
$lang->user->testTask  = 'Testaufgaben';
$lang->user->testCase  = 'Testfälle';
$lang->user->issue     = 'Issue';
$lang->user->risk      = 'Risk';
$lang->user->schedule  = 'Schedule';
$lang->user->todo      = 'Todos';
$lang->user->story     = 'Story';
$lang->user->dynamic   = 'Verlauf';

$lang->user->openedBy    = 'Geöffnet';
$lang->user->assignedTo  = 'Zugeordnet';
$lang->user->finishedBy  = 'Abgeschlossen';
$lang->user->involved    = 'Involved By %s';
$lang->user->resolvedBy  = 'Gelöst';
$lang->user->closedBy    = 'Geschlossen';
$lang->user->reviewedBy  = 'Überprüft';
$lang->user->canceledBy  = 'Abgebrochen';

$lang->user->testTask2Him = 'Build';
$lang->user->case2Him     = 'Fall zugeordnet';
$lang->user->caseByHim    = 'Fall geöffnet';

$lang->user->errorDeny    = "Sorry, your access to <b>%2\$s</b> of <b>%1\$s</b> is denied. Please contact your Admin to get privileges. Return to home page or login again.";
$lang->user->errorView    = "Sorry, your access view <b>%s</b> is denied. Please contact your Admin to get privileges. Return to home page or login again.";
$lang->user->loginFailed  = "Login fehlgeschlagen. Bitte prüfen Sie Ihren Benutzernamen und das Passwort.";
$lang->user->lockWarning  = "Sie haben %s Versuche.";
$lang->user->loginLocked  = "Bitte kontaktieren Sie den Administrator um Ihr Konto entsperren zu lassen oder versuchen Sie in %s Minuten noch ein mal.";
$lang->user->weakPassword = "Ihr Passwort entspricht nicht den Anforderungen.";
$lang->user->errorWeak    = "Passwords cannot use [%s] these commonly used weak passwords.";
$lang->user->errorCaptcha = "Captcha Error";
$lang->user->loginExpired = 'System login has expired, please log in again :)';

$lang->user->roleList['']       = '';
$lang->user->roleList['dev']    = 'Developer';
$lang->user->roleList['qa']     = 'QS';
$lang->user->roleList['pm']     = 'Scrum Master';
$lang->user->roleList['po']     = 'Product Owner';
$lang->user->roleList['td']     = 'Technischer Manager';
$lang->user->roleList['pd']     = 'Produkt Manager';
$lang->user->roleList['qd']     = 'QS Manager';
$lang->user->roleList['top']    = 'Senior Manager';
$lang->user->roleList['others'] = 'Andere';

$lang->user->genderList['m'] = 'Männlich';
$lang->user->genderList['f'] = 'Weiblich';

$lang->user->thirdPerson['m'] = 'Him';
$lang->user->thirdPerson['f'] = 'Her';

$lang->user->typeList['inside']  = $lang->user->inside;
$lang->user->typeList['outside'] = $lang->user->outside;

$lang->user->passwordStrengthList[0] = "<span style='color:red'>Schwach</span>";
$lang->user->passwordStrengthList[1] = "<span style='color:#000'>Gut</span>";
$lang->user->passwordStrengthList[2] = "<span style='color:green'>Stark</span>";

$lang->user->statusList['active'] = 'Aktiviert';
$lang->user->statusList['delete'] = 'Gelöscht';

$lang->user->personalData['createdTodos']        = 'Todos Created';
$lang->user->personalData['createdRequirements'] = "Requirements Created";
$lang->user->personalData['createdStories']      = "Stories Created";
$lang->user->personalData['finishedTasks']       = 'Tasks Finished';
$lang->user->personalData['createdBugs']         = 'Bugs Created';
$lang->user->personalData['resolvedBugs']        = 'Bugs Resolved';
$lang->user->personalData['createdCases']        = 'Cases Created';
$lang->user->personalData['createdRisks']        = 'Risks Created';
$lang->user->personalData['resolvedRisks']       = 'Risks Resolved';
$lang->user->personalData['createdIssues']       = 'Issues Created';
$lang->user->personalData['resolvedIssues']      = 'Issues Resolved';
$lang->user->personalData['createdDocs']         = 'Docs Created';

$lang->user->keepLogin['on']   = 'Angemeldet bleiben';
$lang->user->loginWithDemoUser = 'Login als Demo User:';
$lang->user->scanToLogin       = 'Scan QR Code';

$lang->user->tpl = new stdclass();
$lang->user->tpl->type    = 'Typ';
$lang->user->tpl->title   = 'TPL Titel';
$lang->user->tpl->content = 'Inhalt';
$lang->user->tpl->public  = 'Öffentlich';

$lang->usertpl = new stdclass();
$lang->usertpl->account = 'Creator';
$lang->usertpl->type    = 'Template Type';
$lang->usertpl->title   = 'Template Name';
$lang->usertpl->content = 'Template Content';
$lang->usertpl->public  = 'Public';

$lang->user->placeholder = new stdclass();
$lang->user->placeholder->account   = 'Buchstaben, Understrich und Ziffern. Mindestens 3 Zeichen';
$lang->user->placeholder->password1 = 'Mindestens 6 Zeichen ';
$lang->user->placeholder->role      = "Rolle betrifft Inhalt und die Benutzerreihenfolge.";
$lang->user->placeholder->group     = "Die Gruppe bestimmt die Benutzerrechte.";
$lang->user->placeholder->commiter  = 'SVN/Git Konto';
$lang->user->placeholder->verify    = 'Bitte geben Sie Ihr Login Passwort ein.';

$lang->user->placeholder->loginPassword = 'Enter your password';
$lang->user->placeholder->loginAccount  = 'Enter your account';
$lang->user->placeholder->loginUrl      = 'Enter your ZenTao address';
$lang->user->placeholder->email         = 'Enter your email';

$lang->user->placeholder->passwordStrength[0] = '≥6 letters.';
$lang->user->placeholder->passwordStrength[1] = 'Buchstaben und Ziffern. Mindestens 6 Zeichen';
$lang->user->placeholder->passwordStrength[2] = 'Buchstaben, Ziffern und Sonderzeichen. Mindestens 10 Zeichen';

$lang->user->placeholder->passwordStrengthCheck[0] = 'The password should be ≥ 6 letters.';
$lang->user->placeholder->passwordStrengthCheck[1] = 'The password should be ≥ 6 letters, combination of uppercase, lowercase letters and numbers.';
$lang->user->placeholder->passwordStrengthCheck[2] = 'The password should be ≥ 10 letters, combination of uppercase, lowercase letters, numbers, and special symbols.';

$lang->user->error = new stdclass();
$lang->user->error->account        = 'ID %s，Konto muss Buchstaben, Unterstriche oder Ziffern enthalten. Mindestens 3 Zeichen.';
$lang->user->error->accountDupl    = 'ID %s，Konto wurde genutzt.';
$lang->user->error->realname       = 'ID %s，muss der Realname sein';
$lang->user->error->visions        = 'ID %s，must be version type';
$lang->user->error->password       = 'ID %s，passwort muss mindestens 6 Zeichen lang sein.';
$lang->user->error->mail           = 'ID %s，bitte geben Sie eine gültige Emailadresse an.';
$lang->user->error->reserved       = 'ID %s，Konto ist reserviert.';
$lang->user->error->weakPassword   = 'ID %s，the password strength is less than the system setting.';
$lang->user->error->dangerPassword = "ID %s，Passwords cannot be used with [%s] these commonly used if-passwords.";

$lang->user->error->url              = "Invalid address. Please contact your ZenTao Admin.";
$lang->user->error->verify           = "Wrong account or password.";
$lang->user->error->verifyPassword   = "Verifikation fehlgeschlagen. Bitte geben Sie ihr Loginpasswort ein.";
$lang->user->error->originalPassword = "Altes Passwort ist falsch.";
$lang->user->error->companyEmpty     = "Company name must be not empty.";
$lang->user->error->noAccess         = "This user is not from your department. You have no access to this user information.";
$lang->user->error->accountEmpty     = 'Account must be not empty !';
$lang->user->error->emailEmpty       = 'Email must be not empty !';
$lang->user->error->noUser           = 'Invalid account.';
$lang->user->error->noEmail          = 'The user does not register email. Please get in touch with the administrator to reset the password.';
$lang->user->error->errorEmail       = 'The account does not match the email. Please enter a new one.';
$lang->user->error->emailSetting     = 'No email is configured in the system. Contact the admin to reset the email.';
$lang->user->error->sendMailFail     = 'Message sending failed, please try again!';
$lang->user->error->loginTimeoutTip  = 'Login failed, please check if the proxy service is normal.';
$lang->user->error->uploadAvatar     = "The upload of your profile picture was unsuccessful. Kindly ensure that you're uploading a file of the appropriate image type.";

$lang->user->contactFieldList['phone']    = $lang->user->phone;
$lang->user->contactFieldList['mobile']   = $lang->user->mobile;
$lang->user->contactFieldList['qq']       = $lang->user->qq;
$lang->user->contactFieldList['dingding'] = $lang->user->dingding;
$lang->user->contactFieldList['weixin']   = $lang->user->weixin;
$lang->user->contactFieldList['skype']    = $lang->user->skype;
$lang->user->contactFieldList['slack']    = $lang->user->slack;
$lang->user->contactFieldList['whatsapp'] = $lang->user->whatsapp;

$lang->user->executionTypeList['stage']  = 'Stage';
$lang->user->executionTypeList['sprint'] = $lang->iterationCommon;

$lang->user->contacts = new stdclass();
$lang->user->contacts->common   = 'Kontakt';
$lang->user->contacts->listName = 'Namensliste';
$lang->user->contacts->userList = 'Benutzerliste';

$lang->usercontact = new stdclass;
$lang->usercontact->account  = 'Creator';
$lang->usercontact->listName = 'List Name';
$lang->usercontact->userList = 'Benutzerliste';
$lang->usercontact->public   = 'Public';

$lang->user->contacts->manage        = 'Verwalten';
$lang->user->contacts->contactsList  = 'Kontakte';
$lang->user->contacts->selectedUsers = 'Auswahl';
$lang->user->contacts->selectList    = 'Liste';
$lang->user->contacts->createList    = 'Erstelle Kontaktliste';
$lang->user->contacts->noListYet     = 'Es existiert keine Kontaktliste. Bitte erstellen Sie eine.';
$lang->user->contacts->confirmDelete = 'Möchten Sie diese Liste löschen?';
$lang->user->contacts->or            = ' oder ';

$lang->user->resetFail        = "Fehlgeschlagen. Bitte prüfen Sie das Konto";
$lang->user->resetSuccess     = "Zurückgesetzt! Bitte nutzen Sie Ihr neues Passwort, um sich anzumelden.";
$lang->user->noticeDelete     = 'Do you want to delete "%s" from ZenTao?';
$lang->user->noticeHasDeleted = "This user has been deleted. If you want to view it, please go to the Admin-System-Data-Recycle to restore it.";
$lang->user->noticeResetFile  = "<h5>Contact the Administrator to reset your password.</h5>
    <h5>If you are, please login your Zentao host and create a file named <span> '%s' </span>.</h5>
    <p>Note:</p>
    <ol>
    <li>Keep the file empty.</li>
    <li>If the file exists, remove it and create it again.</li>
    </ol>";
$lang->user->notice4Safe = "Warning: Weak password of one click package detected";
$lang->user->process4DIR = "It is detected that you may be using the one click installation package environment. Other sites in the environment are still using simple passwords. For security reasons, if you do not use other sites, please handle them in time. Delete or rename the %s directory. Visit: <a href='https://www.zentao.pm/book/zentaomanual/fix-weak-password-564.html' target='_blank'>https://www.zentao.pm/book/zentaomanual/fix-weak-password-564.html</a>";
$lang->user->process4DB  = "It is detected that you may be using the one click installation package environment. Other sites in the environment are still using simple passwords. For security reasons, if you do not use other sites, please handle them in time. Please login database and modify password field of zt_user table of %s database. Visit: <a href='https://www.zentao.pm/book/zentaomanual/fix-weak-password-564.html' target='_blank'>https://www.zentao.pm/book/zentaomanual/fix-weak-password-564.html</a>";
$lang->user->mkdirWin = <<<EOT
    <html><head><meta charset='utf-8'></head>
    <body><table align='center' style='width:700px; margin-top:100px; border:1px solid gray; font-size:14px;'><tr><td style='padding:8px'>
    <div style='margin-bottom:8px;'>不能创建临时目录，请确认目录<strong style='color:#ed980f'>%s</strong>是否存在并有操作权限。</div>
    <div>A tmp directory cannot be created. Make sure the directory <strong style='color:#ed980f'>%s</strong> exists and you have the right permission.</div>
    </td></tr></table></body></html>
EOT;
$lang->user->mkdirLinux = <<<EOT
    <html><head><meta charset='utf-8'></head>
    <body><table align='center' style='width:700px; margin-top:100px; border:1px solid gray; font-size:14px;'><tr><td style='padding:8px'>
    <div style='margin-bottom:8px;'>不能创建临时目录，请确认目录<strong style='color:#ed980f'>%s</strong>是否存在并有操作权限。</div>
    <div style='margin-bottom:8px;'>命令为：<strong style='color:#ed980f'>chmod 777 -R %s</strong>。</div>
    <div>A tmp directory cannot be created. Make sure the directory <strong style='color:#ed980f'>%s</strong> exists and you have the right permission.</div>
    <div style='margin-bottom:8px;'>Command: <strong style='color:#ed980f'>chmod 777 -R %s</strong>.</div>
    </td></tr></table></body></html>
EOT;

$lang->user->jumping = "This page will redirect to the previous page in <span id='time'>10</span> seconds. <a href='%s' id='redirect' class='btn primary'>Redirect Now</a>";

$lang->user->zentaoapp = new stdclass();
$lang->user->zentaoapp->logout = 'Logout';

$lang->user->featureBar['todo']['all']             = 'Assigned To Yourself';
$lang->user->featureBar['todo']['before']          = 'Unfinished';
$lang->user->featureBar['todo']['future']          = 'TBD';
$lang->user->featureBar['todo']['thisWeek']        = 'This Week';
$lang->user->featureBar['todo']['thisMonth']       = 'This Month';
$lang->user->featureBar['todo']['thisYear']        = 'This Year';
$lang->user->featureBar['todo']['assignedToOther'] = 'Assigned To Other';
$lang->user->featureBar['todo']['cycle']           = 'Recurrence';

$lang->user->featureBar['dynamic']['all']       = 'All';
$lang->user->featureBar['dynamic']['today']     = 'Today';
$lang->user->featureBar['dynamic']['yesterday'] = 'Yesterday';
$lang->user->featureBar['dynamic']['thisWeek']  = 'This Week';
$lang->user->featureBar['dynamic']['lastWeek']  = 'Last Week';
$lang->user->featureBar['dynamic']['thisMonth'] = 'This Month';
$lang->user->featureBar['dynamic']['lastMonth'] = 'Last Month';
