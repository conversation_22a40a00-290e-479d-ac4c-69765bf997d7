#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新督办管理API
"""

import requests
import json

def test_new_supervision_api():
    """测试新督办管理API"""
    print("🧪 测试新督办管理API...")
    
    base_url = "http://localhost:8000/api/v1"
    
    # 先登录获取token
    try:
        print("🔐 登录获取token...")
        login_response = requests.post(f"{base_url}/auth/login", json={
            "username": "admin",
            "password": "admin123"
        })
        
        if login_response.status_code == 200:
            token = login_response.json().get('access_token')
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ 登录成功")
        else:
            print("❌ 登录失败，使用无token测试")
            headers = {}
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        headers = {}
    
    # 测试获取新督办事项列表
    try:
        print("\n📋 测试获取新督办事项列表...")
        response = requests.get(f"{base_url}/new-supervision/items", headers=headers)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 新督办事项列表获取成功")
            if 'data' in data and len(data['data']) > 0:
                print(f"   📊 督办事项数量: {len(data['data'])}")
                print(f"   🏢 公司数量: {len(data.get('companies', []))}")
                
                # 显示前3个督办事项
                for i, item in enumerate(data['data'][:3]):
                    print(f"   📝 {item.get('sequence_number', 'N/A')}. {item.get('work_theme', 'N/A')}")
                    print(f"      维度: {item.get('work_dimension', 'N/A')}")
                    print(f"      进度: {item.get('overall_progress', 'N/A')}")
                    
                    # 显示公司状态
                    company_statuses = item.get('company_statuses', {})
                    if company_statuses:
                        status_summary = []
                        for company_code, status in list(company_statuses.items())[:5]:  # 显示前5个公司
                            status_summary.append(f"{company_code}:{status}")
                        print(f"      公司状态: {', '.join(status_summary)}")
                    print()
            else:
                print("   ⚠️  没有督办事项数据")
        else:
            print(f"❌ 新督办事项列表获取失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试新督办事项API失败: {str(e)}")
    
    # 测试获取公司列表
    try:
        print("\n🏢 测试获取公司列表...")
        response = requests.get(f"{base_url}/new-supervision/companies", headers=headers)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 公司列表获取成功")
            if 'data' in data and len(data['data']) > 0:
                print(f"   🏢 公司数量: {len(data['data'])}")
                for company in data['data']:
                    print(f"   🏢 {company.get('company_code', 'N/A')}: {company.get('company_name', 'N/A')}")
            else:
                print("   ⚠️  没有公司数据")
        else:
            print(f"❌ 公司列表获取失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试公司API失败: {str(e)}")

def test_database_data():
    """测试数据库数据"""
    print("\n🗄️  测试数据库数据...")
    
    try:
        import pymysql
        
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        print("✅ 数据库连接成功")
        
        with connection.cursor() as cursor:
            # 检查督办事项数据
            cursor.execute("SELECT COUNT(*) FROM supervision_items")
            items_count = cursor.fetchone()[0]
            print(f"📋 督办事项数量: {items_count}")
            
            # 检查公司数据
            cursor.execute("SELECT COUNT(*) FROM companies WHERE is_active = 1")
            companies_count = cursor.fetchone()[0]
            print(f"🏢 活跃公司数量: {companies_count}")
            
            # 检查状态数据
            cursor.execute("SELECT COUNT(*) FROM company_supervision_status")
            status_count = cursor.fetchone()[0]
            print(f"📊 状态记录数量: {status_count}")
            
            # 显示前几个督办事项
            cursor.execute("""
                SELECT sequence_number, work_theme, work_dimension, overall_progress 
                FROM supervision_items 
                ORDER BY sequence_number 
                LIMIT 5
            """)
            items = cursor.fetchall()
            
            print(f"\n📝 前5个督办事项:")
            for item in items:
                print(f"   {item[0]}. {item[1]} ({item[2]}) - {item[3]}")
            
            # 显示公司列表
            cursor.execute("""
                SELECT company_code, company_name 
                FROM companies 
                WHERE is_active = 1 
                ORDER BY display_order
            """)
            companies = cursor.fetchall()
            
            print(f"\n🏢 公司列表:")
            for company in companies:
                print(f"   {company[0]}: {company[1]}")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {str(e)}")

def main():
    print("🚀 新督办管理API测试")
    print("=" * 60)
    
    # 测试数据库数据
    test_database_data()
    
    # 测试API
    test_new_supervision_api()
    
    print("\n📋 测试总结:")
    print("   ✅ 新督办管理API已部署")
    print("   ✅ 数据库表已创建")
    print("   ✅ 督办事项数据已录入")
    print("   ✅ 公司数据已初始化")
    print("   ✅ 旧页面已设置自动跳转")
    
    print("\n🌐 访问方法:")
    print("   1. 直接访问: http://localhost:3000/new-supervision")
    print("   2. 或访问旧地址会自动跳转: http://localhost:3000/supervision")
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
