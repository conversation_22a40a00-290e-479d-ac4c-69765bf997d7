/**
 * simplemde v1.11.2
 * Copyright Next Step Webs, Inc.
 * @link https://github.com/NextStepWebs/simplemde-markdown-editor
 * @license MIT
 */
!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t;t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,t.SimpleMDE=e()}}(function(){var e;return function e(t,n,r){function i(a,l){if(!n[a]){if(!t[a]){var s="function"==typeof require&&require;if(!l&&s)return s(a,!0);if(o)return o(a,!0);var c=new Error("Cannot find module '"+a+"'");throw c.code="MODULE_NOT_FOUND",c}var u=n[a]={exports:{}};t[a][0].call(u.exports,function(e){var n=t[a][1][e];return i(n?n:e)},u,u.exports,e,t,n,r)}return n[a].exports}for(var o="function"==typeof require&&require,a=0;a<r.length;a++)i(r[a]);return i}({1:[function(e,t,n){"use strict";function r(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");return"="===e[t-2]?2:"="===e[t-1]?1:0}function i(e){return 3*e.length/4-r(e)}function o(e){var t,n,i,o,a,l,s=e.length;a=r(e),l=new f(3*s/4-a),i=a>0?s-4:s;var c=0;for(t=0,n=0;t<i;t+=4,n+=3)o=u[e.charCodeAt(t)]<<18|u[e.charCodeAt(t+1)]<<12|u[e.charCodeAt(t+2)]<<6|u[e.charCodeAt(t+3)],l[c++]=o>>16&255,l[c++]=o>>8&255,l[c++]=255&o;return 2===a?(o=u[e.charCodeAt(t)]<<2|u[e.charCodeAt(t+1)]>>4,l[c++]=255&o):1===a&&(o=u[e.charCodeAt(t)]<<10|u[e.charCodeAt(t+1)]<<4|u[e.charCodeAt(t+2)]>>2,l[c++]=o>>8&255,l[c++]=255&o),l}function a(e){return c[e>>18&63]+c[e>>12&63]+c[e>>6&63]+c[63&e]}function l(e,t,n){for(var r,i=[],o=t;o<n;o+=3)r=(e[o]<<16)+(e[o+1]<<8)+e[o+2],i.push(a(r));return i.join("")}function s(e){for(var t,n=e.length,r=n%3,i="",o=[],a=16383,s=0,u=n-r;s<u;s+=a)o.push(l(e,s,s+a>u?u:s+a));return 1===r?(t=e[n-1],i+=c[t>>2],i+=c[t<<4&63],i+="=="):2===r&&(t=(e[n-2]<<8)+e[n-1],i+=c[t>>10],i+=c[t>>4&63],i+=c[t<<2&63],i+="="),o.push(i),o.join("")}n.byteLength=i,n.toByteArray=o,n.fromByteArray=s;for(var c=[],u=[],f="undefined"!=typeof Uint8Array?Uint8Array:Array,h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",d=0,p=h.length;d<p;++d)c[d]=h[d],u[h.charCodeAt(d)]=d;u["-".charCodeAt(0)]=62,u["_".charCodeAt(0)]=63},{}],2:[function(e,t,n){},{}],3:[function(e,t,n){(function(t){"use strict";function r(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}function i(){return a.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function o(e,t){if(i()<t)throw new RangeError("Invalid typed array length");return a.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t),e.__proto__=a.prototype):(null===e&&(e=new a(t)),e.length=t),e}function a(e,t,n){if(!(a.TYPED_ARRAY_SUPPORT||this instanceof a))return new a(e,t,n);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return u(this,e)}return l(this,e,t,n)}function l(e,t,n,r){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?d(e,t,n,r):"string"==typeof t?f(e,t,n):p(e,t)}function s(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function c(e,t,n,r){return s(t),t<=0?o(e,t):void 0!==n?"string"==typeof r?o(e,t).fill(n,r):o(e,t).fill(n):o(e,t)}function u(e,t){if(s(t),e=o(e,t<0?0:0|m(t)),!a.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function f(e,t,n){if("string"==typeof n&&""!==n||(n="utf8"),!a.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|v(t,n);e=o(e,r);var i=e.write(t,n);return i!==r&&(e=e.slice(0,i)),e}function h(e,t){var n=t.length<0?0:0|m(t.length);e=o(e,n);for(var r=0;r<n;r+=1)e[r]=255&t[r];return e}function d(e,t,n,r){if(t.byteLength,n<0||t.byteLength<n)throw new RangeError("'offset' is out of bounds");if(t.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");return t=void 0===n&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,n):new Uint8Array(t,n,r),a.TYPED_ARRAY_SUPPORT?(e=t,e.__proto__=a.prototype):e=h(e,t),e}function p(e,t){if(a.isBuffer(t)){var n=0|m(t.length);return e=o(e,n),0===e.length?e:(t.copy(e,0,0,n),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||X(t.length)?o(e,0):h(e,t);if("Buffer"===t.type&&Q(t.data))return h(e,t.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function m(e){if(e>=i())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i().toString(16)+" bytes");return 0|e}function g(e){return+e!=e&&(e=0),a.alloc(+e)}function v(e,t){if(a.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var r=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return G(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return V(e).length;default:if(r)return G(e).length;t=(""+t).toLowerCase(),r=!0}}function y(e,t,n){var r=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if(n>>>=0,t>>>=0,n<=t)return"";for(e||(e="utf8");;)switch(e){case"hex":return P(this,t,n);case"utf8":case"utf-8":return N(this,t,n);case"ascii":return O(this,t,n);case"latin1":case"binary":return I(this,t,n);case"base64":return A(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return R(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function x(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function b(e,t,n,r,i){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=i?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(i)return-1;n=e.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof t&&(t=a.from(t,r)),a.isBuffer(t))return 0===t.length?-1:w(e,t,n,r,i);if("number"==typeof t)return t=255&t,a.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):w(e,[t],n,r,i);throw new TypeError("val must be string, number or Buffer")}function w(e,t,n,r,i){function o(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}var a=1,l=e.length,s=t.length;if(void 0!==r&&(r=String(r).toLowerCase(),"ucs2"===r||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;a=2,l/=2,s/=2,n/=2}var c;if(i){var u=-1;for(c=n;c<l;c++)if(o(e,c)===o(t,u===-1?0:c-u)){if(u===-1&&(u=c),c-u+1===s)return u*a}else u!==-1&&(c-=c-u),u=-1}else for(n+s>l&&(n=l-s),c=n;c>=0;c--){for(var f=!0,h=0;h<s;h++)if(o(e,c+h)!==o(t,h)){f=!1;break}if(f)return c}return-1}function k(e,t,n,r){n=Number(n)||0;var i=e.length-n;r?(r=Number(r),r>i&&(r=i)):r=i;var o=t.length;if(o%2!==0)throw new TypeError("Invalid hex string");r>o/2&&(r=o/2);for(var a=0;a<r;++a){var l=parseInt(t.substr(2*a,2),16);if(isNaN(l))return a;e[n+a]=l}return a}function S(e,t,n,r){return K(G(t,e.length-n),e,n,r)}function C(e,t,n,r){return K(Y(t),e,n,r)}function L(e,t,n,r){return C(e,t,n,r)}function T(e,t,n,r){return K(V(t),e,n,r)}function M(e,t,n,r){return K($(t,e.length-n),e,n,r)}function A(e,t,n){return 0===t&&n===e.length?Z.fromByteArray(e):Z.fromByteArray(e.slice(t,n))}function N(e,t,n){n=Math.min(e.length,n);for(var r=[],i=t;i<n;){var o=e[i],a=null,l=o>239?4:o>223?3:o>191?2:1;if(i+l<=n){var s,c,u,f;switch(l){case 1:o<128&&(a=o);break;case 2:s=e[i+1],128===(192&s)&&(f=(31&o)<<6|63&s,f>127&&(a=f));break;case 3:s=e[i+1],c=e[i+2],128===(192&s)&&128===(192&c)&&(f=(15&o)<<12|(63&s)<<6|63&c,f>2047&&(f<55296||f>57343)&&(a=f));break;case 4:s=e[i+1],c=e[i+2],u=e[i+3],128===(192&s)&&128===(192&c)&&128===(192&u)&&(f=(15&o)<<18|(63&s)<<12|(63&c)<<6|63&u,f>65535&&f<1114112&&(a=f))}}null===a?(a=65533,l=1):a>65535&&(a-=65536,r.push(a>>>10&1023|55296),a=56320|1023&a),r.push(a),i+=l}return E(r)}function E(e){var t=e.length;if(t<=ee)return String.fromCharCode.apply(String,e);for(var n="",r=0;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=ee));return n}function O(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(127&e[i]);return r}function I(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(e[i]);return r}function P(e,t,n){var r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);for(var i="",o=t;o<n;++o)i+=q(e[o]);return i}function R(e,t,n){for(var r=e.slice(t,n),i="",o=0;o<r.length;o+=2)i+=String.fromCharCode(r[o]+256*r[o+1]);return i}function D(e,t,n){if(e%1!==0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function H(e,t,n,r,i,o){if(!a.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function W(e,t,n,r){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-n,2);i<o;++i)e[n+i]=(t&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function B(e,t,n,r){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-n,4);i<o;++i)e[n+i]=t>>>8*(r?i:3-i)&255}function _(e,t,n,r,i,o){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function F(e,t,n,r,i){return i||_(e,t,n,4,3.4028234663852886e38,-3.4028234663852886e38),J.write(e,t,n,r,23,4),n+4}function z(e,t,n,r,i){return i||_(e,t,n,8,1.7976931348623157e308,-1.7976931348623157e308),J.write(e,t,n,r,52,8),n+8}function j(e){if(e=U(e).replace(te,""),e.length<2)return"";for(;e.length%4!==0;)e+="=";return e}function U(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function q(e){return e<16?"0"+e.toString(16):e.toString(16)}function G(e,t){t=t||1/0;for(var n,r=e.length,i=null,o=[],a=0;a<r;++a){if(n=e.charCodeAt(a),n>55295&&n<57344){if(!i){if(n>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(a+1===r){(t-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(t-=3)>-1&&o.push(239,191,189),i=n;continue}n=(i-55296<<10|n-56320)+65536}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((t-=1)<0)break;o.push(n)}else if(n<2048){if((t-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function Y(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}function $(e,t){for(var n,r,i,o=[],a=0;a<e.length&&!((t-=2)<0);++a)n=e.charCodeAt(a),r=n>>8,i=n%256,o.push(i),o.push(r);return o}function V(e){return Z.toByteArray(j(e))}function K(e,t,n,r){for(var i=0;i<r&&!(i+n>=t.length||i>=e.length);++i)t[i+n]=e[i];return i}function X(e){return e!==e}var Z=e("base64-js"),J=e("ieee754"),Q=e("isarray");n.Buffer=a,n.SlowBuffer=g,n.INSPECT_MAX_BYTES=50,a.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:r(),n.kMaxLength=i(),a.poolSize=8192,a._augment=function(e){return e.__proto__=a.prototype,e},a.from=function(e,t,n){return l(null,e,t,n)},a.TYPED_ARRAY_SUPPORT&&(a.prototype.__proto__=Uint8Array.prototype,a.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&a[Symbol.species]===a&&Object.defineProperty(a,Symbol.species,{value:null,configurable:!0})),a.alloc=function(e,t,n){return c(null,e,t,n)},a.allocUnsafe=function(e){return u(null,e)},a.allocUnsafeSlow=function(e){return u(null,e)},a.isBuffer=function(e){return!(null==e||!e._isBuffer)},a.compare=function(e,t){if(!a.isBuffer(e)||!a.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,r=t.length,i=0,o=Math.min(n,r);i<o;++i)if(e[i]!==t[i]){n=e[i],r=t[i];break}return n<r?-1:r<n?1:0},a.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(e,t){if(!Q(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return a.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var r=a.allocUnsafe(t),i=0;for(n=0;n<e.length;++n){var o=e[n];if(!a.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(r,i),i+=o.length}return r},a.byteLength=v,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var e=this.length;if(e%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)x(this,t,t+1);return this},a.prototype.swap32=function(){var e=this.length;if(e%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)x(this,t,t+3),x(this,t+1,t+2);return this},a.prototype.swap64=function(){var e=this.length;if(e%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)x(this,t,t+7),x(this,t+1,t+6),x(this,t+2,t+5),x(this,t+3,t+4);return this},a.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?N(this,0,e):y.apply(this,arguments)},a.prototype.equals=function(e){if(!a.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===a.compare(this,e)},a.prototype.inspect=function(){var e="",t=n.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,t).match(/.{2}/g).join(" "),this.length>t&&(e+=" ... ")),"<Buffer "+e+">"},a.prototype.compare=function(e,t,n,r,i){if(!a.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),t<0||n>e.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&t>=n)return 0;if(r>=i)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,r>>>=0,i>>>=0,this===e)return 0;for(var o=i-r,l=n-t,s=Math.min(o,l),c=this.slice(r,i),u=e.slice(t,n),f=0;f<s;++f)if(c[f]!==u[f]){o=c[f],l=u[f];break}return o<l?-1:l<o?1:0},a.prototype.includes=function(e,t,n){return this.indexOf(e,t,n)!==-1},a.prototype.indexOf=function(e,t,n){return b(this,e,t,n,!0)},a.prototype.lastIndexOf=function(e,t,n){return b(this,e,t,n,!1)},a.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t=0|t,isFinite(n)?(n=0|n,void 0===r&&(r="utf8")):(r=n,n=void 0)}var i=this.length-t;if((void 0===n||n>i)&&(n=i),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var o=!1;;)switch(r){case"hex":return k(this,e,t,n);case"utf8":case"utf-8":return S(this,e,t,n);case"ascii":return C(this,e,t,n);case"latin1":case"binary":return L(this,e,t,n);case"base64":return T(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return M(this,e,t,n);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var ee=4096;a.prototype.slice=function(e,t){var n=this.length;e=~~e,t=void 0===t?n:~~t,e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),t<e&&(t=e);var r;if(a.TYPED_ARRAY_SUPPORT)r=this.subarray(e,t),r.__proto__=a.prototype;else{var i=t-e;r=new a(i,(void 0));for(var o=0;o<i;++o)r[o]=this[o+e]}return r},a.prototype.readUIntLE=function(e,t,n){e=0|e,t=0|t,n||D(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return r},a.prototype.readUIntBE=function(e,t,n){e=0|e,t=0|t,n||D(e,t,this.length);for(var r=this[e+--t],i=1;t>0&&(i*=256);)r+=this[e+--t]*i;return r},a.prototype.readUInt8=function(e,t){return t||D(e,1,this.length),this[e]},a.prototype.readUInt16LE=function(e,t){return t||D(e,2,this.length),this[e]|this[e+1]<<8},a.prototype.readUInt16BE=function(e,t){return t||D(e,2,this.length),this[e]<<8|this[e+1]},a.prototype.readUInt32LE=function(e,t){return t||D(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},a.prototype.readUInt32BE=function(e,t){return t||D(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},a.prototype.readIntLE=function(e,t,n){e=0|e,t=0|t,n||D(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return i*=128,r>=i&&(r-=Math.pow(2,8*t)),r},a.prototype.readIntBE=function(e,t,n){e=0|e,t=0|t,n||D(e,t,this.length);for(var r=t,i=1,o=this[e+--r];r>0&&(i*=256);)o+=this[e+--r]*i;return i*=128,o>=i&&(o-=Math.pow(2,8*t)),o},a.prototype.readInt8=function(e,t){return t||D(e,1,this.length),128&this[e]?(255-this[e]+1)*-1:this[e]},a.prototype.readInt16LE=function(e,t){t||D(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},a.prototype.readInt16BE=function(e,t){t||D(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},a.prototype.readInt32LE=function(e,t){return t||D(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},a.prototype.readInt32BE=function(e,t){return t||D(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},a.prototype.readFloatLE=function(e,t){return t||D(e,4,this.length),J.read(this,e,!0,23,4)},a.prototype.readFloatBE=function(e,t){return t||D(e,4,this.length),J.read(this,e,!1,23,4)},a.prototype.readDoubleLE=function(e,t){return t||D(e,8,this.length),J.read(this,e,!0,52,8)},a.prototype.readDoubleBE=function(e,t){return t||D(e,8,this.length),J.read(this,e,!1,52,8)},a.prototype.writeUIntLE=function(e,t,n,r){if(e=+e,t=0|t,n=0|n,!r){var i=Math.pow(2,8*n)-1;H(this,e,t,n,i,0)}var o=1,a=0;for(this[t]=255&e;++a<n&&(o*=256);)this[t+a]=e/o&255;return t+n},a.prototype.writeUIntBE=function(e,t,n,r){if(e=+e,t=0|t,n=0|n,!r){var i=Math.pow(2,8*n)-1;H(this,e,t,n,i,0)}var o=n-1,a=1;for(this[t+o]=255&e;--o>=0&&(a*=256);)this[t+o]=e/a&255;return t+n},a.prototype.writeUInt8=function(e,t,n){return e=+e,t=0|t,n||H(this,e,t,1,255,0),a.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},a.prototype.writeUInt16LE=function(e,t,n){return e=+e,t=0|t,n||H(this,e,t,2,65535,0),a.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):W(this,e,t,!0),t+2},a.prototype.writeUInt16BE=function(e,t,n){return e=+e,t=0|t,n||H(this,e,t,2,65535,0),a.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):W(this,e,t,!1),t+2},a.prototype.writeUInt32LE=function(e,t,n){return e=+e,t=0|t,n||H(this,e,t,4,4294967295,0),a.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):B(this,e,t,!0),t+4},a.prototype.writeUInt32BE=function(e,t,n){return e=+e,t=0|t,n||H(this,e,t,4,4294967295,0),a.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):B(this,e,t,!1),t+4},a.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t=0|t,!r){var i=Math.pow(2,8*n-1);H(this,e,t,n,i-1,-i)}var o=0,a=1,l=0;for(this[t]=255&e;++o<n&&(a*=256);)e<0&&0===l&&0!==this[t+o-1]&&(l=1),this[t+o]=(e/a>>0)-l&255;return t+n},a.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t=0|t,!r){var i=Math.pow(2,8*n-1);H(this,e,t,n,i-1,-i)}var o=n-1,a=1,l=0;for(this[t+o]=255&e;--o>=0&&(a*=256);)e<0&&0===l&&0!==this[t+o+1]&&(l=1),this[t+o]=(e/a>>0)-l&255;return t+n},a.prototype.writeInt8=function(e,t,n){return e=+e,t=0|t,n||H(this,e,t,1,127,-128),a.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},a.prototype.writeInt16LE=function(e,t,n){return e=+e,t=0|t,n||H(this,e,t,2,32767,-32768),a.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):W(this,e,t,!0),t+2},a.prototype.writeInt16BE=function(e,t,n){return e=+e,t=0|t,n||H(this,e,t,2,32767,-32768),a.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):W(this,e,t,!1),t+2},a.prototype.writeInt32LE=function(e,t,n){return e=+e,t=0|t,n||H(this,e,t,4,2147483647,-2147483648),a.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):B(this,e,t,!0),t+4},a.prototype.writeInt32BE=function(e,t,n){return e=+e,t=0|t,n||H(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),a.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):B(this,e,t,!1),t+4},a.prototype.writeFloatLE=function(e,t,n){return F(this,e,t,!0,n)},a.prototype.writeFloatBE=function(e,t,n){return F(this,e,t,!1,n)},a.prototype.writeDoubleLE=function(e,t,n){return z(this,e,t,!0,n)},a.prototype.writeDoubleBE=function(e,t,n){return z(this,e,t,!1,n)},a.prototype.copy=function(e,t,n,r){if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);var i,o=r-n;if(this===e&&n<t&&t<r)for(i=o-1;i>=0;--i)e[i+t]=this[i+n];else if(o<1e3||!a.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+o),t);return o},a.prototype.fill=function(e,t,n,r){if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!a.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof e&&(e=255&e);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0);var o;if("number"==typeof e)for(o=t;o<n;++o)this[o]=e;else{var l=a.isBuffer(e)?e:G(new a(e,r).toString()),s=l.length;for(o=0;o<n-t;++o)this[o+t]=l[o%s]}return this};var te=/[^+\/0-9A-Za-z-_]/g}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"base64-js":1,ieee754:15,isarray:16}],4:[function(e,t,n){"use strict";function r(e){return e=e||{},"function"!=typeof e.codeMirrorInstance||"function"!=typeof e.codeMirrorInstance.defineMode?void console.log("CodeMirror Spell Checker: You must provide an instance of CodeMirror via the option `codeMirrorInstance`"):(String.prototype.includes||(String.prototype.includes=function(){return String.prototype.indexOf.apply(this,arguments)!==-1}),void e.codeMirrorInstance.defineMode("spell-checker",function(t){if(!r.aff_loading){r.aff_loading=!0;var n=new XMLHttpRequest;n.open("GET",window.config.webRoot+"js/codemirror/spell-checker/en_US.aff",!0),n.onload=function(){4===n.readyState&&200===n.status&&(r.aff_data=n.responseText,r.num_loaded++,2==r.num_loaded&&(r.typo=new i("en_US",r.aff_data,r.dic_data,{platform:"any"})))},n.send(null)}if(!r.dic_loading){r.dic_loading=!0;var o=new XMLHttpRequest;o.open("GET",window.config.webRoot+"js/codemirror/spell-checker/en_US.dic",!0),o.onload=function(){4===o.readyState&&200===o.status&&(r.dic_data=o.responseText,r.num_loaded++,2==r.num_loaded&&(r.typo=new i("en_US",r.aff_data,r.dic_data,{platform:"any"})))},o.send(null)}var a='!"#$%&()*+,-./:;<=>?@[\\]^_`{|}~ ',l={token:function(e){var t=e.peek(),n="";if(a.includes(t))return e.next(),null;for(;null!=(t=e.peek())&&!a.includes(t);)n+=t,e.next();return r.typo&&!r.typo.check(n)?"spell-error":null}},s=e.codeMirrorInstance.getMode(t,t.backdrop||"text/plain");return e.codeMirrorInstance.overlayMode(s,l,!0)}))}var i=e("typo-js");r.num_loaded=0,r.aff_loading=!1,r.dic_loading=!1,r.aff_data="",r.dic_data="",r.typo,t.exports=r},{"typo-js":18}],5:[function(t,n,r){!function(i){"object"==typeof r&&"object"==typeof n?i(t("../../lib/codemirror")):"function"==typeof e&&e.amd?e(["../../lib/codemirror"],i):i(CodeMirror)}(function(e){"use strict";function t(e){var t=e.getWrapperElement();e.state.fullScreenRestore={scrollTop:window.pageYOffset,scrollLeft:window.pageXOffset,width:t.style.width,height:t.style.height},t.style.width="",t.style.height="auto",t.className+=" CodeMirror-fullscreen",document.documentElement.style.overflow="hidden",e.refresh()}function n(e){var t=e.getWrapperElement();t.className=t.className.replace(/\s*CodeMirror-fullscreen\b/,""),document.documentElement.style.overflow="";var n=e.state.fullScreenRestore;t.style.width=n.width,t.style.height=n.height,window.scrollTo(n.scrollLeft,n.scrollTop),e.refresh()}e.defineOption("fullScreen",!1,function(r,i,o){o==e.Init&&(o=!1),!o!=!i&&(i?t(r):n(r))})})},{"../../lib/codemirror":10}],6:[function(t,n,r){!function(i){"object"==typeof r&&"object"==typeof n?i(t("../../lib/codemirror")):"function"==typeof e&&e.amd?e(["../../lib/codemirror"],i):i(CodeMirror)}(function(e){function t(e){e.state.placeholder&&(e.state.placeholder.parentNode.removeChild(e.state.placeholder),e.state.placeholder=null)}function n(e){t(e);var n=e.state.placeholder=document.createElement("pre");n.style.cssText="height: 0; overflow: visible",n.className="CodeMirror-placeholder";var r=e.getOption("placeholder");"string"==typeof r&&(r=document.createTextNode(r)),n.appendChild(r),e.display.lineSpace.insertBefore(n,e.display.lineSpace.firstChild)}function r(e){o(e)&&n(e)}function i(e){var r=e.getWrapperElement(),i=o(e);r.className=r.className.replace(" CodeMirror-empty","")+(i?" CodeMirror-empty":""),i?n(e):t(e)}function o(e){return 1===e.lineCount()&&""===e.getLine(0)}e.defineOption("placeholder","",function(n,o,a){var l=a&&a!=e.Init;if(o&&!l)n.on("blur",r),n.on("change",i),n.on("swapDoc",i),i(n);else if(!o&&l){n.off("blur",r),n.off("change",i),n.off("swapDoc",i),t(n);var s=n.getWrapperElement();s.className=s.className.replace(" CodeMirror-empty","")}o&&!n.hasFocus()&&r(n)})})},{"../../lib/codemirror":10}],7:[function(t,n,r){!function(i){"object"==typeof r&&"object"==typeof n?i(t("../../lib/codemirror")):"function"==typeof e&&e.amd?e(["../../lib/codemirror"],i):i(CodeMirror)}(function(e){"use strict";var t=/^(\s*)(>[> ]*|- \[[x ]\]\s|[*+-]\s|(\d+)([.)]))(\s*)/,n=/^(\s*)(>[> ]*|- \[[x ]\]|[*+-]|(\d+)[.)])(\s*)$/,r=/[*+-]\s/;e.commands.newlineAndIndentContinueMarkdownList=function(i){if(i.getOption("disableInput"))return e.Pass;for(var o=i.listSelections(),a=[],l=0;l<o.length;l++){var s=o[l].head,c=i.getStateAfter(s.line),u=c.list!==!1,f=0!==c.quote,h=i.getLine(s.line),d=t.exec(h);if(!o[l].empty()||!u&&!f||!d)return void i.execCommand("newlineAndIndent");if(n.test(h))i.replaceRange("",{line:s.line,ch:0},{line:s.line,ch:s.ch+1}),a[l]="\n";else{var p=d[1],m=d[5],g=r.test(d[2])||d[2].indexOf(">")>=0?d[2].replace("x"," "):parseInt(d[3],10)+1+d[4];a[l]="\n"+p+g+m}}i.replaceSelections(a)}})},{"../../lib/codemirror":10}],8:[function(t,n,r){!function(i){"object"==typeof r&&"object"==typeof n?i(t("../../lib/codemirror")):"function"==typeof e&&e.amd?e(["../../lib/codemirror"],i):i(CodeMirror)}(function(e){"use strict";e.overlayMode=function(t,n,r){return{startState:function(){return{base:e.startState(t),overlay:e.startState(n),basePos:0,baseCur:null,overlayPos:0,overlayCur:null,streamSeen:null}},copyState:function(r){return{base:e.copyState(t,r.base),overlay:e.copyState(n,r.overlay),basePos:r.basePos,baseCur:null,overlayPos:r.overlayPos,overlayCur:null}},token:function(e,i){return(e!=i.streamSeen||Math.min(i.basePos,i.overlayPos)<e.start)&&(i.streamSeen=e,i.basePos=i.overlayPos=e.start),e.start==i.basePos&&(i.baseCur=t.token(e,i.base),i.basePos=e.pos),e.start==i.overlayPos&&(e.pos=e.start,i.overlayCur=n.token(e,i.overlay),i.overlayPos=e.pos),e.pos=Math.min(i.basePos,i.overlayPos),null==i.overlayCur?i.baseCur:null!=i.baseCur&&i.overlay.combineTokens||r&&null==i.overlay.combineTokens?i.baseCur+" "+i.overlayCur:i.overlayCur},indent:t.indent&&function(e,n){return t.indent(e.base,n)},electricChars:t.electricChars,innerMode:function(e){return{state:e.base,mode:t}},blankLine:function(e){var i,o;return t.blankLine&&(i=t.blankLine(e.base)),n.blankLine&&(o=n.blankLine(e.overlay)),null==o?i:r&&null!=i?i+" "+o:o}}}})},{"../../lib/codemirror":10}],9:[function(t,n,r){!function(i){"object"==typeof r&&"object"==typeof n?i(t("../../lib/codemirror")):"function"==typeof e&&e.amd?e(["../../lib/codemirror"],i):i(CodeMirror)}(function(e){"use strict";function t(e){e.operation(function(){a(e)})}function n(e){e.state.markedSelection.length&&e.operation(function(){i(e)})}function r(e,t,n,r){if(0!=c(t,n))for(var i=e.state.markedSelection,o=e.state.markedSelectionStyle,a=t.line;;){var u=a==t.line?t:s(a,0),f=a+l,h=f>=n.line,d=h?n:s(f,0),p=e.markText(u,d,{className:o});if(null==r?i.push(p):i.splice(r++,0,p),h)break;a=f}}function i(e){for(var t=e.state.markedSelection,n=0;n<t.length;++n)t[n].clear();t.length=0}function o(e){i(e);for(var t=e.listSelections(),n=0;n<t.length;n++)r(e,t[n].from(),t[n].to())}function a(e){if(!e.somethingSelected())return i(e);if(e.listSelections().length>1)return o(e);var t=e.getCursor("start"),n=e.getCursor("end"),a=e.state.markedSelection;if(!a.length)return r(e,t,n);var s=a[0].find(),u=a[a.length-1].find();if(!s||!u||n.line-t.line<l||c(t,u.to)>=0||c(n,s.from)<=0)return o(e);for(;c(t,s.from)>0;)a.shift().clear(),s=a[0].find();for(c(t,s.from)<0&&(s.to.line-t.line<l?(a.shift().clear(),r(e,t,s.to,0)):r(e,t,s.from,0));c(n,u.to)<0;)a.pop().clear(),u=a[a.length-1].find();c(n,u.to)>0&&(n.line-u.from.line<l?(a.pop().clear(),r(e,u.from,n)):r(e,u.to,n))}e.defineOption("styleSelectedText",!1,function(r,a,l){var s=l&&l!=e.Init;a&&!s?(r.state.markedSelection=[],r.state.markedSelectionStyle="string"==typeof a?a:"CodeMirror-selectedtext",o(r),r.on("cursorActivity",t),r.on("change",n)):!a&&s&&(r.off("cursorActivity",t),r.off("change",n),i(r),r.state.markedSelection=r.state.markedSelectionStyle=null)});var l=8,s=e.Pos,c=e.cmpPos})},{"../../lib/codemirror":10}],10:[function(t,n,r){!function(t,i){"object"==typeof r&&"undefined"!=typeof n?n.exports=i():"function"==typeof e&&e.amd?e(i):t.CodeMirror=i()}(this,function(){"use strict";function e(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}function t(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function n(e,n){return t(e).appendChild(n)}function r(e,t,n,r){var i=document.createElement(e);if(n&&(i.className=n),r&&(i.style.cssText=r),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function i(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do if(11==t.nodeType&&(t=t.host),t==e)return!0;while(t=t.parentNode)}function o(){var e;try{e=document.activeElement}catch(t){e=document.body||null}for(;e&&e.root&&e.root.activeElement;)e=e.root.activeElement;return e}function a(t,n){var r=t.className;e(n).test(r)||(t.className+=(r?" ":"")+n)}function l(t,n){for(var r=t.split(" "),i=0;i<r.length;i++)r[i]&&!e(r[i]).test(n)&&(n+=" "+r[i]);return n}function s(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function c(e,t,n){t||(t={});for(var r in e)!e.hasOwnProperty(r)||n===!1&&t.hasOwnProperty(r)||(t[r]=e[r]);return t}function u(e,t,n,r,i){null==t&&(t=e.search(/[^\s\u00a0]/),t==-1&&(t=e.length));for(var o=r||0,a=i||0;;){var l=e.indexOf("\t",o);if(l<0||l>=t)return a+(t-o);a+=l-o,a+=n-a%n,o=l+1}}function f(){this.id=null}function h(e,t){for(var n=0;n<e.length;++n)if(e[n]==t)return n;return-1}function d(e,t,n){for(var r=0,i=0;;){var o=e.indexOf("\t",r);o==-1&&(o=e.length);var a=o-r;if(o==e.length||i+a>=t)return r+Math.min(a,t-i);if(i+=o-r,i+=n-i%n,r=o+1,i>=t)return r}}function p(e){for(;Na.length<=e;)Na.push(m(Na)+" ");return Na[e]}function m(e){return e[e.length-1]}function g(e,t){for(var n=[],r=0;r<e.length;r++)n[r]=t(e[r],r);return n}function v(e,t,n){for(var r=0,i=n(t);r<e.length&&n(e[r])<=i;)r++;e.splice(r,0,t)}function y(){}function x(e,t){
var n;return Object.create?n=Object.create(e):(y.prototype=e,n=new y),t&&c(t,n),n}function b(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||Ea.test(e))}function w(e,t){return t?!!(t.source.indexOf("\\w")>-1&&b(e))||t.test(e):b(e)}function k(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}function S(e){return e.charCodeAt(0)>=768&&Oa.test(e)}function C(e,t,n){var i=this;this.input=n,i.scrollbarFiller=r("div",null,"CodeMirror-scrollbar-filler"),i.scrollbarFiller.setAttribute("cm-not-content","true"),i.gutterFiller=r("div",null,"CodeMirror-gutter-filler"),i.gutterFiller.setAttribute("cm-not-content","true"),i.lineDiv=r("div",null,"CodeMirror-code"),i.selectionDiv=r("div",null,null,"position: relative; z-index: 1"),i.cursorDiv=r("div",null,"CodeMirror-cursors"),i.measure=r("div",null,"CodeMirror-measure"),i.lineMeasure=r("div",null,"CodeMirror-measure"),i.lineSpace=r("div",[i.measure,i.lineMeasure,i.selectionDiv,i.cursorDiv,i.lineDiv],null,"position: relative; outline: none"),i.mover=r("div",[r("div",[i.lineSpace],"CodeMirror-lines")],null,"position: relative"),i.sizer=r("div",[i.mover],"CodeMirror-sizer"),i.sizerWidth=null,i.heightForcer=r("div",null,null,"position: absolute; height: "+Ca+"px; width: 1px;"),i.gutters=r("div",null,"CodeMirror-gutters"),i.lineGutter=null,i.scroller=r("div",[i.sizer,i.heightForcer,i.gutters],"CodeMirror-scroll"),i.scroller.setAttribute("tabIndex","-1"),i.wrapper=r("div",[i.scrollbarFiller,i.gutterFiller,i.scroller],"CodeMirror"),na&&ra<8&&(i.gutters.style.zIndex=-1,i.scroller.style.paddingRight=0),ia||Qo&&ha||(i.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(i.wrapper):e(i.wrapper)),i.viewFrom=i.viewTo=t.first,i.reportedViewFrom=i.reportedViewTo=t.first,i.view=[],i.renderedView=null,i.externalMeasured=null,i.viewOffset=0,i.lastWrapHeight=i.lastWrapWidth=0,i.updateLineNumbers=null,i.nativeBarWidth=i.barHeight=i.barWidth=0,i.scrollbarsClipped=!1,i.lineNumWidth=i.lineNumInnerWidth=i.lineNumChars=null,i.alignWidgets=!1,i.cachedCharWidth=i.cachedTextHeight=i.cachedPaddingH=null,i.maxLine=null,i.maxLineLength=0,i.maxLineChanged=!1,i.wheelDX=i.wheelDY=i.wheelStartX=i.wheelStartY=null,i.shift=!1,i.selForContextMenu=null,i.activeTouch=null,n.init(i)}function L(e,t){if(t-=e.first,t<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var n=e;!n.lines;)for(var r=0;;++r){var i=n.children[r],o=i.chunkSize();if(t<o){n=i;break}t-=o}return n.lines[t]}function T(e,t,n){var r=[],i=t.line;return e.iter(t.line,n.line+1,function(e){var o=e.text;i==n.line&&(o=o.slice(0,n.ch)),i==t.line&&(o=o.slice(t.ch)),r.push(o),++i}),r}function M(e,t,n){var r=[];return e.iter(t,n,function(e){r.push(e.text)}),r}function A(e,t){var n=t-e.height;if(n)for(var r=e;r;r=r.parent)r.height+=n}function N(e){if(null==e.parent)return null;for(var t=e.parent,n=h(t.lines,e),r=t.parent;r;t=r,r=r.parent)for(var i=0;r.children[i]!=t;++i)n+=r.children[i].chunkSize();return n+t.first}function E(e,t){var n=e.first;e:do{for(var r=0;r<e.children.length;++r){var i=e.children[r],o=i.height;if(t<o){e=i;continue e}t-=o,n+=i.chunkSize()}return n}while(!e.lines);for(var a=0;a<e.lines.length;++a){var l=e.lines[a],s=l.height;if(t<s)break;t-=s}return n+a}function O(e,t){return t>=e.first&&t<e.first+e.size}function I(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function P(e,t){return this instanceof P?(this.line=e,void(this.ch=t)):new P(e,t)}function R(e,t){return e.line-t.line||e.ch-t.ch}function D(e){return P(e.line,e.ch)}function H(e,t){return R(e,t)<0?t:e}function W(e,t){return R(e,t)<0?e:t}function B(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function _(e,t){if(t.line<e.first)return P(e.first,0);var n=e.first+e.size-1;return t.line>n?P(n,L(e,n).text.length):F(t,L(e,t.line).text.length)}function F(e,t){var n=e.ch;return null==n||n>t?P(e.line,t):n<0?P(e.line,0):e}function z(e,t){for(var n=[],r=0;r<t.length;r++)n[r]=_(e,t[r]);return n}function j(){Ia=!0}function U(){Pa=!0}function q(e,t,n){this.marker=e,this.from=t,this.to=n}function G(e,t){if(e)for(var n=0;n<e.length;++n){var r=e[n];if(r.marker==t)return r}}function Y(e,t){for(var n,r=0;r<e.length;++r)e[r]!=t&&(n||(n=[])).push(e[r]);return n}function $(e,t){e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],t.marker.attachLine(e)}function V(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker,l=null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t);if(l||o.from==t&&"bookmark"==a.type&&(!n||!o.marker.insertLeft)){var s=null==o.to||(a.inclusiveRight?o.to>=t:o.to>t);(r||(r=[])).push(new q(a,o.from,s?null:o.to))}}return r}function K(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o=e[i],a=o.marker,l=null==o.to||(a.inclusiveRight?o.to>=t:o.to>t);if(l||o.from==t&&"bookmark"==a.type&&(!n||o.marker.insertLeft)){var s=null==o.from||(a.inclusiveLeft?o.from<=t:o.from<t);(r||(r=[])).push(new q(a,s?null:o.from-t,null==o.to?null:o.to-t))}}return r}function X(e,t){if(t.full)return null;var n=O(e,t.from.line)&&L(e,t.from.line).markedSpans,r=O(e,t.to.line)&&L(e,t.to.line).markedSpans;if(!n&&!r)return null;var i=t.from.ch,o=t.to.ch,a=0==R(t.from,t.to),l=V(n,i,a),s=K(r,o,a),c=1==t.text.length,u=m(t.text).length+(c?i:0);if(l)for(var f=0;f<l.length;++f){var h=l[f];if(null==h.to){var d=G(s,h.marker);d?c&&(h.to=null==d.to?null:d.to+u):h.to=i}}if(s)for(var p=0;p<s.length;++p){var g=s[p];if(null!=g.to&&(g.to+=u),null==g.from){var v=G(l,g.marker);v||(g.from=u,c&&(l||(l=[])).push(g))}else g.from+=u,c&&(l||(l=[])).push(g)}l&&(l=Z(l)),s&&s!=l&&(s=Z(s));var y=[l];if(!c){var x,b=t.text.length-2;if(b>0&&l)for(var w=0;w<l.length;++w)null==l[w].to&&(x||(x=[])).push(new q(l[w].marker,null,null));for(var k=0;k<b;++k)y.push(x);y.push(s)}return y}function Z(e){for(var t=0;t<e.length;++t){var n=e[t];null!=n.from&&n.from==n.to&&n.marker.clearWhenEmpty!==!1&&e.splice(t--,1)}return e.length?e:null}function J(e,t,n){var r=null;if(e.iter(t.line,n.line+1,function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var n=e.markedSpans[t].marker;!n.readOnly||r&&h(r,n)!=-1||(r||(r=[])).push(n)}}),!r)return null;for(var i=[{from:t,to:n}],o=0;o<r.length;++o)for(var a=r[o],l=a.find(0),s=0;s<i.length;++s){var c=i[s];if(!(R(c.to,l.from)<0||R(c.from,l.to)>0)){var u=[s,1],f=R(c.from,l.from),d=R(c.to,l.to);(f<0||!a.inclusiveLeft&&!f)&&u.push({from:c.from,to:l.from}),(d>0||!a.inclusiveRight&&!d)&&u.push({from:l.to,to:c.to}),i.splice.apply(i,u),s+=u.length-1}}return i}function Q(e){var t=e.markedSpans;if(t){for(var n=0;n<t.length;++n)t[n].marker.detachLine(e);e.markedSpans=null}}function ee(e,t){if(t){for(var n=0;n<t.length;++n)t[n].marker.attachLine(e);e.markedSpans=t}}function te(e){return e.inclusiveLeft?-1:0}function ne(e){return e.inclusiveRight?1:0}function re(e,t){var n=e.lines.length-t.lines.length;if(0!=n)return n;var r=e.find(),i=t.find(),o=R(r.from,i.from)||te(e)-te(t);if(o)return-o;var a=R(r.to,i.to)||ne(e)-ne(t);return a?a:t.id-e.id}function ie(e,t){var n,r=Pa&&e.markedSpans;if(r)for(var i=void 0,o=0;o<r.length;++o)i=r[o],i.marker.collapsed&&null==(t?i.from:i.to)&&(!n||re(n,i.marker)<0)&&(n=i.marker);return n}function oe(e){return ie(e,!0)}function ae(e){return ie(e,!1)}function le(e,t,n,r,i){var o=L(e,t),a=Pa&&o.markedSpans;if(a)for(var l=0;l<a.length;++l){var s=a[l];if(s.marker.collapsed){var c=s.marker.find(0),u=R(c.from,n)||te(s.marker)-te(i),f=R(c.to,r)||ne(s.marker)-ne(i);if(!(u>=0&&f<=0||u<=0&&f>=0)&&(u<=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?R(c.to,n)>=0:R(c.to,n)>0)||u>=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?R(c.from,r)<=0:R(c.from,r)<0)))return!0}}}function se(e){for(var t;t=oe(e);)e=t.find(-1,!0).line;return e}function ce(e){for(var t,n;t=ae(e);)e=t.find(1,!0).line,(n||(n=[])).push(e);return n}function ue(e,t){var n=L(e,t),r=se(n);return n==r?t:N(r)}function fe(e,t){if(t>e.lastLine())return t;var n,r=L(e,t);if(!he(e,r))return t;for(;n=ae(r);)r=n.find(1,!0).line;return N(r)+1}function he(e,t){var n=Pa&&t.markedSpans;if(n)for(var r=void 0,i=0;i<n.length;++i)if(r=n[i],r.marker.collapsed){if(null==r.from)return!0;if(!r.marker.widgetNode&&0==r.from&&r.marker.inclusiveLeft&&de(e,t,r))return!0}}function de(e,t,n){if(null==n.to){var r=n.marker.find(1,!0);return de(e,r.line,G(r.line.markedSpans,n.marker))}if(n.marker.inclusiveRight&&n.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if(i=t.markedSpans[o],i.marker.collapsed&&!i.marker.widgetNode&&i.from==n.to&&(null==i.to||i.to!=n.from)&&(i.marker.inclusiveLeft||n.marker.inclusiveRight)&&de(e,t,i))return!0}function pe(e){e=se(e);for(var t=0,n=e.parent,r=0;r<n.lines.length;++r){var i=n.lines[r];if(i==e)break;t+=i.height}for(var o=n.parent;o;n=o,o=n.parent)for(var a=0;a<o.children.length;++a){var l=o.children[a];if(l==n)break;t+=l.height}return t}function me(e){if(0==e.height)return 0;for(var t,n=e.text.length,r=e;t=oe(r);){var i=t.find(0,!0);r=i.from.line,n+=i.from.ch-i.to.ch}for(r=e;t=ae(r);){var o=t.find(0,!0);n-=r.text.length-o.from.ch,r=o.to.line,n+=r.text.length-o.to.ch}return n}function ge(e){var t=e.display,n=e.doc;t.maxLine=L(n,n.first),t.maxLineLength=me(t.maxLine),t.maxLineChanged=!0,n.iter(function(e){var n=me(e);n>t.maxLineLength&&(t.maxLineLength=n,t.maxLine=e)})}function ve(e,t,n,r){if(!e)return r(t,n,"ltr");for(var i=!1,o=0;o<e.length;++o){var a=e[o];(a.from<n&&a.to>t||t==n&&a.to==t)&&(r(Math.max(a.from,t),Math.min(a.to,n),1==a.level?"rtl":"ltr"),i=!0)}i||r(t,n,"ltr")}function ye(e){return e.level%2?e.to:e.from}function xe(e){return e.level%2?e.from:e.to}function be(e){var t=Me(e);return t?ye(t[0]):0}function we(e){var t=Me(e);return t?xe(m(t)):e.text.length}function ke(e,t,n){var r=e[0].level;return t==r||n!=r&&t<n}function Se(e,t){var n;Ra=null;for(var r=0;r<e.length;++r){var i=e[r];if(i.from<t&&i.to>t)return r;if(i.from==t||i.to==t){if(null!=n)return ke(e,i.level,e[n].level)?(i.from!=i.to&&(Ra=n),r):(i.from!=i.to&&(Ra=r),n);n=r}}return n}function Ce(e,t,n,r){if(!r)return t+n;do t+=n;while(t>0&&S(e.text.charAt(t)));return t}function Le(e,t,n,r){var i=Me(e);if(!i)return Te(e,t,n,r);for(var o=Se(i,t),a=i[o],l=Ce(e,t,a.level%2?-n:n,r);;){if(l>a.from&&l<a.to)return l;if(l==a.from||l==a.to)return Se(i,l)==o?l:(a=i[o+=n],n>0==a.level%2?a.to:a.from);if(a=i[o+=n],!a)return null;l=n>0==a.level%2?Ce(e,a.to,-1,r):Ce(e,a.from,1,r)}}function Te(e,t,n,r){var i=t+n;if(r)for(;i>0&&S(e.text.charAt(i));)i+=n;return i<0||i>e.text.length?null:i}function Me(e){var t=e.order;return null==t&&(t=e.order=Da(e.text)),t}function Ae(e,t){return e._handlers&&e._handlers[t]||Ha}function Ne(e,t,n){if(e.removeEventListener)e.removeEventListener(t,n,!1);else if(e.detachEvent)e.detachEvent("on"+t,n);else{var r=e._handlers,i=r&&r[t];if(i){var o=h(i,n);o>-1&&(r[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function Ee(e,t){var n=Ae(e,t);if(n.length)for(var r=Array.prototype.slice.call(arguments,2),i=0;i<n.length;++i)n[i].apply(null,r)}function Oe(e,t,n){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),Ee(e,n||t.type,e,t),We(t)||t.codemirrorIgnore}function Ie(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var n=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),r=0;r<t.length;++r)h(n,t[r])==-1&&n.push(t[r])}function Pe(e,t){return Ae(e,t).length>0}function Re(e){e.prototype.on=function(e,t){Wa(this,e,t)},e.prototype.off=function(e,t){Ne(this,e,t)}}function De(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function He(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function We(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function Be(e){De(e),He(e)}function _e(e){return e.target||e.srcElement}function Fe(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),da&&e.ctrlKey&&1==t&&(t=3),t}function ze(e){if(null==ka){var t=r("span","​");n(e,r("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(ka=t.offsetWidth<=1&&t.offsetHeight>2&&!(na&&ra<8))}var i=ka?r("span","​"):r("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return i.setAttribute("cm-text",""),i}function je(e){if(null!=Sa)return Sa;var r=n(e,document.createTextNode("AخA")),i=va(r,0,1).getBoundingClientRect(),o=va(r,1,2).getBoundingClientRect();return t(e),!(!i||i.left==i.right)&&(Sa=o.right-i.right<3)}function Ue(e){if(null!=ja)return ja;var t=n(e,r("span","x")),i=t.getBoundingClientRect(),o=va(t,0,1).getBoundingClientRect();return ja=Math.abs(i.left-o.left)>1}function qe(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Ua[e]=t}function Ge(e,t){qa[e]=t}function Ye(e){if("string"==typeof e&&qa.hasOwnProperty(e))e=qa[e];else if(e&&"string"==typeof e.name&&qa.hasOwnProperty(e.name)){var t=qa[e.name];"string"==typeof t&&(t={name:t}),e=x(t,e),e.name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Ye("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Ye("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function $e(e,t){t=Ye(t);var n=Ua[t.name];if(!n)return $e(e,"text/plain");var r=n(e,t);if(Ga.hasOwnProperty(t.name)){var i=Ga[t.name];for(var o in i)i.hasOwnProperty(o)&&(r.hasOwnProperty(o)&&(r["_"+o]=r[o]),r[o]=i[o])}if(r.name=t.name,t.helperType&&(r.helperType=t.helperType),t.modeProps)for(var a in t.modeProps)r[a]=t.modeProps[a];return r}function Ve(e,t){var n=Ga.hasOwnProperty(e)?Ga[e]:Ga[e]={};c(t,n)}function Ke(e,t){if(t===!0)return t;if(e.copyState)return e.copyState(t);var n={};for(var r in t){var i=t[r];i instanceof Array&&(i=i.concat([])),n[r]=i}return n}function Xe(e,t){for(var n;e.innerMode&&(n=e.innerMode(t),n&&n.mode!=e);)t=n.state,e=n.mode;return n||{mode:e,state:t}}function Ze(e,t,n){return!e.startState||e.startState(t,n)}function Je(e,t,n,r){var i=[e.state.modeGen],o={};at(e,t.text,e.doc.mode,n,function(e,t){return i.push(e,t)},o,r);for(var a=function(n){var r=e.state.overlays[n],a=1,l=0;at(e,t.text,r.mode,!0,function(e,t){for(var n=a;l<e;){var o=i[a];o>e&&i.splice(a,1,e,i[a+1],o),a+=2,l=Math.min(e,o)}if(t)if(r.opaque)i.splice(n,a-n,e,"overlay "+t),a=n+2;else for(;n<a;n+=2){var s=i[n+1];i[n+1]=(s?s+" ":"")+"overlay "+t}},o)},l=0;l<e.state.overlays.length;++l)a(l);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function Qe(e,t,n){if(!t.styles||t.styles[0]!=e.state.modeGen){var r=et(e,N(t)),i=Je(e,t,t.text.length>e.options.maxHighlightLength?Ke(e.doc.mode,r):r);t.stateAfter=r,t.styles=i.styles,i.classes?t.styleClasses=i.classes:t.styleClasses&&(t.styleClasses=null),n===e.doc.frontier&&e.doc.frontier++}return t.styles}function et(e,t,n){var r=e.doc,i=e.display;if(!r.mode.startState)return!0;var o=lt(e,t,n),a=o>r.first&&L(r,o-1).stateAfter;return a=a?Ke(r.mode,a):Ze(r.mode),r.iter(o,t,function(n){tt(e,n.text,a);var l=o==t-1||o%5==0||o>=i.viewFrom&&o<i.viewTo;n.stateAfter=l?Ke(r.mode,a):null,++o}),n&&(r.frontier=o),a}function tt(e,t,n,r){var i=e.doc.mode,o=new Ya(t,e.options.tabSize);for(o.start=o.pos=r||0,""==t&&nt(i,n);!o.eol();)rt(i,o,n),o.start=o.pos}function nt(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var n=Xe(e,t);return n.mode.blankLine?n.mode.blankLine(n.state):void 0}}function rt(e,t,n,r){for(var i=0;i<10;i++){r&&(r[0]=Xe(e,n).mode);var o=e.token(t,n);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}function it(e,t,n,r){var i,o=function(e){return{start:f.start,end:f.pos,string:f.current(),type:i||null,state:e?Ke(a.mode,u):u}},a=e.doc,l=a.mode;t=_(a,t);var s,c=L(a,t.line),u=et(e,t.line,n),f=new Ya(c.text,e.options.tabSize);for(r&&(s=[]);(r||f.pos<t.ch)&&!f.eol();)f.start=f.pos,i=rt(l,f,u),r&&s.push(o(!0));return r?s:o()}function ot(e,t){if(e)for(;;){var n=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!n)break;e=e.slice(0,n.index)+e.slice(n.index+n[0].length);var r=n[1]?"bgClass":"textClass";null==t[r]?t[r]=n[2]:new RegExp("(?:^|s)"+n[2]+"(?:$|s)").test(t[r])||(t[r]+=" "+n[2])}return e}function at(e,t,n,r,i,o,a){var l=n.flattenSpans;null==l&&(l=e.options.flattenSpans);var s,c=0,u=null,f=new Ya(t,e.options.tabSize),h=e.options.addModeClass&&[null];for(""==t&&ot(nt(n,r),o);!f.eol();){if(f.pos>e.options.maxHighlightLength?(l=!1,a&&tt(e,t,r,f.pos),f.pos=t.length,s=null):s=ot(rt(n,f,r,h),o),h){var d=h[0].name;d&&(s="m-"+(s?d+" "+s:d))}if(!l||u!=s){for(;c<f.start;)c=Math.min(f.start,c+5e3),i(c,u);u=s}f.start=f.pos}for(;c<f.pos;){var p=Math.min(f.pos,c+5e3);i(p,u),c=p}}function lt(e,t,n){for(var r,i,o=e.doc,a=n?-1:t-(e.doc.mode.innerMode?1e3:100),l=t;l>a;--l){if(l<=o.first)return o.first;var s=L(o,l-1);if(s.stateAfter&&(!n||l<=o.frontier))return l;var c=u(s.text,null,e.options.tabSize);(null==i||r>c)&&(i=l-1,r=c)}return i}function st(e,t,n){this.text=e,ee(this,t),this.height=n?n(this):1}function ct(e,t,n,r){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),Q(e),ee(e,n);var i=r?r(e):1;i!=e.height&&A(e,i)}function ut(e){e.parent=null,Q(e)}function ft(e,t){if(!e||/^\s*$/.test(e))return null;var n=t.addModeClass?Ka:Va;return n[e]||(n[e]=e.replace(/\S+/g,"cm-$&"))}function ht(e,t){var n=r("span",null,null,ia?"padding-right: .1px":null),i={pre:r("pre",[n],"CodeMirror-line"),content:n,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:(na||ia)&&e.getOption("lineWrapping")};t.measure={};for(var o=0;o<=(t.rest?t.rest.length:0);o++){var a=o?t.rest[o-1]:t.line,s=void 0;i.pos=0,i.addToken=pt,je(e.display.measure)&&(s=Me(a))&&(i.addToken=gt(i.addToken,s)),i.map=[];var c=t!=e.display.externalMeasured&&N(a);yt(a,i,Qe(e,a,c)),a.styleClasses&&(a.styleClasses.bgClass&&(i.bgClass=l(a.styleClasses.bgClass,i.bgClass||"")),a.styleClasses.textClass&&(i.textClass=l(a.styleClasses.textClass,i.textClass||""))),0==i.map.length&&i.map.push(0,0,i.content.appendChild(ze(e.display.measure))),0==o?(t.measure.map=i.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(i.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(ia){var u=i.content.lastChild;(/\bcm-tab\b/.test(u.className)||u.querySelector&&u.querySelector(".cm-tab"))&&(i.content.className="cm-tab-wrap-hack")}return Ee(e,"renderLine",e,t.line,i.pre),i.pre.className&&(i.textClass=l(i.pre.className,i.textClass||"")),i}function dt(e){var t=r("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function pt(e,t,n,i,o,a,l){if(t){var s,c=e.splitSpaces?mt(t,e.trailingSpace):t,u=e.cm.state.specialChars,f=!1;if(u.test(t)){s=document.createDocumentFragment();for(var h=0;;){u.lastIndex=h;var d=u.exec(t),m=d?d.index-h:t.length-h;if(m){var g=document.createTextNode(c.slice(h,h+m));na&&ra<9?s.appendChild(r("span",[g])):s.appendChild(g),e.map.push(e.pos,e.pos+m,g),e.col+=m,e.pos+=m}if(!d)break;h+=m+1;var v=void 0;if("\t"==d[0]){var y=e.cm.options.tabSize,x=y-e.col%y;v=s.appendChild(r("span",p(x),"cm-tab")),v.setAttribute("role","presentation"),v.setAttribute("cm-text","\t"),e.col+=x}else"\r"==d[0]||"\n"==d[0]?(v=s.appendChild(r("span","\r"==d[0]?"␍":"␤","cm-invalidchar")),v.setAttribute("cm-text",d[0]),e.col+=1):(v=e.cm.options.specialCharPlaceholder(d[0]),v.setAttribute("cm-text",d[0]),na&&ra<9?s.appendChild(r("span",[v])):s.appendChild(v),e.col+=1);e.map.push(e.pos,e.pos+1,v),e.pos++}}else e.col+=t.length,s=document.createTextNode(c),e.map.push(e.pos,e.pos+t.length,s),na&&ra<9&&(f=!0),e.pos+=t.length;if(e.trailingSpace=32==c.charCodeAt(t.length-1),n||i||o||f||l){var b=n||"";i&&(b+=i),o&&(b+=o);var w=r("span",[s],b,l);return a&&(w.title=a),e.content.appendChild(w)}e.content.appendChild(s)}}function mt(e,t){if(e.length>1&&!/  /.test(e))return e;for(var n=t,r="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!n||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),r+=o,n=" "==o}return r}function gt(e,t){return function(n,r,i,o,a,l,s){i=i?i+" cm-force-border":"cm-force-border";for(var c=n.pos,u=c+r.length;;){for(var f=void 0,h=0;h<t.length&&(f=t[h],!(f.to>c&&f.from<=c));h++);if(f.to>=u)return e(n,r,i,o,a,l,s);e(n,r.slice(0,f.to-c),i,o,null,l,s),o=null,r=r.slice(f.to-c),c=f.to}}}function vt(e,t,n,r){var i=!r&&n.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!r&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",n.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function yt(e,t,n){var r=e.markedSpans,i=e.text,o=0;if(r)for(var a,l,s,c,u,f,h,d=i.length,p=0,m=1,g="",v=0;;){if(v==p){s=c=u=f=l="",h=null,v=1/0;for(var y=[],x=void 0,b=0;b<r.length;++b){var w=r[b],k=w.marker;"bookmark"==k.type&&w.from==p&&k.widgetNode?y.push(k):w.from<=p&&(null==w.to||w.to>p||k.collapsed&&w.to==p&&w.from==p)?(null!=w.to&&w.to!=p&&v>w.to&&(v=w.to,c=""),k.className&&(s+=" "+k.className),k.css&&(l=(l?l+";":"")+k.css),k.startStyle&&w.from==p&&(u+=" "+k.startStyle),k.endStyle&&w.to==v&&(x||(x=[])).push(k.endStyle,w.to),k.title&&!f&&(f=k.title),k.collapsed&&(!h||re(h.marker,k)<0)&&(h=w)):w.from>p&&v>w.from&&(v=w.from)}if(x)for(var S=0;S<x.length;S+=2)x[S+1]==v&&(c+=" "+x[S]);if(!h||h.from==p)for(var C=0;C<y.length;++C)vt(t,0,y[C]);if(h&&(h.from||0)==p){if(vt(t,(null==h.to?d+1:h.to)-p,h.marker,null==h.from),null==h.to)return;h.to==p&&(h=!1)}}if(p>=d)break;for(var L=Math.min(d,v);;){if(g){var T=p+g.length;if(!h){var M=T>L?g.slice(0,L-p):g;t.addToken(t,M,a?a+s:s,u,p+M.length==v?c:"",f,l)}if(T>=L){g=g.slice(L-p),p=L;break}p=T,u=""}g=i.slice(o,o=n[m++]),a=ft(n[m++],t.cm.options)}}else for(var A=1;A<n.length;A+=2)t.addToken(t,i.slice(o,o=n[A]),ft(n[A+1],t.cm.options))}function xt(e,t,n){this.line=t,this.rest=ce(t),this.size=this.rest?N(m(this.rest))-n+1:1,this.node=this.text=null,this.hidden=he(e,t)}function bt(e,t,n){for(var r,i=[],o=t;o<n;o=r){var a=new xt(e.doc,L(e.doc,o),o);r=o+a.size,i.push(a)}return i}function wt(e){Xa?Xa.ops.push(e):e.ownsGroup=Xa={ops:[e],delayedCallbacks:[]}}function kt(e){var t=e.delayedCallbacks,n=0;do{for(;n<t.length;n++)t[n].call(null);for(var r=0;r<e.ops.length;r++){var i=e.ops[r];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(n<t.length)}function St(e,t){var n=e.ownsGroup;if(n)try{kt(n)}finally{Xa=null,t(n)}}function Ct(e,t){var n=Ae(e,t);if(n.length){var r,i=Array.prototype.slice.call(arguments,2);Xa?r=Xa.delayedCallbacks:Za?r=Za:(r=Za=[],setTimeout(Lt,0));for(var o=function(e){r.push(function(){return n[e].apply(null,i)})},a=0;a<n.length;++a)o(a)}}function Lt(){var e=Za;Za=null;for(var t=0;t<e.length;++t)e[t]()}function Tt(e,t,n,r){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?Et(e,t):"gutter"==o?It(e,t,n,r):"class"==o?Ot(t):"widget"==o&&Pt(e,t,r)}t.changes=null}function Mt(e){return e.node==e.text&&(e.node=r("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),na&&ra<8&&(e.node.style.zIndex=2)),e.node}function At(e){var t=e.bgClass?e.bgClass+" "+(e.line.bgClass||""):e.line.bgClass;if(t&&(t+=" CodeMirror-linebackground"),e.background)t?e.background.className=t:(e.background.parentNode.removeChild(e.background),e.background=null);else if(t){var n=Mt(e);e.background=n.insertBefore(r("div",null,t),n.firstChild)}}function Nt(e,t){var n=e.display.externalMeasured;return n&&n.line==t.line?(e.display.externalMeasured=null,t.measure=n.measure,n.built):ht(e,t)}function Et(e,t){var n=t.text.className,r=Nt(e,t);t.text==t.node&&(t.node=r.pre),t.text.parentNode.replaceChild(r.pre,t.text),t.text=r.pre,r.bgClass!=t.bgClass||r.textClass!=t.textClass?(t.bgClass=r.bgClass,t.textClass=r.textClass,Ot(t)):n&&(t.text.className=n)}function Ot(e){At(e),e.line.wrapClass?Mt(e).className=e.line.wrapClass:e.node!=e.text&&(e.node.className="");var t=e.textClass?e.textClass+" "+(e.line.textClass||""):e.line.textClass;e.text.className=t||""}function It(e,t,n,i){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var o=Mt(t);t.gutterBackground=r("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?i.fixedPos:-i.gutterTotalWidth)+"px; width: "+i.gutterTotalWidth+"px"),o.insertBefore(t.gutterBackground,t.text)}var a=t.line.gutterMarkers;if(e.options.lineNumbers||a){var l=Mt(t),s=t.gutter=r("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?i.fixedPos:-i.gutterTotalWidth)+"px");if(e.display.input.setUneditable(s),l.insertBefore(s,t.text),t.line.gutterClass&&(s.className+=" "+t.line.gutterClass),!e.options.lineNumbers||a&&a["CodeMirror-linenumbers"]||(t.lineNumber=s.appendChild(r("div",I(e.options,n),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+i.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),a)for(var c=0;c<e.options.gutters.length;++c){var u=e.options.gutters[c],f=a.hasOwnProperty(u)&&a[u];f&&s.appendChild(r("div",[f],"CodeMirror-gutter-elt","left: "+i.gutterLeft[u]+"px; width: "+i.gutterWidth[u]+"px"))}}}function Pt(e,t,n){t.alignable&&(t.alignable=null);for(var r=t.node.firstChild,i=void 0;r;r=i)i=r.nextSibling,"CodeMirror-linewidget"==r.className&&t.node.removeChild(r);Dt(e,t,n)}function Rt(e,t,n,r){var i=Nt(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),Ot(t),It(e,t,n,r),Dt(e,t,r),t.node}function Dt(e,t,n){if(Ht(e,t.line,t,n,!0),t.rest)for(var r=0;r<t.rest.length;r++)Ht(e,t.rest[r],t,n,!1)}function Ht(e,t,n,i,o){if(t.widgets)for(var a=Mt(n),l=0,s=t.widgets;l<s.length;++l){var c=s[l],u=r("div",[c.node],"CodeMirror-linewidget");c.handleMouseEvents||u.setAttribute("cm-ignore-events","true"),Wt(c,u,n,i),e.display.input.setUneditable(u),o&&c.above?a.insertBefore(u,n.gutter||n.text):a.appendChild(u),Ct(c,"redraw")}}function Wt(e,t,n,r){if(e.noHScroll){(n.alignable||(n.alignable=[])).push(t);var i=r.wrapperWidth;t.style.left=r.fixedPos+"px",e.coverGutter||(i-=r.gutterTotalWidth,t.style.paddingLeft=r.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-r.gutterTotalWidth+"px"))}function Bt(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!i(document.body,e.node)){var o="position: relative;";e.coverGutter&&(o+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(o+="width: "+t.display.wrapper.clientWidth+"px;"),n(t.display.measure,r("div",[e.node],null,o))}return e.height=e.node.parentNode.offsetHeight}function _t(e,t){for(var n=_e(t);n!=e.wrapper;n=n.parentNode)if(!n||1==n.nodeType&&"true"==n.getAttribute("cm-ignore-events")||n.parentNode==e.sizer&&n!=e.mover)return!0}function Ft(e){return e.lineSpace.offsetTop}function zt(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function jt(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=n(e.measure,r("pre","x")),i=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,o={left:parseInt(i.paddingLeft),right:parseInt(i.paddingRight)};return isNaN(o.left)||isNaN(o.right)||(e.cachedPaddingH=o),o}function Ut(e){return Ca-e.display.nativeBarWidth}function qt(e){return e.display.scroller.clientWidth-Ut(e)-e.display.barWidth}function Gt(e){return e.display.scroller.clientHeight-Ut(e)-e.display.barHeight}function Yt(e,t,n){var r=e.options.lineWrapping,i=r&&qt(e);if(!t.measure.heights||r&&t.measure.width!=i){var o=t.measure.heights=[];if(r){t.measure.width=i;for(var a=t.text.firstChild.getClientRects(),l=0;l<a.length-1;l++){var s=a[l],c=a[l+1];Math.abs(s.bottom-c.bottom)>2&&o.push((s.bottom+c.top)/2-n.top)}}o.push(n.bottom-n.top)}}function $t(e,t,n){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};for(var r=0;r<e.rest.length;r++)if(e.rest[r]==t)return{map:e.measure.maps[r],cache:e.measure.caches[r]};for(var i=0;i<e.rest.length;i++)if(N(e.rest[i])>n)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}function Vt(e,t){t=se(t);var r=N(t),i=e.display.externalMeasured=new xt(e.doc,t,r);i.lineN=r;var o=i.built=ht(e,i);return i.text=o.pre,n(e.display.lineMeasure,o.pre),i}function Kt(e,t,n,r){return Jt(e,Zt(e,t),n,r)}function Xt(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[Cn(e,t)];var n=e.display.externalMeasured;return n&&t>=n.lineN&&t<n.lineN+n.size?n:void 0}function Zt(e,t){var n=N(t),r=Xt(e,n);r&&!r.text?r=null:r&&r.changes&&(Tt(e,r,n,xn(e)),e.curOp.forceUpdate=!0),r||(r=Vt(e,t));var i=$t(r,t,n);return{line:t,view:r,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function Jt(e,t,n,r,i){t.before&&(n=-1);var o,a=n+(r||"");return t.cache.hasOwnProperty(a)?o=t.cache[a]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(Yt(e,t.view,t.rect),t.hasHeights=!0),o=tn(e,t,n,r),o.bogus||(t.cache[a]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}function Qt(e,t,n){for(var r,i,o,a,l,s,c=0;c<e.length;c+=3)if(l=e[c],s=e[c+1],t<l?(i=0,o=1,a="left"):t<s?(i=t-l,o=i+1):(c==e.length-3||t==s&&e[c+3]>t)&&(o=s-l,i=o-1,t>=s&&(a="right")),null!=i){if(r=e[c+2],l==s&&n==(r.insertLeft?"left":"right")&&(a=n),"left"==n&&0==i)for(;c&&e[c-2]==e[c-3]&&e[c-1].insertLeft;)r=e[(c-=3)+2],a="left";if("right"==n&&i==s-l)for(;c<e.length-3&&e[c+3]==e[c+4]&&!e[c+5].insertLeft;)r=e[(c+=3)+2],a="right";break}return{node:r,start:i,end:o,collapse:a,coverStart:l,coverEnd:s}}function en(e,t){var n=Ja;if("left"==t)for(var r=0;r<e.length&&(n=e[r]).left==n.right;r++);else for(var i=e.length-1;i>=0&&(n=e[i]).left==n.right;i--);return n}function tn(e,t,n,r){var i,o=Qt(t.map,n,r),a=o.node,l=o.start,s=o.end,c=o.collapse;if(3==a.nodeType){for(var u=0;u<4;u++){for(;l&&S(t.line.text.charAt(o.coverStart+l));)--l;for(;o.coverStart+s<o.coverEnd&&S(t.line.text.charAt(o.coverStart+s));)++s;if(i=na&&ra<9&&0==l&&s==o.coverEnd-o.coverStart?a.parentNode.getBoundingClientRect():en(va(a,l,s).getClientRects(),r),i.left||i.right||0==l)break;s=l,l-=1,c="right"}na&&ra<11&&(i=nn(e.display.measure,i))}else{l>0&&(c=r="right");var f;i=e.options.lineWrapping&&(f=a.getClientRects()).length>1?f["right"==r?f.length-1:0]:a.getBoundingClientRect()}if(na&&ra<9&&!l&&(!i||!i.left&&!i.right)){var h=a.parentNode.getClientRects()[0];i=h?{left:h.left,right:h.left+yn(e.display),top:h.top,bottom:h.bottom}:Ja}for(var d=i.top-t.rect.top,p=i.bottom-t.rect.top,m=(d+p)/2,g=t.view.measure.heights,v=0;v<g.length-1&&!(m<g[v]);v++);var y=v?g[v-1]:0,x=g[v],b={left:("right"==c?i.right:i.left)-t.rect.left,right:("left"==c?i.left:i.right)-t.rect.left,top:y,bottom:x};return i.left||i.right||(b.bogus=!0),e.options.singleCursorHeightPerLine||(b.rtop=d,b.rbottom=p),b}function nn(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!Ue(e))return t;var n=screen.logicalXDPI/screen.deviceXDPI,r=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*n,right:t.right*n,top:t.top*r,bottom:t.bottom*r}}function rn(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function on(e){e.display.externalMeasure=null,t(e.display.lineMeasure);for(var n=0;n<e.display.view.length;n++)rn(e.display.view[n])}function an(e){on(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function ln(){return window.pageXOffset||(document.documentElement||document.body).scrollLeft}function sn(){return window.pageYOffset||(document.documentElement||document.body).scrollTop}function cn(e,t,n,r,i){if(!i&&t.widgets)for(var o=0;o<t.widgets.length;++o)if(t.widgets[o].above){var a=Bt(t.widgets[o]);n.top+=a,n.bottom+=a}if("line"==r)return n;r||(r="local");var l=pe(t);if("local"==r?l+=Ft(e.display):l-=e.display.viewOffset,"page"==r||"window"==r){var s=e.display.lineSpace.getBoundingClientRect();l+=s.top+("window"==r?0:sn());var c=s.left+("window"==r?0:ln());
n.left+=c,n.right+=c}return n.top+=l,n.bottom+=l,n}function un(e,t,n){if("div"==n)return t;var r=t.left,i=t.top;if("page"==n)r-=ln(),i-=sn();else if("local"==n||!n){var o=e.display.sizer.getBoundingClientRect();r+=o.left,i+=o.top}var a=e.display.lineSpace.getBoundingClientRect();return{left:r-a.left,top:i-a.top}}function fn(e,t,n,r,i){return r||(r=L(e.doc,t.line)),cn(e,r,Kt(e,r,t.ch,i),n)}function hn(e,t,n,r,i,o){function a(t,a){var l=Jt(e,i,t,a?"right":"left",o);return a?l.left=l.right:l.right=l.left,cn(e,r,l,n)}function l(e,t){var n=s[t],r=n.level%2;return e==ye(n)&&t&&n.level<s[t-1].level?(n=s[--t],e=xe(n)-(n.level%2?0:1),r=!0):e==xe(n)&&t<s.length-1&&n.level<s[t+1].level&&(n=s[++t],e=ye(n)-n.level%2,r=!1),r&&e==n.to&&e>n.from?a(e-1):a(e,r)}r=r||L(e.doc,t.line),i||(i=Zt(e,r));var s=Me(r),c=t.ch;if(!s)return a(c);var u=Se(s,c),f=l(c,u);return null!=Ra&&(f.other=l(c,Ra)),f}function dn(e,t){var n=0;t=_(e.doc,t),e.options.lineWrapping||(n=yn(e.display)*t.ch);var r=L(e.doc,t.line),i=pe(r)+Ft(e.display);return{left:n,right:n,top:i,bottom:i+r.height}}function pn(e,t,n,r){var i=P(e,t);return i.xRel=r,n&&(i.outside=!0),i}function mn(e,t,n){var r=e.doc;if(n+=e.display.viewOffset,n<0)return pn(r.first,0,!0,-1);var i=E(r,n),o=r.first+r.size-1;if(i>o)return pn(r.first+r.size-1,L(r,o).text.length,!0,1);t<0&&(t=0);for(var a=L(r,i);;){var l=gn(e,a,i,t,n),s=ae(a),c=s&&s.find(0,!0);if(!s||!(l.ch>c.from.ch||l.ch==c.from.ch&&l.xRel>0))return l;i=N(a=c.to.line)}}function gn(e,t,n,r,i){function o(r){var i=hn(e,P(n,r),"line",t,c);return l=!0,a>i.bottom?i.left-s:a<i.top?i.left+s:(l=!1,i.left)}var a=i-pe(t),l=!1,s=2*e.display.wrapper.clientWidth,c=Zt(e,t),u=Me(t),f=t.text.length,h=be(t),d=we(t),p=o(h),m=l,g=o(d),v=l;if(r>g)return pn(n,d,v,1);for(;;){if(u?d==h||d==Le(t,h,1):d-h<=1){var y=r<p||r-p<=g-r?h:d,x=y==h?m:v,b=r-(y==h?p:g);if(v&&!u&&!/\s/.test(t.text.charAt(y))&&b>0&&y<t.text.length&&c.view.measure.heights.length>1){var w=Jt(e,c,y,"right");a<=w.bottom&&a>=w.top&&Math.abs(r-w.right)<b&&(x=!1,y++,b=r-w.right)}for(;S(t.text.charAt(y));)++y;var k=pn(n,y,x,b<-1?-1:b>1?1:0);return k}var C=Math.ceil(f/2),L=h+C;if(u){L=h;for(var T=0;T<C;++T)L=Le(t,L,1)}var M=o(L);M>r?(d=L,g=M,(v=l)&&(g+=1e3),f=C):(h=L,p=M,m=l,f-=C)}}function vn(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==$a){$a=r("pre");for(var i=0;i<49;++i)$a.appendChild(document.createTextNode("x")),$a.appendChild(r("br"));$a.appendChild(document.createTextNode("x"))}n(e.measure,$a);var o=$a.offsetHeight/50;return o>3&&(e.cachedTextHeight=o),t(e.measure),o||1}function yn(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=r("span","xxxxxxxxxx"),i=r("pre",[t]);n(e.measure,i);var o=t.getBoundingClientRect(),a=(o.right-o.left)/10;return a>2&&(e.cachedCharWidth=a),a||10}function xn(e){for(var t=e.display,n={},r={},i=t.gutters.clientLeft,o=t.gutters.firstChild,a=0;o;o=o.nextSibling,++a)n[e.options.gutters[a]]=o.offsetLeft+o.clientLeft+i,r[e.options.gutters[a]]=o.clientWidth;return{fixedPos:bn(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:n,gutterWidth:r,wrapperWidth:t.wrapper.clientWidth}}function bn(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function wn(e){var t=vn(e.display),n=e.options.lineWrapping,r=n&&Math.max(5,e.display.scroller.clientWidth/yn(e.display)-3);return function(i){if(he(e.doc,i))return 0;var o=0;if(i.widgets)for(var a=0;a<i.widgets.length;a++)i.widgets[a].height&&(o+=i.widgets[a].height);return n?o+(Math.ceil(i.text.length/r)||1)*t:o+t}}function kn(e){var t=e.doc,n=wn(e);t.iter(function(e){var t=n(e);t!=e.height&&A(e,t)})}function Sn(e,t,n,r){var i=e.display;if(!n&&"true"==_e(t).getAttribute("cm-not-content"))return null;var o,a,l=i.lineSpace.getBoundingClientRect();try{o=t.clientX-l.left,a=t.clientY-l.top}catch(e){return null}var s,c=mn(e,o,a);if(r&&1==c.xRel&&(s=L(e.doc,c.line).text).length==c.ch){var f=u(s,s.length,e.options.tabSize)-s.length;c=P(c.line,Math.max(0,Math.round((o-jt(e.display).left)/yn(e.display))-f))}return c}function Cn(e,t){if(t>=e.display.viewTo)return null;if(t-=e.display.viewFrom,t<0)return null;for(var n=e.display.view,r=0;r<n.length;r++)if(t-=n[r].size,t<0)return r}function Ln(e){e.display.input.showSelection(e.display.input.prepareSelection())}function Tn(e,t){for(var n=e.doc,r={},i=r.cursors=document.createDocumentFragment(),o=r.selection=document.createDocumentFragment(),a=0;a<n.sel.ranges.length;a++)if(t!==!1||a!=n.sel.primIndex){var l=n.sel.ranges[a];if(!(l.from().line>=e.display.viewTo||l.to().line<e.display.viewFrom)){var s=l.empty();(s||e.options.showCursorWhenSelecting)&&Mn(e,l.head,i),s||An(e,l,o)}}return r}function Mn(e,t,n){var i=hn(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),o=n.appendChild(r("div"," ","CodeMirror-cursor"));if(o.style.left=i.left+"px",o.style.top=i.top+"px",o.style.height=Math.max(0,i.bottom-i.top)*e.options.cursorHeight+"px",i.other){var a=n.appendChild(r("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));a.style.display="",a.style.left=i.other.left+"px",a.style.top=i.other.top+"px",a.style.height=.85*(i.other.bottom-i.other.top)+"px"}}function An(e,t,n){function i(e,t,n,i){t<0&&(t=0),t=Math.round(t),i=Math.round(i),s.appendChild(r("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==n?f-e:n)+"px;\n                             height: "+(i-t)+"px"))}function o(t,n,r){function o(n,r){return fn(e,P(t,n),"div",c,r)}var a,s,c=L(l,t),h=c.text.length;return ve(Me(c),n||0,null==r?h:r,function(e,t,l){var c,d,p,m=o(e,"left");if(e==t)c=m,d=p=m.left;else{if(c=o(t-1,"right"),"rtl"==l){var g=m;m=c,c=g}d=m.left,p=c.right}null==n&&0==e&&(d=u),c.top-m.top>3&&(i(d,m.top,null,m.bottom),d=u,m.bottom<c.top&&i(d,m.bottom,null,c.top)),null==r&&t==h&&(p=f),(!a||m.top<a.top||m.top==a.top&&m.left<a.left)&&(a=m),(!s||c.bottom>s.bottom||c.bottom==s.bottom&&c.right>s.right)&&(s=c),d<u+1&&(d=u),i(d,c.top,p-d,c.bottom)}),{start:a,end:s}}var a=e.display,l=e.doc,s=document.createDocumentFragment(),c=jt(e.display),u=c.left,f=Math.max(a.sizerWidth,qt(e)-a.sizer.offsetLeft)-c.right,h=t.from(),d=t.to();if(h.line==d.line)o(h.line,h.ch,d.ch);else{var p=L(l,h.line),m=L(l,d.line),g=se(p)==se(m),v=o(h.line,h.ch,g?p.text.length+1:null).end,y=o(d.line,g?0:null,d.ch).start;g&&(v.top<y.top-2?(i(v.right,v.top,null,v.bottom),i(u,y.top,y.left,y.bottom)):i(v.right,v.top,y.left-v.right,v.bottom)),v.bottom<y.top&&i(u,v.bottom,null,y.top)}n.appendChild(s)}function Nn(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var n=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval(function(){return t.cursorDiv.style.visibility=(n=!n)?"":"hidden"},e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function En(e){e.state.focused||(e.display.input.focus(),In(e))}function On(e){e.state.delayingBlurEvent=!0,setTimeout(function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,Pn(e))},100)}function In(e,t){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(Ee(e,"focus",e,t),e.state.focused=!0,a(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),ia&&setTimeout(function(){return e.display.input.reset(!0)},20)),e.display.input.receivedFocus()),Nn(e))}function Pn(e,t){e.state.delayingBlurEvent||(e.state.focused&&(Ee(e,"blur",e,t),e.state.focused=!1,ba(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout(function(){e.state.focused||(e.display.shift=!1)},150))}function Rn(e){var t=e.display,n=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var r=bn(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=r+"px",a=0;a<n.length;a++)if(!n[a].hidden){e.options.fixedGutter&&(n[a].gutter&&(n[a].gutter.style.left=o),n[a].gutterBackground&&(n[a].gutterBackground.style.left=o));var l=n[a].alignable;if(l)for(var s=0;s<l.length;s++)l[s].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=r+i+"px")}}function Dn(e){if(!e.options.lineNumbers)return!1;var t=e.doc,n=I(e.options,t.first+t.size-1),i=e.display;if(n.length!=i.lineNumChars){var o=i.measure.appendChild(r("div",[r("div",n)],"CodeMirror-linenumber CodeMirror-gutter-elt")),a=o.firstChild.offsetWidth,l=o.offsetWidth-a;return i.lineGutter.style.width="",i.lineNumInnerWidth=Math.max(a,i.lineGutter.offsetWidth-l)+1,i.lineNumWidth=i.lineNumInnerWidth+l,i.lineNumChars=i.lineNumInnerWidth?n.length:-1,i.lineGutter.style.width=i.lineNumWidth+"px",Tr(e),!0}return!1}function Hn(e){for(var t=e.display,n=t.lineDiv.offsetTop,r=0;r<t.view.length;r++){var i=t.view[r],o=void 0;if(!i.hidden){if(na&&ra<8){var a=i.node.offsetTop+i.node.offsetHeight;o=a-n,n=a}else{var l=i.node.getBoundingClientRect();o=l.bottom-l.top}var s=i.line.height-o;if(o<2&&(o=vn(t)),(s>.001||s<-.001)&&(A(i.line,o),Wn(i.line),i.rest))for(var c=0;c<i.rest.length;c++)Wn(i.rest[c])}}}function Wn(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t)e.widgets[t].height=e.widgets[t].node.parentNode.offsetHeight}function Bn(e,t,n){var r=n&&null!=n.top?Math.max(0,n.top):e.scroller.scrollTop;r=Math.floor(r-Ft(e));var i=n&&null!=n.bottom?n.bottom:r+e.wrapper.clientHeight,o=E(t,r),a=E(t,i);if(n&&n.ensure){var l=n.ensure.from.line,s=n.ensure.to.line;l<o?(o=l,a=E(t,pe(L(t,l))+e.wrapper.clientHeight)):Math.min(s,t.lastLine())>=a&&(o=E(t,pe(L(t,s))-e.wrapper.clientHeight),a=s)}return{from:o,to:Math.max(a,o+1)}}function _n(e,t){Math.abs(e.doc.scrollTop-t)<2||(e.doc.scrollTop=t,Qo||Cr(e,{top:t}),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t),e.display.scrollbars.setScrollTop(t),Qo&&Cr(e),xr(e,100))}function Fn(e,t,n){(n?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)||(t=Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth),e.doc.scrollLeft=t,Rn(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function zn(e){var t=e.wheelDeltaX,n=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==n&&e.detail&&e.axis==e.VERTICAL_AXIS?n=e.detail:null==n&&(n=e.wheelDelta),{x:t,y:n}}function jn(e){var t=zn(e);return t.x*=el,t.y*=el,t}function Un(e,t){var n=zn(t),r=n.x,i=n.y,o=e.display,a=o.scroller,l=a.scrollWidth>a.clientWidth,s=a.scrollHeight>a.clientHeight;if(r&&l||i&&s){if(i&&da&&ia)e:for(var c=t.target,u=o.view;c!=a;c=c.parentNode)for(var f=0;f<u.length;f++)if(u[f].node==c){e.display.currentWheelTarget=c;break e}if(r&&!Qo&&!la&&null!=el)return i&&s&&_n(e,Math.max(0,Math.min(a.scrollTop+i*el,a.scrollHeight-a.clientHeight))),Fn(e,Math.max(0,Math.min(a.scrollLeft+r*el,a.scrollWidth-a.clientWidth))),(!i||i&&s)&&De(t),void(o.wheelStartX=null);if(i&&null!=el){var h=i*el,d=e.doc.scrollTop,p=d+o.wrapper.clientHeight;h<0?d=Math.max(0,d+h-50):p=Math.min(e.doc.height,p+h+50),Cr(e,{top:d,bottom:p})}Qa<20&&(null==o.wheelStartX?(o.wheelStartX=a.scrollLeft,o.wheelStartY=a.scrollTop,o.wheelDX=r,o.wheelDY=i,setTimeout(function(){if(null!=o.wheelStartX){var e=a.scrollLeft-o.wheelStartX,t=a.scrollTop-o.wheelStartY,n=t&&o.wheelDY&&t/o.wheelDY||e&&o.wheelDX&&e/o.wheelDX;o.wheelStartX=o.wheelStartY=null,n&&(el=(el*Qa+n)/(Qa+1),++Qa)}},200)):(o.wheelDX+=r,o.wheelDY+=i))}}function qn(e){var t=e.display,n=t.gutters.offsetWidth,r=Math.round(e.doc.height+zt(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?n:0,docHeight:r,scrollHeight:r+Ut(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:n}}function Gn(e,t){t||(t=qn(e));var n=e.display.barWidth,r=e.display.barHeight;Yn(e,t);for(var i=0;i<4&&n!=e.display.barWidth||r!=e.display.barHeight;i++)n!=e.display.barWidth&&e.options.lineWrapping&&Hn(e),Yn(e,qn(e)),n=e.display.barWidth,r=e.display.barHeight}function Yn(e,t){var n=e.display,r=n.scrollbars.update(t);n.sizer.style.paddingRight=(n.barWidth=r.right)+"px",n.sizer.style.paddingBottom=(n.barHeight=r.bottom)+"px",n.heightForcer.style.borderBottom=r.bottom+"px solid transparent",r.right&&r.bottom?(n.scrollbarFiller.style.display="block",n.scrollbarFiller.style.height=r.bottom+"px",n.scrollbarFiller.style.width=r.right+"px"):n.scrollbarFiller.style.display="",r.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(n.gutterFiller.style.display="block",n.gutterFiller.style.height=r.bottom+"px",n.gutterFiller.style.width=t.gutterWidth+"px"):n.gutterFiller.style.display=""}function $n(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&ba(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new rl[e.options.scrollbarStyle](function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),Wa(t,"mousedown",function(){e.state.focused&&setTimeout(function(){return e.display.input.focus()},0)}),t.setAttribute("cm-not-content","true")},function(t,n){"horizontal"==n?Fn(e,t):_n(e,t)},e),e.display.scrollbars.addClass&&a(e.display.wrapper,e.display.scrollbars.addClass)}function Vn(e,t){if(!Oe(e,"scrollCursorIntoView")){var n=e.display,i=n.sizer.getBoundingClientRect(),o=null;if(t.top+i.top<0?o=!0:t.bottom+i.top>(window.innerHeight||document.documentElement.clientHeight)&&(o=!1),null!=o&&!ua){var a=r("div","​",null,"position: absolute;\n                         top: "+(t.top-n.viewOffset-Ft(e.display))+"px;\n                         height: "+(t.bottom-t.top+Ut(e)+n.barHeight)+"px;\n                         left: "+t.left+"px; width: 2px;");e.display.lineSpace.appendChild(a),a.scrollIntoView(o),e.display.lineSpace.removeChild(a)}}}function Kn(e,t,n,r){null==r&&(r=0);for(var i,o=0;o<5;o++){var a=!1;i=hn(e,t);var l=n&&n!=t?hn(e,n):i,s=Zn(e,Math.min(i.left,l.left),Math.min(i.top,l.top)-r,Math.max(i.left,l.left),Math.max(i.bottom,l.bottom)+r),c=e.doc.scrollTop,u=e.doc.scrollLeft;if(null!=s.scrollTop&&(_n(e,s.scrollTop),Math.abs(e.doc.scrollTop-c)>1&&(a=!0)),null!=s.scrollLeft&&(Fn(e,s.scrollLeft),Math.abs(e.doc.scrollLeft-u)>1&&(a=!0)),!a)break}return i}function Xn(e,t,n,r,i){var o=Zn(e,t,n,r,i);null!=o.scrollTop&&_n(e,o.scrollTop),null!=o.scrollLeft&&Fn(e,o.scrollLeft)}function Zn(e,t,n,r,i){var o=e.display,a=vn(e.display);n<0&&(n=0);var l=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:o.scroller.scrollTop,s=Gt(e),c={};i-n>s&&(i=n+s);var u=e.doc.height+zt(o),f=n<a,h=i>u-a;if(n<l)c.scrollTop=f?0:n;else if(i>l+s){var d=Math.min(n,(h?u:i)-s);d!=l&&(c.scrollTop=d)}var p=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:o.scroller.scrollLeft,m=qt(e)-(e.options.fixedGutter?o.gutters.offsetWidth:0),g=r-t>m;return g&&(r=t+m),t<10?c.scrollLeft=0:t<p?c.scrollLeft=Math.max(0,t-(g?0:10)):r>m+p-3&&(c.scrollLeft=r+(g?0:10)-m),c}function Jn(e,t,n){null==t&&null==n||er(e),null!=t&&(e.curOp.scrollLeft=(null==e.curOp.scrollLeft?e.doc.scrollLeft:e.curOp.scrollLeft)+t),null!=n&&(e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+n)}function Qn(e){er(e);var t=e.getCursor(),n=t,r=t;e.options.lineWrapping||(n=t.ch?P(t.line,t.ch-1):t,r=P(t.line,t.ch+1)),e.curOp.scrollToPos={from:n,to:r,margin:e.options.cursorScrollMargin,isCursor:!0}}function er(e){var t=e.curOp.scrollToPos;if(t){e.curOp.scrollToPos=null;var n=dn(e,t.from),r=dn(e,t.to),i=Zn(e,Math.min(n.left,r.left),Math.min(n.top,r.top)-t.margin,Math.max(n.right,r.right),Math.max(n.bottom,r.bottom)+t.margin);e.scrollTo(i.scrollLeft,i.scrollTop)}}function tr(e){e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:null,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++il},wt(e.curOp)}function nr(e){var t=e.curOp;St(t,function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;rr(e)})}function rr(e){for(var t=e.ops,n=0;n<t.length;n++)ir(t[n]);for(var r=0;r<t.length;r++)or(t[r]);for(var i=0;i<t.length;i++)ar(t[i]);for(var o=0;o<t.length;o++)lr(t[o]);for(var a=0;a<t.length;a++)sr(t[a])}function ir(e){var t=e.cm,n=t.display;wr(t),e.updateMaxLine&&ge(t),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<n.viewFrom||e.scrollToPos.to.line>=n.viewTo)||n.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new ol(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function or(e){e.updatedDisplay=e.mustUpdate&&kr(e.cm,e.update)}function ar(e){var t=e.cm,n=t.display;e.updatedDisplay&&Hn(t),e.barMeasure=qn(t),n.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=Kt(t,n.maxLine,n.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(n.scroller.clientWidth,n.sizer.offsetLeft+e.adjustWidthTo+Ut(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,n.sizer.offsetLeft+e.adjustWidthTo-qt(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=n.input.prepareSelection(e.focus))}function lr(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&Fn(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var n=e.focus&&e.focus==o()&&(!document.hasFocus||document.hasFocus());e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,n),(e.updatedDisplay||e.startHeight!=t.doc.height)&&Gn(t,e.barMeasure),e.updatedDisplay&&Mr(t,e.barMeasure),e.selectionChanged&&Nn(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),n&&En(e.cm)}function sr(e){var t=e.cm,n=t.display,r=t.doc;if(e.updatedDisplay&&Sr(t,e.update),null==n.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(n.wheelStartX=n.wheelStartY=null),null==e.scrollTop||n.scroller.scrollTop==e.scrollTop&&!e.forceScroll||(r.scrollTop=Math.max(0,Math.min(n.scroller.scrollHeight-n.scroller.clientHeight,e.scrollTop)),n.scrollbars.setScrollTop(r.scrollTop),n.scroller.scrollTop=r.scrollTop),null==e.scrollLeft||n.scroller.scrollLeft==e.scrollLeft&&!e.forceScroll||(r.scrollLeft=Math.max(0,Math.min(n.scroller.scrollWidth-n.scroller.clientWidth,e.scrollLeft)),n.scrollbars.setScrollLeft(r.scrollLeft),n.scroller.scrollLeft=r.scrollLeft,Rn(t)),e.scrollToPos){var i=Kn(t,_(r,e.scrollToPos.from),_(r,e.scrollToPos.to),e.scrollToPos.margin);e.scrollToPos.isCursor&&t.state.focused&&Vn(t,i)}var o=e.maybeHiddenMarkers,a=e.maybeUnhiddenMarkers;if(o)for(var l=0;l<o.length;++l)o[l].lines.length||Ee(o[l],"hide");if(a)for(var s=0;s<a.length;++s)a[s].lines.length&&Ee(a[s],"unhide");n.wrapper.offsetHeight&&(r.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&Ee(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function cr(e,t){if(e.curOp)return t();tr(e);try{return t()}finally{nr(e)}}function ur(e,t){return function(){if(e.curOp)return t.apply(e,arguments);tr(e);try{return t.apply(e,arguments)}finally{nr(e)}}}function fr(e){return function(){if(this.curOp)return e.apply(this,arguments);tr(this);try{return e.apply(this,arguments)}finally{nr(this)}}}function hr(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);tr(t);try{return e.apply(this,arguments)}finally{nr(t)}}}function dr(e,t,n,r){null==t&&(t=e.doc.first),null==n&&(n=e.doc.first+e.doc.size),r||(r=0);var i=e.display;if(r&&n<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)Pa&&ue(e.doc,t)<i.viewTo&&mr(e);else if(n<=i.viewFrom)Pa&&fe(e.doc,n+r)>i.viewFrom?mr(e):(i.viewFrom+=r,i.viewTo+=r);else if(t<=i.viewFrom&&n>=i.viewTo)mr(e);else if(t<=i.viewFrom){var o=gr(e,n,n+r,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=r):mr(e)}else if(n>=i.viewTo){var a=gr(e,t,t,-1);a?(i.view=i.view.slice(0,a.index),i.viewTo=a.lineN):mr(e)}else{var l=gr(e,t,t,-1),s=gr(e,n,n+r,1);l&&s?(i.view=i.view.slice(0,l.index).concat(bt(e,l.lineN,s.lineN)).concat(i.view.slice(s.index)),i.viewTo+=r):mr(e)}var c=i.externalMeasured;c&&(n<c.lineN?c.lineN+=r:t<c.lineN+c.size&&(i.externalMeasured=null))}function pr(e,t,n){e.curOp.viewChanged=!0;var r=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(r.externalMeasured=null),!(t<r.viewFrom||t>=r.viewTo)){var o=r.view[Cn(e,t)];if(null!=o.node){var a=o.changes||(o.changes=[]);h(a,n)==-1&&a.push(n)}}}function mr(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function gr(e,t,n,r){var i,o=Cn(e,t),a=e.display.view;if(!Pa||n==e.doc.first+e.doc.size)return{index:o,lineN:n};for(var l=e.display.viewFrom,s=0;s<o;s++)l+=a[s].size;if(l!=t){if(r>0){if(o==a.length-1)return null;i=l+a[o].size-t,o++}else i=l-t;t+=i,n+=i}for(;ue(e.doc,n)!=n;){if(o==(r<0?0:a.length-1))return null;n+=r*a[o-(r<0?1:0)].size,o+=r}return{index:o,lineN:n}}function vr(e,t,n){var r=e.display,i=r.view;0==i.length||t>=r.viewTo||n<=r.viewFrom?(r.view=bt(e,t,n),r.viewFrom=t):(r.viewFrom>t?r.view=bt(e,t,r.viewFrom).concat(r.view):r.viewFrom<t&&(r.view=r.view.slice(Cn(e,t))),r.viewFrom=t,r.viewTo<n?r.view=r.view.concat(bt(e,r.viewTo,n)):r.viewTo>n&&(r.view=r.view.slice(0,Cn(e,n)))),r.viewTo=n}function yr(e){for(var t=e.display.view,n=0,r=0;r<t.length;r++){var i=t[r];i.hidden||i.node&&!i.changes||++n}return n}function xr(e,t){e.doc.mode.startState&&e.doc.frontier<e.display.viewTo&&e.state.highlight.set(t,s(br,e))}function br(e){var t=e.doc;if(t.frontier<t.first&&(t.frontier=t.first),!(t.frontier>=e.display.viewTo)){var n=+new Date+e.options.workTime,r=Ke(t.mode,et(e,t.frontier)),i=[];t.iter(t.frontier,Math.min(t.first+t.size,e.display.viewTo+500),function(o){if(t.frontier>=e.display.viewFrom){var a=o.styles,l=o.text.length>e.options.maxHighlightLength,s=Je(e,o,l?Ke(t.mode,r):r,!0);o.styles=s.styles;var c=o.styleClasses,u=s.classes;u?o.styleClasses=u:c&&(o.styleClasses=null);for(var f=!a||a.length!=o.styles.length||c!=u&&(!c||!u||c.bgClass!=u.bgClass||c.textClass!=u.textClass),h=0;!f&&h<a.length;++h)f=a[h]!=o.styles[h];f&&i.push(t.frontier),o.stateAfter=l?r:Ke(t.mode,r)}else o.text.length<=e.options.maxHighlightLength&&tt(e,o.text,r),o.stateAfter=t.frontier%5==0?Ke(t.mode,r):null;if(++t.frontier,+new Date>n)return xr(e,e.options.workDelay),!0}),i.length&&cr(e,function(){for(var t=0;t<i.length;t++)pr(e,i[t],"text")})}}function wr(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=Ut(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=Ut(e)+"px",t.scrollbarsClipped=!0)}function kr(e,n){var r=e.display,i=e.doc;if(n.editorIsHidden)return mr(e),!1;if(!n.force&&n.visible.from>=r.viewFrom&&n.visible.to<=r.viewTo&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo)&&r.renderedView==r.view&&0==yr(e))return!1;Dn(e)&&(mr(e),n.dims=xn(e));var a=i.first+i.size,l=Math.max(n.visible.from-e.options.viewportMargin,i.first),s=Math.min(a,n.visible.to+e.options.viewportMargin);r.viewFrom<l&&l-r.viewFrom<20&&(l=Math.max(i.first,r.viewFrom)),r.viewTo>s&&r.viewTo-s<20&&(s=Math.min(a,r.viewTo)),Pa&&(l=ue(e.doc,l),s=fe(e.doc,s));var c=l!=r.viewFrom||s!=r.viewTo||r.lastWrapHeight!=n.wrapperHeight||r.lastWrapWidth!=n.wrapperWidth;vr(e,l,s),r.viewOffset=pe(L(e.doc,r.viewFrom)),e.display.mover.style.top=r.viewOffset+"px";var u=yr(e);if(!c&&0==u&&!n.force&&r.renderedView==r.view&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo))return!1;var f=o();return u>4&&(r.lineDiv.style.display="none"),Lr(e,r.updateLineNumbers,n.dims),u>4&&(r.lineDiv.style.display=""),r.renderedView=r.view,f&&o()!=f&&f.offsetHeight&&f.focus(),t(r.cursorDiv),t(r.selectionDiv),r.gutters.style.height=r.sizer.style.minHeight=0,c&&(r.lastWrapHeight=n.wrapperHeight,r.lastWrapWidth=n.wrapperWidth,xr(e,400)),r.updateLineNumbers=null,!0}function Sr(e,t){for(var n=t.viewport,r=!0;(r&&e.options.lineWrapping&&t.oldDisplayWidth!=qt(e)||(n&&null!=n.top&&(n={top:Math.min(e.doc.height+zt(e.display)-Gt(e),n.top)}),t.visible=Bn(e.display,e.doc,n),!(t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)))&&kr(e,t);r=!1){Hn(e);var i=qn(e);Ln(e),Gn(e,i),Mr(e,i)}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function Cr(e,t){var n=new ol(e,t);if(kr(e,n)){Hn(e),Sr(e,n);var r=qn(e);Ln(e),Gn(e,r),Mr(e,r),n.finish()}}function Lr(e,n,r){function i(t){var n=t.nextSibling;return ia&&da&&e.display.currentWheelTarget==t?t.style.display="none":t.parentNode.removeChild(t),n}for(var o=e.display,a=e.options.lineNumbers,l=o.lineDiv,s=l.firstChild,c=o.view,u=o.viewFrom,f=0;f<c.length;f++){var d=c[f];if(d.hidden);else if(d.node&&d.node.parentNode==l){for(;s!=d.node;)s=i(s);var p=a&&null!=n&&n<=u&&d.lineNumber;d.changes&&(h(d.changes,"gutter")>-1&&(p=!1),Tt(e,d,u,r)),p&&(t(d.lineNumber),d.lineNumber.appendChild(document.createTextNode(I(e.options,u)))),s=d.node.nextSibling}else{var m=Rt(e,d,u,r);l.insertBefore(m,s)}u+=d.size}for(;s;)s=i(s)}function Tr(e){var t=e.display.gutters.offsetWidth;e.display.sizer.style.marginLeft=t+"px"}function Mr(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Ut(e)+"px"}function Ar(e){var n=e.display.gutters,i=e.options.gutters;t(n);for(var o=0;o<i.length;++o){var a=i[o],l=n.appendChild(r("div",null,"CodeMirror-gutter "+a));"CodeMirror-linenumbers"==a&&(e.display.lineGutter=l,l.style.width=(e.display.lineNumWidth||1)+"px")}n.style.display=o?"":"none",Tr(e)}function Nr(e){var t=h(e.gutters,"CodeMirror-linenumbers");t==-1&&e.lineNumbers?e.gutters=e.gutters.concat(["CodeMirror-linenumbers"]):t>-1&&!e.lineNumbers&&(e.gutters=e.gutters.slice(0),e.gutters.splice(t,1))}function Er(e,t){this.ranges=e,this.primIndex=t}function Or(e,t){this.anchor=e,this.head=t}function Ir(e,t){var n=e[t];e.sort(function(e,t){return R(e.from(),t.from())}),t=h(e,n);for(var r=1;r<e.length;r++){var i=e[r],o=e[r-1];if(R(o.to(),i.from())>=0){var a=W(o.from(),i.from()),l=H(o.to(),i.to()),s=o.empty()?i.from()==i.head:o.from()==o.head;r<=t&&--t,e.splice(--r,2,new Or(s?l:a,s?a:l))}}return new Er(e,t)}function Pr(e,t){return new Er([new Or(e,t||e)],0)}function Rr(e){return e.text?P(e.from.line+e.text.length-1,m(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function Dr(e,t){if(R(e,t.from)<0)return e;if(R(e,t.to)<=0)return Rr(t);var n=e.line+t.text.length-(t.to.line-t.from.line)-1,r=e.ch;return e.line==t.to.line&&(r+=Rr(t).ch-t.to.ch),P(n,r)}function Hr(e,t){for(var n=[],r=0;r<e.sel.ranges.length;r++){var i=e.sel.ranges[r];n.push(new Or(Dr(i.anchor,t),Dr(i.head,t)))}return Ir(n,e.sel.primIndex)}function Wr(e,t,n){return e.line==t.line?P(n.line,e.ch-t.ch+n.ch):P(n.line+(e.line-t.line),e.ch)}function Br(e,t,n){for(var r=[],i=P(e.first,0),o=i,a=0;a<t.length;a++){var l=t[a],s=Wr(l.from,i,o),c=Wr(Rr(l),i,o);if(i=l.to,o=c,"around"==n){var u=e.sel.ranges[a],f=R(u.head,u.anchor)<0;r[a]=new Or(f?c:s,f?s:c)}else r[a]=new Or(s,s)}return new Er(r,e.sel.primIndex)}function _r(e){e.doc.mode=$e(e.options,e.doc.modeOption),Fr(e)}function Fr(e){e.doc.iter(function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)}),e.doc.frontier=e.doc.first,xr(e,100),e.state.modeGen++,e.curOp&&dr(e)}function zr(e,t){return 0==t.from.ch&&0==t.to.ch&&""==m(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function jr(e,t,n,r){function i(e){return n?n[e]:null}function o(e,n,i){ct(e,n,i,r),Ct(e,"change",e,t)}function a(e,t){for(var n=[],o=e;o<t;++o)n.push(new st(c[o],i(o),r));return n}var l=t.from,s=t.to,c=t.text,u=L(e,l.line),f=L(e,s.line),h=m(c),d=i(c.length-1),p=s.line-l.line;if(t.full)e.insert(0,a(0,c.length)),e.remove(c.length,e.size-c.length);else if(zr(e,t)){var g=a(0,c.length-1);o(f,f.text,d),p&&e.remove(l.line,p),g.length&&e.insert(l.line,g)}else if(u==f)if(1==c.length)o(u,u.text.slice(0,l.ch)+h+u.text.slice(s.ch),d);else{var v=a(1,c.length-1);v.push(new st(h+u.text.slice(s.ch),d,r)),o(u,u.text.slice(0,l.ch)+c[0],i(0)),e.insert(l.line+1,v)}else if(1==c.length)o(u,u.text.slice(0,l.ch)+c[0]+f.text.slice(s.ch),i(0)),e.remove(l.line+1,p);else{o(u,u.text.slice(0,l.ch)+c[0],i(0)),o(f,h+f.text.slice(s.ch),d);var y=a(1,c.length-1);p>1&&e.remove(l.line+1,p-1),e.insert(l.line+1,y)}Ct(e,"change",e,t)}function Ur(e,t,n){function r(e,i,o){if(e.linked)for(var a=0;a<e.linked.length;++a){var l=e.linked[a];if(l.doc!=i){var s=o&&l.sharedHist;n&&!s||(t(l.doc,s),r(l.doc,e,s))}}}r(e,null,!0)}function qr(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,kn(e),_r(e),e.options.lineWrapping||ge(e),e.options.mode=t.modeOption,dr(e)}function Gr(e){this.done=[],this.undone=[],this.undoDepth=1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e||1}function Yr(e,t){var n={from:D(t.from),to:Rr(t),text:T(e,t.from,t.to)};return Qr(e,n,t.from.line,t.to.line+1),Ur(e,function(e){return Qr(e,n,t.from.line,t.to.line+1)},!0),n}function $r(e){for(;e.length;){var t=m(e);if(!t.ranges)break;e.pop()}}function Vr(e,t){return t?($r(e.done),m(e.done)):e.done.length&&!m(e.done).ranges?m(e.done):e.done.length>1&&!e.done[e.done.length-2].ranges?(e.done.pop(),m(e.done)):void 0}function Kr(e,t,n,r){var i=e.history;i.undone.length=0;var o,a,l=+new Date;if((i.lastOp==r||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&e.cm&&i.lastModTime>l-e.cm.options.historyEventDelay||"*"==t.origin.charAt(0)))&&(o=Vr(i,i.lastOp==r)))a=m(o.changes),0==R(t.from,t.to)&&0==R(t.from,a.to)?a.to=Rr(t):o.changes.push(Yr(e,t));else{var s=m(i.done);for(s&&s.ranges||Jr(e.sel,i.done),o={changes:[Yr(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(n),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=l,i.lastOp=i.lastSelOp=r,i.lastOrigin=i.lastSelOrigin=t.origin,a||Ee(e,"historyAdded")}function Xr(e,t,n,r){var i=t.charAt(0);return"*"==i||"+"==i&&n.ranges.length==r.ranges.length&&n.somethingSelected()==r.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}function Zr(e,t,n,r){var i=e.history,o=r&&r.origin;n==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||Xr(e,o,m(i.done),t))?i.done[i.done.length-1]=t:Jr(t,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=n,r&&r.clearRedo!==!1&&$r(i.undone)}function Jr(e,t){var n=m(t);n&&n.ranges&&n.equals(e)||t.push(e)}function Qr(e,t,n,r){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,n),Math.min(e.first+e.size,r),function(n){n.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=n.markedSpans),++o})}function ei(e){if(!e)return null;for(var t,n=0;n<e.length;++n)e[n].marker.explicitlyCleared?t||(t=e.slice(0,n)):t&&t.push(e[n]);return t?t.length?t:null:e}function ti(e,t){var n=t["spans_"+e.id];if(!n)return null;for(var r=[],i=0;i<t.text.length;++i)r.push(ei(n[i]));return r}function ni(e,t){var n=ti(e,t),r=X(e,t);if(!n)return r;if(!r)return n;for(var i=0;i<n.length;++i){var o=n[i],a=r[i];if(o&&a)e:for(var l=0;l<a.length;++l){for(var s=a[l],c=0;c<o.length;++c)if(o[c].marker==s.marker)continue e;o.push(s)}else a&&(n[i]=a)}return n}function ri(e,t,n){for(var r=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)r.push(n?Er.prototype.deepCopy.call(o):o);else{var a=o.changes,l=[];r.push({changes:l});for(var s=0;s<a.length;++s){var c=a[s],u=void 0;if(l.push({from:c.from,to:c.to,text:c.text}),t)for(var f in c)(u=f.match(/^spans_(\d+)$/))&&h(t,Number(u[1]))>-1&&(m(l)[f]=c[f],delete c[f])}}}return r}function ii(e,t,n,r){if(e.cm&&e.cm.display.shift||e.extend){
var i=t.anchor;if(r){var o=R(n,i)<0;o!=R(r,i)<0?(i=n,n=r):o!=R(n,r)<0&&(n=r)}return new Or(i,n)}return new Or(r||n,n)}function oi(e,t,n,r){fi(e,new Er([ii(e,e.sel.primary(),t,n)],0),r)}function ai(e,t,n){for(var r=[],i=0;i<e.sel.ranges.length;i++)r[i]=ii(e,e.sel.ranges[i],t[i],null);var o=Ir(r,e.sel.primIndex);fi(e,o,n)}function li(e,t,n,r){var i=e.sel.ranges.slice(0);i[t]=n,fi(e,Ir(i,e.sel.primIndex),r)}function si(e,t,n,r){fi(e,Pr(t,n),r)}function ci(e,t,n){var r={ranges:t.ranges,update:function(t){var n=this;this.ranges=[];for(var r=0;r<t.length;r++)n.ranges[r]=new Or(_(e,t[r].anchor),_(e,t[r].head))},origin:n&&n.origin};return Ee(e,"beforeSelectionChange",e,r),e.cm&&Ee(e.cm,"beforeSelectionChange",e.cm,r),r.ranges!=t.ranges?Ir(r.ranges,r.ranges.length-1):t}function ui(e,t,n){var r=e.history.done,i=m(r);i&&i.ranges?(r[r.length-1]=t,hi(e,t,n)):fi(e,t,n)}function fi(e,t,n){hi(e,t,n),Zr(e,e.sel,e.cm?e.cm.curOp.id:NaN,n)}function hi(e,t,n){(Pe(e,"beforeSelectionChange")||e.cm&&Pe(e.cm,"beforeSelectionChange"))&&(t=ci(e,t,n));var r=n&&n.bias||(R(t.primary().head,e.sel.primary().head)<0?-1:1);di(e,mi(e,t,r,!0)),n&&n.scroll===!1||!e.cm||Qn(e.cm)}function di(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=e.cm.curOp.selectionChanged=!0,Ie(e.cm)),Ct(e,"cursorActivity",e))}function pi(e){di(e,mi(e,e.sel,null,!1),Ta)}function mi(e,t,n,r){for(var i,o=0;o<t.ranges.length;o++){var a=t.ranges[o],l=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],s=vi(e,a.anchor,l&&l.anchor,n,r),c=vi(e,a.head,l&&l.head,n,r);(i||s!=a.anchor||c!=a.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new Or(s,c))}return i?Ir(i,t.primIndex):t}function gi(e,t,n,r,i){var o=L(e,t.line);if(o.markedSpans)for(var a=0;a<o.markedSpans.length;++a){var l=o.markedSpans[a],s=l.marker;if((null==l.from||(s.inclusiveLeft?l.from<=t.ch:l.from<t.ch))&&(null==l.to||(s.inclusiveRight?l.to>=t.ch:l.to>t.ch))){if(i&&(Ee(s,"beforeCursorEnter"),s.explicitlyCleared)){if(o.markedSpans){--a;continue}break}if(!s.atomic)continue;if(n){var c=s.find(r<0?1:-1),u=void 0;if((r<0?s.inclusiveRight:s.inclusiveLeft)&&(c=yi(e,c,-r,c&&c.line==t.line?o:null)),c&&c.line==t.line&&(u=R(c,n))&&(r<0?u<0:u>0))return gi(e,c,t,r,i)}var f=s.find(r<0?-1:1);return(r<0?s.inclusiveLeft:s.inclusiveRight)&&(f=yi(e,f,r,f.line==t.line?o:null)),f?gi(e,f,t,r,i):null}}return t}function vi(e,t,n,r,i){var o=r||1,a=gi(e,t,n,o,i)||!i&&gi(e,t,n,o,!0)||gi(e,t,n,-o,i)||!i&&gi(e,t,n,-o,!0);return a?a:(e.cantEdit=!0,P(e.first,0))}function yi(e,t,n,r){return n<0&&0==t.ch?t.line>e.first?_(e,P(t.line-1)):null:n>0&&t.ch==(r||L(e,t.line)).text.length?t.line<e.first+e.size-1?P(t.line+1,0):null:new P(t.line,t.ch+n)}function xi(e){e.setSelection(P(e.firstLine(),0),P(e.lastLine()),Ta)}function bi(e,t,n){var r={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return r.canceled=!0}};return n&&(r.update=function(t,n,i,o){t&&(r.from=_(e,t)),n&&(r.to=_(e,n)),i&&(r.text=i),void 0!==o&&(r.origin=o)}),Ee(e,"beforeChange",e,r),e.cm&&Ee(e.cm,"beforeChange",e.cm,r),r.canceled?null:{from:r.from,to:r.to,text:r.text,origin:r.origin}}function wi(e,t,n){if(e.cm){if(!e.cm.curOp)return ur(e.cm,wi)(e,t,n);if(e.cm.state.suppressEdits)return}if(!(Pe(e,"beforeChange")||e.cm&&Pe(e.cm,"beforeChange"))||(t=bi(e,t,!0))){var r=Ia&&!n&&J(e,t.from,t.to);if(r)for(var i=r.length-1;i>=0;--i)ki(e,{from:r[i].from,to:r[i].to,text:i?[""]:t.text});else ki(e,t)}}function ki(e,t){if(1!=t.text.length||""!=t.text[0]||0!=R(t.from,t.to)){var n=Hr(e,t);Kr(e,t,n,e.cm?e.cm.curOp.id:NaN),Li(e,t,n,X(e,t));var r=[];Ur(e,function(e,n){n||h(r,e.history)!=-1||(Ei(e.history,t),r.push(e.history)),Li(e,t,null,X(e,t))})}}function Si(e,t,n){if(!e.cm||!e.cm.state.suppressEdits||n){for(var r,i=e.history,o=e.sel,a="undo"==t?i.done:i.undone,l="undo"==t?i.undone:i.done,s=0;s<a.length&&(r=a[s],n?!r.ranges||r.equals(e.sel):r.ranges);s++);if(s!=a.length){for(i.lastOrigin=i.lastSelOrigin=null;r=a.pop(),r.ranges;){if(Jr(r,l),n&&!r.equals(e.sel))return void fi(e,r,{clearRedo:!1});o=r}var c=[];Jr(o,l),l.push({changes:c,generation:i.generation}),i.generation=r.generation||++i.maxGeneration;for(var u=Pe(e,"beforeChange")||e.cm&&Pe(e.cm,"beforeChange"),f=function(n){var i=r.changes[n];if(i.origin=t,u&&!bi(e,i,!1))return a.length=0,{};c.push(Yr(e,i));var o=n?Hr(e,i):m(a);Li(e,i,o,ni(e,i)),!n&&e.cm&&e.cm.scrollIntoView({from:i.from,to:Rr(i)});var l=[];Ur(e,function(e,t){t||h(l,e.history)!=-1||(Ei(e.history,i),l.push(e.history)),Li(e,i,null,ni(e,i))})},d=r.changes.length-1;d>=0;--d){var p=f(d);if(p)return p.v}}}}function Ci(e,t){if(0!=t&&(e.first+=t,e.sel=new Er(g(e.sel.ranges,function(e){return new Or(P(e.anchor.line+t,e.anchor.ch),P(e.head.line+t,e.head.ch))}),e.sel.primIndex),e.cm)){dr(e.cm,e.first,e.first-t,t);for(var n=e.cm.display,r=n.viewFrom;r<n.viewTo;r++)pr(e.cm,r,"gutter")}}function Li(e,t,n,r){if(e.cm&&!e.cm.curOp)return ur(e.cm,Li)(e,t,n,r);if(t.to.line<e.first)return void Ci(e,t.text.length-1-(t.to.line-t.from.line));if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);Ci(e,i),t={from:P(e.first,0),to:P(t.to.line+i,t.to.ch),text:[m(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:P(o,L(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=T(e,t.from,t.to),n||(n=Hr(e,t)),e.cm?Ti(e.cm,t,r):jr(e,t,r),hi(e,n,Ta)}}function Ti(e,t,n){var r=e.doc,i=e.display,o=t.from,a=t.to,l=!1,s=o.line;e.options.lineWrapping||(s=N(se(L(r,o.line))),r.iter(s,a.line+1,function(e){if(e==i.maxLine)return l=!0,!0})),r.sel.contains(t.from,t.to)>-1&&Ie(e),jr(r,t,n,wn(e)),e.options.lineWrapping||(r.iter(s,o.line+t.text.length,function(e){var t=me(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,l=!1)}),l&&(e.curOp.updateMaxLine=!0)),r.frontier=Math.min(r.frontier,o.line),xr(e,400);var c=t.text.length-(a.line-o.line)-1;t.full?dr(e):o.line!=a.line||1!=t.text.length||zr(e.doc,t)?dr(e,o.line,a.line+1,c):pr(e,o.line,"text");var u=Pe(e,"changes"),f=Pe(e,"change");if(f||u){var h={from:o,to:a,text:t.text,removed:t.removed,origin:t.origin};f&&Ct(e,"change",e,h),u&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(h)}e.display.selForContextMenu=null}function Mi(e,t,n,r,i){if(r||(r=n),R(r,n)<0){var o=r;r=n,n=o}"string"==typeof t&&(t=e.splitLines(t)),wi(e,{from:n,to:r,text:t,origin:i})}function Ai(e,t,n,r){n<e.line?e.line+=r:t<e.line&&(e.line=t,e.ch=0)}function Ni(e,t,n,r){for(var i=0;i<e.length;++i){var o=e[i],a=!0;if(o.ranges){o.copied||(o=e[i]=o.deepCopy(),o.copied=!0);for(var l=0;l<o.ranges.length;l++)Ai(o.ranges[l].anchor,t,n,r),Ai(o.ranges[l].head,t,n,r)}else{for(var s=0;s<o.changes.length;++s){var c=o.changes[s];if(n<c.from.line)c.from=P(c.from.line+r,c.from.ch),c.to=P(c.to.line+r,c.to.ch);else if(t<=c.to.line){a=!1;break}}a||(e.splice(0,i+1),i=0)}}}function Ei(e,t){var n=t.from.line,r=t.to.line,i=t.text.length-(r-n)-1;Ni(e.done,n,r,i),Ni(e.undone,n,r,i)}function Oi(e,t,n,r){var i=t,o=t;return"number"==typeof t?o=L(e,B(e,t)):i=N(t),null==i?null:(r(o,i)&&e.cm&&pr(e.cm,i,n),o)}function Ii(e){var t=this;this.lines=e,this.parent=null;for(var n=0,r=0;r<e.length;++r)e[r].parent=t,n+=e[r].height;this.height=n}function Pi(e){var t=this;this.children=e;for(var n=0,r=0,i=0;i<e.length;++i){var o=e[i];n+=o.chunkSize(),r+=o.height,o.parent=t}this.size=n,this.height=r,this.parent=null}function Ri(e,t,n){var r=this;if(n)for(var i in n)n.hasOwnProperty(i)&&(r[i]=n[i]);this.doc=e,this.node=t}function Di(e,t,n){pe(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&Jn(e,null,n)}function Hi(e,t,n,r){var i=new Ri(e,n,r),o=e.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),Oi(e,t,"widget",function(t){var n=t.widgets||(t.widgets=[]);if(null==i.insertAt?n.push(i):n.splice(Math.min(n.length-1,Math.max(0,i.insertAt)),0,i),i.line=t,o&&!he(e,t)){var r=pe(t)<e.scrollTop;A(t,t.height+Bt(i)),r&&Jn(o,null,i.height),o.curOp.forceUpdate=!0}return!0}),i}function Wi(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++al}function Bi(e,t,n,i,o){if(i&&i.shared)return Fi(e,t,n,i,o);if(e.cm&&!e.cm.curOp)return ur(e.cm,Bi)(e,t,n,i,o);var a=new Wi(e,o),l=R(t,n);if(i&&c(i,a,!1),l>0||0==l&&a.clearWhenEmpty!==!1)return a;if(a.replacedWith&&(a.collapsed=!0,a.widgetNode=r("span",[a.replacedWith],"CodeMirror-widget"),i.handleMouseEvents||a.widgetNode.setAttribute("cm-ignore-events","true"),i.insertLeft&&(a.widgetNode.insertLeft=!0)),a.collapsed){if(le(e,t.line,t,n,a)||t.line!=n.line&&le(e,n.line,t,n,a))throw new Error("Inserting collapsed marker partially overlapping an existing one");U()}a.addToHistory&&Kr(e,{from:t,to:n,origin:"markText"},e.sel,NaN);var s,u=t.line,f=e.cm;if(e.iter(u,n.line+1,function(e){f&&a.collapsed&&!f.options.lineWrapping&&se(e)==f.display.maxLine&&(s=!0),a.collapsed&&u!=t.line&&A(e,0),$(e,new q(a,u==t.line?t.ch:null,u==n.line?n.ch:null)),++u}),a.collapsed&&e.iter(t.line,n.line+1,function(t){he(e,t)&&A(t,0)}),a.clearOnEnter&&Wa(a,"beforeCursorEnter",function(){return a.clear()}),a.readOnly&&(j(),(e.history.done.length||e.history.undone.length)&&e.clearHistory()),a.collapsed&&(a.id=++al,a.atomic=!0),f){if(s&&(f.curOp.updateMaxLine=!0),a.collapsed)dr(f,t.line,n.line+1);else if(a.className||a.title||a.startStyle||a.endStyle||a.css)for(var h=t.line;h<=n.line;h++)pr(f,h,"text");a.atomic&&pi(f.doc),Ct(f,"markerAdded",f,a)}return a}function _i(e,t){var n=this;this.markers=e,this.primary=t;for(var r=0;r<e.length;++r)e[r].parent=n}function Fi(e,t,n,r,i){r=c(r),r.shared=!1;var o=[Bi(e,t,n,r,i)],a=o[0],l=r.widgetNode;return Ur(e,function(e){l&&(r.widgetNode=l.cloneNode(!0)),o.push(Bi(e,_(e,t),_(e,n),r,i));for(var s=0;s<e.linked.length;++s)if(e.linked[s].isParent)return;a=m(o)}),new _i(o,a)}function zi(e){return e.findMarks(P(e.first,0),e.clipPos(P(e.lastLine())),function(e){return e.parent})}function ji(e,t){for(var n=0;n<t.length;n++){var r=t[n],i=r.find(),o=e.clipPos(i.from),a=e.clipPos(i.to);if(R(o,a)){var l=Bi(e,o,a,r.primary,r.primary.type);r.markers.push(l),l.parent=r}}}function Ui(e){for(var t=function(t){var n=e[t],r=[n.primary.doc];Ur(n.primary.doc,function(e){return r.push(e)});for(var i=0;i<n.markers.length;i++){var o=n.markers[i];h(r,o.doc)==-1&&(o.parent=null,n.markers.splice(i--,1))}},n=0;n<e.length;n++)t(n)}function qi(e){var t=this;if($i(t),!Oe(t,e)&&!_t(t.display,e)){De(e),na&&(cl=+new Date);var n=Sn(t,e,!0),r=e.dataTransfer.files;if(n&&!t.isReadOnly())if(r&&r.length&&window.FileReader&&window.File)for(var i=r.length,o=Array(i),a=0,l=function(e,r){if(!t.options.allowDropFileTypes||h(t.options.allowDropFileTypes,e.type)!=-1){var l=new FileReader;l.onload=ur(t,function(){var e=l.result;if(/[\x00-\x08\x0e-\x1f]{2}/.test(e)&&(e=""),o[r]=e,++a==i){n=_(t.doc,n);var s={from:n,to:n,text:t.doc.splitLines(o.join(t.doc.lineSeparator())),origin:"paste"};wi(t.doc,s),ui(t.doc,Pr(n,Rr(s)))}}),l.readAsText(e)}},s=0;s<i;++s)l(r[s],s);else{if(t.state.draggingText&&t.doc.sel.contains(n)>-1)return t.state.draggingText(e),void setTimeout(function(){return t.display.input.focus()},20);try{var c=e.dataTransfer.getData("Text");if(c){var u;if(t.state.draggingText&&!t.state.draggingText.copy&&(u=t.listSelections()),hi(t.doc,Pr(n,n)),u)for(var f=0;f<u.length;++f)Mi(t.doc,"",u[f].anchor,u[f].head,"drag");t.replaceSelection(c,"around","paste"),t.display.input.focus()}}catch(e){}}}}function Gi(e,t){if(na&&(!e.state.draggingText||+new Date-cl<100))return void Be(t);if(!Oe(e,t)&&!_t(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!sa)){var n=r("img",null,null,"position: fixed; left: 0; top: 0;");n.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",la&&(n.width=n.height=1,e.display.wrapper.appendChild(n),n._top=n.offsetTop),t.dataTransfer.setDragImage(n,0,0),la&&n.parentNode.removeChild(n)}}function Yi(e,t){var i=Sn(e,t);if(i){var o=document.createDocumentFragment();Mn(e,i,o),e.display.dragCursor||(e.display.dragCursor=r("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),n(e.display.dragCursor,o)}}function $i(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function Vi(e){if(document.body.getElementsByClassName)for(var t=document.body.getElementsByClassName("CodeMirror"),n=0;n<t.length;n++){var r=t[n].CodeMirror;r&&e(r)}}function Ki(){ul||(Xi(),ul=!0)}function Xi(){var e;Wa(window,"resize",function(){null==e&&(e=setTimeout(function(){e=null,Vi(Zi)},100))}),Wa(window,"blur",function(){return Vi(Pn)})}function Zi(e){var t=e.display;t.lastWrapHeight==t.wrapper.clientHeight&&t.lastWrapWidth==t.wrapper.clientWidth||(t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize())}function Ji(e){var t=e.split(/-(?!$)/);e=t[t.length-1];for(var n,r,i,o,a=0;a<t.length-1;a++){var l=t[a];if(/^(cmd|meta|m)$/i.test(l))o=!0;else if(/^a(lt)?$/i.test(l))n=!0;else if(/^(c|ctrl|control)$/i.test(l))r=!0;else{if(!/^s(hift)?$/i.test(l))throw new Error("Unrecognized modifier name: "+l);i=!0}}return n&&(e="Alt-"+e),r&&(e="Ctrl-"+e),o&&(e="Cmd-"+e),i&&(e="Shift-"+e),e}function Qi(e){var t={};for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];if(/^(name|fallthrough|(de|at)tach)$/.test(n))continue;if("..."==r){delete e[n];continue}for(var i=g(n.split(" "),Ji),o=0;o<i.length;o++){var a=void 0,l=void 0;o==i.length-1?(l=i.join(" "),a=r):(l=i.slice(0,o+1).join(" "),a="...");var s=t[l];if(s){if(s!=a)throw new Error("Inconsistent bindings for "+l)}else t[l]=a}delete e[n]}for(var c in t)e[c]=t[c];return e}function eo(e,t,n,r){t=ro(t);var i=t.call?t.call(e,r):t[e];if(i===!1)return"nothing";if("..."===i)return"multi";if(null!=i&&n(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return eo(e,t.fallthrough,n,r);for(var o=0;o<t.fallthrough.length;o++){var a=eo(e,t.fallthrough[o],n,r);if(a)return a}}}function to(e){var t="string"==typeof e?e:fl[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function no(e,t){if(la&&34==e.keyCode&&e.char)return!1;var n=fl[e.keyCode],r=n;return null!=r&&!e.altGraphKey&&(e.altKey&&"Alt"!=n&&(r="Alt-"+r),(ya?e.metaKey:e.ctrlKey)&&"Ctrl"!=n&&(r="Ctrl-"+r),(ya?e.ctrlKey:e.metaKey)&&"Cmd"!=n&&(r="Cmd-"+r),!t&&e.shiftKey&&"Shift"!=n&&(r="Shift-"+r),r)}function ro(e){return"string"==typeof e?ml[e]:e}function io(e,t){for(var n=e.doc.sel.ranges,r=[],i=0;i<n.length;i++){for(var o=t(n[i]);r.length&&R(o.from,m(r).to)<=0;){var a=r.pop();if(R(a.from,o.from)<0){o.from=a.from;break}}r.push(o)}cr(e,function(){for(var t=r.length-1;t>=0;t--)Mi(e.doc,"",r[t].from,r[t].to,"+delete");Qn(e)})}function oo(e,t){var n=L(e.doc,t),r=se(n);r!=n&&(t=N(r));var i=Me(r),o=i?i[0].level%2?we(r):be(r):0;return P(t,o)}function ao(e,t){for(var n,r=L(e.doc,t);n=ae(r);)r=n.find(1,!0).line,t=null;var i=Me(r),o=i?i[0].level%2?be(r):we(r):r.text.length;return P(null==t?N(r):t,o)}function lo(e,t){var n=oo(e,t.line),r=L(e.doc,n.line),i=Me(r);if(!i||0==i[0].level){var o=Math.max(0,r.text.search(/\S/)),a=t.line==n.line&&t.ch<=o&&t.ch;return P(n.line,a?0:o)}return n}function so(e,t,n){if("string"==typeof t&&(t=yl[t],!t))return!1;e.display.input.ensurePolled();var r=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n&&(e.display.shift=!1),i=t(e)!=La}finally{e.display.shift=r,e.state.suppressEdits=!1}return i}function co(e,t,n){for(var r=0;r<e.state.keyMaps.length;r++){var i=eo(t,e.state.keyMaps[r],n,e);if(i)return i}return e.options.extraKeys&&eo(t,e.options.extraKeys,n,e)||eo(t,e.options.keyMap,n,e)}function uo(e,t,n,r){var i=e.state.keySeq;if(i){if(to(t))return"handled";xl.set(50,function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())}),t=i+" "+t}var o=co(e,t,r);return"multi"==o&&(e.state.keySeq=t),"handled"==o&&Ct(e,"keyHandled",e,t,n),"handled"!=o&&"multi"!=o||(De(n),Nn(e)),i&&!o&&/\'$/.test(t)?(De(n),!0):!!o}function fo(e,t){var n=no(t,!0);return!!n&&(t.shiftKey&&!e.state.keySeq?uo(e,"Shift-"+n,t,function(t){return so(e,t,!0)})||uo(e,n,t,function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return so(e,t)}):uo(e,n,t,function(t){return so(e,t)}))}function ho(e,t,n){return uo(e,"'"+n+"'",t,function(t){return so(e,t,!0)})}function po(e){var t=this;if(t.curOp.focus=o(),!Oe(t,e)){na&&ra<11&&27==e.keyCode&&(e.returnValue=!1);var n=e.keyCode;t.display.shift=16==n||e.shiftKey;var r=fo(t,e);la&&(bl=r?n:null,!r&&88==n&&!za&&(da?e.metaKey:e.ctrlKey)&&t.replaceSelection("",null,"cut")),18!=n||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||mo(t)}}function mo(e){function t(e){18!=e.keyCode&&e.altKey||(ba(n,"CodeMirror-crosshair"),Ne(document,"keyup",t),Ne(document,"mouseover",t))}var n=e.display.lineDiv;a(n,"CodeMirror-crosshair"),Wa(document,"keyup",t),Wa(document,"mouseover",t)}function go(e){16==e.keyCode&&(this.doc.sel.shift=!1),Oe(this,e)}function vo(e){var t=this;if(!(_t(t.display,e)||Oe(t,e)||e.ctrlKey&&!e.altKey||da&&e.metaKey)){var n=e.keyCode,r=e.charCode;if(la&&n==bl)return bl=null,void De(e);if(!la||e.which&&!(e.which<10)||!fo(t,e)){var i=String.fromCharCode(null==r?n:r);"\b"!=i&&(ho(t,e,i)||t.display.input.onKeyPress(e))}}}function yo(e){var t=this,n=t.display;if(!(Oe(t,e)||n.activeTouch&&n.input.supportsTouch())){if(n.input.ensurePolled(),n.shift=e.shiftKey,_t(n,e))return void(ia||(n.scroller.draggable=!1,setTimeout(function(){return n.scroller.draggable=!0},100)));if(!So(t,e)){var r=Sn(t,e);switch(window.focus(),Fe(e)){case 1:t.state.selectingText?t.state.selectingText(e):r?xo(t,e,r):_e(e)==n.scroller&&De(e);break;case 2:ia&&(t.state.lastMiddleDown=+new Date),r&&oi(t.doc,r),setTimeout(function(){return n.input.focus()},20),De(e);break;case 3:xa?Co(t,e):On(t)}}}}function xo(e,t,n){na?setTimeout(s(En,e),0):e.curOp.focus=o();var r,i=+new Date;vl&&vl.time>i-400&&0==R(vl.pos,n)?r="triple":gl&&gl.time>i-400&&0==R(gl.pos,n)?(r="double",vl={time:i,pos:n}):(r="single",gl={time:i,pos:n});var a,l=e.doc.sel,c=da?t.metaKey:t.ctrlKey;e.options.dragDrop&&Ba&&!e.isReadOnly()&&"single"==r&&(a=l.contains(n))>-1&&(R((a=l.ranges[a]).from(),n)<0||n.xRel>0)&&(R(a.to(),n)>0||n.xRel<0)?bo(e,t,n,c):wo(e,t,n,r,c)}function bo(e,t,n,r){var i=e.display,o=+new Date,a=ur(e,function(l){ia&&(i.scroller.draggable=!1),e.state.draggingText=!1,Ne(document,"mouseup",a),Ne(i.scroller,"drop",a),Math.abs(t.clientX-l.clientX)+Math.abs(t.clientY-l.clientY)<10&&(De(l),!r&&+new Date-200<o&&oi(e.doc,n),ia||na&&9==ra?setTimeout(function(){document.body.focus(),i.input.focus()},20):i.input.focus())});ia&&(i.scroller.draggable=!0),e.state.draggingText=a,a.copy=da?t.altKey:t.ctrlKey,i.scroller.dragDrop&&i.scroller.dragDrop(),Wa(document,"mouseup",a),Wa(i.scroller,"drop",a)}function wo(e,t,n,r,i){function a(t){if(0!=R(x,t))if(x=t,"rect"==r){for(var i=[],o=e.options.tabSize,a=u(L(f,n.line).text,n.ch,o),l=u(L(f,t.line).text,t.ch,o),s=Math.min(a,l),c=Math.max(a,l),g=Math.min(n.line,t.line),v=Math.min(e.lastLine(),Math.max(n.line,t.line));g<=v;g++){var y=L(f,g).text,b=d(y,s,o);s==c?i.push(new Or(P(g,b),P(g,b))):y.length>b&&i.push(new Or(P(g,b),P(g,d(y,c,o))))}i.length||i.push(new Or(n,n)),fi(f,Ir(m.ranges.slice(0,p).concat(i),p),{origin:"*mouse",scroll:!1}),e.scrollIntoView(t)}else{var w=h,k=w.anchor,S=t;if("single"!=r){var C;C="double"==r?e.findWordAt(t):new Or(P(t.line,0),_(f,P(t.line+1,0))),R(C.anchor,k)>0?(S=C.head,k=W(w.from(),C.anchor)):(S=C.anchor,k=H(w.to(),C.head))}var T=m.ranges.slice(0);T[p]=new Or(_(f,k),S),fi(f,Ir(T,p),Ma)}}function l(t){var n=++w,i=Sn(e,t,!0,"rect"==r);if(i)if(0!=R(i,x)){e.curOp.focus=o(),a(i);var s=Bn(c,f);(i.line>=s.to||i.line<s.from)&&setTimeout(ur(e,function(){w==n&&l(t)}),150)}else{var u=t.clientY<b.top?-20:t.clientY>b.bottom?20:0;u&&setTimeout(ur(e,function(){w==n&&(c.scroller.scrollTop+=u,l(t))}),50)}}function s(t){e.state.selectingText=!1,w=1/0,De(t),c.input.focus(),Ne(document,"mousemove",k),Ne(document,"mouseup",S),f.history.lastSelOrigin=null}var c=e.display,f=e.doc;De(t);var h,p,m=f.sel,g=m.ranges;if(i&&!t.shiftKey?(p=f.sel.contains(n),h=p>-1?g[p]:new Or(n,n)):(h=f.sel.primary(),p=f.sel.primIndex),pa?t.shiftKey&&t.metaKey:t.altKey)r="rect",i||(h=new Or(n,n)),n=Sn(e,t,!0,!0),p=-1;else if("double"==r){var v=e.findWordAt(n);h=e.display.shift||f.extend?ii(f,h,v.anchor,v.head):v}else if("triple"==r){var y=new Or(P(n.line,0),_(f,P(n.line+1,0)));h=e.display.shift||f.extend?ii(f,h,y.anchor,y.head):y}else h=ii(f,h,n);i?p==-1?(p=g.length,fi(f,Ir(g.concat([h]),p),{scroll:!1,origin:"*mouse"})):g.length>1&&g[p].empty()&&"single"==r&&!t.shiftKey?(fi(f,Ir(g.slice(0,p).concat(g.slice(p+1)),0),{scroll:!1,origin:"*mouse"}),m=f.sel):li(f,p,h,Ma):(p=0,fi(f,new Er([h],0),Ma),m=f.sel);var x=n,b=c.wrapper.getBoundingClientRect(),w=0,k=ur(e,function(e){Fe(e)?l(e):s(e)}),S=ur(e,s);e.state.selectingText=S,Wa(document,"mousemove",k),Wa(document,"mouseup",S)}function ko(e,t,n,r){var i,o;try{i=t.clientX,o=t.clientY}catch(e){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;r&&De(t);var a=e.display,l=a.lineDiv.getBoundingClientRect();if(o>l.bottom||!Pe(e,n))return We(t);o-=l.top-a.viewOffset;for(var s=0;s<e.options.gutters.length;++s){var c=a.gutters.childNodes[s];if(c&&c.getBoundingClientRect().right>=i){var u=E(e.doc,o),f=e.options.gutters[s];return Ee(e,n,e,u,f,t),We(t)}}}function So(e,t){return ko(e,t,"gutterClick",!0)}function Co(e,t){_t(e.display,t)||Lo(e,t)||Oe(e,t,"contextmenu")||e.display.input.onContextMenu(t)}function Lo(e,t){return!!Pe(e,"gutterContextMenu")&&ko(e,t,"gutterContextMenu",!1)}function To(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),an(e)}function Mo(e){function t(t,r,i,o){e.defaults[t]=r,i&&(n[t]=o?function(e,t,n){n!=wl&&i(e,t,n)}:i)}var n=e.optionHandlers;e.defineOption=t,e.Init=wl,t("value","",function(e,t){return e.setValue(t)},!0),t("mode",null,function(e,t){e.doc.modeOption=t,_r(e)},!0),t("indentUnit",2,_r,!0),t("indentWithTabs",!1),t("smartIndent",!0),t("tabSize",4,function(e){Fr(e),an(e),dr(e)},!0),t("lineSeparator",null,function(e,t){if(e.doc.lineSep=t,t){var n=[],r=e.doc.first;e.doc.iter(function(e){for(var i=0;;){var o=e.text.indexOf(t,i);if(o==-1)break;i=o+t.length,n.push(P(r,o))}r++});for(var i=n.length-1;i>=0;i--)Mi(e.doc,t,n[i],P(n[i].line,n[i].ch+t.length))}}),t("specialChars",/[\u0000-\u001f\u007f\u00ad\u061c\u200b-\u200f\u2028\u2029\ufeff]/g,function(e,t,n){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),n!=wl&&e.refresh()}),t("specialCharPlaceholder",dt,function(e){return e.refresh()},!0),t("electricChars",!0),t("inputStyle",ha?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),t("spellcheck",!1,function(e,t){return e.getInputField().spellcheck=t},!0),t("rtlMoveVisually",!ma),t("wholeLineUpdateBefore",!0),t("theme","default",function(e){To(e),Ao(e)},!0),t("keyMap","default",function(e,t,n){var r=ro(t),i=n!=wl&&ro(n);i&&i.detach&&i.detach(e,r),r.attach&&r.attach(e,i||null)}),t("extraKeys",null),t("lineWrapping",!1,Eo,!0),t("gutters",[],function(e){Nr(e.options),Ao(e)},!0),t("fixedGutter",!0,function(e,t){e.display.gutters.style.left=t?bn(e.display)+"px":"0",e.refresh()},!0),t("coverGutterNextToScrollbar",!1,function(e){return Gn(e)},!0),t("scrollbarStyle","native",function(e){$n(e),Gn(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)},!0),t("lineNumbers",!1,function(e){Nr(e.options),Ao(e)},!0),t("firstLineNumber",1,Ao,!0),t("lineNumberFormatter",function(e){return e},Ao,!0),t("showCursorWhenSelecting",!1,Ln,!0),t("resetSelectionOnContextMenu",!0),t("lineWiseCopyCut",!0),t("readOnly",!1,function(e,t){"nocursor"==t?(Pn(e),e.display.input.blur(),e.display.disabled=!0):e.display.disabled=!1,e.display.input.readOnlyChanged(t)}),t("disableInput",!1,function(e,t){t||e.display.input.reset()},!0),t("dragDrop",!0,No),t("allowDropFileTypes",null),t("cursorBlinkRate",530),t("cursorScrollMargin",0),t("cursorHeight",1,Ln,!0),t("singleCursorHeightPerLine",!0,Ln,!0),t("workTime",100),t("workDelay",100),t("flattenSpans",!0,Fr,!0),t("addModeClass",!1,Fr,!0),t("pollInterval",100),t("undoDepth",200,function(e,t){return e.doc.history.undoDepth=t}),t("historyEventDelay",1250),t("viewportMargin",10,function(e){return e.refresh()},!0),t("maxHighlightLength",1e4,Fr,!0),t("moveInputWithCursor",!0,function(e,t){t||e.display.input.resetPosition()}),t("tabindex",null,function(e,t){return e.display.input.getField().tabIndex=t||""}),t("autofocus",null)}function Ao(e){Ar(e),dr(e),Rn(e)}function No(e,t,n){var r=n&&n!=wl;if(!t!=!r){var i=e.display.dragFunctions,o=t?Wa:Ne;o(e.display.scroller,"dragstart",i.start),o(e.display.scroller,"dragenter",i.enter),o(e.display.scroller,"dragover",i.over),o(e.display.scroller,"dragleave",i.leave),o(e.display.scroller,"drop",i.drop)}}function Eo(e){e.options.lineWrapping?(a(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(ba(e.display.wrapper,"CodeMirror-wrap"),ge(e)),kn(e),dr(e),an(e),setTimeout(function(){return Gn(e)},100)}function Oo(e,t){var n=this;if(!(this instanceof Oo))return new Oo(e,t);this.options=t=t?c(t):{},c(kl,t,!1),Nr(t);var r=t.value;"string"==typeof r&&(r=new sl(r,t.mode,null,t.lineSeparator)),this.doc=r;var i=new Oo.inputStyles[t.inputStyle](this),o=this.display=new C(e,r,i);o.wrapper.CodeMirror=this,Ar(this),To(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),$n(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:!1,cutIncoming:!1,selectingText:!1,draggingText:!1,highlight:new f,keySeq:null,specialChars:null},t.autofocus&&!ha&&o.input.focus(),na&&ra<11&&setTimeout(function(){return n.display.input.reset(!0)},20),Io(this),Ki(),tr(this),this.curOp.forceUpdate=!0,qr(this,r),t.autofocus&&!ha||this.hasFocus()?setTimeout(s(In,this),20):Pn(this);for(var a in Sl)Sl.hasOwnProperty(a)&&Sl[a](n,t[a],wl);Dn(this),t.finishInit&&t.finishInit(this);for(var l=0;l<Cl.length;++l)Cl[l](n);nr(this),ia&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}function Io(e){function t(){i.activeTouch&&(o=setTimeout(function(){return i.activeTouch=null},1e3),a=i.activeTouch,a.end=+new Date)}function n(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}function r(e,t){if(null==t.left)return!0;var n=t.left-e.left,r=t.top-e.top;return n*n+r*r>400}var i=e.display;Wa(i.scroller,"mousedown",ur(e,yo)),na&&ra<11?Wa(i.scroller,"dblclick",ur(e,function(t){if(!Oe(e,t)){var n=Sn(e,t);if(n&&!So(e,t)&&!_t(e.display,t)){De(t);var r=e.findWordAt(n);oi(e.doc,r.anchor,r.head)}}})):Wa(i.scroller,"dblclick",function(t){return Oe(e,t)||De(t)}),xa||Wa(i.scroller,"contextmenu",function(t){return Co(e,t)});var o,a={end:0};Wa(i.scroller,"touchstart",function(t){if(!Oe(e,t)&&!n(t)){i.input.ensurePolled(),clearTimeout(o);var r=+new Date;i.activeTouch={start:r,moved:!1,prev:r-a.end<=300?a:null},1==t.touches.length&&(i.activeTouch.left=t.touches[0].pageX,i.activeTouch.top=t.touches[0].pageY)}}),Wa(i.scroller,"touchmove",function(){i.activeTouch&&(i.activeTouch.moved=!0)}),Wa(i.scroller,"touchend",function(n){var o=i.activeTouch;if(o&&!_t(i,n)&&null!=o.left&&!o.moved&&new Date-o.start<300){var a,l=e.coordsChar(i.activeTouch,"page");a=!o.prev||r(o,o.prev)?new Or(l,l):!o.prev.prev||r(o,o.prev.prev)?e.findWordAt(l):new Or(P(l.line,0),_(e.doc,P(l.line+1,0))),e.setSelection(a.anchor,a.head),e.focus(),De(n)}t()}),Wa(i.scroller,"touchcancel",t),Wa(i.scroller,"scroll",function(){i.scroller.clientHeight&&(_n(e,i.scroller.scrollTop),Fn(e,i.scroller.scrollLeft,!0),Ee(e,"scroll",e))}),Wa(i.scroller,"mousewheel",function(t){return Un(e,t)}),Wa(i.scroller,"DOMMouseScroll",function(t){return Un(e,t)}),Wa(i.wrapper,"scroll",function(){return i.wrapper.scrollTop=i.wrapper.scrollLeft=0}),i.dragFunctions={enter:function(t){Oe(e,t)||Be(t)},over:function(t){Oe(e,t)||(Yi(e,t),Be(t))},start:function(t){return Gi(e,t)},drop:ur(e,qi),leave:function(t){Oe(e,t)||$i(e)}};var l=i.input.getField();Wa(l,"keyup",function(t){return go.call(e,t)}),Wa(l,"keydown",ur(e,po)),Wa(l,"keypress",ur(e,vo)),Wa(l,"focus",function(t){return In(e,t)}),Wa(l,"blur",function(t){return Pn(e,t)})}function Po(e,t,n,r){var i,o=e.doc;null==n&&(n="add"),"smart"==n&&(o.mode.indent?i=et(e,t):n="prev");var a=e.options.tabSize,l=L(o,t),s=u(l.text,null,a);l.stateAfter&&(l.stateAfter=null);var c,f=l.text.match(/^\s*/)[0];if(r||/\S/.test(l.text)){if("smart"==n&&(c=o.mode.indent(i,l.text.slice(f.length),l.text),c==La||c>150)){if(!r)return;n="prev"}}else c=0,n="not";"prev"==n?c=t>o.first?u(L(o,t-1).text,null,a):0:"add"==n?c=s+e.options.indentUnit:"subtract"==n?c=s-e.options.indentUnit:"number"==typeof n&&(c=s+n),c=Math.max(0,c);var h="",d=0;if(e.options.indentWithTabs)for(var m=Math.floor(c/a);m;--m)d+=a,h+="\t";if(d<c&&(h+=p(c-d)),h!=f)return Mi(o,h,P(t,0),P(t,f.length),"+input"),l.stateAfter=null,!0;for(var g=0;g<o.sel.ranges.length;g++){var v=o.sel.ranges[g];if(v.head.line==t&&v.head.ch<f.length){var y=P(t,f.length);li(o,g,new Or(y,y));break}}}function Ro(e){Ll=e}function Do(e,t,n,r,i){var o=e.doc;e.display.shift=!1,r||(r=o.sel);var a=e.state.pasteIncoming||"paste"==i,l=_a(t),s=null;if(a&&r.ranges.length>1)if(Ll&&Ll.text.join("\n")==t){if(r.ranges.length%Ll.text.length==0){s=[];for(var c=0;c<Ll.text.length;c++)s.push(o.splitLines(Ll.text[c]))}}else l.length==r.ranges.length&&(s=g(l,function(e){return[e]}));for(var u,f=r.ranges.length-1;f>=0;f--){var h=r.ranges[f],d=h.from(),p=h.to();h.empty()&&(n&&n>0?d=P(d.line,d.ch-n):e.state.overwrite&&!a?p=P(p.line,Math.min(L(o,p.line).text.length,p.ch+m(l).length)):Ll&&Ll.lineWise&&Ll.text.join("\n")==t&&(d=p=P(d.line,0))),u=e.curOp.updateInput;var v={from:d,to:p,text:s?s[f%s.length]:l,origin:i||(a?"paste":e.state.cutIncoming?"cut":"+input")};wi(e.doc,v),Ct(e,"inputRead",e,v)}t&&!a&&Wo(e,t),Qn(e),e.curOp.updateInput=u,e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=!1}function Ho(e,t){var n=e.clipboardData&&e.clipboardData.getData("Text");if(n)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||cr(t,function(){return Do(t,n,0,null,"paste")}),!0}function Wo(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var n=e.doc.sel,r=n.ranges.length-1;r>=0;r--){var i=n.ranges[r];if(!(i.head.ch>100||r&&n.ranges[r-1].head.line==i.head.line)){var o=e.getModeAt(i.head),a=!1;if(o.electricChars){for(var l=0;l<o.electricChars.length;l++)if(t.indexOf(o.electricChars.charAt(l))>-1){a=Po(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(L(e.doc,i.head.line).text.slice(0,i.head.ch))&&(a=Po(e,i.head.line,"smart"));a&&Ct(e,"electricInput",e,i.head.line)}}}function Bo(e){for(var t=[],n=[],r=0;r<e.doc.sel.ranges.length;r++){var i=e.doc.sel.ranges[r].head.line,o={anchor:P(i,0),head:P(i+1,0)};n.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:n}}function _o(e,t){e.setAttribute("autocorrect","off"),e.setAttribute("autocapitalize","off"),e.setAttribute("spellcheck",!!t)}function Fo(){var e=r("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; outline: none"),t=r("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return ia?e.style.width="1000px":e.setAttribute("wrap","off"),fa&&(e.style.border="1px solid black"),_o(e),t}function zo(e){var t=e.optionHandlers,n=e.helpers={};e.prototype={constructor:e,focus:function(){window.focus(),this.display.input.focus()},setOption:function(e,n){var r=this.options,i=r[e];r[e]==n&&"mode"!=e||(r[e]=n,t.hasOwnProperty(e)&&ur(this,t[e])(this,n,i),Ee(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](ro(e))},removeKeyMap:function(e){
for(var t=this.state.keyMaps,n=0;n<t.length;++n)if(t[n]==e||t[n].name==e)return t.splice(n,1),!0},addOverlay:fr(function(t,n){var r=t.token?t:e.getMode(this.options,t);if(r.startState)throw new Error("Overlays may not be stateful.");v(this.state.overlays,{mode:r,modeSpec:t,opaque:n&&n.opaque,priority:n&&n.priority||0},function(e){return e.priority}),this.state.modeGen++,dr(this)}),removeOverlay:fr(function(e){for(var t=this,n=this.state.overlays,r=0;r<n.length;++r){var i=n[r].modeSpec;if(i==e||"string"==typeof e&&i.name==e)return n.splice(r,1),t.state.modeGen++,void dr(t)}}),indentLine:fr(function(e,t,n){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),O(this.doc,e)&&Po(this,e,t,n)}),indentSelection:fr(function(e){for(var t=this,n=this.doc.sel.ranges,r=-1,i=0;i<n.length;i++){var o=n[i];if(o.empty())o.head.line>r&&(Po(t,o.head.line,e,!0),r=o.head.line,i==t.doc.sel.primIndex&&Qn(t));else{var a=o.from(),l=o.to(),s=Math.max(r,a.line);r=Math.min(t.lastLine(),l.line-(l.ch?0:1))+1;for(var c=s;c<r;++c)Po(t,c,e);var u=t.doc.sel.ranges;0==a.ch&&n.length==u.length&&u[i].from().ch>0&&li(t.doc,i,new Or(a,u[i].to()),Ta)}}}),getTokenAt:function(e,t){return it(this,e,t)},getLineTokens:function(e,t){return it(this,P(e),t,!0)},getTokenTypeAt:function(e){e=_(this.doc,e);var t,n=Qe(this,L(this.doc,e.line)),r=0,i=(n.length-1)/2,o=e.ch;if(0==o)t=n[2];else for(;;){var a=r+i>>1;if((a?n[2*a-1]:0)>=o)i=a;else{if(!(n[2*a+1]<o)){t=n[2*a+2];break}r=a+1}}var l=t?t.indexOf("overlay "):-1;return l<0?t:0==l?null:t.slice(0,l-1)},getModeAt:function(t){var n=this.doc.mode;return n.innerMode?e.innerMode(n,this.getTokenAt(t).state).mode:n},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var r=this,i=[];if(!n.hasOwnProperty(t))return i;var o=n[t],a=this.getModeAt(e);if("string"==typeof a[t])o[a[t]]&&i.push(o[a[t]]);else if(a[t])for(var l=0;l<a[t].length;l++){var s=o[a[t][l]];s&&i.push(s)}else a.helperType&&o[a.helperType]?i.push(o[a.helperType]):o[a.name]&&i.push(o[a.name]);for(var c=0;c<o._global.length;c++){var u=o._global[c];u.pred(a,r)&&h(i,u.val)==-1&&i.push(u.val)}return i},getStateAfter:function(e,t){var n=this.doc;return e=B(n,null==e?n.first+n.size-1:e),et(this,e+1,t)},cursorCoords:function(e,t){var n,r=this.doc.sel.primary();return n=null==e?r.head:"object"==typeof e?_(this.doc,e):e?r.from():r.to(),hn(this,n,t||"page")},charCoords:function(e,t){return fn(this,_(this.doc,e),t||"page")},coordsChar:function(e,t){return e=un(this,e,t||"page"),mn(this,e.left,e.top)},lineAtHeight:function(e,t){return e=un(this,{top:e,left:0},t||"page").top,E(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,n){var r,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,i=!0),r=L(this.doc,e)}else r=e;return cn(this,r,{top:0,left:0},t||"page",n).top+(i?this.doc.height-pe(r):0)},defaultTextHeight:function(){return vn(this.display)},defaultCharWidth:function(){return yn(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,n,r,i){var o=this.display;e=hn(this,_(this.doc,e));var a=e.bottom,l=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),o.sizer.appendChild(t),"over"==r)a=e.top;else if("above"==r||"near"==r){var s=Math.max(o.wrapper.clientHeight,this.doc.height),c=Math.max(o.sizer.clientWidth,o.lineSpace.clientWidth);("above"==r||e.bottom+t.offsetHeight>s)&&e.top>t.offsetHeight?a=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=s&&(a=e.bottom),l+t.offsetWidth>c&&(l=c-t.offsetWidth)}t.style.top=a+"px",t.style.left=t.style.right="","right"==i?(l=o.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?l=0:"middle"==i&&(l=(o.sizer.clientWidth-t.offsetWidth)/2),t.style.left=l+"px"),n&&Xn(this,l,a,l+t.offsetWidth,a+t.offsetHeight)},triggerOnKeyDown:fr(po),triggerOnKeyPress:fr(vo),triggerOnKeyUp:go,execCommand:function(e){if(yl.hasOwnProperty(e))return yl[e].call(null,this)},triggerElectric:fr(function(e){Wo(this,e)}),findPosH:function(e,t,n,r){var i=this,o=1;t<0&&(o=-1,t=-t);for(var a=_(this.doc,e),l=0;l<t&&(a=jo(i.doc,a,o,n,r),!a.hitSide);++l);return a},moveH:fr(function(e,t){var n=this;this.extendSelectionsBy(function(r){return n.display.shift||n.doc.extend||r.empty()?jo(n.doc,r.head,e,t,n.options.rtlMoveVisually):e<0?r.from():r.to()},Aa)}),deleteH:fr(function(e,t){var n=this.doc.sel,r=this.doc;n.somethingSelected()?r.replaceSelection("",null,"+delete"):io(this,function(n){var i=jo(r,n.head,e,t,!1);return e<0?{from:i,to:n.head}:{from:n.head,to:i}})}),findPosV:function(e,t,n,r){var i=this,o=1,a=r;t<0&&(o=-1,t=-t);for(var l=_(this.doc,e),s=0;s<t;++s){var c=hn(i,l,"div");if(null==a?a=c.left:c.left=a,l=Uo(i,c,o,n),l.hitSide)break}return l},moveV:fr(function(e,t){var n=this,r=this.doc,i=[],o=!this.display.shift&&!r.extend&&r.sel.somethingSelected();if(r.extendSelectionsBy(function(a){if(o)return e<0?a.from():a.to();var l=hn(n,a.head,"div");null!=a.goalColumn&&(l.left=a.goalColumn),i.push(l.left);var s=Uo(n,l,e,t);return"page"==t&&a==r.sel.primary()&&Jn(n,null,fn(n,s,"div").top-l.top),s},Aa),i.length)for(var a=0;a<r.sel.ranges.length;a++)r.sel.ranges[a].goalColumn=i[a]}),findWordAt:function(e){var t=this.doc,n=L(t,e.line).text,r=e.ch,i=e.ch;if(n){var o=this.getHelper(e,"wordChars");(e.xRel<0||i==n.length)&&r?--r:++i;for(var a=n.charAt(r),l=w(a,o)?function(e){return w(e,o)}:/\s/.test(a)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!w(e)};r>0&&l(n.charAt(r-1));)--r;for(;i<n.length&&l(n.charAt(i));)++i}return new Or(P(e.line,r),P(e.line,i))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?a(this.display.cursorDiv,"CodeMirror-overwrite"):ba(this.display.cursorDiv,"CodeMirror-overwrite"),Ee(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==o()},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:fr(function(e,t){null==e&&null==t||er(this),null!=e&&(this.curOp.scrollLeft=e),null!=t&&(this.curOp.scrollTop=t)}),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-Ut(this)-this.display.barHeight,width:e.scrollWidth-Ut(this)-this.display.barWidth,clientHeight:Gt(this),clientWidth:qt(this)}},scrollIntoView:fr(function(e,t){if(null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:P(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line)er(this),this.curOp.scrollToPos=e;else{var n=Zn(this,Math.min(e.from.left,e.to.left),Math.min(e.from.top,e.to.top)-e.margin,Math.max(e.from.right,e.to.right),Math.max(e.from.bottom,e.to.bottom)+e.margin);this.scrollTo(n.scrollLeft,n.scrollTop)}}),setSize:fr(function(e,t){var n=this,r=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=r(e)),null!=t&&(this.display.wrapper.style.height=r(t)),this.options.lineWrapping&&on(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){pr(n,i,"widget");break}++i}),this.curOp.forceUpdate=!0,Ee(this,"refresh",this)}),operation:function(e){return cr(this,e)},refresh:fr(function(){var e=this.display.cachedTextHeight;dr(this),this.curOp.forceUpdate=!0,an(this),this.scrollTo(this.doc.scrollLeft,this.doc.scrollTop),Tr(this),(null==e||Math.abs(e-vn(this.display))>.5)&&kn(this),Ee(this,"refresh",this)}),swapDoc:fr(function(e){var t=this.doc;return t.cm=null,qr(this,e),an(this),this.display.input.reset(),this.scrollTo(e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,Ct(this,"swapDoc",this,t),t}),getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},Re(e),e.registerHelper=function(t,r,i){n.hasOwnProperty(t)||(n[t]=e[t]={_global:[]}),n[t][r]=i},e.registerGlobalHelper=function(t,r,i,o){e.registerHelper(t,r,o),n[t]._global.push({pred:i,val:o})}}function jo(e,t,n,r,i){function o(){var t=l+n;return!(t<e.first||t>=e.first+e.size)&&(l=t,u=L(e,t))}function a(e){var t=(i?Le:Te)(u,s,n,!0);if(null==t){if(e||!o())return!1;s=i?(n<0?we:be)(u):n<0?u.text.length:0}else s=t;return!0}var l=t.line,s=t.ch,c=n,u=L(e,l);if("char"==r)a();else if("column"==r)a(!0);else if("word"==r||"group"==r)for(var f=null,h="group"==r,d=e.cm&&e.cm.getHelper(t,"wordChars"),p=!0;!(n<0)||a(!p);p=!1){var m=u.text.charAt(s)||"\n",g=w(m,d)?"w":h&&"\n"==m?"n":!h||/\s/.test(m)?null:"p";if(!h||p||g||(g="s"),f&&f!=g){n<0&&(n=1,a());break}if(g&&(f=g),n>0&&!a(!p))break}var v=vi(e,P(l,s),t,c,!0);return R(t,v)||(v.hitSide=!0),v}function Uo(e,t,n,r){var i,o=e.doc,a=t.left;if("page"==r){var l=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),s=Math.max(l-.5*vn(e.display),3);i=(n>0?t.bottom:t.top)+n*s}else"line"==r&&(i=n>0?t.bottom+3:t.top-3);for(var c;c=mn(e,a,i),c.outside;){if(n<0?i<=0:i>=o.height){c.hitSide=!0;break}i+=5*n}return c}function qo(e,t){var n=Xt(e,t.line);if(!n||n.hidden)return null;var r=L(e.doc,t.line),i=$t(n,r,t.line),o=Me(r),a="left";if(o){var l=Se(o,t.ch);a=l%2?"right":"left"}var s=Qt(i.map,t.ch,a);return s.offset="right"==s.collapse?s.end:s.start,s}function Go(e,t){return t&&(e.bad=!0),e}function Yo(e,t,n,r,i){function o(e){return function(t){return t.id==e}}function a(t){if(1==t.nodeType){var n=t.getAttribute("cm-text");if(null!=n)return void(l+=""==n?t.textContent.replace(/\u200b/g,""):n);var u,f=t.getAttribute("cm-marker");if(f){var h=e.findMarks(P(r,0),P(i+1,0),o(+f));return void(h.length&&(u=h[0].find())&&(l+=T(e.doc,u.from,u.to).join(c)))}if("false"==t.getAttribute("contenteditable"))return;for(var d=0;d<t.childNodes.length;d++)a(t.childNodes[d]);/^(pre|div|p)$/i.test(t.nodeName)&&(s=!0)}else if(3==t.nodeType){var p=t.nodeValue;if(!p)return;s&&(l+=c,s=!1),l+=p}}for(var l="",s=!1,c=e.doc.lineSeparator();a(t),t!=n;)t=t.nextSibling;return l}function $o(e,t,n){var r;if(t==e.display.lineDiv){if(r=e.display.lineDiv.childNodes[n],!r)return Go(e.clipPos(P(e.display.viewTo-1)),!0);t=null,n=0}else for(r=t;;r=r.parentNode){if(!r||r==e.display.lineDiv)return null;if(r.parentNode&&r.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==r)return Vo(o,t,n)}}function Vo(e,t,n){function r(t,n,r){for(var i=-1;i<(f?f.length:0);i++)for(var o=i<0?u.map:f[i],a=0;a<o.length;a+=3){var l=o[a+2];if(l==t||l==n){var s=N(i<0?e.line:e.rest[i]),c=o[a]+r;return(r<0||l!=t)&&(c=o[a+(r?1:0)]),P(s,c)}}}var o=e.text.firstChild,a=!1;if(!t||!i(o,t))return Go(P(N(e.line),0),!0);if(t==o&&(a=!0,t=o.childNodes[n],n=0,!t)){var l=e.rest?m(e.rest):e.line;return Go(P(N(l),l.text.length),a)}var s=3==t.nodeType?t:null,c=t;for(s||1!=t.childNodes.length||3!=t.firstChild.nodeType||(s=t.firstChild,n&&(n=s.nodeValue.length));c.parentNode!=o;)c=c.parentNode;var u=e.measure,f=u.maps,h=r(s,c,n);if(h)return Go(h,a);for(var d=c.nextSibling,p=s?s.nodeValue.length-n:0;d;d=d.nextSibling){if(h=r(d,d.firstChild,0))return Go(P(h.line,h.ch-p),a);p+=d.textContent.length}for(var g=c.previousSibling,v=n;g;g=g.previousSibling){if(h=r(g,g.firstChild,-1))return Go(P(h.line,h.ch+v),a);v+=g.textContent.length}}function Ko(e,t){function n(){e.value=s.getValue()}if(t=t?c(t):{},t.value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),null==t.autofocus){var r=o();t.autofocus=r==e||null!=e.getAttribute("autofocus")&&r==document.body}var i;if(e.form&&(Wa(e.form,"submit",n),!t.leaveSubmitMethodAlone)){var a=e.form;i=a.submit;try{var l=a.submit=function(){n(),a.submit=i,a.submit(),a.submit=l}}catch(e){}}t.finishInit=function(t){t.save=n,t.getTextArea=function(){return e},t.toTextArea=function(){t.toTextArea=isNaN,n(),e.parentNode.removeChild(t.getWrapperElement()),e.style.display="",e.form&&(Ne(e.form,"submit",n),"function"==typeof e.form.submit&&(e.form.submit=i))}},e.style.display="none";var s=Oo(function(t){return e.parentNode.insertBefore(t,e.nextSibling)},t);return s}function Xo(e){e.off=Ne,e.on=Wa,e.wheelEventPixels=jn,e.Doc=sl,e.splitLines=_a,e.countColumn=u,e.findColumn=d,e.isWordChar=b,e.Pass=La,e.signal=Ee,e.Line=st,e.changeEnd=Rr,e.scrollbarModel=rl,e.Pos=P,e.cmpPos=R,e.modes=Ua,e.mimeModes=qa,e.resolveMode=Ye,e.getMode=$e,e.modeExtensions=Ga,e.extendMode=Ve,e.copyState=Ke,e.startState=Ze,e.innerMode=Xe,e.commands=yl,e.keyMap=ml,e.keyName=no,e.isModifierKey=to,e.lookupKey=eo,e.normalizeKeyMap=Qi,e.StringStream=Ya,e.SharedTextMarker=_i,e.TextMarker=Wi,e.LineWidget=Ri,e.e_preventDefault=De,e.e_stopPropagation=He,e.e_stop=Be,e.addClass=a,e.contains=i,e.rmClass=ba,e.keyNames=fl}var Zo=navigator.userAgent,Jo=navigator.platform,Qo=/gecko\/\d/i.test(Zo),ea=/MSIE \d/.test(Zo),ta=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(Zo),na=ea||ta,ra=na&&(ea?document.documentMode||6:ta[1]),ia=/WebKit\//.test(Zo),oa=ia&&/Qt\/\d+\.\d+/.test(Zo),aa=/Chrome\//.test(Zo),la=/Opera\//.test(Zo),sa=/Apple Computer/.test(navigator.vendor),ca=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(Zo),ua=/PhantomJS/.test(Zo),fa=/AppleWebKit/.test(Zo)&&/Mobile\/\w+/.test(Zo),ha=fa||/Android|webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(Zo),da=fa||/Mac/.test(Jo),pa=/\bCrOS\b/.test(Zo),ma=/win/i.test(Jo),ga=la&&Zo.match(/Version\/(\d*\.\d*)/);ga&&(ga=Number(ga[1])),ga&&ga>=15&&(la=!1,ia=!0);var va,ya=da&&(oa||la&&(null==ga||ga<12.11)),xa=Qo||na&&ra>=9,ba=function(t,n){var r=t.className,i=e(n).exec(r);if(i){var o=r.slice(i.index+i[0].length);t.className=r.slice(0,i.index)+(o?i[1]+o:"")}};va=document.createRange?function(e,t,n,r){var i=document.createRange();return i.setEnd(r||e,n),i.setStart(e,t),i}:function(e,t,n){var r=document.body.createTextRange();try{r.moveToElementText(e.parentNode)}catch(e){return r}return r.collapse(!0),r.moveEnd("character",n),r.moveStart("character",t),r};var wa=function(e){e.select()};fa?wa=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:na&&(wa=function(e){try{e.select()}catch(e){}}),f.prototype.set=function(e,t){clearTimeout(this.id),this.id=setTimeout(t,e)};var ka,Sa,Ca=30,La={toString:function(){return"CodeMirror.Pass"}},Ta={scroll:!1},Ma={origin:"*mouse"},Aa={origin:"+move"},Na=[""],Ea=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/,Oa=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/,Ia=!1,Pa=!1,Ra=null,Da=function(){function e(e){return e<=247?n.charAt(e):1424<=e&&e<=1524?"R":1536<=e&&e<=1785?r.charAt(e-1536):1774<=e&&e<=2220?"r":8192<=e&&e<=8203?"w":8204==e?"b":"L"}function t(e,t,n){this.level=e,this.from=t,this.to=n}var n="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",r="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111",i=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,o=/[stwN]/,a=/[LRr]/,l=/[Lb1n]/,s=/[1n]/,c="L";return function(n){if(!i.test(n))return!1;for(var r=n.length,u=[],f=0;f<r;++f)u.push(e(n.charCodeAt(f)));for(var h=0,d=c;h<r;++h){var p=u[h];"m"==p?u[h]=d:d=p}for(var g=0,v=c;g<r;++g){var y=u[g];"1"==y&&"r"==v?u[g]="n":a.test(y)&&(v=y,"r"==y&&(u[g]="R"))}for(var x=1,b=u[0];x<r-1;++x){var w=u[x];"+"==w&&"1"==b&&"1"==u[x+1]?u[x]="1":","!=w||b!=u[x+1]||"1"!=b&&"n"!=b||(u[x]=b),b=w}for(var k=0;k<r;++k){var S=u[k];if(","==S)u[k]="N";else if("%"==S){var C=void 0;for(C=k+1;C<r&&"%"==u[C];++C);for(var L=k&&"!"==u[k-1]||C<r&&"1"==u[C]?"1":"N",T=k;T<C;++T)u[T]=L;k=C-1}}for(var M=0,A=c;M<r;++M){var N=u[M];"L"==A&&"1"==N?u[M]="L":a.test(N)&&(A=N)}for(var E=0;E<r;++E)if(o.test(u[E])){var O=void 0;for(O=E+1;O<r&&o.test(u[O]);++O);for(var I="L"==(E?u[E-1]:c),P="L"==(O<r?u[O]:c),R=I||P?"L":"R",D=E;D<O;++D)u[D]=R;E=O-1}for(var H,W=[],B=0;B<r;)if(l.test(u[B])){var _=B;for(++B;B<r&&l.test(u[B]);++B);W.push(new t(0,_,B))}else{var F=B,z=W.length;for(++B;B<r&&"L"!=u[B];++B);for(var j=F;j<B;)if(s.test(u[j])){F<j&&W.splice(z,0,new t(1,F,j));var U=j;for(++j;j<B&&s.test(u[j]);++j);W.splice(z,0,new t(2,U,j)),F=j}else++j;F<B&&W.splice(z,0,new t(1,F,B))}return 1==W[0].level&&(H=n.match(/^\s+/))&&(W[0].from=H[0].length,W.unshift(new t(0,0,H[0].length))),1==m(W).level&&(H=n.match(/\s+$/))&&(m(W).to-=H[0].length,W.push(new t(0,r-H[0].length,r))),2==W[0].level&&W.unshift(new t(1,W[0].to,W[0].to)),W[0].level!=m(W).level&&W.push(new t(W[0].level,r,r)),W}}(),Ha=[],Wa=function(e,t,n){if(e.addEventListener)e.addEventListener(t,n,!1);else if(e.attachEvent)e.attachEvent("on"+t,n);else{var r=e._handlers||(e._handlers={});r[t]=(r[t]||Ha).concat(n)}},Ba=function(){if(na&&ra<9)return!1;var e=r("div");return"draggable"in e||"dragDrop"in e}(),_a=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,n=[],r=e.length;t<=r;){var i=e.indexOf("\n",t);i==-1&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),a=o.indexOf("\r");a!=-1?(n.push(o.slice(0,a)),t+=a+1):(n.push(o),t=i+1)}return n}:function(e){return e.split(/\r\n?|\n/)},Fa=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},za=function(){var e=r("div");return"oncopy"in e||(e.setAttribute("oncopy","return;"),"function"==typeof e.oncopy)}(),ja=null,Ua={},qa={},Ga={},Ya=function(e,t){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0};Ya.prototype={eol:function(){return this.pos>=this.string.length},sol:function(){return this.pos==this.lineStart},peek:function(){return this.string.charAt(this.pos)||void 0},next:function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},eat:function(e){var t,n=this.string.charAt(this.pos);if(t="string"==typeof e?n==e:n&&(e.test?e.test(n):e(n)))return++this.pos,n},eatWhile:function(e){for(var t=this.pos;this.eat(e););return this.pos>t},eatSpace:function(){for(var e=this,t=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++e.pos;return this.pos>t},skipToEnd:function(){this.pos=this.string.length},skipTo:function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},backUp:function(e){this.pos-=e},column:function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=u(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?u(this.string,this.lineStart,this.tabSize):0)},indentation:function(){return u(this.string,null,this.tabSize)-(this.lineStart?u(this.string,this.lineStart,this.tabSize):0)},match:function(e,t,n){if("string"!=typeof e){var r=this.string.slice(this.pos).match(e);return r&&r.index>0?null:(r&&t!==!1&&(this.pos+=r[0].length),r)}var i=function(e){return n?e.toLowerCase():e},o=this.string.substr(this.pos,e.length);if(i(o)==i(e))return t!==!1&&(this.pos+=e.length),!0},current:function(){return this.string.slice(this.start,this.pos)},hideFirstChars:function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}}},Re(st),st.prototype.lineNo=function(){return N(this)};var $a,Va={},Ka={},Xa=null,Za=null,Ja={left:0,right:0,top:0,bottom:0},Qa=0,el=null;na?el=-.53:Qo?el=15:aa?el=-.7:sa&&(el=-1/3);var tl=function(e,t,n){this.cm=n;var i=this.vert=r("div",[r("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),o=this.horiz=r("div",[r("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");e(i),e(o),Wa(i,"scroll",function(){i.clientHeight&&t(i.scrollTop,"vertical")}),Wa(o,"scroll",function(){o.clientWidth&&t(o.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,na&&ra<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};tl.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,n=e.scrollHeight>e.clientHeight+1,r=e.nativeBarWidth;if(n){this.vert.style.display="block",this.vert.style.bottom=t?r+"px":"0";var i=e.viewHeight-(t?r:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=n?r+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(n?r:0);this.horiz.firstChild.style.width=e.scrollWidth-e.clientWidth+o+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==r&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:n?r:0,bottom:t?r:0}},tl.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz)},tl.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert)},tl.prototype.zeroWidthHack=function(){var e=da&&!ca?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new f,this.disableVert=new f},tl.prototype.enableZeroWidthBar=function(e,t){function n(){var r=e.getBoundingClientRect(),i=document.elementFromPoint(r.left+1,r.bottom-1);i!=e?e.style.pointerEvents="none":t.set(1e3,n)}e.style.pointerEvents="auto",t.set(1e3,n)},tl.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var nl=function(){};nl.prototype.update=function(){return{bottom:0,right:0}},nl.prototype.setScrollLeft=function(){},nl.prototype.setScrollTop=function(){},nl.prototype.clear=function(){};var rl={native:tl,null:nl},il=0,ol=function(e,t,n){var r=e.display;this.viewport=t,this.visible=Bn(r,e.doc,t),this.editorIsHidden=!r.wrapper.offsetWidth,this.wrapperHeight=r.wrapper.clientHeight,this.wrapperWidth=r.wrapper.clientWidth,this.oldDisplayWidth=qt(e),this.force=n,this.dims=xn(e),this.events=[]};ol.prototype.signal=function(e,t){Pe(e,t)&&this.events.push(arguments)},ol.prototype.finish=function(){for(var e=this,t=0;t<this.events.length;t++)Ee.apply(null,e.events[t])},Er.prototype={primary:function(){return this.ranges[this.primIndex]},equals:function(e){var t=this;if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var n=0;n<this.ranges.length;n++){var r=t.ranges[n],i=e.ranges[n];if(0!=R(r.anchor,i.anchor)||0!=R(r.head,i.head))return!1}return!0},deepCopy:function(){for(var e=this,t=[],n=0;n<this.ranges.length;n++)t[n]=new Or(D(e.ranges[n].anchor),D(e.ranges[n].head));return new Er(t,this.primIndex)},somethingSelected:function(){for(var e=this,t=0;t<this.ranges.length;t++)if(!e.ranges[t].empty())return!0;return!1},contains:function(e,t){var n=this;t||(t=e);for(var r=0;r<this.ranges.length;r++){var i=n.ranges[r];if(R(t,i.from())>=0&&R(e,i.to())<=0)return r}return-1}},Or.prototype={from:function(){return W(this.anchor,this.head)},to:function(){return H(this.anchor,this.head)},empty:function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch}},Ii.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var n=this,r=e,i=e+t;r<i;++r){var o=n.lines[r];n.height-=o.height,ut(o),Ct(o,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,n){var r=this;this.height+=n,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var i=0;i<t.length;++i)t[i].parent=r},iterN:function(e,t,n){for(var r=this,i=e+t;e<i;++e)if(n(r.lines[e]))return!0}},Pi.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){var n=this;this.size-=t;for(var r=0;r<this.children.length;++r){var i=n.children[r],o=i.chunkSize();if(e<o){var a=Math.min(t,o-e),l=i.height;if(i.removeInner(e,a),n.height-=l-i.height,o==a&&(n.children.splice(r--,1),i.parent=null),0==(t-=a))break;e=0}else e-=o}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof Ii))){var s=[];this.collapse(s),this.children=[new Ii(s)],this.children[0].parent=this}},collapse:function(e){for(var t=this,n=0;n<this.children.length;++n)t.children[n].collapse(e)},insertInner:function(e,t,n){var r=this;this.size+=t.length,this.height+=n;for(var i=0;i<this.children.length;++i){var o=r.children[i],a=o.chunkSize();if(e<=a){if(o.insertInner(e,t,n),o.lines&&o.lines.length>50){for(var l=o.lines.length%25+25,s=l;s<o.lines.length;){var c=new Ii(o.lines.slice(s,s+=25));o.height-=c.height,r.children.splice(++i,0,c),c.parent=r}o.lines=o.lines.slice(0,l),r.maybeSpill()}break}e-=a}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=e.children.splice(e.children.length-5,5),n=new Pi(t);if(e.parent){e.size-=n.size,e.height-=n.height;var r=h(e.parent.children,e);e.parent.children.splice(r+1,0,n)}else{var i=new Pi(e.children);i.parent=e,e.children=[i,n],e=i}n.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,n){for(var r=this,i=0;i<this.children.length;++i){var o=r.children[i],a=o.chunkSize();if(e<a){var l=Math.min(t,a-e);if(o.iterN(e,l,n))return!0;if(0==(t-=l))break;e=0}else e-=a}}},Re(Ri),Ri.prototype.clear=function(){var e=this,t=this.doc.cm,n=this.line.widgets,r=this.line,i=N(r);if(null!=i&&n){for(var o=0;o<n.length;++o)n[o]==e&&n.splice(o--,1);n.length||(r.widgets=null);var a=Bt(this);A(r,Math.max(0,r.height-a)),t&&cr(t,function(){Di(t,r,-a),pr(t,i,"widget")})}},Ri.prototype.changed=function(){var e=this.height,t=this.doc.cm,n=this.line;this.height=null;var r=Bt(this)-e;r&&(A(n,n.height+r),t&&cr(t,function(){t.curOp.forceUpdate=!0,Di(t,n,r)}))};var al=0;Re(Wi),Wi.prototype.clear=function(){var e=this;if(!this.explicitlyCleared){var t=this.doc.cm,n=t&&!t.curOp;if(n&&tr(t),Pe(this,"clear")){var r=this.find();r&&Ct(this,"clear",r.from,r.to)}for(var i=null,o=null,a=0;a<this.lines.length;++a){var l=e.lines[a],s=G(l.markedSpans,e);t&&!e.collapsed?pr(t,N(l),"text"):t&&(null!=s.to&&(o=N(l)),null!=s.from&&(i=N(l))),l.markedSpans=Y(l.markedSpans,s),null==s.from&&e.collapsed&&!he(e.doc,l)&&t&&A(l,vn(t.display))}if(t&&this.collapsed&&!t.options.lineWrapping)for(var c=0;c<this.lines.length;++c){var u=se(e.lines[c]),f=me(u);f>t.display.maxLineLength&&(t.display.maxLine=u,t.display.maxLineLength=f,t.display.maxLineChanged=!0)}null!=i&&t&&this.collapsed&&dr(t,i,o+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,t&&pi(t.doc)),t&&Ct(t,"markerCleared",t,this),n&&nr(t),this.parent&&this.parent.clear()}},Wi.prototype.find=function(e,t){var n=this;null==e&&"bookmark"==this.type&&(e=1);for(var r,i,o=0;o<this.lines.length;++o){var a=n.lines[o],l=G(a.markedSpans,n);if(null!=l.from&&(r=P(t?a:N(a),l.from),e==-1))return r;if(null!=l.to&&(i=P(t?a:N(a),l.to),1==e))return i}return r&&{from:r,to:i}},Wi.prototype.changed=function(){var e=this.find(-1,!0),t=this,n=this.doc.cm;e&&n&&cr(n,function(){var r=e.line,i=N(e.line),o=Xt(n,i);if(o&&(rn(o),n.curOp.selectionChanged=n.curOp.forceUpdate=!0),n.curOp.updateMaxLine=!0,!he(t.doc,r)&&null!=t.height){var a=t.height;t.height=null;var l=Bt(t)-a;l&&A(r,r.height+l)}})},Wi.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&h(t.maybeHiddenMarkers,this)!=-1||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},Wi.prototype.detachLine=function(e){if(this.lines.splice(h(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},Re(_i),_i.prototype.clear=function(){var e=this;if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var t=0;t<this.markers.length;++t)e.markers[t].clear();Ct(this,"clear")}},_i.prototype.find=function(e,t){return this.primary.find(e,t)};var ll=0,sl=function(e,t,n,r){if(!(this instanceof sl))return new sl(e,t,n,r);null==n&&(n=0),Pi.call(this,[new Ii([new st("",null)])]),this.first=n,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.frontier=n;var i=P(n,0);this.sel=Pr(i),this.history=new Gr(null),this.id=++ll,this.modeOption=t,this.lineSep=r,this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),jr(this,{from:i,to:i,text:e}),fi(this,Pr(i),Ta)};sl.prototype=x(Pi.prototype,{constructor:sl,iter:function(e,t,n){n?this.iterN(e-this.first,t-e,n):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var n=0,r=0;r<t.length;++r)n+=t[r].height;this.insertInner(e-this.first,t,n)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=M(this,this.first,this.first+this.size);return e===!1?t:t.join(e||this.lineSeparator())},setValue:hr(function(e){var t=P(this.first,0),n=this.first+this.size-1;wi(this,{from:t,to:P(n,L(this,n).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),fi(this,Pr(t))}),replaceRange:function(e,t,n,r){t=_(this,t),n=n?_(this,n):t,Mi(this,e,t,n,r)},getRange:function(e,t,n){var r=T(this,_(this,e),_(this,t));return n===!1?r:r.join(n||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(O(this,e))return L(this,e)},getLineNumber:function(e){return N(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=L(this,e)),se(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return _(this,e)},getCursor:function(e){
var t,n=this.sel.primary();return t=null==e||"head"==e?n.head:"anchor"==e?n.anchor:"end"==e||"to"==e||e===!1?n.to():n.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:hr(function(e,t,n){si(this,_(this,"number"==typeof e?P(e,t||0):e),null,n)}),setSelection:hr(function(e,t,n){si(this,_(this,e),_(this,t||e),n)}),extendSelection:hr(function(e,t,n){oi(this,_(this,e),t&&_(this,t),n)}),extendSelections:hr(function(e,t){ai(this,z(this,e),t)}),extendSelectionsBy:hr(function(e,t){var n=g(this.sel.ranges,e);ai(this,z(this,n),t)}),setSelections:hr(function(e,t,n){var r=this;if(e.length){for(var i=[],o=0;o<e.length;o++)i[o]=new Or(_(r,e[o].anchor),_(r,e[o].head));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),fi(this,Ir(i,t),n)}}),addSelection:hr(function(e,t,n){var r=this.sel.ranges.slice(0);r.push(new Or(_(this,e),_(this,t||e))),fi(this,Ir(r,r.length-1),n)}),getSelection:function(e){for(var t,n=this,r=this.sel.ranges,i=0;i<r.length;i++){var o=T(n,r[i].from(),r[i].to());t=t?t.concat(o):o}return e===!1?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=this,n=[],r=this.sel.ranges,i=0;i<r.length;i++){var o=T(t,r[i].from(),r[i].to());e!==!1&&(o=o.join(e||t.lineSeparator())),n[i]=o}return n},replaceSelection:function(e,t,n){for(var r=[],i=0;i<this.sel.ranges.length;i++)r[i]=e;this.replaceSelections(r,t,n||"+input")},replaceSelections:hr(function(e,t,n){for(var r=this,i=[],o=this.sel,a=0;a<o.ranges.length;a++){var l=o.ranges[a];i[a]={from:l.from(),to:l.to(),text:r.splitLines(e[a]),origin:n}}for(var s=t&&"end"!=t&&Br(this,i,t),c=i.length-1;c>=0;c--)wi(r,i[c]);s?ui(this,s):this.cm&&Qn(this.cm)}),undo:hr(function(){Si(this,"undo")}),redo:hr(function(){Si(this,"redo")}),undoSelection:hr(function(){Si(this,"undo",!0)}),redoSelection:hr(function(){Si(this,"redo",!0)}),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,n=0,r=0;r<e.done.length;r++)e.done[r].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++n;return{undo:t,redo:n}},clearHistory:function(){this.history=new Gr(this.history.maxGeneration)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:ri(this.history.done),undone:ri(this.history.undone)}},setHistory:function(e){var t=this.history=new Gr(this.history.maxGeneration);t.done=ri(e.done.slice(0),null,!0),t.undone=ri(e.undone.slice(0),null,!0)},setGutterMarker:hr(function(e,t,n){return Oi(this,e,"gutter",function(e){var r=e.gutterMarkers||(e.gutterMarkers={});return r[t]=n,!n&&k(r)&&(e.gutterMarkers=null),!0})}),clearGutter:hr(function(e){var t=this,n=this.first;this.iter(function(r){r.gutterMarkers&&r.gutterMarkers[e]&&Oi(t,r,"gutter",function(){return r.gutterMarkers[e]=null,k(r.gutterMarkers)&&(r.gutterMarkers=null),!0}),++n})}),lineInfo:function(e){var t;if("number"==typeof e){if(!O(this,e))return null;if(t=e,e=L(this,e),!e)return null}else if(t=N(e),null==t)return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:hr(function(t,n,r){return Oi(this,t,"gutter"==n?"gutter":"class",function(t){var i="text"==n?"textClass":"background"==n?"bgClass":"gutter"==n?"gutterClass":"wrapClass";if(t[i]){if(e(r).test(t[i]))return!1;t[i]+=" "+r}else t[i]=r;return!0})}),removeLineClass:hr(function(t,n,r){return Oi(this,t,"gutter"==n?"gutter":"class",function(t){var i="text"==n?"textClass":"background"==n?"bgClass":"gutter"==n?"gutterClass":"wrapClass",o=t[i];if(!o)return!1;if(null==r)t[i]=null;else{var a=o.match(e(r));if(!a)return!1;var l=a.index+a[0].length;t[i]=o.slice(0,a.index)+(a.index&&l!=o.length?" ":"")+o.slice(l)||null}return!0})}),addLineWidget:hr(function(e,t,n){return Hi(this,e,t,n)}),removeLineWidget:function(e){e.clear()},markText:function(e,t,n){return Bi(this,_(this,e),_(this,t),n,n&&n.type||"range")},setBookmark:function(e,t){var n={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return e=_(this,e),Bi(this,e,e,n,"bookmark")},findMarksAt:function(e){e=_(this,e);var t=[],n=L(this,e.line).markedSpans;if(n)for(var r=0;r<n.length;++r){var i=n[r];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,n){e=_(this,e),t=_(this,t);var r=[],i=e.line;return this.iter(e.line,t.line+1,function(o){var a=o.markedSpans;if(a)for(var l=0;l<a.length;l++){var s=a[l];null!=s.to&&i==e.line&&e.ch>=s.to||null==s.from&&i!=e.line||null!=s.from&&i==t.line&&s.from>=t.ch||n&&!n(s.marker)||r.push(s.marker.parent||s.marker)}++i}),r},getAllMarks:function(){var e=[];return this.iter(function(t){var n=t.markedSpans;if(n)for(var r=0;r<n.length;++r)null!=n[r].from&&e.push(n[r].marker)}),e},posFromIndex:function(e){var t,n=this.first,r=this.lineSeparator().length;return this.iter(function(i){var o=i.text.length+r;return o>e?(t=e,!0):(e-=o,void++n)}),_(this,P(n,t))},indexFromPos:function(e){e=_(this,e);var t=e.ch;if(e.line<this.first||e.ch<0)return 0;var n=this.lineSeparator().length;return this.iter(this.first,e.line,function(e){t+=e.text.length+n}),t},copy:function(e){var t=new sl(M(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,n=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<n&&(n=e.to);var r=new sl(M(this,t,n),e.mode||this.modeOption,t,this.lineSep);return e.sharedHist&&(r.history=this.history),(this.linked||(this.linked=[])).push({doc:r,sharedHist:e.sharedHist}),r.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],ji(r,zi(this)),r},unlinkDoc:function(e){var t=this;if(e instanceof Oo&&(e=e.doc),this.linked)for(var n=0;n<this.linked.length;++n){var r=t.linked[n];if(r.doc==e){t.linked.splice(n,1),e.unlinkDoc(t),Ui(zi(t));break}}if(e.history==this.history){var i=[e.id];Ur(e,function(e){return i.push(e.id)},!0),e.history=new Gr(null),e.history.done=ri(this.history.done,i),e.history.undone=ri(this.history.undone,i)}},iterLinkedDocs:function(e){Ur(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):_a(e)},lineSeparator:function(){return this.lineSep||"\n"}}),sl.prototype.eachLine=sl.prototype.iter;for(var cl=0,ul=!1,fl={3:"Enter",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",127:"Delete",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},hl=0;hl<10;hl++)fl[hl+48]=fl[hl+96]=String(hl);for(var dl=65;dl<=90;dl++)fl[dl]=String.fromCharCode(dl);for(var pl=1;pl<=12;pl++)fl[pl+111]=fl[pl+63235]="F"+pl;var ml={};ml.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},ml.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},ml.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-D":"delWordAfter","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},ml.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},ml.default=da?ml.macDefault:ml.pcDefault;var gl,vl,yl={selectAll:xi,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),Ta)},killLine:function(e){return io(e,function(t){if(t.empty()){var n=L(e.doc,t.head.line).text.length;return t.head.ch==n&&t.head.line<e.lastLine()?{from:t.head,to:P(t.head.line+1,0)}:{from:t.head,to:P(t.head.line,n)}}return{from:t.from(),to:t.to()}})},deleteLine:function(e){return io(e,function(t){return{from:P(t.from().line,0),to:_(e.doc,P(t.to().line+1,0))}})},delLineLeft:function(e){return io(e,function(e){return{from:P(e.from().line,0),to:e.from()}})},delWrappedLineLeft:function(e){return io(e,function(t){var n=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:n},"div");return{from:r,to:t.from()}})},delWrappedLineRight:function(e){return io(e,function(t){var n=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div");return{from:t.from(),to:r}})},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(P(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(P(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy(function(t){return oo(e,t.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy(function(t){return lo(e,t.head)},{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy(function(t){return ao(e,t.head.line)},{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy(function(t){var n=e.charCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div")},Aa)},goLineLeft:function(e){return e.extendSelectionsBy(function(t){var n=e.charCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:n},"div")},Aa)},goLineLeftSmart:function(e){return e.extendSelectionsBy(function(t){var n=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:n},"div");return r.ch<e.getLine(r.line).search(/\S/)?lo(e,t.head):r},Aa)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"char")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],n=e.listSelections(),r=e.options.tabSize,i=0;i<n.length;i++){var o=n[i].from(),a=u(e.getLine(o.line),o.ch,r);t.push(p(r-a%r))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return cr(e,function(){for(var t=e.listSelections(),n=[],r=0;r<t.length;r++)if(t[r].empty()){var i=t[r].head,o=L(e.doc,i.line).text;if(o)if(i.ch==o.length&&(i=new P(i.line,i.ch-1)),i.ch>0)i=new P(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),P(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var a=L(e.doc,i.line-1).text;a&&(i=new P(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+a.charAt(a.length-1),P(i.line-1,a.length-1),i,"+transpose"))}n.push(new Or(i,i))}e.setSelections(n)})},newlineAndIndent:function(e){return cr(e,function(){for(var t=e.listSelections(),n=t.length-1;n>=0;n--)e.replaceRange(e.doc.lineSeparator(),t[n].anchor,t[n].head,"+input");t=e.listSelections();for(var r=0;r<t.length;r++)e.indentLine(t[r].from().line,null,!0);Qn(e)})},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}},xl=new f,bl=null,wl={toString:function(){return"CodeMirror.Init"}},kl={},Sl={};Oo.defaults=kl,Oo.optionHandlers=Sl;var Cl=[];Oo.defineInitHook=function(e){return Cl.push(e)};var Ll=null,Tl=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new f,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};Tl.prototype.init=function(e){function t(e){if(!Oe(i,e)){if(i.somethingSelected())Ro({lineWise:!1,text:i.getSelections()}),"cut"==e.type&&i.replaceSelection("",null,"cut");else{if(!i.options.lineWiseCopyCut)return;var t=Bo(i);Ro({lineWise:!0,text:t.text}),"cut"==e.type&&i.operation(function(){i.setSelections(t.ranges,0,Ta),i.replaceSelection("",null,"cut")})}if(e.clipboardData){e.clipboardData.clearData();var n=Ll.text.join("\n");if(e.clipboardData.setData("Text",n),e.clipboardData.getData("Text")==n)return void e.preventDefault()}var a=Fo(),l=a.firstChild;i.display.lineSpace.insertBefore(a,i.display.lineSpace.firstChild),l.value=Ll.text.join("\n");var s=document.activeElement;wa(l),setTimeout(function(){i.display.lineSpace.removeChild(a),s.focus(),s==o&&r.showPrimarySelection()},50)}}var n=this,r=this,i=r.cm,o=r.div=e.lineDiv;_o(o,i.options.spellcheck),Wa(o,"paste",function(e){Oe(i,e)||Ho(e,i)||ra<=11&&setTimeout(ur(i,function(){r.pollContent()||dr(i)}),20)}),Wa(o,"compositionstart",function(e){n.composing={data:e.data,done:!1}}),Wa(o,"compositionupdate",function(e){n.composing||(n.composing={data:e.data,done:!1})}),Wa(o,"compositionend",function(e){n.composing&&(e.data!=n.composing.data&&n.readFromDOMSoon(),n.composing.done=!0)}),Wa(o,"touchstart",function(){return r.forceCompositionEnd()}),Wa(o,"input",function(){n.composing||n.readFromDOMSoon()}),Wa(o,"copy",t),Wa(o,"cut",t)},Tl.prototype.prepareSelection=function(){var e=Tn(this.cm,!1);return e.focus=this.cm.state.focused,e},Tl.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},Tl.prototype.showPrimarySelection=function(){var e=window.getSelection(),t=this.cm.doc.sel.primary(),n=$o(this.cm,e.anchorNode,e.anchorOffset),r=$o(this.cm,e.focusNode,e.focusOffset);if(!n||n.bad||!r||r.bad||0!=R(W(n,r),t.from())||0!=R(H(n,r),t.to())){var i=qo(this.cm,t.from()),o=qo(this.cm,t.to());if(i||o){var a=this.cm.display.view,l=e.rangeCount&&e.getRangeAt(0);if(i){if(!o){var s=a[a.length-1].measure,c=s.maps?s.maps[s.maps.length-1]:s.map;o={node:c[c.length-1],offset:c[c.length-2]-c[c.length-3]}}}else i={node:a[0].measure.map[2],offset:0};var u;try{u=va(i.node,i.offset,o.offset,o.node)}catch(e){}u&&(!Qo&&this.cm.state.focused?(e.collapse(i.node,i.offset),u.collapsed||(e.removeAllRanges(),e.addRange(u))):(e.removeAllRanges(),e.addRange(u)),l&&null==e.anchorNode?e.addRange(l):Qo&&this.startGracePeriod()),this.rememberSelection()}}},Tl.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation(function(){return e.cm.curOp.selectionChanged=!0})},20)},Tl.prototype.showMultipleSelections=function(e){n(this.cm.display.cursorDiv,e.cursors),n(this.cm.display.selectionDiv,e.selection)},Tl.prototype.rememberSelection=function(){var e=window.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},Tl.prototype.selectionInEditor=function(){var e=window.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return i(this.div,t)},Tl.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()||this.showSelection(this.prepareSelection(),!0),this.div.focus())},Tl.prototype.blur=function(){this.div.blur()},Tl.prototype.getField=function(){return this.div},Tl.prototype.supportsTouch=function(){return!0},Tl.prototype.receivedFocus=function(){function e(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,e))}var t=this;this.selectionInEditor()?this.pollSelection():cr(this.cm,function(){return t.cm.curOp.selectionChanged=!0}),this.polling.set(this.cm.options.pollInterval,e)},Tl.prototype.selectionChanged=function(){var e=window.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},Tl.prototype.pollSelection=function(){if(!this.composing&&null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=window.getSelection(),t=this.cm;this.rememberSelection();var n=$o(t,e.anchorNode,e.anchorOffset),r=$o(t,e.focusNode,e.focusOffset);n&&r&&cr(t,function(){fi(t.doc,Pr(n,r),Ta),(n.bad||r.bad)&&(t.curOp.selectionChanged=!0)})}},Tl.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e=this.cm,t=e.display,n=e.doc.sel.primary(),r=n.from(),i=n.to();if(0==r.ch&&r.line>e.firstLine()&&(r=P(r.line-1,L(e.doc,r.line-1).length)),i.ch==L(e.doc,i.line).text.length&&i.line<e.lastLine()&&(i=P(i.line+1,0)),r.line<t.viewFrom||i.line>t.viewTo-1)return!1;var o,a,l;r.line==t.viewFrom||0==(o=Cn(e,r.line))?(a=N(t.view[0].line),l=t.view[0].node):(a=N(t.view[o].line),l=t.view[o-1].node.nextSibling);var s,c,u=Cn(e,i.line);if(u==t.view.length-1?(s=t.viewTo-1,c=t.lineDiv.lastChild):(s=N(t.view[u+1].line)-1,c=t.view[u+1].node.previousSibling),!l)return!1;for(var f=e.doc.splitLines(Yo(e,l,c,a,s)),h=T(e.doc,P(a,0),P(s,L(e.doc,s).text.length));f.length>1&&h.length>1;)if(m(f)==m(h))f.pop(),h.pop(),s--;else{if(f[0]!=h[0])break;f.shift(),h.shift(),a++}for(var d=0,p=0,g=f[0],v=h[0],y=Math.min(g.length,v.length);d<y&&g.charCodeAt(d)==v.charCodeAt(d);)++d;for(var x=m(f),b=m(h),w=Math.min(x.length-(1==f.length?d:0),b.length-(1==h.length?d:0));p<w&&x.charCodeAt(x.length-p-1)==b.charCodeAt(b.length-p-1);)++p;f[f.length-1]=x.slice(0,x.length-p).replace(/^\u200b+/,""),f[0]=f[0].slice(d).replace(/\u200b+$/,"");var k=P(a,d),S=P(s,h.length?m(h).length-p:0);return f.length>1||f[0]||R(k,S)?(Mi(e.doc,f,k,S,"+input"),!0):void 0},Tl.prototype.ensurePolled=function(){this.forceCompositionEnd()},Tl.prototype.reset=function(){this.forceCompositionEnd()},Tl.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.pollContent()||dr(this.cm),this.div.blur(),this.div.focus())},Tl.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout(function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}!e.cm.isReadOnly()&&e.pollContent()||cr(e.cm,function(){return dr(e.cm)})},80))},Tl.prototype.setUneditable=function(e){e.contentEditable="false"},Tl.prototype.onKeyPress=function(e){e.preventDefault(),this.cm.isReadOnly()||ur(this.cm,Do)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0)},Tl.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},Tl.prototype.onContextMenu=function(){},Tl.prototype.resetPosition=function(){},Tl.prototype.needsContentAttribute=!0;var Ml=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new f,this.inaccurateSelection=!1,this.hasSelection=!1,this.composing=null};Ml.prototype.init=function(e){function t(e){if(!Oe(i,e)){if(i.somethingSelected())Ro({lineWise:!1,text:i.getSelections()}),r.inaccurateSelection&&(r.prevInput="",r.inaccurateSelection=!1,a.value=Ll.text.join("\n"),wa(a));else{if(!i.options.lineWiseCopyCut)return;var t=Bo(i);Ro({lineWise:!0,text:t.text}),"cut"==e.type?i.setSelections(t.ranges,null,Ta):(r.prevInput="",a.value=t.text.join("\n"),wa(a))}"cut"==e.type&&(i.state.cutIncoming=!0)}}var n=this,r=this,i=this.cm,o=this.wrapper=Fo(),a=this.textarea=o.firstChild;e.wrapper.insertBefore(o,e.wrapper.firstChild),fa&&(a.style.width="0px"),Wa(a,"input",function(){na&&ra>=9&&n.hasSelection&&(n.hasSelection=null),r.poll()}),Wa(a,"paste",function(e){Oe(i,e)||Ho(e,i)||(i.state.pasteIncoming=!0,r.fastPoll())}),Wa(a,"cut",t),Wa(a,"copy",t),Wa(e.scroller,"paste",function(t){_t(e,t)||Oe(i,t)||(i.state.pasteIncoming=!0,r.focus())}),Wa(e.lineSpace,"selectstart",function(t){_t(e,t)||De(t)}),Wa(a,"compositionstart",function(){var e=i.getCursor("from");r.composing&&r.composing.range.clear(),r.composing={start:e,range:i.markText(e,i.getCursor("to"),{className:"CodeMirror-composing"})}}),Wa(a,"compositionend",function(){r.composing&&(r.poll(),r.composing.range.clear(),r.composing=null)})},Ml.prototype.prepareSelection=function(){var e=this.cm,t=e.display,n=e.doc,r=Tn(e);if(e.options.moveInputWithCursor){var i=hn(e,n.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),a=t.lineDiv.getBoundingClientRect();r.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+a.top-o.top)),r.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+a.left-o.left))}return r},Ml.prototype.showSelection=function(e){var t=this.cm,r=t.display;n(r.cursorDiv,e.cursors),n(r.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},Ml.prototype.reset=function(e){if(!this.contextMenuPending){var t,n,r=this.cm,i=r.doc;if(r.somethingSelected()){this.prevInput="";var o=i.sel.primary();t=za&&(o.to().line-o.from().line>100||(n=r.getSelection()).length>1e3);var a=t?"-":n||r.getSelection();this.textarea.value=a,r.state.focused&&wa(this.textarea),na&&ra>=9&&(this.hasSelection=a)}else e||(this.prevInput=this.textarea.value="",na&&ra>=9&&(this.hasSelection=null));this.inaccurateSelection=t}},Ml.prototype.getField=function(){return this.textarea},Ml.prototype.supportsTouch=function(){return!1},Ml.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!ha||o()!=this.textarea))try{this.textarea.focus()}catch(e){}},Ml.prototype.blur=function(){this.textarea.blur()},Ml.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},Ml.prototype.receivedFocus=function(){this.slowPoll()},Ml.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){e.poll(),e.cm.state.focused&&e.slowPoll()})},Ml.prototype.fastPoll=function(){function e(){var r=n.poll();r||t?(n.pollingFast=!1,n.slowPoll()):(t=!0,n.polling.set(60,e))}var t=!1,n=this;n.pollingFast=!0,n.polling.set(20,e)},Ml.prototype.poll=function(){var e=this,t=this.cm,n=this.textarea,r=this.prevInput;if(this.contextMenuPending||!t.state.focused||Fa(n)&&!r&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=n.value;if(i==r&&!t.somethingSelected())return!1;if(na&&ra>=9&&this.hasSelection===i||da&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||r||(r="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var a=0,l=Math.min(r.length,i.length);a<l&&r.charCodeAt(a)==i.charCodeAt(a);)++a;return cr(t,function(){Do(t,i.slice(a),r.length-a,null,e.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?n.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},Ml.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},Ml.prototype.onKeyPress=function(){na&&ra>=9&&(this.hasSelection=null),this.fastPoll()},Ml.prototype.onContextMenu=function(e){function t(){if(null!=a.selectionStart){var e=i.somethingSelected(),t="​"+(e?a.value:"");a.value="⇚",a.value=t,r.prevInput=e?"":"​",a.selectionStart=1,a.selectionEnd=t.length,o.selForContextMenu=i.doc.sel}}function n(){if(r.contextMenuPending=!1,r.wrapper.style.cssText=f,a.style.cssText=u,na&&ra<9&&o.scrollbars.setScrollTop(o.scroller.scrollTop=s),null!=a.selectionStart){(!na||na&&ra<9)&&t();var e=0,n=function(){o.selForContextMenu==i.doc.sel&&0==a.selectionStart&&a.selectionEnd>0&&"​"==r.prevInput?ur(i,xi)(i):e++<10?o.detectingSelectAll=setTimeout(n,500):o.input.reset()};o.detectingSelectAll=setTimeout(n,200)}}var r=this,i=r.cm,o=i.display,a=r.textarea,l=Sn(i,e),s=o.scroller.scrollTop;if(l&&!la){var c=i.options.resetSelectionOnContextMenu;c&&i.doc.sel.contains(l)==-1&&ur(i,fi)(i.doc,Pr(l),Ta);var u=a.style.cssText,f=r.wrapper.style.cssText;r.wrapper.style.cssText="position: absolute";var h=r.wrapper.getBoundingClientRect();a.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-h.top-5)+"px; left: "+(e.clientX-h.left-5)+"px;\n      z-index: 1000; background: "+(na?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);";var d;if(ia&&(d=window.scrollY),o.input.focus(),ia&&window.scrollTo(null,d),o.input.reset(),i.somethingSelected()||(a.value=r.prevInput=" "),r.contextMenuPending=!0,o.selForContextMenu=i.doc.sel,clearTimeout(o.detectingSelectAll),na&&ra>=9&&t(),xa){Be(e);var p=function(){Ne(window,"mouseup",p),setTimeout(n,20)};Wa(window,"mouseup",p)}else setTimeout(n,50)}},Ml.prototype.readOnlyChanged=function(e){e||this.reset()},Ml.prototype.setUneditable=function(){},Ml.prototype.needsContentAttribute=!1,Mo(Oo),zo(Oo);var Al="iter insert remove copy getEditor constructor".split(" ");for(var Nl in sl.prototype)sl.prototype.hasOwnProperty(Nl)&&h(Al,Nl)<0&&(Oo.prototype[Nl]=function(e){return function(){return e.apply(this.doc,arguments)}}(sl.prototype[Nl]));return Re(sl),Oo.inputStyles={textarea:Ml,contenteditable:Tl},Oo.defineMode=function(e){Oo.defaults.mode||"null"==e||(Oo.defaults.mode=e),qe.apply(this,arguments)},Oo.defineMIME=Ge,Oo.defineMode("null",function(){return{token:function(e){return e.skipToEnd()}}}),Oo.defineMIME("text/plain","null"),Oo.defineExtension=function(e,t){Oo.prototype[e]=t},Oo.defineDocExtension=function(e,t){sl.prototype[e]=t},Oo.fromTextArea=Ko,Xo(Oo),Oo.version="5.22.0",Oo})},{}],11:[function(t,n,r){!function(i){"object"==typeof r&&"object"==typeof n?i(t("../../lib/codemirror"),t("../markdown/markdown"),t("../../addon/mode/overlay")):"function"==typeof e&&e.amd?e(["../../lib/codemirror","../markdown/markdown","../../addon/mode/overlay"],i):i(CodeMirror)}(function(e){"use strict";var t=/^((?:(?:aaas?|about|acap|adiumxtra|af[ps]|aim|apt|attachment|aw|beshare|bitcoin|bolo|callto|cap|chrome(?:-extension)?|cid|coap|com-eventbrite-attendee|content|crid|cvs|data|dav|dict|dlna-(?:playcontainer|playsingle)|dns|doi|dtn|dvb|ed2k|facetime|feed|file|finger|fish|ftp|geo|gg|git|gizmoproject|go|gopher|gtalk|h323|hcp|https?|iax|icap|icon|im|imap|info|ipn|ipp|irc[6s]?|iris(?:\.beep|\.lwz|\.xpc|\.xpcs)?|itms|jar|javascript|jms|keyparc|lastfm|ldaps?|magnet|mailto|maps|market|message|mid|mms|ms-help|msnim|msrps?|mtqp|mumble|mupdate|mvn|news|nfs|nih?|nntp|notes|oid|opaquelocktoken|palm|paparazzi|platform|pop|pres|proxy|psyc|query|res(?:ource)?|rmi|rsync|rtmp|rtsp|secondlife|service|session|sftp|sgn|shttp|sieve|sips?|skype|sm[bs]|snmp|soap\.beeps?|soldat|spotify|ssh|steam|svn|tag|teamspeak|tel(?:net)?|tftp|things|thismessage|tip|tn3270|tv|udp|unreal|urn|ut2004|vemmi|ventrilo|view-source|webcal|wss?|wtai|wyciwyg|xcon(?:-userid)?|xfire|xmlrpc\.beeps?|xmpp|xri|ymsgr|z39\.50[rs]?):(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]|\([^\s()<>]*\))+(?:\([^\s()<>]*\)|[^\s`*!()\[\]{};:'".,<>?«»“”‘’]))/i;e.defineMode("gfm",function(n,r){function i(e){return e.code=!1,null}var o=0,a={startState:function(){return{code:!1,codeBlock:!1,ateSpace:!1}},copyState:function(e){return{code:e.code,codeBlock:e.codeBlock,ateSpace:e.ateSpace}},token:function(e,n){if(n.combineTokens=null,n.codeBlock)return e.match(/^```+/)?(n.codeBlock=!1,null):(e.skipToEnd(),null);if(e.sol()&&(n.code=!1),e.sol()&&e.match(/^```+/))return e.skipToEnd(),n.codeBlock=!0,null;if("`"===e.peek()){e.next();var i=e.pos;e.eatWhile("`");var a=1+e.pos-i;return n.code?a===o&&(n.code=!1):(o=a,n.code=!0),null}if(n.code)return e.next(),null;if(e.eatSpace())return n.ateSpace=!0,null;if((e.sol()||n.ateSpace)&&(n.ateSpace=!1,r.gitHubSpice!==!1)){if(e.match(/^(?:[a-zA-Z0-9\-_]+\/)?(?:[a-zA-Z0-9\-_]+@)?(?:[a-f0-9]{7,40}\b)/))return n.combineTokens=!0,"link";if(e.match(/^(?:[a-zA-Z0-9\-_]+\/)?(?:[a-zA-Z0-9\-_]+)?#[0-9]+\b/))return n.combineTokens=!0,"link"}return e.match(t)&&"]("!=e.string.slice(e.start-2,e.start)&&(0==e.start||/\W/.test(e.string.charAt(e.start-1)))?(n.combineTokens=!0,"link"):(e.next(),null)},blankLine:i},l={underscoresBreakWords:!1,taskLists:!0,fencedCodeBlocks:"```",strikethrough:!0};for(var s in r)l[s]=r[s];return l.name="markdown",e.overlayMode(e.getMode(n,l),a)},"markdown"),e.defineMIME("text/x-gfm","gfm")})},{"../../addon/mode/overlay":8,"../../lib/codemirror":10,"../markdown/markdown":12}],12:[function(t,n,r){!function(i){"object"==typeof r&&"object"==typeof n?i(t("../../lib/codemirror"),t("../xml/xml"),t("../meta")):"function"==typeof e&&e.amd?e(["../../lib/codemirror","../xml/xml","../meta"],i):i(CodeMirror)}(function(e){"use strict";e.defineMode("markdown",function(t,n){function r(n){if(e.findModeByName){var r=e.findModeByName(n);r&&(n=r.mime||r.mimes[0])}var i=e.getMode(t,n);return"null"==i.name?null:i}function i(e,t,n){return t.f=t.inline=n,n(e,t)}function o(e,t,n){return t.f=t.block=n,n(e,t)}function a(e){return!e||!/\S/.test(e.string)}function l(e){return e.linkTitle=!1,e.em=!1,e.strong=!1,e.strikethrough=!1,e.quote=0,e.indentedCode=!1,k&&e.f==c&&(e.f=p,e.block=s),e.trailingSpace=0,e.trailingSpaceNewLine=!1,
e.prevLine=e.thisLine,e.thisLine=null,null}function s(t,o){var l=t.sol(),s=o.list!==!1,c=o.indentedCode;o.indentedCode=!1,s&&(o.indentationDiff>=0?(o.indentationDiff<4&&(o.indentation-=o.indentationDiff),o.list=null):o.indentation>0?o.list=null:o.list=!1);var f=null;if(o.indentationDiff>=4)return t.skipToEnd(),c||a(o.prevLine)?(o.indentation-=4,o.indentedCode=!0,S.code):null;if(t.eatSpace())return null;if((f=t.match(A))&&f[1].length<=6)return o.header=f[1].length,n.highlightFormatting&&(o.formatting="header"),o.f=o.inline,h(o);if(!(a(o.prevLine)||o.quote||s||c)&&(f=t.match(N)))return o.header="="==f[0].charAt(0)?1:2,n.highlightFormatting&&(o.formatting="header"),o.f=o.inline,h(o);if(t.eat(">"))return o.quote=l?1:o.quote+1,n.highlightFormatting&&(o.formatting="quote"),t.eatSpace(),h(o);if("["===t.peek())return i(t,o,y);if(t.match(L,!0))return o.hr=!0,S.hr;if(f=t.match(T)){var d=f[1]?"ol":"ul";for(o.indentation=t.column()+t.current().length,o.list=!0;o.listStack&&t.column()<o.listStack[o.listStack.length-1];)o.listStack.pop();return o.listStack.push(o.indentation),n.taskLists&&t.match(M,!1)&&(o.taskList=!0),o.f=o.inline,n.highlightFormatting&&(o.formatting=["list","list-"+d]),h(o)}return n.fencedCodeBlocks&&(f=t.match(O,!0))?(o.fencedChars=f[1],o.localMode=r(f[2]),o.localMode&&(o.localState=e.startState(o.localMode)),o.f=o.block=u,n.highlightFormatting&&(o.formatting="code-block"),o.code=-1,h(o)):i(t,o,o.inline)}function c(t,n){var r=w.token(t,n.htmlState);if(!k){var i=e.innerMode(w,n.htmlState);("xml"==i.mode.name&&null===i.state.tagStart&&!i.state.context&&i.state.tokenize.isInText||n.md_inside&&t.current().indexOf(">")>-1)&&(n.f=p,n.block=s,n.htmlState=null)}return r}function u(e,t){return t.fencedChars&&e.match(t.fencedChars,!1)?(t.localMode=t.localState=null,t.f=t.block=f,null):t.localMode?t.localMode.token(e,t.localState):(e.skipToEnd(),S.code)}function f(e,t){e.match(t.fencedChars),t.block=s,t.f=p,t.fencedChars=null,n.highlightFormatting&&(t.formatting="code-block"),t.code=1;var r=h(t);return t.code=0,r}function h(e){var t=[];if(e.formatting){t.push(S.formatting),"string"==typeof e.formatting&&(e.formatting=[e.formatting]);for(var r=0;r<e.formatting.length;r++)t.push(S.formatting+"-"+e.formatting[r]),"header"===e.formatting[r]&&t.push(S.formatting+"-"+e.formatting[r]+"-"+e.header),"quote"===e.formatting[r]&&(!n.maxBlockquoteDepth||n.maxBlockquoteDepth>=e.quote?t.push(S.formatting+"-"+e.formatting[r]+"-"+e.quote):t.push("error"))}if(e.taskOpen)return t.push("meta"),t.length?t.join(" "):null;if(e.taskClosed)return t.push("property"),t.length?t.join(" "):null;if(e.linkHref?t.push(S.linkHref,"url"):(e.strong&&t.push(S.strong),e.em&&t.push(S.em),e.strikethrough&&t.push(S.strikethrough),e.linkText&&t.push(S.linkText),e.code&&t.push(S.code),e.image&&t.push(S.image),e.imageAltText&&t.push(S.imageAltText,"link"),e.imageMarker&&t.push(S.imageMarker)),e.header&&t.push(S.header,S.header+"-"+e.header),e.quote&&(t.push(S.quote),!n.maxBlockquoteDepth||n.maxBlockquoteDepth>=e.quote?t.push(S.quote+"-"+e.quote):t.push(S.quote+"-"+n.maxBlockquoteDepth)),e.list!==!1){var i=(e.listStack.length-1)%3;i?1===i?t.push(S.list2):t.push(S.list3):t.push(S.list1)}return e.trailingSpaceNewLine?t.push("trailing-space-new-line"):e.trailingSpace&&t.push("trailing-space-"+(e.trailingSpace%2?"a":"b")),t.length?t.join(" "):null}function d(e,t){if(e.match(E,!0))return h(t)}function p(t,r){var i=r.text(t,r);if("undefined"!=typeof i)return i;if(r.list)return r.list=null,h(r);if(r.taskList){var a="x"!==t.match(M,!0)[1];return a?r.taskOpen=!0:r.taskClosed=!0,n.highlightFormatting&&(r.formatting="task"),r.taskList=!1,h(r)}if(r.taskOpen=!1,r.taskClosed=!1,r.header&&t.match(/^#+$/,!0))return n.highlightFormatting&&(r.formatting="header"),h(r);var l=t.sol(),s=t.next();if(r.linkTitle){r.linkTitle=!1;var u=s;"("===s&&(u=")"),u=(u+"").replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1");var f="^\\s*(?:[^"+u+"\\\\]+|\\\\\\\\|\\\\.)"+u;if(t.match(new RegExp(f),!0))return S.linkHref}if("`"===s){var d=r.formatting;n.highlightFormatting&&(r.formatting="code"),t.eatWhile("`");var p=t.current().length;if(0==r.code)return r.code=p,h(r);if(p==r.code){var v=h(r);return r.code=0,v}return r.formatting=d,h(r)}if(r.code)return h(r);if("\\"===s&&(t.next(),n.highlightFormatting)){var y=h(r),x=S.formatting+"-escape";return y?y+" "+x:x}if("!"===s&&t.match(/\[[^\]]*\] ?(?:\(|\[)/,!1))return r.imageMarker=!0,r.image=!0,n.highlightFormatting&&(r.formatting="image"),h(r);if("["===s&&r.imageMarker&&t.match(/[^\]]*\](\(.*?\)| ?\[.*?\])/,!1))return r.imageMarker=!1,r.imageAltText=!0,n.highlightFormatting&&(r.formatting="image"),h(r);if("]"===s&&r.imageAltText){n.highlightFormatting&&(r.formatting="image");var y=h(r);return r.imageAltText=!1,r.image=!1,r.inline=r.f=g,y}if("["===s&&t.match(/[^\]]*\](\(.*\)| ?\[.*?\])/,!1)&&!r.image)return r.linkText=!0,n.highlightFormatting&&(r.formatting="link"),h(r);if("]"===s&&r.linkText&&t.match(/\(.*?\)| ?\[.*?\]/,!1)){n.highlightFormatting&&(r.formatting="link");var y=h(r);return r.linkText=!1,r.inline=r.f=g,y}if("<"===s&&t.match(/^(https?|ftps?):\/\/(?:[^\\>]|\\.)+>/,!1)){r.f=r.inline=m,n.highlightFormatting&&(r.formatting="link");var y=h(r);return y?y+=" ":y="",y+S.linkInline}if("<"===s&&t.match(/^[^> \\]+@(?:[^\\>]|\\.)+>/,!1)){r.f=r.inline=m,n.highlightFormatting&&(r.formatting="link");var y=h(r);return y?y+=" ":y="",y+S.linkEmail}if("<"===s&&t.match(/^(!--|\w)/,!1)){var b=t.string.indexOf(">",t.pos);if(b!=-1){var k=t.string.substring(t.start,b);/markdown\s*=\s*('|"){0,1}1('|"){0,1}/.test(k)&&(r.md_inside=!0)}return t.backUp(1),r.htmlState=e.startState(w),o(t,r,c)}if("<"===s&&t.match(/^\/\w*?>/))return r.md_inside=!1,"tag";var C=!1;if(!n.underscoresBreakWords&&"_"===s&&"_"!==t.peek()&&t.match(/(\w)/,!1)){var L=t.pos-2;if(L>=0){var T=t.string.charAt(L);"_"!==T&&T.match(/(\w)/,!1)&&(C=!0)}}if("*"===s||"_"===s&&!C)if(l&&" "===t.peek());else{if(r.strong===s&&t.eat(s)){n.highlightFormatting&&(r.formatting="strong");var v=h(r);return r.strong=!1,v}if(!r.strong&&t.eat(s))return r.strong=s,n.highlightFormatting&&(r.formatting="strong"),h(r);if(r.em===s){n.highlightFormatting&&(r.formatting="em");var v=h(r);return r.em=!1,v}if(!r.em)return r.em=s,n.highlightFormatting&&(r.formatting="em"),h(r)}else if(" "===s&&(t.eat("*")||t.eat("_"))){if(" "===t.peek())return h(r);t.backUp(1)}if(n.strikethrough)if("~"===s&&t.eatWhile(s)){if(r.strikethrough){n.highlightFormatting&&(r.formatting="strikethrough");var v=h(r);return r.strikethrough=!1,v}if(t.match(/^[^\s]/,!1))return r.strikethrough=!0,n.highlightFormatting&&(r.formatting="strikethrough"),h(r)}else if(" "===s&&t.match(/^~~/,!0)){if(" "===t.peek())return h(r);t.backUp(2)}return" "===s&&(t.match(/ +$/,!1)?r.trailingSpace++:r.trailingSpace&&(r.trailingSpaceNewLine=!0)),h(r)}function m(e,t){var r=e.next();if(">"===r){t.f=t.inline=p,n.highlightFormatting&&(t.formatting="link");var i=h(t);return i?i+=" ":i="",i+S.linkInline}return e.match(/^[^>]+/,!0),S.linkInline}function g(e,t){if(e.eatSpace())return null;var r=e.next();return"("===r||"["===r?(t.f=t.inline=v("("===r?")":"]",0),n.highlightFormatting&&(t.formatting="link-string"),t.linkHref=!0,h(t)):"error"}function v(e){return function(t,r){var i=t.next();if(i===e){r.f=r.inline=p,n.highlightFormatting&&(r.formatting="link-string");var o=h(r);return r.linkHref=!1,o}return t.match(I[e]),r.linkHref=!0,h(r)}}function y(e,t){return e.match(/^([^\]\\]|\\.)*\]:/,!1)?(t.f=x,e.next(),n.highlightFormatting&&(t.formatting="link"),t.linkText=!0,h(t)):i(e,t,p)}function x(e,t){if(e.match(/^\]:/,!0)){t.f=t.inline=b,n.highlightFormatting&&(t.formatting="link");var r=h(t);return t.linkText=!1,r}return e.match(/^([^\]\\]|\\.)+/,!0),S.linkText}function b(e,t){return e.eatSpace()?null:(e.match(/^[^\s]+/,!0),void 0===e.peek()?t.linkTitle=!0:e.match(/^(?:\s+(?:"(?:[^"\\]|\\\\|\\.)+"|'(?:[^'\\]|\\\\|\\.)+'|\((?:[^)\\]|\\\\|\\.)+\)))?/,!0),t.f=t.inline=p,S.linkHref+" url")}var w=e.getMode(t,"text/html"),k="null"==w.name;void 0===n.highlightFormatting&&(n.highlightFormatting=!1),void 0===n.maxBlockquoteDepth&&(n.maxBlockquoteDepth=0),void 0===n.underscoresBreakWords&&(n.underscoresBreakWords=!0),void 0===n.taskLists&&(n.taskLists=!1),void 0===n.strikethrough&&(n.strikethrough=!1),void 0===n.tokenTypeOverrides&&(n.tokenTypeOverrides={});var S={header:"header",code:"comment",quote:"quote",list1:"variable-2",list2:"variable-3",list3:"keyword",hr:"hr",image:"image",imageAltText:"image-alt-text",imageMarker:"image-marker",formatting:"formatting",linkInline:"link",linkEmail:"link",linkText:"link",linkHref:"string",em:"em",strong:"strong",strikethrough:"strikethrough"};for(var C in S)S.hasOwnProperty(C)&&n.tokenTypeOverrides[C]&&(S[C]=n.tokenTypeOverrides[C]);var L=/^([*\-_])(?:\s*\1){2,}\s*$/,T=/^(?:[*\-+]|^[0-9]+([.)]))\s+/,M=/^\[(x| )\](?=\s)/,A=n.allowAtxHeaderWithoutSpace?/^(#+)/:/^(#+)(?: |$)/,N=/^ *(?:\={1,}|-{1,})\s*$/,E=/^[^#!\[\]*_\\<>` "'(~]+/,O=new RegExp("^("+(n.fencedCodeBlocks===!0?"~~~+|```+":n.fencedCodeBlocks)+")[ \\t]*([\\w+#-]*)"),I={")":/^(?:[^\\\(\)]|\\.|\((?:[^\\\(\)]|\\.)*\))*?(?=\))/,"]":/^(?:[^\\\[\]]|\\.|\[(?:[^\\\[\\]]|\\.)*\])*?(?=\])/},P={startState:function(){return{f:s,prevLine:null,thisLine:null,block:s,htmlState:null,indentation:0,inline:p,text:d,formatting:!1,linkText:!1,linkHref:!1,linkTitle:!1,code:0,em:!1,strong:!1,header:0,hr:!1,taskList:!1,list:!1,listStack:[],quote:0,trailingSpace:0,trailingSpaceNewLine:!1,strikethrough:!1,fencedChars:null}},copyState:function(t){return{f:t.f,prevLine:t.prevLine,thisLine:t.thisLine,block:t.block,htmlState:t.htmlState&&e.copyState(w,t.htmlState),indentation:t.indentation,localMode:t.localMode,localState:t.localMode?e.copyState(t.localMode,t.localState):null,inline:t.inline,text:t.text,formatting:!1,linkTitle:t.linkTitle,code:t.code,em:t.em,strong:t.strong,strikethrough:t.strikethrough,header:t.header,hr:t.hr,taskList:t.taskList,list:t.list,listStack:t.listStack.slice(0),quote:t.quote,indentedCode:t.indentedCode,trailingSpace:t.trailingSpace,trailingSpaceNewLine:t.trailingSpaceNewLine,md_inside:t.md_inside,fencedChars:t.fencedChars}},token:function(e,t){if(t.formatting=!1,e!=t.thisLine){var n=t.header||t.hr;if(t.header=0,t.hr=!1,e.match(/^\s*$/,!0)||n){if(l(t),!n)return null;t.prevLine=null}t.prevLine=t.thisLine,t.thisLine=e,t.taskList=!1,t.trailingSpace=0,t.trailingSpaceNewLine=!1,t.f=t.block;var r=e.match(/^\s*/,!0)[0].replace(/\t/g,"    ").length;if(t.indentationDiff=Math.min(r-t.indentation,4),t.indentation=t.indentation+t.indentationDiff,r>0)return null}return t.f(e,t)},innerMode:function(e){return e.block==c?{state:e.htmlState,mode:w}:e.localState?{state:e.localState,mode:e.localMode}:{state:e,mode:P}},blankLine:l,getType:h,closeBrackets:"()[]{}''\"\"``",fold:"markdown"};return P},"xml"),e.defineMIME("text/x-markdown","markdown")})},{"../../lib/codemirror":10,"../meta":13,"../xml/xml":14}],13:[function(t,n,r){!function(i){"object"==typeof r&&"object"==typeof n?i(t("../lib/codemirror")):"function"==typeof e&&e.amd?e(["../lib/codemirror"],i):i(CodeMirror)}(function(e){"use strict";e.modeInfo=[{name:"APL",mime:"text/apl",mode:"apl",ext:["dyalog","apl"]},{name:"PGP",mimes:["application/pgp","application/pgp-keys","application/pgp-signature"],mode:"asciiarmor",ext:["pgp"]},{name:"ASN.1",mime:"text/x-ttcn-asn",mode:"asn.1",ext:["asn","asn1"]},{name:"Asterisk",mime:"text/x-asterisk",mode:"asterisk",file:/^extensions\.conf$/i},{name:"Brainfuck",mime:"text/x-brainfuck",mode:"brainfuck",ext:["b","bf"]},{name:"C",mime:"text/x-csrc",mode:"clike",ext:["c","h"]},{name:"C++",mime:"text/x-c++src",mode:"clike",ext:["cpp","c++","cc","cxx","hpp","h++","hh","hxx"],alias:["cpp"]},{name:"Cobol",mime:"text/x-cobol",mode:"cobol",ext:["cob","cpy"]},{name:"C#",mime:"text/x-csharp",mode:"clike",ext:["cs"],alias:["csharp"]},{name:"Clojure",mime:"text/x-clojure",mode:"clojure",ext:["clj","cljc","cljx"]},{name:"ClojureScript",mime:"text/x-clojurescript",mode:"clojure",ext:["cljs"]},{name:"Closure Stylesheets (GSS)",mime:"text/x-gss",mode:"css",ext:["gss"]},{name:"CMake",mime:"text/x-cmake",mode:"cmake",ext:["cmake","cmake.in"],file:/^CMakeLists.txt$/},{name:"CoffeeScript",mime:"text/x-coffeescript",mode:"coffeescript",ext:["coffee"],alias:["coffee","coffee-script"]},{name:"Common Lisp",mime:"text/x-common-lisp",mode:"commonlisp",ext:["cl","lisp","el"],alias:["lisp"]},{name:"Cypher",mime:"application/x-cypher-query",mode:"cypher",ext:["cyp","cypher"]},{name:"Cython",mime:"text/x-cython",mode:"python",ext:["pyx","pxd","pxi"]},{name:"Crystal",mime:"text/x-crystal",mode:"crystal",ext:["cr"]},{name:"CSS",mime:"text/css",mode:"css",ext:["css"]},{name:"CQL",mime:"text/x-cassandra",mode:"sql",ext:["cql"]},{name:"D",mime:"text/x-d",mode:"d",ext:["d"]},{name:"Dart",mimes:["application/dart","text/x-dart"],mode:"dart",ext:["dart"]},{name:"diff",mime:"text/x-diff",mode:"diff",ext:["diff","patch"]},{name:"Django",mime:"text/x-django",mode:"django"},{name:"Dockerfile",mime:"text/x-dockerfile",mode:"dockerfile",file:/^Dockerfile$/},{name:"DTD",mime:"application/xml-dtd",mode:"dtd",ext:["dtd"]},{name:"Dylan",mime:"text/x-dylan",mode:"dylan",ext:["dylan","dyl","intr"]},{name:"EBNF",mime:"text/x-ebnf",mode:"ebnf"},{name:"ECL",mime:"text/x-ecl",mode:"ecl",ext:["ecl"]},{name:"edn",mime:"application/edn",mode:"clojure",ext:["edn"]},{name:"Eiffel",mime:"text/x-eiffel",mode:"eiffel",ext:["e"]},{name:"Elm",mime:"text/x-elm",mode:"elm",ext:["elm"]},{name:"Embedded Javascript",mime:"application/x-ejs",mode:"htmlembedded",ext:["ejs"]},{name:"Embedded Ruby",mime:"application/x-erb",mode:"htmlembedded",ext:["erb"]},{name:"Erlang",mime:"text/x-erlang",mode:"erlang",ext:["erl"]},{name:"Factor",mime:"text/x-factor",mode:"factor",ext:["factor"]},{name:"FCL",mime:"text/x-fcl",mode:"fcl"},{name:"Forth",mime:"text/x-forth",mode:"forth",ext:["forth","fth","4th"]},{name:"Fortran",mime:"text/x-fortran",mode:"fortran",ext:["f","for","f77","f90"]},{name:"F#",mime:"text/x-fsharp",mode:"mllike",ext:["fs"],alias:["fsharp"]},{name:"Gas",mime:"text/x-gas",mode:"gas",ext:["s"]},{name:"Gherkin",mime:"text/x-feature",mode:"gherkin",ext:["feature"]},{name:"GitHub Flavored Markdown",mime:"text/x-gfm",mode:"gfm",file:/^(readme|contributing|history).md$/i},{name:"Go",mime:"text/x-go",mode:"go",ext:["go"]},{name:"Groovy",mime:"text/x-groovy",mode:"groovy",ext:["groovy","gradle"],file:/^Jenkinsfile$/},{name:"HAML",mime:"text/x-haml",mode:"haml",ext:["haml"]},{name:"Haskell",mime:"text/x-haskell",mode:"haskell",ext:["hs"]},{name:"Haskell (Literate)",mime:"text/x-literate-haskell",mode:"haskell-literate",ext:["lhs"]},{name:"Haxe",mime:"text/x-haxe",mode:"haxe",ext:["hx"]},{name:"HXML",mime:"text/x-hxml",mode:"haxe",ext:["hxml"]},{name:"ASP.NET",mime:"application/x-aspx",mode:"htmlembedded",ext:["aspx"],alias:["asp","aspx"]},{name:"HTML",mime:"text/html",mode:"htmlmixed",ext:["html","htm"],alias:["xhtml"]},{name:"HTTP",mime:"message/http",mode:"http"},{name:"IDL",mime:"text/x-idl",mode:"idl",ext:["pro"]},{name:"Pug",mime:"text/x-pug",mode:"pug",ext:["jade","pug"],alias:["jade"]},{name:"Java",mime:"text/x-java",mode:"clike",ext:["java"]},{name:"Java Server Pages",mime:"application/x-jsp",mode:"htmlembedded",ext:["jsp"],alias:["jsp"]},{name:"JavaScript",mimes:["text/javascript","text/ecmascript","application/javascript","application/x-javascript","application/ecmascript"],mode:"javascript",ext:["js"],alias:["ecmascript","js","node"]},{name:"JSON",mimes:["application/json","application/x-json"],mode:"javascript",ext:["json","map"],alias:["json5"]},{name:"JSON-LD",mime:"application/ld+json",mode:"javascript",ext:["jsonld"],alias:["jsonld"]},{name:"JSX",mime:"text/jsx",mode:"jsx",ext:["jsx"]},{name:"Jinja2",mime:"null",mode:"jinja2"},{name:"Julia",mime:"text/x-julia",mode:"julia",ext:["jl"]},{name:"Kotlin",mime:"text/x-kotlin",mode:"clike",ext:["kt"]},{name:"LESS",mime:"text/x-less",mode:"css",ext:["less"]},{name:"LiveScript",mime:"text/x-livescript",mode:"livescript",ext:["ls"],alias:["ls"]},{name:"Lua",mime:"text/x-lua",mode:"lua",ext:["lua"]},{name:"Markdown",mime:"text/x-markdown",mode:"markdown",ext:["markdown","md","mkd"]},{name:"mIRC",mime:"text/mirc",mode:"mirc"},{name:"MariaDB SQL",mime:"text/x-mariadb",mode:"sql"},{name:"Mathematica",mime:"text/x-mathematica",mode:"mathematica",ext:["m","nb"]},{name:"Modelica",mime:"text/x-modelica",mode:"modelica",ext:["mo"]},{name:"MUMPS",mime:"text/x-mumps",mode:"mumps",ext:["mps"]},{name:"MS SQL",mime:"text/x-mssql",mode:"sql"},{name:"mbox",mime:"application/mbox",mode:"mbox",ext:["mbox"]},{name:"MySQL",mime:"text/x-mysql",mode:"sql"},{name:"Nginx",mime:"text/x-nginx-conf",mode:"nginx",file:/nginx.*\.conf$/i},{name:"NSIS",mime:"text/x-nsis",mode:"nsis",ext:["nsh","nsi"]},{name:"NTriples",mime:"text/n-triples",mode:"ntriples",ext:["nt"]},{name:"Objective C",mime:"text/x-objectivec",mode:"clike",ext:["m","mm"],alias:["objective-c","objc"]},{name:"OCaml",mime:"text/x-ocaml",mode:"mllike",ext:["ml","mli","mll","mly"]},{name:"Octave",mime:"text/x-octave",mode:"octave",ext:["m"]},{name:"Oz",mime:"text/x-oz",mode:"oz",ext:["oz"]},{name:"Pascal",mime:"text/x-pascal",mode:"pascal",ext:["p","pas"]},{name:"PEG.js",mime:"null",mode:"pegjs",ext:["jsonld"]},{name:"Perl",mime:"text/x-perl",mode:"perl",ext:["pl","pm"]},{name:"PHP",mime:"application/x-httpd-php",mode:"php",ext:["php","php3","php4","php5","phtml"]},{name:"Pig",mime:"text/x-pig",mode:"pig",ext:["pig"]},{name:"Plain Text",mime:"text/plain",mode:"null",ext:["txt","text","conf","def","list","log"]},{name:"PLSQL",mime:"text/x-plsql",mode:"sql",ext:["pls"]},{name:"PowerShell",mime:"application/x-powershell",mode:"powershell",ext:["ps1","psd1","psm1"]},{name:"Properties files",mime:"text/x-properties",mode:"properties",ext:["properties","ini","in"],alias:["ini","properties"]},{name:"ProtoBuf",mime:"text/x-protobuf",mode:"protobuf",ext:["proto"]},{name:"Python",mime:"text/x-python",mode:"python",ext:["BUILD","bzl","py","pyw"],file:/^(BUCK|BUILD)$/},{name:"Puppet",mime:"text/x-puppet",mode:"puppet",ext:["pp"]},{name:"Q",mime:"text/x-q",mode:"q",ext:["q"]},{name:"R",mime:"text/x-rsrc",mode:"r",ext:["r","R"],alias:["rscript"]},{name:"reStructuredText",mime:"text/x-rst",mode:"rst",ext:["rst"],alias:["rst"]},{name:"RPM Changes",mime:"text/x-rpm-changes",mode:"rpm"},{name:"RPM Spec",mime:"text/x-rpm-spec",mode:"rpm",ext:["spec"]},{name:"Ruby",mime:"text/x-ruby",mode:"ruby",ext:["rb"],alias:["jruby","macruby","rake","rb","rbx"]},{name:"Rust",mime:"text/x-rustsrc",mode:"rust",ext:["rs"]},{name:"SAS",mime:"text/x-sas",mode:"sas",ext:["sas"]},{name:"Sass",mime:"text/x-sass",mode:"sass",ext:["sass"]},{name:"Scala",mime:"text/x-scala",mode:"clike",ext:["scala"]},{name:"Scheme",mime:"text/x-scheme",mode:"scheme",ext:["scm","ss"]},{name:"SCSS",mime:"text/x-scss",mode:"css",ext:["scss"]},{name:"Shell",mime:"text/x-sh",mode:"shell",ext:["sh","ksh","bash"],alias:["bash","sh","zsh"],file:/^PKGBUILD$/},{name:"Sieve",mime:"application/sieve",mode:"sieve",ext:["siv","sieve"]},{name:"Slim",mimes:["text/x-slim","application/x-slim"],mode:"slim",ext:["slim"]},{name:"Smalltalk",mime:"text/x-stsrc",mode:"smalltalk",ext:["st"]},{name:"Smarty",mime:"text/x-smarty",mode:"smarty",ext:["tpl"]},{name:"Solr",mime:"text/x-solr",mode:"solr"},{name:"Soy",mime:"text/x-soy",mode:"soy",ext:["soy"],alias:["closure template"]},{name:"SPARQL",mime:"application/sparql-query",mode:"sparql",ext:["rq","sparql"],alias:["sparul"]},{name:"Spreadsheet",mime:"text/x-spreadsheet",mode:"spreadsheet",alias:["excel","formula"]},{name:"SQL",mime:"text/x-sql",mode:"sql",ext:["sql"]},{name:"Squirrel",mime:"text/x-squirrel",mode:"clike",ext:["nut"]},{name:"Stylus",mime:"text/x-styl",mode:"stylus",ext:["styl"]},{name:"Swift",mime:"text/x-swift",mode:"swift",ext:["swift"]},{name:"sTeX",mime:"text/x-stex",mode:"stex"},{name:"LaTeX",mime:"text/x-latex",mode:"stex",ext:["text","ltx"],alias:["tex"]},{name:"SystemVerilog",mime:"text/x-systemverilog",mode:"verilog",ext:["v"]},{name:"Tcl",mime:"text/x-tcl",mode:"tcl",ext:["tcl"]},{name:"Textile",mime:"text/x-textile",mode:"textile",ext:["textile"]},{name:"TiddlyWiki ",mime:"text/x-tiddlywiki",mode:"tiddlywiki"},{name:"Tiki wiki",mime:"text/tiki",mode:"tiki"},{name:"TOML",mime:"text/x-toml",mode:"toml",ext:["toml"]},{name:"Tornado",mime:"text/x-tornado",mode:"tornado"},{name:"troff",mime:"text/troff",mode:"troff",ext:["1","2","3","4","5","6","7","8","9"]},{name:"TTCN",mime:"text/x-ttcn",mode:"ttcn",ext:["ttcn","ttcn3","ttcnpp"]},{name:"TTCN_CFG",mime:"text/x-ttcn-cfg",mode:"ttcn-cfg",ext:["cfg"]},{name:"Turtle",mime:"text/turtle",mode:"turtle",ext:["ttl"]},{name:"TypeScript",mime:"application/typescript",mode:"javascript",ext:["ts"],alias:["ts"]},{name:"Twig",mime:"text/x-twig",mode:"twig"},{name:"Web IDL",mime:"text/x-webidl",mode:"webidl",ext:["webidl"]},{name:"VB.NET",mime:"text/x-vb",mode:"vb",ext:["vb"]},{name:"VBScript",mime:"text/vbscript",mode:"vbscript",ext:["vbs"]},{name:"Velocity",mime:"text/velocity",mode:"velocity",ext:["vtl"]},{name:"Verilog",mime:"text/x-verilog",mode:"verilog",ext:["v"]},{name:"VHDL",mime:"text/x-vhdl",mode:"vhdl",ext:["vhd","vhdl"]},{name:"Vue.js Component",mimes:["script/x-vue","text/x-vue"],mode:"vue",ext:["vue"]},{name:"XML",mimes:["application/xml","text/xml"],mode:"xml",ext:["xml","xsl","xsd"],alias:["rss","wsdl","xsd"]},{name:"XQuery",mime:"application/xquery",mode:"xquery",ext:["xy","xquery"]},{name:"Yacas",mime:"text/x-yacas",mode:"yacas",ext:["ys"]},{name:"YAML",mimes:["text/x-yaml","text/yaml"],mode:"yaml",ext:["yaml","yml"],alias:["yml"]},{name:"Z80",mime:"text/x-z80",mode:"z80",ext:["z80"]},{name:"mscgen",mime:"text/x-mscgen",mode:"mscgen",ext:["mscgen","mscin","msc"]},{name:"xu",mime:"text/x-xu",mode:"mscgen",ext:["xu"]},{name:"msgenny",mime:"text/x-msgenny",mode:"mscgen",ext:["msgenny"]}];for(var t=0;t<e.modeInfo.length;t++){var n=e.modeInfo[t];n.mimes&&(n.mime=n.mimes[0])}e.findModeByMIME=function(t){t=t.toLowerCase();for(var n=0;n<e.modeInfo.length;n++){var r=e.modeInfo[n];if(r.mime==t)return r;if(r.mimes)for(var i=0;i<r.mimes.length;i++)if(r.mimes[i]==t)return r}},e.findModeByExtension=function(t){for(var n=0;n<e.modeInfo.length;n++){var r=e.modeInfo[n];if(r.ext)for(var i=0;i<r.ext.length;i++)if(r.ext[i]==t)return r}},e.findModeByFileName=function(t){for(var n=0;n<e.modeInfo.length;n++){var r=e.modeInfo[n];if(r.file&&r.file.test(t))return r}var i=t.lastIndexOf("."),o=i>-1&&t.substring(i+1,t.length);if(o)return e.findModeByExtension(o)},e.findModeByName=function(t){t=t.toLowerCase();for(var n=0;n<e.modeInfo.length;n++){var r=e.modeInfo[n];if(r.name.toLowerCase()==t)return r;if(r.alias)for(var i=0;i<r.alias.length;i++)if(r.alias[i].toLowerCase()==t)return r}}})},{"../lib/codemirror":10}],14:[function(t,n,r){!function(i){"object"==typeof r&&"object"==typeof n?i(t("../../lib/codemirror")):"function"==typeof e&&e.amd?e(["../../lib/codemirror"],i):i(CodeMirror)}(function(e){"use strict";var t={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},n={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,caseFold:!1};e.defineMode("xml",function(r,i){function o(e,t){function n(n){return t.tokenize=n,n(e,t)}var r=e.next();if("<"==r)return e.eat("!")?e.eat("[")?e.match("CDATA[")?n(s("atom","]]>")):null:e.match("--")?n(s("comment","-->")):e.match("DOCTYPE",!0,!0)?(e.eatWhile(/[\w\._\-]/),n(c(1))):null:e.eat("?")?(e.eatWhile(/[\w\._\-]/),t.tokenize=s("meta","?>"),"meta"):(T=e.eat("/")?"closeTag":"openTag",t.tokenize=a,"tag bracket");if("&"==r){var i;return i=e.eat("#")?e.eat("x")?e.eatWhile(/[a-fA-F\d]/)&&e.eat(";"):e.eatWhile(/[\d]/)&&e.eat(";"):e.eatWhile(/[\w\.\-:]/)&&e.eat(";"),i?"atom":"error"}return e.eatWhile(/[^&<]/),null}function a(e,t){var n=e.next();if(">"==n||"/"==n&&e.eat(">"))return t.tokenize=o,T=">"==n?"endTag":"selfcloseTag","tag bracket";if("="==n)return T="equals",null;if("<"==n){t.tokenize=o,t.state=d,t.tagName=t.tagStart=null;var r=t.tokenize(e,t);return r?r+" tag error":"tag error"}return/[\'\"]/.test(n)?(t.tokenize=l(n),t.stringStartCol=e.column(),t.tokenize(e,t)):(e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word")}function l(e){var t=function(t,n){for(;!t.eol();)if(t.next()==e){n.tokenize=a;break}return"string"};return t.isInAttribute=!0,t}function s(e,t){return function(n,r){for(;!n.eol();){if(n.match(t)){r.tokenize=o;break}n.next()}return e}}function c(e){return function(t,n){for(var r;null!=(r=t.next());){if("<"==r)return n.tokenize=c(e+1),n.tokenize(t,n);if(">"==r){if(1==e){n.tokenize=o;break}return n.tokenize=c(e-1),n.tokenize(t,n)}}return"meta"}}function u(e,t,n){this.prev=e.context,this.tagName=t,this.indent=e.indented,this.startOfLine=n,(S.doNotIndent.hasOwnProperty(t)||e.context&&e.context.noIndent)&&(this.noIndent=!0)}function f(e){e.context&&(e.context=e.context.prev)}function h(e,t){for(var n;;){if(!e.context)return;if(n=e.context.tagName,!S.contextGrabbers.hasOwnProperty(n)||!S.contextGrabbers[n].hasOwnProperty(t))return;f(e)}}function d(e,t,n){return"openTag"==e?(n.tagStart=t.column(),p):"closeTag"==e?m:d}function p(e,t,n){return"word"==e?(n.tagName=t.current(),M="tag",y):(M="error",p)}function m(e,t,n){if("word"==e){var r=t.current();return n.context&&n.context.tagName!=r&&S.implicitlyClosed.hasOwnProperty(n.context.tagName)&&f(n),n.context&&n.context.tagName==r||S.matchClosing===!1?(M="tag",g):(M="tag error",v)}return M="error",v}function g(e,t,n){return"endTag"!=e?(M="error",g):(f(n),d)}function v(e,t,n){return M="error",g(e,t,n)}function y(e,t,n){if("word"==e)return M="attribute",x;if("endTag"==e||"selfcloseTag"==e){var r=n.tagName,i=n.tagStart;return n.tagName=n.tagStart=null,"selfcloseTag"==e||S.autoSelfClosers.hasOwnProperty(r)?h(n,r):(h(n,r),n.context=new u(n,r,i==n.indented)),d}return M="error",y}function x(e,t,n){return"equals"==e?b:(S.allowMissing||(M="error"),y(e,t,n))}function b(e,t,n){return"string"==e?w:"word"==e&&S.allowUnquoted?(M="string",y):(M="error",y(e,t,n))}function w(e,t,n){return"string"==e?w:y(e,t,n)}var k=r.indentUnit,S={},C=i.htmlMode?t:n;for(var L in C)S[L]=C[L];for(var L in i)S[L]=i[L];var T,M;return o.isInText=!0,{startState:function(e){var t={tokenize:o,state:d,indented:e||0,tagName:null,tagStart:null,context:null};return null!=e&&(t.baseIndent=e),t},token:function(e,t){if(!t.tagName&&e.sol()&&(t.indented=e.indentation()),e.eatSpace())return null;T=null;var n=t.tokenize(e,t);return(n||T)&&"comment"!=n&&(M=null,t.state=t.state(T||n,e,t),M&&(n="error"==M?n+" error":M)),n},indent:function(t,n,r){var i=t.context;if(t.tokenize.isInAttribute)return t.tagStart==t.indented?t.stringStartCol+1:t.indented+k;if(i&&i.noIndent)return e.Pass;if(t.tokenize!=a&&t.tokenize!=o)return r?r.match(/^(\s*)/)[0].length:0;if(t.tagName)return S.multilineTagIndentPastTag!==!1?t.tagStart+t.tagName.length+2:t.tagStart+k*(S.multilineTagIndentFactor||1);if(S.alignCDATA&&/<!\[CDATA\[/.test(n))return 0;var l=n&&/^<(\/)?([\w_:\.-]*)/.exec(n);if(l&&l[1])for(;i;){if(i.tagName==l[2]){i=i.prev;break}if(!S.implicitlyClosed.hasOwnProperty(i.tagName))break;i=i.prev}else if(l)for(;i;){var s=S.contextGrabbers[i.tagName];if(!s||!s.hasOwnProperty(l[2]))break;i=i.prev}for(;i&&i.prev&&!i.startOfLine;)i=i.prev;return i?i.indent+k:t.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"<!--",blockCommentEnd:"-->",configuration:S.htmlMode?"html":"xml",helperType:S.htmlMode?"html":"xml",skipAttribute:function(e){e.state==b&&(e.state=y)}}}),e.defineMIME("text/xml","xml"),e.defineMIME("application/xml","xml"),e.mimeModes.hasOwnProperty("text/html")||e.defineMIME("text/html",{name:"xml",htmlMode:!0})})},{"../../lib/codemirror":10}],15:[function(e,t,n){n.read=function(e,t,n,r,i){var o,a,l=8*i-r-1,s=(1<<l)-1,c=s>>1,u=-7,f=n?i-1:0,h=n?-1:1,d=e[t+f];for(f+=h,o=d&(1<<-u)-1,d>>=-u,u+=l;u>0;o=256*o+e[t+f],f+=h,u-=8);for(a=o&(1<<-u)-1,o>>=-u,u+=r;u>0;a=256*a+e[t+f],f+=h,u-=8);if(0===o)o=1-c;else{if(o===s)return a?NaN:(d?-1:1)*(1/0);a+=Math.pow(2,r),o-=c}return(d?-1:1)*a*Math.pow(2,o-r)},n.write=function(e,t,n,r,i,o){var a,l,s,c=8*o-i-1,u=(1<<c)-1,f=u>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=r?0:o-1,p=r?1:-1,m=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(l=isNaN(t)?1:0,a=u):(a=Math.floor(Math.log(t)/Math.LN2),t*(s=Math.pow(2,-a))<1&&(a--,s*=2),t+=a+f>=1?h/s:h*Math.pow(2,1-f),t*s>=2&&(a++,s/=2),a+f>=u?(l=0,a=u):a+f>=1?(l=(t*s-1)*Math.pow(2,i),a+=f):(l=t*Math.pow(2,f-1)*Math.pow(2,i),a=0));i>=8;e[n+d]=255&l,d+=p,l/=256,i-=8);for(a=a<<i|l,c+=i;c>0;e[n+d]=255&a,d+=p,a/=256,c-=8);e[n+d-p]|=128*m}},{}],16:[function(e,t,n){var r={}.toString;t.exports=Array.isArray||function(e){return"[object Array]"==r.call(e)}},{}],17:[function(t,n,r){(function(t){(function(){function t(e){this.tokens=[],this.tokens.links={},this.options=e||h.defaults,this.rules=d.normal,this.options.gfm&&(this.options.tables?this.rules=d.tables:this.rules=d.gfm)}function i(e,t){if(this.options=t||h.defaults,this.links=e,this.rules=p.normal,this.renderer=this.options.renderer||new o,this.renderer.options=this.options,!this.links)throw new Error("Tokens array requires a `links` property.");this.options.gfm?this.options.breaks?this.rules=p.breaks:this.rules=p.gfm:this.options.pedantic&&(this.rules=p.pedantic)}function o(e){this.options=e||{}}function a(e){this.tokens=[],this.token=null,this.options=e||h.defaults,this.options.renderer=this.options.renderer||new o,this.renderer=this.options.renderer,this.renderer.options=this.options}function l(e,t){return e.replace(t?/&/g:/&(?!#?\w+;)/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;")}function s(e){return e.replace(/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/g,function(e,t){return t=t.toLowerCase(),"colon"===t?":":"#"===t.charAt(0)?"x"===t.charAt(1)?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""})}function c(e,t){return e=e.source,t=t||"",function n(r,i){return r?(i=i.source||i,i=i.replace(/(^|[^\[])\^/g,"$1"),e=e.replace(r,i),n):new RegExp(e,t)}}function u(){}function f(e){for(var t,n,r=1;r<arguments.length;r++){t=arguments[r];for(n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}function h(e,n,r){if(r||"function"==typeof n){r||(r=n,n=null),n=f({},h.defaults,n||{});var i,o,s=n.highlight,c=0;try{i=t.lex(e,n)}catch(e){return r(e)}o=i.length;var u=function(e){if(e)return n.highlight=s,r(e);var t;try{t=a.parse(i,n)}catch(t){e=t}return n.highlight=s,e?r(e):r(null,t)};if(!s||s.length<3)return u();if(delete n.highlight,!o)return u();for(;c<i.length;c++)!function(e){return"code"!==e.type?--o||u():s(e.text,e.lang,function(t,n){return t?u(t):null==n||n===e.text?--o||u():(e.text=n,e.escaped=!0,void(--o||u()))})}(i[c])}else try{return n&&(n=f({},h.defaults,n)),a.parse(t.lex(e,n),n)}catch(e){if(e.message+="\nPlease report this to https://github.com/chjj/marked.",(n||h.defaults).silent)return"<p>An error occured:</p><pre>"+l(e.message+"",!0)+"</pre>";throw e}}var d={newline:/^\n+/,code:/^( {4}[^\n]+\n*)+/,fences:u,hr:/^( *[-*_]){3,} *(?:\n+|$)/,heading:/^ *(#{1,6}) *([^\n]+?) *#* *(?:\n+|$)/,nptable:u,lheading:/^([^\n]+)\n *(=|-){2,} *(?:\n+|$)/,blockquote:/^( *>[^\n]+(\n(?!def)[^\n]+)*\n*)+/,list:/^( *)(bull) [\s\S]+?(?:hr|def|\n{2,}(?! )(?!\1bull )\n*|\s*$)/,
html:/^ *(?:comment *(?:\n|\s*$)|closed *(?:\n{2,}|\s*$)|closing *(?:\n{2,}|\s*$))/,def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +["(]([^\n]+)[")])? *(?:\n+|$)/,table:u,paragraph:/^((?:[^\n]+\n?(?!hr|heading|lheading|blockquote|tag|def))+)\n*/,text:/^[^\n]+/};d.bullet=/(?:[*+-]|\d+\.)/,d.item=/^( *)(bull) [^\n]*(?:\n(?!\1bull )[^\n]*)*/,d.item=c(d.item,"gm")(/bull/g,d.bullet)(),d.list=c(d.list)(/bull/g,d.bullet)("hr","\\n+(?=\\1?(?:[-*_] *){3,}(?:\\n+|$))")("def","\\n+(?="+d.def.source+")")(),d.blockquote=c(d.blockquote)("def",d.def)(),d._tag="(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:/|[^\\w\\s@]*@)\\b",d.html=c(d.html)("comment",/<!--[\s\S]*?-->/)("closed",/<(tag)[\s\S]+?<\/\1>/)("closing",/<tag(?:"[^"]*"|'[^']*'|[^'">])*?>/)(/tag/g,d._tag)(),d.paragraph=c(d.paragraph)("hr",d.hr)("heading",d.heading)("lheading",d.lheading)("blockquote",d.blockquote)("tag","<"+d._tag)("def",d.def)(),d.normal=f({},d),d.gfm=f({},d.normal,{fences:/^ *(`{3,}|~{3,})[ \.]*(\S+)? *\n([\s\S]*?)\s*\1 *(?:\n+|$)/,paragraph:/^/,heading:/^ *(#{1,6}) +([^\n]+?) *#* *(?:\n+|$)/}),d.gfm.paragraph=c(d.paragraph)("(?!","(?!"+d.gfm.fences.source.replace("\\1","\\2")+"|"+d.list.source.replace("\\1","\\3")+"|")(),d.tables=f({},d.gfm,{nptable:/^ *(\S.*\|.*)\n *([-:]+ *\|[-| :]*)\n((?:.*\|.*(?:\n|$))*)\n*/,table:/^ *\|(.+)\n *\|( *[-:]+[-| :]*)\n((?: *\|.*(?:\n|$))*)\n*/}),t.rules=d,t.lex=function(e,n){var r=new t(n);return r.lex(e)},t.prototype.lex=function(e){return e=e.replace(/\r\n|\r/g,"\n").replace(/\t/g,"    ").replace(/\u00a0/g," ").replace(/\u2424/g,"\n"),this.token(e,!0)},t.prototype.token=function(e,t,n){for(var r,i,o,a,l,s,c,u,f,e=e.replace(/^ +$/gm,"");e;)if((o=this.rules.newline.exec(e))&&(e=e.substring(o[0].length),o[0].length>1&&this.tokens.push({type:"space"})),o=this.rules.code.exec(e))e=e.substring(o[0].length),o=o[0].replace(/^ {4}/gm,""),this.tokens.push({type:"code",text:this.options.pedantic?o:o.replace(/\n+$/,"")});else if(o=this.rules.fences.exec(e))e=e.substring(o[0].length),this.tokens.push({type:"code",lang:o[2],text:o[3]||""});else if(o=this.rules.heading.exec(e))e=e.substring(o[0].length),this.tokens.push({type:"heading",depth:o[1].length,text:o[2]});else if(t&&(o=this.rules.nptable.exec(e))){for(e=e.substring(o[0].length),s={type:"table",header:o[1].replace(/^ *| *\| *$/g,"").split(/ *\| */),align:o[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:o[3].replace(/\n$/,"").split("\n")},u=0;u<s.align.length;u++)/^ *-+: *$/.test(s.align[u])?s.align[u]="right":/^ *:-+: *$/.test(s.align[u])?s.align[u]="center":/^ *:-+ *$/.test(s.align[u])?s.align[u]="left":s.align[u]=null;for(u=0;u<s.cells.length;u++)s.cells[u]=s.cells[u].split(/ *\| */);this.tokens.push(s)}else if(o=this.rules.lheading.exec(e))e=e.substring(o[0].length),this.tokens.push({type:"heading",depth:"="===o[2]?1:2,text:o[1]});else if(o=this.rules.hr.exec(e))e=e.substring(o[0].length),this.tokens.push({type:"hr"});else if(o=this.rules.blockquote.exec(e))e=e.substring(o[0].length),this.tokens.push({type:"blockquote_start"}),o=o[0].replace(/^ *> ?/gm,""),this.token(o,t,!0),this.tokens.push({type:"blockquote_end"});else if(o=this.rules.list.exec(e)){for(e=e.substring(o[0].length),a=o[2],this.tokens.push({type:"list_start",ordered:a.length>1}),o=o[0].match(this.rules.item),r=!1,f=o.length,u=0;u<f;u++)s=o[u],c=s.length,s=s.replace(/^ *([*+-]|\d+\.) +/,""),~s.indexOf("\n ")&&(c-=s.length,s=this.options.pedantic?s.replace(/^ {1,4}/gm,""):s.replace(new RegExp("^ {1,"+c+"}","gm"),"")),this.options.smartLists&&u!==f-1&&(l=d.bullet.exec(o[u+1])[0],a===l||a.length>1&&l.length>1||(e=o.slice(u+1).join("\n")+e,u=f-1)),i=r||/\n\n(?!\s*$)/.test(s),u!==f-1&&(r="\n"===s.charAt(s.length-1),i||(i=r)),this.tokens.push({type:i?"loose_item_start":"list_item_start"}),this.token(s,!1,n),this.tokens.push({type:"list_item_end"});this.tokens.push({type:"list_end"})}else if(o=this.rules.html.exec(e))e=e.substring(o[0].length),this.tokens.push({type:this.options.sanitize?"paragraph":"html",pre:!this.options.sanitizer&&("pre"===o[1]||"script"===o[1]||"style"===o[1]),text:o[0]});else if(!n&&t&&(o=this.rules.def.exec(e)))e=e.substring(o[0].length),this.tokens.links[o[1].toLowerCase()]={href:o[2],title:o[3]};else if(t&&(o=this.rules.table.exec(e))){for(e=e.substring(o[0].length),s={type:"table",header:o[1].replace(/^ *| *\| *$/g,"").split(/ *\| */),align:o[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:o[3].replace(/(?: *\| *)?\n$/,"").split("\n")},u=0;u<s.align.length;u++)/^ *-+: *$/.test(s.align[u])?s.align[u]="right":/^ *:-+: *$/.test(s.align[u])?s.align[u]="center":/^ *:-+ *$/.test(s.align[u])?s.align[u]="left":s.align[u]=null;for(u=0;u<s.cells.length;u++)s.cells[u]=s.cells[u].replace(/^ *\| *| *\| *$/g,"").split(/ *\| */);this.tokens.push(s)}else if(t&&(o=this.rules.paragraph.exec(e)))e=e.substring(o[0].length),this.tokens.push({type:"paragraph",text:"\n"===o[1].charAt(o[1].length-1)?o[1].slice(0,-1):o[1]});else if(o=this.rules.text.exec(e))e=e.substring(o[0].length),this.tokens.push({type:"text",text:o[0]});else if(e)throw new Error("Infinite loop on byte: "+e.charCodeAt(0));return this.tokens};var p={escape:/^\\([\\`*{}\[\]()#+\-.!_>])/,autolink:/^<([^ >]+(@|:\/)[^ >]+)>/,url:u,tag:/^<!--[\s\S]*?-->|^<\/?\w+(?:"[^"]*"|'[^']*'|[^'">])*?>/,link:/^!?\[(inside)\]\(href\)/,reflink:/^!?\[(inside)\]\s*\[([^\]]*)\]/,nolink:/^!?\[((?:\[[^\]]*\]|[^\[\]])*)\]/,strong:/^__([\s\S]+?)__(?!_)|^\*\*([\s\S]+?)\*\*(?!\*)/,em:/^\b_((?:[^_]|__)+?)_\b|^\*((?:\*\*|[\s\S])+?)\*(?!\*)/,code:/^(`+)\s*([\s\S]*?[^`])\s*\1(?!`)/,br:/^ {2,}\n(?!\s*$)/,del:u,text:/^[\s\S]+?(?=[\\<!\[_*`]| {2,}\n|$)/};p._inside=/(?:\[[^\]]*\]|[^\[\]]|\](?=[^\[]*\]))*/,p._href=/\s*<?([\s\S]*?)>?(?:\s+['"]([\s\S]*?)['"])?\s*/,p.link=c(p.link)("inside",p._inside)("href",p._href)(),p.reflink=c(p.reflink)("inside",p._inside)(),p.normal=f({},p),p.pedantic=f({},p.normal,{strong:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,em:/^_(?=\S)([\s\S]*?\S)_(?!_)|^\*(?=\S)([\s\S]*?\S)\*(?!\*)/}),p.gfm=f({},p.normal,{escape:c(p.escape)("])","~|])")(),url:/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,del:/^~~(?=\S)([\s\S]*?\S)~~/,text:c(p.text)("]|","~]|")("|","|https?://|")()}),p.breaks=f({},p.gfm,{br:c(p.br)("{2,}","*")(),text:c(p.gfm.text)("{2,}","*")()}),i.rules=p,i.output=function(e,t,n){var r=new i(t,n);return r.output(e)},i.prototype.output=function(e){for(var t,n,r,i,o="";e;)if(i=this.rules.escape.exec(e))e=e.substring(i[0].length),o+=i[1];else if(i=this.rules.autolink.exec(e))e=e.substring(i[0].length),"@"===i[2]?(n=":"===i[1].charAt(6)?this.mangle(i[1].substring(7)):this.mangle(i[1]),r=this.mangle("mailto:")+n):(n=l(i[1]),r=n),o+=this.renderer.link(r,null,n);else if(this.inLink||!(i=this.rules.url.exec(e))){if(i=this.rules.tag.exec(e))!this.inLink&&/^<a /i.test(i[0])?this.inLink=!0:this.inLink&&/^<\/a>/i.test(i[0])&&(this.inLink=!1),e=e.substring(i[0].length),o+=this.options.sanitize?this.options.sanitizer?this.options.sanitizer(i[0]):l(i[0]):i[0];else if(i=this.rules.link.exec(e))e=e.substring(i[0].length),this.inLink=!0,o+=this.outputLink(i,{href:i[2],title:i[3]}),this.inLink=!1;else if((i=this.rules.reflink.exec(e))||(i=this.rules.nolink.exec(e))){if(e=e.substring(i[0].length),t=(i[2]||i[1]).replace(/\s+/g," "),t=this.links[t.toLowerCase()],!t||!t.href){o+=i[0].charAt(0),e=i[0].substring(1)+e;continue}this.inLink=!0,o+=this.outputLink(i,t),this.inLink=!1}else if(i=this.rules.strong.exec(e))e=e.substring(i[0].length),o+=this.renderer.strong(this.output(i[2]||i[1]));else if(i=this.rules.em.exec(e))e=e.substring(i[0].length),o+=this.renderer.em(this.output(i[2]||i[1]));else if(i=this.rules.code.exec(e))e=e.substring(i[0].length),o+=this.renderer.codespan(l(i[2],!0));else if(i=this.rules.br.exec(e))e=e.substring(i[0].length),o+=this.renderer.br();else if(i=this.rules.del.exec(e))e=e.substring(i[0].length),o+=this.renderer.del(this.output(i[1]));else if(i=this.rules.text.exec(e))e=e.substring(i[0].length),o+=this.renderer.text(l(this.smartypants(i[0])));else if(e)throw new Error("Infinite loop on byte: "+e.charCodeAt(0))}else e=e.substring(i[0].length),n=l(i[1]),r=n,o+=this.renderer.link(r,null,n);return o},i.prototype.outputLink=function(e,t){var n=l(t.href),r=t.title?l(t.title):null;return"!"!==e[0].charAt(0)?this.renderer.link(n,r,this.output(e[1])):this.renderer.image(n,r,l(e[1]))},i.prototype.smartypants=function(e){return this.options.smartypants?e.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014\/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014\/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…"):e},i.prototype.mangle=function(e){if(!this.options.mangle)return e;for(var t,n="",r=e.length,i=0;i<r;i++)t=e.charCodeAt(i),Math.random()>.5&&(t="x"+t.toString(16)),n+="&#"+t+";";return n},o.prototype.code=function(e,t,n){if(this.options.highlight){var r=this.options.highlight(e,t);null!=r&&r!==e&&(n=!0,e=r)}return t?'<pre><code class="'+this.options.langPrefix+l(t,!0)+'">'+(n?e:l(e,!0))+"\n</code></pre>\n":"<pre><code>"+(n?e:l(e,!0))+"\n</code></pre>"},o.prototype.blockquote=function(e){return"<blockquote>\n"+e+"</blockquote>\n"},o.prototype.html=function(e){return e},o.prototype.heading=function(e,t,n){return"<h"+t+' id="'+this.options.headerPrefix+n.toLowerCase().replace(/[^\w]+/g,"-")+'">'+e+"</h"+t+">\n"},o.prototype.hr=function(){return this.options.xhtml?"<hr/>\n":"<hr>\n"},o.prototype.list=function(e,t){var n=t?"ol":"ul";return"<"+n+">\n"+e+"</"+n+">\n"},o.prototype.listitem=function(e){return"<li>"+e+"</li>\n"},o.prototype.paragraph=function(e){return"<p>"+e+"</p>\n"},o.prototype.table=function(e,t){return"<table>\n<thead>\n"+e+"</thead>\n<tbody>\n"+t+"</tbody>\n</table>\n"},o.prototype.tablerow=function(e){return"<tr>\n"+e+"</tr>\n"},o.prototype.tablecell=function(e,t){var n=t.header?"th":"td",r=t.align?"<"+n+' style="text-align:'+t.align+'">':"<"+n+">";return r+e+"</"+n+">\n"},o.prototype.strong=function(e){return"<strong>"+e+"</strong>"},o.prototype.em=function(e){return"<em>"+e+"</em>"},o.prototype.codespan=function(e){return"<code>"+e+"</code>"},o.prototype.br=function(){return this.options.xhtml?"<br/>":"<br>"},o.prototype.del=function(e){return"<del>"+e+"</del>"},o.prototype.link=function(e,t,n){if(this.options.sanitize){try{var r=decodeURIComponent(s(e)).replace(/[^\w:]/g,"").toLowerCase()}catch(e){return""}if(0===r.indexOf("javascript:")||0===r.indexOf("vbscript:"))return""}var i='<a href="'+e+'"';return t&&(i+=' title="'+t+'"'),i+=">"+n+"</a>"},o.prototype.image=function(e,t,n){var r='<img src="'+e+'" alt="'+n+'"';return t&&(r+=' title="'+t+'"'),r+=this.options.xhtml?"/>":">"},o.prototype.text=function(e){return e},a.parse=function(e,t,n){var r=new a(t,n);return r.parse(e)},a.prototype.parse=function(e){this.inline=new i(e.links,this.options,this.renderer),this.tokens=e.reverse();for(var t="";this.next();)t+=this.tok();return t},a.prototype.next=function(){return this.token=this.tokens.pop()},a.prototype.peek=function(){return this.tokens[this.tokens.length-1]||0},a.prototype.parseText=function(){for(var e=this.token.text;"text"===this.peek().type;)e+="\n"+this.next().text;return this.inline.output(e)},a.prototype.tok=function(){switch(this.token.type){case"space":return"";case"hr":return this.renderer.hr();case"heading":return this.renderer.heading(this.inline.output(this.token.text),this.token.depth,this.token.text);case"code":return this.renderer.code(this.token.text,this.token.lang,this.token.escaped);case"table":var e,t,n,r,i,o="",a="";for(n="",e=0;e<this.token.header.length;e++)r={header:!0,align:this.token.align[e]},n+=this.renderer.tablecell(this.inline.output(this.token.header[e]),{header:!0,align:this.token.align[e]});for(o+=this.renderer.tablerow(n),e=0;e<this.token.cells.length;e++){for(t=this.token.cells[e],n="",i=0;i<t.length;i++)n+=this.renderer.tablecell(this.inline.output(t[i]),{header:!1,align:this.token.align[i]});a+=this.renderer.tablerow(n)}return this.renderer.table(o,a);case"blockquote_start":for(var a="";"blockquote_end"!==this.next().type;)a+=this.tok();return this.renderer.blockquote(a);case"list_start":for(var a="",l=this.token.ordered;"list_end"!==this.next().type;)a+=this.tok();return this.renderer.list(a,l);case"list_item_start":for(var a="";"list_item_end"!==this.next().type;)a+="text"===this.token.type?this.parseText():this.tok();return this.renderer.listitem(a);case"loose_item_start":for(var a="";"list_item_end"!==this.next().type;)a+=this.tok();return this.renderer.listitem(a);case"html":var s=this.token.pre||this.options.pedantic?this.token.text:this.inline.output(this.token.text);return this.renderer.html(s);case"paragraph":return this.renderer.paragraph(this.inline.output(this.token.text));case"text":return this.renderer.paragraph(this.parseText())}},u.exec=u,h.options=h.setOptions=function(e){return f(h.defaults,e),h},h.defaults={gfm:!0,tables:!0,breaks:!1,pedantic:!1,sanitize:!1,sanitizer:null,mangle:!0,smartLists:!1,silent:!1,highlight:null,langPrefix:"lang-",smartypants:!1,headerPrefix:"",renderer:new o,xhtml:!1},h.Parser=a,h.parser=a.parse,h.Renderer=o,h.Lexer=t,h.lexer=t.lex,h.InlineLexer=i,h.inlineLexer=i.output,h.parse=h,"undefined"!=typeof n&&"object"==typeof r?n.exports=h:"function"==typeof e&&e.amd?e(function(){return h}):this.marked=h}).call(function(){return this||("undefined"!=typeof window?window:t)}())}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],18:[function(e,t,n){(function(n,r){var i;!function(){"use strict";i=function(e,t,n,i){function o(e,t){var n=p._readFile(e,null,i.asyncLoad);i.asyncLoad?n.then(function(e){t(e)}):t(n)}function a(e){t=e,n&&s()}function l(e){n=e,t&&s()}function s(){for(p.rules=p._parseAFF(t),p.compoundRuleCodes={},u=0,h=p.compoundRules.length;u<h;u++){var e=p.compoundRules[u];for(f=0,d=e.length;f<d;f++)p.compoundRuleCodes[e[f]]=[]}"ONLYINCOMPOUND"in p.flags&&(p.compoundRuleCodes[p.flags.ONLYINCOMPOUND]=[]),p.dictionaryTable=p._parseDIC(n);for(u in p.compoundRuleCodes)0===p.compoundRuleCodes[u].length&&delete p.compoundRuleCodes[u];for(u=0,h=p.compoundRules.length;u<h;u++){var r=p.compoundRules[u],o="";for(f=0,d=r.length;f<d;f++){var a=r[f];o+=a in p.compoundRuleCodes?"("+p.compoundRuleCodes[a].join("|")+")":a}p.compoundRules[u]=new RegExp(o,"i")}p.loaded=!0,i.asyncLoad&&i.loadedCallback&&i.loadedCallback(p)}i=i||{},this.dictionary=null,this.rules={},this.dictionaryTable={},this.compoundRules=[],this.compoundRuleCodes={},this.replacementTable=[],this.flags=i.flags||{},this.memoized={},this.loaded=!1;var c,u,f,h,d,p=this;return e&&(p.dictionary=e,t&&n?s():"undefined"!=typeof window&&"chrome"in window&&"extension"in window.chrome&&"getURL"in window.chrome.extension?(c=i.dictionaryPath?i.dictionaryPath:"typo/dictionaries",t||o(chrome.extension.getURL(c+"/"+e+"/"+e+".aff"),a),n||o(chrome.extension.getURL(c+"/"+e+"/"+e+".dic"),l)):(c=i.dictionaryPath?i.dictionaryPath:"undefined"!=typeof r?r+"/dictionaries":"./dictionaries",t||o(c+"/"+e+"/"+e+".aff",a),n||o(c+"/"+e+"/"+e+".dic",l))),this},i.prototype={load:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);return this},_readFile:function(t,r,i){if(r=r||"utf8","undefined"!=typeof XMLHttpRequest){var o,a=new XMLHttpRequest;return a.open("GET",t,i),i&&(o=new Promise(function(e,t){a.onload=function(){200===a.status?e(a.responseText):t(a.statusText)},a.onerror=function(){t(a.statusText)}})),a.overrideMimeType&&a.overrideMimeType("text/plain; charset="+r),a.send(null),i?o:a.responseText}if("undefined"!=typeof e){var l=e("fs");try{if(l.existsSync(t)){var s=l.statSync(t),c=l.openSync(t,"r"),u=new n(s.size);return l.readSync(c,u,0,u.length,null),u.toString(r,0,u.length)}console.log("Path "+t+" does not exist.")}catch(e){return console.log(e),""}}},_parseAFF:function(e){var t,n,r,i,o,a,l,s,c={};e=this._removeAffixComments(e);var u=e.split("\n");for(o=0,l=u.length;o<l;o++){t=u[o];var f=t.split(/\s+/),h=f[0];if("PFX"==h||"SFX"==h){var d=f[1],p=f[2];r=parseInt(f[3],10);var m=[];for(a=o+1,s=o+1+r;a<s;a++){n=u[a],i=n.split(/\s+/);var g=i[2],v=i[3].split("/"),y=v[0];"0"===y&&(y="");var x=this.parseRuleCodes(v[1]),b=i[4],w={};w.add=y,x.length>0&&(w.continuationClasses=x),"."!==b&&("SFX"===h?w.match=new RegExp(b+"$"):w.match=new RegExp("^"+b)),"0"!=g&&("SFX"===h?w.remove=new RegExp(g+"$"):w.remove=g),m.push(w)}c[d]={type:h,combineable:"Y"==p,entries:m},o+=r}else if("COMPOUNDRULE"===h){for(r=parseInt(f[1],10),a=o+1,s=o+1+r;a<s;a++)t=u[a],i=t.split(/\s+/),this.compoundRules.push(i[1]);o+=r}else"REP"===h?(i=t.split(/\s+/),3===i.length&&this.replacementTable.push([i[1],i[2]])):this.flags[h]=f[1]}return c},_removeAffixComments:function(e){return e=e.replace(/#.*$/gm,""),e=e.replace(/^\s\s*/m,"").replace(/\s\s*$/m,""),e=e.replace(/\n{2,}/g,"\n"),e=e.replace(/^\s\s*/,"").replace(/\s\s*$/,"")},_parseDIC:function(e){function t(e,t){r.hasOwnProperty(e)||(r[e]=null),t.length>0&&(null===r[e]&&(r[e]=[]),r[e].push(t))}e=this._removeDicComments(e);for(var n=e.split("\n"),r={},i=1,o=n.length;i<o;i++){var a=n[i],l=a.split("/",2),s=l[0];if(l.length>1){var c=this.parseRuleCodes(l[1]);"NEEDAFFIX"in this.flags&&c.indexOf(this.flags.NEEDAFFIX)!=-1||t(s,c);for(var u=0,f=c.length;u<f;u++){var h=c[u],d=this.rules[h];if(d)for(var p=this._applyRule(s,d),m=0,g=p.length;m<g;m++){var v=p[m];if(t(v,[]),d.combineable)for(var y=u+1;y<f;y++){var x=c[y],b=this.rules[x];if(b&&b.combineable&&d.type!=b.type)for(var w=this._applyRule(v,b),k=0,S=w.length;k<S;k++){var C=w[k];t(C,[])}}}h in this.compoundRuleCodes&&this.compoundRuleCodes[h].push(s)}}else t(s.trim(),[])}return r},_removeDicComments:function(e){return e=e.replace(/^\t.*$/gm,"")},parseRuleCodes:function(e){if(!e)return[];if(!("FLAG"in this.flags))return e.split("");if("long"===this.flags.FLAG){for(var t=[],n=0,r=e.length;n<r;n+=2)t.push(e.substr(n,2));return t}return"num"===this.flags.FLAG?e.split(","):void 0},_applyRule:function(e,t){for(var n=t.entries,r=[],i=0,o=n.length;i<o;i++){var a=n[i];if(!a.match||e.match(a.match)){var l=e;if(a.remove&&(l=l.replace(a.remove,"")),"SFX"===t.type?l+=a.add:l=a.add+l,r.push(l),"continuationClasses"in a)for(var s=0,c=a.continuationClasses.length;s<c;s++){var u=this.rules[a.continuationClasses[s]];u&&(r=r.concat(this._applyRule(l,u)))}}}return r},check:function(e){if(!this.loaded)throw"Dictionary not loaded.";var t=e.replace(/^\s\s*/,"").replace(/\s\s*$/,"");if(this.checkExact(t))return!0;if(t.toUpperCase()===t){var n=t[0]+t.substring(1).toLowerCase();if(this.hasFlag(n,"KEEPCASE"))return!1;if(this.checkExact(n))return!0}var r=t.toLowerCase();if(r!==t){if(this.hasFlag(r,"KEEPCASE"))return!1;if(this.checkExact(r))return!0}return!1},checkExact:function(e){if(!this.loaded)throw"Dictionary not loaded.";var t,n,r=this.dictionaryTable[e];if("undefined"==typeof r){if("COMPOUNDMIN"in this.flags&&e.length>=this.flags.COMPOUNDMIN)for(t=0,n=this.compoundRules.length;t<n;t++)if(e.match(this.compoundRules[t]))return!0}else{if(null===r)return!0;if("object"==typeof r)for(t=0,n=r.length;t<n;t++)if(!this.hasFlag(e,"ONLYINCOMPOUND",r[t]))return!0}return!1},hasFlag:function(e,t,n){if(!this.loaded)throw"Dictionary not loaded.";return!!(t in this.flags&&("undefined"==typeof n&&(n=Array.prototype.concat.apply([],this.dictionaryTable[e])),n&&n.indexOf(this.flags[t])!==-1))},alphabet:"",suggest:function(e,t){function n(e){var t,n,r,i,o,a,l=[];for(t=0,i=e.length;t<i;t++){var s=e[t];for(n=0,o=s.length+1;n<o;n++){var c=[s.substring(0,n),s.substring(n)];if(c[1]&&l.push(c[0]+c[1].substring(1)),c[1].length>1&&c[1][1]!==c[1][0]&&l.push(c[0]+c[1][1]+c[1][0]+c[1].substring(2)),c[1])for(r=0,a=u.alphabet.length;r<a;r++)u.alphabet[r]!=c[1].substring(0,1)&&l.push(c[0]+u.alphabet[r]+c[1].substring(1));if(c[1])for(r=0,a=u.alphabet.length;r<a;r++)l.push(c[0]+u.alphabet[r]+c[1])}}return l}function r(e){for(var t=[],n=0,r=e.length;n<r;n++)u.check(e[n])&&t.push(e[n]);return t}function i(e){function i(e,t){return e[1]<t[1]?-1:1}var o,a,l=n([e]),s=n(l),c=r(l.concat(s)),f={};for(o=0,a=c.length;o<a;o++)c[o]in f?f[c[o]]+=1:f[c[o]]=1;var h=[];for(o in f)f.hasOwnProperty(o)&&h.push([o,f[o]]);h.sort(i).reverse();var d=[],p="lowercase";for(e.toUpperCase()===e?p="uppercase":e.substr(0,1).toUpperCase()+e.substr(1).toLowerCase()===e&&(p="capitalized"),o=0,a=Math.min(t,h.length);o<a;o++)"uppercase"===p?h[o][0]=h[o][0].toUpperCase():"capitalized"===p&&(h[o][0]=h[o][0].substr(0,1).toUpperCase()+h[o][0].substr(1)),u.hasFlag(h[o][0],"NOSUGGEST")||d.push(h[o][0]);return d}if(!this.loaded)throw"Dictionary not loaded.";if(t=t||5,this.memoized.hasOwnProperty(e)){var o=this.memoized[e].limit;if(t<=o||this.memoized[e].suggestions.length<o)return this.memoized[e].suggestions.slice(0,t)}if(this.check(e))return[];for(var a=0,l=this.replacementTable.length;a<l;a++){var s=this.replacementTable[a];if(e.indexOf(s[0])!==-1){var c=e.replace(s[0],s[1]);if(this.check(c))return[c]}}var u=this;return u.alphabet="abcdefghijklmnopqrstuvwxyz",this.memoized[e]={suggestions:i(e),limit:t},this.memoized[e].suggestions}}}(),"undefined"!=typeof t&&(t.exports=i)}).call(this,e("buffer").Buffer,"/node_modules/typo-js")},{buffer:3,fs:2}],19:[function(e,t,n){var r=e("codemirror");r.commands.tabAndIndentMarkdownList=function(e){var t=e.listSelections(),n=t[0].head,r=e.getStateAfter(n.line),i=r.list!==!1;if(i)return void e.execCommand("indentMore");if(e.options.indentWithTabs)e.execCommand("insertTab");else{var o=Array(e.options.tabSize+1).join(" ");e.replaceSelection(o)}},r.commands.shiftTabAndUnindentMarkdownList=function(e){var t=e.listSelections(),n=t[0].head,r=e.getStateAfter(n.line),i=r.list!==!1;if(i)return void e.execCommand("indentLess");if(e.options.indentWithTabs)e.execCommand("insertTab");else{var o=Array(e.options.tabSize+1).join(" ");e.replaceSelection(o)}}},{codemirror:10}],20:[function(e,t,n){"use strict";function r(e){return e=U?e.replace("Ctrl","Cmd"):e.replace("Cmd","Ctrl")}function i(e,t,n){e=e||{};var r=document.createElement("a");return t=void 0==t||t,e.title&&t&&(r.title=a(e.title,e.action,n),U&&(r.title=r.title.replace("Ctrl","⌘"),r.title=r.title.replace("Alt","⌥"))),r.tabIndex=-1,r.className=e.className,r}function o(){var e=document.createElement("i");return e.className="separator",e.innerHTML="|",e}function a(e,t,n){var i,o=e;return t&&(i=Y(t),n[i]&&(o+=" ("+r(n[i])+")")),o}function l(e,t){t=t||e.getCursor("start");var n=e.getTokenAt(t);if(!n.type)return{};for(var r,i,o=n.type.split(" "),a={},l=0;l<o.length;l++)r=o[l],"strong"===r?a.bold=!0:"variable-2"===r?(i=e.getLine(t.line),/^\s*\d+\.\s/.test(i)?a["ordered-list"]=!0:a["unordered-list"]=!0):"atom"===r?a.quote=!0:"em"===r?a.italic=!0:"quote"===r?a.quote=!0:"strikethrough"===r?a.strikethrough=!0:"comment"===r?a.code=!0:"link"===r?a.link=!0:"tag"===r?a.image=!0:r.match(/^header(\-[1-6])?$/)&&(a[r.replace("header","heading")]=!0);return a}function s(e){var t=e.codemirror;t.setOption("fullScreen",!t.getOption("fullScreen")),t.getOption("fullScreen")?(V=document.body.style.overflow,document.body.style.overflow="hidden"):document.body.style.overflow=V;var n=t.getWrapperElement();/fullscreen/.test(n.previousSibling.className)?n.previousSibling.className=n.previousSibling.className.replace(/\s*fullscreen\b/,""):n.previousSibling.className+=" fullscreen";var r=e.toolbarElements.fullscreen;/active/.test(r.className)?r.className=r.className.replace(/\s*active\s*/g,""):r.className+=" active";var i=t.getWrapperElement().nextSibling;/editor-preview-active-side/.test(i.className)&&A(e)}function c(e){P(e,"bold",e.options.blockStyles.bold)}function u(e){P(e,"italic",e.options.blockStyles.italic)}function f(e){P(e,"strikethrough","~~")}function h(e){function t(e){if("object"!=typeof e)throw"fencing_line() takes a 'line' object (not a line number, or line text).  Got: "+typeof e+": "+e;return e.styles&&e.styles[2]&&e.styles[2].indexOf("formatting-code-block")!==-1}function n(e){return e.state.base.base||e.state.base}function r(e,r,i,o,a){i=i||e.getLineHandle(r),o=o||e.getTokenAt({line:r,ch:1}),a=a||!!i.text&&e.getTokenAt({line:r,ch:i.text.length-1});var l=o.type?o.type.split(" "):[];return a&&n(a).indentedCode?"indented":l.indexOf("comment")!==-1&&(n(o).fencedChars||n(a).fencedChars||t(i)?"fenced":"single")}function i(e,t,n,r){var i=t.line+1,o=n.line+1,a=t.line!==n.line,l=r+"\n",s="\n"+r;a&&o++,a&&0===n.ch&&(s=r+"\n",o--),E(e,!1,[l,s]),e.setSelection({line:i,ch:0},{line:o,ch:0})}var o,a,l,s=e.options.blockStyles.code,c=e.codemirror,u=c.getCursor("start"),f=c.getCursor("end"),h=c.getTokenAt({line:u.line,ch:u.ch||1}),d=c.getLineHandle(u.line),p=r(c,u.line,d,h);if("single"===p){var m=d.text.slice(0,u.ch).replace("`",""),g=d.text.slice(u.ch).replace("`","");c.replaceRange(m+g,{line:u.line,ch:0},{line:u.line,ch:99999999999999}),u.ch--,u!==f&&f.ch--,c.setSelection(u,f),c.focus()}else if("fenced"===p)if(u.line!==f.line||u.ch!==f.ch){for(o=u.line;o>=0&&(d=c.getLineHandle(o),!t(d));o--);var v,y,x,b,w=c.getTokenAt({line:o,ch:1}),k=n(w).fencedChars;t(c.getLineHandle(u.line))?(v="",y=u.line):t(c.getLineHandle(u.line-1))?(v="",y=u.line-1):(v=k+"\n",y=u.line),t(c.getLineHandle(f.line))?(x="",b=f.line,0===f.ch&&(b+=1)):0!==f.ch&&t(c.getLineHandle(f.line+1))?(x="",b=f.line+1):(x=k+"\n",b=f.line+1),0===f.ch&&(b-=1),c.operation(function(){c.replaceRange(x,{line:b,ch:0},{line:b+(x?0:1),ch:0}),c.replaceRange(v,{line:y,ch:0},{line:y+(v?0:1),ch:0})}),c.setSelection({line:y+(v?1:0),ch:0},{line:b+(v?1:-1),ch:0}),c.focus()}else{var S=u.line;if(t(c.getLineHandle(u.line))&&("fenced"===r(c,u.line+1)?(o=u.line,S=u.line+1):(a=u.line,S=u.line-1)),void 0===o)for(o=S;o>=0&&(d=c.getLineHandle(o),!t(d));o--);if(void 0===a)for(l=c.lineCount(),a=S;a<l&&(d=c.getLineHandle(a),!t(d));a++);c.operation(function(){c.replaceRange("",{line:o,ch:0},{line:o+1,ch:0}),c.replaceRange("",{line:a-1,ch:0},{line:a,ch:0})}),c.focus()}else if("indented"===p){if(u.line!==f.line||u.ch!==f.ch)o=u.line,a=f.line,0===f.ch&&a--;else{for(o=u.line;o>=0;o--)if(d=c.getLineHandle(o),!d.text.match(/^\s*$/)&&"indented"!==r(c,o,d)){o+=1;break}for(l=c.lineCount(),a=u.line;a<l;a++)if(d=c.getLineHandle(a),!d.text.match(/^\s*$/)&&"indented"!==r(c,a,d)){a-=1;break}}var C=c.getLineHandle(a+1),L=C&&c.getTokenAt({line:a+1,ch:C.text.length-1}),T=L&&n(L).indentedCode;T&&c.replaceRange("\n",{line:a+1,ch:0});for(var M=o;M<=a;M++)c.indentLine(M,"subtract");c.focus()}else{var A=u.line===f.line&&u.ch===f.ch&&0===u.ch,N=u.line!==f.line;A||N?i(c,u,f,s):E(c,!1,["`","`"])}}function d(e){var t=e.codemirror;I(t,"quote")}function p(e){var t=e.codemirror;O(t,"smaller")}function m(e){var t=e.codemirror;O(t,"bigger")}function g(e){var t=e.codemirror;O(t,void 0,1)}function v(e){var t=e.codemirror;O(t,void 0,2)}function y(e){var t=e.codemirror;O(t,void 0,3)}function x(e){var t=e.codemirror;I(t,"unordered-list")}function b(e){var t=e.codemirror;I(t,"ordered-list")}function w(e){var t=e.codemirror;R(t)}function k(e){var t=e.codemirror,n=l(t),r=e.options,i="http://";return!(r.promptURLs&&(i=prompt(r.promptTexts.link),!i))&&void E(t,n.link,r.insertTexts.link,i)}function S(e){var t=e.codemirror,n=l(t),r=e.options,i="http://";return!(r.promptURLs&&(i=prompt(r.promptTexts.image),!i))&&void E(t,n.image,r.insertTexts.image,i)}function C(e){var t=e.codemirror,n=l(t),r=e.options;E(t,n.table,r.insertTexts.table)}function L(e){var t=e.codemirror,n=l(t),r=e.options;E(t,n.image,r.insertTexts.horizontalRule)}function T(e){var t=e.codemirror;t.undo(),t.focus()}function M(e){var t=e.codemirror;t.redo(),t.focus()}function A(e){var t=e.codemirror,n=t.getWrapperElement(),r=n.nextSibling,i=e.toolbarElements["side-by-side"],o=!1;/editor-preview-active-side/.test(r.className)?(r.className=r.className.replace(/\s*editor-preview-active-side\s*/g,""),i.className=i.className.replace(/\s*active\s*/g,""),n.className=n.className.replace(/\s*CodeMirror-sided\s*/g," ")):(setTimeout(function(){t.getOption("fullScreen")||s(e),r.className+=" editor-preview-active-side"},1),i.className+=" active",n.className+=" CodeMirror-sided",o=!0);var a=n.lastChild;if(/editor-preview-active/.test(a.className)){a.className=a.className.replace(/\s*editor-preview-active\s*/g,"");var l=e.toolbarElements.preview,c=n.previousSibling;l.className=l.className.replace(/\s*active\s*/g,""),c.className=c.className.replace(/\s*disabled-for-preview*/g,"")}var u=function(){r.innerHTML=e.options.previewRender(e.value(),r)};t.sideBySideRenderingFunction||(t.sideBySideRenderingFunction=u),o?(r.innerHTML=e.options.previewRender(e.value(),r),t.on("update",t.sideBySideRenderingFunction)):t.off("update",t.sideBySideRenderingFunction),t.refresh()}function N(e){var t=e.codemirror,n=t.getWrapperElement(),r=n.previousSibling,i=!!e.options.toolbar&&e.toolbarElements.preview,o=n.lastChild;o&&/editor-preview/.test(o.className)||(o=document.createElement("div"),o.className="editor-preview",n.appendChild(o)),/editor-preview-active/.test(o.className)?(o.className=o.className.replace(/\s*editor-preview-active\s*/g,""),i&&(i.className=i.className.replace(/\s*active\s*/g,""),r.className=r.className.replace(/\s*disabled-for-preview*/g,""))):(setTimeout(function(){o.className+=" editor-preview-active"},1),i&&(i.className+=" active",r.className+=" disabled-for-preview")),o.innerHTML=e.options.previewRender(e.value(),o);var a=t.getWrapperElement().nextSibling;/editor-preview-active-side/.test(a.className)&&A(e)}function E(e,t,n,r){if(!/editor-preview-active/.test(e.getWrapperElement().lastChild.className)){var i,o=n[0],a=n[1],l=e.getCursor("start"),s=e.getCursor("end");r&&(a=a.replace("#url#",r)),t?(i=e.getLine(l.line),o=i.slice(0,l.ch),a=i.slice(l.ch),e.replaceRange(o+a,{line:l.line,ch:0})):(i=e.getSelection(),e.replaceSelection(o+i+a),l.ch+=o.length,l!==s&&(s.ch+=o.length)),e.setSelection(l,s),e.focus()}}function O(e,t,n){if(!/editor-preview-active/.test(e.getWrapperElement().lastChild.className)){for(var r=e.getCursor("start"),i=e.getCursor("end"),o=r.line;o<=i.line;o++)!function(r){var i=e.getLine(r),o=i.search(/[^#]/);i=void 0!==t?o<=0?"bigger"==t?"###### "+i:"# "+i:6==o&&"smaller"==t?i.substr(7):1==o&&"bigger"==t?i.substr(2):"bigger"==t?i.substr(1):"#"+i:1==n?o<=0?"# "+i:o==n?i.substr(o+1):"# "+i.substr(o+1):2==n?o<=0?"## "+i:o==n?i.substr(o+1):"## "+i.substr(o+1):o<=0?"### "+i:o==n?i.substr(o+1):"### "+i.substr(o+1),e.replaceRange(i,{line:r,ch:0},{line:r,ch:99999999999999})}(o);e.focus()}}function I(e,t){if(!/editor-preview-active/.test(e.getWrapperElement().lastChild.className)){for(var n=l(e),r=e.getCursor("start"),i=e.getCursor("end"),o={quote:/^(\s*)\>\s+/,"unordered-list":/^(\s*)(\*|\-|\+)\s+/,"ordered-list":/^(\s*)\d+\.\s+/},a={quote:"> ","unordered-list":"* ","ordered-list":"1. "},s=r.line;s<=i.line;s++)!function(r){var i=e.getLine(r);i=n[t]?i.replace(o[t],"$1"):a[t]+i,e.replaceRange(i,{line:r,ch:0},{line:r,ch:99999999999999})}(s);e.focus()}}function P(e,t,n,r){if(!/editor-preview-active/.test(e.codemirror.getWrapperElement().lastChild.className)){r="undefined"==typeof r?n:r;var i,o=e.codemirror,a=l(o),s=n,c=r,u=o.getCursor("start"),f=o.getCursor("end");a[t]?(i=o.getLine(u.line),s=i.slice(0,u.ch),c=i.slice(u.ch),"bold"==t?(s=s.replace(/(\*\*|__)(?![\s\S]*(\*\*|__))/,""),c=c.replace(/(\*\*|__)/,"")):"italic"==t?(s=s.replace(/(\*|_)(?![\s\S]*(\*|_))/,""),c=c.replace(/(\*|_)/,"")):"strikethrough"==t&&(s=s.replace(/(\*\*|~~)(?![\s\S]*(\*\*|~~))/,""),c=c.replace(/(\*\*|~~)/,"")),o.replaceRange(s+c,{line:u.line,ch:0},{line:u.line,ch:99999999999999}),"bold"==t||"strikethrough"==t?(u.ch-=2,u!==f&&(f.ch-=2)):"italic"==t&&(u.ch-=1,u!==f&&(f.ch-=1))):(i=o.getSelection(),"bold"==t?(i=i.split("**").join(""),i=i.split("__").join("")):"italic"==t?(i=i.split("*").join(""),i=i.split("_").join("")):"strikethrough"==t&&(i=i.split("~~").join("")),o.replaceSelection(s+i+c),u.ch+=n.length,f.ch=u.ch+i.length),o.setSelection(u,f),o.focus()}}function R(e){if(!/editor-preview-active/.test(e.getWrapperElement().lastChild.className))for(var t,n=e.getCursor("start"),r=e.getCursor("end"),i=n.line;i<=r.line;i++)t=e.getLine(i),
t=t.replace(/^[ ]*([# ]+|\*|\-|[> ]+|[0-9]+(.|\)))[ ]*/,""),e.replaceRange(t,{line:i,ch:0},{line:i,ch:99999999999999})}function D(e,t){for(var n in t)t.hasOwnProperty(n)&&(t[n]instanceof Array?e[n]=t[n].concat(e[n]instanceof Array?e[n]:[]):null!==t[n]&&"object"==typeof t[n]&&t[n].constructor===Object?e[n]=D(e[n]||{},t[n]):e[n]=t[n]);return e}function H(e){for(var t=1;t<arguments.length;t++)e=D(e,arguments[t]);return e}function W(e){var t=/[a-zA-Z0-9_\u0392-\u03c9\u0410-\u04F9]+|[\u4E00-\u9FFF\u3400-\u4dbf\uf900-\ufaff\u3040-\u309f\uac00-\ud7af]+/g,n=e.match(t),r=0;if(null===n)return r;for(var i=0;i<n.length;i++)r+=n[i].charCodeAt(0)>=19968?n[i].length:1;return r}function B(e){e=e||{},e.parent=this;var t=!0;if(e.autoDownloadFontAwesome===!1&&(t=!1),e.autoDownloadFontAwesome!==!0)for(var n=document.styleSheets,r=0;r<n.length;r++)n[r].href&&n[r].href.indexOf("/fontawesome/css/")>-1&&(t=!1);if(t){var i=document.createElement("link");i.rel="stylesheet",i.href=window.config.webRoot+'js/markdown/fontawesome/css/fontawesome.min.css',document.getElementsByTagName("head")[0].appendChild(i)}if(e.element)this.element=e.element;else if(null===e.element)return void console.log("SimpleMDE: Error. No element was found.");if(void 0===e.toolbar){e.toolbar=[];for(var o in K)K.hasOwnProperty(o)&&(o.indexOf("separator-")!=-1&&e.toolbar.push("|"),(K[o].default===!0||e.showIcons&&e.showIcons.constructor===Array&&e.showIcons.indexOf(o)!=-1)&&e.toolbar.push(o))}e.hasOwnProperty("status")||(e.status=["autosave","lines","words","cursor"]),e.previewRender||(e.previewRender=function(e){return this.parent.markdown(e)}),e.parsingConfig=H({highlightFormatting:!0},e.parsingConfig||{}),e.insertTexts=H({},X,e.insertTexts||{}),e.promptTexts=Z,e.blockStyles=H({},J,e.blockStyles||{}),e.shortcuts=H({},G,e.shortcuts||{}),void 0!=e.autosave&&void 0!=e.autosave.unique_id&&""!=e.autosave.unique_id&&(e.autosave.uniqueId=e.autosave.unique_id),this.options=e,this.render(),!e.initialValue||this.options.autosave&&this.options.autosave.foundSavedValue===!0||this.value(e.initialValue)}function _(){if("object"!=typeof localStorage)return!1;try{localStorage.setItem("smde_localStorage",1),localStorage.removeItem("smde_localStorage")}catch(e){return!1}return!0}var F=e("codemirror");e("codemirror/addon/edit/continuelist.js"),e("./codemirror/tablist"),e("codemirror/addon/display/fullscreen.js"),e("codemirror/mode/markdown/markdown.js"),e("codemirror/addon/mode/overlay.js"),e("codemirror/addon/display/placeholder.js"),e("codemirror/addon/selection/mark-selection.js"),e("codemirror/mode/gfm/gfm.js"),e("codemirror/mode/xml/xml.js");var z=e("codemirror-spell-checker"),j=e("marked"),U=/Mac/.test(navigator.platform),q={toggleBold:c,toggleItalic:u,drawLink:k,toggleHeadingSmaller:p,toggleHeadingBigger:m,drawImage:S,toggleBlockquote:d,toggleOrderedList:b,toggleUnorderedList:x,toggleCodeBlock:h,togglePreview:N,toggleStrikethrough:f,toggleHeading1:g,toggleHeading2:v,toggleHeading3:y,cleanBlock:w,drawTable:C,drawHorizontalRule:L,undo:T,redo:M,toggleSideBySide:A,toggleFullScreen:s},G={toggleBold:"Cmd-B",toggleItalic:"Cmd-I",drawLink:"Cmd-K",toggleHeadingSmaller:"Cmd-H",toggleHeadingBigger:"Shift-Cmd-H",cleanBlock:"Cmd-E",drawImage:"Cmd-Alt-I",toggleBlockquote:"Cmd-'",toggleOrderedList:"Cmd-Alt-L",toggleUnorderedList:"Cmd-L",toggleCodeBlock:"Cmd-Alt-C",togglePreview:"Cmd-P",toggleSideBySide:"F9",toggleFullScreen:"F11"},Y=function(e){for(var t in q)if(q[t]===e)return t;return null},$=function(){var e=!1;return function(t){(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(t)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(t.substr(0,4)))&&(e=!0)}(navigator.userAgent||navigator.vendor||window.opera),e},V="",K={bold:{name:"bold",action:c,className:"icon icon-bold",title:"Bold",default:!0},italic:{name:"italic",action:u,className:"icon icon-italic",title:"Italic",default:!0},strikethrough:{name:"strikethrough",action:f,className:"icon icon-strikethrough",title:"Strikethrough"},heading:{name:"heading",action:p,className:"icon icon-header",title:"Heading",default:!0},"heading-smaller":{name:"heading-smaller",action:p,className:"icon icon-header fa-header-x fa-header-smaller",title:"Smaller Heading"},"heading-bigger":{name:"heading-bigger",action:m,className:"icon icon-header fa-header-x fa-header-bigger",title:"Bigger Heading"},"heading-1":{name:"heading-1",action:g,className:"icon icon-header fa-header-x fa-header-1",title:"Big Heading"},"heading-2":{name:"heading-2",action:v,className:"icon icon-header fa-header-x fa-header-2",title:"Medium Heading"},"heading-3":{name:"heading-3",action:y,className:"icon icon-header fa-header-x fa-header-3",title:"Small Heading"},"separator-1":{name:"separator-1"},code:{name:"code",action:h,className:"icon icon-code",title:"Code"},quote:{name:"quote",action:d,className:"icon icon-quote-left",title:"Quote",default:!0},"unordered-list":{name:"unordered-list",action:x,className:"icon icon-list-ul",title:"Generic List",default:!0},"ordered-list":{name:"ordered-list",action:b,className:"icon icon-list-ol",title:"Numbered List",default:!0},"clean-block":{name:"clean-block",action:w,className:"icon icon-eraser fa-clean-block",title:"Clean block"},"separator-2":{name:"separator-2"},link:{name:"link",action:k,className:"icon icon-link",title:"Create Link",default:!0},image:{name:"image",action:S,className:"icon icon-picture",title:"Insert Image",default:!0},table:{name:"table",action:C,className:"icon icon-table",title:"Insert Table"},"horizontal-rule":{name:"horizontal-rule",action:L,className:"icon icon-minus",title:"Insert Horizontal Line"},"separator-3":{name:"separator-3"},preview:{name:"preview",action:N,className:"icon icon-eye-open no-disable",title:"Toggle Preview",default:!0},"side-by-side":{name:"side-by-side",action:A,className:"icon icon-columns no-disable no-mobile",title:"Toggle Side by Side",default:!0},fullscreen:{name:"fullscreen",action:s,className:"icon icon-expand-full no-disable no-mobile",title:"Toggle Fullscreen",default:!0},"separator-4":{name:"separator-4"},guide:{name:"guide",action:"https://simplemde.com/markdown-guide",className:"icon icon-question-sign",title:"Markdown Guide",default:!0},"separator-5":{name:"separator-5"},undo:{name:"undo",action:T,className:"icon icon-undo no-disable",title:"Undo"},redo:{name:"redo",action:M,className:"icon icon-repeat no-disable",title:"Redo"}},X={link:["[","](#url#)"],image:["![](","#url#)"],table:["","\n\n| Column 1 | Column 2 | Column 3 |\n| -------- | -------- | -------- |\n| Text     | Text     | Text     |\n\n"],horizontalRule:["","\n\n-----\n\n"]},Z={link:"URL for the link:",image:"URL of the image:"},J={bold:"**",code:"```",italic:"*"};B.prototype.markdown=function(e){if(j){var t=((this.options&&this.options.renderingConfig&&this.options.renderingConfig.markedOptions)||{});return this.options&&this.options.renderingConfig&&this.options.renderingConfig.singleLineBreaks===!1?t.breaks=!1:t.breaks=!0,this.options&&this.options.renderingConfig&&this.options.renderingConfig.codeSyntaxHighlighting===!0&&window.hljs&&(t.highlight=function(e){return window.hljs.highlightAuto(e).value}),j.setOptions(t),j(e)}},B.prototype.render=function(e){if(e||(e=this.element||document.getElementsByTagName("textarea")[0]),!this._rendered||this._rendered!==e){this.element=e;var t=this.options,n=this,i={};for(var o in t.shortcuts)null!==t.shortcuts[o]&&null!==q[o]&&!function(e){i[r(t.shortcuts[e])]=function(){q[e](n)}}(o);i.Enter="newlineAndIndentContinueMarkdownList",i.Tab="tabAndIndentMarkdownList",i["Shift-Tab"]="shiftTabAndUnindentMarkdownList",i.Esc=function(e){e.getOption("fullScreen")&&s(n)},document.addEventListener("keydown",function(e){e=e||window.event,27==e.keyCode&&n.codemirror.getOption("fullScreen")&&s(n)},!1);var a,l;if(t.spellChecker!==!1?(a="spell-checker",l=t.parsingConfig,l.name="gfm",l.gitHubSpice=!1,z({codeMirrorInstance:F})):(a=t.parsingConfig,a.name="gfm",a.gitHubSpice=!1),this.codemirror=F.fromTextArea(e,{mode:a,backdrop:l,theme:"paper",tabSize:void 0!=t.tabSize?t.tabSize:2,indentUnit:void 0!=t.tabSize?t.tabSize:2,indentWithTabs:t.indentWithTabs!==!1,lineNumbers:!1,autofocus:t.autofocus===!0,extraKeys:i,lineWrapping:t.lineWrapping!==!1,allowDropFileTypes:["text/plain"],placeholder:t.placeholder||e.getAttribute("placeholder")||"",styleSelectedText:void 0==t.styleSelectedText||t.styleSelectedText}),t.forceSync===!0){var c=this.codemirror;c.on("change",function(){c.save()})}this.gui={},t.toolbar!==!1&&(this.gui.toolbar=this.createToolbar()),t.status!==!1&&(this.gui.statusbar=this.createStatusbar()),void 0!=t.autosave&&t.autosave.enabled===!0&&this.autosave(),this.gui.sideBySide=this.createSideBySide(),this._rendered=this.element;var u=this.codemirror;setTimeout(function(){u.refresh()}.bind(u),0)}},B.prototype.autosave=function(){if(_()){var e=this;if(void 0==this.options.autosave.uniqueId||""==this.options.autosave.uniqueId)return void console.log("SimpleMDE: You must set a uniqueId to use the autosave feature");null!=e.element.form&&void 0!=e.element.form&&e.element.form.addEventListener("submit",function(){localStorage.removeItem("smde_"+e.options.autosave.uniqueId)}),this.options.autosave.loaded!==!0&&("string"==typeof localStorage.getItem("smde_"+this.options.autosave.uniqueId)&&""!=localStorage.getItem("smde_"+this.options.autosave.uniqueId)&&(this.codemirror.setValue(localStorage.getItem("smde_"+this.options.autosave.uniqueId)),this.options.autosave.foundSavedValue=!0),this.options.autosave.loaded=!0),localStorage.setItem("smde_"+this.options.autosave.uniqueId,e.value());var t=document.getElementById("autosaved");if(null!=t&&void 0!=t&&""!=t){var n=new Date,r=n.getHours(),i=n.getMinutes(),o="am",a=r;a>=12&&(a=r-12,o="pm"),0==a&&(a=12),i=i<10?"0"+i:i,t.innerHTML="Autosaved: "+a+":"+i+" "+o}this.autosaveTimeoutId=setTimeout(function(){e.autosave()},this.options.autosave.delay||1e4)}else console.log("SimpleMDE: localStorage not available, cannot autosave")},B.prototype.clearAutosavedValue=function(){if(_()){if(void 0==this.options.autosave||void 0==this.options.autosave.uniqueId||""==this.options.autosave.uniqueId)return void console.log("SimpleMDE: You must set a uniqueId to clear the autosave value");localStorage.removeItem("smde_"+this.options.autosave.uniqueId)}else console.log("SimpleMDE: localStorage not available, cannot autosave")},B.prototype.createSideBySide=function(){var e=this.codemirror,t=e.getWrapperElement(),n=t.nextSibling;n&&/editor-preview-side/.test(n.className)||(n=document.createElement("div"),n.className="editor-preview-side",t.parentNode.insertBefore(n,t.nextSibling));var r=!1,i=!1;return e.on("scroll",function(e){if(r)return void(r=!1);i=!0;var t=e.getScrollInfo().height-e.getScrollInfo().clientHeight,o=parseFloat(e.getScrollInfo().top)/t,a=(n.scrollHeight-n.clientHeight)*o;n.scrollTop=a}),n.onscroll=function(){if(i)return void(i=!1);r=!0;var t=n.scrollHeight-n.clientHeight,o=parseFloat(n.scrollTop)/t,a=(e.getScrollInfo().height-e.getScrollInfo().clientHeight)*o;e.scrollTo(0,a)},n},B.prototype.createToolbar=function(e){if(e=e||this.options.toolbar,e&&0!==e.length){var t;for(t=0;t<e.length;t++)void 0!=K[e[t]]&&(e[t]=K[e[t]]);var n=document.createElement("div");n.className="editor-toolbar";var r=this,a={};for(r.toolbar=e,t=0;t<e.length;t++)if(("guide"!=e[t].name||r.options.toolbarGuideIcon!==!1)&&!(r.options.hideIcons&&r.options.hideIcons.indexOf(e[t].name)!=-1||("fullscreen"==e[t].name||"side-by-side"==e[t].name)&&$())){if("|"===e[t]){for(var s=!1,c=t+1;c<e.length;c++)"|"===e[c]||r.options.hideIcons&&r.options.hideIcons.indexOf(e[c].name)!=-1||(s=!0);if(!s)continue}!function(e){var t;t="|"===e?o():i(e,r.options.toolbarTips,r.options.shortcuts),e.action&&("function"==typeof e.action?t.onclick=function(t){t.preventDefault(),e.action(r)}:"string"==typeof e.action&&(t.href=e.action,t.target="_blank")),a[e.name||e]=t,n.appendChild(t)}(e[t])}r.toolbarElements=a;var u=this.codemirror;u.on("cursorActivity",function(){var e=l(u);for(var t in a)!function(t){var n=a[t];e[t]?n.className+=" active":"fullscreen"!=t&&"side-by-side"!=t&&(n.className=n.className.replace(/\s*active\s*/g,""))}(t)});var f=u.getWrapperElement();return f.parentNode.insertBefore(n,f),n}},B.prototype.createStatusbar=function(e){e=e||this.options.status;var t=this.options,n=this.codemirror;if(e&&0!==e.length){var r,i,o,a=[];for(r=0;r<e.length;r++)if(i=void 0,o=void 0,"object"==typeof e[r])a.push({className:e[r].className,defaultValue:e[r].defaultValue,onUpdate:e[r].onUpdate});else{var l=e[r];"words"===l?(o=function(e){e.innerHTML=W(n.getValue())},i=function(e){e.innerHTML=W(n.getValue())}):"lines"===l?(o=function(e){e.innerHTML=n.lineCount()},i=function(e){e.innerHTML=n.lineCount()}):"cursor"===l?(o=function(e){e.innerHTML="0:0"},i=function(e){var t=n.getCursor();e.innerHTML=t.line+":"+t.ch}):"autosave"===l&&(o=function(e){void 0!=t.autosave&&t.autosave.enabled===!0&&e.setAttribute("id","autosaved")}),a.push({className:l,defaultValue:o,onUpdate:i})}var s=document.createElement("div");for(s.className="editor-statusbar",r=0;r<a.length;r++){var c=a[r],u=document.createElement("span");u.className=c.className,"function"==typeof c.defaultValue&&c.defaultValue(u),"function"==typeof c.onUpdate&&this.codemirror.on("update",function(e,t){return function(){t.onUpdate(e)}}(u,c)),s.appendChild(u)}var f=this.codemirror.getWrapperElement();return f.parentNode.insertBefore(s,f.nextSibling),s}},B.prototype.value=function(e){return void 0===e?this.codemirror.getValue():(this.codemirror.getDoc().setValue(e),this)},B.toggleBold=c,B.toggleItalic=u,B.toggleStrikethrough=f,B.toggleBlockquote=d,B.toggleHeadingSmaller=p,B.toggleHeadingBigger=m,B.toggleHeading1=g,B.toggleHeading2=v,B.toggleHeading3=y,B.toggleCodeBlock=h,B.toggleUnorderedList=x,B.toggleOrderedList=b,B.cleanBlock=w,B.drawLink=k,B.drawImage=S,B.drawTable=C,B.drawHorizontalRule=L,B.undo=T,B.redo=M,B.togglePreview=N,B.toggleSideBySide=A,B.toggleFullScreen=s,B.prototype.toggleBold=function(){c(this)},B.prototype.toggleItalic=function(){u(this)},B.prototype.toggleStrikethrough=function(){f(this)},B.prototype.toggleBlockquote=function(){d(this)},B.prototype.toggleHeadingSmaller=function(){p(this)},B.prototype.toggleHeadingBigger=function(){m(this)},B.prototype.toggleHeading1=function(){g(this)},B.prototype.toggleHeading2=function(){v(this)},B.prototype.toggleHeading3=function(){y(this)},B.prototype.toggleCodeBlock=function(){h(this)},B.prototype.toggleUnorderedList=function(){x(this)},B.prototype.toggleOrderedList=function(){b(this)},B.prototype.cleanBlock=function(){w(this)},B.prototype.drawLink=function(){k(this)},B.prototype.drawImage=function(){S(this)},B.prototype.drawTable=function(){C(this)},B.prototype.drawHorizontalRule=function(){L(this)},B.prototype.undo=function(){T(this)},B.prototype.redo=function(){M(this)},B.prototype.togglePreview=function(){N(this)},B.prototype.toggleSideBySide=function(){A(this)},B.prototype.toggleFullScreen=function(){s(this)},B.prototype.isPreviewActive=function(){var e=this.codemirror,t=e.getWrapperElement(),n=t.lastChild;return/editor-preview-active/.test(n.className)},B.prototype.isSideBySideActive=function(){var e=this.codemirror,t=e.getWrapperElement(),n=t.nextSibling;return/editor-preview-active-side/.test(n.className)},B.prototype.isFullscreenActive=function(){var e=this.codemirror;return e.getOption("fullScreen")},B.prototype.getState=function(){var e=this.codemirror;return l(e)},B.prototype.toTextArea=function(){var e=this.codemirror,t=e.getWrapperElement();t.parentNode&&(this.gui.toolbar&&t.parentNode.removeChild(this.gui.toolbar),this.gui.statusbar&&t.parentNode.removeChild(this.gui.statusbar),this.gui.sideBySide&&t.parentNode.removeChild(this.gui.sideBySide)),e.toTextArea(),this.autosaveTimeoutId&&(clearTimeout(this.autosaveTimeoutId),this.autosaveTimeoutId=void 0,this.clearAutosavedValue())},t.exports=B},{"./codemirror/tablist":19,codemirror:10,"codemirror-spell-checker":4,"codemirror/addon/display/fullscreen.js":5,"codemirror/addon/display/placeholder.js":6,"codemirror/addon/edit/continuelist.js":7,"codemirror/addon/mode/overlay.js":8,"codemirror/addon/selection/mark-selection.js":9,"codemirror/mode/gfm/gfm.js":11,"codemirror/mode/markdown/markdown.js":12,"codemirror/mode/xml/xml.js":14,marked:17}]},{},[20])(20)});
