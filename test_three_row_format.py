#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的三行显示格式
验证AI更新界面是否按照三行格式正确显示
"""

import asyncio
import aiohttp
import json
import os

# 设置测试模式环境变量
os.environ["PMO_TEST_MODE"] = "true"

BASE_URL = "http://localhost:8000"
TEST_PROJECT_CODE = "C202500012"

async def test_three_row_format():
    """测试三行显示格式"""
    async with aiohttp.ClientSession() as session:
        print("🎯 测试新的三行显示格式")
        print("=" * 60)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test_token"
        }
        
        # 触发AI更新分析
        print("📤 触发AI更新分析...")
        async with session.post(f"{BASE_URL}/api/v1/project/{TEST_PROJECT_CODE}/ai-update", 
                               headers=headers) as response:
            if response.status == 200:
                result = await response.json()
                print("✅ AI更新分析成功")
                
                if result.get("code") == 200:
                    data = result.get("data", {})
                    steps = data.get("steps", {})
                    
                    # 检查步骤6的数据
                    step6 = steps.get("step6_user_confirm", {})
                    if step6.get("status") == "ready":
                        print("\n🎨 新的三行显示格式验证:")
                        
                        # 解析AI建议的字段
                        ai_response = step6.get("data", {}).get("ai_response_markdown", "")
                        ai_fields = parse_ai_response(ai_response)
                        
                        print(f"✅ 1. 三行格式设计:")
                        print(f"   📋 第一行：原记录（按项目管理页面表格格式显示）")
                        print(f"   ✏️ 第二行：建议修改行（用户可编辑，使用项目管理表格选项）")
                        print(f"   💡 第三行：修改原因（AI分析依据）")
                        
                        print(f"\n✅ 2. 字段显示示例:")
                        for i, field in enumerate(ai_fields[:5], 1):  # 显示前5个字段作为示例
                            field_key = field['field_key']
                            current_value = field['current_value']
                            suggested_value = field['suggested_value']
                            reason = field['reason']
                            
                            print(f"\n   {i}. {get_field_label(field_key)} ({field_key}):")
                            print(f"      📋 原记录：{format_display_value(current_value, field_key)}")
                            print(f"      ✏️ 修改为：{format_display_value(suggested_value, field_key)} ({get_field_type(field_key)})")
                            print(f"      💡 原因：{reason[:80]}...")
                        
                        print(f"\n✅ 3. 编辑控件类型分布:")
                        field_types = analyze_field_types(ai_fields)
                        for field_type, fields in field_types.items():
                            print(f"   - {field_type}: {len(fields)}个字段")
                            if len(fields) <= 3:
                                print(f"     {', '.join(fields)}")
                        
                        print(f"\n✅ 4. 用户交互功能:")
                        print(f"   ✅ 字段选择：复选框选择要更新的字段")
                        print(f"   ✅ 值编辑：根据字段类型提供合适的编辑控件")
                        print(f"   ✅ 快速操作：重置为AI建议值、清空按钮")
                        print(f"   ✅ 视觉区分：不同行使用不同背景色")
                        
                        print(f"\n🎯 界面优势:")
                        print(f"   ✅ 信息层次清晰：三行结构一目了然")
                        print(f"   ✅ 对比直观：原值和建议值并排显示")
                        print(f"   ✅ 编辑便捷：直接在建议行进行修改")
                        print(f"   ✅ 原因可见：AI决策依据透明展示")
                        print(f"   ✅ 空间高效：紧凑布局节约屏幕空间")
                        
                        return True
                    else:
                        print("❌ 步骤6未准备就绪")
                        return False
                else:
                    print(f"❌ AI更新失败: {result.get('message')}")
                    return False
            else:
                print(f"❌ 请求失败: HTTP {response.status}")
                return False

def parse_ai_response(ai_response):
    """解析AI回答中的字段建议"""
    fields = []
    lines = ai_response.split('\n')
    
    for line in lines:
        if line.startswith('|') and line.endswith('|'):
            cells = line.split('|')[1:-1]  # 去掉首尾空元素
            cells = [cell.strip() for cell in cells]
            
            if len(cells) >= 4 and cells[0] and cells[1] and cells[2]:
                # 跳过表头和分隔行
                if not cells[0].startswith('-') and '字段名称' not in cells[0]:
                    fields.append({
                        'field_key': cells[0],
                        'current_value': cells[1],
                        'suggested_value': cells[2],
                        'reason': cells[3] if len(cells) > 3 else ''
                    })
    
    return fields

def get_field_label(field_key):
    """获取字段标签"""
    field_labels = {
        'project_name': '项目名称',
        'project_establishment_name': '项目立项名称',
        'current_progress': '当前进度',
        'responsible_person': '责任人',
        'responsible_department': '负责部门',
        'construction_content': '建设内容',
        'project_overview': '项目概述',
        'start_time': '开始时间',
        'acceptance_time': '验收时间',
        'project_establishment_time': '项目立项时间',
        'project_implementation_time': '项目实施时间',
        'annual_investment_plan': '年度投资计划',
        'budget': '预算',
        'project_planned_total_investment': '项目计划总投资',
        'investment_type': '投资类型',
        'is_hardware': '是否硬件项目',
        'next_steps': '下一步工作',
        'itbp_team_member': 'ITBP团队成员',
        'remarks': '备注'
    }
    return field_labels.get(field_key, field_key)

def get_field_type(field_key):
    """获取字段类型"""
    date_fields = ['start_time', 'acceptance_time', 'project_establishment_time', 'project_implementation_time']
    select_fields = ['current_progress', 'investment_type', 'is_hardware', 'responsible_department']
    number_fields = ['annual_investment_plan', 'budget', 'project_planned_total_investment']
    textarea_fields = ['construction_content', 'project_overview', 'next_steps', 'remarks']
    
    if field_key in date_fields:
        return '日期选择器'
    elif field_key in select_fields:
        return '下拉选择框'
    elif field_key in number_fields:
        return '数字输入框'
    elif field_key in textarea_fields:
        return '文本域'
    else:
        return '文本输入框'

def format_display_value(value, field_key):
    """格式化显示值"""
    if not value or value == 'null':
        return '-'
    
    # 截断长文本
    if len(str(value)) > 30:
        return str(value)[:30] + '...'
    
    return str(value)

def analyze_field_types(ai_fields):
    """分析字段类型分布"""
    field_types = {
        '日期字段': [],
        '选择字段': [],
        '数字字段': [],
        '文本字段': [],
        '文本域字段': []
    }
    
    for field in ai_fields:
        field_key = field['field_key']
        field_type = get_field_type(field_key)
        
        if '日期' in field_type:
            field_types['日期字段'].append(field_key)
        elif '选择' in field_type:
            field_types['选择字段'].append(field_key)
        elif '数字' in field_type:
            field_types['数字字段'].append(field_key)
        elif '文本域' in field_type:
            field_types['文本域字段'].append(field_key)
        else:
            field_types['文本字段'].append(field_key)
    
    return field_types

async def main():
    """主函数"""
    success = await test_three_row_format()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 新的三行显示格式验证成功！")
        print("\n📋 三行格式总结:")
        print("1. 📋 第一行：原记录 - 灰色背景，显示当前数据库值")
        print("2. ✏️ 第二行：修改为 - 白色背景，可编辑的建议值")
        print("3. 💡 第三行：原因 - 蓝色背景，AI分析依据")
        print("\n🎨 设计优势:")
        print("✅ 信息层次清晰，一目了然")
        print("✅ 编辑操作直观，用户体验佳")
        print("✅ 使用项目管理页面相同的编辑控件")
        print("✅ AI决策过程完全透明")
        print("\n🌐 访问地址: http://localhost:3000")
        print("📝 使用方法: 项目管理 → 点击'AI更新' → 查看步骤6的三行格式界面")
    else:
        print("\n❌ 三行显示格式验证失败")

if __name__ == "__main__":
    asyncio.run(main())
