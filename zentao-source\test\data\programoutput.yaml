title: table zt_programoutput
desc: "过程文档"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: program
    note: "所属项目"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: process
    note: "过程ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: activity
    note: "活动ID"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: output
    note: "文档ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: content
    note: ""
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: name
    note: ""
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: reason
    note: ""
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: result
    note: "是否执行"
    range: yes,no
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: linkedBy
    note: "链接者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: "创建者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdDate
    note: "创建日期"
    from: common.date.v1.yaml
    use: dateB
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
