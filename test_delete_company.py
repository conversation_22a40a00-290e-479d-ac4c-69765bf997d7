#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除公司功能
"""

import requests
import json
import logging
import random
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

BASE_URL = 'http://localhost:8000/api/v1'

def test_delete_company():
    """测试删除公司功能"""
    try:
        # 1. 先创建一个测试公司
        logging.info("步骤1: 创建测试公司")
        random_suffix = random.randint(1000, 9999)
        new_company = {
            "company_code": f"DELETE_TEST_{random_suffix}",
            "company_name": f"待删除测试公司_{random_suffix}",
            "display_order": 999
        }
        
        response = requests.post(f'{BASE_URL}/supervision/companies', json=new_company)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                company_id = data.get('data', {}).get('id')
                logging.info(f"✅ 创建测试公司成功，ID: {company_id}")
                
                # 2. 删除这个公司
                logging.info("步骤2: 删除测试公司")
                delete_response = requests.delete(f'{BASE_URL}/supervision/companies/{company_id}')
                if delete_response.status_code == 200:
                    delete_data = delete_response.json()
                    if delete_data.get('success'):
                        logging.info("✅ 删除公司成功")
                        
                        # 3. 验证公司已被删除
                        logging.info("步骤3: 验证公司已被删除")
                        companies_response = requests.get(f'{BASE_URL}/new-supervision/companies')
                        if companies_response.status_code == 200:
                            companies_data = companies_response.json()
                            if companies_data.get('success'):
                                companies = companies_data.get('data', [])
                                deleted_company = next((c for c in companies if c['id'] == company_id), None)
                                if deleted_company is None:
                                    logging.info("✅ 验证成功：公司已从数据库中删除")
                                    return True
                                else:
                                    logging.error("❌ 验证失败：公司仍然存在于数据库中")
                                    return False
                    else:
                        logging.error(f"❌ 删除公司失败: {delete_data.get('message')}")
                        return False
                else:
                    logging.error(f"❌ 删除公司失败，状态码: {delete_response.status_code}")
                    logging.error(f"   响应内容: {delete_response.text}")
                    return False
            else:
                logging.error(f"❌ 创建测试公司失败: {data.get('message')}")
                return False
        else:
            logging.error(f"❌ 创建测试公司失败，状态码: {response.status_code}")
            logging.error(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        logging.error(f"❌ 测试删除公司功能异常: {e}")
        return False

def test_delete_nonexistent_company():
    """测试删除不存在的公司"""
    try:
        logging.info("步骤4: 测试删除不存在的公司")
        fake_id = 99999
        response = requests.delete(f'{BASE_URL}/supervision/companies/{fake_id}')
        if response.status_code == 404:
            logging.info("✅ 正确处理了删除不存在公司的情况")
            return True
        else:
            logging.error(f"❌ 删除不存在公司的处理不正确，状态码: {response.status_code}")
            return False
    except Exception as e:
        logging.error(f"❌ 测试删除不存在公司异常: {e}")
        return False

def main():
    """主测试函数"""
    logging.info("🚀 开始测试删除公司功能")
    logging.info("=" * 60)
    
    # 测试删除功能
    success1 = test_delete_company()
    success2 = test_delete_nonexistent_company()
    
    logging.info("=" * 60)
    if success1 and success2:
        logging.info("🎉 所有删除功能测试通过")
    else:
        logging.error("❌ 部分删除功能测试失败")
    
    logging.info("💡 现在可以在前端测试删除功能了")
    logging.info("   1. 打开 http://localhost:3000/#/new-supervision")
    logging.info("   2. 点击'管理公司列'按钮")
    logging.info("   3. 点击某个公司的删除按钮")
    logging.info("   4. 确认删除操作")
    logging.info("   5. 验证公司已从列表中消失")

if __name__ == "__main__":
    main()
