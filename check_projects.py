#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看数据库中的项目列表
"""

import asyncio
import aiohttp
import json
import os

# 设置测试模式环境变量
os.environ["PMO_TEST_MODE"] = "true"

BASE_URL = "http://localhost:8000"

async def check_projects():
    """查看项目列表"""
    async with aiohttp.ClientSession() as session:
        print("🔍 查看数据库中的项目列表")
        print("=" * 50)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test_token"
        }
        
        async with session.get(f"{BASE_URL}/api/v1/project/list", headers=headers) as response:
            if response.status == 200:
                result = await response.json()
                print("✅ 获取项目列表成功")
                
                if result.get("code") == 200:
                    projects = result.get("data", [])
                    print(f"\n📋 找到 {len(projects)} 个项目:")
                    
                    for i, project in enumerate(projects[:10]):  # 只显示前10个
                        project_code = project.get("project_code", "无编号")
                        project_name = project.get("project_name", "无名称")
                        print(f"  {i+1}. {project_code} - {project_name}")
                    
                    if len(projects) > 10:
                        print(f"  ... 还有 {len(projects) - 10} 个项目")
                    
                    # 返回第一个项目的编号用于测试
                    if projects:
                        first_project = projects[0]
                        return first_project.get("project_code")
                else:
                    print(f"❌ API返回错误: {result.get('message')}")
            else:
                print(f"❌ HTTP错误: {response.status}")
                error_text = await response.text()
                print(f"错误信息: {error_text}")
        
        return None

async def main():
    """主函数"""
    project_code = await check_projects()
    if project_code:
        print(f"\n💡 可以使用项目编号 '{project_code}' 进行测试")
    else:
        print("\n❌ 没有找到可用的项目")

if __name__ == "__main__":
    asyncio.run(main())
