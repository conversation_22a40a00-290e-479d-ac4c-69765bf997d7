<?php
/**
 * The testsuite module en file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     testsuite
 * @version     $Id: zh-cn.php 4490 2013-02-27 03:27:05Z <EMAIL> $
 * @link        https://www.zentao.net
 */
$lang->testsuite->create           = "Create Suite";
$lang->testsuite->delete           = "Delete Suite";
$lang->testsuite->view             = "Suite Detail";
$lang->testsuite->edit             = "Edit Suite";
$lang->testsuite->browse           = "Suite List";
$lang->testsuite->linkCase         = "Link Case";
$lang->testsuite->linkVersion      = "Version";
$lang->testsuite->unlinkCase       = "Unlink";
$lang->testsuite->unlinkCaseAction = "Unlink Case";
$lang->testsuite->batchUnlinkCases = "Batch Unlink Cases";
$lang->testsuite->deleted          = 'Deleted';
$lang->testsuite->successSaved     = 'Saved';

$lang->testsuite->id             = 'ID';
$lang->testsuite->pri            = 'Priority';
$lang->testsuite->common         = 'Test Suite';
$lang->testsuite->project        = $lang->projectCommon;
$lang->testsuite->product        = $lang->productCommon;
$lang->testsuite->name           = 'Suite Name';
$lang->testsuite->type           = 'Type';
$lang->testsuite->desc           = 'Description';
$lang->testsuite->mailto         = 'Mailto';
$lang->testsuite->author         = 'Access Control';
$lang->testsuite->addedBy        = 'CreatedBy';
$lang->testsuite->addedDate      = 'CreatedDate';
$lang->testsuite->addedTime      = 'Create Time';
$lang->testsuite->lastEditedBy   = 'LastEditedBy';
$lang->testsuite->lastEditedDate = 'LastEditedDate';

$lang->testsuite->legendDesc      = 'Description';
$lang->testsuite->legendBasicInfo = 'Basic Info';

$lang->testsuite->unlinkedCases = 'Unlinked Cases';

$lang->testsuite->confirmDelete     = 'Do you want to delete this test suite?';
$lang->testsuite->confirmUnlinkCase = 'Do you want to unlink this Case?';
$lang->testsuite->noticeNone        = 'You have not created any suite yet.';
$lang->testsuite->noModule          = '<div>You have no modules.</div><div>Manage it now.</div>';
$lang->testsuite->noTestsuite       = 'No suites yet.';
$lang->testsuite->summary           = "Total suites: <strong>%d</strong>, public: <strong>%d</strong>, private: <strong>%d</strong>.";

$lang->testsuite->lblCases      = 'Cases';
$lang->testsuite->lblUnlinkCase = 'Unlink Case';

$lang->testsuite->authorList['private'] = 'Private';
$lang->testsuite->authorList['public']  = 'Public';

$lang->testsuite->featureBar['browse']['all'] = 'All';
