title: table zt_file
desc: "文件上传记录"
author: automated export
version: "1.0"
fields:
  - field: id
    range: "1-10000"
    note: "ID"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: pathname
    note: "路径名称"
    range: ""
  - field: title
    note: "文件标题"
    range: 1-1000000
    prefix: "文件标题"
    postfix: ""
    format: ""
  - field: extension
    note: "文件后缀"
    range: "txt,doc,docx,dot,wps,wri,pdf,ppt,pptx,xls,xlsx,ett,xlt,xlsm,csv,jpg,jpeg,png,psd,gif,ico,bmp,swf,avi,rmvb,rm,mp3,mp4,3gp,flv,mov,movie,rar,zip,bz,bz2,tar,gz,mpp,rp,pdm,vsdx,vsd,sql"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: size
    note: "文件大小"
    range: "1000-40000:R"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: objectType
    note: "对象类型"
    range: task,bug,story,testcase,traincourse,traincontents
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: objectID
    note: "对象ID"
    range: "1-10000"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: addedBy
    range: admin
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: addedDate
    note: "添加时间"
    range: "(M)-(w)"
    type: timestamp
    postfix: ""
    format: "YY/MM/DD"
  - field: downloads
    note: "下载次数"
    range: "0-100"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: extra
    note: "备注"
    range: "0"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: "0"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
