#mainContent .table-form>tbody>tr>th {width: 105px;}
#mainContent {padding-left: 0px;}
#mainContent .importBox .radio-inline+.radio-inline {padding-left: 20px;}
#mainContent .objectBox {padding-top: 2px;}
#mainContent .objectBox .checkbox-primary {display: inline-block;}
#mainContent .objectBox .checkbox-primary:not(:first-child) {margin-left: 10px;}
#mainContent .objectBox .checkbox-primary>label:not(:first-child) {padding-left: 20px;}
#mainContent .objectBox .checkbox-primary>label:before {top: 2px; left: 0px;}
#mainContent .objectBox .checkbox-primary>label:after {width: 14px; height: 14px;}
[lang^='en'] .columnWidth {width: 120px;}
[lang^='zh-cn'] .columnWidth {width: 80px;}

#copyKanbanModal {padding: 0;}
#copyKanbanModal {padding: 0 2px;}
#copyKanbanModal > div {position: relative;}
#copyKanbanModal a {display: block; min-height: 30px; line-height: 30px; padding: 5px 15px; border: 1px solid #e5e5e5; color: #333; margin: 5px 0; border-radius: 3px;}
#copyKanbanModal a > i {display: inline-block; margin-right: 5px;}
#copyKanbanModal a:hover {border: 1px solid #00a9fc; background-color: #E9F2FB; text-decoration: none;}
#copyKanbanModal a.active {border-color: #00da88; color: #00da88; background-color: #E5FFE6;}
#copyKanbanModal a.active:after {position: absolute; content: '\e92f'; font-family: ZentaoIcon; font-size: 20px; right: 25px;}
#copyKanbanModal a.cancel {color: #ff5d5d;}
#copyKanbanModal .copyContentBox {display: inline-block;}
#copyKanbanModal .copyContentBox > .checkbox-primary {display: inline-block; margin-left: 10px; margin-top: -1px;}
#copyKanbanModal .copyContentBox > .checkbox-primary:first-child {margin-left: 20px;}
#copyContentbasicInfo {cursor: not-allowed;}
#team ~ #contactListMenu_chosen {vertical-align: top;}
#whitelist ~ #contactListMenu_chosen {vertical-align: bottom;}
#team ~ #contactListMenu_chosen > .chosen-drop {top: 32px;}
#whitelist ~ #contactListMenu_chosen > .chosen-drop {bottom: 32px;}
#colWidth + .fixedTip, #maxColWidth + .autoTip {margin-left: 35px; color: #2667E3; padding-top: 1px;}
[lang^=en] #colWidth + .fixedTip, [lang^=en] #maxColWidth + .autoTip, [lang^=de] #colWidth + .fixedTip, [lang^=de] #maxColWidth + .autoTip, [lang^=fr] #colWidth + .fixedTip, [lang^=fr] #maxColWidth + .autoTip {margin-left: 50px;}
