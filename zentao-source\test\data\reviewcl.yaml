title: table zt_flow_reviewcl
desc: "评审检查清单"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: title
    note: "检查清单标题"
    range: 1-10000
    prefix: "检查清单标题"
    postfix: ""
    loop: 0
    format: ""
  - field: object
    note: "检查清单标题"
    range: PP,QAP,CMP,ITP,URS,SRS,HLDS,DDS,Code,ITTC,STP,STTC,UM
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: category
    note: "分类"
    range: project,engineering,support
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: parent
    note: "父流程ID"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedTo
    note: "指派给"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: order
    note: "排序"
    range: 10-100:R
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: "状态"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: subStatus
    note: "子状态"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: "由谁创建"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdDate
    note: "创建日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedBy
    note: "由谁编辑"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedDate
    note: "编辑日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedBy
    note: "由谁指派"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedDate
    note: "指派日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
