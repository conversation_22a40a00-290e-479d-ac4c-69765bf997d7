TRUNCATE TABLE zt_pivot;
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1000, 2, '86', '{"zh-cn":"\\u5b8c\\u6210\\u9879\\u76ee\\u5de5\\u671f\\u900f\\u89c6\\u8868","zh-tw":"","en":"","de":"","fr":""}', '', 'select
t1.name,
t2.program1,
t1.begin,
t1.end,
t1.realBegan,
t1.realEnd,
t1.closedDate,
t1.realduration,
t1.realduration-t1.planduration duration_deviation,
round((t1.realduration-t1.planduration)/t1.planduration,3) rate
from
(select 
name,
substr(`path`,2,4) program1,
begin,
end,
realBegan,
realEnd,
left(closedDate,10) closedDate,
datediff(`end`,`begin`) planduration,
ifnull(if(left(`realEnd`,4) != \'0000\',datediff(`realEnd`,`realBegan`),datediff(`closedDate`,`realBegan`)),0) realduration
from
zt_project
where type=\'project\' and status=\'closed\' and deleted=\'0\') t1
left join
(select
id programid,
name program1
from
zt_project
where type=\'program\' and grade=1) t2
on t1.program1=t2.programid', '{"name":{"name":"\\u9879\\u76ee\\u540d\\u79f0","object":"project","field":"name","type":"string"},"program1":{"name":"program1","object":"project","field":"program1","type":"string"},"begin":{"name":"\\u8ba1\\u5212\\u5f00\\u59cb\\u65e5\\u671f","object":"project","field":"begin","type":"date"},"end":{"name":"\\u8ba1\\u5212\\u5b8c\\u6210\\u65e5\\u671f","object":"project","field":"end","type":"date"},"realBegan":{"name":"\\u5b9e\\u9645\\u5f00\\u59cb\\u65e5\\u671f","object":"project","field":"realBegan","type":"date"},"realEnd":{"name":"\\u5b9e\\u9645\\u5b8c\\u6210\\u65e5\\u671f","object":"project","field":"realEnd","type":"date"},"closedDate":{"name":"\\u5173\\u95ed\\u65e5\\u671f","object":"project","field":"closedDate","type":"date"},"realduration":{"name":"realduration","object":"project","field":"realduration","type":"number"},"duration_deviation":{"name":"duration_deviation","object":"project","field":"duration_deviation","type":"number"},"rate":{"name":"rate","object":"project","field":"rate","type":"number"}}', '{"name":{"zh-cn":"\\u9879\\u76ee\\u540d\\u79f0","zh-tw":"","en":"","de":"","fr":""},"program1":{"zh-cn":"\\u4e00\\u7ea7\\u9879\\u76ee\\u96c6","zh-tw":"","en":"","de":"","fr":""},"begin":{"zh-cn":"\\u8ba1\\u5212\\u5f00\\u59cb\\u65e5\\u671f","zh-tw":"","en":"","de":"","fr":""},"end":{"zh-cn":"\\u8ba1\\u5212\\u5b8c\\u6210\\u65e5\\u671f","zh-tw":"","en":"","de":"","fr":""},"realBegan":{"zh-cn":"\\u5b9e\\u9645\\u5f00\\u59cb\\u65e5\\u671f","zh-tw":"","en":"","de":"","fr":""},"realEnd":{"zh-cn":"\\u5b9e\\u9645\\u5b8c\\u6210\\u65e5\\u671f","zh-tw":"","en":"","de":"","fr":""},"closedDate":{"zh-cn":"\\u5173\\u95ed\\u65e5\\u671f","zh-tw":"","en":"","de":"","fr":""},"realduration":{"zh-cn":"\\u5b9e\\u9645\\u5de5\\u671f","zh-tw":"","en":"","de":"","fr":""},"duration_deviation":{"zh-cn":"\\u5de5\\u671f\\u504f\\u5dee","zh-tw":"","en":"","de":"","fr":""},"rate":{"zh-cn":"\\u5de5\\u671f\\u504f\\u5dee\\u7387","zh-tw":"","en":"","de":"","fr":""}}', '', '', '{"columns":[{"field":"begin","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"end","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"realBegan","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"realEnd","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"closedDate","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"realduration","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"duration_deviation","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"rate","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"}],"group3":"program1","group6":"name","lastStep":"4"}', '[]', 0, 'published', '1', 'admin', '2023-04-06 14:07:26', 'admin', '2023-04-06 14:07:26', '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1001, 2, '85', '{"zh-cn":"\\u5b8c\\u6210\\u9879\\u76ee\\u5de5\\u65f6\\u900f\\u89c6\\u8868","zh-tw":"","en":"","de":"","fr":""}', '', 'select
t1.name "项目名称",
t4.program1 "一级项目集",
round(t2.estimate,2) "预计工时",
round(t2.consumed,2) "消耗工时",
round(t2.consumed-t2.estimate,2) "工时偏差",
round((t2.consumed-t2.estimate)/t2.estimate,2) "工时偏差率",
ifnull(t3.storys,0) "完成需求数",
ifnull(t3.storyestimate,0) "完成需求规模数",
round(ifnull(t3.storyestimate,0)/ifnull(t2.consumed,0),2) "单位时间交付需求规模数",
t1.closedDate closedDate
from(
select
id,
name,
substr(`path`,2,4) program1,
closedDate
from
zt_project
where deleted=\'0\' and type=\'project\' and status=\'closed\') t1
left join(
select
project,
sum(estimate) estimate,
sum(consumed) consumed
from
zt_task
where deleted=\'0\' and project !=0
group by project) t2
on t1.id=t2.project
left join (
select
tt3.project,
count(tt3.id) storys,
sum(estimate) storyestimate
from(
select
tt1.id,
tt1.estimate,
tt2.project
from
zt_story tt1
left join
zt_projectstory tt2
on tt1.id=tt2.story
where tt1.deleted=\'0\' and tt1.status=\'closed\' and tt1.closedReason=\'done\') tt3
group by tt3.project) t3
on t1.id=t3.project
left join
(select
id programid,
name program1
from
zt_project
where type=\'program\' and grade=1) t4
on t1.program1=t4.programid', '{"\\u9879\\u76ee\\u540d\\u79f0":{"name":"\\u9879\\u76ee\\u540d\\u79f0","object":"project","field":"\\u9879\\u76ee\\u540d\\u79f0","type":"string"},"\\u4e00\\u7ea7\\u9879\\u76ee\\u96c6":{"name":"\\u4e00\\u7ea7\\u9879\\u76ee\\u96c6","object":"project","field":"\\u4e00\\u7ea7\\u9879\\u76ee\\u96c6","type":"string"},"\\u9884\\u8ba1\\u5de5\\u65f6":{"name":"\\u9884\\u8ba1\\u5de5\\u65f6","object":"project","field":"\\u9884\\u8ba1\\u5de5\\u65f6","type":"number"},"\\u6d88\\u8017\\u5de5\\u65f6":{"name":"\\u6d88\\u8017\\u5de5\\u65f6","object":"project","field":"\\u6d88\\u8017\\u5de5\\u65f6","type":"number"},"\\u5de5\\u65f6\\u504f\\u5dee":{"name":"\\u5de5\\u65f6\\u504f\\u5dee","object":"project","field":"\\u5de5\\u65f6\\u504f\\u5dee","type":"number"},"\\u5de5\\u65f6\\u504f\\u5dee\\u7387":{"name":"\\u5de5\\u65f6\\u504f\\u5dee\\u7387","object":"project","field":"\\u5de5\\u65f6\\u504f\\u5dee\\u7387","type":"number"},"\\u5b8c\\u6210\\u9700\\u6c42\\u6570":{"name":"\\u5b8c\\u6210\\u9700\\u6c42\\u6570","object":"project","field":"\\u5b8c\\u6210\\u9700\\u6c42\\u6570","type":"string"},"\\u5b8c\\u6210\\u9700\\u6c42\\u89c4\\u6a21\\u6570":{"name":"\\u5b8c\\u6210\\u9700\\u6c42\\u89c4\\u6a21\\u6570","object":"project","field":"\\u5b8c\\u6210\\u9700\\u6c42\\u89c4\\u6a21\\u6570","type":"number"},"\\u5355\\u4f4d\\u65f6\\u95f4\\u4ea4\\u4ed8\\u9700\\u6c42\\u89c4\\u6a21\\u6570":{"name":"\\u5355\\u4f4d\\u65f6\\u95f4\\u4ea4\\u4ed8\\u9700\\u6c42\\u89c4\\u6a21\\u6570","object":"project","field":"\\u5355\\u4f4d\\u65f6\\u95f4\\u4ea4\\u4ed8\\u9700\\u6c42\\u89c4\\u6a21\\u6570","type":"number"},"closedDate":{"name":"\\u5173\\u95ed\\u65e5\\u671f","object":"project","field":"closedDate","type":"date"}}', '', '', '', '{"columns":[{"field":"\\u9884\\u8ba1\\u5de5\\u65f6","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"\\u6d88\\u8017\\u5de5\\u65f6","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"\\u5de5\\u65f6\\u504f\\u5dee","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"\\u5de5\\u65f6\\u504f\\u5dee\\u7387","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"\\u5b8c\\u6210\\u9700\\u6c42\\u6570","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"\\u5b8c\\u6210\\u9700\\u6c42\\u89c4\\u6a21\\u6570","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"\\u5355\\u4f4d\\u65f6\\u95f4\\u4ea4\\u4ed8\\u9700\\u6c42\\u89c4\\u6a21\\u6570","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"}],"columnTotal":"noShow","group1":"\\u4e00\\u7ea7\\u9879\\u76ee\\u96c6","group2":"\\u9879\\u76ee\\u540d\\u79f0","lastStep":"4"}', '[{"field":"closedDate","type":"date","name":"\\u5173\\u95ed\\u65e5\\u671f","default":{"begin":"","end":""}}]', 0, 'published', '1', 'admin', '2023-04-11 13:14:37', 'admin', '2023-04-11 13:14:37', '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1002, 3, '100', '{"zh-cn":"\\u4ea7\\u54c1\\u7f3a\\u9677\\u6570\\u636e\\u6c47\\u603b\\u8868","zh-tw":"","en":"","de":"","fr":""}', '', 'SELECT 
  t1.name AS  "产品", 
  IFNULL(t2.name, \'/\') AS "一级项目集", 
  IFNULL(t3.name, \'/\') AS "产品线", 
  IFNULL(t6.exfixedstorys, 0) AS "研发完成需求数", 
  round(IFNULL(t6.exfixedstorysmate, 0),3) AS "研发完成需求规模数",
  IFNULL(t8.storycases, 0) AS "需求用例数",
  round(storycases/exfixedstorysmate,3) AS "用例密度",
  round(IFNULL(t10.casestorys/t6.exfixedstorys,0),3) AS "用例覆盖率",
  IFNULL(t7.bug, 0) AS "Bug数",
  IFNULL(t7.effbugs, 0) AS "有效Bug数",
  IFNULL(t7.pri12bugs, 0) AS "优先级为1，2的Bug数",
  round(bug/exfixedstorysmate,3) AS "Bug密度",
  IFNULL(t7.fixedbugs, 0) AS "修复Bug数",
  round(t7.fixedbugs/bug,3) "Bug修复率"
FROM 
  zt_product AS t1 
  LEFT JOIN zt_project AS t2 ON t1.program = t2.id AND t2.type = \'program\' AND t2.grade = 1 
  LEFT JOIN zt_module AS t3 ON t1.line = t3.id AND t3.type = \'line\' 
  LEFT JOIN (
  SELECT
  product, 
  count(id) exfixedstorys, 
  sum(estimate) exfixedstorysmate
  FROM zt_story 
  WHERE deleted = \'0\'  and (stage in (\'developed\',\'testing\',\'verfied\',\'released\') or (status=\'closed\' and closedReason=\'done\'))
  GROUP BY product) AS t6 ON t1.id = t6.product 
  LEFT JOIN (SELECT product, COUNT(id)  AS bug,
  SUM(case when  resolution in (\'fixed\',\'postponed\') or status=\'active\' then 1 else 0 end) effbugs, 
  SUM(CASE WHEN  resolution=\'fixed\' then 1 else 0 end) fixedbugs,
  SUM(CASE WHEN severity in (1,2) then 1 else 0 end) pri12bugs
  FROM zt_bug WHERE deleted = \'0\' GROUP BY product) AS t7 ON t1.id = t7.product 
  LEFT JOIN (SELECT product, COUNT(id) AS storycases FROM zt_case WHERE deleted=\'0\' GROUP BY product) AS t8 ON t1.id=t8.product
  LEFT JOIN (
   select 
   t9.product, 
   count(t9.story) casestorys
   from(
     SELECT 
     zt_case.product,zt_case.story 
     FROM zt_case 
     left join zt_story
     on zt_case.story=zt_story.id
     WHERE zt_case.deleted=\'0\' and zt_case.story !=\'0\' and zt_story.deleted=\'0\' and  (zt_story.stage in (\'developed\',\'testing\',\'verfied\',\'released\') or (zt_story.status=\'closed\' and zt_story.closedReason=\'done\'))
     GROUP BY product, story) t9
     group by product) t10
     on t1.id=t10.product
WHERE t1.deleted = \'0\' AND t1.status != \'closed\' AND t1.shadow = \'0\'AND t1.vision = \'rnd\'
ORDER BY t1.order', '{"\\u4ea7\\u54c1":{"name":"\\u4ea7\\u54c1","object":"story","field":"\\u4ea7\\u54c1","type":"string"},"\\u4e00\\u7ea7\\u9879\\u76ee\\u96c6":{"name":"\\u4e00\\u7ea7\\u9879\\u76ee\\u96c6","object":"story","field":"\\u4e00\\u7ea7\\u9879\\u76ee\\u96c6","type":"string"},"\\u4ea7\\u54c1\\u7ebf":{"name":"\\u4ea7\\u54c1\\u7ebf","object":"story","field":"\\u4ea7\\u54c1\\u7ebf","type":"string"},"\\u7814\\u53d1\\u5b8c\\u6210\\u9700\\u6c42\\u6570":{"name":"\\u7814\\u53d1\\u5b8c\\u6210\\u9700\\u6c42\\u6570","object":"story","field":"\\u7814\\u53d1\\u5b8c\\u6210\\u9700\\u6c42\\u6570","type":"string"},"\\u7814\\u53d1\\u5b8c\\u6210\\u9700\\u6c42\\u89c4\\u6a21\\u6570":{"name":"\\u7814\\u53d1\\u5b8c\\u6210\\u9700\\u6c42\\u89c4\\u6a21\\u6570","object":"story","field":"\\u7814\\u53d1\\u5b8c\\u6210\\u9700\\u6c42\\u89c4\\u6a21\\u6570","type":"number"},"\\u9700\\u6c42\\u7528\\u4f8b\\u6570":{"name":"\\u9700\\u6c42\\u7528\\u4f8b\\u6570","object":"story","field":"\\u9700\\u6c42\\u7528\\u4f8b\\u6570","type":"string"},"\\u7528\\u4f8b\\u5bc6\\u5ea6":{"name":"\\u7528\\u4f8b\\u5bc6\\u5ea6","object":"story","field":"\\u7528\\u4f8b\\u5bc6\\u5ea6","type":"number"},"\\u7528\\u4f8b\\u8986\\u76d6\\u7387":{"name":"\\u7528\\u4f8b\\u8986\\u76d6\\u7387","object":"story","field":"\\u7528\\u4f8b\\u8986\\u76d6\\u7387","type":"number"},"Bug\\u6570":{"name":"Bug\\u6570","object":"story","field":"Bug\\u6570","type":"string"},"\\u6709\\u6548Bug\\u6570":{"name":"\\u6709\\u6548Bug\\u6570","object":"story","field":"\\u6709\\u6548Bug\\u6570","type":"number"},"\\u4f18\\u5148\\u7ea7\\u4e3a1\\uff0c2\\u7684Bug\\u6570":{"name":"\\u4f18\\u5148\\u7ea7\\u4e3a1\\uff0c2\\u7684Bug\\u6570","object":"story","field":"\\u4f18\\u5148\\u7ea7\\u4e3a1\\uff0c2\\u7684Bug\\u6570","type":"number"},"Bug\\u5bc6\\u5ea6":{"name":"Bug\\u5bc6\\u5ea6","object":"story","field":"Bug\\u5bc6\\u5ea6","type":"number"},"\\u4fee\\u590dBug\\u6570":{"name":"\\u4fee\\u590dBug\\u6570","object":"story","field":"\\u4fee\\u590dBug\\u6570","type":"number"},"Bug\\u4fee\\u590d\\u7387":{"name":"Bug\\u4fee\\u590d\\u7387","object":"story","field":"Bug\\u4fee\\u590d\\u7387","type":"number"}}', '', '', '', '{"columns":[{"field":"\\u7814\\u53d1\\u5b8c\\u6210\\u9700\\u6c42\\u6570","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"\\u7814\\u53d1\\u5b8c\\u6210\\u9700\\u6c42\\u89c4\\u6a21\\u6570","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"\\u9700\\u6c42\\u7528\\u4f8b\\u6570","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"\\u7528\\u4f8b\\u5bc6\\u5ea6","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"\\u7528\\u4f8b\\u8986\\u76d6\\u7387","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"Bug\\u6570","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"\\u6709\\u6548Bug\\u6570","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"\\u4f18\\u5148\\u7ea7\\u4e3a1\\uff0c2\\u7684Bug\\u6570","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"Bug\\u5bc6\\u5ea6","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"\\u4fee\\u590dBug\\u6570","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"},{"field":"Bug\\u4fee\\u590d\\u7387","stat":"sum","slice":"noSlice","showMode":"default","monopolize":"0","showTotal":"noShow"}],"columnTotal":"noShow","group1":"\\u4e00\\u7ea7\\u9879\\u76ee\\u96c6","group3":"\\u4ea7\\u54c1\\u7ebf","group2":"\\u4ea7\\u54c1","lastStep":"4"}', '[]', 0, 'published', '1', 'admin', '2023-04-06 13:16:52', 'admin', '2023-04-06 13:16:52', '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1003, 1, '59', '{"zh-cn":"\\u4ea7\\u54c1\\u5b8c\\u6210\\u5ea6\\u7edf\\u8ba1\\u8868","zh-tw":"\\u7522\\u54c1\\u5b8c\\u6210\\u5ea6\\u7d71\\u8a08\\u8868","en":"Product Progress","de":"Product Progress","fr":"Product Progress"}', '{"zh-cn":"\\u6309\\u7167\\u4ea7\\u54c1\\u5217\\u51fa\\u9700\\u6c42\\u603b\\u6570\\uff0c\\u5b8c\\u6210\\u7684\\u603b\\u6570(\\u72b6\\u6001\\u662f\\u5173\\u95ed\\uff0c\\u6216\\u8005\\u7814\\u53d1\\u9636\\u6bb5\\u662f\\u53d1\\u5e03)\\uff0c\\u5b8c\\u6210\\u7684\\u767e\\u5206\\u6bd4\\u3002","zh-tw":"\\u6309\\u7167\\u7522\\u54c1\\u5217\\u51fa\\u9700\\u6c42\\u7e3d\\u6578\\uff0c\\u5b8c\\u6210\\u7684\\u7e3d\\u6578(\\u72c0\\u614b\\u662f\\u95dc\\u9589\\uff0c\\u6216\\u8005\\u7814\\u767c\\u968e\\u6bb5\\u662f\\u767c\\u5e03)\\uff0c\\u5b8c\\u6210\\u7684\\u767e\\u5206\\u6bd4\\u3002","en":"Number of total stories,done stories(state is closed, or stage is released), percent of completion.","de":"Number of total stories,done stories(state is closed, or stage is released), percent of completion.","fr":"Number of total stories,done stories(state is closed, or stage is released), percent of completion."}', 'select t1.*,t2.name, (case when t1.status = \'closed\' or t1.stage = \'released\' then 1 else 0 end) as done, 1 as count from zt_story as t1 
left join zt_product as t2 on t1.product=t2.id 
left join zt_project as t3 on t2.program=t3.id 
where t1.deleted=\'0\' and t2.deleted=\'0\'
order by t3.`order` asc, t2.line desc, t2.`order` asc', '{"id":{"object":"story","field":"id","type":"string"},"vision":{"object":"project","field":"vision","type":"string"},"parent":{"object":"story","field":"parent","type":"string"},"product":{"object":"story","field":"product","type":"string"},"branch":{"object":"story","field":"branch","type":"string"},"module":{"object":"story","field":"module","type":"string"},"plan":{"object":"story","field":"plan","type":"string"},"source":{"object":"story","field":"source","type":"string"},"sourceNote":{"object":"story","field":"sourceNote","type":"string"},"fromBug":{"object":"story","field":"fromBug","type":"string"},"feedback":{"object":"story","field":"feedback","type":"string"},"title":{"object":"story","field":"title","type":"string"},"keywords":{"object":"story","field":"keywords","type":"string"},"type":{"object":"story","field":"type","type":"string"},"category":{"object":"story","field":"category","type":"string"},"pri":{"object":"story","field":"pri","type":"string"},"estimate":{"object":"story","field":"estimate","type":"string"},"status":{"object":"story","field":"status","type":"string"},"subStatus":{"object":"story","field":"subStatus","type":"string"},"color":{"object":"story","field":"color","type":"string"},"stage":{"object":"story","field":"stage","type":"string"},"stagedBy":{"object":"story","field":"stagedBy","type":"string"},"mailto":{"object":"story","field":"mailto","type":"string"},"lib":{"object":"project","field":"lib","type":"string"},"fromStory":{"object":"project","field":"fromStory","type":"string"},"fromVersion":{"object":"project","field":"fromVersion","type":"string"},"openedBy":{"object":"story","field":"openedBy","type":"string"},"openedDate":{"object":"story","field":"openedDate","type":"string"},"assignedTo":{"object":"story","field":"assignedTo","type":"string"},"assignedDate":{"object":"story","field":"assignedDate","type":"string"},"approvedDate":{"object":"project","field":"approvedDate","type":"string"},"lastEditedBy":{"object":"story","field":"lastEditedBy","type":"string"},"lastEditedDate":{"object":"story","field":"lastEditedDate","type":"string"},"changedBy":{"object":"story","field":"changedBy","type":"string"},"changedDate":{"object":"story","field":"changedDate","type":"string"},"reviewedBy":{"object":"story","field":"reviewedBy","type":"string"},"reviewedDate":{"object":"story","field":"reviewedDate","type":"string"},"closedBy":{"object":"story","field":"closedBy","type":"string"},"closedDate":{"object":"story","field":"closedDate","type":"string"},"closedReason":{"object":"story","field":"closedReason","type":"string"},"activatedDate":{"object":"story","field":"activatedDate","type":"string"},"toBug":{"object":"story","field":"toBug","type":"string"},"childStories":{"object":"story","field":"childStories","type":"string"},"linkStories":{"object":"story","field":"linkStories","type":"string"},"linkRequirements":{"object":"story","field":"linkRequirements","type":"string"},"twins":{"object":"story","field":"twins","type":"string"},"duplicateStory":{"object":"story","field":"duplicateStory","type":"string"},"version":{"object":"story","field":"version","type":"string"},"storyChanged":{"object":"project","field":"storyChanged","type":"string"},"feedbackBy":{"object":"story","field":"feedbackBy","type":"string"},"notifyEmail":{"object":"story","field":"notifyEmail","type":"string"},"URChanged":{"object":"story","field":"URChanged","type":"string"},"deleted":{"object":"story","field":"deleted","type":"string"},"name":{"object":"product","field":"name","type":"string"},"done":{"object":"project","field":"done","type":"string"},"count":{"object":"project","field":"count","type":"string"}}', '{"count":{"zh-cn":"\\u9700\\u6c42\\u6570","zh-tw":"\\u9700\\u6c42\\u6570","en":"Stories"},"done":{"zh-cn":"\\u5b8c\\u6210\\u6570","zh-tw":"\\u5b8c\\u6210\\u6570","en":"Done"}}', '', null, '{"group1":"name","group2":"","columnTotal":"sum","columns":[{"field":"count","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"done","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"1"}]}', '', 4, 'published', '0', 'admin', '2015-07-21 15:07:48', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1004, 1, '59', '{"zh-cn":"\\u4ea7\\u54c1\\u9700\\u6c42\\u72b6\\u6001\\u5206\\u5e03\\u8868","zh-tw":"\\u7522\\u54c1\\u9700\\u6c42\\u72c0\\u614b\\u5206\\u5e03\\u8868","en":"Story Status","de":"Story Status","fr":"Story Status"}', '{"zh-cn":"\\u6309\\u7167\\u4ea7\\u54c1\\u5217\\u51fa\\u9700\\u6c42\\u603b\\u6570\\uff0c\\u72b6\\u6001\\u7684\\u5206\\u5e03\\u60c5\\u51b5\\u3002","zh-tw":"\\u6309\\u7167\\u7522\\u54c1\\u5217\\u51fa\\u9700\\u6c42\\u7e3d\\u6578\\uff0c\\u72c0\\u614b\\u7684\\u5206\\u5e03\\u60c5\\u6cc1\\u3002","en":"Total number and status distribution of stories.","de":"Total number and status distribution of stories.","fr":"Total number and status distribution of stories."}', 'select t1.*,t2.name from zt_story as t1
 left join zt_product as t2 on t1.product=t2.id 
left join zt_project as t3 on t2.program=t3.id 
where t1.deleted=\'0\' and t2.deleted=\'0\'
order by t3.`order` asc, t2.line desc, t2.`order` asc', '{"id":{"object":"story","field":"id","type":"string"},"vision":{"object":"project","field":"vision","type":"string"},"parent":{"object":"story","field":"parent","type":"string"},"product":{"object":"story","field":"product","type":"string"},"branch":{"object":"story","field":"branch","type":"string"},"module":{"object":"story","field":"module","type":"string"},"plan":{"object":"story","field":"plan","type":"string"},"source":{"object":"story","field":"source","type":"string"},"sourceNote":{"object":"story","field":"sourceNote","type":"string"},"fromBug":{"object":"story","field":"fromBug","type":"string"},"feedback":{"object":"story","field":"feedback","type":"string"},"title":{"object":"story","field":"title","type":"string"},"keywords":{"object":"story","field":"keywords","type":"string"},"type":{"object":"story","field":"type","type":"string"},"category":{"object":"story","field":"category","type":"string"},"pri":{"object":"story","field":"pri","type":"string"},"estimate":{"object":"story","field":"estimate","type":"string"},"status":{"object":"story","field":"status","type":"string"},"subStatus":{"object":"story","field":"subStatus","type":"string"},"color":{"object":"story","field":"color","type":"string"},"stage":{"object":"story","field":"stage","type":"string"},"stagedBy":{"object":"story","field":"stagedBy","type":"string"},"mailto":{"object":"story","field":"mailto","type":"string"},"lib":{"object":"project","field":"lib","type":"string"},"fromStory":{"object":"project","field":"fromStory","type":"string"},"fromVersion":{"object":"project","field":"fromVersion","type":"string"},"openedBy":{"object":"story","field":"openedBy","type":"string"},"openedDate":{"object":"story","field":"openedDate","type":"string"},"assignedTo":{"object":"story","field":"assignedTo","type":"string"},"assignedDate":{"object":"story","field":"assignedDate","type":"string"},"approvedDate":{"object":"project","field":"approvedDate","type":"string"},"lastEditedBy":{"object":"story","field":"lastEditedBy","type":"string"},"lastEditedDate":{"object":"story","field":"lastEditedDate","type":"string"},"changedBy":{"object":"story","field":"changedBy","type":"string"},"changedDate":{"object":"story","field":"changedDate","type":"string"},"reviewedBy":{"object":"story","field":"reviewedBy","type":"string"},"reviewedDate":{"object":"story","field":"reviewedDate","type":"string"},"closedBy":{"object":"story","field":"closedBy","type":"string"},"closedDate":{"object":"story","field":"closedDate","type":"string"},"closedReason":{"object":"story","field":"closedReason","type":"string"},"activatedDate":{"object":"story","field":"activatedDate","type":"string"},"toBug":{"object":"story","field":"toBug","type":"string"},"childStories":{"object":"story","field":"childStories","type":"string"},"linkStories":{"object":"story","field":"linkStories","type":"string"},"linkRequirements":{"object":"story","field":"linkRequirements","type":"string"},"twins":{"object":"story","field":"twins","type":"string"},"duplicateStory":{"object":"story","field":"duplicateStory","type":"string"},"version":{"object":"story","field":"version","type":"string"},"storyChanged":{"object":"project","field":"storyChanged","type":"string"},"feedbackBy":{"object":"story","field":"feedbackBy","type":"string"},"notifyEmail":{"object":"story","field":"notifyEmail","type":"string"},"URChanged":{"object":"story","field":"URChanged","type":"string"},"deleted":{"object":"story","field":"deleted","type":"string"},"name":{"object":"product","field":"name","type":"string"}}', '', '', null, '{"group1":"name","group2":"","columnTotal":"sum","columns":[{"field":"status","slice":"status","stat":"count","showTotal":"sum","showMode":"default","monopolize":"0"}]}', '', 4, 'published', '0', 'admin', '2015-07-21 15:35:38', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1005, 1, '59', '{"zh-cn":"\\u4ea7\\u54c1\\u9700\\u6c42\\u9636\\u6bb5\\u5206\\u5e03\\u8868","zh-tw":"\\u7522\\u54c1\\u9700\\u6c42\\u968e\\u6bb5\\u5206\\u5e03\\u8868","en":"Story Stage","de":"Story Stage","fr":"Story Stage"}', '{"zh-cn":"\\u6309\\u7167\\u4ea7\\u54c1\\u5217\\u51fa\\u9700\\u6c42\\u603b\\u6570\\uff0c\\u7814\\u53d1\\u9636\\u6bb5\\u7684\\u5206\\u5e03\\u60c5\\u51b5\\u3002","zh-tw":"\\u6309\\u7167\\u7522\\u54c1\\u5217\\u51fa\\u9700\\u6c42\\u7e3d\\u6578\\uff0c\\u7814\\u767c\\u968e\\u6bb5\\u7684\\u5206\\u5e03\\u60c5\\u6cc1\\u3002","en":"Total number and stage distribution of stories ","de":"Total number and stage distribution of stories ","fr":"Total number and stage distribution of stories "}', 'select t1.*,t2.name from zt_story as t1
 left join zt_product as t2 on t1.product=t2.id 
left join zt_project as t3 on t2.program=t3.id 
where t1.deleted=\'0\' and t2.deleted=\'0\'
order by t3.`order` asc, t2.line desc, t2.`order` asc', '{"id":{"object":"story","field":"id","type":"string"},"vision":{"object":"project","field":"vision","type":"string"},"parent":{"object":"story","field":"parent","type":"string"},"product":{"object":"story","field":"product","type":"string"},"branch":{"object":"story","field":"branch","type":"string"},"module":{"object":"story","field":"module","type":"string"},"plan":{"object":"story","field":"plan","type":"string"},"source":{"object":"story","field":"source","type":"string"},"sourceNote":{"object":"story","field":"sourceNote","type":"string"},"fromBug":{"object":"story","field":"fromBug","type":"string"},"feedback":{"object":"story","field":"feedback","type":"string"},"title":{"object":"story","field":"title","type":"string"},"keywords":{"object":"story","field":"keywords","type":"string"},"type":{"object":"story","field":"type","type":"string"},"category":{"object":"story","field":"category","type":"string"},"pri":{"object":"story","field":"pri","type":"string"},"estimate":{"object":"story","field":"estimate","type":"string"},"status":{"object":"story","field":"status","type":"string"},"subStatus":{"object":"story","field":"subStatus","type":"string"},"color":{"object":"story","field":"color","type":"string"},"stage":{"object":"story","field":"stage","type":"string"},"stagedBy":{"object":"story","field":"stagedBy","type":"string"},"mailto":{"object":"story","field":"mailto","type":"string"},"lib":{"object":"project","field":"lib","type":"string"},"fromStory":{"object":"project","field":"fromStory","type":"string"},"fromVersion":{"object":"project","field":"fromVersion","type":"string"},"openedBy":{"object":"story","field":"openedBy","type":"string"},"openedDate":{"object":"story","field":"openedDate","type":"string"},"assignedTo":{"object":"story","field":"assignedTo","type":"string"},"assignedDate":{"object":"story","field":"assignedDate","type":"string"},"approvedDate":{"object":"project","field":"approvedDate","type":"string"},"lastEditedBy":{"object":"story","field":"lastEditedBy","type":"string"},"lastEditedDate":{"object":"story","field":"lastEditedDate","type":"string"},"changedBy":{"object":"story","field":"changedBy","type":"string"},"changedDate":{"object":"story","field":"changedDate","type":"string"},"reviewedBy":{"object":"story","field":"reviewedBy","type":"string"},"reviewedDate":{"object":"story","field":"reviewedDate","type":"string"},"closedBy":{"object":"story","field":"closedBy","type":"string"},"closedDate":{"object":"story","field":"closedDate","type":"string"},"closedReason":{"object":"story","field":"closedReason","type":"string"},"activatedDate":{"object":"story","field":"activatedDate","type":"string"},"toBug":{"object":"story","field":"toBug","type":"string"},"childStories":{"object":"story","field":"childStories","type":"string"},"linkStories":{"object":"story","field":"linkStories","type":"string"},"linkRequirements":{"object":"story","field":"linkRequirements","type":"string"},"twins":{"object":"story","field":"twins","type":"string"},"duplicateStory":{"object":"story","field":"duplicateStory","type":"string"},"version":{"object":"story","field":"version","type":"string"},"storyChanged":{"object":"project","field":"storyChanged","type":"string"},"feedbackBy":{"object":"story","field":"feedbackBy","type":"string"},"notifyEmail":{"object":"story","field":"notifyEmail","type":"string"},"URChanged":{"object":"story","field":"URChanged","type":"string"},"deleted":{"object":"story","field":"deleted","type":"string"},"name":{"object":"product","field":"name","type":"string"}}', '', '', null, '{"group1":"name","group2":"","columnTotal":"sum","columns":[{"field":"stage","slice":"stage","stat":"count","showTotal":"sum","showMode":"default","monopolize":"0"}]}', '', 4, 'published', '0', 'admin', '2015-07-21 15:38:34', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1006, 1, '59', '{"zh-cn":"\\u4ea7\\u54c1\\u53d1\\u5e03\\u6570\\u91cf\\u7edf\\u8ba1\\u8868","zh-tw":"\\u7522\\u54c1\\u767c\\u5e03\\u6578\\u91cf\\u7d71\\u8a08\\u8868","en":"Product Release","de":"Product Release","fr":"Product Release"}', '{"zh-cn":"\\u6309\\u7167\\u4ea7\\u54c1\\u5217\\u51fa\\u53d1\\u5e03\\u7684\\u6570\\u91cf\\u3002","zh-tw":"\\u6309\\u7167\\u7522\\u54c1\\u5217\\u51fa\\u767c\\u5e03\\u7684\\u6578\\u91cf\\u3002","en":"Product Release.","de":"Product Release.","fr":"Product Release."}', 'select t2.name, 1 as releases from zt_release as t1 
left join zt_product as t2 on t1.product=t2.id 
left join zt_project as t3 on t2.program=t3.id 
where t1.deleted=\'0\' and t2.deleted=\'0\'
order by t3.`order` asc, t2.line desc, t2.`order` asc', '{"name":{"object":"release","field":"name","type":"string"},"releases":{"object":"product","field":"releases","type":"string"}}', '{"count":{"zh-cn":"\\u9700\\u6c42\\u6570","zh-tw":"\\u9700\\u6c42\\u6570","en":"Stories"},"done":{"zh-cn":"\\u5b8c\\u6210\\u6570","zh-tw":"\\u5b8c\\u6210\\u6570","en":"Done"}}', '', null, '{"group1":"name","group2":"","columnTotal":"sum","columns":[{"field":"releases","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"}]}', '', 4, 'published', '0', 'admin', '2015-07-21 16:00:52', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1007, 1, '60', '{"zh-cn":"\\u4efb\\u52a1\\u72b6\\u6001\\u7edf\\u8ba1\\u8868","zh-tw":"\\u4efb\\u52d9\\u72c0\\u614b\\u7d71\\u8a08\\u8868","en":"Task Status Report","de":"Task Status Report","fr":"Task Status Report","vi":"Task Status Report","ja":"Task Status Report"}', '{"zh-cn":"\\u6309\\u7167\\u6267\\u884c\\u7edf\\u8ba1\\u4efb\\u52a1\\u7684\\u72b6\\u6001\\u5206\\u5e03\\u60c5\\u51b5\\u3002","zh-tw":"\\u6309\\u7167\\u57f7\\u884c\\u7d71\\u8a08\\u4efb\\u52d9\\u7684\\u72c0\\u614b\\u5206\\u5e03\\u60c5\\u6cc1\\u3002","en":"","de":"","fr":"","vi":"","ja":""}', 'select t1.id,t3.name as project,t1.name,t2.status,IF(t3.multiple="1",t1.name,"") as execution,t2.id as taskID,  t1.status as projectstatus, (case when t2.deadline < CURDATE() and t2.deadline != \'0000-00-00\' and t2.status != \'closed\' and t2.status != \'done\' and t2.status != \'cancel\' then 1 else 0 end) as timeout from zt_project as t1
 left join zt_task as t2 on t1.id=t2.execution
 left join zt_project as t3 on t3.id=t1.project
 where t1.deleted=\'0\' and t1.type in (\'sprint\',\'stage\') and t2.deleted=\'0\' and if($project=\'\',1,t3.id=$project) and if($status=\'\',1,t1.status=$status) and if($beginDate=\'\',1,t1.begin>=$beginDate) and if($endDate=\'\',1,t1.end<=$endDate)', '{"id":{"object":"project","field":"id","type":"string"},"project":{"object":"project","field":"project","type":"string"},"name":{"object":"project","field":"name","type":"string"},"status":{"object":"project","field":"status","type":"string"},"execution":{"object":"project","field":"execution","type":"string"},"taskID":{"object":"task","field":"taskID","type":"string"},"projectstatus":{"object":"task","field":"projectstatus","type":"string"},"timeout":{"object":"task","field":"timeout","type":"string"}}', '{"project":{"zh-cn":"\\u9879\\u76ee\\u540d\\u79f0"},"execution":{"zh-cn":"\\u6267\\u884c\\u540d\\u79f0"}}', '{"varName":["project","status","beginDate","endDate"],"showName":["\\u9879\\u76ee\\u5217\\u8868","\\u6267\\u884c\\u72b6\\u6001","\\u6267\\u884c\\u8d77\\u59cb\\u65e5\\u671f","\\u6267\\u884c\\u7ed3\\u675f\\u65e5\\u671f"],"requestType":["select","select","date","date"],"selectList":["project","project.status","user","user"],"default":["","","$MONTHBEGIN","$MONTHEND"]}', null, '{"group1":"project","group2":"execution","columnTotal":"sum","columns":[{"field":"status","slice":"status","stat":"count","showTotal":"sum","showMode":"default","monopolize":"0"}]}', '[{"from":"query","field":"project","name":"\\u9879\\u76ee\\u5217\\u8868","type":"select","typeOption":"project","default":""},{"from":"query","field":"status","name":"\\u6267\\u884c\\u72b6\\u6001","type":"select","typeOption":"project.status","default":""},{"from":"query","field":"beginDate","name":"\\u6267\\u884c\\u8d77\\u59cb\\u65e5\\u671f","type":"date","typeOption":"","default":"$MONTHBEGIN"},{"from":"query","field":"endDate","name":"\\u6267\\u884c\\u7ed3\\u675f\\u65e5\\u671f","type":"date","typeOption":"","default":"$MONTHEND"}]', 4, 'published', '0', 'admin', '2015-07-22 11:28:33', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1008, 1, '60', '{"zh-cn":"\\u4efb\\u52a1\\u7c7b\\u578b\\u7edf\\u8ba1\\u8868","zh-tw":"\\u4efb\\u52d9\\u985e\\u578b\\u7d71\\u8a08\\u8868","en":"Task Type Report","de":"Task Type Report","fr":"Task Type Report","vi":"Task Type Report","ja":"Task Type Report"}', '{"zh-cn":"\\u6309\\u7167\\u9879\\u76ee\\u7edf\\u8ba1\\u4efb\\u52a1\\u7684\\u7c7b\\u578b\\u5206\\u5e03\\u60c5\\u51b5\\u3002","zh-tw":"\\u6309\\u7167\\u9805\\u76ee\\u7d71\\u8a08\\u4efb\\u52d9\\u7684\\u985e\\u578b\\u5206\\u5e03\\u60c5\\u6cc1\\u3002","en":"","de":"","fr":"","vi":"","ja":""}', 'select t1.id,t3.name as project,IF(t3.multiple="1",t1.name,"") as execution,t2.type,t2.id as taskID, t1.status as projectstatus from zt_project as t1 
left join zt_task as t2 on t1.id=t2.execution
left join zt_project as t3 on t3.id=t1.project
where t1.deleted=\'0\' and t1.type in (\'sprint\',\'stage\') and t2.deleted=\'0\' and if($project=\'\',1,t3.id=$project) and if($status=\'\',1,t1.status=$status) and if($beginDate=\'\',1,t1.begin>=$beginDate) and if($endDate=\'\',1,t1.end<=$endDate)', '{"id":{"object":"project","field":"id","type":"string"},"project":{"object":"project","field":"project","type":"string"},"execution":{"object":"project","field":"execution","type":"string"},"type":{"object":"task","field":"type","type":"option"},"taskID":{"object":"task","field":"taskID","type":"string"},"projectstatus":{"object":"task","field":"projectstatus","type":"string"}}', '{"project":{"zh-cn":"\\u9879\\u76ee\\u540d\\u79f0","zh-tw":"","en":"","de":"","fr":""},"execution":{"zh-cn":"\\u6267\\u884c\\u540d\\u79f0","zh-tw":"","en":"","de":"","fr":""},"id":{"zh-cn":"\\u9879\\u76eeID","zh-tw":"","en":"","de":"","fr":""},"type":{"zh-cn":"\\u4efb\\u52a1\\u7c7b\\u578b","zh-tw":"","en":"","de":"","fr":""},"taskID":{"zh-cn":"taskID","zh-tw":"","en":"","de":"","fr":""},"projectstatus":{"zh-cn":"projectstatus","zh-tw":"","en":"","de":"","fr":""}}', '{"varName":["project","status","beginDate","endDate"],"showName":["\\u9879\\u76ee\\u5217\\u8868","\\u6267\\u884c\\u72b6\\u6001","\\u6267\\u884c\\u8d77\\u59cb\\u65e5\\u671f","\\u6267\\u884c\\u7ed3\\u675f\\u65e5\\u671f"],"requestType":["select","select","date","date"],"selectList":["project","project.status","user","user"],"default":["","","$MONTHBEGIN","$MONTHEND"]}', null, '{"group1":"project","group2":"execution","columnTotal":"sum","columns":[{"field":"type","slice":"type","stat":"count","showTotal":"sum","showMode":"default","monopolize":"0"}]}', '[{"from":"query","field":"project","name":"\\u9879\\u76ee\\u5217\\u8868","type":"select","typeOption":"project","default":""},{"from":"query","field":"status","name":"\\u6267\\u884c\\u72b6\\u6001","type":"select","typeOption":"project.status","default":""},{"from":"query","field":"beginDate","name":"\\u6267\\u884c\\u8d77\\u59cb\\u65e5\\u671f","type":"date","typeOption":"","default":"$MONTHBEGIN"},{"from":"query","field":"endDate","name":"\\u6267\\u884c\\u7ed3\\u675f\\u65e5\\u671f","type":"date","typeOption":"","default":"$MONTHEND"}]', 4, 'published', '0', 'admin', '2015-07-22 13:06:46', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1009, 1, '60', '{"zh-cn":"\\u9879\\u76ee\\u4efb\\u52a1\\u6307\\u6d3e\\u7edf\\u8ba1\\u8868","zh-tw":"\\u9805\\u76ee\\u4efb\\u52d9\\u6307\\u6d3e\\u7d71\\u8a08\\u8868","en":"Task Assign Report","de":"Task Assign Report","fr":"Task Assign Report","vi":"Task Assign Report","ja":"Task Assign Report"}', '{"zh-cn":"\\u6309\\u7167\\u9879\\u76ee\\u7edf\\u8ba1\\u4efb\\u52a1\\u7684\\u6307\\u6d3e\\u7ed9\\u5206\\u5e03\\u60c5\\u51b5\\u3002","zh-tw":"\\u6309\\u7167\\u9805\\u76ee\\u7d71\\u8a08\\u4efb\\u52d9\\u7684\\u6307\\u6d3e\\u7d66\\u5206\\u5e03\\u60c5\\u6cc1\\u3002","en":"","de":"","fr":"","vi":"","ja":""}', 'select t1.id,t4.name as project,IF(t4.multiple="1",t1.name,"") as execution,if(t3.account is not null, t3.account,t2.assignedTo) as assignedTo,t2.id as taskID, t1.status as projectstatus from zt_project as t1
 left join zt_task as t2 on t1.id=t2.execution
 left join zt_team as t3 on t3.type=\'task\' && t3.root=t2.id 
left join zt_project as t4 on t1.project=t4.id
where t1.deleted=\'0\' and t1.type in (\'sprint\',\'stage\') and t2.deleted=\'0\' and if($project=\'\',1,t4.id=$project) and if($status=\'\',1,t1.status=$status) and if($beginDate=\'\',1,t1.begin>=$beginDate) and if($endDate=\'\',1,t1.end<=$endDate)', '{"id":{"object":"project","field":"id","type":"string"},"project":{"object":"project","field":"project","type":"string"},"execution":{"object":"project","field":"execution","type":"string"},"assignedTo":{"object":"task","field":"assignedTo","type":"string"},"taskID":{"object":"team","field":"taskID","type":"string"},"projectstatus":{"object":"team","field":"projectstatus","type":"string"}}', '{"assignedTo":{"zh-cn":"\\u6307\\u6d3e\\u7ed9"},"execution":{"zh-cn":"\\u6267\\u884c\\u540d\\u79f0"}}', '{"varName":["project","status","beginDate","endDate"],"showName":["\\u9879\\u76ee\\u5217\\u8868","\\u6267\\u884c\\u72b6\\u6001","\\u6267\\u884c\\u8d77\\u59cb\\u65e5\\u671f","\\u6267\\u884c\\u7ed3\\u675f\\u65e5\\u671f"],"requestType":["select","select","date","date"],"selectList":["project","project.status","user","user"],"default":["","","$MONTHBEGIN","$MONTHEND"]}', null, '{"group1":"project","group2":"execution","columnTotal":"sum","columns":[{"field":"assignedTo","slice":"assignedTo","stat":"count","showTotal":"noShow","showMode":"default","monopolize":"0"}]}', '[{"from":"query","field":"project","name":"\\u9879\\u76ee\\u5217\\u8868","type":"select","typeOption":"project","default":""},{"from":"query","field":"status","name":"\\u6267\\u884c\\u72b6\\u6001","type":"select","typeOption":"project.status","default":""},{"from":"query","field":"beginDate","name":"\\u6267\\u884c\\u8d77\\u59cb\\u65e5\\u671f","type":"date","typeOption":"","default":"$MONTHBEGIN"},{"from":"query","field":"endDate","name":"\\u6267\\u884c\\u7ed3\\u675f\\u65e5\\u671f","type":"date","typeOption":"","default":"$MONTHEND"}]', 4, 'published', '0', 'admin', '2015-07-22 13:13:28', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1010, 1, '60', '{"zh-cn":"\\u9879\\u76ee\\u4efb\\u52a1\\u5b8c\\u6210\\u8005\\u7edf\\u8ba1\\u8868","zh-tw":"\\u9805\\u76ee\\u4efb\\u52d9\\u5b8c\\u6210\\u8005\\u7d71\\u8a08\\u8868","en":"Task Finish Report","de":"Task Finish Report","fr":"Task Finish Report","vi":"Task Finish Report","ja":"Task Finish Report"}', '{"zh-cn":"\\u6309\\u7167\\u9879\\u76ee\\u7edf\\u8ba1\\u4efb\\u52a1\\u7684\\u5b8c\\u6210\\u8005\\u5206\\u5e03\\u60c5\\u51b5\\u3002","zh-tw":"\\u6309\\u7167\\u9805\\u76ee\\u7d71\\u8a08\\u4efb\\u52d9\\u7684\\u5b8c\\u6210\\u8005\\u5206\\u5e03\\u60c5\\u6cc1\\u3002","en":"","de":"","fr":"","vi":"","ja":""}', 'select t1.id,t3.name as project,IF(t3.multiple="1",t1.name,"") as execution,t2.finishedBy,t2.id as taskID, t1.status as projectstatus from zt_project as t1 
left join zt_task as t2 on t1.id=t2.execution
left join zt_project as t3 on t1.project=t3.id 
where t1.deleted=\'0\' and t1.type in (\'sprint\',\'stage\') and t2.deleted=\'0\' and t2.finishedBy!=\'\' and if($project=\'\',1,t3.id=$project) and if($status=\'\',1,t1.status=$status) and if($beginDate=\'\',1,t1.begin>=$beginDate) and if($endDate=\'\',1,t1.end<=$endDate)', '{"id":{"object":"project","field":"id","type":"string"},"project":{"object":"project","field":"project","type":"string"},"execution":{"object":"project","field":"execution","type":"string"},"finishedBy":{"object":"task","field":"finishedBy","type":"string"},"taskID":{"object":"task","field":"taskID","type":"string"},"projectstatus":{"object":"task","field":"projectstatus","type":"string"}}', '{"project":{"zh-cn":"\\u9879\\u76ee\\u540d\\u79f0"},"execution":{"zh-cn":"\\u6267\\u884c\\u540d\\u79f0"}}', '{"varName":["project","status","beginDate","endDate"],"showName":["\\u9879\\u76ee\\u5217\\u8868","\\u6267\\u884c\\u72b6\\u6001","\\u6267\\u884c\\u8d77\\u59cb\\u65e5\\u671f","\\u6267\\u884c\\u7ed3\\u675f\\u65e5\\u671f"],"requestType":["select","select","date","date"],"selectList":["project","project.status","user","user"],"default":["","","$MONTHBEGIN","$MONTHEND"]}', null, '{"group1":"project","group2":"execution","columnTotal":"sum","columns":[{"field":"finishedBy","slice":"finishedBy","stat":"count","showTotal":"sum","showMode":"default","monopolize":"0"}]}', '[{"from":"query","field":"project","name":"\\u9879\\u76ee\\u5217\\u8868","type":"select","typeOption":"project","default":""},{"from":"query","field":"status","name":"\\u6267\\u884c\\u72b6\\u6001","type":"select","typeOption":"project.status","default":""},{"from":"query","field":"beginDate","name":"\\u6267\\u884c\\u8d77\\u59cb\\u65e5\\u671f","type":"date","typeOption":"","default":"$MONTHBEGIN"},{"from":"query","field":"endDate","name":"\\u6267\\u884c\\u7ed3\\u675f\\u65e5\\u671f","type":"date","typeOption":"","default":"$MONTHEND"}]', 4, 'published', '0', 'admin', '2015-07-22 13:16:21', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1011, 1, '60', '{"zh-cn":"\\u9879\\u76ee\\u6295\\u5165\\u7edf\\u8ba1\\u8868","zh-tw":"\\u9805\\u76ee\\u6295\\u5165\\u7d71\\u8a08\\u8868","en":"Project Invest Report","de":"Project Invest Report","fr":"Project Invest Report","vi":"Project Invest Report","ja":"Project Invest Report"}', '{"zh-cn":"\\u6309\\u7167\\u9879\\u76ee\\u5217\\u51fa\\uff1a\\u4efb\\u52a1\\u6570\\uff0c\\u9700\\u6c42\\u6570\\uff0c\\u4eba\\u6570\\uff0c\\u603b\\u6d88\\u8017\\u5de5\\u65f6\\u3002","zh-tw":"\\u6309\\u7167\\u9805\\u76ee\\u5217\\u51fa\\uff1a\\u4efb\\u52d9\\u6578\\uff0c\\u9700\\u6c42\\u6578\\uff0c\\u4eba\\u6578\\uff0c\\u7e3d\\u6d88\\u8017\\u5de5\\u6642\\u3002","en":"","de":"","fr":"","vi":"","ja":""}', 'select t1.id,t5.name as project,IF(t5.multiple="1",t1.name,"") as execution,CONCAT(t1.begin,\' ~ \',t1.end) as timeLimit,t2.teams,t3.stories,round(t4.consumed,1) as consumed,t4.number, t1.status as projectstatus 
from zt_project as t1
 left join ztv_projectteams as t2 on t1.id=t2.execution
left join ztv_projectstories as t3 on t1.id=t3.execution
 left join ztv_executionsummary as t4 on t1.id=t4.execution 
left join zt_project as t5 on t1.project=t5.id 
where t1.deleted=\'0\' and t1.type in (\'sprint\',\'stage\') and if($project=\'\',1,t5.id=$project) and if($status=\'\',1,t1.status=$status) and if($beginDate=\'\',1,t1.begin>=$beginDate) and if($endDate=\'\',1,t1.end<=$endDate)', '{"id":{"object":"project","field":"id","type":"string"},"project":{"object":"project","field":"project","type":"string"},"execution":{"object":"project","field":"execution","type":"string"},"timeLimit":{"object":"project","field":"timeLimit","type":"string"},"teams":{"object":"project","field":"teams","type":"string"},"stories":{"object":"project","field":"stories","type":"string"},"consumed":{"object":"project","field":"consumed","type":"string"},"number":{"object":"project","field":"number","type":"string"},"projectstatus":{"object":"project","field":"projectstatus","type":"string"}}', '{"timeLimit":{"zh-cn":"\\u5de5\\u671f"},"teams":{"zh-cn":"\\u4eba\\u6570"},"stories":{"zh-cn":"\\u9700\\u6c42\\u6570"},"consumed":{"zh-cn":"\\u603b\\u6d88\\u8017"},"number":{"zh-cn":"\\u4efb\\u52a1\\u6570"},"project":{"zh-cn":"\\u9879\\u76ee\\u540d\\u79f0"},"execution":{"zh-cn":"\\u6267\\u884c\\u540d\\u79f0"}}', '{"varName":["project","status","beginDate","endDate"],"showName":["\\u9879\\u76ee\\u5217\\u8868","\\u6267\\u884c\\u72b6\\u6001","\\u6267\\u884c\\u8d77\\u59cb\\u65e5\\u671f","\\u6267\\u884c\\u7ed3\\u675f\\u65e5\\u671f"],"requestType":["select","select","date","date"],"selectList":["project","project.status","user","user"],"default":["","","$MONTHBEGIN","$MONTHEND"]}', null, '{"group1":"project","group2":"execution","columnTotal":"sum","columns":[{"field":"number","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"stories","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"teams","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"consumed","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"}]}', '[{"from":"query","field":"project","name":"\\u9879\\u76ee\\u5217\\u8868","type":"select","typeOption":"project","default":""},{"from":"query","field":"status","name":"\\u6267\\u884c\\u72b6\\u6001","type":"select","typeOption":"project.status","default":""},{"from":"query","field":"beginDate","name":"\\u6267\\u884c\\u8d77\\u59cb\\u65e5\\u671f","type":"date","typeOption":"","default":"$MONTHBEGIN"},{"from":"query","field":"endDate","name":"\\u6267\\u884c\\u7ed3\\u675f\\u65e5\\u671f","type":"date","typeOption":"","default":"$MONTHEND"}]', 4, 'published', '0', 'admin', '2015-07-22 16:37:38', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1012, 1, '60', '{"zh-cn":"\\u9879\\u76ee\\u9700\\u6c42\\u72b6\\u6001\\u5206\\u5e03\\u8868","zh-tw":"\\u9805\\u76ee\\u9700\\u6c42\\u72c0\\u614b\\u5206\\u5e03\\u8868","en":"Project Story Status","de":"Project Story Status","fr":"Project Story Status","vi":"Project Story Status","ja":"Project Story Status"}', '{"zh-cn":"\\u6309\\u7167\\u9879\\u76ee\\u7edf\\u8ba1\\u9700\\u6c42\\u7684\\u72b6\\u6001\\u5206\\u5e03\\u60c5\\u51b5\\u3002","zh-tw":"\\u6309\\u7167\\u9805\\u76ee\\u7d71\\u8a08\\u9700\\u6c42\\u7684\\u72c0\\u614b\\u5206\\u5e03\\u60c5\\u6cc1\\u3002","en":"","de":"","fr":"","vi":"","ja":""}', 'select t2.id, t4.name as project,IF(t4.multiple="1",t2.name,"") as execution,t3.status from zt_projectstory as t1 
left join zt_project as t2 on t1.project=t2.id 
left join zt_story as t3 on t1.story=t3.id 
left join zt_project as t4 on t4.id=t2.project
where t2.deleted=\'0\' and t2.type in(\'sprint\', \'stage\') and if($project=\'\',1,t4.id=$project) and if($execution=\'\',1,t2.id=$execution) and if($status=\'\',1,t2.status=$status)', '{"id":{"object":"project","field":"id","type":"string"},"project":{"object":"projectstory","field":"project","type":"string"},"execution":{"object":"project","field":"execution","type":"string"},"status":{"object":"story","field":"status","type":"option"}}', '{"project":{"zh-cn":"\\u9879\\u76ee\\u540d\\u79f0"},"execution":{"zh-cn":"\\u6267\\u884c\\u540d\\u79f0"}}', '{"varName":["project","execution","status"],"showName":["\\u9879\\u76ee\\u5217\\u8868","\\u6267\\u884c\\u5217\\u8868","\\u6267\\u884c\\u72b6\\u6001"],"requestType":["select","select","select"],"selectList":["project","execution","project.status"],"default":["","",""]}', null, '{"group1":"project","group2":"execution","columnTotal":"sum","columns":[{"field":"status","slice":"status","stat":"count","showTotal":"sum","showMode":"default","monopolize":"0"}]}', '[{"from":"query","field":"project","name":"\\u9879\\u76ee\\u5217\\u8868","type":"select","typeOption":"project","default":""},{"from":"query","field":"execution","name":"\\u6267\\u884c\\u5217\\u8868","type":"select","typeOption":"execution","default":""},{"from":"query","field":"status","name":"\\u6267\\u884c\\u72b6\\u6001","type":"select","typeOption":"project.status","default":""}]', 4, 'published', '0', 'admin', '2015-07-23 15:35:08', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1013, 1, '60', '{"zh-cn":"\\u9879\\u76ee\\u9700\\u6c42\\u9636\\u6bb5\\u5206\\u5e03\\u8868","zh-tw":"\\u9805\\u76ee\\u9700\\u6c42\\u968e\\u6bb5\\u5206\\u5e03\\u8868","en":"Project Stage Report","de":"Project Stage Report","fr":"Project Stage Report","vi":"Project Stage Report","ja":"Project Stage Report"}', '{"zh-cn":"\\u6309\\u7167\\u9879\\u76ee\\u7edf\\u8ba1\\u9700\\u6c42\\u9636\\u6bb5\\u5206\\u5e03\\u60c5\\u51b5\\u3002","zh-tw":"\\u6309\\u7167\\u9805\\u76ee\\u7d71\\u8a08\\u9700\\u6c42\\u968e\\u6bb5\\u5206\\u5e03\\u60c5\\u6cc1\\u3002","en":"","de":"","fr":"","vi":"","ja":""}', 'select t2.id, t4.name as project,IF(t4.multiple="1",t2.name,"") as execution,t3.stage from zt_projectstory as t1 
left join zt_project as t2 on t1.project=t2.id 
left join zt_story as t3 on t1.story=t3.id 
left join zt_project as t4 on t4.id=t2.project
where t2.deleted=\'0\' and t2.type in(\'sprint\', \'stage\') and if($project=\'\',1,t4.id=$project) and if($execution=\'\',1,t2.id=$execution) and if($status=\'\',1,t2.status=$status)', '{"id":{"object":"project","field":"id","type":"string"},"project":{"object":"projectstory","field":"project","type":"string"},"execution":{"object":"project","field":"execution","type":"string"},"stage":{"object":"story","field":"stage","type":"option"}}', '{"project":{"zh-cn":"\\u9879\\u76ee\\u540d\\u79f0","zh-tw":"","en":"","de":"","fr":""},"execution":{"zh-cn":"\\u6267\\u884c\\u540d\\u79f0","zh-tw":"","en":"","de":"","fr":""},"id":{"zh-cn":"\\u9879\\u76eeID","zh-tw":"","en":"","de":"","fr":""},"stage":{"zh-cn":"\\u9636\\u6bb5","zh-tw":"","en":"","de":"","fr":""}}', '{"varName":["project","execution","status"],"showName":["\\u9879\\u76ee\\u5217\\u8868","\\u6267\\u884c\\u5217\\u8868","\\u6267\\u884c\\u72b6\\u6001"],"requestType":["select","select","select"],"selectList":["project","execution","project.status"],"default":["","",""]}', null, '{"group1":"project","group2":"execution","columnTotal":"sum","columns":[{"field":"stage","slice":"stage","stat":"count","showTotal":"sum","showMode":"default","monopolize":"0"}]}', '[{"from":"query","field":"project","name":"\\u9879\\u76ee\\u5217\\u8868","type":"select","typeOption":"project","default":""},{"from":"query","field":"execution","name":"\\u6267\\u884c\\u5217\\u8868","type":"select","typeOption":"execution","default":""},{"from":"query","field":"status","name":"\\u6267\\u884c\\u72b6\\u6001","type":"select","typeOption":"project.status","default":""}]', 4, 'published', '0', 'admin', '2015-07-23 15:38:18', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1014, 1, '60,61', '{"zh-cn":"\\u9879\\u76eeBug\\u89e3\\u51b3\\u65b9\\u6848\\u5206\\u5e03\\u8868","zh-tw":"\\u9805\\u76eeBug\\u89e3\\u6c7a\\u65b9\\u6848\\u5206\\u5e03\\u8868","en":"Project Bug Resolution","de":"Project Bug Resolution","fr":"Project Bug Resolution","vi":"Project Bug Resolution","ja":"Project Bug Resolution"}', '{"zh-cn":"\\u6309\\u7167\\u9879\\u76ee\\u7edf\\u8ba1Bug\\u7684\\u89e3\\u51b3\\u65b9\\u6848\\u5206\\u5e03\\u60c5\\u51b5\\u3002","zh-tw":"\\u6309\\u7167\\u9805\\u76ee\\u7d71\\u8a08Bug\\u7684\\u89e3\\u6c7a\\u65b9\\u6848\\u5206\\u5e03\\u60c5\\u6cc1\\u3002","en":"","de":"","fr":"","vi":"","ja":""}', 'select t1.id,t3.name as project,t3.id as projectID,IF(t3.multiple="1",t1.name,"") as execution,t1.id as bugID,t2.resolution from zt_project as t1 
left join zt_bug as t2 on t1.id=t2.execution
left join zt_project as t3 on t3.id=t1.project
 where t1.deleted=\'0\' and t2.deleted=\'0\' and t2.resolution!=\'\' having bugID!=\'\' and if($project=\'\',1,t3.id=$project) and if($execution=\'\',1,t1.id=$execution)', '{"id":{"object":"project","field":"id","type":"string"},"project":{"object":"project","field":"project","type":"string"},"t3id":{"object":"project","field":"t3id","type":"string"},"execution":{"object":"project","field":"execution","type":"string"},"bugID":{"object":"bug","field":"bugID","type":"string"},"resolution":{"object":"bug","field":"resolution","type":"string"}}', '{"project":{"zh-cn":"\\u9879\\u76ee\\u540d\\u79f0"},"execution":{"zh-cn":"\\u6267\\u884c\\u540d\\u79f0"}}', '{"varName":["project","execution"],"showName":["\\u9879\\u76ee\\u5217\\u8868","\\u6267\\u884c\\u5217\\u8868"],"requestType":["select","select"],"selectList":["project","execution"],"default":["",""]}', null, '{"group1":"project","group2":"execution","columnTotal":"sum","columns":[{"field":"resolution","slice":"resolution","stat":"count","showTotal":"sum","showMode":"default","monopolize":"0"}]}', '[{"from":"query","field":"project","name":"\\u9879\\u76ee\\u5217\\u8868","type":"select","typeOption":"project","default":""},{"from":"query","field":"execution","name":"\\u6267\\u884c\\u5217\\u8868","type":"select","typeOption":"execution","default":""}]', 4, 'published', '0', 'admin', '2015-07-23 16:04:46', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1015, 1, '60,61', '{"zh-cn":"\\u9879\\u76eeBug\\u72b6\\u6001\\u5206\\u5e03\\u8868","zh-tw":"\\u9805\\u76eeBug\\u72c0\\u614b\\u5206\\u5e03\\u8868","en":"Project Bug Status","de":"Project Bug Status","fr":"Project Bug Status","vi":"Project Bug Status","ja":"Project Bug Status"}', '{"zh-cn":"\\u6309\\u7167\\u9879\\u76ee\\u7edf\\u8ba1Bug\\u7684\\u72b6\\u6001\\u5206\\u5e03\\u60c5\\u51b5\\u3002","zh-tw":"\\u6309\\u7167\\u9805\\u76ee\\u7d71\\u8a08Bug\\u7684\\u72c0\\u614b\\u5206\\u5e03\\u60c5\\u6cc1\\u3002","en":"","de":"","fr":"","vi":"","ja":""}', 'select t1.id,t3.name as project,t3.id as projectID,IF(t3.multiple="1",t1.name,"") as execution,t1.id as bugID,t2.status from zt_project as t1 
left join zt_bug as t2 on t1.id=t2.execution
left join zt_project as t3 on t3.id=t1.project
where t1.deleted=\'0\' and t2.deleted=\'0\' having bugID!=\' \' and if($project=\'\',1,t3.id=$project) and if($execution=\'\',1,t1.id=$execution)', '{"id":{"object":"project","field":"id","type":"string"},"project":{"object":"project","field":"project","type":"string"},"t3id":{"object":"project","field":"t3id","type":"string"},"execution":{"object":"project","field":"execution","type":"string"},"bugID":{"object":"bug","field":"bugID","type":"string"},"status":{"object":"bug","field":"status","type":"option"}}', '{"project":{"zh-cn":"\\u9879\\u76ee\\u540d\\u79f0"},"execution":{"zh-cn":"\\u6267\\u884c\\u540d\\u79f0"}}', '{"varName":["project","execution"],"showName":["\\u9879\\u76ee\\u5217\\u8868","\\u6267\\u884c\\u5217\\u8868"],"requestType":["select","select"],"selectList":["project","execution"],"default":["",""]}', null, '{"group1":"project","group2":"execution","columnTotal":"sum","columns":[{"field":"status","slice":"status","stat":"count","showTotal":"noShow","showMode":"default","monopolize":"0"}]}', '[{"from":"query","field":"project","name":"\\u9879\\u76ee\\u5217\\u8868","type":"select","typeOption":"project","default":""},{"from":"query","field":"execution","name":"\\u6267\\u884c\\u5217\\u8868","type":"select","typeOption":"execution","default":""}]', 4, 'published', '0', 'admin', '2015-07-23 15:48:03', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1016, 1, '60,61', '{"zh-cn":"\\u9879\\u76eeBug\\u521b\\u5efa\\u8005\\u5206\\u5e03\\u8868","zh-tw":"\\u9805\\u76eeBug\\u5275\\u5efa\\u8005\\u5206\\u5e03\\u8868","en":"Project Bug Opened","de":"Project Bug Opened","fr":"Project Bug Opened","vi":"Project Bug Opened","ja":"Project Bug Opened"}', '{"zh-cn":"\\u6309\\u7167\\u9879\\u76ee\\u7edf\\u8ba1Bug\\u7684\\u521b\\u5efa\\u8005\\u5206\\u5e03\\u60c5\\u51b5\\u3002","zh-tw":"\\u6309\\u7167\\u9805\\u76ee\\u7d71\\u8a08Bug\\u7684\\u5275\\u5efa\\u8005\\u5206\\u5e03\\u60c5\\u6cc1\\u3002","en":"","de":"","fr":"","vi":"","ja":""}', 'select t1.id,t3.name as project,t3.id as projectID,IF(t3.multiple="1",t1.name,"") as execution,t1.id as bugID,t2.openedBy from zt_project as t1 
left join zt_bug as t2 on t1.id=t2.execution
left join zt_project as t3 on t3.id=t1.project
where t1.deleted=\'0\' and t2.deleted=\'0\' having bugID!=\'\' and if($project=\'\',1,t3.id=$project) and if($execution=\'\',1,t1.id=$execution)', '{"id":{"object":"project","field":"id","type":"string"},"project":{"object":"project","field":"project","type":"string"},"t3id":{"object":"project","field":"t3id","type":"string"},"execution":{"object":"project","field":"execution","type":"string"},"bugID":{"object":"bug","field":"bugID","type":"string"},"openedBy":{"object":"project","field":"openedBy","type":"string"}}', '{"project":{"zh-cn":"\\u9879\\u76ee\\u540d\\u79f0"},"execution":{"zh-cn":"\\u6267\\u884c\\u540d\\u79f0"}}', '{"varName":["project","execution"],"showName":["\\u9879\\u76ee\\u5217\\u8868","\\u6267\\u884c\\u5217\\u8868"],"requestType":["select","select"],"selectList":["project","execution"],"default":["",""]}', null, '{"group1":"project","group2":"execution","columnTotal":"sum","columns":[{"field":"openedBy","slice":"openedBy","stat":"count","showTotal":"sum","showMode":"default","monopolize":"0"}]}', '[{"from":"query","field":"project","name":"\\u9879\\u76ee\\u5217\\u8868","type":"select","typeOption":"project","default":""},{"from":"query","field":"execution","name":"\\u6267\\u884c\\u5217\\u8868","type":"select","typeOption":"execution","default":""}]', 4, 'published', '0', 'admin', '2015-07-23 16:08:10', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1017, 1, '60,61', '{"zh-cn":"\\u9879\\u76eeBug\\u89e3\\u51b3\\u8005\\u5206\\u5e03\\u8868","zh-tw":"\\u9805\\u76eeBug\\u89e3\\u6c7a\\u8005\\u5206\\u5e03\\u8868","en":"Project Bug Resolve","de":"Project Bug Resolve","fr":"Project Bug Resolve","vi":"Project Bug Resolve","ja":"Project Bug Resolve"}', '{"zh-cn":"\\u6309\\u7167\\u9879\\u76ee\\u7edf\\u8ba1Bug\\u7684\\u89e3\\u51b3\\u8005\\u5206\\u5e03\\u60c5\\u51b5\\u3002","zh-tw":"\\u6309\\u7167\\u9805\\u76ee\\u7d71\\u8a08Bug\\u7684\\u89e3\\u6c7a\\u8005\\u5206\\u5e03\\u60c5\\u6cc1\\u3002","en":"","de":"","fr":"","vi":"","ja":""}', 'select t1.id,t3.name as project,t3.id as projectID,IF(t3.multiple="1",t1.name,"") as execution,t1.id as bugID,t2.resolvedBy from zt_project as t1 
left join zt_bug as t2 on t1.id=t2.execution
left join zt_project as t3 on t3.id=t1.project
where t1.deleted=\'0\' and t2.deleted=\'0\' and t2.status!=\'active\' and t2.resolvedBy!=\'\' having bugID!=\'\' and if($project=\'\',1,t3.id=$project) and if($execution=\'\',1,t1.id=$execution)', '{"id":{"object":"project","field":"id","type":"string"},"project":{"object":"project","field":"project","type":"string"},"t3id":{"object":"project","field":"t3id","type":"string"},"execution":{"object":"project","field":"execution","type":"string"},"bugID":{"object":"bug","field":"bugID","type":"string"},"resolvedBy":{"object":"bug","field":"resolvedBy","type":"string"}}', '{"project":{"zh-cn":"\\u9879\\u76ee\\u540d\\u79f0"},"execution":{"zh-cn":"\\u6267\\u884c\\u540d\\u79f0"}}', '{"varName":["project","execution"],"showName":["\\u9879\\u76ee\\u5217\\u8868","\\u6267\\u884c\\u5217\\u8868"],"requestType":["select","select"],"selectList":["project","execution"],"default":["",""]}', null, '{"group1":"project","group2":"execution","columnTotal":"sum","columns":[{"field":"resolvedBy","slice":"resolvedBy","stat":"count","showTotal":"sum","showMode":"default","monopolize":"0"}]}', '[{"from":"query","field":"project","name":"\\u9879\\u76ee\\u5217\\u8868","type":"select","typeOption":"project","default":""},{"from":"query","field":"execution","name":"\\u6267\\u884c\\u5217\\u8868","type":"select","typeOption":"execution","default":""}]', 4, 'published', '0', 'admin', '2015-07-23 16:13:16', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1018, 1, '60,61', '{"zh-cn":"\\u9879\\u76eeBug\\u6307\\u6d3e\\u7ed9\\u5206\\u5e03\\u8868","zh-tw":"\\u9805\\u76eeBug\\u6307\\u6d3e\\u7d66\\u5206\\u5e03\\u8868","en":"Project Bug Assign","de":"Project Bug Assign","fr":"Project Bug Assign","vi":"Project Bug Assign","ja":"Project Bug Assign"}', '{"zh-cn":"\\u6309\\u7167\\u9879\\u76ee\\u7edf\\u8ba1Bug\\u7684\\u6307\\u6d3e\\u7ed9\\u5206\\u5e03\\u60c5\\u51b5\\u3002","zh-tw":"\\u6309\\u7167\\u9805\\u76ee\\u7d71\\u8a08Bug\\u7684\\u6307\\u6d3e\\u7d66\\u5206\\u5e03\\u60c5\\u6cc1\\u3002","en":"","de":"","fr":"","vi":"","ja":""}', 'select t1.id,t3.name as project,t3.id as projectID,IF(t3.multiple="1",t1.name,"") as execution,t1.id as bugID,t2.assignedTo from zt_project as t1 
left join zt_bug as t2 on t1.id=t2.execution 
left join zt_project as t3 on t3.id=t1.project
where t1.deleted=\'0\' and t2.deleted=\'0\' having bugID!=\'\' and if($project=\'\',1,t3.id=$project) and if($execution=\'\',1,t1.id=$execution)', '{"id":{"object":"project","field":"id","type":"string"},"project":{"object":"project","field":"project","type":"string"},"t3id":{"object":"project","field":"t3id","type":"string"},"execution":{"object":"project","field":"execution","type":"string"},"bugID":{"object":"bug","field":"bugID","type":"string"},"assignedTo":{"object":"bug","field":"assignedTo","type":"string"}}', '{"project":{"zh-cn":"\\u9879\\u76ee\\u540d\\u79f0"},"execution":{"zh-cn":"\\u6267\\u884c\\u540d\\u79f0"}}', '{"varName":["project","execution"],"showName":["\\u9879\\u76ee\\u5217\\u8868","\\u6267\\u884c\\u5217\\u8868"],"requestType":["select","select"],"selectList":["project","execution"],"default":["",""]}', null, '{"group1":"project","group2":"execution","columnTotal":"sum","columns":[{"field":"assignedTo","slice":"assignedTo","stat":"count","showTotal":"sum","showMode":"default","monopolize":"0"}]}', '[{"from":"query","field":"project","name":"\\u9879\\u76ee\\u5217\\u8868","type":"select","typeOption":"project","default":""},{"from":"query","field":"execution","name":"\\u6267\\u884c\\u5217\\u8868","type":"select","typeOption":"execution","default":""}]', 4, 'published', '0', 'admin', '2015-07-23 16:29:10', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1019, 1, '60', '{"zh-cn":"\\u9879\\u76ee\\u8d28\\u91cf\\u8868","zh-tw":"\\u9805\\u76ee\\u8cea\\u91cf\\u8868","en":"Project Quality Report","de":"Project Quality Report","fr":"Project Quality Report","vi":"Project Quality Report","ja":"Project Quality Report"}', '{"zh-cn":"\\u5217\\u51fa\\u9879\\u76ee\\u7684\\u9700\\u6c42\\u603b\\u6570\\uff0c\\u5b8c\\u6210\\u9700\\u6c42\\u6570\\uff0c\\u4efb\\u52a1\\u603b\\u6570\\uff0c\\u5b8c\\u6210\\u7684\\u4efb\\u52a1\\u6570\\uff0cBug\\u6570\\uff0c\\u89e3\\u51b3\\u7684Bug\\u6570\\uff0cBug\\/\\u9700\\u6c42\\uff0cBug\\/\\u4efb\\u52a1\\uff0c\\u91cd\\u8981Bug\\u6570\\u91cf(\\u4e25\\u91cd\\u7a0b\\u5ea6\\u4e0d\\u5927\\u4e8e3\\uff09\\u3002","zh-tw":"\\u5217\\u51fa\\u9805\\u76ee\\u7684\\u9700\\u6c42\\u7e3d\\u6578\\uff0c\\u5b8c\\u6210\\u9700\\u6c42\\u6578\\uff0c\\u4efb\\u52d9\\u7e3d\\u6578\\uff0c\\u5b8c\\u6210\\u7684\\u4efb\\u52d9\\u6578\\uff0cBug\\u6578\\uff0c\\u89e3\\u6c7a\\u7684Bug\\u6578\\uff0cBug\\/\\u9700\\u6c42\\uff0cBug\\/\\u4efb\\u52d9\\uff0c\\u91cd\\u8981Bug\\u6578\\u91cf(\\u56b4\\u91cd\\u7a0b\\u5ea6\\u4e0d\\u5927\\u65bc3\\uff09\\u3002","en":"","de":"","fr":"","vi":"","ja":""}', 'select t1.id, t5.name as project,IF(t5.multiple="1",t1.name,"") as execution,t2.stories,(t2.stories-t2.undone) as doneStory,t3.number,(t3.number-t3.undone) as doneTask,t4.bugs,t4.resolutions, round(t4.bugs/(t2.stories-t2.undone),2) as bugthanstory,round(t4.bugs/(t3.number-t3.undone),2) as bugthantask,t4.seriousBugs from zt_project as t1 
left join ztv_projectstories as t2 on t1.id=t2.execution
left join ztv_executionsummary as t3 on t1.id=t3.execution
left join ztv_projectbugs as t4 on t1.id=t4.execution
left join zt_project as t5 on t5.id=t1.project
where t1.deleted=\'0\' and t1.type in (\'sprint\',\'stage\') and t1.grade=\'1\' and if($project=\'\',1,t5.id=$project) and if($execution=\'\',1,t1.id=$execution)', '{"id":{"object":"project","field":"id","type":"string"},"project":{"object":"project","field":"project","type":"string"},"t5id":{"object":"project","field":"t5id","type":"string"},"execution":{"object":"project","field":"execution","type":"string"},"stories":{"object":"project","field":"stories","type":"string"},"doneStory":{"object":"project","field":"doneStory","type":"string"},"number":{"object":"project","field":"number","type":"string"},"doneTask":{"object":"project","field":"doneTask","type":"string"},"bugs":{"object":"project","field":"bugs","type":"string"},"resolutions":{"object":"project","field":"resolutions","type":"string"},"bugthanstory":{"object":"project","field":"bugthanstory","type":"string"},"bugthantask":{"object":"project","field":"bugthantask","type":"string"},"seriousBugs":{"object":"project","field":"seriousBugs","type":"string"}}', '{"stories":{"zh-cn":"\\u9700\\u6c42\\u603b\\u6570"},"doneStory":{"zh-cn":"\\u5b8c\\u6210\\u9700\\u6c42\\u6570"},"number":{"zh-cn":"\\u4efb\\u52a1\\u603b\\u6570"},"doneTask":{"zh-cn":"\\u5b8c\\u6210\\u4efb\\u52a1\\u6570"},"bugs":{"zh-cn":"Bug\\u6570"},"resolutions":{"zh-cn":"\\u89e3\\u51b3Bug\\u6570"},"bugthanstory":{"zh-cn":"Bug\\/\\u5b8c\\u6210\\u9700\\u6c42"},"bugthantask":{"zh-cn":"Bug\\/\\u5b8c\\u6210\\u4efb\\u52a1"},"seriousBugs":{"zh-cn":"\\u91cd\\u8981Bug\\u6570"},"seriousBugsPercent":{"zh-cn":"\\u4e25\\u91cdBug\\u6bd4\\u7387"},"project":{"zh-cn":"\\u9879\\u76ee\\u540d\\u79f0"},"execution":{"zh-cn":"\\u6267\\u884c\\u540d\\u79f0"}}', '{"varName":["project","execution"],"showName":["\\u9879\\u76ee\\u5217\\u8868","\\u6267\\u884c\\u5217\\u8868"],"requestType":["select","select"],"selectList":["project","execution"],"default":["",""]}', null, '{"group1":"project","group2":"execution","columnTotal":"sum","columns":[{"field":"stories","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"doneStory","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"number","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"doneTask","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"bugs","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"resolutions","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"bugthanstory","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"bugthantask","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"seriousBugs","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"}]}', '[{"from":"query","field":"project","name":"\\u9879\\u76ee\\u5217\\u8868","type":"select","typeOption":"project","default":""},{"from":"query","field":"execution","name":"\\u6267\\u884c\\u5217\\u8868","type":"select","typeOption":"execution","default":""}]', 4, 'published', '0', 'admin', '2015-07-23 17:03:10', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1020, 1, '59,61', '{"zh-cn":"\\u4ea7\\u54c1Bug\\u7c7b\\u578b\\u7edf\\u8ba1\\u8868","zh-tw":"\\u7522\\u54c1Bug\\u985e\\u578b\\u7d71\\u8a08\\u8868","en":"Bug Type of Product","de":"Bug Type of Product","fr":"Bug Type of Product"}', '{"zh-cn":"\\u6309\\u7167\\u4ea7\\u54c1\\u7edf\\u8ba1Bug\\u7684\\u7c7b\\u578b\\u5206\\u5e03\\u60c5\\u51b5\\u3002","zh-tw":"\\u6309\\u7167\\u7522\\u54c1\\u7d71\\u8a08Bug\\u7684\\u985e\\u578b\\u5206\\u5e03\\u60c5\\u6cc1\\u3002","en":"Type distribution of Bugs.","de":"Type distribution of Bugs.","fr":"Type distribution of Bugs."}', 'select t1.id,t1.name,t2.id as bugID,t2.type from zt_product as t1 
left join zt_bug as t2 on t1.id=t2.product 
left join zt_project as t3 on t1.program=t3.id 
where t1.deleted=\'0\' and t2.deleted=\'0\'
order by t3.`order` asc, t1.line desc, t1.`order` asc', '{"id":{"object":"product","field":"id","type":"string"},"name":{"object":"product","field":"name","type":"string"},"bugID":{"object":"project","field":"bugID","type":"string"},"type":{"object":"bug","field":"type","type":"option"}}', '{"count":{"zh-cn":"\\u9700\\u6c42\\u6570","zh-tw":"\\u9700\\u6c42\\u6570","en":"Stories"},"done":{"zh-cn":"\\u5b8c\\u6210\\u6570","zh-tw":"\\u5b8c\\u6210\\u6570","en":"Done"},"id":{"zh-cn":"\\u7f16\\u53f7","zh-tw":"","en":"","de":"","fr":""},"name":{"zh-cn":"\\u4ea7\\u54c1\\u540d\\u79f0","zh-tw":"","en":"","de":"","fr":""},"bugID":{"zh-cn":"bugID","zh-tw":"","en":"","de":"","fr":""},"type":{"zh-cn":"Bug\\u7c7b\\u578b","zh-tw":"","en":"","de":"","fr":""}}', '', null, '{"group1":"name","group2":"","columnTotal":"sum","columns":[{"field":"type","slice":"type","stat":"count","showTotal":"sum","showMode":"default","monopolize":"0"}]}', '', 4, 'published', '0', 'admin', '2015-07-24 13:48:22', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1021, 1, '59', '{"zh-cn":"\\u4ea7\\u54c1\\u8d28\\u91cf\\u8868","zh-tw":"\\u7522\\u54c1\\u8cea\\u91cf\\u8868","en":"Product Quality","de":"Product Quality","fr":"Product Quality"}', '{"zh-cn":"\\u5217\\u51fa\\u4ea7\\u54c1\\u7684\\u9700\\u6c42\\u6570\\uff0c\\u5b8c\\u6210\\u7684\\u9700\\u6c42\\u603b\\u6570\\uff0cBug\\u6570\\uff0c\\u89e3\\u51b3\\u7684Bug\\u603b\\u6570\\uff0cBug\\/\\u9700\\u6c42\\uff0c\\u91cd\\u8981Bug\\u6570\\u91cf(\\u4e25\\u91cd\\u7a0b\\u5ea6\\u5c0f\\u4e8e3)\\u3002","zh-tw":"\\u5217\\u51fa\\u7522\\u54c1\\u7684\\u9700\\u6c42\\u6578\\uff0c\\u5b8c\\u6210\\u7684\\u9700\\u6c42\\u7e3d\\u6578\\uff0cBug\\u6578\\uff0c\\u89e3\\u6c7a\\u7684Bug\\u7e3d\\u6578\\uff0cBug\\/\\u9700\\u6c42\\uff0c\\u91cd\\u8981Bug\\u6578\\u91cf(\\u56b4\\u91cd\\u7a0b\\u5ea6\\u5c0f\\u65bc3)\\u3002","en":"Serious Bug (severity is less than 3).","de":"Serious Bug (severity is less than 3).","fr":"Serious Bug (severity is less than 3)."}', 'select t1.id,t1.name,t2.stories,(t2.stories-t2.undone) as doneStory,t3.bugs,t3.resolutions,round(t3.bugs/(t2.stories-t2.undone),2) as bugthanstory,t3.seriousBugs from zt_product as t1 
left join ztv_productstories as t2 on t1.id=t2.product 
left join ztv_productbugs as t3 on t1.id=t3.product 
left join zt_project as t4 on t1.program=t4.id 
where t1.deleted=\'0\'
order by t4.`order` asc, t1.line desc, t1.`order` asc', '{"id":{"object":"product","field":"id","type":"string"},"name":{"object":"product","field":"name","type":"string"},"stories":{"object":"project","field":"stories","type":"string"},"doneStory":{"object":"project","field":"doneStory","type":"string"},"bugs":{"object":"product","field":"bugs","type":"string"},"resolutions":{"object":"project","field":"resolutions","type":"string"},"bugthanstory":{"object":"project","field":"bugthanstory","type":"string"},"seriousBugs":{"object":"project","field":"seriousBugs","type":"string"}}', '{"stories":{"zh-cn":"\\u9700\\u6c42\\u603b\\u6570","zh-tw":"\\u9700\\u6c42\\u603b\\u6570","en":"Stories"},"doneStory":{"zh-cn":"\\u5b8c\\u6210\\u9700\\u6c42\\u6570","zh-tw":"\\u5b8c\\u6210\\u9700\\u6c42\\u6570","en":"Finished Stories"},"bugs":{"zh-cn":"Bug\\u6570","zh-tw":"Bug\\u6570","en":"Bugs"},"resolutions":{"zh-cn":"\\u89e3\\u51b3Bug\\u6570","zh-tw":"\\u89e3\\u51b3Bug\\u6570","en":"Solved Bugs"},"bugthanstory":{"zh-cn":"Bug\\/\\u5b8c\\u6210\\u9700\\u6c42","zh-tw":"Bug\\/\\u5b8c\\u6210\\u9700\\u6c42","en":"Bug\\/Finished Story"},"seriousBugs":{"zh-cn":"\\u91cd\\u8981Bug\\u6570","zh-tw":"\\u91cd\\u8981Bug\\u6570","en":"Serious Bugs"},"seriousBugsPercent":{"zh-cn":"\\u4e25\\u91cdbug\\u6bd4\\u7387","zh-tw":"\\u4e25\\u91cdbug\\u6bd4\\u7387","en":"Serious Bugs %"}}', '', null, '{"group1":"name","group2":"","columnTotal":"sum","columns":[{"field":"stories","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"doneStory","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"bugs","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"resolutions","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"bugthanstory","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"seriousBugs","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"1"}]}', '', 4, 'published', '0', 'admin', '2015-07-23 17:17:40', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1022, 1, '62', '{"zh-cn":"\\u5458\\u5de5\\u767b\\u5f55\\u6b21\\u6570\\u7edf\\u8ba1\\u8868","zh-tw":"\\u54e1\\u5de5\\u767b\\u9304\\u6b21\\u6578\\u7d71\\u8a08\\u8868","en":"Login Times","de":"Login Times","fr":"Login Times"}', '{"zh-cn":"\\u5b9e\\u73b0\\u5458\\u5de5\\u767b\\u5f55\\u6b21\\u6570\\u7edf\\u8ba1\\u62a5\\u8868\\uff0c\\u6309\\u7167\\u5929\\u7edf\\u8ba1\\u6bcf\\u5929\\u6bcf\\u4e2a\\u4eba\\u7684\\u767b\\u5f55\\u6b21\\u6570\\uff0c\\u4ee5\\u53ca\\u603b\\u6570\\u3002","zh-tw":"\\u5be6\\u73fe\\u54e1\\u5de5\\u767b\\u9304\\u6b21\\u6578\\u7d71\\u8a08\\u5831\\u8868\\uff0c\\u6309\\u7167\\u5929\\u7d71\\u8a08\\u6bcf\\u5929\\u6bcf\\u500b\\u4eba\\u7684\\u767b\\u9304\\u6b21\\u6578\\uff0c\\u4ee5\\u53ca\\u7e3d\\u6578\\u3002","en":"The summary of user login times.","de":"The summary of user login times.","fr":"The summary of user login times."}', 'select actor,LEFT(`date`,10) as `day` from zt_action where `action`=\'login\' and if($startDate=\'\',1,`date`>=$startDate) and if($endDate=\'\',1,`date`<=$endDate) order by `date` asc, actor asc', '{"actor":{"object":"action","field":"actor","type":"user","name":"\\u64cd\\u4f5c\\u8005"},"day":{"object":"action","field":"day","type":"string","name":"day"}}', '{"count":{"zh-cn":"\\u9700\\u6c42\\u6570","zh-tw":"\\u9700\\u6c42\\u6570","en":"Stories"},"done":{"zh-cn":"\\u5b8c\\u6210\\u6570","zh-tw":"\\u5b8c\\u6210\\u6570","en":"Done"},"actor":{"zh-cn":"\\u64cd\\u4f5c\\u8005","zh-tw":"","en":"","de":"","fr":""},"day":{"zh-cn":"day","zh-tw":"","en":"","de":"","fr":""}}', '{"varName":["startDate","endDate"],"showName":["\\u8d77\\u59cb\\u65f6\\u95f4","\\u7ed3\\u675f\\u65f6\\u95f4"],"requestType":["date","date"],"selectList":["user","user"],"default":["$MONTHBEGIN","$MONTHEND"]}', null, '{"group1":"actor","columnTotal":"sum","columns":[{"field":"day","slice":"day","stat":"count","showTotal":"sum","showMode":"default","monopolize":"0"}],"lastStep":"4"}', '[{"from":"query","field":"startDate","name":"\\u8d77\\u59cb\\u65f6\\u95f4","type":"date","typeOption":"","default":"$MONTHBEGIN"},{"from":"query","field":"endDate","name":"\\u7ed3\\u675f\\u65f6\\u95f4","type":"date","typeOption":"","default":"$MONTHEND"}]', 4, 'published', '0', 'admin', '2015-07-24 14:28:11', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1023, 1, '62', '{"zh-cn":"\\u65e5\\u5fd7\\u6c47\\u603b\\u8868","zh-tw":"\\u65e5\\u8a8c\\u532f\\u7e3d\\u8868","en":"Effort Summary","de":"Effort Summary","fr":"Effort Summary"}', '{"zh-cn":"\\u67e5\\u770b\\u67d0\\u4e2a\\u65f6\\u95f4\\u6bb5\\u5185\\u7684\\u65e5\\u5fd7\\u60c5\\u51b5\\uff0c\\u53ef\\u4ee5\\u6309\\u7167\\u90e8\\u95e8\\u9009\\u62e9\\u3002","zh-tw":"\\u67e5\\u770b\\u67d0\\u500b\\u6642\\u9593\\u6bb5\\u5167\\u7684\\u65e5\\u8a8c\\u60c5\\u6cc1\\uff0c\\u53ef\\u4ee5\\u6309\\u7167\\u90e8\\u9580\\u9078\\u64c7\\u3002","en":"Effort summary of users.","de":"Effort summary of users","fr":"Effort summary of users"}', 'select t1.account, t1.consumed, t1.`date`, if($dept=\'\', 0, t2.dept) as dept from zt_effort as t1 left join zt_user as t2 on t1.account = t2.account left join zt_dept as t3 on t2.dept = t3.id where t1.`deleted` = \'0\' and if($startDate=\'\', 1, t1.`date` >= $startDate) and if($endDate=\'\', 1, t1.`date` <= $endDate) and (t3.path like concat((select path from zt_dept where id=$dept), \'%\') or $dept=0) order by t1.`date` asc', '{"account":{"object":"effort","field":"account","type":"user","name":"account"},"consumed":{"object":"effort","field":"consumed","type":"object","name":"consumed"},"date":{"object":"effort","field":"date","type":"object","name":"date"},"dept":{"object":"effort","field":"dept","type":"object","name":"dept"}}', '{"date":{"zh-cn":"\\u65e5\\u671f","zh-tw":"\\u65e5\\u671f","en":"Date","de":"","fr":""},"consumed":{"zh-cn":"\\u6d88\\u8017\\u5de5\\u65f6","zh-tw":"\\u6d88\\u8017\\u5de5\\u65f6","en":"Cost","de":"","fr":""},"account":{"zh-cn":"\\u540d\\u79f0","zh-tw":"","en":"","de":"","fr":""},"dept":{"zh-cn":"dept","zh-tw":"","en":"","de":"","fr":""}}', '{"varName":["dept","startDate","endDate"],"showName":["\\u90e8\\u95e8","\\u8d77\\u59cb\\u65f6\\u95f4","\\u7ed3\\u675f\\u65f6\\u95f4"],"requestType":["select","date","date"],"selectList":["dept","user","user"],"default":["","$MONTHBEGIN","$MONTHEND"]}', null, '{"group1":"account","group2":"","columnTotal":"sum","columns":[{"field":"consumed","slice":"date","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"}],"lastStep":"4"}', '[{"from":"query","field":"dept","name":"\\u90e8\\u95e8","type":"select","typeOption":"dept","default":""},{"from":"query","field":"startDate","name":"\\u8d77\\u59cb\\u65f6\\u95f4","type":"date","typeOption":"","default":"$MONTHBEGIN"},{"from":"query","field":"endDate","name":"\\u7ed3\\u675f\\u65f6\\u95f4","type":"date","typeOption":"","default":"$MONTHEND"}]', 4, 'published', '0', 'admin', '2015-07-27 13:53:32', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1024, 1, '62', '{"zh-cn":"\\u516c\\u53f8\\u52a8\\u6001\\u6c47\\u603b\\u8868","zh-tw":"\\u516c\\u53f8\\u52d5\\u614b\\u532f\\u7e3d\\u8868","en":"Company Dynamics","de":"Company Dynamics","fr":"Company Dynamics"}', '{"zh-cn":"\\u53ef\\u4ee5\\u6307\\u5b9a\\u4e00\\u4e2a\\u65f6\\u671f\\uff0c\\u5217\\u51fa\\u76f8\\u5e94\\u7684\\u6570\\u636e\\uff1a1. \\u6bcf\\u5929\\u7684\\u767b\\u5f55\\u6b21\\u6570\\u30022. \\u6bcf\\u5929\\u7684\\u65e5\\u5fd7\\u5de5\\u65f6\\u91cf\\u30023. \\u6bcf\\u5929\\u65b0\\u589e\\u7684\\u9700\\u6c42\\u6570\\u30024. \\u6bcf\\u5929\\u5173\\u95ed\\u7684\\u9700\\u6c42\\u6570\\u30025. \\u6bcf\\u5929\\u65b0\\u589e\\u7684\\u4efb\\u52a1\\u6570\\u30026. \\u6bcf\\u5929\\u5b8c\\u6210\\u7684\\u4efb\\u52a1\\u6570\\u30027. \\u6bcf\\u5929\\u65b0\\u589e\\u7684Bug\\u6570\\u30028. \\u6bcf\\u5929\\u89e3\\u51b3\\u7684Bug\\u6570\\u30029. \\u6bcf\\u5929\\u7684\\u52a8\\u6001\\u6570\\u3002","zh-tw":"\\u53ef\\u4ee5\\u6307\\u5b9a\\u4e00\\u500b\\u6642\\u671f\\uff0c\\u5217\\u51fa\\u76f8\\u61c9\\u7684\\u6578\\u64da\\uff1a1. \\u6bcf\\u5929\\u7684\\u767b\\u9304\\u6b21\\u6578\\u30022. \\u6bcf\\u5929\\u7684\\u65e5\\u8a8c\\u5de5\\u6642\\u91cf\\u30023. \\u6bcf\\u5929\\u65b0\\u589e\\u7684\\u9700\\u6c42\\u6578\\u30024. \\u6bcf\\u5929\\u95dc\\u9589\\u7684\\u9700\\u6c42\\u6578\\u30025. \\u6bcf\\u5929\\u65b0\\u589e\\u7684\\u4efb\\u52d9\\u6578\\u30026. \\u6bcf\\u5929\\u5b8c\\u6210\\u7684\\u4efb\\u52d9\\u6578\\u30027. \\u6bcf\\u5929\\u65b0\\u589e\\u7684Bug\\u6578\\u30028. \\u6bcf\\u5929\\u89e3\\u6c7a\\u7684Bug\\u6578\\u30029. \\u6bcf\\u5929\\u7684\\u52d5\\u614b\\u6578\\u3002","en":"The summary of company dynamics","de":"The summary of company dynamics","fr":"The summary of company dynamics"}', 'select t1.day,t2.userlogin,t3.consumed,t4.storyopen,t5.storyclose,t6.taskopen,t7.taskfinish,t8.bugopen,t9.bugresolve,t1.actions from ztv_dayactions as t1 left join ztv_dayuserlogin as t2 on t1.day=t2.day left join ztv_dayeffort as t3 on t1.day=t3.date left join ztv_daystoryopen as t4 on t1.day=t4.day left join ztv_daystoryclose as t5 on t1.day=t5.day left join ztv_daytaskopen as t6 on t1.day=t6.day left join ztv_daytaskfinish as t7 on t1.day=t7.day left join ztv_daybugopen as t8 on t1.day=t8.day left join ztv_daybugresolve as t9 on t1.day=t9.day where if($startDate=\'\',1,t1.day>=$startDate) and if($endDate=\'\',1,t1.day<=$endDate)', '{"day":{"object":false,"field":"day","type":"string"},"userlogin":{"object":false,"field":"userlogin","type":"string"},"consumed":{"object":false,"field":"consumed","type":"string"},"storyopen":{"object":false,"field":"storyopen","type":"string"},"storyclose":{"object":false,"field":"storyclose","type":"string"},"taskopen":{"object":false,"field":"taskopen","type":"string"},"taskfinish":{"object":false,"field":"taskfinish","type":"string"},"bugopen":{"object":false,"field":"bugopen","type":"string"},"bugresolve":{"object":false,"field":"bugresolve","type":"string"},"actions":{"object":false,"field":"actions","type":"string"}}', '{"day":{"zh-cn":"\\u65e5\\u671f","zh-tw":"\\u65e5\\u671f","en":"Date"},"userlogin":{"zh-cn":"\\u767b\\u5f55\\u6b21\\u6570","zh-tw":"\\u767b\\u9304\\u6b21\\u6578","en":"Login"},"consumed":{"zh-cn":"\\u65e5\\u5fd7\\u5de5\\u65f6","zh-tw":"\\u65e5\\u8a8c\\u5de5\\u6642","en":"Cost(h)"},"storyopen":{"zh-cn":"\\u65b0\\u589e\\u9700\\u6c42\\u6570","zh-tw":"\\u65b0\\u589e\\u9700\\u6c42\\u6578","en":"Open Story"},"storyclose":{"zh-cn":"\\u5173\\u95ed\\u9700\\u6c42\\u6570","zh-tw":"\\u95dc\\u9589\\u9700\\u6c42\\u6578","en":"Closed Story"},"taskopen":{"zh-cn":"\\u65b0\\u589e\\u4efb\\u52a1\\u6570","zh-tw":"\\u65b0\\u589e\\u4efb\\u52d9\\u6578","en":"Open Task"},"taskfinish":{"zh-cn":"\\u5b8c\\u6210\\u4efb\\u52a1\\u6570","zh-tw":"\\u5b8c\\u6210\\u4efb\\u52d9\\u6578","en":"Finished Task"},"bugopen":{"zh-cn":"\\u65b0\\u589eBug\\u6570","zh-tw":"\\u65b0\\u589eBug\\u6578","en":"Open Bug"},"bugresolve":{"zh-cn":"\\u89e3\\u51b3Bug\\u6570","zh-tw":"\\u89e3\\u51b3Bug\\u6578","en":"Resolved bug"},"actions":{"zh-cn":"\\u52a8\\u6001\\u6570","zh-tw":"\\u52d5\\u614b\\u6578","en":"Dynamics"}}', '{"varName":["startDate","endDate"],"showName":["\\u8d77\\u59cb\\u65f6\\u95f4","\\u7ed3\\u675f\\u65f6\\u95f4"],"requestType":["date","date"],"selectList":["user","user"],"default":["$MONTHBEGIN","$MONTHEND"]}', null, '{"group1":"day","group2":"","columnTotal":"sum","columns":[{"field":"userlogin","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"consumed","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"storyopen","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"storyclose","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"taskopen","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"taskfinish","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"bugopen","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"bugresolve","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"actions","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"}]}', '[{"from":"query","field":"startDate","name":"\\u8d77\\u59cb\\u65f6\\u95f4","type":"date","typeOption":"","default":"$MONTHBEGIN"},{"from":"query","field":"endDate","name":"\\u7ed3\\u675f\\u65f6\\u95f4","type":"date","typeOption":"","default":"$MONTHEND"}]', 4, 'published', '0', 'admin', '2015-07-27 15:09:42', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1025, 1, '61', '{"zh-cn":"Bug\\u89e3\\u51b3\\u8868","zh-tw":"Bug\\u89e3\\u6c7a\\u8868","en":"Solved Bugs","de":"Solved Bugs","fr":"Solved Bugs"}', '{"zh-cn":"\\u5217\\u51fa\\u89e3\\u51b3\\u7684Bug\\u603b\\u6570\\uff0c\\u89e3\\u51b3\\u65b9\\u6848\\u7684\\u5206\\u5e03\\uff0c\\u5360\\u7684\\u6bd4\\u4f8b\\uff08\\u8be5\\u7528\\u6237\\u89e3\\u51b3\\u7684Bug\\u7684\\u6570\\u91cf\\u5360\\u6240\\u6709\\u7684\\u89e3\\u51b3\\u7684Bug\\u7684\\u6570\\u91cf)\\u3002","zh-tw":"\\u5217\\u51fa\\u89e3\\u6c7a\\u7684Bug\\u7e3d\\u6578\\uff0c\\u89e3\\u6c7a\\u65b9\\u6848\\u7684\\u5206\\u5e03\\uff0c\\u5360\\u7684\\u6bd4\\u4f8b\\uff08\\u8a72\\u7528\\u6236\\u89e3\\u6c7a\\u7684Bug\\u7684\\u6578\\u91cf\\u5360\\u6240\\u6709\\u7684\\u89e3\\u6c7a\\u7684Bug\\u7684\\u6578\\u91cf)\\u3002","en":"percentage:self resolved \\/ all resolved","de":"percentage:self resolved \\/ all resolved","fr":"percentage:self resolved \\/ all resolved"}', 'select t1.*,if($product=\'\',0,t1.product) as customproduct from zt_bug as t1 left join zt_product as t2 on t1.product = t2.id where t1.deleted=\'0\' and t2.deleted=\'0\' and t1.resolution!=\'\' and if($startDate=\'\',1,t1.resolvedDate>=$startDate) and if($endDate=\'\',1,t1.resolvedDate<=$endDate) having customproduct=$product', '{"id":{"object":"bug","field":"id","type":"string"},"project":{"object":"bug","field":"project","type":"string"},"product":{"object":"bug","field":"product","type":"string"},"injection":{"object":"bug","field":"injection","type":"string"},"identify":{"object":"bug","field":"identify","type":"string"},"branch":{"object":"bug","field":"branch","type":"string"},"module":{"object":"bug","field":"module","type":"string"},"execution":{"object":"bug","field":"execution","type":"string"},"plan":{"object":"bug","field":"plan","type":"string"},"story":{"object":"bug","field":"story","type":"string"},"storyVersion":{"object":"bug","field":"storyVersion","type":"string"},"task":{"object":"bug","field":"task","type":"string"},"toTask":{"object":"bug","field":"toTask","type":"string"},"toStory":{"object":"bug","field":"toStory","type":"string"},"title":{"object":"bug","field":"title","type":"string"},"keywords":{"object":"bug","field":"keywords","type":"string"},"severity":{"object":"bug","field":"severity","type":"string"},"pri":{"object":"bug","field":"pri","type":"string"},"type":{"object":"bug","field":"type","type":"string"},"os":{"object":"bug","field":"os","type":"string"},"browser":{"object":"bug","field":"browser","type":"string"},"hardware":{"object":"bug","field":"hardware","type":"string"},"found":{"object":"bug","field":"found","type":"string"},"steps":{"object":"bug","field":"steps","type":"string"},"status":{"object":"bug","field":"status","type":"string"},"subStatus":{"object":"bug","field":"subStatus","type":"string"},"color":{"object":"bug","field":"color","type":"string"},"confirmed":{"object":"bug","field":"confirmed","type":"string"},"activatedCount":{"object":"bug","field":"activatedCount","type":"string"},"activatedDate":{"object":"bug","field":"activatedDate","type":"string"},"feedbackBy":{"object":"bug","field":"feedbackBy","type":"string"},"notifyEmail":{"object":"bug","field":"notifyEmail","type":"string"},"mailto":{"object":"bug","field":"mailto","type":"string"},"openedBy":{"object":"bug","field":"openedBy","type":"string"},"openedDate":{"object":"bug","field":"openedDate","type":"string"},"openedBuild":{"object":"bug","field":"openedBuild","type":"string"},"assignedTo":{"object":"bug","field":"assignedTo","type":"string"},"assignedDate":{"object":"bug","field":"assignedDate","type":"string"},"deadline":{"object":"bug","field":"deadline","type":"string"},"resolvedBy":{"object":"bug","field":"resolvedBy","type":"string"},"resolution":{"object":"bug","field":"resolution","type":"string"},"resolvedBuild":{"object":"bug","field":"resolvedBuild","type":"string"},"resolvedDate":{"object":"bug","field":"resolvedDate","type":"string"},"closedBy":{"object":"bug","field":"closedBy","type":"string"},"closedDate":{"object":"bug","field":"closedDate","type":"string"},"duplicateBug":{"object":"bug","field":"duplicateBug","type":"string"},"linkBug":{"object":"bug","field":"linkBug","type":"string"},"case":{"object":"bug","field":"case","type":"string"},"caseVersion":{"object":"bug","field":"caseVersion","type":"string"},"feedback":{"object":"bug","field":"feedback","type":"string"},"result":{"object":"bug","field":"result","type":"string"},"repo":{"object":"bug","field":"repo","type":"string"},"mr":{"object":"bug","field":"mr","type":"string"},"entry":{"object":"bug","field":"entry","type":"string"},"lines":{"object":"bug","field":"lines","type":"string"},"v1":{"object":"bug","field":"v1","type":"string"},"v2":{"object":"bug","field":"v2","type":"string"},"repoType":{"object":"bug","field":"repoType","type":"string"},"issueKey":{"object":"bug","field":"issueKey","type":"string"},"testtask":{"object":"bug","field":"testtask","type":"string"},"lastEditedBy":{"object":"bug","field":"lastEditedBy","type":"string"},"lastEditedDate":{"object":"bug","field":"lastEditedDate","type":"string"},"deleted":{"object":"bug","field":"deleted","type":"string"},"customproduct":{"object":"bug","field":"customproduct","type":"string"}}', '{"count":{"zh-cn":"\\u9700\\u6c42\\u6570","zh-tw":"\\u9700\\u6c42\\u6570","en":"Stories"},"done":{"zh-cn":"\\u5b8c\\u6210\\u6570","zh-tw":"\\u5b8c\\u6210\\u6570","en":"Done"}}', '{"varName":["product","startDate","endDate"],"showName":["\\u4ea7\\u54c1","\\u89e3\\u51b3\\u65e5\\u671f\\u5f00\\u59cb","\\u89e3\\u51b3\\u65e5\\u671f\\u7ed3\\u675f"],"requestType":["select","date","date"],"selectList":["product","user","user"],"default":["","$MONTHBEGIN","$MONTHEND"]}', null, '{"group1":"resolvedBy","group2":"","columnTotal":"sum","columns":[{"field":"resolution","slice":"resolution","stat":"count","showTotal":"sum","showMode":"total","monopolize":"1"}]}', '[{"from":"query","field":"product","name":"\\u4ea7\\u54c1","type":"select","typeOption":"product","default":""},{"from":"query","field":"startDate","name":"\\u89e3\\u51b3\\u65e5\\u671f\\u5f00\\u59cb","type":"date","typeOption":"","default":"$MONTHBEGIN"},{"from":"query","field":"endDate","name":"\\u89e3\\u51b3\\u65e5\\u671f\\u7ed3\\u675f","type":"date","typeOption":"","default":"$MONTHEND"}]', 4, 'published', '0', 'admin', '2015-07-24 13:44:25', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1026, 1, '60', '{"zh-cn":"\\u9879\\u76ee\\u8fdb\\u5c55\\u8868","zh-tw":"\\u9805\\u76ee\\u9032\\u5c55\\u8868","en":"Project Progress Report","de":"Project Progress Report","fr":"Project Progress Report","vi":"Project Progress Report","ja":"Project Progress Report"}', '{"zh-cn":"\\u9879\\u76ee\\u7684\\u9700\\u6c42\\u6570\\uff0c\\u4efb\\u52a1\\u6570\\uff0c\\u5df2\\u6d88\\u8017\\u5de5\\u65f6\\uff0c\\u5269\\u4f59\\u5de5\\u65f6\\uff0c\\u5269\\u4f59\\u9700\\u6c42\\u6570\\uff0c\\u5269\\u4f59\\u4efb\\u52a1\\u6570\\uff0c\\u8fdb\\u5ea6\\u3002","zh-tw":"\\u9805\\u76ee\\u7684\\u9700\\u6c42\\u6578\\uff0c\\u4efb\\u52d9\\u6578\\uff0c\\u5df2\\u6d88\\u8017\\u5de5\\u6642\\uff0c\\u5269\\u9918\\u5de5\\u6642\\uff0c\\u5269\\u9918\\u9700\\u6c42\\u6578\\uff0c\\u5269\\u9918\\u4efb\\u52d9\\u6578\\uff0c\\u9032\\u5ea6\\u3002","en":"","de":"","fr":"","vi":"","ja":""}', 'select t1.id,t4.name as project,IF(t4.multiple="1",t1.name,"") as execution,t1.status,t2.number as tasks,round(t2.consumed,2) as consumed,round(t2.`left`,2) as `left`,t3.stories,t2.undone as undoneTask,t3.undone as undoneStory,t2.totalReal from zt_project as t1 
left join ztv_executionsummary as t2 on t1.id=t2.execution
left join ztv_projectstories as t3 on t1.id=t3.execution
left join zt_project as t4 on t4.id=t1.project
where t1.deleted=\'0\' and t1.type in (\'sprint\',\'stage\') and if($project=\'\',1,t4.id=$project) and if($execution=\'\',1,t1.id=$execution) and if($status=\'\',1,t1.status=$status)', '{"id":{"object":"project","field":"id","type":"string"},"project":{"object":"project","field":"project","type":"string"},"t4id":{"object":"project","field":"t4id","type":"string"},"execution":{"object":"project","field":"execution","type":"string"},"status":{"object":"project","field":"status","type":"string"},"tasks":{"object":"project","field":"tasks","type":"string"},"consumed":{"object":"project","field":"consumed","type":"string"},"left":{"object":"project","field":"left","type":"string"},"stories":{"object":"project","field":"stories","type":"string"},"undoneTask":{"object":"project","field":"undoneTask","type":"string"},"undoneStory":{"object":"project","field":"undoneStory","type":"string"},"totalReal":{"object":"project","field":"totalReal","type":"string"}}', '{"stories":{"zh-cn":"\\u9700\\u6c42\\u6570","zh-tw":"\\u9700\\u6c42\\u6570","en":"Stories"},"tasks":{"zh-cn":"\\u4efb\\u52a1\\u6570","zh-tw":"\\u4efb\\u52a1\\u6570","en":"Tasks"},"undoneStory":{"zh-cn":"\\u5269\\u4f59\\u9700\\u6c42\\u6570","zh-tw":"\\u5269\\u4f59\\u9700\\u6c42\\u6570","en":"Undone Story"},"undoneTask":{"zh-cn":"\\u5269\\u4f59\\u4efb\\u52a1\\u6570","zh-tw":"\\u5269\\u4f59\\u4efb\\u52a1\\u6570","en":"Undone Task"},"consumed":{"zh-cn":"\\u5df2\\u6d88\\u8017\\u5de5\\u65f6","zh-tw":"\\u5df2\\u6d88\\u8017\\u5de5\\u65f6","en":"Cost(h)"},"left":{"zh-cn":"\\u5269\\u4f59\\u5de5\\u65f6","zh-tw":"\\u5269\\u4f59\\u5de5\\u65f6","en":"Left(h)"},"consumedPercent":{"zh-cn":"\\u8fdb\\u5ea6","zh-tw":"\\u8fdb\\u5ea6","en":"Process"},"execution":{"zh-cn":"\\u6267\\u884c\\u540d\\u79f0"}}', '{"varName":["project","execution","status"],"showName":["\\u9879\\u76ee\\u5217\\u8868","\\u6267\\u884c\\u5217\\u8868","\\u6267\\u884c\\u72b6\\u6001"],"requestType":["select","select","select"],"selectList":["project","execution","project.status"],"default":["","",""]}', null, '{"group1":"project","group2":"execution","columnTotal":"sum","columns":[{"field":"stories","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"undoneStory","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"tasks","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"undoneTask","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"left","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"0"},{"field":"consumed","slice":"noSlice","stat":"sum","showTotal":"noShow","showMode":"default","monopolize":"1"}]}', '[{"from":"query","field":"project","name":"\\u9879\\u76ee\\u5217\\u8868","type":"select","typeOption":"project","default":""},{"from":"query","field":"execution","name":"\\u6267\\u884c\\u5217\\u8868","type":"select","typeOption":"execution","default":""},{"from":"query","field":"status","name":"\\u6267\\u884c\\u72b6\\u6001","type":"select","typeOption":"project.status","default":""}]', 4, 'published', '0', 'admin', '2015-07-23 14:03:06', '', null, '0');
INSERT INTO zt_pivot (id, dimension, `group`, name, `desc`, `sql`, fields, langs, vars, objects, settings, filters, step, stage, builtin, createdBy, createdDate, editedBy, editedDate, deleted) VALUES (1027, 1, '60,61', '{"zh-cn":"\\u9879\\u76eeBug\\u7c7b\\u578b\\u7edf\\u8ba1\\u8868","zh-tw":"\\u9805\\u76eeBug\\u985e\\u578b\\u7d71\\u8a08\\u8868","en":"Project Bug Type","de":"Project Bug Type","fr":"Project Bug Type","vi":"Project Bug Type","ja":"Project Bug Type"}', '{"zh-cn":"\\u6309\\u7167\\u9879\\u76ee\\u7edf\\u8ba1Bug\\u7684\\u7c7b\\u578b\\u5206\\u5e03\\u60c5\\u51b5\\u3002","zh-tw":"\\u6309\\u7167\\u9805\\u76ee\\u7d71\\u8a08Bug\\u7684\\u985e\\u578b\\u5206\\u5e03\\u60c5\\u6cc1\\u3002","en":"","de":"","fr":"","vi":"","ja":""}', 'select t1.id,t3.name as project,IF(t3.multiple="1",t1.name,"") as execution,t2.id as bugID,t2.type from zt_project as t1 
left join zt_bug as t2 on t1.id=t2.execution
left join zt_project as t3 on t3.id=t1.project
where t1.deleted=\'0\' and t2.deleted=\'0\' and if($project=\'\',1,t3.id=$project) and if($execution=\'\',1,t1.id=$execution)', '{"id":{"object":"project","field":"id","type":"string"},"project":{"object":"project","field":"project","type":"string"},"t3id":{"object":"project","field":"t3id","type":"string"},"execution":{"object":"project","field":"execution","type":"string"},"bugID":{"object":"bug","field":"bugID","type":"string"},"type":{"object":"bug","field":"type","type":"option"}}', '{"stories":{"zh-cn":"\\u9700\\u6c42\\u6570","zh-tw":"\\u9700\\u6c42\\u6570","en":"Stories"},"tasks":{"zh-cn":"\\u4efb\\u52a1\\u6570","zh-tw":"\\u4efb\\u52a1\\u6570","en":"Tasks"},"undoneStory":{"zh-cn":"\\u5269\\u4f59\\u9700\\u6c42\\u6570","zh-tw":"\\u5269\\u4f59\\u9700\\u6c42\\u6570","en":"Undone Story"},"undoneTask":{"zh-cn":"\\u5269\\u4f59\\u4efb\\u52a1\\u6570","zh-tw":"\\u5269\\u4f59\\u4efb\\u52a1\\u6570","en":"Undone Task"},"consumed":{"zh-cn":"\\u5df2\\u6d88\\u8017\\u5de5\\u65f6","zh-tw":"\\u5df2\\u6d88\\u8017\\u5de5\\u65f6","en":"Cost(h)"},"left":{"zh-cn":"\\u5269\\u4f59\\u5de5\\u65f6","zh-tw":"\\u5269\\u4f59\\u5de5\\u65f6","en":"Left(h)"},"consumedPercent":{"zh-cn":"\\u8fdb\\u5ea6","zh-tw":"\\u8fdb\\u5ea6","en":"Process"},"execution":{"zh-cn":"\\u6267\\u884c\\u540d\\u79f0","zh-tw":"","en":"","de":"","fr":""},"id":{"zh-cn":"\\u9879\\u76eeID","zh-tw":"","en":"","de":"","fr":""},"project":{"zh-cn":"\\u9879\\u76ee\\u540d\\u79f0","zh-tw":"","en":"","de":"","fr":""},"bugID":{"zh-cn":"bugID","zh-tw":"","en":"","de":"","fr":""},"type":{"zh-cn":"Bug\\u7c7b\\u578b","zh-tw":"","en":"","de":"","fr":""}}', '{"varName":["project","execution"],"showName":["\\u9879\\u76ee\\u5217\\u8868","\\u6267\\u884c\\u5217\\u8868"],"requestType":["select","select"],"selectList":["project","execution"],"default":["",""]}', null, '{"group1":"project","group2":"execution","columnTotal":"sum","columns":[{"field":"type","slice":"type","stat":"count","showTotal":"noShow","showMode":"default","monopolize":"0"}]}', '[{"from":"query","field":"project","name":"\\u9879\\u76ee\\u5217\\u8868","type":"select","typeOption":"project","default":""},{"from":"query","field":"execution","name":"\\u6267\\u884c\\u5217\\u8868","type":"select","typeOption":"execution","default":""}]', 4, 'published', '0', 'admin', '2015-08-04 13:54:22', '', null, '0');
