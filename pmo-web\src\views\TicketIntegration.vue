<template>
  <div class="ticket-integration">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>工单系统集成</h1>
      <p>工单系统数据分析与项目管理集成</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card clickable" @click="showProjectList">
          <div class="stat-content">
            <div class="stat-icon project-icon">
              <el-icon><Folder /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ dashboardStats.project_stats?.total_projects || 0 }}</div>
              <div class="stat-label">总项目数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card clickable" @click="showTicketsByStatus('all')">
          <div class="stat-content">
            <div class="stat-icon ticket-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ dashboardStats.ticket_stats?.total_tickets || 0 }}</div>
              <div class="stat-label">总工单数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card clickable" @click="showTicketsByStatus('completed')">
          <div class="stat-content">
            <div class="stat-icon completed-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ dashboardStats.ticket_stats?.completed_tickets || 0 }}</div>
              <div class="stat-label">已完成工单</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card clickable" @click="showTicketsByStatus('urgent')">
          <div class="stat-content">
            <div class="stat-icon urgent-icon">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ dashboardStats.ticket_stats?.urgent_tickets || 0 }}</div>
              <div class="stat-label">紧急工单</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作区域 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :span="24">
        <el-card class="action-card">
          <template #header>
            <div class="card-header">
              <span>快捷操作</span>
            </div>
          </template>

          <div class="action-buttons">
            <el-button type="primary" @click="showUsersList">
              <el-icon><User /></el-icon>
              人员列表
            </el-button>
            <el-button type="success" @click="showCompaniesList">
              <el-icon><OfficeBuilding /></el-icon>
              主体列表
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" class="main-content">
      <!-- 左侧：项目列表 -->
      <el-col :span="12">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <span>项目列表</span>
              <el-button type="primary" size="small" @click="refreshProjects">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <div v-loading="projectsLoading" class="projects-list">
            <div
              v-for="(project, index) in projects"
              :key="project.feelec_project_id"
              class="project-item"
              :class="{ active: selectedProject?.feelec_project_id === project.feelec_project_id }"
              @click="selectProject(project)"
            >
              <div class="project-header">
                <div class="project-title-section">
                  <div class="project-number">{{ index + 1 }}</div>
                  <h4>{{ project.feelec_name }}</h4>
                </div>
                <el-tag
                  :type="project.is_completed ? 'success' : 'primary'"
                  size="small"
                >
                  {{ project.is_completed ? '已完成' : '进行中' }}
                </el-tag>
              </div>
              
              <div class="project-info">
                <div class="info-item">
                  <span class="label">项目经理:</span>
                  <span class="value">{{ project.manager_name || '未指定' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">所属部门:</span>
                  <span class="value">{{ project.department_name || '未指定' }}</span>
                </div>
              </div>

              <div class="project-stats">
                <div class="stat-item">
                  <span class="label">工单总数:</span>
                  <span class="value">{{ project.total_tickets || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">完成率:</span>
                  <span class="value">{{ project.completion_rate || 0 }}%</span>
                </div>
                <div class="stat-item">
                  <span class="label">紧急工单:</span>
                  <span class="value urgent">{{ project.urgent_tickets || 0 }}</span>
                </div>
              </div>

              <div class="project-time">
                <small>创建时间: {{ project.create_time_formatted }}</small>
              </div>
            </div>
            
            <div v-if="projects.length === 0" class="empty-state">
              <el-empty description="暂无项目数据" />
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：工单详情 -->
      <el-col :span="12">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <span>{{ selectedProject ? `${selectedProject.feelec_name} - 工单详情` : '选择项目查看工单' }}</span>
              <el-button 
                v-if="selectedProject" 
                type="primary" 
                size="small" 
                @click="refreshTickets"
              >
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <div v-if="!selectedProject" class="empty-state">
            <el-empty description="请选择左侧项目查看工单详情" />
          </div>
          
          <div v-else v-loading="ticketsLoading" class="tickets-list">
            <div 
              v-for="ticket in tickets" 
              :key="ticket.feelec_ticket_id"
              class="ticket-item"
            >
              <div class="ticket-header">
                <h5>{{ ticket.feelec_title }}</h5>
                <div class="ticket-badges">
                  <el-tag 
                    :type="ticket.priority_color" 
                    size="small"
                  >
                    {{ ticket.priority_text }}
                  </el-tag>
                  <el-tag 
                    :type="ticket.is_completed ? 'success' : 'info'"
                    size="small"
                  >
                    {{ ticket.status_name || (ticket.is_completed ? '已完成' : '进行中') }}
                  </el-tag>
                  <el-tag 
                    v-if="ticket.is_overdue"
                    type="danger"
                    size="small"
                  >
                    逾期{{ ticket.overdue_days }}天
                  </el-tag>
                </div>
              </div>
              
              <div class="ticket-info">
                <div class="info-row">
                  <span class="label">工单编号:</span>
                  <span class="value">{{ ticket.feelec_ticket_no }}</span>
                </div>
                <div class="info-row">
                  <span class="label">创建时间:</span>
                  <span class="value">{{ ticket.create_time_formatted }}</span>
                </div>
                <div class="info-row">
                  <span class="label">完成时间:</span>
                  <span class="value">{{ ticket.complete_time_formatted }}</span>
                </div>
                <div v-if="ticket.deadline_formatted !== '无期限'" class="info-row">
                  <span class="label">截止时间:</span>
                  <span class="value">{{ ticket.deadline_formatted }}</span>
                </div>
                <div v-if="ticket.process_duration_hours" class="info-row">
                  <span class="label">处理时长:</span>
                  <span class="value">{{ ticket.process_duration_hours }}小时</span>
                </div>
              </div>
            </div>
            
            <div v-if="tickets.length === 0" class="empty-state">
              <el-empty description="该项目暂无工单" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 底部：工作负荷分析和集成建议 -->
    <el-row :gutter="20" class="bottom-content">
      <!-- 工作负荷分析 -->
      <el-col :span="14">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <span>团队工作负荷分析</span>
              <el-button type="primary" size="small" @click="refreshWorkload">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <div v-loading="workloadLoading" class="workload-analysis">
            <div class="workload-summary">
              <div class="summary-item">
                <span class="label">分析周期:</span>
                <span class="value">{{ workloadStats.total_stats?.analysis_period }}</span>
              </div>
              <div class="summary-item">
                <span class="label">处理人数:</span>
                <span class="value">{{ workloadStats.total_stats?.total_processors }}</span>
              </div>
              <div class="summary-item">
                <span class="label">总工单数:</span>
                <span class="value">{{ workloadStats.total_stats?.total_tickets }}</span>
              </div>
              <div class="summary-item">
                <span class="label">整体完成率:</span>
                <span class="value">{{ workloadStats.total_stats?.overall_completion_rate }}%</span>
              </div>
            </div>
            
            <div class="workload-list">
              <div 
                v-for="item in workloadStats.workload_data?.slice(0, 8)" 
                :key="item.feelec_processor_id"
                class="workload-item"
              >
                <div class="processor-info">
                  <span class="processor-id">处理人: {{ item.processor_name || item.feelec_processor_id || '未分配' }}</span>
                  <el-tag 
                    :type="item.workload_level === 'high' ? 'danger' : item.workload_level === 'medium' ? 'warning' : 'success'"
                    size="small"
                  >
                    {{ item.workload_text }}
                  </el-tag>
                </div>
                <div class="workload-stats">
                  <span>总工单: {{ item.total_tickets }}</span>
                  <span>完成率: {{ item.completion_rate }}%</span>
                  <span>平均处理: {{ item.avg_process_hours }}小时</span>
                  <span v-if="item.urgent_tickets > 0" class="urgent">紧急: {{ item.urgent_tickets }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 集成建议 -->
      <el-col :span="10">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <span>集成建议</span>
              <el-button type="primary" size="small" @click="refreshSuggestions">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <div v-loading="suggestionsLoading" class="suggestions-list">
            <div 
              v-for="suggestion in suggestions" 
              :key="suggestion.title"
              class="suggestion-item"
            >
              <div class="suggestion-header">
                <h5>{{ suggestion.title }}</h5>
                <el-tag 
                  :type="suggestion.priority === 'high' ? 'danger' : 'warning'"
                  size="small"
                >
                  {{ suggestion.priority === 'high' ? '高优先级' : '中优先级' }}
                </el-tag>
              </div>
              <div class="suggestion-category">
                <el-tag type="info" size="small">{{ suggestion.category }}</el-tag>
              </div>
              <p class="suggestion-description">{{ suggestion.description }}</p>
              <div class="suggestion-benefits">
                <span class="benefits-label">预期收益:</span>
                <ul>
                  <li v-for="benefit in suggestion.benefits" :key="benefit">{{ benefit }}</li>
                </ul>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 项目清单对话框 -->
    <el-dialog
      v-model="projectListVisible"
      title="项目清单"
      width="80%"
      :before-close="closeProjectList"
    >
      <div v-loading="allProjectsLoading">
        <el-table
          :data="allProjects"
          style="width: 100%"
          @row-click="viewProjectDetail"
          class="clickable-table"
        >
          <el-table-column prop="feelec_project_id" label="项目ID" width="120" />
          <el-table-column prop="feelec_name" label="项目名称" min-width="200" />
          <el-table-column prop="manager_name" label="项目经理" width="120">
            <template #default="scope">
              {{ scope.row.manager_name || scope.row.feelec_manager_id || '未指定' }}
            </template>
          </el-table-column>
          <el-table-column prop="department_name" label="所属部门" width="120">
            <template #default="scope">
              {{ scope.row.department_name || scope.row.feelec_department_id || '未指定' }}
            </template>
          </el-table-column>
          <el-table-column prop="total_tickets" label="工单总数" width="100" />
          <el-table-column prop="completion_rate" label="完成率" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.completion_rate >= 80 ? 'success' : 'warning'">
                {{ scope.row.completion_rate }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="urgent_tickets" label="紧急工单" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.urgent_tickets > 0" type="danger">
                {{ scope.row.urgent_tickets }}
              </el-tag>
              <span v-else>0</span>
            </template>
          </el-table-column>
          <el-table-column prop="create_time_formatted" label="创建时间" width="160" />
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.is_completed ? 'success' : 'primary'">
                {{ scope.row.is_completed ? '已完成' : '进行中' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 项目工单列表对话框 -->
    <el-dialog
      v-model="projectTicketsVisible"
      title="项目工单列表"
      width="80%"
      :before-close="closeProjectTickets"
    >
      <div v-loading="projectTicketsLoading">
        <div v-if="selectedProjectForDetail" class="project-detail-header">
          <h3>{{ selectedProjectForDetail.feelec_name }}</h3>
          <p>项目ID: {{ selectedProjectForDetail.feelec_project_id }}</p>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="exportProjectTicketsToExcel" :icon="Download">导出Excel</el-button>
          </div>
        </div>

        <el-table
          :data="projectTickets"
          style="width: 100%"
          class="full-info-table"
          :scroll="{ x: 4300 }"
        >
          <!-- 序号列 -->
          <el-table-column type="index" label="序号" width="80" fixed="left" :index="(index) => index + 1" />

          <!-- 基础信息 -->
          <el-table-column prop="feelec_ticket_no" label="工单编号" width="120" fixed="left" />
          <el-table-column prop="feelec_title" label="工单标题" min-width="200" fixed="left" />

          <!-- 人员信息 -->
          <el-table-column prop="publisher_name" label="发布人" width="100">
            <template #default="scope">
              {{ scope.row.publisher_name || scope.row.feelec_publisher_id || '未知' }}
            </template>
          </el-table-column>
          <el-table-column prop="processor_name" label="处理人" width="100">
            <template #default="scope">
              {{ scope.row.processor_name || scope.row.feelec_processor_id || '未分配' }}
            </template>
          </el-table-column>

          <!-- 组织信息 -->
          <el-table-column prop="department_name" label="所属部门" width="120">
            <template #default="scope">
              {{ scope.row.department_name || scope.row.feelec_department_id || '未指定' }}
            </template>
          </el-table-column>
          <el-table-column prop="company_name" label="主体公司" width="120">
            <template #default="scope">
              {{ scope.row.company_name || scope.row.feelec_company_id || '未指定' }}
            </template>
          </el-table-column>

          <!-- 状态和优先级 -->
          <el-table-column prop="status_name" label="状态" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.is_completed ? 'success' : 'info'">
                {{ scope.row.status_name || (scope.row.is_completed ? '已完成' : '进行中') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority_text" label="优先级" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.priority_color">
                {{ scope.row.priority_text }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 其他重要信息 -->
          <el-table-column prop="template_name" label="工单模板" width="120">
            <template #default="scope">
              {{ scope.row.template_name || '默认模板' }}
            </template>
          </el-table-column>
          <el-table-column prop="source_text" label="工单来源" width="100">
            <template #default="scope">
              {{ scope.row.source_text || '未知' }}
            </template>
          </el-table-column>
          <el-table-column label="是否逾期" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.is_overdue ? 'danger' : 'success'">
                {{ scope.row.is_overdue ? `逾期${scope.row.overdue_days}天` : '正常' }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 时间信息 -->
          <el-table-column prop="create_time_formatted" label="创建时间" width="160" />
          <el-table-column prop="first_assign_time_formatted" label="首次分配时间" width="160" />
          <el-table-column prop="first_process_time_formatted" label="首次处理时间" width="160" />
          <el-table-column prop="complete_time_formatted" label="完成时间" width="160" />
          <el-table-column prop="deadline_formatted" label="截止时间" width="160" />
          <el-table-column prop="process_duration_text" label="处理时长" width="120" />

          <!-- 原始时间戳字段 -->
          <el-table-column prop="create_time" label="创建时间戳" width="120" />
          <el-table-column prop="first_assign_time" label="分配时间戳" width="120" />
          <el-table-column prop="first_process_time" label="处理时间戳" width="120" />
          <el-table-column prop="complete_time" label="完成时间戳" width="120" />
          <el-table-column prop="deadlines" label="截止时间戳" width="120" />

          <!-- 更多字段信息 -->
          <el-table-column prop="feelec_ticket_id" label="工单ID" width="100" />
          <el-table-column prop="feelec_project_id" label="项目ID" width="100" />
          <el-table-column prop="project_name" label="项目名称" width="150" />
          <el-table-column prop="feelec_publisher_id" label="发布人ID" width="100" />
          <el-table-column prop="feelec_processor_id" label="处理人ID" width="100" />
          <el-table-column prop="feelec_department_id" label="部门ID" width="100" />
          <el-table-column prop="feelec_company_id" label="公司ID" width="100" />
          <el-table-column prop="feelec_status_id" label="状态ID" width="100" />
          <el-table-column prop="feelec_template_id" label="模板ID" width="100" />
          <el-table-column prop="feelec_priority" label="优先级数值" width="120" />
          <el-table-column prop="feelec_source" label="来源数值" width="100" />
          <el-table-column prop="feelec_delete" label="删除标记" width="100" />

          <!-- 工单内容 -->
          <el-table-column prop="feelec_content" label="工单内容" min-width="300">
            <template #default="scope">
              <div class="content-preview">
                {{ scope.row.feelec_content || '暂无内容' }}
              </div>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="viewTicketDetail(scope.row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 工单详情对话框 -->
    <el-dialog
      v-model="ticketDetailVisible"
      title="工单详情"
      width="90%"
      :before-close="closeTicketDetail"
    >
      <div v-loading="ticketContentLoading">
        <div v-if="selectedTicketContent" class="ticket-full-content">
          <!-- 基本信息 -->
          <el-card class="content-section">
            <template #header>
              <h4>基本信息</h4>
            </template>
            <el-descriptions :column="2" border>
              <!-- 基础标识信息 -->
              <el-descriptions-item label="工单编号">{{ selectedTicketContent.feelec_ticket_no }}</el-descriptions-item>
              <el-descriptions-item label="工单标题">{{ selectedTicketContent.feelec_title }}</el-descriptions-item>
              <el-descriptions-item label="工单ID">{{ selectedTicketContent.feelec_ticket_id }}</el-descriptions-item>

              <!-- 关联信息 -->
              <el-descriptions-item label="项目名称">{{ selectedTicketContent.project_name || '无关联项目' }}</el-descriptions-item>
              <el-descriptions-item label="工单模板">{{ selectedTicketContent.template_name || '默认模板' }}</el-descriptions-item>

              <!-- 人员信息 -->
              <el-descriptions-item label="发布人">{{ selectedTicketContent.publisher_name || selectedTicketContent.feelec_publisher_id || '未知' }}</el-descriptions-item>
              <el-descriptions-item label="处理人">{{ selectedTicketContent.processor_name || selectedTicketContent.feelec_processor_id || '未分配' }}</el-descriptions-item>

              <!-- 组织信息 -->
              <el-descriptions-item label="所属部门">{{ selectedTicketContent.department_name || selectedTicketContent.feelec_department_id || '未指定' }}</el-descriptions-item>
              <el-descriptions-item label="主体公司">{{ selectedTicketContent.company_name || selectedTicketContent.feelec_company_id || '未指定' }}</el-descriptions-item>

              <!-- 状态和优先级 -->
              <el-descriptions-item label="当前状态">
                <el-tag :type="selectedTicketContent.is_completed ? 'success' : 'info'">
                  {{ selectedTicketContent.status_name || (selectedTicketContent.is_completed ? '已完成' : '进行中') }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="优先级">
                <el-tag :type="selectedTicketContent.priority_color">{{ selectedTicketContent.priority_text }}</el-tag>
              </el-descriptions-item>

              <!-- 其他信息 -->
              <el-descriptions-item label="工单来源">{{ selectedTicketContent.source_text || '未知' }}</el-descriptions-item>
              <el-descriptions-item label="是否逾期">
                <el-tag :type="selectedTicketContent.is_overdue ? 'danger' : 'success'">
                  {{ selectedTicketContent.is_overdue ? `逾期${selectedTicketContent.overdue_days}天` : '正常' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="删除状态">
                <el-tag :type="selectedTicketContent.feelec_delete === 20 ? 'success' : 'danger'">
                  {{ selectedTicketContent.feelec_delete === 20 ? '正常' : '已删除' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>

          <!-- 时间信息 -->
          <el-card class="content-section">
            <template #header>
              <h4>时间信息</h4>
            </template>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="创建时间">{{ selectedTicketContent.create_time_formatted }}</el-descriptions-item>
              <el-descriptions-item label="首次分配时间">{{ selectedTicketContent.first_assign_time_formatted }}</el-descriptions-item>
              <el-descriptions-item label="首次处理时间">{{ selectedTicketContent.first_process_time_formatted }}</el-descriptions-item>
              <el-descriptions-item label="完成时间">{{ selectedTicketContent.complete_time_formatted }}</el-descriptions-item>
              <el-descriptions-item label="截止时间">{{ selectedTicketContent.deadline_formatted }}</el-descriptions-item>
              <el-descriptions-item label="处理时长">{{ selectedTicketContent.process_duration_text }}</el-descriptions-item>
            </el-descriptions>
          </el-card>

          <!-- 工单详细字段 -->
          <el-card v-if="selectedTicketContent.detail_fields && Object.keys(selectedTicketContent.detail_fields).length > 0" class="content-section">
            <template #header>
              <h4>工单详细字段</h4>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item v-for="(value, fieldName) in selectedTicketContent.detail_fields" :key="fieldName" :label="fieldName">
                <div v-if="value && value.length > 100" class="long-content">
                  <div v-html="value" class="rich-content"></div>
                </div>
                <span v-else>{{ value || '无' }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>

          <!-- 需求来源信息 -->
          <el-card v-if="selectedTicketContent.publisher_info" class="content-section">
            <template #header>
              <h4>需求来源信息</h4>
            </template>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="发布人">{{ selectedTicketContent.publisher_info.feelec_name }}</el-descriptions-item>
              <el-descriptions-item label="联系电话">{{ selectedTicketContent.publisher_info.feelec_mobile || '无' }}</el-descriptions-item>
              <el-descriptions-item label="邮箱">{{ selectedTicketContent.publisher_info.feelec_email || '无' }}</el-descriptions-item>
              <el-descriptions-item label="所属公司" v-if="selectedTicketContent.publisher_info.company_info">
                {{ selectedTicketContent.publisher_info.company_info.company_name }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>

          <!-- 工单详细内容 -->
          <el-card class="content-section">
            <template #header>
              <h4>工单详细内容</h4>
            </template>
            <div v-if="selectedTicketContent.ticket_content" class="ticket-content-detail">
              <div v-if="selectedTicketContent.ticket_content.title" class="content-item">
                <h5>标题:</h5>
                <p>{{ selectedTicketContent.ticket_content.title }}</p>
              </div>
              <div v-if="selectedTicketContent.ticket_content.content" class="content-item">
                <h5>详细内容:</h5>
                <div v-html="selectedTicketContent.ticket_content.content" class="rich-content"></div>
              </div>
              <!-- 显示其他自定义字段 -->
              <div v-for="(value, key) in selectedTicketContent.ticket_content" :key="key" class="content-item">
                <div v-if="key !== 'title' && key !== 'content' && value">
                  <h5>{{ key }}:</h5>
                  <p>{{ value }}</p>
                </div>
              </div>
            </div>
            <div v-else class="ticket-content-text">
              {{ selectedTicketContent.feelec_content || '暂无详细内容' }}
            </div>
          </el-card>

          <!-- 处理记录 -->
          <el-card v-if="selectedTicketContent.process_records && selectedTicketContent.process_records.length > 0" class="content-section">
            <template #header>
              <h4>处理记录</h4>
            </template>
            <el-timeline>
              <el-timeline-item
                v-for="record in selectedTicketContent.process_records"
                :key="record.id"
                :timestamp="record.create_time_formatted"
                placement="top"
              >
                <el-card>
                  <h4>{{ record.action_name }}</h4>
                  <p>处理人: {{ record.processor_name || record.processor_id }}</p>
                  <p v-if="record.content">{{ record.content }}</p>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </el-card>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeTicketDetail">关闭</el-button>
          <el-button type="primary" @click="exportTicketToExcel" :icon="Download">导出Excel</el-button>
        </span>
      </template>
    </el-dialog>



    <!-- 按状态查看工单对话框已删除 - 现在复用项目清单页面 -->

    <!-- 用户列表对话框 -->
    <el-dialog
      v-model="usersListVisible"
      title="人员列表"
      width="80%"
      top="5vh"
    >
      <el-table :data="usersList" style="width: 100%" max-height="500">
        <el-table-column prop="feelec_name" label="姓名" width="120" />
        <el-table-column prop="feelec_mobile" label="手机号" width="130" />
        <el-table-column prop="feelec_email" label="邮箱" min-width="180" />
        <el-table-column prop="company_name" label="所属公司" width="150" />
        <el-table-column prop="total_tickets" label="工单总数" width="100" />
        <el-table-column prop="completed_tickets" label="已完成" width="100" />
        <el-table-column prop="completion_rate" label="完成率" width="100">
          <template #default="scope">
            {{ scope.row.completion_rate }}%
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewUserTickets(scope.row)">
              查看工单
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 主体列表对话框 -->
    <el-dialog
      v-model="companiesListVisible"
      title="主体列表"
      width="80%"
      top="5vh"
    >
      <el-table :data="companiesList" style="width: 100%" max-height="500">
        <el-table-column prop="feelec_name" label="主体名称" min-width="200" />
        <el-table-column prop="feelec_contact" label="联系人" width="120" />
        <el-table-column prop="feelec_phone" label="联系电话" width="130" />
        <el-table-column prop="total_tickets" label="工单总数" width="100" />
        <el-table-column prop="completed_tickets" label="已完成" width="100" />
        <el-table-column prop="completion_rate" label="完成率" width="100">
          <template #default="scope">
            {{ scope.row.completion_rate }}%
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewCompanyTickets(scope.row)">
              查看工单
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 用户工单对话框 -->
    <el-dialog
      v-model="userTicketsVisible"
      :title="`${selectedUser?.feelec_name || ''} 的工单列表`"
      width="80%"
      top="5vh"
    >
      <el-table :data="userTickets" style="width: 100%" max-height="500">
        <el-table-column prop="feelec_ticket_no" label="工单编号" width="120" />
        <el-table-column prop="feelec_title" label="工单标题" min-width="200" />
        <el-table-column prop="priority_text" label="优先级" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.priority_color" size="small">
              {{ scope.row.priority_text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status_name" label="状态" width="100" />
        <el-table-column prop="create_time_formatted" label="创建时间" width="150" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewTicketContent(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 主体工单对话框 -->
    <el-dialog
      v-model="companyTicketsVisible"
      :title="`${selectedCompany?.feelec_name || ''} 的工单列表`"
      width="80%"
      top="5vh"
    >
      <el-table :data="companyTickets" style="width: 100%" max-height="500">
        <el-table-column prop="feelec_ticket_no" label="工单编号" width="120" />
        <el-table-column prop="feelec_title" label="工单标题" min-width="200" />
        <el-table-column prop="priority_text" label="优先级" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.priority_color" size="small">
              {{ scope.row.priority_text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status_name" label="状态" width="100" />
        <el-table-column prop="publisher_name" label="发布人" width="100" />
        <el-table-column prop="processor_name" label="处理人" width="100" />
        <el-table-column prop="create_time_formatted" label="创建时间" width="150" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewTicketContent(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 工单列表对话框 -->
    <el-dialog
      v-model="ticketListVisible"
      :title="getTicketListTitle()"
      width="95%"
      :before-close="closeTicketList"
      top="2vh"
    >
      <div v-loading="ticketsListLoading">
        <div class="ticket-list-header">
          <div class="header-actions">
            <el-button type="primary" size="small" @click="exportTicketsListToExcel" :icon="Download">导出Excel</el-button>
          </div>
        </div>

        <el-table
          :data="ticketsList"
          style="width: 100%"
          class="full-info-table"
          :scroll="{ x: 4300 }"
          max-height="70vh"
        >
          <!-- 序号列 -->
          <el-table-column type="index" label="序号" width="80" fixed="left" :index="(index) => index + 1" />

          <!-- 基础信息 -->
          <el-table-column prop="feelec_ticket_no" label="工单编号" width="120" fixed="left" />
          <el-table-column prop="feelec_title" label="工单标题" min-width="200" fixed="left" />

          <!-- 人员信息 -->
          <el-table-column prop="publisher_name" label="发布人" width="100">
            <template #default="scope">
              {{ scope.row.publisher_name || scope.row.feelec_publisher_id || '未知' }}
            </template>
          </el-table-column>
          <el-table-column prop="processor_name" label="处理人" width="100">
            <template #default="scope">
              {{ scope.row.processor_name || scope.row.feelec_processor_id || '未分配' }}
            </template>
          </el-table-column>

          <!-- 组织信息 -->
          <el-table-column prop="department_name" label="所属部门" width="120">
            <template #default="scope">
              {{ scope.row.department_name || scope.row.feelec_department_id || '未指定' }}
            </template>
          </el-table-column>
          <el-table-column prop="company_name" label="主体公司" width="120">
            <template #default="scope">
              {{ scope.row.company_name || scope.row.feelec_company_id || '未指定' }}
            </template>
          </el-table-column>

          <!-- 状态和优先级 -->
          <el-table-column prop="status_name" label="状态" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.is_completed ? 'success' : 'info'">
                {{ scope.row.status_name || (scope.row.is_completed ? '已完成' : '进行中') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority_text" label="优先级" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.priority_color">
                {{ scope.row.priority_text }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 其他重要信息 -->
          <el-table-column prop="template_name" label="工单模板" width="120">
            <template #default="scope">
              {{ scope.row.template_name || '默认模板' }}
            </template>
          </el-table-column>
          <el-table-column prop="source_text" label="工单来源" width="100">
            <template #default="scope">
              {{ scope.row.source_text || '未知' }}
            </template>
          </el-table-column>
          <el-table-column label="是否逾期" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.is_overdue ? 'danger' : 'success'">
                {{ scope.row.is_overdue ? `逾期${scope.row.overdue_days}天` : '正常' }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 时间信息 -->
          <el-table-column prop="create_time_formatted" label="创建时间" width="160" />
          <el-table-column prop="first_assign_time_formatted" label="首次分配时间" width="160" />
          <el-table-column prop="first_process_time_formatted" label="首次处理时间" width="160" />
          <el-table-column prop="complete_time_formatted" label="完成时间" width="160" />
          <el-table-column prop="deadline_formatted" label="截止时间" width="160" />
          <el-table-column prop="process_duration_text" label="处理时长" width="120" />

          <!-- 原始时间戳字段 -->
          <el-table-column prop="create_time" label="创建时间戳" width="120" />
          <el-table-column prop="first_assign_time" label="分配时间戳" width="120" />
          <el-table-column prop="first_process_time" label="处理时间戳" width="120" />
          <el-table-column prop="complete_time" label="完成时间戳" width="120" />
          <el-table-column prop="deadlines" label="截止时间戳" width="120" />

          <!-- 更多字段信息 -->
          <el-table-column prop="feelec_ticket_id" label="工单ID" width="100" />
          <el-table-column prop="feelec_project_id" label="项目ID" width="100" />
          <el-table-column prop="project_name" label="项目名称" width="150" />
          <el-table-column prop="feelec_publisher_id" label="发布人ID" width="100" />
          <el-table-column prop="feelec_processor_id" label="处理人ID" width="100" />
          <el-table-column prop="feelec_department_id" label="部门ID" width="100" />
          <el-table-column prop="feelec_company_id" label="公司ID" width="100" />
          <el-table-column prop="feelec_status_id" label="状态ID" width="100" />
          <el-table-column prop="feelec_template_id" label="模板ID" width="100" />
          <el-table-column prop="feelec_priority" label="优先级数值" width="120" />
          <el-table-column prop="feelec_source" label="来源数值" width="100" />
          <el-table-column prop="feelec_delete" label="删除标记" width="100" />

          <!-- 工单内容 -->
          <el-table-column prop="feelec_content" label="工单内容" min-width="300">
            <template #default="scope">
              <div class="content-preview">
                {{ scope.row.feelec_content || '暂无内容' }}
              </div>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="viewTicketDetail(scope.row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="ticketsList.length === 0 && !ticketsListLoading" style="text-align: center; padding: 40px; color: #999;">
          暂无工单数据
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Folder,
  Document,
  CircleCheck,
  Warning,
  Refresh,
  User,
  OfficeBuilding,
  Download
} from '@element-plus/icons-vue'
import { saveAs } from 'file-saver'
import * as XLSX from 'xlsx'
import {
  getTicketProjects,
  getProjectTickets,
  getWorkloadAnalysis,
  getDashboardStats,
  getIntegrationSuggestions,
  getTicketFullContent,
  getTicketsByStatus,
  getUsersList,
  getCompaniesList,
  getUserTickets,
  getCompanyTickets
} from '@/api/ticketIntegration'

export default {
  name: 'TicketIntegration',
  components: {
    Folder,
    Document,
    CircleCheck,
    Warning,
    Refresh,
    User,
    OfficeBuilding,
    Download
  },
  setup() {
    // 响应式数据
    const dashboardStats = reactive({})
    const projects = ref([])
    const selectedProject = ref(null)
    const tickets = ref([])
    const workloadStats = reactive({})
    const suggestions = ref([])

    // 新增对话框相关数据
    const projectListVisible = ref(false)
    const projectTicketsVisible = ref(false)
    const ticketDetailVisible = ref(false)
    const ticketListVisible = ref(false)  // 工单列表对话框
    const allProjects = ref([])
    const selectedProjectForDetail = ref(null)
    const projectTickets = ref([])
    const selectedTicketContent = ref(null)
    const ticketsList = ref([])  // 按状态获取的工单列表
    const currentTicketStatus = ref('')  // 当前查看的工单状态

    // 新增功能相关数据
    const usersListVisible = ref(false)
    const companiesListVisible = ref(false)
    const userTicketsVisible = ref(false)
    const companyTicketsVisible = ref(false)

    const usersList = ref([])
    const companiesList = ref([])
    const userTickets = ref([])
    const companyTickets = ref([])

    const selectedUser = ref(null)
    const selectedCompany = ref(null)

    // 加载状态
    const projectsLoading = ref(false)
    const ticketsLoading = ref(false)
    const workloadLoading = ref(false)
    const suggestionsLoading = ref(false)
    const allProjectsLoading = ref(false)
    const projectTicketsLoading = ref(false)
    const ticketContentLoading = ref(false)
    const ticketsListLoading = ref(false)  // 工单列表加载状态

    // 获取仪表盘统计
    const fetchDashboardStats = async () => {
      try {
        const response = await getDashboardStats()
        if (response.success) {
          Object.assign(dashboardStats, response.data)
        }
      } catch (error) {
        console.error('获取仪表盘统计失败:', error)
      }
    }

    // 获取项目列表
    const fetchProjects = async () => {
      projectsLoading.value = true
      try {
        const response = await getTicketProjects({ limit: 20 })
        if (response.success) {
          projects.value = response.data.projects || []
        } else {
          ElMessage.error(response.message || '获取项目列表失败')
        }
      } catch (error) {
        console.error('获取项目列表失败:', error)
        ElMessage.error('获取项目列表失败')
      } finally {
        projectsLoading.value = false
      }
    }

    // 选择项目
    const selectProject = async (project) => {
      selectedProject.value = project
      await fetchTickets(project.feelec_project_id)
    }

    // 获取项目工单
    const fetchTickets = async (projectId) => {
      ticketsLoading.value = true
      try {
        const response = await getProjectTickets(projectId)
        if (response.success) {
          tickets.value = response.data.tickets || []
        } else {
          ElMessage.error(response.message || '获取工单列表失败')
        }
      } catch (error) {
        console.error('获取工单列表失败:', error)
        ElMessage.error('获取工单列表失败')
      } finally {
        ticketsLoading.value = false
      }
    }

    // 获取工作负荷分析
    const fetchWorkloadAnalysis = async () => {
      workloadLoading.value = true
      try {
        const response = await getWorkloadAnalysis({ days: 30 })
        if (response.success) {
          Object.assign(workloadStats, response.data)
        } else {
          ElMessage.error(response.message || '获取工作负荷分析失败')
        }
      } catch (error) {
        console.error('获取工作负荷分析失败:', error)
        ElMessage.error('获取工作负荷分析失败')
      } finally {
        workloadLoading.value = false
      }
    }

    // 获取集成建议
    const fetchSuggestions = async () => {
      suggestionsLoading.value = true
      try {
        const response = await getIntegrationSuggestions()
        if (response.success) {
          suggestions.value = response.data.suggestions || []
        } else {
          ElMessage.error(response.message || '获取集成建议失败')
        }
      } catch (error) {
        console.error('获取集成建议失败:', error)
        ElMessage.error('获取集成建议失败')
      } finally {
        suggestionsLoading.value = false
      }
    }

    // 显示项目清单
    const showProjectList = async () => {
      projectListVisible.value = true
      await fetchAllProjects()
    }

    // 获取所有项目
    const fetchAllProjects = async () => {
      allProjectsLoading.value = true
      try {
        const response = await getTicketProjects({ limit: 100 })
        if (response.success) {
          allProjects.value = response.data.projects || []
        } else {
          ElMessage.error(response.message || '获取项目清单失败')
        }
      } catch (error) {
        console.error('获取项目清单失败:', error)
        ElMessage.error('获取项目清单失败')
      } finally {
        allProjectsLoading.value = false
      }
    }

    // 查看项目详情
    const viewProjectDetail = async (project) => {
      selectedProjectForDetail.value = project
      projectTicketsVisible.value = true
      await fetchProjectTickets(project.feelec_project_id)
    }

    // 获取项目工单详情
    const fetchProjectTickets = async (projectId) => {
      projectTicketsLoading.value = true
      try {
        const response = await getProjectTickets(projectId)
        if (response.success) {
          projectTickets.value = response.data.tickets || []
        } else {
          ElMessage.error(response.message || '获取项目工单失败')
        }
      } catch (error) {
        console.error('获取项目工单失败:', error)
        ElMessage.error('获取项目工单失败')
      } finally {
        projectTicketsLoading.value = false
      }
    }

    // 查看工单详情
    const viewTicketDetail = async (ticket) => {
      ticketDetailVisible.value = true
      await fetchTicketFullContent(ticket.feelec_ticket_id)
    }

    // 兼容旧方法名
    const viewTicketContent = viewTicketDetail

    // 获取工单完整内容
    const fetchTicketFullContent = async (ticketId) => {
      ticketContentLoading.value = true
      try {
        const response = await getTicketFullContent(ticketId)
        if (response.success) {
          selectedTicketContent.value = response.data
        } else {
          ElMessage.error(response.message || '获取工单详情失败')
        }
      } catch (error) {
        console.error('获取工单详情失败:', error)
        ElMessage.error('获取工单详情失败')
      } finally {
        ticketContentLoading.value = false
      }
    }

    // 关闭对话框方法
    const closeProjectList = () => {
      projectListVisible.value = false
      allProjects.value = []
    }

    const closeProjectTickets = () => {
      projectTicketsVisible.value = false
      selectedProjectForDetail.value = null
      projectTickets.value = []
    }

    const closeTicketDetail = () => {
      ticketDetailVisible.value = false
      selectedTicketContent.value = null
    }

    const closeTicketList = () => {
      ticketListVisible.value = false
      ticketsList.value = []
      currentTicketStatus.value = ''
    }

    // 获取工单列表标题
    const getTicketListTitle = () => {
      switch(currentTicketStatus.value) {
        case 'all':
          return `全部工单 (${ticketsList.value.length})`
        case 'completed':
          return `已完成工单 (${ticketsList.value.length})`
        case 'urgent':
          return `紧急工单 (${ticketsList.value.length})`
        default:
          return `工单列表 (${ticketsList.value.length})`
      }
    }

    // Excel导出功能
    const exportTicketToExcel = () => {
      try {
        if (!selectedTicketContent.value) {
          ElMessage.warning('没有可导出的工单数据')
          return
        }

        const ticket = selectedTicketContent.value

        // 准备基础数据
        const basicData = {
          '工单编号': ticket.feelec_ticket_no,
          '工单标题': ticket.feelec_title,
          '工单ID': ticket.feelec_ticket_id,
          '项目名称': ticket.project_name || '无关联项目',
          '工单模板': ticket.template_name || '默认模板',
          '发布人': ticket.publisher_name || ticket.feelec_publisher_id || '未知',
          '处理人': ticket.processor_name || ticket.feelec_processor_id || '未分配',
          '所属部门': ticket.department_name || ticket.feelec_department_id || '未指定',
          '主体公司': ticket.company_name || ticket.feelec_company_id || '未指定',
          '当前状态': ticket.status_name || (ticket.is_completed ? '已完成' : '进行中'),
          '优先级': ticket.priority_text,
          '工单来源': ticket.source_text || '未知',
          '是否逾期': ticket.is_overdue ? `逾期${ticket.overdue_days}天` : '正常',
          '删除状态': ticket.feelec_delete === 20 ? '正常' : '已删除',
          '创建时间': ticket.create_time_formatted,
          '首次分配时间': ticket.first_assign_time_formatted,
          '首次处理时间': ticket.first_process_time_formatted,
          '完成时间': ticket.complete_time_formatted,
          '截止时间': ticket.deadline_formatted,
          '处理时长': ticket.process_duration_text
        }

        // 添加详细字段
        if (ticket.detail_fields && Object.keys(ticket.detail_fields).length > 0) {
          Object.keys(ticket.detail_fields).forEach(fieldName => {
            basicData[`详细字段-${fieldName}`] = ticket.detail_fields[fieldName] || '无'
          })
        }

        // 创建工作簿
        const worksheet = XLSX.utils.json_to_sheet([basicData])
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, '工单详情')

        // 如果有处理记录，添加到第二个工作表
        if (ticket.process_records && ticket.process_records.length > 0) {
          const processData = ticket.process_records.map(record => ({
            '处理时间': record.create_time_formatted,
            '处理动作': record.action_name,
            '处理人': record.processor_name || record.processor_id,
            '处理内容': record.content || '无'
          }))
          const processWorksheet = XLSX.utils.json_to_sheet(processData)
          XLSX.utils.book_append_sheet(workbook, processWorksheet, '处理记录')
        }

        // 导出文件
        const fileName = `工单详情-${ticket.feelec_ticket_no}-${new Date().toISOString().split('T')[0]}.xlsx`
        XLSX.writeFile(workbook, fileName)

        ElMessage.success('导出成功')
      } catch (error) {
        console.error('导出Excel失败:', error)
        ElMessage.error('导出Excel失败，请稍后重试')
      }
    }

    const exportProjectTicketsToExcel = () => {
      try {
        if (!projectTickets.value || projectTickets.value.length === 0) {
          ElMessage.warning('没有可导出的工单数据')
          return
        }

        // 准备导出数据 - 包含所有32个字段
        const exportData = projectTickets.value.map((ticket, index) => ({
          '序号': index + 1,
          '工单编号': ticket.feelec_ticket_no,
          '工单标题': ticket.feelec_title,
          '工单ID': ticket.feelec_ticket_id,
          '项目ID': ticket.feelec_project_id,
          '项目名称': ticket.project_name,
          '发布人': ticket.publisher_name || '未知',
          '发布人ID': ticket.feelec_publisher_id,
          '处理人': ticket.processor_name || '未分配',
          '处理人ID': ticket.feelec_processor_id,
          '所属部门': ticket.department_name || '未指定',
          '部门ID': ticket.feelec_department_id,
          '主体公司': ticket.company_name || '未指定',
          '公司ID': ticket.feelec_company_id,
          '状态': ticket.status_name || (ticket.is_completed ? '已完成' : '进行中'),
          '状态ID': ticket.feelec_status_id,
          '优先级': ticket.priority_text,
          '优先级数值': ticket.feelec_priority,
          '是否逾期': ticket.is_overdue ? `逾期${ticket.overdue_days}天` : '正常',
          '工单模板': ticket.template_name || '默认模板',
          '模板ID': ticket.feelec_template_id,
          '工单来源': ticket.source_text || '未知',
          '来源数值': ticket.feelec_source,
          '创建时间': ticket.create_time_formatted,
          '首次分配时间': ticket.first_assign_time_formatted,
          '首次处理时间': ticket.first_process_time_formatted,
          '完成时间': ticket.complete_time_formatted,
          '截止时间': ticket.deadline_formatted,
          '处理时长': ticket.process_duration_text,
          '创建时间戳': ticket.create_time,
          '分配时间戳': ticket.first_assign_time,
          '处理时间戳': ticket.first_process_time,
          '完成时间戳': ticket.complete_time,
          '截止时间戳': ticket.deadlines,
          '删除标记': ticket.feelec_delete,
          '工单内容': ticket.feelec_content || '暂无内容'
        }))

        // 创建工作簿
        const worksheet = XLSX.utils.json_to_sheet(exportData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, '项目工单列表')

        // 导出文件
        const projectName = selectedProjectForDetail.value?.feelec_name || '项目'
        const fileName = `${projectName}-工单列表-${new Date().toISOString().split('T')[0]}.xlsx`
        XLSX.writeFile(workbook, fileName)

        ElMessage.success('导出成功')
      } catch (error) {
        console.error('导出Excel失败:', error)
        ElMessage.error('导出Excel失败，请稍后重试')
      }
    }

    // 导出工单列表到Excel
    const exportTicketsListToExcel = () => {
      try {
        if (!ticketsList.value || ticketsList.value.length === 0) {
          ElMessage.warning('没有可导出的工单数据')
          return
        }

        // 准备导出数据 - 包含所有32个字段，和项目工单明细完全一样
        const exportData = ticketsList.value.map((ticket, index) => ({
          '序号': index + 1,
          '工单编号': ticket.feelec_ticket_no,
          '工单标题': ticket.feelec_title,
          '工单ID': ticket.feelec_ticket_id,
          '项目ID': ticket.feelec_project_id,
          '项目名称': ticket.project_name,
          '发布人': ticket.publisher_name || '未知',
          '发布人ID': ticket.feelec_publisher_id,
          '处理人': ticket.processor_name || '未分配',
          '处理人ID': ticket.feelec_processor_id,
          '所属部门': ticket.department_name || '未指定',
          '部门ID': ticket.feelec_department_id,
          '主体公司': ticket.company_name || '未指定',
          '公司ID': ticket.feelec_company_id,
          '状态': ticket.status_name || (ticket.is_completed ? '已完成' : '进行中'),
          '状态ID': ticket.feelec_status_id,
          '优先级': ticket.priority_text,
          '优先级数值': ticket.feelec_priority,
          '是否逾期': ticket.is_overdue ? `逾期${ticket.overdue_days}天` : '正常',
          '工单模板': ticket.template_name || '默认模板',
          '模板ID': ticket.feelec_template_id,
          '工单来源': ticket.source_text || '未知',
          '来源数值': ticket.feelec_source,
          '创建时间': ticket.create_time_formatted,
          '首次分配时间': ticket.first_assign_time_formatted,
          '首次处理时间': ticket.first_process_time_formatted,
          '完成时间': ticket.complete_time_formatted,
          '截止时间': ticket.deadline_formatted,
          '处理时长': ticket.process_duration_text,
          '创建时间戳': ticket.create_time,
          '分配时间戳': ticket.first_assign_time,
          '处理时间戳': ticket.first_process_time,
          '完成时间戳': ticket.complete_time,
          '截止时间戳': ticket.deadlines,
          '删除标记': ticket.feelec_delete,
          '工单内容': ticket.feelec_content || '暂无内容'
        }))

        // 创建工作簿
        const worksheet = XLSX.utils.json_to_sheet(exportData)
        const workbook = XLSX.utils.book_new()

        // 根据状态设置工作表名称
        let sheetName = '工单列表'
        switch(currentTicketStatus.value) {
          case 'all':
            sheetName = '全部工单'
            break
          case 'completed':
            sheetName = '已完成工单'
            break
          case 'urgent':
            sheetName = '紧急工单'
            break
        }

        XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)

        // 导出文件
        const fileName = `${sheetName}-${new Date().toISOString().split('T')[0]}.xlsx`
        XLSX.writeFile(workbook, fileName)

        ElMessage.success('导出成功')
      } catch (error) {
        console.error('导出Excel失败:', error)
        ElMessage.error('导出Excel失败，请稍后重试')
      }
    }

    // 刷新方法
    const refreshProjects = () => {
      fetchProjects()
      fetchDashboardStats()
    }

    // 按状态查看工单 - 直接显示工单列表
    const showTicketsByStatus = async (status) => {
      try {
        currentTicketStatus.value = status
        ticketListVisible.value = true
        await fetchTicketsByStatus(status)

        // 根据状态设置提示信息
        let message = ''
        switch(status) {
          case 'all':
            message = '正在加载所有工单...'
            break
          case 'completed':
            message = '正在加载已完成工单...'
            break
          case 'urgent':
            message = '正在加载紧急工单...'
            break
          default:
            message = '正在加载工单列表...'
        }

        ElMessage.success(message)

      } catch (error) {
        console.error('获取工单列表失败:', error)
        ElMessage.error('获取工单列表失败，请稍后重试')
      }
    }

    // 按状态获取工单列表
    const fetchTicketsByStatus = async (status) => {
      ticketsListLoading.value = true
      try {
        const response = await getTicketsByStatus(status, 999999)
        if (response.success) {
          ticketsList.value = response.data.tickets || []
        } else {
          ElMessage.error(response.message || '获取工单列表失败')
        }
      } catch (error) {
        console.error('获取工单列表失败:', error)
        ElMessage.error('获取工单列表失败')
      } finally {
        ticketsListLoading.value = false
      }
    }

    // 显示用户列表
    const showUsersList = async () => {
      usersListVisible.value = true
      await fetchUsersList()
    }

    const fetchUsersList = async () => {
      try {
        const response = await getUsersList(50)
        if (response.success) {
          usersList.value = response.data.users || []
        } else {
          ElMessage.error(response.message || '获取用户列表失败')
        }
      } catch (error) {
        console.error('获取用户列表失败:', error)
        ElMessage.error('获取用户列表失败')
      }
    }

    // 显示主体列表
    const showCompaniesList = async () => {
      companiesListVisible.value = true
      await fetchCompaniesList()
    }

    const fetchCompaniesList = async () => {
      try {
        const response = await getCompaniesList(50)
        if (response.success) {
          companiesList.value = response.data.companies || []
        } else {
          ElMessage.error(response.message || '获取主体列表失败')
        }
      } catch (error) {
        console.error('获取主体列表失败:', error)
        ElMessage.error('获取主体列表失败')
      }
    }

    // 查看用户工单
    const viewUserTickets = async (user) => {
      selectedUser.value = user
      userTicketsVisible.value = true
      await fetchUserTickets(user.feelec_user_id)
    }

    const fetchUserTickets = async (userId) => {
      try {
        const response = await getUserTickets(userId)
        if (response.success) {
          userTickets.value = response.data.tickets || []
        } else {
          ElMessage.error(response.message || '获取用户工单失败')
        }
      } catch (error) {
        console.error('获取用户工单失败:', error)
        ElMessage.error('获取用户工单失败')
      }
    }

    // 查看主体工单
    const viewCompanyTickets = async (company) => {
      selectedCompany.value = company
      companyTicketsVisible.value = true
      await fetchCompanyTickets(company.feelec_company_id)
    }

    const fetchCompanyTickets = async (companyId) => {
      try {
        const response = await getCompanyTickets(companyId)
        if (response.success) {
          companyTickets.value = response.data.tickets || []
        } else {
          ElMessage.error(response.message || '获取主体工单失败')
        }
      } catch (error) {
        console.error('获取主体工单失败:', error)
        ElMessage.error('获取主体工单失败')
      }
    }

    const refreshTickets = () => {
      if (selectedProject.value) {
        fetchTickets(selectedProject.value.feelec_project_id)
      }
    }

    const refreshWorkload = () => {
      fetchWorkloadAnalysis()
    }

    const refreshSuggestions = () => {
      fetchSuggestions()
    }

    // 初始化
    onMounted(() => {
      fetchDashboardStats()
      fetchProjects()
      fetchWorkloadAnalysis()
      fetchSuggestions()
    })

    return {
      // 数据
      dashboardStats,
      projects,
      selectedProject,
      tickets,
      workloadStats,
      suggestions,

      // 对话框相关数据
      projectListVisible,
      projectTicketsVisible,
      ticketDetailVisible,
      ticketListVisible,
      allProjects,
      selectedProjectForDetail,
      projectTickets,
      selectedTicketContent,
      ticketsList,
      currentTicketStatus,

      // 新增对话框数据
      usersListVisible,
      companiesListVisible,
      userTicketsVisible,
      companyTicketsVisible,
      usersList,
      companiesList,
      userTickets,
      companyTickets,
      selectedUser,
      selectedCompany,

      // 加载状态
      projectsLoading,
      ticketsLoading,
      workloadLoading,
      suggestionsLoading,
      allProjectsLoading,
      projectTicketsLoading,
      ticketContentLoading,
      ticketsListLoading,

      // 方法
      selectProject,
      showProjectList,
      viewProjectDetail,
      viewTicketDetail,
      viewTicketContent,
      closeProjectList,
      closeProjectTickets,
      closeTicketDetail,
      closeTicketList,
      getTicketListTitle,
      fetchTicketsByStatus,
      refreshProjects,
      refreshTickets,
      refreshWorkload,
      refreshSuggestions,

      // Excel导出方法
      exportTicketToExcel,
      exportProjectTicketsToExcel,
      exportTicketsListToExcel,

      // 新增方法
      showTicketsByStatus,
      showUsersList,
      showCompaniesList,
      viewUserTickets,
      viewCompanyTickets
    }
  }
}
</script>

<style scoped>
.ticket-integration {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 20px;
}

/* 快捷操作区域 */
.quick-actions {
  margin-bottom: 20px;
}

.action-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-card.clickable {
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.project-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.ticket-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.completed-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.urgent-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 内容卡片 */
.main-content,
.bottom-content {
  margin-bottom: 20px;
}

.content-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 500px;
}

.content-card .el-card__body {
  height: calc(100% - 60px);
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

/* 项目列表 */
.projects-list {
  height: 100%;
  overflow-y: auto;
}

.project-item {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.project-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.project-item.active {
  border-color: #409eff;
  background: #f0f9ff;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.project-title-section {
  display: flex;
  align-items: center;
  flex: 1;
}

.project-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  margin-right: 8px;
  flex-shrink: 0;
}

.project-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.project-info {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-item .label {
  color: #909399;
  font-size: 12px;
}

.info-item .value {
  color: #409eff;
  font-weight: 500;
  font-size: 12px;
}

.project-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-item .label {
  color: #909399;
  font-size: 12px;
}

.stat-item .value {
  color: #303133;
  font-weight: 500;
  font-size: 12px;
}

.stat-item .value.urgent {
  color: #f56c6c;
}

.project-time {
  color: #c0c4cc;
  font-size: 12px;
}

/* 工单列表 */
.tickets-list {
  height: 100%;
  overflow-y: auto;
}

.ticket-item {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 12px;
  background: white;
}

.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.ticket-header h5 {
  margin: 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  flex: 1;
  margin-right: 12px;
}

.ticket-badges {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.ticket-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-row .label {
  color: #909399;
  font-size: 12px;
  min-width: 60px;
}

.info-row .value {
  color: #303133;
  font-size: 12px;
  font-weight: 500;
}

/* 工作负荷分析 */
.workload-analysis {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.workload-summary {
  display: flex;
  gap: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-item .label {
  color: #909399;
  font-size: 12px;
}

.summary-item .value {
  color: #303133;
  font-weight: 600;
  font-size: 12px;
}

.workload-list {
  flex: 1;
  overflow-y: auto;
}

.workload-item {
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  margin-bottom: 8px;
  background: white;
}

.processor-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.processor-id {
  font-weight: 500;
  color: #303133;
  font-size: 13px;
}

.workload-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #606266;
}

.workload-stats .urgent {
  color: #f56c6c;
}

/* 集成建议 */
.suggestions-list {
  height: 100%;
  overflow-y: auto;
}

.suggestion-item {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 12px;
  background: white;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.suggestion-header h5 {
  margin: 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.suggestion-category {
  margin-bottom: 8px;
}

.suggestion-description {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 13px;
  line-height: 1.5;
}

.suggestion-benefits {
  font-size: 12px;
}

.benefits-label {
  color: #909399;
  font-weight: 500;
}

.suggestion-benefits ul {
  margin: 4px 0 0 0;
  padding-left: 16px;
  color: #606266;
}

.suggestion-benefits li {
  margin-bottom: 2px;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

/* 可点击表格 */
.clickable-table .el-table__row {
  cursor: pointer;
}

.clickable-table .el-table__row:hover {
  background-color: #f5f7fa;
}

/* 完整信息表格 */
.full-info-table {
  font-size: 12px;
}

.full-info-table .el-table__cell {
  padding: 8px 0;
}

.content-preview {
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-height: 1.4;
  word-break: break-all;
}

/* 项目详情头部 */
.project-detail-header {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.project-detail-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.project-detail-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 工单列表头部 */
.ticket-list-header {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ticket-list-header .header-actions {
  display: flex;
  gap: 10px;
}

/* 工单完整内容样式 */
.ticket-full-content {
  max-height: 70vh;
  overflow-y: auto;
}

.content-section {
  margin-bottom: 20px;
}

.content-section:last-child {
  margin-bottom: 0;
}

.content-section .el-card__header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.ticket-content-text {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-word;
  min-height: 60px;
}

/* 对话框样式优化 */
.el-dialog__body {
  max-height: 70vh;
  overflow-y: auto;
}

/* 描述列表样式 */
.el-descriptions {
  margin-bottom: 0;
}

/* 时间轴样式 */
.el-timeline {
  padding-left: 0;
}

.el-timeline-item__content .el-card {
  margin-top: 0;
}

.el-timeline-item__content .el-card .el-card__body {
  padding: 12px 16px;
}

.el-timeline-item__content h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.el-timeline-item__content p {
  margin: 4px 0;
  color: #606266;
  font-size: 13px;
}

/* 工单详细内容样式 */
.ticket-content-detail {
  padding: 20px;
}

.content-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.content-item h5 {
  margin: 0 0 10px 0;
  color: #409eff;
  font-weight: 600;
  font-size: 14px;
}

.content-item p {
  margin: 0;
  line-height: 1.6;
  color: #606266;
}

.rich-content {
  line-height: 1.6;
  color: #606266;
}

.rich-content p {
  margin: 10px 0;
}

.rich-content h1, .rich-content h2, .rich-content h3, .rich-content h4, .rich-content h5, .rich-content h6 {
  margin: 15px 0 10px 0;
  color: #303133;
}

.rich-content ul, .rich-content ol {
  margin: 10px 0;
  padding-left: 20px;
}

.rich-content li {
  margin: 5px 0;
}
</style>
