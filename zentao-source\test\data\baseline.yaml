title: table zt_baseline
desc: ""
author: automated export
version: "1.0"
fields:
  - field: id
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: objectType
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: version
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: versionType
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: title
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: objectData
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedTo
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdDate
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deadline
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: result
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
