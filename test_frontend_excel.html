<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>督办管理Excel功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .test-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #66b1ff;
        }
        .test-button:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }
        .success {
            color: #67c23a;
            font-weight: bold;
        }
        .error {
            color: #f56c6c;
            font-weight: bold;
        }
        .info {
            color: #909399;
        }
        .result-area {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .file-input {
            margin: 10px 0;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #409eff;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 督办管理Excel功能测试</h1>
        <p class="info">此页面用于测试督办管理系统的Excel导入导出功能</p>
        
        <!-- 服务器连接测试 -->
        <div class="test-section">
            <h3>🔗 服务器连接测试</h3>
            <button class="test-button" onclick="testConnection()">测试连接</button>
            <div id="connection-result" class="result-area"></div>
        </div>
        
        <!-- Excel导出测试 -->
        <div class="test-section">
            <h3>📤 Excel导出功能测试</h3>
            <button class="test-button" onclick="testExport()">测试导出</button>
            <button class="test-button" onclick="downloadTestTemplate()">下载测试模板</button>
            <div id="export-result" class="result-area"></div>
        </div>
        
        <!-- Excel导入测试 -->
        <div class="test-section">
            <h3>📥 Excel导入功能测试</h3>
            <div class="file-input">
                <input type="file" id="import-file" accept=".xlsx,.xls" />
                <button class="test-button" onclick="testImport()">测试导入</button>
            </div>
            <div class="progress" id="import-progress" style="display: none;">
                <div class="progress-bar" id="import-progress-bar"></div>
            </div>
            <div id="import-result" class="result-area"></div>
        </div>
        
        <!-- 自动化测试 -->
        <div class="test-section">
            <h3>🤖 自动化测试</h3>
            <button class="test-button" onclick="runAutoTests()">运行所有自动化测试</button>
            <div class="progress" id="auto-progress" style="display: none;">
                <div class="progress-bar" id="auto-progress-bar"></div>
            </div>
            <div id="auto-result" class="result-area"></div>
        </div>
        
        <!-- 测试结果汇总 -->
        <div class="test-section">
            <h3>📊 测试结果汇总</h3>
            <div id="summary-result" class="result-area">等待测试开始...</div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script>
        const API_BASE = 'http://localhost:8000/api/v1/new-supervision';
        let testResults = [];
        
        function logResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }
        
        function clearResult(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }
        
        async function testConnection() {
            clearResult('connection-result');
            logResult('connection-result', '正在测试服务器连接...');
            
            try {
                const response = await fetch(`${API_BASE}/items`);
                if (response.ok) {
                    const data = await response.json();
                    logResult('connection-result', `✅ 连接成功！获取到 ${data.data.length} 条督办事项`, 'success');
                    testResults.push({name: '服务器连接', success: true});
                } else {
                    logResult('connection-result', `❌ 连接失败！HTTP状态码: ${response.status}`, 'error');
                    testResults.push({name: '服务器连接', success: false});
                }
            } catch (error) {
                logResult('connection-result', `❌ 连接异常: ${error.message}`, 'error');
                testResults.push({name: '服务器连接', success: false});
            }
            updateSummary();
        }
        
        async function testExport() {
            clearResult('export-result');
            logResult('export-result', '正在测试Excel导出...');
            
            try {
                const response = await fetch(`${API_BASE}/export-excel`);
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `测试导出_${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.xlsx`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    
                    logResult('export-result', `✅ 导出成功！文件大小: ${(blob.size/1024).toFixed(2)} KB`, 'success');
                    testResults.push({name: 'Excel导出', success: true});
                } else {
                    logResult('export-result', `❌ 导出失败！HTTP状态码: ${response.status}`, 'error');
                    testResults.push({name: 'Excel导出', success: false});
                }
            } catch (error) {
                logResult('export-result', `❌ 导出异常: ${error.message}`, 'error');
                testResults.push({name: 'Excel导出', success: false});
            }
            updateSummary();
        }
        
        async function downloadTestTemplate() {
            logResult('export-result', '正在生成测试模板...');
            
            // 创建测试数据
            const testData = [
                {
                    '操作类型': 'ADD',
                    'ID': '',
                    '序号': 9998,
                    '工作维度': '测试维度',
                    '工作主题': '测试主题-新增',
                    '督办来源': '测试来源',
                    '工作内容和完成标志': '这是一个测试新增的督办事项',
                    '是否年度绩效考核指标': '是',
                    '完成时限': '2024-12-31',
                    '整体进度': 'X 未启动'
                },
                {
                    '操作类型': 'UPDATE',
                    'ID': '1',
                    '序号': 1,
                    '工作维度': '更新后的维度',
                    '工作主题': '更新后的主题',
                    '督办来源': '更新后的来源',
                    '工作内容和完成标志': '这是一个测试更新的督办事项',
                    '是否年度绩效考核指标': '否',
                    '完成时限': '2024-11-30',
                    '整体进度': 'O进行中'
                },
                {
                    '操作类型': 'DELETE',
                    'ID': '',
                    '序号': 9998,
                    '工作维度': '',
                    '工作主题': '',
                    '督办来源': '',
                    '工作内容和完成标志': '',
                    '是否年度绩效考核指标': '',
                    '完成时限': '',
                    '整体进度': ''
                }
            ];
            
            // 创建Excel文件
            const ws = XLSX.utils.json_to_sheet(testData);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, '测试数据');
            
            // 下载文件
            XLSX.writeFile(wb, `督办管理测试模板_${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.xlsx`);
            
            logResult('export-result', '✅ 测试模板已生成并下载', 'success');
        }
        
        async function testImport() {
            const fileInput = document.getElementById('import-file');
            const file = fileInput.files[0];
            
            if (!file) {
                logResult('import-result', '❌ 请先选择要导入的Excel文件', 'error');
                return;
            }
            
            clearResult('import-result');
            showProgress('import-progress', 'import-progress-bar');
            logResult('import-result', `正在导入文件: ${file.name}`);
            
            try {
                const formData = new FormData();
                formData.append('file', file);
                
                updateProgress('import-progress-bar', 50);
                
                const response = await fetch(`${API_BASE}/import-excel`, {
                    method: 'POST',
                    body: formData
                });
                
                updateProgress('import-progress-bar', 100);
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        const data = result.data;
                        logResult('import-result', `✅ 导入成功！${result.message}`, 'success');
                        logResult('import-result', `详细信息: 新增${data.created_count}条，更新${data.updated_count}条，删除${data.deleted_count}条，跳过${data.skipped_count}条，错误${data.error_count}条`);
                        
                        if (data.error_details && data.error_details.length > 0) {
                            logResult('import-result', `错误详情: ${data.error_details.join('; ')}`, 'error');
                        }
                        
                        testResults.push({name: 'Excel导入', success: true});
                    } else {
                        logResult('import-result', `❌ 导入失败: ${result.message}`, 'error');
                        testResults.push({name: 'Excel导入', success: false});
                    }
                } else {
                    const errorText = await response.text();
                    logResult('import-result', `❌ 导入失败！HTTP状态码: ${response.status}`, 'error');
                    logResult('import-result', `错误信息: ${errorText}`, 'error');
                    testResults.push({name: 'Excel导入', success: false});
                }
            } catch (error) {
                logResult('import-result', `❌ 导入异常: ${error.message}`, 'error');
                testResults.push({name: 'Excel导入', success: false});
            } finally {
                hideProgress('import-progress');
                updateSummary();
            }
        }
        
        async function runAutoTests() {
            clearResult('auto-result');
            showProgress('auto-progress', 'auto-progress-bar');
            logResult('auto-result', '🤖 开始运行自动化测试...');
            
            const tests = [
                {name: '服务器连接', func: testConnection},
                {name: 'Excel导出', func: testExport}
            ];
            
            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                logResult('auto-result', `正在执行: ${test.name}`);
                
                try {
                    await test.func();
                    updateProgress('auto-progress-bar', ((i + 1) / tests.length) * 100);
                    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
                } catch (error) {
                    logResult('auto-result', `❌ ${test.name} 执行失败: ${error.message}`, 'error');
                }
            }
            
            hideProgress('auto-progress');
            logResult('auto-result', '🎉 自动化测试完成！', 'success');
            updateSummary();
        }
        
        function showProgress(progressId, barId) {
            document.getElementById(progressId).style.display = 'block';
            updateProgress(barId, 0);
        }
        
        function hideProgress(progressId) {
            document.getElementById(progressId).style.display = 'none';
        }
        
        function updateProgress(barId, percent) {
            document.getElementById(barId).style.width = percent + '%';
        }
        
        function updateSummary() {
            const summaryElement = document.getElementById('summary-result');
            
            if (testResults.length === 0) {
                summaryElement.innerHTML = '等待测试开始...';
                return;
            }
            
            const total = testResults.length;
            const passed = testResults.filter(r => r.success).length;
            const failed = total - passed;
            const successRate = total > 0 ? (passed / total * 100).toFixed(1) : 0;
            
            let summary = `📊 测试结果统计:\n`;
            summary += `总测试数: ${total}\n`;
            summary += `通过: ${passed}\n`;
            summary += `失败: ${failed}\n`;
            summary += `成功率: ${successRate}%\n\n`;
            
            if (failed > 0) {
                summary += `❌ 失败的测试:\n`;
                testResults.filter(r => !r.success).forEach(r => {
                    summary += `  - ${r.name}\n`;
                });
            } else if (total > 0) {
                summary += `🎉 所有测试都通过了！`;
            }
            
            summaryElement.innerHTML = summary;
        }
        
        // 页面加载完成后自动测试连接
        window.addEventListener('load', () => {
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
