#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试前端字段显示问题
"""

import asyncio
import aiohttp
import json
import os

# 设置测试模式环境变量
os.environ["PMO_TEST_MODE"] = "true"

BASE_URL = "http://localhost:8000"
TEST_PROJECT_CODE = "C202500012"

async def debug_frontend():
    """调试前端字段显示"""
    async with aiohttp.ClientSession() as session:
        print("🔧 调试前端字段显示问题")
        print("=" * 60)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test_token"
        }
        
        # 触发AI更新分析
        print("📤 触发AI更新分析...")
        async with session.post(f"{BASE_URL}/api/v1/project/{TEST_PROJECT_CODE}/ai-update", 
                               headers=headers) as response:
            if response.status == 200:
                result = await response.json()
                print("✅ AI更新分析成功")
                
                if result.get("code") == 200:
                    data = result.get("data", {})
                    steps = data.get("steps", {})
                    
                    print("\n🔍 检查各步骤状态:")
                    for step_key, step_info in steps.items():
                        status = step_info.get("status", "unknown")
                        print(f"  {step_key}: {status}")
                    
                    # 检查步骤6的详细数据
                    step6 = steps.get("step6_user_confirm", {})
                    print(f"\n📋 步骤6详细信息:")
                    print(f"  状态: {step6.get('status')}")
                    print(f"  消息: {step6.get('message')}")
                    
                    step6_data = step6.get("data", {})
                    print(f"  ready_for_confirmation: {step6_data.get('ready_for_confirmation')}")
                    print(f"  ai_response_markdown长度: {len(step6_data.get('ai_response_markdown', ''))}")
                    
                    # 检查步骤3的项目信息
                    step3 = steps.get("step3_get_project_info", {})
                    step3_data = step3.get("data", {})
                    current_fields = step3_data.get("current_fields", {})
                    print(f"\n📊 步骤3项目信息:")
                    print(f"  current_fields字段数: {len(current_fields)}")
                    print(f"  前5个字段: {list(current_fields.keys())[:5]}")
                    
                    # 解析AI建议的字段
                    ai_response = step6_data.get("ai_response_markdown", "")
                    ai_fields = parse_ai_response(ai_response)
                    print(f"\n🤖 AI建议字段解析:")
                    print(f"  解析到的字段数: {len(ai_fields)}")
                    
                    if ai_fields:
                        print(f"  前3个AI建议字段:")
                        for i, field in enumerate(ai_fields[:3], 1):
                            print(f"    {i}. {field['field_key']}: {field['current_value']} → {field['suggested_value']}")
                    
                    print(f"\n💡 前端应该显示的内容:")
                    print(f"  1. 检查浏览器控制台是否有 '🔧 initializeEditableFields 被调用' 日志")
                    print(f"  2. 如果有调用，查看后续的调试信息")
                    print(f"  3. 如果没有调用，说明条件检查有问题")
                    print(f"  4. 当前步骤6状态是: {step6.get('status')}")
                    print(f"  5. 前端条件检查: step6_user_confirm.status === 'ready'")
                    
                    return True
                else:
                    print(f"❌ AI更新失败: {result.get('message')}")
                    return False
            else:
                print(f"❌ 请求失败: HTTP {response.status}")
                return False

def parse_ai_response(ai_response):
    """解析AI回答中的字段建议"""
    fields = []
    lines = ai_response.split('\n')
    
    for line in lines:
        if line.startswith('|') and line.endswith('|'):
            cells = line.split('|')[1:-1]  # 去掉首尾空元素
            cells = [cell.strip() for cell in cells]
            
            if len(cells) >= 4 and cells[0] and cells[1] and cells[2]:
                # 跳过表头和分隔行
                if not cells[0].startswith('-') and '字段名称' not in cells[0]:
                    fields.append({
                        'field_key': cells[0],
                        'current_value': cells[1],
                        'suggested_value': cells[2],
                        'reason': cells[3] if len(cells) > 3 else ''
                    })
    
    return fields

async def main():
    """主函数"""
    success = await debug_frontend()
    
    if success:
        print("\n" + "=" * 60)
        print("🔧 调试信息已输出")
        print("\n📋 下一步调试步骤:")
        print("1. 打开浏览器 http://localhost:3000")
        print("2. 打开开发者工具 (F12)")
        print("3. 进入项目管理页面")
        print("4. 点击项目的'AI更新'按钮")
        print("5. 查看控制台日志，寻找:")
        print("   - '🔧 initializeEditableFields 被调用'")
        print("   - '📊 当前项目信息字段数: XX'")
        print("   - '🤖 AI建议字段数: XX'")
        print("   - '📋 创建的可编辑字段总数: XX'")
        print("6. 如果没有这些日志，说明函数没有被调用")
        print("7. 如果有日志但字段数为0，说明数据解析有问题")
    else:
        print("\n❌ 调试失败")

if __name__ == "__main__":
    asyncio.run(main())
