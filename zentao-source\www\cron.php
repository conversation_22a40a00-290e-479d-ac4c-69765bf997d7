<?php
/**
 * The cron-worker file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.zentao.net)
 * @license     ZPL(https://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     cron
 * @link        https://www.zentao.net
 */

/* Set the error reporting. */
error_reporting(0);

/* Load the framework. */
include '../framework/zand/router.class.php';
include '../framework/control.class.php';
include '../framework/model.class.php';
include '../framework/helper.class.php';

/* Log the time and define the run mode. */
$startTime = getTime();

/* Instance the app. */
$app = zandRouter::createApp('pms', dirname(dirname(__FILE__)), 'zandRouter');

/* Run the app. */
$app->setStartTime($startTime);
$common = $app->loadCommon();

$app->moduleName = 'cron';
$app->methodName = 'ajaxSchedule';
$app->setControlFile();
$app->loadModule();
