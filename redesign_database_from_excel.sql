-- ===================================================================
-- PMO督办管理系统 - 基于Excel表重新设计数据库
-- 参考文件: 督办表
-- 目标: 去掉整体进度字段，动态计算进度
-- ===================================================================

-- 1. 备份现有数据
-- ===================================================================
CREATE TABLE supervision_items_backup_20250731 AS SELECT * FROM supervision_items;
CREATE TABLE company_progress_backup_20250731 AS SELECT * FROM company_progress;
CREATE TABLE company_supervision_status_backup_20250731 AS SELECT * FROM company_supervision_status;

-- 2. 重新设计督办事项表 (去掉冗余字段)
-- ===================================================================

-- 创建新的督办事项表
DROP TABLE IF EXISTS supervision_items_new;

CREATE TABLE supervision_items_new (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    sequence_number INT NOT NULL COMMENT '序号',
    work_dimension VARCHAR(100) NOT NULL COMMENT '工作维度',
    work_theme VARCHAR(200) NOT NULL COMMENT '工作主题', 
    supervision_source VARCHAR(100) NOT NULL COMMENT '督办来源',
    work_content TEXT NOT NULL COMMENT '工作内容和完成标志',
    is_annual_assessment ENUM('是','否') DEFAULT '否' COMMENT '是否年度绩效考核指标',
    completion_deadline DATE COMMENT '完成时限',
    progress_description TEXT COMMENT '进度情况描述',
    completion_date DATE COMMENT '实际完成日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    
    -- 索引
    UNIQUE KEY uk_sequence_number (sequence_number) COMMENT '序号唯一索引',
    KEY idx_work_dimension (work_dimension) COMMENT '工作维度索引',
    KEY idx_completion_deadline (completion_deadline) COMMENT '完成时限索引',
    KEY idx_is_annual_assessment (is_annual_assessment) COMMENT '年度考核索引',
    KEY idx_deleted_at (deleted_at) COMMENT '删除时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='督办事项表';

-- 3. 重新设计公司表 (确保与Excel中的公司一致)
-- ===================================================================

-- 先清空现有公司数据，重新插入
TRUNCATE TABLE companies;

-- 插入Excel表中的公司数据 (按Excel表头顺序)
INSERT INTO companies (company_code, company_name, display_order, is_active) VALUES
('caixian', '财险', 1, 1),
('shouxian', '寿险', 2, 1),
('jinzu', '金租', 3, 1),
('ziguan', '资管', 4, 1),
('guangzu', '广租', 5, 1),
('tongsheng', '通盛', 6, 1),
('danbao', '担保', 7, 1),
('xiaodai', '小贷', 8, 1),
('baoli', '保理', 9, 1),
('budongchan', '不动产', 10, 1),
('zhengxin', '征信', 11, 1),
('jinfu', '金服', 12, 1),
('benbu', '本部', 13, 1);

-- 4. 创建统一的公司督办进度表
-- ===================================================================

DROP TABLE IF EXISTS company_supervision_progress;

CREATE TABLE company_supervision_progress (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    supervision_item_id INT NOT NULL COMMENT '督办事项ID',
    company_id INT NOT NULL COMMENT '公司ID',
    status ENUM('√', 'O', '！', 'X', '—') NOT NULL DEFAULT 'X' COMMENT '进度状态: √已完成 O进行中 ！逾期 X未启动 —不需要执行',
    progress_description TEXT COMMENT '进度描述',
    existing_problems TEXT COMMENT '存在问题',
    next_plan TEXT COMMENT '下一步计划',
    completion_date DATE COMMENT '完成日期',
    updated_by VARCHAR(50) DEFAULT 'system' COMMENT '更新人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引和约束
    UNIQUE KEY uk_supervision_company (supervision_item_id, company_id) COMMENT '督办事项-公司唯一索引',
    KEY idx_supervision_item (supervision_item_id) COMMENT '督办事项索引',
    KEY idx_company (company_id) COMMENT '公司索引',
    KEY idx_status (status) COMMENT '状态索引',
    KEY idx_updated_at (updated_at) COMMENT '更新时间索引',
    
    -- 外键约束
    FOREIGN KEY (supervision_item_id) REFERENCES supervision_items_new(id) ON DELETE CASCADE,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司督办进度表';

-- 5. 从Excel数据导入督办事项
-- ===================================================================

INSERT INTO supervision_items_new (
    sequence_number, work_dimension, work_theme, supervision_source, 
    work_content, is_annual_assessment, completion_deadline, progress_description
) VALUES
(1, '一、监管和制度', '建立条线ITBP团队管理办法', '3月科技例会', 
 '各单位及部门明确对接人，以条线形式建立ITBP服务团队。完成标志：各单位提供科技工作人员分工和花名册信息，变更科技人员须备案科委办。', 
 '否', '2024-05-31', '各部门及子公司已制定ITBP对接人，数字金服已按条线建立ITBP服务团队，对重点项目进行"周跟踪"。'),

(2, '一、监管和制度', '建立项目红绿灯管理办法', '3月科技例会',
 '开展重点项目周跟踪机制，推行"亮黄牌"机制，明确子公司数字化应用覆盖要求。完成标志：各单位参考集团建立科技项目周跟踪机制，确保提前识别项目风险并解决，至年底所有重点项目均按计划上线。',
 '否', '2024-04-30', '已建立公共台账实时反应项目红绿灯，子公司每月根据科委会公布的红绿灯对项目进行"月复盘"。'),

(3, '一、监管和制度', '印发8个信息化管理制度', '5月科技例会',
 '各子公司参照集团印发的信息化管理制度，印发包括科技风险、项目管理等8个制度，并落地执行。完成标志：各单位对照集团信息化制度，检查和印发自身信息化管理制度清单。',
 '否', '2024-08-31', '本部制度印发已较为完整；其他企业仍缺部分制度，8月底完成印发。'),

(4, '一、监管和制度', '印发非信创采购管理制度', '7月科技例会',
 '科委办及各单位参照广投集团印发非信创采购管理制度，含含非信创设备评审流程，并落地执行。完成标志：科委办及各单位对照广投制度，印发非信创制度。',
 '否', '2024-10-31', '广投集团的非信创采购管理制度未印发，待其印发后，科委办及各单位参照印发。'),

(5, '二、数据治理和系统覆盖', '第一批次数据治理', '3月科技例会',
 '持续开展数据治理工作，准确率、完整率、及时率达到要求。完成标志：各单位配合完成集团下达的财务，风控和经营18项指标数据治理，三率（准确、及时、T+1）90%。',
 '是', '2024-04-30', '已完成第一批次，财务，风控和经营18项指标数据治理');

-- 6. 从Excel数据导入公司进度状态
-- ===================================================================

-- 督办事项1的状态 (全部√)
INSERT INTO company_supervision_progress (supervision_item_id, company_id, status) 
SELECT 1, id, '√' FROM companies WHERE is_active = 1;

-- 督办事项2的状态 (全部√)  
INSERT INTO company_supervision_progress (supervision_item_id, company_id, status)
SELECT 2, id, '√' FROM companies WHERE is_active = 1;

-- 督办事项3的状态 (本部√，其他O)
INSERT INTO company_supervision_progress (supervision_item_id, company_id, status)
SELECT 3, c.id, CASE WHEN c.company_code = 'benbu' THEN '√' ELSE 'O' END
FROM companies c WHERE c.is_active = 1;

-- 督办事项4的状态 (全部X)
INSERT INTO company_supervision_progress (supervision_item_id, company_id, status)
SELECT 4, id, 'X' FROM companies WHERE is_active = 1;

-- 督办事项5的状态 (寿险—，其他√)
INSERT INTO company_supervision_progress (supervision_item_id, company_id, status)
SELECT 5, c.id, CASE WHEN c.company_code = 'shouxian' THEN '—' ELSE '√' END
FROM companies c WHERE c.is_active = 1;

-- 7. 替换原表
-- ===================================================================

-- 删除旧表
DROP TABLE IF EXISTS company_progress;
DROP TABLE IF EXISTS company_supervision_status;

-- 重命名新表
RENAME TABLE supervision_items TO supervision_items_old;
RENAME TABLE supervision_items_new TO supervision_items;

-- 8. 创建动态进度计算视图
-- ===================================================================

CREATE VIEW v_supervision_items_with_progress AS
SELECT 
    si.*,
    -- 动态计算整体进度状态
    (SELECT 
        CASE 
            WHEN COUNT(CASE WHEN csp.status = '！' THEN 1 END) > 0 THEN '！'
            WHEN COUNT(CASE WHEN csp.status = '√' THEN 1 END) = COUNT(CASE WHEN csp.status != '—' THEN 1 END) 
                 AND COUNT(CASE WHEN csp.status != '—' THEN 1 END) > 0 THEN '√'
            WHEN COUNT(CASE WHEN csp.status = 'O' THEN 1 END) > 0 THEN 'O'
            ELSE 'X'
        END
     FROM company_supervision_progress csp 
     JOIN companies c ON csp.company_id = c.id 
     WHERE csp.supervision_item_id = si.id AND c.is_active = 1
    ) AS overall_progress,
    
    -- 动态计算完成率
    (SELECT 
        CASE 
            WHEN COUNT(CASE WHEN csp.status != '—' THEN 1 END) = 0 THEN 100.0
            ELSE ROUND(
                COUNT(CASE WHEN csp.status = '√' THEN 1 END) * 100.0 / 
                COUNT(CASE WHEN csp.status != '—' THEN 1 END), 1
            )
        END
     FROM company_supervision_progress csp 
     JOIN companies c ON csp.company_id = c.id 
     WHERE csp.supervision_item_id = si.id AND c.is_active = 1
    ) AS completion_rate,
    
    -- 动态判断是否逾期
    (SELECT 
        CASE 
            WHEN si.completion_deadline IS NOT NULL AND CURDATE() > si.completion_deadline THEN 1
            ELSE 0
        END
    ) AS is_overdue

FROM supervision_items si
WHERE si.deleted_at IS NULL;

-- 9. 创建触发器确保数据完整性
-- ===================================================================

DELIMITER $$

-- 当添加新督办事项时，自动为所有活跃公司创建进度记录
CREATE TRIGGER tr_supervision_items_insert_new
AFTER INSERT ON supervision_items
FOR EACH ROW
BEGIN
    INSERT INTO company_supervision_progress (supervision_item_id, company_id, status, updated_by)
    SELECT NEW.id, c.id, 'X', 'system'
    FROM companies c 
    WHERE c.is_active = 1;
END$$

-- 当添加新公司时，自动为所有督办事项创建进度记录
CREATE TRIGGER tr_companies_insert_new
AFTER INSERT ON companies
FOR EACH ROW
BEGIN
    IF NEW.is_active = 1 THEN
        INSERT INTO company_supervision_progress (supervision_item_id, company_id, status, updated_by)
        SELECT si.id, NEW.id, 'X', 'system'
        FROM supervision_items si 
        WHERE si.deleted_at IS NULL;
    END IF;
END$$

DELIMITER ;

-- 10. 数据验证
-- ===================================================================

-- 验证督办事项数量
SELECT '督办事项验证' as check_type, COUNT(*) as count FROM supervision_items WHERE deleted_at IS NULL;

-- 验证公司数量  
SELECT '公司验证' as check_type, COUNT(*) as count FROM companies WHERE is_active = 1;

-- 验证进度记录数量
SELECT '进度记录验证' as check_type, COUNT(*) as count FROM company_supervision_progress;

-- 验证状态分布
SELECT '状态分布' as check_type, status, COUNT(*) as count 
FROM company_supervision_progress 
GROUP BY status 
ORDER BY status;

-- 验证动态进度计算
SELECT 
    si.sequence_number,
    si.work_theme,
    v.overall_progress,
    v.completion_rate,
    v.is_overdue
FROM supervision_items si
JOIN v_supervision_items_with_progress v ON si.id = v.id
ORDER BY si.sequence_number
LIMIT 5;

-- ===================================================================
-- 数据库重设计完成！
-- 基于Excel表结构，去掉整体进度字段，实现动态计算
-- ===================================================================
