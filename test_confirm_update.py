#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI更新确认功能
"""

import asyncio
import aiohttp
import json
import os

# 设置测试模式环境变量
os.environ["PMO_TEST_MODE"] = "true"

BASE_URL = "http://localhost:8000"
TEST_PROJECT_CODE = "C202500012"

async def test_confirm_update():
    """测试确认更新功能"""
    async with aiohttp.ClientSession() as session:
        print("🎯 测试AI更新确认功能")
        print("=" * 60)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test_token"
        }
        
        # 模拟用户选择的更新数据（使用正确的数据库字段名）
        confirmed_updates = {
            "project_name": "AI更新功能测试项目",
            "responsible_person": "张三",
            "responsible_department": "技术开发部",
            "current_progress": "实施阶段"
        }
        
        update_data = {
            "confirmed_updates": confirmed_updates
        }
        
        print("📤 发送确认更新请求...")
        print(f"更新数据: {json.dumps(confirmed_updates, ensure_ascii=False, indent=2)}")
        
        async with session.post(
            f"{BASE_URL}/api/v1/project/{TEST_PROJECT_CODE}/ai-update/confirm",
            json=update_data,
            headers=headers
        ) as response:
            
            print(f"📥 响应状态码: {response.status}")
            
            if response.status == 200:
                result = await response.json()
                print("✅ 确认更新成功！")
                print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                if result.get("code") == 200:
                    data = result.get("data", {})
                    updated_fields = data.get("updated_fields", [])
                    print(f"\n🎉 更新结果:")
                    print(f"  - 项目编号: {data.get('project_code')}")
                    print(f"  - 更新字段数: {len(updated_fields)}")
                    print(f"  - 更新字段: {', '.join(updated_fields)}")
                    return True
                else:
                    print(f"❌ 更新失败: {result.get('message')}")
                    return False
            else:
                error_text = await response.text()
                print(f"❌ 请求失败: HTTP {response.status}")
                print(f"错误信息: {error_text}")
                return False

async def verify_update_result():
    """验证更新结果"""
    async with aiohttp.ClientSession() as session:
        print("\n🔍 验证更新结果")
        print("-" * 30)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test_token"
        }
        
        # 获取项目列表，查看更新后的数据
        async with session.get(f"{BASE_URL}/api/v1/project/list", headers=headers) as response:
            if response.status == 200:
                result = await response.json()
                if result.get("code") == 200:
                    projects = result.get("data", [])
                    
                    # 查找测试项目
                    test_project = None
                    for project in projects:
                        if project.get("project_code") == TEST_PROJECT_CODE:
                            test_project = project
                            break
                    
                    if test_project:
                        print("✅ 找到测试项目，验证更新结果:")
                        print(f"  - 项目名称: {test_project.get('project_name')}")
                        print(f"  - 责任人: {test_project.get('responsible_person')}")
                        print(f"  - 负责部门: {test_project.get('responsible_department')}")
                        print(f"  - 当前进度: {test_project.get('current_progress')}")
                        return True
                    else:
                        print(f"❌ 未找到项目编号为 {TEST_PROJECT_CODE} 的项目")
                        return False
                else:
                    print(f"❌ 获取项目列表失败: {result.get('message')}")
                    return False
            else:
                print(f"❌ 请求失败: HTTP {response.status}")
                return False

async def main():
    """主函数"""
    print("🚀 开始测试AI更新确认功能")
    
    # 测试确认更新
    update_success = await test_confirm_update()
    
    if update_success:
        # 验证更新结果
        verify_success = await verify_update_result()
        
        if verify_success:
            print("\n🎉 AI更新确认功能测试完全成功！")
            print("\n✅ 功能验证:")
            print("  - AI分析档案文件 ✅")
            print("  - 生成更新建议 ✅")
            print("  - 用户确认更新 ✅")
            print("  - 应用更新到数据库 ✅")
            print("  - 更新结果验证 ✅")
        else:
            print("\n⚠️ 更新成功但验证失败")
    else:
        print("\n❌ AI更新确认功能测试失败")

if __name__ == "__main__":
    asyncio.run(main())
