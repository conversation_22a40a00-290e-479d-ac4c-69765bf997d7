# MySQL 数据库管理工具

这是一个基于 Flask 的 MySQL 数据库管理工具，可以方便地管理数据库、表和记录。

## 功能特点

- 数据库管理（创建、删除）
- 表管理（创建、删除、导入 Excel）
- 字段管理（添加、修改、删除）
- 记录管理（添加、修改、删除）
- 索引和主键管理
- 数据导出为 Excel

## 快速开始

### 使用启动脚本

1. **基本启动脚本**：
   - 双击 `start_app.bat` 文件即可启动应用

2. **高级启动脚本**：
   - 双击 `start_app_advanced.bat` 文件
   - 选择启动模式：
     - 1: 正常模式启动
     - 2: 调试模式启动（开发时使用）
     - 3: 安装/更新依赖
     - 4: 退出

### 手动启动

1. 安装依赖：
   ```
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
   ```

2. 启动应用：
   ```
   python app.py
   ```

## 访问应用

启动后，在浏览器中访问：http://localhost:5000

## 环境配置

可以通过修改 `.env` 文件来配置数据库连接信息和应用设置：

```
# 数据库配置
DB_HOST=数据库服务器地址
DB_PORT=3306
DB_USER=用户名
DB_PASSWORD=密码
DB_TIMEOUT=10

# Flask 配置
SECRET_KEY=密钥
HOST=0.0.0.0
PORT=5000
FLASK_DEBUG=False
``` 