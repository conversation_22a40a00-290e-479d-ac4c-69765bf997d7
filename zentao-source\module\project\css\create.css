.popover-sm .popover-content {padding: 8px 10px; max-width: 228px;}

#dateRange {vertical-align: top; padding-top: 13px;}
#endList {vertical-align: top; padding-top: 13px;}
#copyProjectModal {padding: 0;}
#copyProjectModal {padding: 0 2px;}
#copyProjectModal > div {position: relative;}
#copyProjectModal a {display: block; min-height: 30px; line-height: 30px; padding: 5px 15px; border: 1px solid #e5e5e5; color: #333; margin: 5px 0; border-radius: 3px;}
#copyProjectModal a > i {display: inline-block; margin-right: 5px;}
#copyProjectModal a:hover {border: 1px solid #00a9fc; background-color: #E9F2FB; text-decoration: none;}
#copyProjectModal a.active {border-color: #00da88; color: #00da88; background-color: #E5FFE6;}
#copyProjectModal a.active:after {position: absolute; content: '\e92f'; font-family: ZentaoIcon; font-size: 20px; right: 25px;}
#copyProjectModal a.cancel {color: #ff5d5d;}
#mainContent td.required:after {right: -4px;}
#budget {border-right: 0px;}
#budgetUnit {border-left: 0px;}
#projectName {display: inline-block; width: 32%;}
.productsBox a {border-radius: 2px !important;}
.productsBox .required:after {right: -12px !important; top: 7px !important;}
.productsBox .addProduct {padding-left: 0px !important;}
.productsBox .addProduct.required:after {right: -1px !important;}
.productsBox .input-group-addon > div {display: inline-block !important;}
.productsBox .required + .text-danger.help-text {position: relative; left: 10px;}
.productsBox > #productNameLabel {padding-top: 8px;}
.futureBox {vertical-align: top !important; padding-top: 13px !important;}
#projectType {padding-top: 7px !important;}
.stageBy .icon-help {margin-left: 15px}
.has-info {border-color: #0c64eb!important;}
