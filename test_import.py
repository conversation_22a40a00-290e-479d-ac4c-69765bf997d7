#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel导入功能
"""

import requests
import os

def test_excel_import():
    """测试Excel导入功能"""
    try:
        print("🔍 测试Excel导入...")
        
        # 检查导出的文件是否存在
        if not os.path.exists('test_export.xlsx'):
            print("❌ 测试文件不存在，请先运行导出测试")
            return
        
        # 上传文件
        with open('test_export.xlsx', 'rb') as f:
            files = {'file': ('test_import.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            
            response = requests.post(
                'http://127.0.0.1:8001/api/v1/simple-supervision/import',
                files=files
            )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 导入成功: {result.get('message', '无消息')}")
        else:
            print(f"❌ 导入失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    test_excel_import()
