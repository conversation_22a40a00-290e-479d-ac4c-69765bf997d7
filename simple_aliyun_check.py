#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单检查阿里云数据库
"""

import pymysql
import sys

def main():
    print("🔍 连接阿里云数据库...")
    
    try:
        connection = pymysql.connect(
            host='rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
            port=3306,
            user='cyh',
            password='Qq188788',
            database='kanban2',
            charset='utf8mb4',
            connect_timeout=10
        )
        
        print("✅ 连接成功")
        
        with connection.cursor() as cursor:
            # 显示所有表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            print(f"📋 数据库中有 {len(tables)} 个表:")
            for table in tables:
                print(f"   {table[0]}")
            
            # 检查督办相关表
            supervision_tables = ['supervision_items', 'companies', 'company_progress']
            print(f"\n🔍 检查督办相关表:")
            
            for table_name in supervision_tables:
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                if cursor.fetchone():
                    print(f"✅ {table_name} 存在")
                    
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = cursor.fetchall()
                    print(f"   字段: {[col[0] for col in columns]}")
                    
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"   数据量: {count}")
                else:
                    print(f"❌ {table_name} 不存在")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
