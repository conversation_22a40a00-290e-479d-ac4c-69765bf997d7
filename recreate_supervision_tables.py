#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新创建督办管理表
"""

import pymysql

def recreate_supervision_tables():
    """重新创建督办管理表"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        print("✅ 数据库连接成功")
        
        with connection.cursor() as cursor:
            print("🗑️  删除旧表...")
            
            # 删除旧表（如果存在）
            cursor.execute("DROP TABLE IF EXISTS supervision_status_history")
            cursor.execute("DROP TABLE IF EXISTS company_supervision_status")
            cursor.execute("DROP TABLE IF EXISTS supervision_items")
            cursor.execute("DROP TABLE IF EXISTS companies")
            
            print("✅ 旧表删除完成")
            
            print("🔧 创建新表...")
            
            # 1. 创建督办事项表
            cursor.execute("""
                CREATE TABLE supervision_items (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    sequence_number INT NOT NULL COMMENT '序号',
                    work_dimension VARCHAR(100) NOT NULL COMMENT '工作维度',
                    work_theme VARCHAR(200) NOT NULL COMMENT '工作主题',
                    supervision_source VARCHAR(100) NOT NULL COMMENT '督办来源',
                    work_content TEXT NOT NULL COMMENT '工作内容和完成标志',
                    is_annual_assessment ENUM('是', '否') DEFAULT '否' COMMENT '是否年度绩效考核指标',
                    completion_deadline VARCHAR(50) NOT NULL COMMENT '完成时限',
                    progress_description TEXT COMMENT '7月28日进度情况',
                    overall_progress ENUM('X 未启动', 'O进行中', '！逾期', '√ 已完成', '— 不需要执行') DEFAULT 'X 未启动' COMMENT '整体进度',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='督办事项表'
            """)
            print("✅ supervision_items 表创建成功")
            
            # 2. 创建公司表
            cursor.execute("""
                CREATE TABLE companies (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    company_code VARCHAR(50) NOT NULL UNIQUE COMMENT '公司代码',
                    company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
                    display_order INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
                    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司表'
            """)
            print("✅ companies 表创建成功")
            
            # 3. 创建公司督办状态表
            cursor.execute("""
                CREATE TABLE company_supervision_status (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    supervision_item_id INT NOT NULL COMMENT '督办事项ID',
                    company_id INT NOT NULL COMMENT '公司ID',
                    status ENUM('√', 'O', '！', 'X', '—') DEFAULT 'X' COMMENT '状态：√已完成 O进行中 ！逾期 X未启动 —不需要执行',
                    progress_description TEXT COMMENT '当前进展情况',
                    existing_problems TEXT COMMENT '存在问题',
                    next_plan TEXT COMMENT '下一步计划',
                    updated_by VARCHAR(50) COMMENT '更新人',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY uk_item_company (supervision_item_id, company_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司督办状态表'
            """)
            print("✅ company_supervision_status 表创建成功")
            
            # 4. 创建督办状态变更历史表
            cursor.execute("""
                CREATE TABLE supervision_status_history (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    supervision_item_id INT NOT NULL COMMENT '督办事项ID',
                    company_id INT NOT NULL COMMENT '公司ID',
                    old_status ENUM('√', 'O', '！', 'X', '—') COMMENT '原状态',
                    new_status ENUM('√', 'O', '！', 'X', '—') COMMENT '新状态',
                    old_progress_description TEXT COMMENT '原进展情况',
                    new_progress_description TEXT COMMENT '新进展情况',
                    old_existing_problems TEXT COMMENT '原存在问题',
                    new_existing_problems TEXT COMMENT '新存在问题',
                    old_next_plan TEXT COMMENT '原下一步计划',
                    new_next_plan TEXT COMMENT '新下一步计划',
                    change_reason VARCHAR(500) COMMENT '变更原因',
                    updated_by VARCHAR(50) COMMENT '更新人',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='督办状态变更历史表'
            """)
            print("✅ supervision_status_history 表创建成功")
            
            # 插入公司数据
            print("🏢 插入公司数据...")
            companies_data = [
                ('CXBX', '财险', 1),
                ('SXBX', '寿险', 2),
                ('JINZU', '金租', 3),
                ('ZICHAN', '资管', 4),
                ('GUANGZU', '广租', 5),
                ('TONGSHENG', '通盛', 6),
                ('DANBAO', '担保', 7),
                ('XIAODAI', '小贷', 8),
                ('BAOLI', '保理', 9),
                ('BUDONGCHAN', '不动产', 10),
                ('ZHENGXIN', '征信', 11),
                ('JINFU', '金服', 12),
                ('BENBU', '本部', 13)
            ]
            
            for company_code, company_name, display_order in companies_data:
                cursor.execute("""
                    INSERT INTO companies (company_code, company_name, display_order)
                    VALUES (%s, %s, %s)
                """, (company_code, company_name, display_order))
            
            print("✅ 公司数据插入成功")
            
            # 插入督办事项数据
            print("📋 插入督办事项数据...")
            items_data = [
                (1, '一、监管和制度', '建立条线ITBP团队管理办法', '3月科技例会', 
                 '各单位及部门明确对接人，以条线形式建立ITBP服务团队。完成标志：各单位提供科技工作人员分工和花名册信息，变更科技人员须备案科委办。', 
                 '否', '5月末', '各部门及子公司已制定ITBP对接人，数字金服已按条线建立ITBP服务团队，对重点项目进行"周跟踪"。', '√ 已完成'),
                (2, '一、监管和制度', '建立项目红绿灯管理办法', '3月科技例会',
                 '开展重点项目周跟踪机制，推行"亮黄牌"机制，明确子公司数字化应用覆盖要求。完成标志：各单位参考集团建立科技项目周跟踪机制，确保提前识别项目风险并解决，至年底所有重点项目均按计划上线。',
                 '否', '4月末', '已建立公共台账实时反应项目红绿灯，子公司每月根据科委会公布的红绿灯对项目进行"月复盘"。', '√ 已完成'),
                (3, '一、监管和制度', '印发8个信息化管理制度', '5月科技例会',
                 '各子公司参照集团印发的信息化管理制度，印发包括科技风险、项目管理等8个制度，并落地执行。完成标志：各单位对照集团信息化制度，检查和印发自身信息化管理制度清单。',
                 '否', '8月末', '本部制度印发已较为完整；其他企业仍缺部分制度，8月底完成印发。', 'O进行中'),
                (4, '一、监管和制度', '印发非信创采购管理制度', '7月科技例会',
                 '科委办及各单位参照广投集团印发非信创采购管理制度，含含非信创设备评审流程，并落地执行。完成标志：科委办及各单位对照广投制度，印发非信创制度。',
                 '否', '10月末', '广投集团的非信创采购管理制度未印发，待其印发后，科委办及各单位参照印发。', 'X 未启动'),
                (5, '二、数据治理和系统覆盖', '第一批次数据治理', '3月科技例会',
                 '持续开展数据治理工作，准确率、完整率、及时率达到要求。完成标志：各单位配合完成集团下达的财务，风控和经营18项指标数据治理，三率（准确、及时、T+1）90%。',
                 '是', '4月末', '已完成第一批次，财务，风控和经营18项指标数据治理', '√ 已完成')
            ]
            
            for item_data in items_data:
                cursor.execute("""
                    INSERT INTO supervision_items 
                    (sequence_number, work_dimension, work_theme, supervision_source, work_content, 
                     is_annual_assessment, completion_deadline, progress_description, overall_progress)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, item_data)
            
            print("✅ 督办事项数据插入成功")
            
            # 为每个督办事项创建公司状态记录
            print("📊 创建公司状态记录...")
            cursor.execute("SELECT id FROM supervision_items")
            items = cursor.fetchall()
            
            cursor.execute("SELECT id FROM companies WHERE is_active = 1")
            companies = cursor.fetchall()
            
            for item in items:
                for company in companies:
                    cursor.execute("""
                        INSERT INTO company_supervision_status (supervision_item_id, company_id, status)
                        VALUES (%s, %s, 'X')
                    """, (item[0], company[0]))
            
            print("✅ 公司状态记录创建成功")
            
            # 提交所有操作
            connection.commit()
            
            # 验证创建结果
            cursor.execute("SELECT COUNT(*) FROM supervision_items")
            items_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM companies")
            companies_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM company_supervision_status")
            status_count = cursor.fetchone()[0]
            
            print(f"\n📊 创建结果:")
            print(f"   督办事项: {items_count} 条")
            print(f"   公司: {companies_count} 个")
            print(f"   状态记录: {status_count} 条")
            
            # 测试查询
            print(f"\n🧪 测试查询:")
            cursor.execute("SELECT id, sequence_number, work_theme, is_annual_assessment, overall_progress FROM supervision_items LIMIT 3")
            rows = cursor.fetchall()
            
            for row in rows:
                print(f"   {row[1]}. {row[2]} - 考核指标:{row[3]} - 进度:{row[4]}")
        
        connection.close()
        print("🎉 督办管理表重新创建完成！")
        
    except Exception as e:
        print(f"❌ 重新创建失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    recreate_supervision_tables()
