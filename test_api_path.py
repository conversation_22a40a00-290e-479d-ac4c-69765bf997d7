#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API路径修复
"""

import requests

def test_api_paths():
    """测试新的API路径"""
    try:
        print("🔍 测试新的API路径...")
        
        # 测试获取督办事项
        print("\n1. 测试获取督办事项...")
        response = requests.get('http://127.0.0.1:8001/api/v1/supervision/items')
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 获取成功，共 {len(result.get('data', []))} 条数据")
        else:
            print(f"   ❌ 获取失败: {response.text}")
        
        # 测试API文档
        print("\n2. 测试API文档...")
        doc_response = requests.get('http://127.0.0.1:8001/docs')
        print(f"   API文档状态码: {doc_response.status_code}")
        
        if doc_response.status_code == 200:
            print("   ✅ API文档可访问")
        else:
            print("   ❌ API文档不可访问")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始测试API路径修复...")
    test_api_paths()
    print("\n🎉 测试完成！")
