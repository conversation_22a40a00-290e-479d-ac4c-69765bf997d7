<?php
/* Open. */
$lang->upgrade->fromVersions['0_3beta']     = '0.3 BETA';
$lang->upgrade->fromVersions['0_4beta']     = '0.4 BETA';
$lang->upgrade->fromVersions['0_5beta']     = '0.5 BETA';
$lang->upgrade->fromVersions['0_6beta']     = '0.6 BETA';
$lang->upgrade->fromVersions['1_0beta']     = '1.0 BETA';
$lang->upgrade->fromVersions['1_0rc1']      = '1.0 RC1';
$lang->upgrade->fromVersions['1_0rc2']      = '1.0 RC2';
$lang->upgrade->fromVersions['1_0']         = '1.0 STABLE';
$lang->upgrade->fromVersions['1_0_1']       = '1.0.1';
$lang->upgrade->fromVersions['1_1']         = '1.1';
$lang->upgrade->fromVersions['1_2']         = '1.2';
$lang->upgrade->fromVersions['1_3']         = '1.3';
$lang->upgrade->fromVersions['1_4']         = '1.4';
$lang->upgrade->fromVersions['1_5']         = '1.5';
$lang->upgrade->fromVersions['2_0']         = '2.0';
$lang->upgrade->fromVersions['2_1']         = '2.1';
$lang->upgrade->fromVersions['2_2']         = '2.2';
$lang->upgrade->fromVersions['2_3']         = '2.3';
$lang->upgrade->fromVersions['2_4']         = '2.4';
$lang->upgrade->fromVersions['3_0_beta1']   = '3.0 BETA1';
$lang->upgrade->fromVersions['3_0_beta2']   = '3.0 BETA2';
$lang->upgrade->fromVersions['3_0']         = '3.0 STABLE';
$lang->upgrade->fromVersions['3_1']         = '3.1';
$lang->upgrade->fromVersions['3_2']         = '3.2';
$lang->upgrade->fromVersions['3_2_1']       = '3.2.1';
$lang->upgrade->fromVersions['3_3']         = '3.3';
$lang->upgrade->fromVersions['4_0_beta1']   = '4.0 BETA1';
$lang->upgrade->fromVersions['4_0_beta2']   = '4.0 BETA2';
$lang->upgrade->fromVersions['4_0']         = '4.0';
$lang->upgrade->fromVersions['4_0_1']       = '4.0.1';
$lang->upgrade->fromVersions['4_1']         = '4.1';
$lang->upgrade->fromVersions['4_2_beta']    = '4.2.beta';
$lang->upgrade->fromVersions['4_3_beta']    = '4.3.beta';
$lang->upgrade->fromVersions['5_0_beta1']   = '5.0.beta1';
$lang->upgrade->fromVersions['5_0_beta2']   = '5.0.beta2';
$lang->upgrade->fromVersions['5_0']         = '5.0';
$lang->upgrade->fromVersions['5_1']         = '5.1';
$lang->upgrade->fromVersions['5_2']         = '5.2';
$lang->upgrade->fromVersions['5_2_1']       = '5.2.1';
$lang->upgrade->fromVersions['5_3']         = '5.3';
$lang->upgrade->fromVersions['6_0_beta1']   = '6.0.beta1';
$lang->upgrade->fromVersions['6_0']         = '6.0';
$lang->upgrade->fromVersions['6_1']         = '6.1';
$lang->upgrade->fromVersions['6_2']         = '6.2';
$lang->upgrade->fromVersions['6_3']         = '6.3';
$lang->upgrade->fromVersions['6_4']         = '6.4';
$lang->upgrade->fromVersions['7_0']         = '7.0';
$lang->upgrade->fromVersions['7_1']         = '7.1';
$lang->upgrade->fromVersions['7_2']         = '7.2';
$lang->upgrade->fromVersions['7_2_4']       = '7.2.4';
$lang->upgrade->fromVersions['7_2_5']       = '7.2.5';
$lang->upgrade->fromVersions['7_3']         = '7.3';
$lang->upgrade->fromVersions['7_4_beta']    = '7.4.beta';
$lang->upgrade->fromVersions['8_0']         = '8.0';
$lang->upgrade->fromVersions['8_0_1']       = '8.0.1';
$lang->upgrade->fromVersions['8_1']         = '8.1';
$lang->upgrade->fromVersions['8_1_3']       = '8.1.3';
$lang->upgrade->fromVersions['8_2_beta']    = '8.2.beta';
$lang->upgrade->fromVersions['8_2']         = '8.2';
$lang->upgrade->fromVersions['8_2_1']       = '8.2.1';
$lang->upgrade->fromVersions['8_2_2']       = '8.2.2';
$lang->upgrade->fromVersions['8_2_3']       = '8.2.3';
$lang->upgrade->fromVersions['8_2_4']       = '8.2.4';
$lang->upgrade->fromVersions['8_2_5']       = '8.2.5';
$lang->upgrade->fromVersions['8_2_6']       = '8.2.6';
$lang->upgrade->fromVersions['8_3']         = '8.3';
$lang->upgrade->fromVersions['8_3_1']       = '8.3.1';
$lang->upgrade->fromVersions['8_4']         = '8.4';
$lang->upgrade->fromVersions['8_4_1']       = '8.4.1';
$lang->upgrade->fromVersions['9_0_beta']    = '9.0.beta';
$lang->upgrade->fromVersions['9_0']         = '9.0';
$lang->upgrade->fromVersions['9_0_1']       = '9.0.1';
$lang->upgrade->fromVersions['9_1']         = '9.1';
$lang->upgrade->fromVersions['9_1_1']       = '9.1.1';
$lang->upgrade->fromVersions['9_1_2']       = '9.1.2';
$lang->upgrade->fromVersions['9_2']         = '9.2';
$lang->upgrade->fromVersions['9_2_1']       = '9.2.1';
$lang->upgrade->fromVersions['9_3_beta']    = '9.3.beta';
$lang->upgrade->fromVersions['9_4']         = '9.4';
$lang->upgrade->fromVersions['9_5']         = '9.5';
$lang->upgrade->fromVersions['9_5_1']       = '9.5.1';
$lang->upgrade->fromVersions['9_6']         = '9.6';
$lang->upgrade->fromVersions['9_6_1']       = '9.6.1';
$lang->upgrade->fromVersions['9_6_2']       = '9.6.2';
$lang->upgrade->fromVersions['9_6_3']       = '9.6.3';
$lang->upgrade->fromVersions['9_7']         = '9.7';
$lang->upgrade->fromVersions['9_8']         = '9.8';
$lang->upgrade->fromVersions['9_8_1']       = '9.8.1';
$lang->upgrade->fromVersions['9_8_2']       = '9.8.2';
$lang->upgrade->fromVersions['9_8_3']       = '9.8.3';
$lang->upgrade->fromVersions['10_0_alpha']  = '10.0.alpha';
$lang->upgrade->fromVersions['10_0_beta']   = '10.0.beta';
$lang->upgrade->fromVersions['10_0']        = '10.0';
$lang->upgrade->fromVersions['10_1']        = '10.1';
$lang->upgrade->fromVersions['10_2']        = '10.2';
$lang->upgrade->fromVersions['10_3']        = '10.3';
$lang->upgrade->fromVersions['10_3_1']      = '10.3.1';
$lang->upgrade->fromVersions['10_4']        = '10.4';
$lang->upgrade->fromVersions['10_5']        = '10.5';
$lang->upgrade->fromVersions['10_5_1']      = '10.5.1';
$lang->upgrade->fromVersions['10_6']        = '10.6';
$lang->upgrade->fromVersions['11_0']        = '11.0';
$lang->upgrade->fromVersions['11_1']        = '11.1';
$lang->upgrade->fromVersions['11_2']        = '11.2';
$lang->upgrade->fromVersions['11_3']        = '11.3';
$lang->upgrade->fromVersions['11_4']        = '11.4';
$lang->upgrade->fromVersions['11_4_1']      = '11.4.1';
$lang->upgrade->fromVersions['11_5']        = '11.5';
$lang->upgrade->fromVersions['11_5_1']      = '11.5.1';
$lang->upgrade->fromVersions['11_5_2']      = '11.5.2';
$lang->upgrade->fromVersions['11_6']        = '11.6';
$lang->upgrade->fromVersions['11_6_1']      = '11.6.1';
$lang->upgrade->fromVersions['11_6_2']      = '11.6.2';
$lang->upgrade->fromVersions['11_6_3']      = '11.6.3';
$lang->upgrade->fromVersions['11_6_4']      = '11.6.4';
$lang->upgrade->fromVersions['11_6_5']      = '11.6.5';
$lang->upgrade->fromVersions['11_7']        = '11.7';
$lang->upgrade->fromVersions['12_0']        = '12.0';
$lang->upgrade->fromVersions['12_0_1']      = '12.0.1';
$lang->upgrade->fromVersions['12_1']        = '12.1';
$lang->upgrade->fromVersions['12_2']        = '12.2';
$lang->upgrade->fromVersions['12_3']        = '12.3';
$lang->upgrade->fromVersions['12_3_1']      = '12.3.1';
$lang->upgrade->fromVersions['12_3_2']      = '12.3.2';
$lang->upgrade->fromVersions['12_3_3']      = '12.3.3';
$lang->upgrade->fromVersions['12_4']        = '12.4';
$lang->upgrade->fromVersions['12_4_1']      = '12.4.1';
$lang->upgrade->fromVersions['12_4_2']      = '12.4.2';
$lang->upgrade->fromVersions['12_4_3']      = '12.4.3';
$lang->upgrade->fromVersions['12_4_4']      = '12.4.4';
$lang->upgrade->fromVersions['12_5']        = '12.5';
$lang->upgrade->fromVersions['12_5_1']      = '12.5.1';
$lang->upgrade->fromVersions['12_5_2']      = '12.5.2';
$lang->upgrade->fromVersions['12_5_3']      = '12.5.3';
$lang->upgrade->fromVersions['15_0_rc1']    = '15.0.rc1';
$lang->upgrade->fromVersions['15_0_rc2']    = '15.0.rc2';
$lang->upgrade->fromVersions['15_0_rc3']    = '15.0.rc3';
$lang->upgrade->fromVersions['15_0']        = '15.0';
$lang->upgrade->fromVersions['15_0_1']      = '15.0.1';
$lang->upgrade->fromVersions['15_0_2']      = '15.0.2';
$lang->upgrade->fromVersions['15_0_3']      = '15.0.3';
$lang->upgrade->fromVersions['15_2']        = '15.2';
$lang->upgrade->fromVersions['15_3']        = '15.3';
$lang->upgrade->fromVersions['15_4']        = '15.4';
$lang->upgrade->fromVersions['15_5']        = '15.5';
$lang->upgrade->fromVersions['15_6']        = '15.6';
$lang->upgrade->fromVersions['15_7']        = '15.7';
$lang->upgrade->fromVersions['15_7_1']      = '15.7.1';
$lang->upgrade->fromVersions['16_0_beta1']  = '16.0.beta1';
$lang->upgrade->fromVersions['16_0']        = '16.0';
$lang->upgrade->fromVersions['16_1']        = '16.1';
$lang->upgrade->fromVersions['16_2']        = '16.2';
$lang->upgrade->fromVersions['16_3']        = '16.3';
$lang->upgrade->fromVersions['16_4']        = '16.4';
$lang->upgrade->fromVersions['16_5_beta1']  = '16.5.beta1';
$lang->upgrade->fromVersions['16_5']        = '16.5';
$lang->upgrade->fromVersions['17_0_beta1']  = '17.0.beta1';
$lang->upgrade->fromVersions['17_0_beta2']  = '17.0.beta2';
$lang->upgrade->fromVersions['17_0']        = '17.0';
$lang->upgrade->fromVersions['17_1']        = '17.1';
$lang->upgrade->fromVersions['17_2']        = '17.2';
$lang->upgrade->fromVersions['17_3']        = '17.3';
$lang->upgrade->fromVersions['17_4']        = '17.4';
$lang->upgrade->fromVersions['17_5']        = '17.5';
$lang->upgrade->fromVersions['17_6']        = '17.6';
$lang->upgrade->fromVersions['17_6_1']      = '17.6.1';
$lang->upgrade->fromVersions['17_6_2']      = '17.6.2';
$lang->upgrade->fromVersions['17_7']        = '17.7';
$lang->upgrade->fromVersions['17_8']        = '17.8';
$lang->upgrade->fromVersions['18_0_beta1']  = '18.0.beta1';
$lang->upgrade->fromVersions['18_0_beta2']  = '18.0.beta2';
$lang->upgrade->fromVersions['18_0_beta3']  = '18.0.beta3';
$lang->upgrade->fromVersions['18_0']        = '18.0';
$lang->upgrade->fromVersions['18_1']        = '18.1';
$lang->upgrade->fromVersions['18_2']        = '18.2';
$lang->upgrade->fromVersions['18_3']        = '18.3';
$lang->upgrade->fromVersions['18_4_alpha1'] = '18.4.alpha1';
$lang->upgrade->fromVersions['18_4_beta1']  = '18.4.beta1';
$lang->upgrade->fromVersions['18_4']        = '18.4';
$lang->upgrade->fromVersions['18_5']        = '18.5';
$lang->upgrade->fromVersions['18_6']        = '18.6';
$lang->upgrade->fromVersions['18_7']        = '18.7';
$lang->upgrade->fromVersions['18_8']        = '18.8';
$lang->upgrade->fromVersions['18_9']        = '18.9';
$lang->upgrade->fromVersions['18_10']       = '18.10';
$lang->upgrade->fromVersions['18_10_1']     = '18.10.1';
$lang->upgrade->fromVersions['18_11']       = '18.11';
$lang->upgrade->fromVersions['18_12']       = '18.12';
$lang->upgrade->fromVersions['18_13']       = '18.13';
$lang->upgrade->fromVersions['20_0_alpha1'] = '20.0.alpha1';
$lang->upgrade->fromVersions['20_0_beta1']  = '20.0.beta1';
$lang->upgrade->fromVersions['20_0_beta2']  = '20.0.beta2';
$lang->upgrade->fromVersions['20_0']        = '20.0';
$lang->upgrade->fromVersions['20_1_0']      = '20.1.0';
$lang->upgrade->fromVersions['20_1_1']      = '20.1.1';
$lang->upgrade->fromVersions['20_2_0']      = '20.2.0';
$lang->upgrade->fromVersions['20_3_0']      = '20.3.0';
$lang->upgrade->fromVersions['20_4']        = '20.4';
$lang->upgrade->fromVersions['20_5']        = '20.5';
$lang->upgrade->fromVersions['20_6']        = '20.6';
$lang->upgrade->fromVersions['20_7']        = '20.7';
$lang->upgrade->fromVersions['20_7_1']      = '20.7.1';
$lang->upgrade->fromVersions['20_8']        = '20.8';
$lang->upgrade->fromVersions['21_0']        = '21.0';
$lang->upgrade->fromVersions['21_1']        = '21.1';
$lang->upgrade->fromVersions['21_2']        = '21.2';
$lang->upgrade->fromVersions['21_3']        = '21.3';
$lang->upgrade->fromVersions['21_4']        = '21.4';
$lang->upgrade->fromVersions['21_5']        = '21.5';
$lang->upgrade->fromVersions['21_6_beta']   = '21.6.beta';
$lang->upgrade->fromVersions['21_6']        = '21.6';
$lang->upgrade->fromVersions['21_6_1']      = '21.6.1';
$lang->upgrade->fromVersions['21_7']        = '21.7';
$lang->upgrade->fromVersions['21_7_1']      = '21.7.1'; // pms insert position.

global $config;
/* Lite. */
$lang->upgrade->fromVersions['lite1_0'] = 'Lite1.0';
$lang->upgrade->fromVersions['lite1_1'] = 'Lite1.1';
if($config->edition != 'open') $lang->upgrade->fromVersions['liteVIP1_1'] = 'LiteVIP1.1';

/* Pro. */
$lang->upgrade->fromVersions['pro1_0']        = 'Pro1.0';
$lang->upgrade->fromVersions['pro1_1']        = 'Pro1.1';
$lang->upgrade->fromVersions['pro1_1_1']      = 'Pro1.1.1';
$lang->upgrade->fromVersions['pro1_2']        = 'Pro1.2';
$lang->upgrade->fromVersions['pro1_3']        = 'Pro1.3';
$lang->upgrade->fromVersions['pro2_0']        = 'Pro2.0';
$lang->upgrade->fromVersions['pro2_0_1']      = 'Pro2.0.1';
$lang->upgrade->fromVersions['pro2_1']        = 'Pro2.1';
$lang->upgrade->fromVersions['pro2_2_beta']   = 'Pro2.2.beta';
$lang->upgrade->fromVersions['pro2_3_beta']   = 'Pro2.3.beta';
$lang->upgrade->fromVersions['pro3_0_beta1']  = 'Pro3.0.beta1';
$lang->upgrade->fromVersions['pro3_0']        = 'Pro3.0';
$lang->upgrade->fromVersions['pro3_1']        = 'Pro3.1';
$lang->upgrade->fromVersions['pro3_2']        = 'Pro3.2';
$lang->upgrade->fromVersions['pro3_2_1']      = 'Pro3.2.1';
$lang->upgrade->fromVersions['pro3_3']        = 'Pro3.3';
$lang->upgrade->fromVersions['pro4_0_beta1']  = 'Pro4.0.beta1';
$lang->upgrade->fromVersions['pro4_0']        = 'Pro4.0';
$lang->upgrade->fromVersions['pro4_1_beta']   = 'Pro4.1.beta';
$lang->upgrade->fromVersions['pro4_2']        = 'Pro4.2';
$lang->upgrade->fromVersions['pro4_3']        = 'Pro4.3';
$lang->upgrade->fromVersions['pro4_4']        = 'Pro4.4';
$lang->upgrade->fromVersions['pro4_5']        = 'Pro4.5';
$lang->upgrade->fromVersions['pro4_6']        = 'Pro4.6';
$lang->upgrade->fromVersions['pro4_7']        = 'Pro4.7';
$lang->upgrade->fromVersions['pro4_7_1']      = 'Pro4.7.1';
$lang->upgrade->fromVersions['pro5_0']        = 'Pro5.0';
$lang->upgrade->fromVersions['pro5_0_1']      = 'Pro5.0.1';
$lang->upgrade->fromVersions['pro5_1']        = 'Pro5.1';
$lang->upgrade->fromVersions['pro5_1_3']      = 'Pro5.1.3';
$lang->upgrade->fromVersions['pro5_2']        = 'Pro5.2';
$lang->upgrade->fromVersions['pro5_2_1']      = 'Pro5.2.1';
$lang->upgrade->fromVersions['pro5_3']        = 'Pro5.3';
$lang->upgrade->fromVersions['pro5_3_1']      = 'Pro5.3.1';
$lang->upgrade->fromVersions['pro5_3_2']      = 'Pro5.3.2';
$lang->upgrade->fromVersions['pro5_3_3']      = 'Pro5.3.3';
$lang->upgrade->fromVersions['pro5_4']        = 'Pro5.4';
$lang->upgrade->fromVersions['pro5_4_1']      = 'Pro5.4.1';
$lang->upgrade->fromVersions['pro5_5']        = 'Pro5.5';
$lang->upgrade->fromVersions['pro5_5_1']      = 'Pro5.5.1';
$lang->upgrade->fromVersions['pro6_0_beta']   = 'Pro6.0.beta';
$lang->upgrade->fromVersions['pro6_0']        = 'Pro6.0';
$lang->upgrade->fromVersions['pro6_0_1']      = 'Pro6.0.1';
$lang->upgrade->fromVersions['pro6_1']        = 'Pro6.1';
$lang->upgrade->fromVersions['pro6_2']        = 'Pro6.2';
$lang->upgrade->fromVersions['pro6_3']        = 'Pro6.3';
$lang->upgrade->fromVersions['pro6_3_1']      = 'Pro6.3.1';
$lang->upgrade->fromVersions['pro6_4']        = 'Pro6.4';
$lang->upgrade->fromVersions['pro6_5']        = 'Pro6.5';
$lang->upgrade->fromVersions['pro6_5_1']      = 'Pro6.5.1';
$lang->upgrade->fromVersions['pro6_6']        = 'Pro6.6';
$lang->upgrade->fromVersions['pro6_6_1']      = 'Pro6.6.1';
$lang->upgrade->fromVersions['pro6_7']        = 'Pro6.7';
$lang->upgrade->fromVersions['pro6_7_1']      = 'Pro6.7.1';
$lang->upgrade->fromVersions['pro6_7_2']      = 'Pro6.7.2';
$lang->upgrade->fromVersions['pro6_7_3']      = 'Pro6.7.3';
$lang->upgrade->fromVersions['pro7_0_beta']   = 'Pro7.0.beta';
$lang->upgrade->fromVersions['pro7_1']        = 'Pro7.1';
$lang->upgrade->fromVersions['pro7_2']        = 'Pro7.2';
$lang->upgrade->fromVersions['pro7_3']        = 'Pro7.3';
$lang->upgrade->fromVersions['pro7_4']        = 'Pro7.4';
$lang->upgrade->fromVersions['pro7_5']        = 'Pro7.5';
$lang->upgrade->fromVersions['pro7_5_1']      = 'Pro7.5.1';
$lang->upgrade->fromVersions['pro8_0']        = 'Pro8.0';
$lang->upgrade->fromVersions['pro8_1']        = 'Pro8.1';
$lang->upgrade->fromVersions['pro8_2']        = 'Pro8.2';
$lang->upgrade->fromVersions['pro8_3']        = 'Pro8.3';
$lang->upgrade->fromVersions['pro8_3_1']      = 'Pro8.3.1';
$lang->upgrade->fromVersions['pro8_4']        = 'Pro8.4';
$lang->upgrade->fromVersions['pro8_5']        = 'Pro8.5';
$lang->upgrade->fromVersions['pro8_5_1']      = 'Pro8.5.1';
$lang->upgrade->fromVersions['pro8_5_2']      = 'Pro8.5.2';
$lang->upgrade->fromVersions['pro8_5_3']      = 'Pro8.5.3';
$lang->upgrade->fromVersions['pro8_6']        = 'Pro8.6';
$lang->upgrade->fromVersions['pro8_7']        = 'Pro8.7';
$lang->upgrade->fromVersions['pro8_7_1']      = 'Pro8.7.1';
$lang->upgrade->fromVersions['pro8_8']        = 'Pro8.8';
$lang->upgrade->fromVersions['pro8_8_1']      = 'Pro8.8.1';
$lang->upgrade->fromVersions['pro8_8_2']      = 'Pro8.8.2';
$lang->upgrade->fromVersions['pro8_8_3']      = 'Pro8.8.3';
$lang->upgrade->fromVersions['pro8_9']        = 'Pro8.9';
$lang->upgrade->fromVersions['pro8_9_1']      = 'Pro8.9.1';
$lang->upgrade->fromVersions['pro8_9_2']      = 'Pro8.9.2';
$lang->upgrade->fromVersions['pro8_9_3']      = 'Pro8.9.3';
$lang->upgrade->fromVersions['pro8_9_4']      = 'Pro8.9.4';
$lang->upgrade->fromVersions['pro9_0']        = 'Pro9.0';
$lang->upgrade->fromVersions['pro9_0_1']      = 'Pro9.0.1';
$lang->upgrade->fromVersions['pro9_0_2']      = 'Pro9.0.2';
$lang->upgrade->fromVersions['pro9_0_3']      = 'Pro9.0.3';
$lang->upgrade->fromVersions['pro10_0_rc1']   = 'Pro10.0.rc1';
$lang->upgrade->fromVersions['pro10_0']       = 'Pro10.0';
$lang->upgrade->fromVersions['pro10_0_1']     = 'Pro10.0.1';
$lang->upgrade->fromVersions['pro10_0_2']     = 'Pro10.0.2';
$lang->upgrade->fromVersions['pro10_1']       = 'Pro10.1';
$lang->upgrade->fromVersions['pro10_2']       = 'Pro10.2';
$lang->upgrade->fromVersions['pro10_3']       = 'Pro10.3';
$lang->upgrade->fromVersions['pro10_3_1']     = 'Pro10.3.1';
$lang->upgrade->fromVersions['pro11_0_beta1'] = 'Pro11.0.beta1';
$lang->upgrade->fromVersions['pro11_0']       = 'Pro11.0';

/* Biz. */
$lang->upgrade->fromVersions['biz1_0']          = 'Biz1.0';
$lang->upgrade->fromVersions['biz1_1']          = 'Biz1.1';
$lang->upgrade->fromVersions['biz1_1_1']        = 'Biz1.1.1';
$lang->upgrade->fromVersions['biz1_1_2']        = 'Biz1.1.2';
$lang->upgrade->fromVersions['biz1_1_3']        = 'Biz1.1.3';
$lang->upgrade->fromVersions['biz1_1_4']        = 'Biz1.1.4';
$lang->upgrade->fromVersions['biz2_0_beta']     = 'Biz2.0.beta';
$lang->upgrade->fromVersions['biz2_1']          = 'Biz2.1';
$lang->upgrade->fromVersions['biz2_2']          = 'Biz2.2';
$lang->upgrade->fromVersions['biz2_3']          = 'Biz2.3';
$lang->upgrade->fromVersions['biz2_3_1']        = 'Biz2.3.1';
$lang->upgrade->fromVersions['biz2_4']          = 'Biz2.4';
$lang->upgrade->fromVersions['biz3_0']          = 'Biz3.0';
$lang->upgrade->fromVersions['biz3_1']          = 'Biz3.1';
$lang->upgrade->fromVersions['biz3_2']          = 'Biz3.2';
$lang->upgrade->fromVersions['biz3_2_1']        = 'Biz3.2.1';
$lang->upgrade->fromVersions['biz3_3']          = 'Biz3.3';
$lang->upgrade->fromVersions['biz3_4']          = 'Biz3.4';
$lang->upgrade->fromVersions['biz3_5_alpha']    = 'Biz3.5.alpha';
$lang->upgrade->fromVersions['biz3_5_beta']     = 'Biz3.5.beta';
$lang->upgrade->fromVersions['biz3_5']          = 'Biz3.5';
$lang->upgrade->fromVersions['biz3_5_1']        = 'Biz3.5.1';
$lang->upgrade->fromVersions['biz3_6']          = 'Biz3.6';
$lang->upgrade->fromVersions['biz3_6_1']        = 'Biz3.6.1';
$lang->upgrade->fromVersions['biz3_7']          = 'Biz3.7';
$lang->upgrade->fromVersions['biz3_7_1']        = 'Biz3.7.1';
$lang->upgrade->fromVersions['biz3_7_2']        = 'Biz3.7.2';
$lang->upgrade->fromVersions['biz4_0']          = 'Biz4.0';
$lang->upgrade->fromVersions['biz4_0_1']        = 'Biz4.0.1';
$lang->upgrade->fromVersions['biz4_0_2']        = 'Biz4.0.2';
$lang->upgrade->fromVersions['biz4_0_3']        = 'Biz4.0.3';
$lang->upgrade->fromVersions['biz4_0_4']        = 'Biz4.0.4';
$lang->upgrade->fromVersions['biz4_1']          = 'Biz4.1';
$lang->upgrade->fromVersions['biz4_1_1']        = 'Biz4.1.1';
$lang->upgrade->fromVersions['biz4_1_2']        = 'Biz4.1.2';
$lang->upgrade->fromVersions['biz4_1_3']        = 'Biz4.1.3';
$lang->upgrade->fromVersions['biz5_0_rc1']      = 'Biz5.0.rc1';
$lang->upgrade->fromVersions['biz5_0']          = 'Biz5.0';
$lang->upgrade->fromVersions['biz5_0_1']        = 'Biz5.0.1';
$lang->upgrade->fromVersions['biz5_1']          = 'Biz5.1';
$lang->upgrade->fromVersions['biz5_2']          = 'Biz5.2';
$lang->upgrade->fromVersions['biz5_3']          = 'Biz5.3';
$lang->upgrade->fromVersions['biz5_3_1']        = 'Biz5.3.1';
$lang->upgrade->fromVersions['biz6_0_beta1']    = 'Biz6.0.beta1';
$lang->upgrade->fromVersions['biz6_0']          = 'Biz6.0';
$lang->upgrade->fromVersions['biz6_1']          = 'Biz6.1';
$lang->upgrade->fromVersions['biz6_2']          = 'Biz6.2';
$lang->upgrade->fromVersions['biz6_3']          = 'Biz6.3';
$lang->upgrade->fromVersions['biz6_4']          = 'Biz6.4';
$lang->upgrade->fromVersions['biz6_5_beta1']    = 'Biz6.5.beta1';
$lang->upgrade->fromVersions['biz6_5']          = 'Biz6.5';
$lang->upgrade->fromVersions['biz7_0_beta1']    = 'Biz7.0.beta1';
$lang->upgrade->fromVersions['biz7_0_beta2']    = 'Biz7.0.beta2';
$lang->upgrade->fromVersions['biz7_0']          = 'Biz7.0';
$lang->upgrade->fromVersions['biz7_1']          = 'Biz7.1';
$lang->upgrade->fromVersions['biz7_2']          = 'Biz7.2';
$lang->upgrade->fromVersions['biz7_3']          = 'Biz7.3';
$lang->upgrade->fromVersions['biz7_4']          = 'Biz7.4';
$lang->upgrade->fromVersions['biz7_5']          = 'Biz7.5';
$lang->upgrade->fromVersions['biz7_6']          = 'Biz7.6';
$lang->upgrade->fromVersions['biz7_6_1']        = 'Biz7.6.1';
$lang->upgrade->fromVersions['biz7_6_2']        = 'Biz7.6.2';
$lang->upgrade->fromVersions['biz7_7']          = 'Biz7.7';
$lang->upgrade->fromVersions['biz7_8']          = 'Biz7.8';
$lang->upgrade->fromVersions['biz8_0_beta1']    = 'Biz8.0.beta1';
$lang->upgrade->fromVersions['biz8_0_beta2']    = 'Biz8.0.beta2';
$lang->upgrade->fromVersions['biz8_0_beta3']    = 'Biz8.0.beta3';
$lang->upgrade->fromVersions['biz8_0']          = 'Biz8.0';
$lang->upgrade->fromVersions['biz8_1']          = 'Biz8.1';
$lang->upgrade->fromVersions['biz8_2']          = 'Biz8.2';
$lang->upgrade->fromVersions['biz8_3']          = 'Biz8.3';
$lang->upgrade->fromVersions['biz8_4_alpha1']   = 'Biz8.4.alpha1';
$lang->upgrade->fromVersions['biz8_4_beta1']    = 'Biz8.4.beta1';
$lang->upgrade->fromVersions['biz8_4']          = 'Biz8.4';
$lang->upgrade->fromVersions['biz8_5']          = 'Biz8.5';
$lang->upgrade->fromVersions['biz8_6']          = 'Biz8.6';
$lang->upgrade->fromVersions['biz8_7']          = 'Biz8.7';
$lang->upgrade->fromVersions['biz8_8']          = 'Biz8.8';
$lang->upgrade->fromVersions['biz8_9']          = 'Biz8.9';
$lang->upgrade->fromVersions['biz8_10']         = 'Biz8.10';
$lang->upgrade->fromVersions['biz8_10_1']       = 'Biz8.10.1';
$lang->upgrade->fromVersions['biz8_11']         = 'Biz8.11';
$lang->upgrade->fromVersions['biz8_12']         = 'Biz8.12';
$lang->upgrade->fromVersions['biz8_13']         = 'Biz8.13';
$lang->upgrade->fromVersions['biz10_0_0_beta1'] = 'Biz10.0.0.beta1';
$lang->upgrade->fromVersions['biz10_0_0']       = 'Biz10.0.0';
$lang->upgrade->fromVersions['biz10_1_0']       = 'Biz10.1.0';
$lang->upgrade->fromVersions['biz10_2_0']       = 'Biz10.2.0';
$lang->upgrade->fromVersions['biz10_3']         = 'Biz10.3';
$lang->upgrade->fromVersions['biz10_4']         = 'Biz10.4';
$lang->upgrade->fromVersions['biz10_5']         = 'Biz10.5';
$lang->upgrade->fromVersions['biz10_6']         = 'Biz10.6';
$lang->upgrade->fromVersions['biz10_6_1']       = 'Biz10.6.1';
$lang->upgrade->fromVersions['biz10_7']         = 'Biz10.7';
$lang->upgrade->fromVersions['biz11_0']         = 'Biz11.0';
$lang->upgrade->fromVersions['biz11_1']         = 'Biz11.1';
$lang->upgrade->fromVersions['biz11_2']         = 'Biz11.2';
$lang->upgrade->fromVersions['biz11_3']         = 'Biz11.3';
$lang->upgrade->fromVersions['biz11_4']         = 'Biz11.4';
$lang->upgrade->fromVersions['biz11_5']         = 'Biz11.5';
$lang->upgrade->fromVersions['biz11_6_beta']    = 'Biz11.6.beta';
$lang->upgrade->fromVersions['biz11_6']         = 'Biz11.6';
$lang->upgrade->fromVersions['biz11_6_1']       = 'Biz11.6.1';
$lang->upgrade->fromVersions['biz11_7']         = 'Biz11.7';
$lang->upgrade->fromVersions['biz12_0']         = 'Biz12.0'; // biz insert position.

/* Max. */
$lang->upgrade->fromVersions['max2_0_beta4']   = 'Max2.0.beta4';
$lang->upgrade->fromVersions['max2_0_rc1']     = 'Max2.0.rc1';
$lang->upgrade->fromVersions['max2_0']         = 'Max2.0';
$lang->upgrade->fromVersions['max2_1']         = 'Max2.1';
$lang->upgrade->fromVersions['max2_2']         = 'Max2.2';
$lang->upgrade->fromVersions['max2_3']         = 'Max2.3';
$lang->upgrade->fromVersions['max2_3_1']       = 'Max2.3.1';
$lang->upgrade->fromVersions['max2_4_beta1']   = 'Max2.4.beta1';
$lang->upgrade->fromVersions['max2_4']         = 'Max2.4';
$lang->upgrade->fromVersions['max2_5']         = 'Max2.5';
$lang->upgrade->fromVersions['max2_6']         = 'Max2.6';
$lang->upgrade->fromVersions['max2_7']         = 'Max2.7';
$lang->upgrade->fromVersions['max2_8']         = 'Max2.8';
$lang->upgrade->fromVersions['max3_0_beta1']   = 'Max3.0.beta1';
$lang->upgrade->fromVersions['max3_0']         = 'Max3.0';
$lang->upgrade->fromVersions['max3_1_beta1']   = 'Max3.1.beta1';
$lang->upgrade->fromVersions['max3_1_beta2']   = 'Max3.1.beta2';
$lang->upgrade->fromVersions['max3_1']         = 'Max3.1';
$lang->upgrade->fromVersions['max3_2']         = 'Max3.2';
$lang->upgrade->fromVersions['max3_3']         = 'Max3.3';
$lang->upgrade->fromVersions['max3_4']         = 'Max3.4';
$lang->upgrade->fromVersions['max3_5']         = 'Max3.5';
$lang->upgrade->fromVersions['max3_6']         = 'Max3.6';
$lang->upgrade->fromVersions['max3_6_1']       = 'Max3.6.1';
$lang->upgrade->fromVersions['max3_6_2']       = 'Max3.6.2';
$lang->upgrade->fromVersions['max3_6_3']       = 'Max3.6.3';
$lang->upgrade->fromVersions['max3_7']         = 'Max3.7';
$lang->upgrade->fromVersions['max3_8']         = 'Max3.8';
$lang->upgrade->fromVersions['max4_0_beta1']   = 'Max4.0.beta1';
$lang->upgrade->fromVersions['max4_0_beta2']   = 'Max4.0.beta2';
$lang->upgrade->fromVersions['max4_0_beta3']   = 'Max4.0.beta3';
$lang->upgrade->fromVersions['max4_0']         = 'Max4.0';
$lang->upgrade->fromVersions['max4_1']         = 'Max4.1';
$lang->upgrade->fromVersions['max4_2']         = 'Max4.2';
$lang->upgrade->fromVersions['max4_2']         = 'Max4.2';
$lang->upgrade->fromVersions['max4_3']         = 'Max4.3';
$lang->upgrade->fromVersions['max4_4_alpha1']  = 'Max4.4.alpha1';
$lang->upgrade->fromVersions['max4_4_beta1']   = 'Max4.4.beta1';
$lang->upgrade->fromVersions['max4_4']         = 'Max4.4';
$lang->upgrade->fromVersions['max4_5']         = 'Max4.5';
$lang->upgrade->fromVersions['max4_6']         = 'Max4.6';
$lang->upgrade->fromVersions['max4_7']         = 'Max4.7';
$lang->upgrade->fromVersions['max4_8']         = 'Max4.8';
$lang->upgrade->fromVersions['max4_9']         = 'Max4.9';
$lang->upgrade->fromVersions['max4_10']        = 'Max4.10';
$lang->upgrade->fromVersions['max4_10_1']      = 'Max4.10.1';
$lang->upgrade->fromVersions['max4_11']        = 'Max4.11';
$lang->upgrade->fromVersions['max4_12']        = 'Max4.12';
$lang->upgrade->fromVersions['max4_13']        = 'Max4.13';
$lang->upgrade->fromVersions['max5_0_0_beta1'] = 'Max5.0.0.beta1';
$lang->upgrade->fromVersions['max5_0_0']       = 'Max5.0.0';
$lang->upgrade->fromVersions['max5_1_0']       = 'Max5.1.0';
$lang->upgrade->fromVersions['max5_2_0']       = 'Max5.2.0';
$lang->upgrade->fromVersions['max5_3']         = 'Max5.3';
$lang->upgrade->fromVersions['max5_4']         = 'Max5.4';
$lang->upgrade->fromVersions['max5_5']         = 'Max5.5';
$lang->upgrade->fromVersions['max5_6']         = 'Max5.6';
$lang->upgrade->fromVersions['max5_6_1']       = 'Max5.6.1';
$lang->upgrade->fromVersions['max5_7']         = 'Max5.7';
$lang->upgrade->fromVersions['max6_0']         = 'Max6.0';
$lang->upgrade->fromVersions['max6_1']         = 'Max6.1';
$lang->upgrade->fromVersions['max6_2']         = 'Max6.2';
$lang->upgrade->fromVersions['max6_3']         = 'Max6.3';
$lang->upgrade->fromVersions['max6_4']         = 'Max6.4';
$lang->upgrade->fromVersions['max6_5']         = 'Max6.5';
$lang->upgrade->fromVersions['max6_6_beta']    = 'Max6.6.beta';
$lang->upgrade->fromVersions['max6_6']         = 'Max6.6';
$lang->upgrade->fromVersions['max6_6_1']       = 'Max6.6.1';
$lang->upgrade->fromVersions['max6_7']         = 'Max6.7';
$lang->upgrade->fromVersions['max7_0']         = 'Max7.0'; // max insert position.

/* Ipd */
$lang->upgrade->fromVersions['ipd1_0_beta1']   = 'Ipd1.0.beta1';
$lang->upgrade->fromVersions['ipd1_0']         = 'Ipd1.0';
$lang->upgrade->fromVersions['ipd1_0_1']       = 'Ipd1.0.1';
$lang->upgrade->fromVersions['ipd1_0_2']       = 'Ipd1.0.2';
$lang->upgrade->fromVersions['ipd1_1']         = 'Ipd1.1';
$lang->upgrade->fromVersions['ipd1_1_1']       = 'Ipd1.1.1';
$lang->upgrade->fromVersions['ipd1_1_2']       = 'Ipd1.1.2';
$lang->upgrade->fromVersions['ipd1_2']         = 'Ipd1.2';
$lang->upgrade->fromVersions['ipd1_3']         = 'Ipd1.3';
$lang->upgrade->fromVersions['ipd1_4']         = 'Ipd1.4';
$lang->upgrade->fromVersions['ipd1_5']         = 'Ipd1.5';
$lang->upgrade->fromVersions['ipd2_0_0_beta1'] = 'Ipd2.0.0.beta1';
$lang->upgrade->fromVersions['ipd2_0_0']       = 'Ipd2.0.0';
$lang->upgrade->fromVersions['ipd2_1_0']       = 'Ipd2.1.0';
$lang->upgrade->fromVersions['ipd2_2_0']       = 'Ipd2.2.0';
$lang->upgrade->fromVersions['ipd2_3']         = 'Ipd2.3';
$lang->upgrade->fromVersions['ipd2_4']         = 'Ipd2.4';
$lang->upgrade->fromVersions['ipd2_5']         = 'Ipd2.5';
$lang->upgrade->fromVersions['ipd2_6']         = 'Ipd2.6';
$lang->upgrade->fromVersions['ipd2_6_1']       = 'Ipd2.6.1';
$lang->upgrade->fromVersions['ipd2_7']         = 'Ipd2.7';
$lang->upgrade->fromVersions['ipd3_0']         = 'Ipd3.0';
$lang->upgrade->fromVersions['ipd3_1']         = 'Ipd3.1';
$lang->upgrade->fromVersions['ipd3_2']         = 'Ipd3.2';
$lang->upgrade->fromVersions['ipd3_3']         = 'Ipd3.3';
$lang->upgrade->fromVersions['ipd3_4']         = 'Ipd3.4';
$lang->upgrade->fromVersions['ipd3_5']         = 'Ipd3.5';
$lang->upgrade->fromVersions['ipd3_6_beta']    = 'Ipd3.6.beta';
$lang->upgrade->fromVersions['ipd3_6']         = 'Ipd3.6';
$lang->upgrade->fromVersions['ipd3_6_1']       = 'Ipd3.6.1';
$lang->upgrade->fromVersions['ipd3_7']         = 'Ipd3.7'; // ipd insert position.