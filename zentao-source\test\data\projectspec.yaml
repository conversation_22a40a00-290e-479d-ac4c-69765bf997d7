title: table zt_projectspec
desc: "项目描述"
author: automated export
version: "1.0"
fields:
  - field: project
    note: "项目ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: version
    note: "版本"
    range: 1
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: name
    note: "名称"
    range: 1-10000
    prefix: "这里是项目名称"
    postfix: ""
    loop: 0
    format: ""
  - field: milestone
    note: "是否里程碑"
    range: 1,0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: begin
    note: "开始日期"
    from: common.date.v1.yaml
    use: dateB
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: end
    note: "结束日期"
    from: common.date.v1.yaml
    use: dateB
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
