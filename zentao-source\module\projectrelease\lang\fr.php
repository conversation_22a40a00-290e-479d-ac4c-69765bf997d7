<?php
/**
 * The release module zh-cn file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     release
 * @version     $Id: zh-cn.php 4129 2020-11-27 01:58:14Z wwccss $
 * @link        https://www.zentao.net
 */
$lang->projectrelease->common           = 'Release';
$lang->projectrelease->create           = "Créer Release";
$lang->projectrelease->edit             = "Editer Release";
$lang->projectrelease->linkStory        = "Intégrer Story";
$lang->projectrelease->linkBug          = "Intégrer Bug";
$lang->projectrelease->delete           = "Supprimer Release";
$lang->projectrelease->deleted          = 'Supprimée';
$lang->projectrelease->view             = "Détail Release";
$lang->projectrelease->browse           = "Liste Release";
$lang->projectrelease->changeStatus     = "Change Statut";
$lang->projectrelease->batchUnlink      = "Retirer par Lot";
$lang->projectrelease->batchUnlinkStory = "Retirer Stories par Lot";
$lang->projectrelease->batchUnlinkBug   = "Retirer Bugs par Lot";
$lang->projectrelease->unlinkStory      = "Unlink {$lang->SRCommon}";
$lang->projectrelease->unlinkBug        = 'Unlink Bug';
$lang->projectrelease->export           = 'Export HTML';
$lang->projectrelease->browseAction     = "Release List";
$lang->projectrelease->notify           = 'notify';
$lang->projectrelease->publish          = "Publish";
$lang->projectrelease->product          = $lang->productCommon;
$lang->projectrelease->name             = $lang->product->system . ' Version';

$lang->projectrelease->featureBar['browse']['all']       = 'All';
$lang->projectrelease->featureBar['browse']['wait']      = 'Wait';
$lang->projectrelease->featureBar['browse']['normal']    = 'Released';
$lang->projectrelease->featureBar['browse']['terminate'] = 'Terminated';
