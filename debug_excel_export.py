#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Excel导出问题
"""

import requests
import time
import os

def debug_excel_export():
    """调试Excel导出"""
    print("🔍 调试Excel导出问题...")
    
    try:
        print("📤 调用导出API...")
        response = requests.get("http://localhost:8000/api/v1/new-supervision/export-excel", timeout=30)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            content = response.content
            content_length = len(content)
            
            print(f"内容长度: {content_length} 字节")
            print(f"内容类型: {type(content)}")
            
            # 检查内容前100字节
            preview = content[:100]
            print(f"内容预览: {preview}")
            
            # 检查是否是有效的Excel文件
            if content.startswith(b'PK'):  # Excel文件的魔数
                print("✅ 这是一个有效的Excel文件")
                
                # 保存文件
                filename = f"debug_export_{int(time.time())}.xlsx"
                with open(filename, 'wb') as f:
                    f.write(content)
                
                file_size = os.path.getsize(filename)
                print(f"✅ 文件保存成功: {filename} ({file_size} 字节)")
                
                return True
            else:
                print("❌ 这不是一个有效的Excel文件")
                print(f"内容开头: {content[:200]}")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 调试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_excel_generation():
    """直接测试Excel生成"""
    print("\n📊 直接测试Excel生成...")
    
    try:
        import pandas as pd
        import io
        
        # 创建测试数据
        data = {
            '序号': [1, 2, 3],
            '工作主题': ['测试1', '测试2', '测试3'],
            '完成时限': ['2024-12-31', '2024-11-30', '2024-10-31'],
            '财险': ['√', 'O', 'X'],
            '寿险': ['O', '√', '—']
        }
        
        df = pd.DataFrame(data)
        print(f"DataFrame: {df.shape}")
        
        # 生成Excel
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='测试', index=False)
        
        output.seek(0)
        content = output.read()
        
        print(f"生成的Excel大小: {len(content)} 字节")
        
        # 保存测试文件
        test_filename = f"direct_test_{int(time.time())}.xlsx"
        with open(test_filename, 'wb') as f:
            f.write(content)
        
        print(f"✅ 直接生成Excel成功: {test_filename}")
        return True
        
    except Exception as e:
        print(f"❌ 直接生成Excel失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 Excel导出问题调试")
    print("=" * 50)
    
    # 等待服务启动
    time.sleep(2)
    
    # 1. 测试直接Excel生成
    direct_success = test_direct_excel_generation()
    
    # 2. 调试API导出
    api_success = debug_excel_export()
    
    print("\n" + "=" * 50)
    print("📋 调试结果:")
    print(f"直接Excel生成: {'✅ 成功' if direct_success else '❌ 失败'}")
    print(f"API Excel导出: {'✅ 成功' if api_success else '❌ 失败'}")
    
    if direct_success and not api_success:
        print("\n🔍 问题分析:")
        print("   • pandas和openpyxl库工作正常")
        print("   • 问题出现在API层面")
        print("   • 可能是数据序列化或响应格式问题")
    elif not direct_success:
        print("\n🔍 问题分析:")
        print("   • pandas或openpyxl库有问题")
        print("   • 需要检查依赖库安装")
    else:
        print("\n🎉 Excel功能正常！")
    
    print("\n🏁 调试完成")

if __name__ == "__main__":
    main()
