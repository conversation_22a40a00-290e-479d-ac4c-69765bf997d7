<!DOCTYPE html>
<html>
<head>
    <title>测试前端公司列显示</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>测试前端公司列显示</h1>
    
    <h2>1. 后端公司列表</h2>
    <div id="backend-companies"></div>
    
    <h2>2. 本地存储的公司列表</h2>
    <div id="local-companies"></div>
    
    <h2>3. 测试操作</h2>
    <button onclick="loadBackendCompanies()">获取后端公司列表</button>
    <button onclick="clearLocalStorage()">清除本地存储</button>
    <button onclick="testMergeLogic()">测试合并逻辑</button>
    
    <script>
        async function loadBackendCompanies() {
            try {
                const response = await axios.get('http://127.0.0.1:8001/api/v1/supervision/companies');
                if (response.data.success) {
                    const companies = response.data.data.map(item => item.company_name);
                    document.getElementById('backend-companies').innerHTML = 
                        '✅ 后端公司列表: ' + companies.join(', ');
                    return companies;
                } else {
                    document.getElementById('backend-companies').innerHTML = 
                        '❌ 获取失败: ' + response.data.message;
                }
            } catch (error) {
                document.getElementById('backend-companies').innerHTML = 
                    '❌ 请求异常: ' + error.message;
            }
        }
        
        function clearLocalStorage() {
            localStorage.removeItem('supervision-visible-companies');
            document.getElementById('local-companies').innerHTML = '✅ 已清除本地存储';
            showLocalStorage();
        }
        
        function showLocalStorage() {
            const saved = localStorage.getItem('supervision-visible-companies');
            if (saved) {
                const companies = JSON.parse(saved);
                document.getElementById('local-companies').innerHTML = 
                    '💾 本地存储: ' + companies.join(', ');
            } else {
                document.getElementById('local-companies').innerHTML = 
                    '📭 本地存储为空';
            }
        }
        
        async function testMergeLogic() {
            // 模拟前端的合并逻辑
            const companies = await loadBackendCompanies();
            if (!companies) return;
            
            const savedColumns = localStorage.getItem('supervision-visible-companies');
            let visibleCompanies;
            
            if (savedColumns) {
                const savedCompanies = JSON.parse(savedColumns);
                console.log('本地保存的公司:', savedCompanies);
                // 合并保存的公司和新的公司，去重
                const mergedCompanies = [...new Set([...savedCompanies, ...companies])];
                // 只保留实际存在的公司
                visibleCompanies = mergedCompanies.filter(company => companies.includes(company));
            } else {
                // 如果没有保存的设置，显示所有公司
                visibleCompanies = [...companies];
            }
            
            console.log('最终可见公司:', visibleCompanies);
            
            // 更新本地存储
            localStorage.setItem('supervision-visible-companies', JSON.stringify(visibleCompanies));
            
            alert('合并逻辑测试完成，请查看控制台输出');
            showLocalStorage();
        }
        
        // 页面加载时显示本地存储
        showLocalStorage();
    </script>
</body>
</html>
