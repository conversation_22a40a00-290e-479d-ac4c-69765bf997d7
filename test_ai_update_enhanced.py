#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的AI更新功能
验证提示词显示和用户编辑功能
"""

import asyncio
import aiohttp
import json
import os

# 设置测试模式环境变量
os.environ["PMO_TEST_MODE"] = "true"

BASE_URL = "http://localhost:8000"
# 使用一个有真实档案文件的项目编号
TEST_PROJECT_CODE = "C202500012"

async def test_enhanced_ai_update():
    """测试增强的AI更新功能"""
    async with aiohttp.ClientSession() as session:
        print("🚀 测试增强的AI更新功能")
        print("=" * 60)
        
        # 不创建测试文件，使用真实的档案文件
        print("📁 使用真实的项目档案文件进行测试")
        
        # 测试AI更新分析
        print("🧪 测试AI更新分析...")
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test_token"
        }
        
        async with session.post(f"{BASE_URL}/api/v1/project/{TEST_PROJECT_CODE}/ai-update", 
                               headers=headers) as response:
            if response.status == 200:
                result = await response.json()
                print("✅ AI更新分析成功")
                
                # 检查步骤信息
                steps = result.get("data", {}).get("steps", {})
                
                print("\n📋 检查各步骤完成情况:")
                for step_key, step_info in steps.items():
                    status = step_info.get("status", "unknown")
                    message = step_info.get("message", "")
                    print(f"  {step_key}: {status} - {message}")
                
                # 检查步骤4：AI提示词
                step4 = steps.get("step4_generate_prompt", {})
                if step4.get("status") == "completed":
                    step4_data = step4.get("data", {})
                    prompt = step4_data.get("prompt", "")
                    prompt_length = step4_data.get("prompt_length", 0)
                    
                    print(f"\n📝 步骤4 - AI提示词检查:")
                    print(f"  ✅ 提示词长度: {prompt_length:,} 字符")
                    print(f"  ✅ 提示词内容预览: {prompt[:200]}...")
                    
                    # 验证提示词包含关键内容
                    if "当前项目信息" in prompt and "项目档案文件内容" in prompt:
                        print("  ✅ 提示词包含必要的项目信息和档案内容")
                    else:
                        print("  ❌ 提示词缺少关键内容")
                else:
                    print("  ❌ 步骤4未完成")
                
                # 检查步骤5：AI分析
                step5 = steps.get("step5_ai_analysis", {})
                if step5.get("status") == "completed":
                    step5_data = step5.get("data", {})
                    ai_response = step5_data.get("ai_response", "")
                    model_used = step5_data.get("model_used", "")
                    
                    print(f"\n🤖 步骤5 - AI分析检查:")
                    print(f"  ✅ 使用模型: {model_used}")
                    print(f"  ✅ AI回答长度: {len(ai_response):,} 字符")
                    print(f"  ✅ AI回答预览: {ai_response[:200]}...")
                    
                    # 验证AI回答是否包含表格格式
                    if "|" in ai_response and "字段名称" in ai_response:
                        print("  ✅ AI回答包含Markdown表格格式")
                    else:
                        print("  ❌ AI回答不包含预期的表格格式")
                else:
                    print("  ❌ 步骤5未完成")
                
                # 检查步骤6：用户确认
                step6 = steps.get("step6_user_confirm", {})
                if step6.get("status") == "ready":
                    step6_data = step6.get("data", {})
                    ready_for_confirmation = step6_data.get("ready_for_confirmation", False)
                    ai_response_markdown = step6_data.get("ai_response_markdown", "")
                    
                    print(f"\n✅ 步骤6 - 用户确认检查:")
                    print(f"  ✅ 准备确认状态: {ready_for_confirmation}")
                    print(f"  ✅ Markdown内容长度: {len(ai_response_markdown):,} 字符")
                    
                    if ready_for_confirmation:
                        print("  ✅ 系统已准备好接受用户确认")
                        
                        # 解析Markdown表格内容
                        fields = parse_markdown_table(ai_response_markdown)
                        print(f"  ✅ 解析到 {len(fields)} 个可更新字段:")
                        for field in fields:
                            print(f"    - {field['field']}: {field['current']} → {field['suggested']}")
                    else:
                        print("  ❌ 系统未准备好接受用户确认")
                else:
                    print("  ❌ 步骤6未完成")
                
                print(f"\n🎯 总结:")
                print(f"  - 总步骤数: 6")
                print(f"  - 完成步骤: {len([s for s in steps.values() if s.get('status') in ['completed', 'ready']])}")
                print(f"  - AI更新功能状态: {'✅ 完全可用' if step6.get('status') == 'ready' else '❌ 部分功能异常'}")
                
            else:
                print(f"❌ AI更新分析失败: HTTP {response.status}")
                error_text = await response.text()
                print(f"错误信息: {error_text}")

async def create_test_archive_files():
    """创建测试档案文件"""
    try:
        # 在后端目录下创建档案文件
        backend_dir = "pmo-backend"
        archive_dir = f"{backend_dir}/project_archive_materials/{TEST_PROJECT_CODE}"
        os.makedirs(archive_dir, exist_ok=True)
        
        # 创建更详细的测试markdown文件
        test_content = """# 项目立项文档

## 项目基本信息
- **项目名称**: AI更新功能测试项目
- **项目编号**: TEST_001
- **负责人**: 张三
- **负责部门**: 技术开发部
- **投资主体**: 数字金服
- **开始日期**: 2024-01-01
- **结束日期**: 2024-12-31
- **投资金额**: 2000000元
- **年度投资计划**: 1500000元

## 项目描述
这是一个用于测试AI更新功能的示例项目，主要验证系统能否正确解析档案文件内容并生成准确的更新建议。

项目包含以下主要功能：
1. 档案文件解析
2. AI智能分析
3. 用户确认界面
4. 数据更新功能

## 项目进度
当前项目处于**实施阶段**，已完成以下工作：
- ✅ 需求分析 (100%)
- ✅ 系统设计 (100%)
- 🔄 开发实施 (80%)
- ⏳ 系统测试 (0%)
- ⏳ 用户验收 (0%)

## 成本信息
- **总成本**: 1800000元
- **外包成本**: 800000元
- **人工成本**: 1000000元

## 分类信息
- **项目类别**: 信息化项目
- **投资类型**: 新建
- **一级分类**: 技术类
- **二级分类**: 系统开发
"""
        
        with open(os.path.join(archive_dir, "项目立项.md"), "w", encoding="utf-8") as f:
            f.write(test_content)
        
        print(f"📄 创建测试档案文件: {archive_dir}/项目立项.md")
        
    except Exception as e:
        print(f"❌ 创建测试档案文件失败: {str(e)}")

def parse_markdown_table(markdown):
    """解析Markdown表格"""
    fields = []
    lines = markdown.split('\n')
    
    for line in lines:
        if line.startswith('|') and line.endswith('|'):
            cells = line.split('|')[1:-1]  # 去掉首尾空元素
            cells = [cell.strip() for cell in cells]
            
            if len(cells) >= 3 and cells[0] and cells[1] and cells[2]:
                # 跳过表头和分隔行
                if not cells[0].startswith('-') and '字段名称' not in cells[0]:
                    fields.append({
                        'field': cells[0],
                        'current': cells[1],
                        'suggested': cells[2],
                        'reason': cells[3] if len(cells) > 3 else ''
                    })
    
    return fields

async def main():
    """主函数"""
    await test_enhanced_ai_update()

if __name__ == "__main__":
    asyncio.run(main())
