body {background: #f1f1f1;}
.container {padding: 0;}
.modal-dialog {width: 100%; margin-top: 20px;}
.modal-footer {text-align: center; margin-top: 0;}
.w-350px {width: 350px;}
.text-notice {padding-left: 20px; padding-bottom: 20px;}

.table,.alert {margin: 0;}
.table+.alert {margin-top: 20px;}
.table.table-form>thead>tr>th, .table.table-form>tbody>tr>th, .table.table-form>tfoot>tr>th {color: #666;}
.table>thead>tr>th {background-color: transparent;}
.table.table-form>thead>tr>th, .table.table-form>tbody>tr>th, .table.table-form>tfoot>tr>th, .table.table-form>thead>tr>td, .table.table-form>tbody>tr>td, .table.table-form>tfoot>tr>td {vertical-align: middle;}

@media (max-width: 700px) {.modal-dialog {padding: 0;} .modal-content {box-shadow: none; border-width: 1px 0; border-radius: 0;};}

#checker .ok {background: green; color: white;}
#checker .fail {background: red; color: white;}
