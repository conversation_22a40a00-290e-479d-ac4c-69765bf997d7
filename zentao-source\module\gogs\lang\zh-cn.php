<?php
$lang->gogs->common        = 'Gogs';
$lang->gogs->browse        = '浏览Gogs';
$lang->gogs->search        = '搜索';
$lang->gogs->create        = '添加Gogs';
$lang->gogs->edit          = '编辑Gogs';
$lang->gogs->view          = 'Gogs详情';
$lang->gogs->delete        = '删除Gogs';
$lang->gogs->confirmDelete = '确认删除该Gogs吗？';
$lang->gogs->bindUser      = '绑定用户';
$lang->gogs->gogsAvatar    = '头像';
$lang->gogs->gogsAccount   = 'Gogs用户';
$lang->gogs->gogsEmail     = '邮箱';
$lang->gogs->zentaoAccount = '禅道用户';
$lang->gogs->bindingStatus = '绑定状态';
$lang->gogs->all           = '全部';
$lang->gogs->notBind       = '未绑定';
$lang->gogs->binded        = '已绑定';
$lang->gogs->bindDynamic   = '%s与禅道用户%s';
$lang->gogs->bindedError   = '绑定的用户已删除或者已修改，请重新绑定';
$lang->gogs->zentaoEmail   = '禅道用户邮箱';
$lang->gogs->accountDesc   = '(系统会将相同邮箱地址的用户自动匹配)';

$lang->gogs->bindStatus['binded']      = $lang->gogs->binded;
$lang->gogs->bindStatus['notBind']     = "<span class='text-danger'>{$lang->gogs->notBind}</span>";
$lang->gogs->bindStatus['bindedError'] = "<span class='text-danger'>{$lang->gogs->bindedError}</span>";

$lang->gogs->browseAction = 'Gogs列表';
$lang->gogs->deleteAction = '删除Gogs';

$lang->gogs->id    = 'ID';
$lang->gogs->name  = "应用名称";
$lang->gogs->url   = '服务器地址';
$lang->gogs->token = 'Token';

$lang->gogs->tokenLimit    = "Gogs Token权限不足。";
$lang->gogs->hostError     = "当前Gogs服务器地址无效，请确认当前服务器可被访问";
$lang->gogs->bindUserError = "不能重复绑定用户 %s";

$lang->gogs->server        = "服务器列表";
$lang->gogs->lblCreate     = '添加Gogs服务器';
$lang->gogs->emptyError    = "不能为空";
$lang->gogs->createSuccess = "创建成功";
