import{t,m as e}from"./p-aa688caf.js";import"./p-7900c24a.js";import"./p-986e5fe7.js";
/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/var n=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,a=(t,e,a,o)=>{if(e&&"object"==typeof e||"function"==typeof e)for(let c of i(e))s.call(t,c)||c===a||n(t,c,{get:()=>e[c],enumerable:!(o=r(e,c))||o.enumerable});return t},o=(t,e,r)=>(((t,e,r)=>{e in t?n(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r})(t,"symbol"!=typeof e?e+"":e,r),r),c={};a(c,e,"default");var u=class{constructor(t,e){this._modeId=t,this._defaults=e,this._worker=null,this._client=null,this._configChangeListener=this._defaults.onDidChange((()=>this._stopWorker())),this._updateExtraLibsToken=0,this._extraLibsChangeListener=this._defaults.onDidExtraLibsChange((()=>this._updateExtraLibs()))}_configChangeListener;_updateExtraLibsToken;_extraLibsChangeListener;_worker;_client;dispose(){this._configChangeListener.dispose(),this._extraLibsChangeListener.dispose(),this._stopWorker()}_stopWorker(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null}async _updateExtraLibs(){if(!this._worker)return;const t=++this._updateExtraLibsToken,e=await this._worker.getProxy();this._updateExtraLibsToken===t&&e.updateExtraLibs(this._defaults.getExtraLibs())}_getClient(){return this._client||(this._client=(async()=>(this._worker=c.editor.createWebWorker({moduleId:"vs/language/typescript/tsWorker",label:this._modeId,keepIdleModels:!0,createData:{compilerOptions:this._defaults.getCompilerOptions(),extraLibs:this._defaults.getExtraLibs(),customWorkerPath:this._defaults.workerOptions.customWorkerPath,inlayHintsOptions:this._defaults.inlayHintsOptions}}),this._defaults.getEagerModelSync()?await this._worker.withSyncedResources(c.editor.getModels().filter((t=>t.getLanguageId()===this._modeId)).map((t=>t.uri))):await this._worker.getProxy()))()),this._client}async getLanguageServiceWorker(...t){const e=await this._getClient();return this._worker&&await this._worker.withSyncedResources(t),e}},l={};function h(t,e,n=0){if("string"==typeof t)return t;if(void 0===t)return"";let r="";if(n){r+=e;for(let t=0;t<n;t++)r+="  "}if(r+=t.messageText,n++,t.next)for(const i of t.next)r+=h(i,e,n);return r}function d(t){return t?t.map((t=>t.text)).join(""):""}l["lib.d.ts"]=!0,l["lib.decorators.d.ts"]=!0,l["lib.decorators.legacy.d.ts"]=!0,l["lib.dom.d.ts"]=!0,l["lib.dom.iterable.d.ts"]=!0,l["lib.es2015.collection.d.ts"]=!0,l["lib.es2015.core.d.ts"]=!0,l["lib.es2015.d.ts"]=!0,l["lib.es2015.generator.d.ts"]=!0,l["lib.es2015.iterable.d.ts"]=!0,l["lib.es2015.promise.d.ts"]=!0,l["lib.es2015.proxy.d.ts"]=!0,l["lib.es2015.reflect.d.ts"]=!0,l["lib.es2015.symbol.d.ts"]=!0,l["lib.es2015.symbol.wellknown.d.ts"]=!0,l["lib.es2016.array.include.d.ts"]=!0,l["lib.es2016.d.ts"]=!0,l["lib.es2016.full.d.ts"]=!0,l["lib.es2017.d.ts"]=!0,l["lib.es2017.full.d.ts"]=!0,l["lib.es2017.intl.d.ts"]=!0,l["lib.es2017.object.d.ts"]=!0,l["lib.es2017.sharedmemory.d.ts"]=!0,l["lib.es2017.string.d.ts"]=!0,l["lib.es2017.typedarrays.d.ts"]=!0,l["lib.es2018.asyncgenerator.d.ts"]=!0,l["lib.es2018.asynciterable.d.ts"]=!0,l["lib.es2018.d.ts"]=!0,l["lib.es2018.full.d.ts"]=!0,l["lib.es2018.intl.d.ts"]=!0,l["lib.es2018.promise.d.ts"]=!0,l["lib.es2018.regexp.d.ts"]=!0,l["lib.es2019.array.d.ts"]=!0,l["lib.es2019.d.ts"]=!0,l["lib.es2019.full.d.ts"]=!0,l["lib.es2019.intl.d.ts"]=!0,l["lib.es2019.object.d.ts"]=!0,l["lib.es2019.string.d.ts"]=!0,l["lib.es2019.symbol.d.ts"]=!0,l["lib.es2020.bigint.d.ts"]=!0,l["lib.es2020.d.ts"]=!0,l["lib.es2020.date.d.ts"]=!0,l["lib.es2020.full.d.ts"]=!0,l["lib.es2020.intl.d.ts"]=!0,l["lib.es2020.number.d.ts"]=!0,l["lib.es2020.promise.d.ts"]=!0,l["lib.es2020.sharedmemory.d.ts"]=!0,l["lib.es2020.string.d.ts"]=!0,l["lib.es2020.symbol.wellknown.d.ts"]=!0,l["lib.es2021.d.ts"]=!0,l["lib.es2021.full.d.ts"]=!0,l["lib.es2021.intl.d.ts"]=!0,l["lib.es2021.promise.d.ts"]=!0,l["lib.es2021.string.d.ts"]=!0,l["lib.es2021.weakref.d.ts"]=!0,l["lib.es2022.array.d.ts"]=!0,l["lib.es2022.d.ts"]=!0,l["lib.es2022.error.d.ts"]=!0,l["lib.es2022.full.d.ts"]=!0,l["lib.es2022.intl.d.ts"]=!0,l["lib.es2022.object.d.ts"]=!0,l["lib.es2022.regexp.d.ts"]=!0,l["lib.es2022.sharedmemory.d.ts"]=!0,l["lib.es2022.string.d.ts"]=!0,l["lib.es2023.array.d.ts"]=!0,l["lib.es2023.d.ts"]=!0,l["lib.es2023.full.d.ts"]=!0,l["lib.es5.d.ts"]=!0,l["lib.es6.d.ts"]=!0,l["lib.esnext.d.ts"]=!0,l["lib.esnext.full.d.ts"]=!0,l["lib.esnext.intl.d.ts"]=!0,l["lib.scripthost.d.ts"]=!0,l["lib.webworker.d.ts"]=!0,l["lib.webworker.importscripts.d.ts"]=!0,l["lib.webworker.iterable.d.ts"]=!0;var f=class{constructor(t){this._worker=t}_textSpanToRange(t,e){let n=t.getPositionAt(e.start),r=t.getPositionAt(e.start+e.length),{lineNumber:i,column:s}=n,{lineNumber:a,column:o}=r;return{startLineNumber:i,startColumn:s,endLineNumber:a,endColumn:o}}},m=class{constructor(t){this._worker=t,this._libFiles={},this._hasFetchedLibFiles=!1,this._fetchLibFilesPromise=null}_libFiles;_hasFetchedLibFiles;_fetchLibFilesPromise;isLibFile(t){return!!t&&0===t.path.indexOf("/lib.")&&!!l[t.path.slice(1)]}getOrCreateModel(e){const n=c.Uri.parse(e),r=c.editor.getModel(n);if(r)return r;if(this.isLibFile(n)&&this._hasFetchedLibFiles)return c.editor.createModel(this._libFiles[n.path.slice(1)],"typescript",n);const i=t.getExtraLibs()[e];return i?c.editor.createModel(i.content,"typescript",n):null}_containsLibFile(t){for(let e of t)if(this.isLibFile(e))return!0;return!1}async fetchLibFilesIfNecessary(t){this._containsLibFile(t)&&await this._fetchLibFiles()}_fetchLibFiles(){return this._fetchLibFilesPromise||(this._fetchLibFilesPromise=this._worker().then((t=>t.getLibFiles())).then((t=>{this._hasFetchedLibFiles=!0,this._libFiles=t}))),this._fetchLibFilesPromise}},p=class extends f{constructor(t,e,n,r){super(r),this._libFiles=t,this._defaults=e,this._selector=n;const i=t=>{if(t.getLanguageId()!==n)return;const e=()=>{const{onlyVisible:e}=this._defaults.getDiagnosticsOptions();e?t.isAttachedToEditor()&&this._doValidate(t):this._doValidate(t)};let r;const i=t.onDidChangeContent((()=>{clearTimeout(r),r=window.setTimeout(e,500)})),s=t.onDidChangeAttached((()=>{const{onlyVisible:n}=this._defaults.getDiagnosticsOptions();n&&(t.isAttachedToEditor()?e():c.editor.setModelMarkers(t,this._selector,[]))}));this._listener[t.uri.toString()]={dispose(){i.dispose(),s.dispose(),clearTimeout(r)}},e()},s=t=>{c.editor.setModelMarkers(t,this._selector,[]);const e=t.uri.toString();this._listener[e]&&(this._listener[e].dispose(),delete this._listener[e])};this._disposables.push(c.editor.onDidCreateModel((t=>i(t)))),this._disposables.push(c.editor.onWillDisposeModel(s)),this._disposables.push(c.editor.onDidChangeModelLanguage((t=>{s(t.model),i(t.model)}))),this._disposables.push({dispose(){for(const t of c.editor.getModels())s(t)}});const a=()=>{for(const t of c.editor.getModels())s(t),i(t)};this._disposables.push(this._defaults.onDidChange(a)),this._disposables.push(this._defaults.onDidExtraLibsChange(a)),c.editor.getModels().forEach((t=>i(t)))}_disposables=[];_listener=Object.create(null);dispose(){this._disposables.forEach((t=>t&&t.dispose())),this._disposables=[]}async _doValidate(t){const e=await this._worker(t.uri);if(t.isDisposed())return;const n=[],{noSyntaxValidation:r,noSemanticValidation:i,noSuggestionDiagnostics:s}=this._defaults.getDiagnosticsOptions();r||n.push(e.getSyntacticDiagnostics(t.uri.toString())),i||n.push(e.getSemanticDiagnostics(t.uri.toString())),s||n.push(e.getSuggestionDiagnostics(t.uri.toString()));const a=await Promise.all(n);if(!a||t.isDisposed())return;const o=a.reduce(((t,e)=>e.concat(t)),[]).filter((t=>-1===(this._defaults.getDiagnosticsOptions().diagnosticCodesToIgnore||[]).indexOf(t.code))),u=o.map((t=>t.relatedInformation||[])).reduce(((t,e)=>e.concat(t)),[]).map((t=>t.file?c.Uri.parse(t.file.fileName):null));await this._libFiles.fetchLibFilesIfNecessary(u),t.isDisposed()||c.editor.setModelMarkers(t,this._selector,o.map((e=>this._convertDiagnostics(t,e))))}_convertDiagnostics(t,e){const n=e.start||0,r=e.length||1,{lineNumber:i,column:s}=t.getPositionAt(n),{lineNumber:a,column:o}=t.getPositionAt(n+r),u=[];return e.reportsUnnecessary&&u.push(c.MarkerTag.Unnecessary),e.reportsDeprecated&&u.push(c.MarkerTag.Deprecated),{severity:this._tsDiagnosticCategoryToMarkerSeverity(e.category),startLineNumber:i,startColumn:s,endLineNumber:a,endColumn:o,message:h(e.messageText,"\n"),code:e.code.toString(),tags:u,relatedInformation:this._convertRelatedInformation(t,e.relatedInformation)}}_convertRelatedInformation(t,e){if(!e)return[];const n=[];return e.forEach((e=>{let r=t;if(e.file&&(r=this._libFiles.getOrCreateModel(e.file.fileName)),!r)return;const i=e.start||0,s=e.length||1,{lineNumber:a,column:o}=r.getPositionAt(i),{lineNumber:c,column:u}=r.getPositionAt(i+s);n.push({resource:r.uri,startLineNumber:a,startColumn:o,endLineNumber:c,endColumn:u,message:h(e.messageText,"\n")})})),n}_tsDiagnosticCategoryToMarkerSeverity(t){switch(t){case 1:return c.MarkerSeverity.Error;case 3:return c.MarkerSeverity.Info;case 0:return c.MarkerSeverity.Warning;case 2:return c.MarkerSeverity.Hint}return c.MarkerSeverity.Info}},g=class extends f{get triggerCharacters(){return["."]}async provideCompletionItems(t,e,n,r){const i=t.getWordUntilPosition(e),s=new c.Range(e.lineNumber,i.startColumn,e.lineNumber,i.endColumn),a=t.uri,o=t.getOffsetAt(e),u=await this._worker(a);if(t.isDisposed())return;const l=await u.getCompletionsAtPosition(a.toString(),o);return l&&!t.isDisposed()?{suggestions:l.entries.map((n=>{let r=s;if(n.replacementSpan){const e=t.getPositionAt(n.replacementSpan.start),i=t.getPositionAt(n.replacementSpan.start+n.replacementSpan.length);r=new c.Range(e.lineNumber,e.column,i.lineNumber,i.column)}const i=[];return void 0!==n.kindModifiers&&-1!==n.kindModifiers.indexOf("deprecated")&&i.push(c.languages.CompletionItemTag.Deprecated),{uri:a,position:e,offset:o,range:r,label:n.name,insertText:n.name,sortText:n.sortText,kind:g.convertKind(n.kind),tags:i}}))}:void 0}async resolveCompletionItem(t,e){const n=t,r=n.uri,i=n.position,s=n.offset,a=await this._worker(r),o=await a.getCompletionEntryDetails(r.toString(),s,n.label);return o?{uri:r,position:i,label:o.name,kind:g.convertKind(o.kind),detail:d(o.displayParts),documentation:{value:g.createDocumentationString(o)}}:n}static convertKind(t){switch(t){case k.primitiveType:case k.keyword:return c.languages.CompletionItemKind.Keyword;case k.variable:case k.localVariable:return c.languages.CompletionItemKind.Variable;case k.memberVariable:case k.memberGetAccessor:case k.memberSetAccessor:return c.languages.CompletionItemKind.Field;case k.function:case k.memberFunction:case k.constructSignature:case k.callSignature:case k.indexSignature:return c.languages.CompletionItemKind.Function;case k.enum:return c.languages.CompletionItemKind.Enum;case k.module:return c.languages.CompletionItemKind.Module;case k.class:return c.languages.CompletionItemKind.Class;case k.interface:return c.languages.CompletionItemKind.Interface;case k.warning:return c.languages.CompletionItemKind.File}return c.languages.CompletionItemKind.Property}static createDocumentationString(t){let e=d(t.documentation);if(t.tags)for(const n of t.tags)e+=`\n\n${w(n)}`;return e}};function w(t){let e=`*@${t.name}*`;if("param"===t.name&&t.text){const[n,...r]=t.text;e+=`\`${n.text}\``,r.length>0&&(e+=` — ${r.map((t=>t.text)).join(" ")}`)}else Array.isArray(t.text)?e+=` — ${t.text.map((t=>t.text)).join(" ")}`:t.text&&(e+=` — ${t.text}`);return e}var b=class extends f{signatureHelpTriggerCharacters=["(",","];static _toSignatureHelpTriggerReason(t){switch(t.triggerKind){case c.languages.SignatureHelpTriggerKind.TriggerCharacter:return t.triggerCharacter?t.isRetrigger?{kind:"retrigger",triggerCharacter:t.triggerCharacter}:{kind:"characterTyped",triggerCharacter:t.triggerCharacter}:{kind:"invoked"};case c.languages.SignatureHelpTriggerKind.ContentChange:return t.isRetrigger?{kind:"retrigger"}:{kind:"invoked"};default:return{kind:"invoked"}}}async provideSignatureHelp(t,e,n,r){const i=t.uri,s=t.getOffsetAt(e),a=await this._worker(i);if(t.isDisposed())return;const o=await a.getSignatureHelpItems(i.toString(),s,{triggerReason:b._toSignatureHelpTriggerReason(r)});if(!o||t.isDisposed())return;const c={activeSignature:o.selectedItemIndex,activeParameter:o.argumentIndex,signatures:[]};return o.items.forEach((t=>{const e={label:"",parameters:[]};e.documentation={value:d(t.documentation)},e.label+=d(t.prefixDisplayParts),t.parameters.forEach(((n,r,i)=>{const s=d(n.displayParts),a={label:s,documentation:{value:d(n.documentation)}};e.label+=s,e.parameters.push(a),r<i.length-1&&(e.label+=d(t.separatorDisplayParts))})),e.label+=d(t.suffixDisplayParts),c.signatures.push(e)})),{value:c,dispose(){}}}},v=class extends f{async provideHover(t,e,n){const r=t.uri,i=t.getOffsetAt(e),s=await this._worker(r);if(t.isDisposed())return;const a=await s.getQuickInfoAtPosition(r.toString(),i);if(!a||t.isDisposed())return;const o=d(a.documentation),c=a.tags?a.tags.map((t=>w(t))).join("  \n\n"):"",u=d(a.displayParts);return{range:this._textSpanToRange(t,a.textSpan),contents:[{value:"```typescript\n"+u+"\n```\n"},{value:o+(c?"\n\n"+c:"")}]}}},y=class extends f{async provideDocumentHighlights(t,e,n){const r=t.uri,i=t.getOffsetAt(e),s=await this._worker(r);if(t.isDisposed())return;const a=await s.getDocumentHighlights(r.toString(),i,[r.toString()]);return a&&!t.isDisposed()?a.flatMap((e=>e.highlightSpans.map((e=>({range:this._textSpanToRange(t,e.textSpan),kind:"writtenReference"===e.kind?c.languages.DocumentHighlightKind.Write:c.languages.DocumentHighlightKind.Text}))))):void 0}},x=class extends f{constructor(t,e){super(e),this._libFiles=t}async provideDefinition(t,e,n){const r=t.uri,i=t.getOffsetAt(e),s=await this._worker(r);if(t.isDisposed())return;const a=await s.getDefinitionAtPosition(r.toString(),i);if(!a||t.isDisposed())return;if(await this._libFiles.fetchLibFilesIfNecessary(a.map((t=>c.Uri.parse(t.fileName)))),t.isDisposed())return;const o=[];for(let t of a){const e=this._libFiles.getOrCreateModel(t.fileName);e&&o.push({uri:e.uri,range:this._textSpanToRange(e,t.textSpan)})}return o}},S=class extends f{constructor(t,e){super(e),this._libFiles=t}async provideReferences(t,e,n,r){const i=t.uri,s=t.getOffsetAt(e),a=await this._worker(i);if(t.isDisposed())return;const o=await a.getReferencesAtPosition(i.toString(),s);if(!o||t.isDisposed())return;if(await this._libFiles.fetchLibFilesIfNecessary(o.map((t=>c.Uri.parse(t.fileName)))),t.isDisposed())return;const u=[];for(let t of o){const e=this._libFiles.getOrCreateModel(t.fileName);e&&u.push({uri:e.uri,range:this._textSpanToRange(e,t.textSpan)})}return u}},C=class extends f{async provideDocumentSymbols(t,e){const n=t.uri,r=await this._worker(n);if(t.isDisposed())return;const i=await r.getNavigationTree(n.toString());if(!i||t.isDisposed())return;const s=(e,n)=>({name:e.text,detail:"",kind:N[e.kind]||c.languages.SymbolKind.Variable,range:this._textSpanToRange(t,e.spans[0]),selectionRange:this._textSpanToRange(t,e.spans[0]),tags:[],children:e.childItems?.map((t=>s(t,e.text))),containerName:n});return i.childItems?i.childItems.map((t=>s(t))):[]}},k=class{};o(k,"unknown",""),o(k,"keyword","keyword"),o(k,"script","script"),o(k,"module","module"),o(k,"class","class"),o(k,"interface","interface"),o(k,"type","type"),o(k,"enum","enum"),o(k,"variable","var"),o(k,"localVariable","local var"),o(k,"function","function"),o(k,"localFunction","local function"),o(k,"memberFunction","method"),o(k,"memberGetAccessor","getter"),o(k,"memberSetAccessor","setter"),o(k,"memberVariable","property"),o(k,"constructorImplementation","constructor"),o(k,"callSignature","call"),o(k,"indexSignature","index"),o(k,"constructSignature","construct"),o(k,"parameter","parameter"),o(k,"typeParameter","type parameter"),o(k,"primitiveType","primitive type"),o(k,"label","label"),o(k,"alias","alias"),o(k,"const","const"),o(k,"let","let"),o(k,"warning","warning");var N=Object.create(null);N[k.module]=c.languages.SymbolKind.Module,N[k.class]=c.languages.SymbolKind.Class,N[k.enum]=c.languages.SymbolKind.Enum,N[k.interface]=c.languages.SymbolKind.Interface,N[k.memberFunction]=c.languages.SymbolKind.Method,N[k.memberVariable]=c.languages.SymbolKind.Property,N[k.memberGetAccessor]=c.languages.SymbolKind.Property,N[k.memberSetAccessor]=c.languages.SymbolKind.Property,N[k.variable]=c.languages.SymbolKind.Variable,N[k.const]=c.languages.SymbolKind.Variable,N[k.localVariable]=c.languages.SymbolKind.Variable,N[k.variable]=c.languages.SymbolKind.Variable,N[k.function]=c.languages.SymbolKind.Function,N[k.localFunction]=c.languages.SymbolKind.Function;var _,I,F=class extends f{static _convertOptions(t){return{ConvertTabsToSpaces:t.insertSpaces,TabSize:t.tabSize,IndentSize:t.tabSize,IndentStyle:2,NewLineCharacter:"\n",InsertSpaceAfterCommaDelimiter:!0,InsertSpaceAfterSemicolonInForStatements:!0,InsertSpaceBeforeAndAfterBinaryOperators:!0,InsertSpaceAfterKeywordsInControlFlowStatements:!0,InsertSpaceAfterFunctionKeywordForAnonymousFunctions:!0,InsertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis:!1,InsertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets:!1,InsertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces:!1,PlaceOpenBraceOnNewLineForControlBlocks:!1,PlaceOpenBraceOnNewLineForFunctions:!1}}_convertTextChanges(t,e){return{text:e.newText,range:this._textSpanToRange(t,e.span)}}},L=class extends F{canFormatMultipleRanges=!1;async provideDocumentRangeFormattingEdits(t,e,n,r){const i=t.uri,s=t.getOffsetAt({lineNumber:e.startLineNumber,column:e.startColumn}),a=t.getOffsetAt({lineNumber:e.endLineNumber,column:e.endColumn}),o=await this._worker(i);if(t.isDisposed())return;const c=await o.getFormattingEditsForRange(i.toString(),s,a,F._convertOptions(n));return c&&!t.isDisposed()?c.map((e=>this._convertTextChanges(t,e))):void 0}},O=class extends F{get autoFormatTriggerCharacters(){return[";","}","\n"]}async provideOnTypeFormattingEdits(t,e,n,r,i){const s=t.uri,a=t.getOffsetAt(e),o=await this._worker(s);if(t.isDisposed())return;const c=await o.getFormattingEditsAfterKeystroke(s.toString(),a,n,F._convertOptions(r));return c&&!t.isDisposed()?c.map((e=>this._convertTextChanges(t,e))):void 0}},T=class extends F{async provideCodeActions(t,e,n,r){const i=t.uri,s=t.getOffsetAt({lineNumber:e.startLineNumber,column:e.startColumn}),a=t.getOffsetAt({lineNumber:e.endLineNumber,column:e.endColumn}),o=F._convertOptions(t.getOptions()),c=n.markers.filter((t=>t.code)).map((t=>t.code)).map(Number),u=await this._worker(i);if(t.isDisposed())return;const l=await u.getCodeFixesAtPosition(i.toString(),s,a,c,o);return!l||t.isDisposed()?{actions:[],dispose:()=>{}}:{actions:l.filter((t=>0===t.changes.filter((t=>t.isNewFile)).length)).map((e=>this._tsCodeFixActionToMonacoCodeAction(t,n,e))),dispose:()=>{}}}_tsCodeFixActionToMonacoCodeAction(t,e,n){const r=[];for(const e of n.changes)for(const n of e.textChanges)r.push({resource:t.uri,versionId:void 0,textEdit:{range:this._textSpanToRange(t,n.span),text:n.newText}});return{title:n.description,edit:{edits:r},diagnostics:e.markers,kind:"quickfix"}}},A=class extends f{constructor(t,e){super(e),this._libFiles=t}async provideRenameEdits(t,e,n,r){const i=t.uri,s=i.toString(),a=t.getOffsetAt(e),o=await this._worker(i);if(t.isDisposed())return;const c=await o.getRenameInfo(s,a,{allowRenameOfImportPath:!1});if(!1===c.canRename)return{edits:[],rejectReason:c.localizedErrorMessage};if(void 0!==c.fileToRename)throw new Error("Renaming files is not supported.");const u=await o.findRenameLocations(s,a,!1,!1,!1);if(!u||t.isDisposed())return;const l=[];for(const t of u){const e=this._libFiles.getOrCreateModel(t.fileName);if(!e)throw new Error(`Unknown file ${t.fileName}.`);l.push({resource:e.uri,versionId:void 0,textEdit:{range:this._textSpanToRange(e,t.textSpan),text:n}})}return{edits:l}}},R=class extends f{async provideInlayHints(t,e,n){const r=t.uri,i=r.toString(),s=t.getOffsetAt({lineNumber:e.startLineNumber,column:e.startColumn}),a=t.getOffsetAt({lineNumber:e.endLineNumber,column:e.endColumn}),o=await this._worker(r);return t.isDisposed()?null:{hints:(await o.provideInlayHints(i,s,a)).map((e=>({...e,label:e.text,position:t.getPositionAt(e.position),kind:this._convertHintKind(e.kind)}))),dispose:()=>{}}}_convertHintKind(t){return"Parameter"===t?c.languages.InlayHintKind.Parameter:c.languages.InlayHintKind.Type}};function j(t){I=E(t,"typescript")}function P(t){_=E(t,"javascript")}function B(){return new Promise(((t,e)=>{if(!_)return e("JavaScript not registered!");t(_)}))}function D(){return new Promise(((t,e)=>{if(!I)return e("TypeScript not registered!");t(I)}))}function E(t,e){const n=[],r=new u(e,t),i=(...t)=>r.getLanguageServiceWorker(...t),s=new m(i);return function(){const{modeConfiguration:r}=t;!function(t){for(;t.length;)t.pop().dispose()}(n),r.completionItems&&n.push(c.languages.registerCompletionItemProvider(e,new g(i))),r.signatureHelp&&n.push(c.languages.registerSignatureHelpProvider(e,new b(i))),r.hovers&&n.push(c.languages.registerHoverProvider(e,new v(i))),r.documentHighlights&&n.push(c.languages.registerDocumentHighlightProvider(e,new y(i))),r.definitions&&n.push(c.languages.registerDefinitionProvider(e,new x(s,i))),r.references&&n.push(c.languages.registerReferenceProvider(e,new S(s,i))),r.documentSymbols&&n.push(c.languages.registerDocumentSymbolProvider(e,new C(i))),r.rename&&n.push(c.languages.registerRenameProvider(e,new A(s,i))),r.documentRangeFormattingEdits&&n.push(c.languages.registerDocumentRangeFormattingEditProvider(e,new L(i))),r.onTypeFormattingEdits&&n.push(c.languages.registerOnTypeFormattingEditProvider(e,new O(i))),r.codeActions&&n.push(c.languages.registerCodeActionProvider(e,new T(i))),r.inlayHints&&n.push(c.languages.registerInlayHintsProvider(e,new R(i))),r.diagnostics&&n.push(new p(s,t,e,i))}(),i}export{f as Adapter,T as CodeActionAdaptor,x as DefinitionAdapter,p as DiagnosticsAdapter,y as DocumentHighlightAdapter,L as FormatAdapter,F as FormatHelper,O as FormatOnTypeAdapter,R as InlayHintsAdapter,k as Kind,m as LibFiles,C as OutlineAdapter,v as QuickInfoAdapter,S as ReferenceAdapter,A as RenameAdapter,b as SignatureHelpAdapter,g as SuggestAdapter,u as WorkerManager,h as flattenDiagnosticMessageText,B as getJavaScriptWorker,D as getTypeScriptWorker,P as setupJavaScript,j as setupTypeScript}