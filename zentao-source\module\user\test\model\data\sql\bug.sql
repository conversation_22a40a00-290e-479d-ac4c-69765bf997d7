-- MySQL dump 10.19  Distrib 10.3.37-MariaD<PERSON>, for debian-linux-gnu (x86_64)
--
-- Host: 127.0.0.1    Database: zentaotmp0104
-- ------------------------------------------------------
-- Server version	10.3.34-MariaDB-0ubuntu0.20.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `zt_bug`
--

DROP TABLE IF EXISTS `zt_bug`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `zt_bug` (
  `id` mediumint(8) NOT NULL AUTO_INCREMENT,
  `project` mediumint(8) unsigned NOT NULL,
  `product` mediumint(8) unsigned NOT NULL DEFAULT 0,
  `injection` mediumint(8) unsigned NOT NULL,
  `identify` mediumint(8) unsigned NOT NULL,
  `branch` mediumint(8) unsigned NOT NULL DEFAULT 0,
  `module` mediumint(8) unsigned NOT NULL DEFAULT 0,
  `execution` mediumint(8) unsigned NOT NULL DEFAULT 0,
  `plan` mediumint(8) unsigned NOT NULL DEFAULT 0,
  `story` mediumint(8) unsigned NOT NULL DEFAULT 0,
  `storyVersion` smallint(6) NOT NULL DEFAULT 1,
  `task` mediumint(8) unsigned NOT NULL DEFAULT 0,
  `toTask` mediumint(8) unsigned NOT NULL DEFAULT 0,
  `toStory` mediumint(8) NOT NULL DEFAULT 0,
  `title` varchar(255) NOT NULL,
  `keywords` varchar(255) NOT NULL,
  `severity` tinyint(4) NOT NULL DEFAULT 0,
  `pri` tinyint(3) unsigned NOT NULL,
  `type` varchar(30) NOT NULL DEFAULT '',
  `os` varchar(255) NOT NULL DEFAULT '',
  `browser` varchar(255) NOT NULL DEFAULT '',
  `hardware` varchar(30) NOT NULL,
  `found` varchar(30) NOT NULL DEFAULT '',
  `steps` mediumtext NOT NULL,
  `status` enum('active','resolved','closed') NOT NULL DEFAULT 'active',
  `subStatus` varchar(30) NOT NULL DEFAULT '',
  `color` char(7) NOT NULL,
  `confirmed` tinyint(1) NOT NULL DEFAULT 0,
  `activatedCount` smallint(6) NOT NULL,
  `activatedDate` datetime NOT NULL,
  `feedbackBy` varchar(100) NOT NULL,
  `notifyEmail` varchar(100) NOT NULL,
  `mailto` text DEFAULT NULL,
  `openedBy` varchar(30) NOT NULL DEFAULT '',
  `openedDate` datetime NOT NULL,
  `openedBuild` varchar(255) NOT NULL,
  `assignedTo` varchar(30) NOT NULL DEFAULT '',
  `assignedDate` datetime NOT NULL,
  `deadline` date NOT NULL,
  `resolvedBy` varchar(30) NOT NULL DEFAULT '',
  `resolution` varchar(30) NOT NULL DEFAULT '',
  `resolvedBuild` varchar(30) NOT NULL DEFAULT '',
  `resolvedDate` datetime NOT NULL,
  `closedBy` varchar(30) NOT NULL DEFAULT '',
  `closedDate` datetime NOT NULL,
  `duplicateBug` mediumint(8) unsigned NOT NULL,
  `linkBug` varchar(255) NOT NULL,
  `case` mediumint(8) unsigned NOT NULL,
  `caseVersion` smallint(6) NOT NULL DEFAULT 1,
  `feedback` mediumint(8) unsigned NOT NULL DEFAULT 0,
  `result` mediumint(8) unsigned NOT NULL,
  `repo` mediumint(8) unsigned NOT NULL,
  `mr` mediumint(8) unsigned NOT NULL,
  `entry` text NOT NULL,
  `lines` varchar(10) NOT NULL,
  `v1` varchar(40) NOT NULL,
  `v2` varchar(40) NOT NULL,
  `repoType` varchar(30) NOT NULL DEFAULT '',
  `issueKey` varchar(50) NOT NULL DEFAULT '',
  `testtask` mediumint(8) unsigned NOT NULL,
  `lastEditedBy` varchar(30) NOT NULL DEFAULT '',
  `lastEditedDate` datetime NOT NULL,
  `deleted` enum('0','1') NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `product` (`product`),
  KEY `execution` (`execution`),
  KEY `status` (`status`),
  KEY `plan` (`plan`),
  KEY `story` (`story`),
  KEY `case` (`case`),
  KEY `toStory` (`toStory`),
  KEY `result` (`result`),
  KEY `assignedTo` (`assignedTo`)
) ENGINE=InnoDB AUTO_INCREMENT=501 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zt_bug`
--

LOCK TABLES `zt_bug` WRITE;
/*!40000 ALTER TABLE `zt_bug` DISABLE KEYS */;
INSERT INTO `zt_bug` VALUES (1,11,1,0,0,0,1821,101,1,2,1,0,0,0,'BUG1','',1,1,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-14 15:33:08','1','admin','2022-12-14 00:00:00','2022-12-14','','','','2023-01-21 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(2,11,1,0,0,0,1822,101,1,6,1,0,0,0,'BUG2','',2,2,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-15 15:33:08','0','admin','2022-12-15 00:00:00','2022-12-15','','','','2023-01-20 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(3,11,1,0,0,0,1823,101,1,10,1,0,0,0,'BUG3','',3,3,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-16 15:33:08','1','admin','2022-12-16 00:00:00','2022-12-16','','','','2023-01-19 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(4,12,2,0,0,0,1825,102,4,14,1,0,0,0,'BUG4','',4,4,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-17 15:33:08','trunk','admin','2022-12-17 00:00:00','2022-12-17','','','','2023-01-18 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(5,12,2,0,0,0,1826,102,4,18,1,0,0,0,'BUG5','',1,1,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-18 15:33:08','trunk','admin','2022-12-18 00:00:00','2022-12-18','','','','2023-01-17 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(6,12,2,0,0,0,1827,102,4,22,1,0,0,0,'BUG6','',2,2,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-19 15:33:08','trunk','admin','2022-12-19 00:00:00','2022-12-19','','','','2023-01-16 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(7,13,3,0,0,0,1831,103,7,26,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;7','',3,3,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-20 15:33:08','trunk','admin','2022-12-20 00:00:00','2022-12-20','','','','2023-01-15 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(8,13,3,0,0,0,1832,103,7,30,1,0,0,0,'bug8','',4,4,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-21 15:33:08','trunk','admin','2022-12-21 00:00:00','2022-12-21','','','','2023-01-14 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(9,13,3,0,0,0,1833,103,7,34,1,0,0,0,'BUG9','',1,1,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-22 15:33:08','trunk','admin','2022-12-22 00:00:00','2022-12-22','','','','2023-01-13 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(10,14,4,0,0,0,0,104,0,38,1,0,0,0,'BUG10','',2,2,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-23 15:33:08','trunk','admin','2022-12-23 00:00:00','2022-12-23','','','','2023-01-12 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(11,14,4,0,0,0,0,104,0,42,1,0,0,0,'BUG11','',3,3,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-24 15:33:08','trunk','admin','2022-12-24 00:00:00','2022-12-24','','','','2023-01-11 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(12,14,4,0,0,0,0,104,0,46,1,0,0,0,'BUG12','',4,4,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-25 15:33:08','trunk','admin','2022-12-25 00:00:00','2022-12-25','','','','2023-01-10 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(13,15,5,0,0,0,0,105,0,50,1,0,0,0,'BUG13','',1,1,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-26 15:33:08','trunk','admin','2022-12-26 00:00:00','2022-12-26','','','','2023-01-09 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(14,15,5,0,0,0,0,105,0,54,1,0,0,0,'BUG14','',2,2,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-27 15:33:08','trunk','admin','2022-12-27 00:00:00','2022-12-27','','','','2023-01-08 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(15,15,5,0,0,0,0,105,0,58,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;15','',3,3,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-28 15:33:08','trunk','admin','2022-12-28 00:00:00','2022-12-28','','','','2023-01-07 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(16,16,6,0,0,0,0,106,0,62,1,0,0,0,'bug16','',4,4,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-29 15:33:08','trunk','admin','2022-12-29 00:00:00','2022-12-29','','','','2023-01-06 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(17,16,6,0,0,0,0,106,0,66,1,0,0,0,'BUG17','',1,1,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-30 15:33:08','trunk','admin','2022-12-30 00:00:00','2022-12-30','','','','2023-01-05 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(18,16,6,0,0,0,0,106,0,70,1,0,0,0,'BUG18','',2,2,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-31 15:33:08','trunk','admin','2022-12-31 00:00:00','2022-12-31','','','','2023-01-04 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(19,17,7,0,0,0,0,107,0,74,1,0,0,0,'BUG19','',3,3,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-01 15:33:08','trunk','admin','2023-01-01 00:00:00','2023-01-01','','','','2023-01-03 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(20,17,7,0,0,0,0,107,0,78,1,0,0,0,'BUG20','',4,4,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-02 15:33:08','trunk','admin','2023-01-02 00:00:00','2023-01-02','','','','2023-01-02 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(21,17,7,0,0,0,0,107,0,82,1,0,0,0,'BUG21','',1,1,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-03 15:33:08','trunk','admin','2023-01-03 00:00:00','2023-01-03','','','','2023-01-01 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(22,18,8,0,0,0,0,108,0,86,1,0,0,0,'BUG22','',2,2,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-04 15:33:08','trunk','admin','2023-01-04 00:00:00','2023-01-04','','','','2023-01-21 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(23,18,8,0,0,0,0,108,0,90,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;23','',3,3,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-05 15:33:08','trunk','admin','2023-01-05 00:00:00','2023-01-05','','','','2023-01-20 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(24,18,8,0,0,0,0,108,0,94,1,0,0,0,'bug24','',4,4,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-06 15:33:08','trunk','admin','2023-01-06 00:00:00','2023-01-06','','','','2023-01-19 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(25,19,9,0,0,0,0,109,0,98,1,0,0,0,'BUG25','',1,1,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-07 15:33:08','trunk','admin','2023-01-07 00:00:00','2023-01-07','','','','2023-01-18 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(26,19,9,0,0,0,0,109,0,102,1,0,0,0,'BUG26','',2,2,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-08 15:33:08','trunk','admin','2023-01-08 00:00:00','2023-01-08','','','','2023-01-17 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(27,19,9,0,0,0,0,109,0,106,1,0,0,0,'BUG27','',3,3,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-09 15:33:08','trunk','admin','2023-01-09 00:00:00','2023-01-09','','','','2023-01-16 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(28,20,10,0,0,0,0,110,0,110,1,0,0,0,'BUG28','',4,4,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-10 15:33:08','trunk','admin','2023-01-10 00:00:00','2023-01-10','','','','2023-01-15 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(29,20,10,0,0,0,0,110,0,114,1,0,0,0,'BUG29','',1,1,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-11 15:33:08','trunk','admin','2023-01-11 00:00:00','2023-01-11','','','','2023-01-14 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(30,20,10,0,0,0,0,110,0,118,1,0,0,0,'BUG30','',2,2,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-12 15:33:08','trunk','admin','2023-01-12 00:00:00','2023-01-12','','','','2023-01-13 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(31,21,11,0,0,0,0,111,0,122,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;31','',3,3,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-13 15:33:08','trunk','dev1','2023-01-13 00:00:00','2023-01-13','','','','2023-01-12 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(32,21,11,0,0,0,0,111,0,126,1,0,0,0,'bug32','',4,4,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-14 15:33:08','trunk','dev1','2023-01-14 00:00:00','2023-01-14','','','','2023-01-11 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(33,21,11,0,0,0,0,111,0,130,1,0,0,0,'BUG33','',1,1,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-15 15:33:08','trunk','dev1','2023-01-15 00:00:00','2023-01-15','','','','2023-01-10 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(34,22,12,0,0,0,0,112,0,134,1,0,0,0,'BUG34','',2,2,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-16 15:33:08','trunk','dev1','2023-01-16 00:00:00','2023-01-16','','','','2023-01-09 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(35,22,12,0,0,0,0,112,0,138,1,0,0,0,'BUG35','',3,3,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-17 15:33:08','trunk','dev1','2023-01-17 00:00:00','2023-01-17','','','','2023-01-08 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(36,22,12,0,0,0,0,112,0,142,1,0,0,0,'BUG36','',4,4,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-18 15:33:08','trunk','dev1','2023-01-18 00:00:00','2023-01-18','','','','2023-01-07 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(37,23,13,0,0,0,0,113,0,146,1,0,0,0,'BUG37','',1,1,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-19 15:33:08','trunk','dev1','2023-01-19 00:00:00','2023-01-19','','','','2023-01-06 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(38,23,13,0,0,0,0,113,0,150,1,0,0,0,'BUG38','',2,2,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-20 15:33:08','trunk','dev1','2023-01-20 00:00:00','2023-01-20','','','','2023-01-05 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(39,23,13,0,0,0,0,113,0,154,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;39','',3,3,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-14 15:33:08','trunk','dev1','2022-12-14 00:00:00','2022-12-14','','','','2023-01-04 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(40,24,14,0,0,0,0,114,0,158,1,0,0,0,'bug40','',4,4,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-15 15:33:08','trunk','dev1','2022-12-15 00:00:00','2022-12-15','','','','2023-01-03 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(41,24,14,0,0,0,0,114,0,162,1,0,0,0,'BUG41','',1,1,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-16 15:33:08','trunk','dev1','2022-12-16 00:00:00','2022-12-16','','','','2023-01-02 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(42,24,14,0,0,0,0,114,0,166,1,0,0,0,'BUG42','',2,2,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-17 15:33:08','trunk','dev1','2022-12-17 00:00:00','2022-12-17','','','','2023-01-01 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(43,25,15,0,0,0,0,115,0,170,1,0,0,0,'BUG43','',3,3,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-18 15:33:08','trunk','dev1','2022-12-18 00:00:00','2022-12-18','','','','2023-01-21 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(44,25,15,0,0,0,0,115,0,174,1,0,0,0,'BUG44','',4,4,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-19 15:33:08','trunk','dev1','2022-12-19 00:00:00','2022-12-19','','','','2023-01-20 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(45,25,15,0,0,0,0,115,0,178,1,0,0,0,'BUG45','',1,1,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-20 15:33:08','trunk','dev1','2022-12-20 00:00:00','2022-12-20','','','','2023-01-19 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(46,26,16,0,0,0,0,116,0,182,1,0,0,0,'BUG46','',2,2,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-21 15:33:08','trunk','dev1','2022-12-21 00:00:00','2022-12-21','','','','2023-01-18 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(47,26,16,0,0,0,0,116,0,186,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;47','',3,3,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-22 15:33:08','trunk','dev1','2022-12-22 00:00:00','2022-12-22','','','','2023-01-17 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(48,26,16,0,0,0,0,116,0,190,1,0,0,0,'bug48','',4,4,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-23 15:33:08','trunk','dev1','2022-12-23 00:00:00','2022-12-23','','','','2023-01-16 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(49,27,17,0,0,0,0,117,0,194,1,0,0,0,'BUG49','',1,1,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-24 15:33:08','trunk','dev1','2022-12-24 00:00:00','2022-12-24','','','','2023-01-15 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(50,27,17,0,0,0,0,117,0,198,1,0,0,0,'BUG50','',2,2,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-25 15:33:08','trunk','dev1','2022-12-25 00:00:00','2022-12-25','','','','2023-01-14 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(51,27,17,0,0,0,0,117,0,202,1,0,0,0,'BUG51','',3,3,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-26 15:33:08','trunk','dev1','2022-12-26 00:00:00','2022-12-26','$assignedTo','bydesign','','2023-01-13 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(52,28,18,0,0,0,0,118,0,206,1,0,0,0,'BUG52','',4,4,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-27 15:33:08','trunk','dev1','2022-12-27 00:00:00','2022-12-27','$assignedTo','bydesign','','2023-01-12 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(53,28,18,0,0,0,0,118,0,210,1,0,0,0,'BUG53','',1,1,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-28 15:33:08','trunk','dev1','2022-12-28 00:00:00','2022-12-28','$assignedTo','bydesign','','2023-01-11 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(54,28,18,0,0,0,0,118,0,214,1,0,0,0,'BUG54','',2,2,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-29 15:33:08','trunk','dev1','2022-12-29 00:00:00','2022-12-29','$assignedTo','bydesign','','2023-01-10 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(55,29,19,0,0,0,0,119,0,218,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;55','',3,3,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-30 15:33:08','trunk','dev1','2022-12-30 00:00:00','2022-12-30','$assignedTo','bydesign','','2023-01-09 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(56,29,19,0,0,0,0,119,0,222,1,0,0,0,'bug56','',4,4,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-31 15:33:08','trunk','dev1','2022-12-31 00:00:00','2022-12-31','$assignedTo','duplicate','trunk','2023-01-08 00:00:00','admin','0000-00-00 00:00:00',1,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(57,29,19,0,0,0,0,119,0,226,1,0,0,0,'BUG57','',1,1,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-01 15:33:08','trunk','dev1','2023-01-01 00:00:00','2023-01-01','$assignedTo','duplicate','trunk','2023-01-07 00:00:00','admin','0000-00-00 00:00:00',2,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(58,30,20,0,0,0,0,120,0,230,1,0,0,0,'BUG58','',2,2,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-02 15:33:08','trunk','dev1','2023-01-02 00:00:00','2023-01-02','$assignedTo','duplicate','trunk','2023-01-06 00:00:00','admin','0000-00-00 00:00:00',3,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(59,30,20,0,0,0,0,120,0,234,1,0,0,0,'BUG59','',3,3,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-03 15:33:08','trunk','dev1','2023-01-03 00:00:00','2023-01-03','$assignedTo','duplicate','trunk','2023-01-05 00:00:00','admin','0000-00-00 00:00:00',4,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(60,30,20,0,0,0,0,120,0,238,1,0,0,0,'BUG60','',4,4,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-04 15:33:08','trunk','dev1','2023-01-04 00:00:00','2023-01-04','$assignedTo','duplicate','trunk','2023-01-04 00:00:00','admin','0000-00-00 00:00:00',5,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(61,31,21,0,0,0,0,121,0,242,1,0,0,0,'BUG61','',1,1,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-05 15:33:08','trunk','test1','2023-01-05 00:00:00','2023-01-05','$assignedTo','external','trunk','2023-01-03 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(62,31,21,0,0,0,0,121,0,246,1,0,0,0,'BUG62','',2,2,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-06 15:33:08','trunk','test1','2023-01-06 00:00:00','2023-01-06','$assignedTo','external','trunk','2023-01-02 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(63,31,21,0,0,0,0,121,0,250,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;63','',3,3,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-07 15:33:08','trunk','test1','2023-01-07 00:00:00','2023-01-07','$assignedTo','external','trunk','2023-01-01 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(64,32,22,0,0,0,0,122,0,254,1,0,0,0,'bug64','',4,4,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-08 15:33:08','trunk','test1','2023-01-08 00:00:00','2023-01-08','$assignedTo','external','trunk','2023-01-21 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(65,32,22,0,0,0,0,122,0,258,1,0,0,0,'BUG65','',1,1,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-09 15:33:08','trunk','test1','2023-01-09 00:00:00','2023-01-09','$assignedTo','external','trunk','2023-01-20 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(66,32,22,0,0,0,0,122,0,262,1,0,0,0,'BUG66','',2,2,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-10 15:33:08','trunk','test1','2023-01-10 00:00:00','2023-01-10','$assignedTo','fixed','trunk','2023-01-19 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(67,33,23,0,0,0,0,123,0,266,1,0,0,0,'BUG67','',3,3,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-11 15:33:08','trunk','test1','2023-01-11 00:00:00','2023-01-11','$assignedTo','fixed','trunk','2023-01-18 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(68,33,23,0,0,0,0,123,0,270,1,0,0,0,'BUG68','',4,4,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-12 15:33:08','trunk','test1','2023-01-12 00:00:00','2023-01-12','$assignedTo','fixed','trunk','2023-01-17 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(69,33,23,0,0,0,0,123,0,274,1,0,0,0,'BUG69','',1,1,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-13 15:33:08','trunk','test1','2023-01-13 00:00:00','2023-01-13','$assignedTo','fixed','trunk','2023-01-16 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(70,34,24,0,0,0,0,124,0,278,1,0,0,0,'BUG70','',2,2,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-14 15:33:08','trunk','test1','2023-01-14 00:00:00','2023-01-14','$assignedTo','fixed','trunk','2023-01-15 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(71,34,24,0,0,0,0,124,0,282,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;71','',3,3,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-15 15:33:08','trunk','test1','2023-01-15 00:00:00','2023-01-15','$assignedTo','notrepro','','2023-01-14 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(72,34,24,0,0,0,0,124,0,286,1,0,0,0,'bug72','',4,4,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-16 15:33:08','trunk','test1','2023-01-16 00:00:00','2023-01-16','$assignedTo','notrepro','','2023-01-13 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(73,35,25,0,0,0,0,125,0,290,1,0,0,0,'BUG73','',1,1,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-17 15:33:08','trunk','test1','2023-01-17 00:00:00','2023-01-17','$assignedTo','postponed','','2023-01-12 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(74,35,25,0,0,0,0,125,0,294,1,0,0,0,'BUG74','',2,2,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-18 15:33:08','trunk','test1','2023-01-18 00:00:00','2023-01-18','$assignedTo','postponed','','2023-01-11 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(75,35,25,0,0,0,0,125,0,298,1,0,0,0,'BUG75','',3,3,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-19 15:33:08','trunk','test1','2023-01-19 00:00:00','2023-01-19','$assignedTo','postponed','','2023-01-10 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(76,36,26,0,0,0,0,126,0,302,1,0,0,0,'BUG76','',4,4,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-20 15:33:08','trunk','test1','2023-01-20 00:00:00','2023-01-20','$assignedTo','willnotfix','','2023-01-09 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(77,36,26,0,0,0,0,126,0,306,1,0,0,0,'BUG77','',1,1,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-14 15:33:08','trunk','test1','2022-12-14 00:00:00','2022-12-14','$assignedTo','willnotfix','','2023-01-08 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(78,36,26,0,0,0,0,126,0,310,1,0,0,0,'BUG78','',2,2,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-15 15:33:08','trunk','test1','2022-12-15 00:00:00','2022-12-15','$assignedTo','willnotfix','','2023-01-07 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(79,37,27,0,0,0,0,127,0,314,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;79','',3,3,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-16 15:33:08','trunk','test1','2022-12-16 00:00:00','2022-12-16','$assignedTo','willnotfix','','2023-01-06 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(80,37,27,0,0,0,0,127,0,318,1,0,0,0,'bug80','',4,4,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-17 15:33:08','trunk','test1','2022-12-17 00:00:00','2022-12-17','$assignedTo','willnotfix','','2023-01-05 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(81,37,27,0,0,0,0,127,0,322,1,0,0,0,'BUG81','',1,1,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-18 15:33:08','trunk','test1','2022-12-18 00:00:00','2022-12-18','admin','fixed','','2023-01-04 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(82,38,28,0,0,0,0,128,0,326,1,0,0,0,'BUG82','',2,2,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-19 15:33:08','trunk','test1','2022-12-19 00:00:00','2022-12-19','admin','fixed','','2023-01-03 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(83,38,28,0,0,0,0,128,0,330,1,0,0,0,'BUG83','',3,3,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-20 15:33:08','trunk','test1','2022-12-20 00:00:00','2022-12-20','admin','fixed','','2023-01-02 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(84,38,28,0,0,0,0,128,0,334,1,0,0,0,'BUG84','',4,4,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-21 15:33:08','trunk','test1','2022-12-21 00:00:00','2022-12-21','admin','fixed','','2023-01-01 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(85,39,29,0,0,0,0,129,0,338,1,0,0,0,'BUG85','',1,1,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-22 15:33:08','trunk','test1','2022-12-22 00:00:00','2022-12-22','admin','fixed','','2023-01-21 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(86,39,29,0,0,0,0,129,0,342,1,0,0,0,'BUG86','',2,2,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-23 15:33:08','trunk','test1','2022-12-23 00:00:00','2022-12-23','admin','fixed','','2023-01-20 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(87,39,29,0,0,0,0,129,0,346,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;87','',3,3,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-24 15:33:08','trunk','test1','2022-12-24 00:00:00','2022-12-24','admin','fixed','','2023-01-19 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(88,40,30,0,0,0,0,130,0,350,1,0,0,0,'bug88','',4,4,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-25 15:33:08','trunk','test1','2022-12-25 00:00:00','2022-12-25','admin','fixed','','2023-01-18 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(89,40,30,0,0,0,0,130,0,354,1,0,0,0,'BUG89','',1,1,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-26 15:33:08','trunk','test1','2022-12-26 00:00:00','2022-12-26','admin','fixed','','2023-01-17 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(90,40,30,0,0,0,0,130,0,358,1,0,0,0,'BUG90','',2,2,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-27 15:33:08','trunk','test1','2022-12-27 00:00:00','2022-12-27','admin','fixed','','2023-01-16 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(91,41,31,0,0,0,0,131,0,362,1,0,0,0,'BUG91','',3,3,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-28 15:33:08','trunk','','2022-12-28 00:00:00','2022-12-28','admin','bydesign','','2023-01-15 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(92,41,31,0,0,0,0,131,0,366,1,0,0,0,'BUG92','',4,4,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-29 15:33:08','trunk','','2022-12-29 00:00:00','2022-12-29','admin','external','','2023-01-14 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(93,41,31,0,0,0,0,131,0,370,1,0,0,0,'BUG93','',1,1,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-30 15:33:08','trunk','','2022-12-30 00:00:00','2022-12-30','admin','notrepro','','2023-01-13 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(94,42,32,0,0,0,0,132,0,374,1,0,0,0,'BUG94','',2,2,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2022-12-31 15:33:08','trunk','','2022-12-31 00:00:00','2022-12-31','admin','postponed','','2023-01-12 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(95,42,32,0,0,0,0,132,0,378,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;95','',3,3,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#ffaf38',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-01 15:33:08','trunk','','2023-01-01 00:00:00','2023-01-01','admin','willnotfix','','2023-01-11 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(96,42,32,0,0,0,0,132,0,382,1,0,0,0,'bug96','',4,4,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-02 15:33:08','trunk','','2023-01-02 00:00:00','2023-01-02','admin','duplicate','trunk','2023-01-10 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(97,43,33,0,0,0,0,133,0,386,1,0,0,0,'BUG97','',1,1,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#3da7f5',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-03 15:33:08','trunk','','2023-01-03 00:00:00','2023-01-03','admin','duplicate','trunk','2023-01-09 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(98,43,33,0,0,0,0,133,0,390,1,0,0,0,'BUG98','',2,2,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#75c941',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-04 15:33:08','trunk','','2023-01-04 00:00:00','2023-01-04','admin','duplicate','trunk','2023-01-08 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(99,43,33,0,0,0,0,133,0,394,1,0,0,0,'BUG99','',3,3,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-05 15:33:08','trunk','','2023-01-05 00:00:00','2023-01-05','admin','duplicate','trunk','2023-01-07 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(100,44,34,0,0,0,0,134,0,398,1,0,0,0,'BUG100','',4,4,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#797ec9',1,0,'0000-00-00 00:00:00','','','admin','admin','2023-01-06 15:33:08','trunk','','2023-01-06 00:00:00','2023-01-06','admin','duplicate','trunk','2023-01-06 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(101,44,34,0,0,0,0,134,0,2,1,0,0,0,'BUG101','',1,1,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-07 15:33:08','1','','2023-01-07 00:00:00','2023-01-07','','','','2023-01-05 00:00:00','','0000-00-00 00:00:00',10,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(102,44,34,0,0,0,0,134,0,6,1,0,0,0,'BUG102','',2,2,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-08 15:33:08','0','','2023-01-08 00:00:00','2023-01-08','','','','2023-01-04 00:00:00','','0000-00-00 00:00:00',11,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(103,45,35,0,0,0,0,135,0,10,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;103','',3,3,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-09 15:33:08','1','','2023-01-09 00:00:00','2023-01-09','','','','2023-01-03 00:00:00','','0000-00-00 00:00:00',12,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(104,45,35,0,0,0,0,135,0,14,1,0,0,0,'bug104','',4,4,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-10 15:33:08','trunk','','2023-01-10 00:00:00','2023-01-10','','','','2023-01-02 00:00:00','','0000-00-00 00:00:00',13,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(105,45,35,0,0,0,0,135,0,18,1,0,0,0,'BUG105','',1,1,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-11 15:33:08','trunk','','2023-01-11 00:00:00','2023-01-11','','','','2023-01-01 00:00:00','','0000-00-00 00:00:00',14,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(106,46,36,0,0,0,0,136,0,22,1,0,0,0,'BUG106','',2,2,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-12 15:33:08','trunk','','2023-01-12 00:00:00','2023-01-12','','','','2023-01-21 00:00:00','','0000-00-00 00:00:00',15,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(107,46,36,0,0,0,0,136,0,26,1,0,0,0,'BUG107','',3,3,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-13 15:33:08','trunk','','2023-01-13 00:00:00','2023-01-13','','','','2023-01-20 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(108,46,36,0,0,0,0,136,0,30,1,0,0,0,'BUG108','',4,4,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-14 15:33:08','trunk','','2023-01-14 00:00:00','2023-01-14','','','','2023-01-19 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(109,47,37,0,0,0,0,137,0,34,1,0,0,0,'BUG109','',1,1,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-15 15:33:08','trunk','','2023-01-15 00:00:00','2023-01-15','','','','2023-01-18 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(110,47,37,0,0,0,0,137,0,38,1,0,0,0,'BUG110','',2,2,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-16 15:33:08','trunk','','2023-01-16 00:00:00','2023-01-16','','','','2023-01-17 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(111,47,37,0,0,0,0,137,0,42,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;111','',3,3,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-17 15:33:08','trunk','admin','2023-01-17 00:00:00','2023-01-17','','','','2023-01-16 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(112,48,38,0,0,0,0,138,0,46,1,0,0,0,'bug112','',4,4,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-18 15:33:08','trunk','admin','2023-01-18 00:00:00','2023-01-18','','','','2023-01-15 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(113,48,38,0,0,0,0,138,0,50,1,0,0,0,'BUG113','',1,1,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-19 15:33:08','trunk','admin','2023-01-19 00:00:00','2023-01-19','','','','2023-01-14 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(114,48,38,0,0,0,0,138,0,54,1,0,0,0,'BUG114','',2,2,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-20 15:33:08','trunk','admin','2023-01-20 00:00:00','2023-01-20','','','','2023-01-13 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(115,49,39,0,0,0,0,139,0,58,1,0,0,0,'BUG115','',3,3,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-14 15:33:08','trunk','admin','2022-12-14 00:00:00','2022-12-14','','','','2023-01-12 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(116,49,39,0,0,0,0,139,0,62,1,0,0,0,'BUG116','',4,4,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-15 15:33:08','trunk','admin','2022-12-15 00:00:00','2022-12-15','','','','2023-01-11 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(117,49,39,0,0,0,0,139,0,66,1,0,0,0,'BUG117','',1,1,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-16 15:33:08','trunk','admin','2022-12-16 00:00:00','2022-12-16','','','','2023-01-10 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(118,50,40,0,0,0,0,140,0,70,1,0,0,0,'BUG118','',2,2,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-17 15:33:08','trunk','admin','2022-12-17 00:00:00','2022-12-17','','','','2023-01-09 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(119,50,40,0,0,0,0,140,0,74,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;119','',3,3,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-18 15:33:08','trunk','admin','2022-12-18 00:00:00','2022-12-18','','','','2023-01-08 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(120,50,40,0,0,0,0,140,0,78,1,0,0,0,'bug120','',4,4,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-19 15:33:08','trunk','admin','2022-12-19 00:00:00','2022-12-19','','','','2023-01-07 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(121,51,41,0,0,0,0,141,0,82,1,0,0,0,'BUG121','',1,1,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-20 15:33:08','trunk','admin','2022-12-20 00:00:00','2022-12-20','','','','2023-01-06 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(122,51,41,0,0,1,0,141,0,86,1,0,0,0,'BUG122','',2,2,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-21 15:33:08','trunk','admin','2022-12-21 00:00:00','2022-12-21','','','','2023-01-05 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(123,51,41,0,0,2,0,141,0,90,1,0,0,0,'BUG123','',3,3,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-22 15:33:08','trunk','admin','2022-12-22 00:00:00','2022-12-22','','','','2023-01-04 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(124,52,42,0,0,0,0,142,0,94,1,0,0,0,'BUG124','',4,4,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-23 15:33:08','trunk','admin','2022-12-23 00:00:00','2022-12-23','','','','2023-01-03 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(125,52,42,0,0,3,0,142,0,98,1,0,0,0,'BUG125','',1,1,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-24 15:33:08','trunk','admin','2022-12-24 00:00:00','2022-12-24','','','','2023-01-02 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(126,52,42,0,0,4,0,142,0,102,1,0,0,0,'BUG126','',2,2,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-25 15:33:08','trunk','admin','2022-12-25 00:00:00','2022-12-25','','','','2023-01-01 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(127,53,43,0,0,0,0,143,0,106,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;127','',3,3,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-26 15:33:08','trunk','admin','2022-12-26 00:00:00','2022-12-26','','','','2023-01-21 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(128,53,43,0,0,5,0,143,0,110,1,0,0,0,'bug128','',4,4,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-27 15:33:08','trunk','admin','2022-12-27 00:00:00','2022-12-27','','','','2023-01-20 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(129,53,43,0,0,6,0,143,0,114,1,0,0,0,'BUG129','',1,1,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-28 15:33:08','trunk','admin','2022-12-28 00:00:00','2022-12-28','','','','2023-01-19 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(130,54,44,0,0,0,0,144,0,118,1,0,0,0,'BUG130','',2,2,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-29 15:33:08','trunk','admin','2022-12-29 00:00:00','2022-12-29','','','','2023-01-18 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(131,54,44,0,0,7,0,144,0,122,1,0,0,0,'BUG131','',3,3,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-30 15:33:08','trunk','dev1','2022-12-30 00:00:00','2022-12-30','','','','2023-01-17 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(132,54,44,0,0,8,0,144,0,126,1,0,0,0,'BUG132','',4,4,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-31 15:33:08','trunk','dev1','2022-12-31 00:00:00','2022-12-31','','','','2023-01-16 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(133,55,45,0,0,0,0,145,0,130,1,0,0,0,'BUG133','',1,1,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-01 15:33:08','trunk','dev1','2023-01-01 00:00:00','2023-01-01','','','','2023-01-15 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(134,55,45,0,0,9,0,145,0,134,1,0,0,0,'BUG134','',2,2,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-02 15:33:08','trunk','dev1','2023-01-02 00:00:00','2023-01-02','','','','2023-01-14 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(135,55,45,0,0,10,0,145,0,138,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;135','',3,3,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-03 15:33:08','trunk','dev1','2023-01-03 00:00:00','2023-01-03','','','','2023-01-13 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(136,56,46,0,0,0,0,146,0,142,1,0,0,0,'bug136','',4,4,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-04 15:33:08','trunk','dev1','2023-01-04 00:00:00','2023-01-04','','','','2023-01-12 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(137,56,46,0,0,0,0,146,0,146,1,0,0,0,'BUG137','',1,1,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-05 15:33:08','trunk','dev1','2023-01-05 00:00:00','2023-01-05','','','','2023-01-11 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(138,56,46,0,0,0,0,146,0,150,1,0,0,0,'BUG138','',2,2,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-06 15:33:08','trunk','dev1','2023-01-06 00:00:00','2023-01-06','','','','2023-01-10 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(139,57,47,0,0,0,0,147,0,154,1,0,0,0,'BUG139','',3,3,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-07 15:33:08','trunk','dev1','2023-01-07 00:00:00','2023-01-07','','','','2023-01-09 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(140,57,47,0,0,0,0,147,0,158,1,0,0,0,'BUG140','',4,4,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-08 15:33:08','trunk','dev1','2023-01-08 00:00:00','2023-01-08','','','','2023-01-08 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(141,57,47,0,0,0,0,147,0,162,1,0,0,0,'BUG141','',1,1,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-09 15:33:08','trunk','dev1','2023-01-09 00:00:00','2023-01-09','','','','2023-01-07 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(142,58,48,0,0,0,0,148,0,166,1,0,0,0,'BUG142','',2,2,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-10 15:33:08','trunk','dev1','2023-01-10 00:00:00','2023-01-10','','','','2023-01-06 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(143,58,48,0,0,0,0,148,0,170,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;143','',3,3,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-11 15:33:08','trunk','dev1','2023-01-11 00:00:00','2023-01-11','','','','2023-01-05 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(144,58,48,0,0,0,0,148,0,174,1,0,0,0,'bug144','',4,4,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-12 15:33:08','trunk','dev1','2023-01-12 00:00:00','2023-01-12','','','','2023-01-04 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(145,59,49,0,0,0,0,149,0,178,1,0,0,0,'BUG145','',1,1,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-13 15:33:08','trunk','dev1','2023-01-13 00:00:00','2023-01-13','','','','2023-01-03 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(146,59,49,0,0,0,0,149,0,182,1,0,0,0,'BUG146','',2,2,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-14 15:33:08','trunk','dev1','2023-01-14 00:00:00','2023-01-14','','','','2023-01-02 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(147,59,49,0,0,0,0,149,0,186,1,0,0,0,'BUG147','',3,3,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-15 15:33:08','trunk','dev1','2023-01-15 00:00:00','2023-01-15','','','','2023-01-01 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(148,60,50,0,0,0,0,150,0,190,1,0,0,0,'BUG148','',4,4,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-16 15:33:08','trunk','dev1','2023-01-16 00:00:00','2023-01-16','','','','2023-01-21 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(149,60,50,0,0,0,0,150,0,194,1,0,0,0,'BUG149','',1,1,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-17 15:33:08','trunk','dev1','2023-01-17 00:00:00','2023-01-17','','','','2023-01-20 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(150,60,50,0,0,0,0,150,0,198,1,0,0,0,'BUG150','',2,2,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-18 15:33:08','trunk','dev1','2023-01-18 00:00:00','2023-01-18','','','','2023-01-19 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(151,61,51,0,0,0,0,151,0,202,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;151','',3,3,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-19 15:33:08','trunk','test1','2023-01-19 00:00:00','2023-01-19','$assignedTo','bydesign','','2023-01-18 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(152,61,51,0,0,0,0,151,0,206,1,0,0,0,'bug152','',4,4,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-20 15:33:08','trunk','test1','2023-01-20 00:00:00','2023-01-20','$assignedTo','bydesign','','2023-01-17 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(153,61,51,0,0,0,0,151,0,210,1,0,0,0,'BUG153','',1,1,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-14 15:33:08','trunk','test1','2022-12-14 00:00:00','2022-12-14','$assignedTo','bydesign','','2023-01-16 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(154,62,52,0,0,0,0,152,0,214,1,0,0,0,'BUG154','',2,2,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-15 15:33:08','trunk','test1','2022-12-15 00:00:00','2022-12-15','$assignedTo','bydesign','','2023-01-15 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(155,62,52,0,0,0,0,152,0,218,1,0,0,0,'BUG155','',3,3,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-16 15:33:08','trunk','test1','2022-12-16 00:00:00','2022-12-16','$assignedTo','bydesign','','2023-01-14 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(156,62,52,0,0,0,0,152,0,222,1,0,0,0,'BUG156','',4,4,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-17 15:33:08','trunk','test1','2022-12-17 00:00:00','2022-12-17','$assignedTo','duplicate','trunk','2023-01-13 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(157,63,53,0,0,0,0,153,0,226,1,0,0,0,'BUG157','',1,1,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-18 15:33:08','trunk','test1','2022-12-18 00:00:00','2022-12-18','$assignedTo','duplicate','trunk','2023-01-12 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(158,63,53,0,0,0,0,153,0,230,1,0,0,0,'BUG158','',2,2,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-19 15:33:08','trunk','test1','2022-12-19 00:00:00','2022-12-19','$assignedTo','duplicate','trunk','2023-01-11 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(159,63,53,0,0,0,0,153,0,234,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;159','',3,3,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-20 15:33:08','trunk','test1','2022-12-20 00:00:00','2022-12-20','$assignedTo','duplicate','trunk','2023-01-10 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(160,64,54,0,0,0,0,154,0,238,1,0,0,0,'bug160','',4,4,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-21 15:33:08','trunk','test1','2022-12-21 00:00:00','2022-12-21','$assignedTo','duplicate','trunk','2023-01-09 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(161,64,54,0,0,0,0,154,0,242,1,0,0,0,'BUG161','',1,1,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-22 15:33:08','trunk','test1','2022-12-22 00:00:00','2022-12-22','$assignedTo','external','trunk','2023-01-08 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(162,64,54,0,0,0,0,154,0,246,1,0,0,0,'BUG162','',2,2,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-23 15:33:08','trunk','test1','2022-12-23 00:00:00','2022-12-23','$assignedTo','external','trunk','2023-01-07 00:00:00','','0000-00-00 00:00:00',1,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(163,65,55,0,0,0,0,155,0,250,1,0,0,0,'BUG163','',3,3,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-24 15:33:08','trunk','test1','2022-12-24 00:00:00','2022-12-24','$assignedTo','external','trunk','2023-01-06 00:00:00','','0000-00-00 00:00:00',2,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(164,65,55,0,0,0,0,155,0,254,1,0,0,0,'BUG164','',4,4,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-25 15:33:08','trunk','test1','2022-12-25 00:00:00','2022-12-25','$assignedTo','external','trunk','2023-01-05 00:00:00','','0000-00-00 00:00:00',3,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(165,65,55,0,0,0,0,155,0,258,1,0,0,0,'BUG165','',1,1,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-26 15:33:08','trunk','test1','2022-12-26 00:00:00','2022-12-26','$assignedTo','external','trunk','2023-01-04 00:00:00','','0000-00-00 00:00:00',4,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(166,66,56,0,0,0,0,156,0,262,1,0,0,0,'BUG166','',2,2,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-27 15:33:08','trunk','test1','2022-12-27 00:00:00','2022-12-27','$assignedTo','fixed','trunk','2023-01-03 00:00:00','','0000-00-00 00:00:00',5,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(167,66,56,0,0,0,0,156,0,266,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;167','',3,3,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-28 15:33:08','trunk','test1','2022-12-28 00:00:00','2022-12-28','$assignedTo','fixed','trunk','2023-01-02 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(168,66,56,0,0,0,0,156,0,270,1,0,0,0,'bug168','',4,4,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-29 15:33:08','trunk','test1','2022-12-29 00:00:00','2022-12-29','$assignedTo','fixed','trunk','2023-01-01 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(169,67,57,0,0,0,0,157,0,274,1,0,0,0,'BUG169','',1,1,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-30 15:33:08','trunk','test1','2022-12-30 00:00:00','2022-12-30','$assignedTo','fixed','trunk','2023-01-21 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(170,67,57,0,0,0,0,157,0,278,1,0,0,0,'BUG170','',2,2,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-31 15:33:08','trunk','test1','2022-12-31 00:00:00','2022-12-31','$assignedTo','fixed','trunk','2023-01-20 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(171,67,57,0,0,0,0,157,0,282,1,0,0,0,'BUG171','',3,3,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-01 15:33:08','trunk','','2023-01-01 00:00:00','2023-01-01','$assignedTo','notrepro','','2023-01-19 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(172,68,58,0,0,0,0,158,0,286,1,0,0,0,'BUG172','',4,4,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-02 15:33:08','trunk','','2023-01-02 00:00:00','2023-01-02','$assignedTo','notrepro','','2023-01-18 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(173,68,58,0,0,0,0,158,0,290,1,0,0,0,'BUG173','',1,1,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-03 15:33:08','trunk','','2023-01-03 00:00:00','2023-01-03','$assignedTo','postponed','','2023-01-17 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(174,68,58,0,0,0,0,158,0,294,1,0,0,0,'BUG174','',2,2,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-04 15:33:08','trunk','','2023-01-04 00:00:00','2023-01-04','$assignedTo','postponed','','2023-01-16 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(175,69,59,0,0,0,0,159,0,298,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;175','',3,3,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-05 15:33:08','trunk','','2023-01-05 00:00:00','2023-01-05','$assignedTo','postponed','','2023-01-15 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(176,69,59,0,0,0,0,159,0,302,1,0,0,0,'bug176','',4,4,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-06 15:33:08','trunk','','2023-01-06 00:00:00','2023-01-06','$assignedTo','willnotfix','','2023-01-14 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(177,69,59,0,0,0,0,159,0,306,1,0,0,0,'BUG177','',1,1,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-07 15:33:08','trunk','','2023-01-07 00:00:00','2023-01-07','$assignedTo','willnotfix','','2023-01-13 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(178,70,60,0,0,0,0,160,0,310,1,0,0,0,'BUG178','',2,2,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-08 15:33:08','trunk','','2023-01-08 00:00:00','2023-01-08','$assignedTo','willnotfix','','2023-01-12 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(179,70,60,0,0,0,0,160,0,314,1,0,0,0,'BUG179','',3,3,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-09 15:33:08','trunk','','2023-01-09 00:00:00','2023-01-09','$assignedTo','willnotfix','','2023-01-11 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(180,70,60,0,0,0,0,160,0,318,1,0,0,0,'BUG180','',4,4,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-10 15:33:08','trunk','','2023-01-10 00:00:00','2023-01-10','$assignedTo','willnotfix','','2023-01-10 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(181,71,61,0,0,0,0,161,0,322,1,0,0,0,'BUG181','',1,1,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-11 15:33:08','trunk','closed','2023-01-11 00:00:00','2023-01-11','admin','fixed','','2023-01-09 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(182,71,61,0,0,0,0,161,0,326,1,0,0,0,'BUG182','',2,2,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-12 15:33:08','trunk','closed','2023-01-12 00:00:00','2023-01-12','admin','fixed','','2023-01-08 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(183,71,61,0,0,0,0,161,0,330,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;183','',3,3,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-13 15:33:08','trunk','closed','2023-01-13 00:00:00','2023-01-13','admin','fixed','','2023-01-07 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(184,72,62,0,0,0,0,162,0,334,1,0,0,0,'bug184','',4,4,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-14 15:33:08','trunk','closed','2023-01-14 00:00:00','2023-01-14','admin','fixed','','2023-01-06 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(185,72,62,0,0,0,0,162,0,338,1,0,0,0,'BUG185','',1,1,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-15 15:33:08','trunk','closed','2023-01-15 00:00:00','2023-01-15','admin','fixed','','2023-01-05 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(186,72,62,0,0,0,0,162,0,342,1,0,0,0,'BUG186','',2,2,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-16 15:33:08','trunk','closed','2023-01-16 00:00:00','2023-01-16','admin','fixed','','2023-01-04 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(187,73,63,0,0,0,0,163,0,346,1,0,0,0,'BUG187','',3,3,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-17 15:33:08','trunk','closed','2023-01-17 00:00:00','2023-01-17','admin','fixed','','2023-01-03 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(188,73,63,0,0,0,0,163,0,350,1,0,0,0,'BUG188','',4,4,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-18 15:33:08','trunk','closed','2023-01-18 00:00:00','2023-01-18','admin','fixed','','2023-01-02 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(189,73,63,0,0,0,0,163,0,354,1,0,0,0,'BUG189','',1,1,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-19 15:33:08','trunk','closed','2023-01-19 00:00:00','2023-01-19','admin','fixed','','2023-01-01 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(190,74,64,0,0,0,0,164,0,358,1,0,0,0,'BUG190','',2,2,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-20 15:33:08','trunk','closed','2023-01-20 00:00:00','2023-01-20','admin','fixed','','2023-01-21 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(191,74,64,0,0,0,0,164,0,362,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;191','',3,3,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-14 15:33:08','trunk','closed','2022-12-14 00:00:00','2022-12-14','admin','bydesign','','2023-01-20 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(192,74,64,0,0,0,0,164,0,366,1,0,0,0,'bug192','',4,4,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-15 15:33:08','trunk','closed','2022-12-15 00:00:00','2022-12-15','admin','external','','2023-01-19 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(193,75,65,0,0,0,0,165,0,370,1,0,0,0,'BUG193','',1,1,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-16 15:33:08','trunk','closed','2022-12-16 00:00:00','2022-12-16','admin','notrepro','','2023-01-18 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(194,75,65,0,0,0,0,165,0,374,1,0,0,0,'BUG194','',2,2,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-17 15:33:08','trunk','closed','2022-12-17 00:00:00','2022-12-17','admin','postponed','','2023-01-17 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(195,75,65,0,0,0,0,165,0,378,1,0,0,0,'BUG195','',3,3,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-18 15:33:08','trunk','closed','2022-12-18 00:00:00','2022-12-18','admin','willnotfix','','2023-01-16 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(196,76,66,0,0,0,0,166,0,382,1,0,0,0,'BUG196','',4,4,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-19 15:33:08','trunk','closed','2022-12-19 00:00:00','2022-12-19','admin','duplicate','trunk','2023-01-15 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(197,76,66,0,0,0,0,166,0,386,1,0,0,0,'BUG197','',1,1,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-20 15:33:08','trunk','closed','2022-12-20 00:00:00','2022-12-20','admin','duplicate','trunk','2023-01-14 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(198,76,66,0,0,0,0,166,0,390,1,0,0,0,'BUG198','',2,2,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-21 15:33:08','trunk','closed','2022-12-21 00:00:00','2022-12-21','admin','duplicate','trunk','2023-01-13 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(199,77,67,0,0,0,0,167,0,394,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;199','',3,3,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-22 15:33:08','trunk','closed','2022-12-22 00:00:00','2022-12-22','admin','duplicate','trunk','2023-01-12 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(200,77,67,0,0,0,0,167,0,398,1,0,0,0,'bug200','',4,4,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-23 15:33:08','trunk','closed','2022-12-23 00:00:00','2022-12-23','admin','duplicate','trunk','2023-01-11 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(201,77,67,0,0,0,0,167,0,2,1,0,0,0,'BUG201','',1,1,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-24 15:33:08','1','admin','2022-12-24 00:00:00','2022-12-24','','','','2023-01-10 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(202,78,68,0,0,0,0,168,0,6,1,0,0,0,'BUG202','',2,2,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-25 15:33:08','0','admin','2022-12-25 00:00:00','2022-12-25','','','','2023-01-09 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(203,78,68,0,0,0,0,168,0,10,1,0,0,0,'BUG203','',3,3,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-26 15:33:08','1','admin','2022-12-26 00:00:00','2022-12-26','','','','2023-01-08 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(204,78,68,0,0,0,0,168,0,14,1,0,0,0,'BUG204','',4,4,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-27 15:33:08','trunk','admin','2022-12-27 00:00:00','2022-12-27','','','','2023-01-07 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(205,79,69,0,0,0,0,169,0,18,1,0,0,0,'BUG205','',1,1,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-28 15:33:08','trunk','admin','2022-12-28 00:00:00','2022-12-28','','','','2023-01-06 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(206,79,69,0,0,0,0,169,0,22,1,0,0,0,'BUG206','',2,2,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-29 15:33:08','trunk','admin','2022-12-29 00:00:00','2022-12-29','','','','2023-01-05 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(207,79,69,0,0,0,0,169,0,26,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;207','',3,3,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-30 15:33:08','trunk','admin','2022-12-30 00:00:00','2022-12-30','','','','2023-01-04 00:00:00','','0000-00-00 00:00:00',10,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(208,80,70,0,0,0,0,170,0,30,1,0,0,0,'bug208','',4,4,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-31 15:33:08','trunk','admin','2022-12-31 00:00:00','2022-12-31','','','','2023-01-03 00:00:00','','0000-00-00 00:00:00',11,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(209,80,70,0,0,0,0,170,0,34,1,0,0,0,'BUG209','',1,1,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-01 15:33:08','trunk','admin','2023-01-01 00:00:00','2023-01-01','','','','2023-01-02 00:00:00','','0000-00-00 00:00:00',12,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(210,80,70,0,0,0,0,170,0,38,1,0,0,0,'BUG210','',2,2,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-02 15:33:08','trunk','admin','2023-01-02 00:00:00','2023-01-02','','','','2023-01-01 00:00:00','','0000-00-00 00:00:00',13,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(211,81,71,0,0,0,0,171,0,42,1,0,0,0,'BUG211','',3,3,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-03 15:33:08','trunk','admin','2023-01-03 00:00:00','2023-01-03','','','','2023-01-21 00:00:00','admin','0000-00-00 00:00:00',14,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(212,81,71,0,0,0,0,171,0,46,1,0,0,0,'BUG212','',4,4,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-04 15:33:08','trunk','admin','2023-01-04 00:00:00','2023-01-04','','','','2023-01-20 00:00:00','admin','0000-00-00 00:00:00',15,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(213,81,71,0,0,0,0,171,0,50,1,0,0,0,'BUG213','',1,1,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-05 15:33:08','trunk','admin','2023-01-05 00:00:00','2023-01-05','','','','2023-01-19 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(214,82,72,0,0,0,0,172,0,54,1,0,0,0,'BUG214','',2,2,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-06 15:33:08','trunk','admin','2023-01-06 00:00:00','2023-01-06','','','','2023-01-18 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(215,82,72,0,0,0,0,172,0,58,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;215','',3,3,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-07 15:33:08','trunk','admin','2023-01-07 00:00:00','2023-01-07','','','','2023-01-17 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(216,82,72,0,0,0,0,172,0,62,1,0,0,0,'bug216','',4,4,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-08 15:33:08','trunk','admin','2023-01-08 00:00:00','2023-01-08','','','','2023-01-16 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(217,83,73,0,0,0,0,173,0,66,1,0,0,0,'BUG217','',1,1,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-09 15:33:08','trunk','admin','2023-01-09 00:00:00','2023-01-09','','','','2023-01-15 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(218,83,73,0,0,0,0,173,0,70,1,0,0,0,'BUG218','',2,2,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-10 15:33:08','trunk','admin','2023-01-10 00:00:00','2023-01-10','','','','2023-01-14 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(219,83,73,0,0,0,0,173,0,74,1,0,0,0,'BUG219','',3,3,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-11 15:33:08','trunk','admin','2023-01-11 00:00:00','2023-01-11','','','','2023-01-13 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(220,84,74,0,0,0,0,174,0,78,1,0,0,0,'BUG220','',4,4,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-12 15:33:08','trunk','admin','2023-01-12 00:00:00','2023-01-12','','','','2023-01-12 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(221,84,74,0,0,0,0,174,0,82,1,0,0,0,'BUG221','',1,1,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-13 15:33:08','trunk','admin','2023-01-13 00:00:00','2023-01-13','','','','2023-01-11 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(222,84,74,0,0,0,0,174,0,86,1,0,0,0,'BUG222','',2,2,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-14 15:33:08','trunk','admin','2023-01-14 00:00:00','2023-01-14','','','','2023-01-10 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(223,85,75,0,0,0,0,175,0,90,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;223','',3,3,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-15 15:33:08','trunk','admin','2023-01-15 00:00:00','2023-01-15','','','','2023-01-09 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(224,85,75,0,0,0,0,175,0,94,1,0,0,0,'bug224','',4,4,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-16 15:33:08','trunk','admin','2023-01-16 00:00:00','2023-01-16','','','','2023-01-08 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(225,85,75,0,0,0,0,175,0,98,1,0,0,0,'BUG225','',1,1,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-17 15:33:08','trunk','admin','2023-01-17 00:00:00','2023-01-17','','','','2023-01-07 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(226,86,76,0,0,0,0,176,0,102,1,0,0,0,'BUG226','',2,2,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-18 15:33:08','trunk','admin','2023-01-18 00:00:00','2023-01-18','','','','2023-01-06 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(227,86,76,0,0,0,0,176,0,106,1,0,0,0,'BUG227','',3,3,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-19 15:33:08','trunk','admin','2023-01-19 00:00:00','2023-01-19','','','','2023-01-05 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(228,86,76,0,0,0,0,176,0,110,1,0,0,0,'BUG228','',4,4,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-20 15:33:08','trunk','admin','2023-01-20 00:00:00','2023-01-20','','','','2023-01-04 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(229,87,77,0,0,0,0,177,0,114,1,0,0,0,'BUG229','',1,1,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-14 15:33:08','trunk','admin','2022-12-14 00:00:00','2022-12-14','','','','2023-01-03 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(230,87,77,0,0,0,0,177,0,118,1,0,0,0,'BUG230','',2,2,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-15 15:33:08','trunk','admin','2022-12-15 00:00:00','2022-12-15','','','','2023-01-02 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(231,87,77,0,0,0,0,177,0,122,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;231','',3,3,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-16 15:33:08','trunk','dev1','2022-12-16 00:00:00','2022-12-16','','','','2023-01-01 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(232,88,78,0,0,0,0,178,0,126,1,0,0,0,'bug232','',4,4,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-17 15:33:08','trunk','dev1','2022-12-17 00:00:00','2022-12-17','','','','2023-01-21 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(233,88,78,0,0,0,0,178,0,130,1,0,0,0,'BUG233','',1,1,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-18 15:33:08','trunk','dev1','2022-12-18 00:00:00','2022-12-18','','','','2023-01-20 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(234,88,78,0,0,0,0,178,0,134,1,0,0,0,'BUG234','',2,2,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-19 15:33:08','trunk','dev1','2022-12-19 00:00:00','2022-12-19','','','','2023-01-19 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(235,89,79,0,0,0,0,179,0,138,1,0,0,0,'BUG235','',3,3,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-20 15:33:08','trunk','dev1','2022-12-20 00:00:00','2022-12-20','','','','2023-01-18 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(236,89,79,0,0,0,0,179,0,142,1,0,0,0,'BUG236','',4,4,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-21 15:33:08','trunk','dev1','2022-12-21 00:00:00','2022-12-21','','','','2023-01-17 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(237,89,79,0,0,0,0,179,0,146,1,0,0,0,'BUG237','',1,1,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-22 15:33:08','trunk','dev1','2022-12-22 00:00:00','2022-12-22','','','','2023-01-16 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(238,90,80,0,0,0,0,180,0,150,1,0,0,0,'BUG238','',2,2,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-23 15:33:08','trunk','dev1','2022-12-23 00:00:00','2022-12-23','','','','2023-01-15 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(239,90,80,0,0,0,0,180,0,154,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;239','',3,3,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-24 15:33:08','trunk','dev1','2022-12-24 00:00:00','2022-12-24','','','','2023-01-14 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(240,90,80,0,0,0,0,180,0,158,1,0,0,0,'bug240','',4,4,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-25 15:33:08','trunk','dev1','2022-12-25 00:00:00','2022-12-25','','','','2023-01-13 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(241,91,81,0,0,0,0,181,0,162,1,0,0,0,'BUG241','',1,1,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-26 15:33:08','trunk','dev1','2022-12-26 00:00:00','2022-12-26','','','','2023-01-12 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(242,91,81,0,0,0,0,181,0,166,1,0,0,0,'BUG242','',2,2,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-27 15:33:08','trunk','dev1','2022-12-27 00:00:00','2022-12-27','','','','2023-01-11 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(243,91,81,0,0,0,0,181,0,170,1,0,0,0,'BUG243','',3,3,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-28 15:33:08','trunk','dev1','2022-12-28 00:00:00','2022-12-28','','','','2023-01-10 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(244,92,82,0,0,0,0,182,0,174,1,0,0,0,'BUG244','',4,4,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-29 15:33:08','trunk','dev1','2022-12-29 00:00:00','2022-12-29','','','','2023-01-09 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(245,92,82,0,0,0,0,182,0,178,1,0,0,0,'BUG245','',1,1,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-30 15:33:08','trunk','dev1','2022-12-30 00:00:00','2022-12-30','','','','2023-01-08 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(246,92,82,0,0,0,0,182,0,182,1,0,0,0,'BUG246','',2,2,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-31 15:33:08','trunk','dev1','2022-12-31 00:00:00','2022-12-31','','','','2023-01-07 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(247,93,83,0,0,0,0,183,0,186,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;247','',3,3,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-01 15:33:08','trunk','dev1','2023-01-01 00:00:00','2023-01-01','','','','2023-01-06 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(248,93,83,0,0,0,0,183,0,190,1,0,0,0,'bug248','',4,4,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-02 15:33:08','trunk','dev1','2023-01-02 00:00:00','2023-01-02','','','','2023-01-05 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(249,93,83,0,0,0,0,183,0,194,1,0,0,0,'BUG249','',1,1,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','active','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-03 15:33:08','trunk','dev1','2023-01-03 00:00:00','2023-01-03','','','','2023-01-04 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(250,94,84,0,0,0,0,184,0,198,1,0,0,0,'BUG250','',2,2,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','active','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-04 15:33:08','trunk','dev1','2023-01-04 00:00:00','2023-01-04','','','','2023-01-03 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','1'),(251,94,84,0,0,0,0,184,0,202,1,0,0,0,'BUG251','',3,3,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-05 15:33:08','trunk','dev1','2023-01-05 00:00:00','2023-01-05','$assignedTo','bydesign','','2023-01-02 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(252,94,84,0,0,0,0,184,0,206,1,0,0,0,'BUG252','',4,4,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-06 15:33:08','trunk','dev1','2023-01-06 00:00:00','2023-01-06','$assignedTo','bydesign','','2023-01-01 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(253,95,85,0,0,0,0,185,0,210,1,0,0,0,'BUG253','',1,1,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-07 15:33:08','trunk','dev1','2023-01-07 00:00:00','2023-01-07','$assignedTo','bydesign','','2023-01-21 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(254,95,85,0,0,0,0,185,0,214,1,0,0,0,'BUG254','',2,2,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-08 15:33:08','trunk','dev1','2023-01-08 00:00:00','2023-01-08','$assignedTo','bydesign','','2023-01-20 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(255,95,85,0,0,0,0,185,0,218,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;255','',3,3,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-09 15:33:08','trunk','dev1','2023-01-09 00:00:00','2023-01-09','$assignedTo','bydesign','','2023-01-19 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(256,96,86,0,0,0,0,186,0,222,1,0,0,0,'bug256','',4,4,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-10 15:33:08','trunk','dev1','2023-01-10 00:00:00','2023-01-10','$assignedTo','duplicate','trunk','2023-01-18 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(257,96,86,0,0,0,0,186,0,226,1,0,0,0,'BUG257','',1,1,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-11 15:33:08','trunk','dev1','2023-01-11 00:00:00','2023-01-11','$assignedTo','duplicate','trunk','2023-01-17 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(258,96,86,0,0,0,0,186,0,230,1,0,0,0,'BUG258','',2,2,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-12 15:33:08','trunk','dev1','2023-01-12 00:00:00','2023-01-12','$assignedTo','duplicate','trunk','2023-01-16 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(259,97,87,0,0,0,0,187,0,234,1,0,0,0,'BUG259','',3,3,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-13 15:33:08','trunk','dev1','2023-01-13 00:00:00','2023-01-13','$assignedTo','duplicate','trunk','2023-01-15 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(260,97,87,0,0,0,0,187,0,238,1,0,0,0,'BUG260','',4,4,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-14 15:33:08','trunk','dev1','2023-01-14 00:00:00','2023-01-14','$assignedTo','duplicate','trunk','2023-01-14 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(261,97,87,0,0,0,0,187,0,242,1,0,0,0,'BUG261','',1,1,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-15 15:33:08','trunk','test1','2023-01-15 00:00:00','2023-01-15','$assignedTo','external','trunk','2023-01-13 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(262,98,88,0,0,0,0,188,0,246,1,0,0,0,'BUG262','',2,2,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-16 15:33:08','trunk','test1','2023-01-16 00:00:00','2023-01-16','$assignedTo','external','trunk','2023-01-12 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(263,98,88,0,0,0,0,188,0,250,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;263','',3,3,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-17 15:33:08','trunk','test1','2023-01-17 00:00:00','2023-01-17','$assignedTo','external','trunk','2023-01-11 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(264,98,88,0,0,0,0,188,0,254,1,0,0,0,'bug264','',4,4,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-18 15:33:08','trunk','test1','2023-01-18 00:00:00','2023-01-18','$assignedTo','external','trunk','2023-01-10 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(265,99,89,0,0,0,0,189,0,258,1,0,0,0,'BUG265','',1,1,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-19 15:33:08','trunk','test1','2023-01-19 00:00:00','2023-01-19','$assignedTo','external','trunk','2023-01-09 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(266,99,89,0,0,0,0,189,0,262,1,0,0,0,'BUG266','',2,2,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-20 15:33:08','trunk','test1','2023-01-20 00:00:00','2023-01-20','$assignedTo','fixed','trunk','2023-01-08 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(267,99,89,0,0,0,0,189,0,266,1,0,0,0,'BUG267','',3,3,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-14 15:33:08','trunk','test1','2022-12-14 00:00:00','2022-12-14','$assignedTo','fixed','trunk','2023-01-07 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(268,100,90,0,0,0,0,190,0,270,1,0,0,0,'BUG268','',4,4,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-15 15:33:08','trunk','test1','2022-12-15 00:00:00','2022-12-15','$assignedTo','fixed','trunk','2023-01-06 00:00:00','','0000-00-00 00:00:00',1,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(269,100,90,0,0,0,0,190,0,274,1,0,0,0,'BUG269','',1,1,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-16 15:33:08','trunk','test1','2022-12-16 00:00:00','2022-12-16','$assignedTo','fixed','trunk','2023-01-05 00:00:00','','0000-00-00 00:00:00',2,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(270,100,90,0,0,0,0,190,0,278,1,0,0,0,'BUG270','',2,2,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-17 15:33:08','trunk','test1','2022-12-17 00:00:00','2022-12-17','$assignedTo','fixed','trunk','2023-01-04 00:00:00','','0000-00-00 00:00:00',3,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(271,0,91,0,0,0,0,191,0,282,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;271','',3,3,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-18 15:33:08','trunk','test1','2022-12-18 00:00:00','2022-12-18','$assignedTo','notrepro','','2023-01-03 00:00:00','','0000-00-00 00:00:00',4,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(272,0,91,0,0,0,0,191,0,286,1,0,0,0,'bug272','',4,4,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-19 15:33:08','trunk','test1','2022-12-19 00:00:00','2022-12-19','$assignedTo','notrepro','','2023-01-02 00:00:00','','0000-00-00 00:00:00',5,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(273,0,91,0,0,0,0,191,0,290,1,0,0,0,'BUG273','',1,1,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-20 15:33:08','trunk','test1','2022-12-20 00:00:00','2022-12-20','$assignedTo','postponed','','2023-01-01 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(274,0,92,0,0,0,0,192,0,294,1,0,0,0,'BUG274','',2,2,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-21 15:33:08','trunk','test1','2022-12-21 00:00:00','2022-12-21','$assignedTo','postponed','','2023-01-21 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(275,0,92,0,0,0,0,192,0,298,1,0,0,0,'BUG275','',3,3,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-22 15:33:08','trunk','test1','2022-12-22 00:00:00','2022-12-22','$assignedTo','postponed','','2023-01-20 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(276,0,92,0,0,0,0,192,0,302,1,0,0,0,'BUG276','',4,4,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-23 15:33:08','trunk','test1','2022-12-23 00:00:00','2022-12-23','$assignedTo','willnotfix','','2023-01-19 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(277,0,93,0,0,0,0,193,0,306,1,0,0,0,'BUG277','',1,1,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-24 15:33:08','trunk','test1','2022-12-24 00:00:00','2022-12-24','$assignedTo','willnotfix','','2023-01-18 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(278,0,93,0,0,0,0,193,0,310,1,0,0,0,'BUG278','',2,2,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-25 15:33:08','trunk','test1','2022-12-25 00:00:00','2022-12-25','$assignedTo','willnotfix','','2023-01-17 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(279,0,93,0,0,0,0,193,0,314,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;279','',3,3,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','resolved','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-26 15:33:08','trunk','test1','2022-12-26 00:00:00','2022-12-26','$assignedTo','willnotfix','','2023-01-16 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(280,0,94,0,0,0,0,194,0,318,1,0,0,0,'bug280','',4,4,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','resolved','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-27 15:33:08','trunk','test1','2022-12-27 00:00:00','2022-12-27','$assignedTo','willnotfix','','2023-01-15 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(281,0,94,0,0,0,0,194,0,322,1,0,0,0,'BUG281','',1,1,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-28 15:33:08','trunk','test1','2022-12-28 00:00:00','2022-12-28','admin','fixed','','2023-01-14 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(282,0,94,0,0,0,0,194,0,326,1,0,0,0,'BUG282','',2,2,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-29 15:33:08','trunk','test1','2022-12-29 00:00:00','2022-12-29','admin','fixed','','2023-01-13 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(283,0,95,0,0,0,0,195,0,330,1,0,0,0,'BUG283','',3,3,'security','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2022-12-30 15:33:08','trunk','test1','2022-12-30 00:00:00','2022-12-30','admin','fixed','','2023-01-12 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(284,0,95,0,0,0,0,195,0,334,1,0,0,0,'BUG284','',4,4,'performance','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2022-12-31 15:33:08','trunk','test1','2022-12-31 00:00:00','2022-12-31','admin','fixed','','2023-01-11 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(285,0,95,0,0,0,0,195,0,338,1,0,0,0,'BUG285','',1,1,'standard','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-01 15:33:08','trunk','test1','2023-01-01 00:00:00','2023-01-01','admin','fixed','','2023-01-10 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(286,0,96,0,0,0,0,196,0,342,1,0,0,0,'BUG286','',2,2,'automation','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-02 15:33:08','trunk','test1','2023-01-02 00:00:00','2023-01-02','admin','fixed','','2023-01-09 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(287,0,96,0,0,0,0,196,0,346,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;287','',3,3,'designdefect','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-03 15:33:08','trunk','test1','2023-01-03 00:00:00','2023-01-03','admin','fixed','','2023-01-08 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(288,0,96,0,0,0,0,196,0,350,1,0,0,0,'bug288','',4,4,'others','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-04 15:33:08','trunk','test1','2023-01-04 00:00:00','2023-01-04','admin','fixed','','2023-01-07 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(289,0,97,0,0,0,0,197,0,354,1,0,0,0,'BUG289','',1,1,'codeerror','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-05 15:33:08','trunk','test1','2023-01-05 00:00:00','2023-01-05','admin','fixed','','2023-01-06 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(290,0,97,0,0,0,0,197,0,358,1,0,0,0,'BUG290','',2,2,'config','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-06 15:33:08','trunk','test1','2023-01-06 00:00:00','2023-01-06','admin','fixed','','2023-01-05 00:00:00','','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(291,0,97,0,0,0,0,197,0,362,1,0,0,0,'BUG291','',3,3,'install','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-07 15:33:08','trunk','','2023-01-07 00:00:00','2023-01-07','admin','bydesign','','2023-01-04 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(292,0,98,0,0,0,0,198,0,366,1,0,0,0,'BUG292','',4,4,'security','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-08 15:33:08','trunk','','2023-01-08 00:00:00','2023-01-08','admin','external','','2023-01-03 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(293,0,98,0,0,0,0,198,0,370,1,0,0,0,'BUG293','',1,1,'performance','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-09 15:33:08','trunk','','2023-01-09 00:00:00','2023-01-09','admin','notrepro','','2023-01-02 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(294,0,98,0,0,0,0,198,0,374,1,0,0,0,'BUG294','',2,2,'standard','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-10 15:33:08','trunk','','2023-01-10 00:00:00','2023-01-10','admin','postponed','','2023-01-01 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(295,0,99,0,0,0,0,199,0,378,1,0,0,0,'缺陷!@()(){}|+=%^&*$#测试bug名称到底可以有多长！@#￥%&*\'\":.<>。?/（）;295','',3,3,'automation','','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#3da7f5',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-11 15:33:08','trunk','','2023-01-11 00:00:00','2023-01-11','admin','willnotfix','','2023-01-21 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(296,0,99,0,0,0,0,199,0,382,1,0,0,0,'bug296','',4,4,'designdefect','','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#75c941',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-12 15:33:08','trunk','','2023-01-12 00:00:00','2023-01-12','admin','duplicate','trunk','2023-01-20 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(297,0,99,0,0,0,0,199,0,386,1,0,0,0,'BUG297','',1,1,'others','win10','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#2dbdb2',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-13 15:33:08','trunk','','2023-01-13 00:00:00','2023-01-13','admin','duplicate','trunk','2023-01-19 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(298,0,100,0,0,0,0,200,0,390,1,0,0,0,'BUG298','',2,2,'codeerror','windows','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#797ec9',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-14 15:33:08','trunk','','2023-01-14 00:00:00','2023-01-14','admin','duplicate','trunk','2023-01-18 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(299,0,100,0,0,0,0,200,0,394,1,0,0,0,'BUG299','',3,3,'config','android','','','','<p>【步骤】</p><br/><p>【结果】</p><br/><p>【期望】</p><br/>','closed','','#ffaf38',0,0,'0000-00-00 00:00:00','','','','admin','2023-01-15 15:33:08','trunk','','2023-01-15 00:00:00','2023-01-15','admin','duplicate','trunk','2023-01-17 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0'),(300,0,100,0,0,0,0,200,0,398,1,0,0,0,'BUG300','',4,4,'install','ios','','','','【步骤】进入成果展示<br/>【结果】乱码<br/>【期望】正常显示<br/>','closed','','#ff4e3e',1,0,'0000-00-00 00:00:00','','','','admin','2023-01-16 15:33:08','trunk','','2023-01-16 00:00:00','2023-01-16','admin','duplicate','trunk','2023-01-16 00:00:00','admin','0000-00-00 00:00:00',0,'',0,0,0,0,0,0,'','','','','','',0,'','0000-00-00 00:00:00','0');
/*!40000 ALTER TABLE `zt_bug` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2023-01-14 15:33:15
