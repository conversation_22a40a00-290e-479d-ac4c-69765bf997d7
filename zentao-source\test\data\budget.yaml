title: table zt_budget
desc: "费用估算"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: "1-10000"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: program
    note: "项目ID"
    range: "1-10000"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: stage
    note: "阶段ID"
    range: "1-10000"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: subject
    note: "主题ID"
    range: "1-10000"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: amount
    note: "费用数额"
    range: "100,200,300,350,400,420,600,800,700,150,50"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: name
    note: "费用名称"
    range: "1-10000"
    prefix: "费用名称"
    postfix: ""
    loop: 0
    format: ""
  - field: desc
    note: "费用描述"
    range: "1-10000"
    prefix: "费用描述"
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: "创建者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdDate
    note: "创建时间"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: lastEditedBy
    note: "最后编辑者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: lastEditedDate
    note: "最后编辑时间"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: "0,1"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
