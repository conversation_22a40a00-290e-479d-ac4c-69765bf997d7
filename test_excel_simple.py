#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试Excel导出
"""

import requests
import time

def test_excel_export():
    """测试Excel导出"""
    print("📤 测试Excel导出...")
    
    try:
        # 直接调用导出API
        response = requests.get("http://localhost:8000/api/v1/new-supervision/export-excel", timeout=30)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            content_length = len(response.content)
            print(f"文件大小: {content_length} 字节")
            
            # 保存文件
            with open('test_export_simple.xlsx', 'wb') as f:
                f.write(response.content)
            
            print("✅ Excel导出成功，文件已保存为 test_export_simple.xlsx")
            
            # 检查文件内容
            if content_length > 5000:  # Excel文件应该有一定大小
                print("✅ 文件大小正常")
                return True
            else:
                print("❌ 文件太小，可能有问题")
                return False
        else:
            print(f"❌ 导出失败: {response.status_code}")
            print(f"错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

def main():
    print("🚀 简单Excel导出测试")
    print("=" * 40)
    
    time.sleep(2)  # 等待服务启动
    
    success = test_excel_export()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 测试成功！")
    else:
        print("❌ 测试失败！")

if __name__ == "__main__":
    main()
