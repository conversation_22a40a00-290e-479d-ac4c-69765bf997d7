/*treeview*/
.treeview .hitarea {position: absolute; left: 15px; top: 5px; background-color: #fff; cursor: pointer;}
.treeview > li > .hitarea {left: -1px; top: 0px;}
.treeview .hitarea:before {color: #999; content: '\e6f1'; font-family: ZenIcon; transition:all 0.3s; font-size: 14px}
.treeview li:hover .hitarea:before {color: #2973EA;}
.treeview .hitarea.collapsable-hitarea:before {content: '\e6f2';}

.treeview .form-control, .treeview input[type='text'] {padding: 1px 2px; height: 20px; display: inline-block;}
