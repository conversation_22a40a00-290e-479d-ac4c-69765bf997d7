title: table zt_job
desc: "构建任务"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: name
    note: "名称"
    range: 1-10000
    prefix: "这是一个Job"
    postfix: ""
    loop: 0
    format: ""
  - field: repo
    note: "代码库"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: product
    note: "关联产品"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: frame
    note: "工具/框架"
    range: sonarqube,junit,testng,phpunit,pytest,jtest,cppunit,gtest,qtest
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: engine
    note: "引擎"
    range: jenkins,gitlab
  - field: server
    note: "服务器ID"
    range: 3,1
  - field: pipeline
    note: "流水线"
    range: dave,[{"project":"1569","reference":"master"}]
  - field: triggerType
    note: "触发方式"
    range: tag,commit,schedule
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: sonarqubeServer
    note: "Sonarqube服务器"
    range: 2,0{8}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: projectKey
    note: "Sonarqube项目"
    range: zentaopms
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: svnDir
    note: "SVN监控路径"
    range: caselib,effort,git,mail,output
    prefix: "/module/"
    postfix: ""
    loop: 0
    format: ""
  - field: atDay
    note: "自定义日期"
    range: 0-6
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: atTime
    note: "执行时间"
    range: "22:05"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: customParam
    note: "自定义构建参数"
    range: ''
    prefix: "["
    postfix: "]"
    loop: 0
    format: ""
  - field: comment
    note: "匹配关键字"
    range: a-z
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: "创建者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdDate
    note: "创建日期"
    range: "(M)-(w)"
    type: timestamp
    postfix: ""
    format: "YY/MM/DD"
  - field: editedBy
    note: "编辑者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedDate
    note: "编辑日期"
    range: "(M)-(w)"
    type: timestamp
    postfix: ""
    format: "YY/MM/DD"
  - field: lastExec
    note: "最后执行时间"
    range: "(M)-(w)"
    type: timestamp
    postfix: ""
    format: "YY/MM/DD"
  - field: lastStatus
    note: "最后执行状态"
    range: "a-z"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: lastTag
    note: "最后执行标签"
    range: "a-z"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
