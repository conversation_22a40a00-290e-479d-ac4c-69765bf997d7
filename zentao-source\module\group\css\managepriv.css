.flex-center {display: flex; justify-content: center; align-items: center;}
#mainContainer {display: flex; gap: 16px; height: calc(100vh - 165px);}
#mainContainer > .main {flex: 1 1 75%; overflow-y: auto; padding: 0px; border: 1px solid #FFF; box-sizing: content-box;}
#mainContainer > .side {flex: 1 1 25%;}
#mainContainer .main .btn-group {position: absolute; visibility: hidden; top: 2px; display: inline-block; z-index: 2;}
.priv-panel {background: #fff; padding: 10px 16px; box-shadow: 0 1px 1px rgb(0 0 0 / 5%), 0 2px 6px 0 rgb(0 0 0 / 5%);}
.priv-panel {height: 40%;}
.priv-panel .table-empty-tip {padding: calc(15% - 10px) 10px;}
.priv-panel + .priv-panel .table-empty-tip {padding: calc(30% - 20px) 10px;}
.priv-panel + .priv-panel {margin-top: 16px; height: calc(60% - 16px);}
.priv-panel > .panel-content {height: calc(100% - 25px); overflow-y: auto; padding-left: 4px; margin-top: 10px;}
.priv-panel > .panel-bottom {padding-top: 20px;}
.priv-panel > .panel-title {position: relative;}
.priv-panel > .panel-title > .icon-help {position: absolute; top: 3px; margin-left: 8px;}
.icon-help + .popover {font-weight: 400;}
.menuTree li.has-list:not(:first-child) {margin-top: 20px;}

/* tree */
.tree ul > li {display: inline-block; width: 50%;}
.menuTree li > ul {margin-bottom: 0px;}
.menuTree > li.has-list.open:before {top: 25px;}
.priv-item > .icon {padding-left: 5px;}
.empty-tip {height: 100%;}
.menuTree > li {padding-left: 2px;}
.menuTree > li.has-list.open:before {left: 9px;}
.menuTree ul > li {padding-left: 20px; padding-top: 10px;}

.table-bymodule select.form-control {height: 250px;}
.group-item {display: block; width: 170px; float: left; font-size: 13px;}
.group-item .checkbox-inline label {padding-left: 8px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;}
#privList .package label, #privList .module label {overflow: visible;}
.table.table-form tbody > tr:last-child td {border-top: 1px solid #ddd;}
@-moz-document url-prefix() {.table.table-form tbody > tr:last-child td, .table.table-form tbody > tr:last-child th {border-bottom: 1px solid #ddd;};}

#mainMenu #groupName {line-height: 33px; float: left;}
.checkbox-right {padding-left: 0px !important;}

#privList td.menus {border-right: 0; padding-right: 0; width: 170px !important;}
#privList td.menus + td {border-left: 0; padding-left: 0px; padding}
#privList .menus .checkbox-primary {float: left; width: 160px;}
#privList .menus .checkbox-primary:first-child {float: left; width: auto;}
#privList .menus a {margin-left: 10px;}

#privList {margin-bottom: 0px;}
#privList tr:hover>td {background-color: #FFF;}
#privList th.module {width: 142px; min-width: 142px; background-color: #EDEEF28C; font-weight: 600;}
#privList th.package {width: 142px; min-width: 142px; background-color: #F4F5F78C; font-weight: 600;}
#privList th .checkbox-inline input {margin-left: 0px;}
#privList .check-all {display: inline-block;}
#privList .check-all > label {padding-left: 0; font-weight: 600;}
#privList .text-middle.text-left {padding-left: 24px;}
#privList tbody > tr > td {padding: 4px 20px 20px 20px;}
#privList .group-item {margin-top: 16px;}
#privList .group-item .checkbox-inline label {padding-left: 3px;}
.table-bordered td, .table-bordered th {border-top-style: dashed; border-bottom-style: dashed; border-color: #FFF;}
.table-bordered td, .table-bordered thead th:last-child {border-color: #F5F6F8;}
.check-all>label.checkbox-indeterminate-block:before {content: ' '; opacity: 1; transform: scale(1);}
.priv-footer {padding: 14px; z-index: 11; box-shadow: inset 0 0px 0px rgb(0 0 0 / 10%), 0 0 5px rgb(0 0 0 / 10%); position: relative; min-height: 40px; background: #fff; border-radius: 0 0 4px 4px;}
.priv-footer .btn {margin 0px; padding-top: 7px;}
.priv-footer .check-all {display: inline-block;}
.priv-footer .check-all label {padding-left: 0px; margin-bottom: 0px; font-weight: 400;}
.priv-footer #submit {margin: 0px 20px;}
main#main {padding-bottom: 0px;}
.side .priv-panel {padding: 16px;}

#privPackageList th.module {width: 142px; min-width: 142px; max-width: 142px; background-color: #EDEEF28C; font-weight: 600; padding-left: 20px; border-color: #FFF;}
#privPackageList .module label {padding-left: 0; overflow: visible;}
#privPackageList td.package-column {padding: 20px 16px; background-color: #FFF;}
#privPackageList div.package {display: inline-block; width: calc(25% - 5px); padding: 10px 0;}
#privPackageList .package + .package {padding: 0;}
#privPackageList .package .check-all {display: inline-block; height: 20px;}
#privPackageList .package input {margin-top: 0px; margin-left: 0px; left: 0px; height: 20px;}
#privPackageList .package label {padding-left: 0; display: inline-block;}
#privPackageList .privs, #privPackageList .menus-privs {padding: 20px 20px 0 20px; margin: 10px 0px;border: 1px solid #D9DBE1; background-color: #F9FAFB; position: relative; display: block; max-width: unset; box-shadow: unset; z-index: 1;}
#privPackageList .menus-privs {margin-top: 35px; border-style: dashed;}
#privPackageList .popover .popover-content {display: inline-block; padding: 0;}
#privPackageList .popover .arrow {border-bottom-color: #D9DBE1; top: -10px;}
#privPackageList .popover .arrow:after {border-bottom-color: #F9FAFB;}
#privPackageList .group-item .checkbox-inline label {padding-left: 0}
#privPackageList .group-item {display: inline-block; width: 158px; margin-bottom: 15px;}
#privPackageList .group-item > .checkbox-primary {display: inline-block;}
#privPackageList .priv-toggle.icon {width: 12px; height: 12px; position: relative; display: inline-block; color: #9EA3B0; margin-left: 4px; line-height: 12px;}
#privPackageList .priv-toggle.icon:before {content: "\e925"; font-size: 12px; min-width: unset; transform: scale(0.5); font-weight: 900;}
#privPackageList .priv-toggle.icon:after {position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; display: inline-block; content: ""; border: 1px solid; border-radius: 10%;}
#privPackageList .priv-toggle.icon.open:before {content: "\e926";}

.table-bymodule select option:hover {color: #fff;}
