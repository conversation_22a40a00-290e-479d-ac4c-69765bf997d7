.container {margin: 0 auto;}

.modal-dialog {width: 100%;}
.checkbox-primary {overflow: hidden;}
.pgmWidth {width: 48% !important;}
#whitelistBox .checkbox-inline {display: inline-block !important; padding-left: 0px !important;}
#whitelistBox .checkbox-inline:first-child {margin-left: 10px !important;}
#budget {border-right: 0px;}
.divider {background: #f1f1f1 !important; width: 20px !important; padding: 1px !important;}
.gray {background: #f1f1f1 !important;}

.side-col {padding-right: 0px;}
div.divider {vertical-align: middle;}

.main-row {margin-top: 20px; padding: 20px; background: #f1f1f1;}
.main-row .side-col .nav li.active div a {background: #e3f2fd; color: #000; border-radius: 4px;}
.main-row .side-col .nav li.currentPage div a {background: #006af1; color: #fff; border-radius: 4px;}
.main-row .side-col .nav li div a {width: 80%; display: inline-block; padding: 5px 15px; margin-bottom: 5px;}
.main-row > [class*=" col-"], .main-row > [class*="-col"], .main-row > [class^=col-] {vertical-align: middle}

#lineBox > .cell {height: 700px; overflow: scroll;}
#source > .cell {padding: 0; position: relative; height: 700px;}
#source > .cell > .lineGroup-title {position: absolute; top: 0; left: 0; right: 0; padding: 10px;}
#source > .cell > .line-groups {position: absolute; top: 40px; left: 0; bottom: 0; right: 0; overflow: auto; padding: 0 10px 10px 10px;}
#source .lineGroup-title .item {float: left;}
#source .lineGroup-title .item:first-child {width: 170px; margin-left: 6px;}
#source .lineGroup>div {display: table-cell;}
#source .lineGroup {background: #f1f1f1; border-radius: 4px; display: table; width: 100%;}
#source .lineGroup+.lineGroup {margin-top: 10px;}
#source .lineGroup .productList {vertical-align: middle; padding: 6px;}
#source .lineGroup .projectList .scroll-handle {max-height: 200px; overflow-y: scroll; padding: 6px;}
#source .lineGroup .productList {width: 170px;}
#lineList .checkbox-primary {display: inline-block;}
.main-row .side-col .check-allLine > label {font-weight: 700}
.sprintGroup {margin-left: 6px;}
.programParams td > label:first-child {margin-right: 30px;}
.programParams td > label:last-child {margin-left: 0px;}
.programParams > tbody > tr > td {padding: 7px 30px;}

.projectList .scroll-handle .checkbox-primary {display: inline-block;}
.sprintGroup .sprintItem .checkbox-primary {display: inline-block;}

