<?php
/**
 * The task module English file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     task
 * @version     $Id: en.php 5040 2013-07-06 06:22:18Z <EMAIL> $
 * @link        https://www.zentao.net
 */
$lang->task->index               = "Accueil";
$lang->task->browse              = "Liste des Tâches";
$lang->task->create              = "Créer Tâche";
$lang->task->batchCreate         = "Créer par Lots";
$lang->task->batchCreateChildren = "Créer sous-tâches par lots";
$lang->task->batchEdit           = "Edition par lots";
$lang->task->batchChangeModule   = "Change Modules par lots";
$lang->task->batchClose          = "Fermeture par lots";
$lang->task->batchCancel         = "Annulation par lots";
$lang->task->edit                = "Editer Tâche";
$lang->task->delete              = "Supprimer";
$lang->task->deleteAction        = "supprimer Tâche";
$lang->task->deleted             = "Supprimée";
$lang->task->delayed             = 'Ajournées';
$lang->task->view                = "Détail Tâche";
$lang->task->logEfforts          = "Effort";
$lang->task->record              = "Estimés";
$lang->task->recordedBy          = "Recorded By";
$lang->task->teamConsumed        = "Tean Consumed";
$lang->task->start               = "Démarrer";
$lang->task->startAction         = "Démarrer Tâche";
$lang->task->restart             = "Continuer";
$lang->task->restartAction       = "Continuer Tâche";
$lang->task->finishAction        = "Terminer Tâche";
$lang->task->finish              = "Finie";
$lang->task->pause               = "Interrompre";
$lang->task->pauseAction         = "Interrompre Tâche";
$lang->task->close               = "Fermer";
$lang->task->closeAction         = "Fermer Tâche";
$lang->task->cancel              = "Annuler";
$lang->task->cancelAction        = "Annuler Tâche";
$lang->task->activateAction      = "Activer Tâche";
$lang->task->activate            = "Activer";
$lang->task->activatedDate       = "Activate Date";
$lang->task->export              = "Exporter Données";
$lang->task->exportAction        = "Exporter Tâche";
$lang->task->reportChart         = "Rapport Graphique";
$lang->task->fromBug             = 'à partir Bug';
$lang->task->fromBugID           = 'From Bug ID';
$lang->task->case                = 'Associée CasTest';
$lang->task->process             = 'Process Task';
$lang->task->confirmStoryChange  = "Confirmer Changement";
$lang->task->confirmDeleteParent = 'La suppression d\'une tâche parent supprimera également toutes ses tâches enfants. Êtes-vous sûr de vouloir supprimer cette tâche ?';
$lang->task->storyChange         = "Story Changée";
$lang->task->progress            = 'Progression';
$lang->task->progressAB          = 'Progression';
$lang->task->progressTips        = 'Coût/(Coût+Reste)';
$lang->task->copy                = 'Copier Tâche';
$lang->task->waitTask            = 'Tâche en attente';
$lang->task->allModule           = 'Tous Modules';
$lang->task->replace             = 'Replace';
$lang->task->committed           = 'Committed';
$lang->task->myEffort            = 'My Effort';
$lang->task->allEffort           = 'Team Effort';
$lang->task->teamOrder           = 'Order';
$lang->task->manageTeam          = 'Manage Team';
$lang->task->unfoldEffort        = 'Unfold Effort';
$lang->task->foldEffort          = 'Fold Effort';
$lang->task->addEffort           = 'Add Effort';
$lang->task->codeBranch          = 'Code Branch';
$lang->task->unlinkBranch        = 'Unlink code branch';
$lang->task->branchName          = 'Branch Name';
$lang->task->branchFrom          = 'Create from';
$lang->task->codeRepo            = 'Code Library';
$lang->task->relatedBranch       = 'Related Branch';
$lang->task->keywords            = 'Tags';
$lang->task->syncStory           = 'Sync to task';
$lang->task->addSibling          = 'Add Sibling';
$lang->task->addSub              = 'Add Child';
$lang->task->otherExecution      = 'Other Execution';

$lang->task->common            = 'Tâche';
$lang->task->id                = 'ID';
$lang->task->project           = $lang->projectCommon;
$lang->task->execution         = 'Execution';
$lang->task->stage             = $lang->executionCommon;
$lang->task->module            = 'Module';
$lang->task->moduleAB          = 'Module';
$lang->task->design            = 'Design';
$lang->task->story             = 'Story';
$lang->task->storyAB           = 'Story';
$lang->task->storySpec         = 'Description Story';
$lang->task->storyVerify       = "Critères d'Acceptance";
$lang->task->storyVersion      = 'Story Version';
$lang->task->storyFiles        = 'Story Dateien';
$lang->task->designVersion     = "Design Version";
$lang->task->color             = 'Couleur';
$lang->task->name              = 'Nom';
$lang->task->type              = 'Type';
$lang->task->typeAB            = 'Type';
$lang->task->mode              = 'Mode';
$lang->task->sync2Gitlab       = 'Sync to GitLab';
$lang->task->pri               = 'Priorité';
$lang->task->mailto            = 'Mailto';
$lang->task->estimate          = 'Estimés';
$lang->task->estimateAB        = 'Esti.';
$lang->task->estimateLabel     = 'Est(Unit: h)';
$lang->task->left              = 'Reste Heures';
$lang->task->leftAB            = 'Reste';
$lang->task->consumed          = 'Coût Total';
$lang->task->currentConsumed   = 'Coût Actuel';
$lang->task->myConsumed        = 'Mon Coût';
$lang->task->consumedAB        = 'Coût';
$lang->task->consumedHours     = 'Coût';
$lang->task->hour              = 'Heures';
$lang->task->consumedThisTime  = 'Coût';
$lang->task->leftThisTime      = 'Reste';
$lang->task->datePlan          = 'Période';
$lang->task->estStarted        = 'Date départ';
$lang->task->realStarted       = 'Départ réel';
$lang->task->date              = 'Date';
$lang->task->deadline          = 'Date Butoir';
$lang->task->deadlineAB        = 'Date Butoir';
$lang->task->status            = 'Statut';
$lang->task->statusAB          = 'Status';
$lang->task->subStatus         = 'Sous-statut';
$lang->task->desc              = 'Description';
$lang->task->version           = 'Version';
$lang->task->estimateStartDate = 'Estimate Start Date';
$lang->task->actualStartDate   = 'Actual Start Date';
$lang->task->planDuration      = 'Plan Duration';
$lang->task->realDuration      = 'Real Duration';
$lang->task->version           = 'Version';
$lang->task->assign            = 'Assigner';
$lang->task->assignAction      = 'Assigner Tâche';
$lang->task->assignTo          = $lang->task->assign;
$lang->task->batchAssignTo     = 'Assigner en lots';
$lang->task->assignedTo        = 'Affectée à';
$lang->task->assignedToAB      = 'Assigné à';
$lang->task->assignedDate      = 'Date Assign.';
$lang->task->openedBy          = 'Créé par';
$lang->task->openedByAB        = 'Créer';
$lang->task->openedDate        = 'Date création';
$lang->task->openedDateAB      = 'Date création';
$lang->task->finishedBy        = 'Terminé par';
$lang->task->finishedByAB      = 'Terminé par';
$lang->task->finishedDate      = 'Date fin réelle';
$lang->task->finishedDateAB    = 'Date fin réelle';
$lang->task->finishedList      = 'Finie par';
$lang->task->canceledBy        = 'Annulée par';
$lang->task->canceledDate      = 'Date Annul';
$lang->task->closedBy          = 'Fermée par';
$lang->task->closedDate        = 'Date Fermeture';
$lang->task->closedReason      = 'Raison fermeture';
$lang->task->lastEditedBy      = 'Edité par';
$lang->task->lastEditedDate    = 'Date Modif';
$lang->task->lastEdited        = 'Edité par';
$lang->task->recordWorkhour    = 'Effort';
$lang->task->editEffort        = 'Editer Estimé';
$lang->task->deleteWorkhour    = 'Supprimer Estimés';
$lang->task->repo              = 'Repo';
$lang->task->mr                = 'Merge Requests';
$lang->task->entry             = 'Code Path';
$lang->task->lines             = 'Lines';
$lang->task->v1                = 'Version A';
$lang->task->v2                = 'Version B';
$lang->task->vision            = 'Vision';
$lang->task->colorTag          = 'Couleur';
$lang->task->files             = 'Fichiers';
$lang->task->my                = 'My ';
$lang->task->hasConsumed       = 'Consommé';
$lang->task->multiple          = 'Utilisateurs Multiples';
$lang->task->multipleAB        = 'M';
$lang->task->teamSetting       = 'Team Setting';
$lang->task->team              = 'Equipe';
$lang->task->transfer          = 'Transferer';
$lang->task->transferTo        = 'Transferer à';
$lang->task->children          = 'Sous-Tâche';
$lang->task->childrenAB        = 'C';
$lang->task->parent            = 'Tâche Parent';
$lang->task->parentAB          = 'Parent';
$lang->task->showParent        = 'Show Parent Tasks';
$lang->task->lblPri            = 'P';
$lang->task->lblHour           = '(h)';
$lang->task->lblTestStory      = 'Story Testée';
$lang->task->teamMember        = 'Team Member';
$lang->task->addMember         = 'Add Member';
$lang->task->to                = 'To';
$lang->task->suffixHour        = 'h';
$lang->task->update            = 'Update';
$lang->task->isParent          = 'Is Parent';
$lang->task->path              = 'Path';

/* Fields of zt_taskestimate. */
$lang->task->task    = 'Task';
$lang->task->account = 'Account';
$lang->task->work    = 'Work';

$lang->task->recordWorkhourAction = 'Record Estimate';

$lang->task->ditto             = 'Idem';
$lang->task->dittoNotice       = "Cette Tâche n'est pas associée au %s comme la précédente peut l'être !";
$lang->task->selectTestStory   = "Select {$lang->SRCommon}";
$lang->task->selectAllUser     = 'Tous Utilis.';
$lang->task->noStory           = 'Aucune Story associée';
$lang->task->noAssigned        = 'Non affectées';
$lang->task->noFinished        = 'Non Finies';
$lang->task->noClosed          = 'Non Fermées';
$lang->task->yesterdayFinished = 'Tâche Terminée Hier';
$lang->task->allTasks          = 'Tâche';
$lang->task->linkMR            = 'Related MRs';
$lang->task->linkPR            = 'Related PRs';
$lang->task->linkCommit        = 'Related Commits';

$lang->task->statusList['']        = '';
$lang->task->statusList['wait']    = 'A Faire';
$lang->task->statusList['doing']   = 'En Cours';
$lang->task->statusList['done']    = 'Faite';
$lang->task->statusList['pause']   = 'Interrompue';
$lang->task->statusList['cancel']  = 'Annulée';
$lang->task->statusList['closed']  = 'Fermée';

$lang->task->typeList['']        = '';
$lang->task->typeList['design']  = 'Design';
$lang->task->typeList['devel']   = 'Développement';
$lang->task->typeList['request'] = 'Request';
$lang->task->typeList['test']    = 'Test';
$lang->task->typeList['study']   = 'Analyse';
$lang->task->typeList['discuss'] = 'Discussion';
$lang->task->typeList['ui']      = 'UI';
$lang->task->typeList['affair']  = 'Commercial';
$lang->task->typeList['misc']    = 'Divers';

$lang->task->priList[0]  = '';
$lang->task->priList[1]  = '1';
$lang->task->priList[2]  = '2';
$lang->task->priList[3]  = '3';
$lang->task->priList[4]  = '4';

$lang->task->reasonList['']       = '';
$lang->task->reasonList['done']   = 'Fait';
$lang->task->reasonList['cancel'] = 'Annulé';

$lang->task->modeList['linear'] = 'Multiple Person Serial';
$lang->task->modeList['multi']  = 'Multiple Person Parallel';

$lang->task->editModeList['single'] = 'Single';
$lang->task->editModeList['linear'] = 'Serial';
$lang->task->editModeList['multi']  = 'Parallel';

$lang->task->viewTypeList['tiled'] = 'Tiled';
$lang->task->viewTypeList['tree']  = 'Tree';

$lang->task->afterChoices['continueAdding'] = ' Continuer Ajouter Tâches';
$lang->task->afterChoices['toTaskList']     = 'Aller à la liste des Tâches';
$lang->task->afterChoices['toStoryList']    = 'Aller à la liste des Stories';

$lang->task->legendBasic  = 'Infos de Base';
$lang->task->legendEffort = 'Effort';
$lang->task->legendLife   = 'Vie de la Tâche';
$lang->task->legendDesc   = 'Description Tâche';
$lang->task->legendDetail = 'Task Detail';
$lang->task->legendMisc   = 'Misc.';

$lang->task->action = new stdclass();
$lang->task->action->linked2revision       = array('main' => '$date, linked by <strong>$actor</strong> to Revision <strong>$extra</strong>.');
$lang->task->action->unlinkedfromrevision  = array('main' => '$date, unlinked by <strong>$actor</strong> to Revision <strong>$extra</strong>.');
$lang->task->action->autobyparentrestarted = array('main' => '$date, continued parent task by <strong>$actor</strong>, this task automatically continue.');
$lang->task->action->autobychildrestarted  = array('main' => '$date, continued child task by <strong>$actor</strong>, this task automatically continue.');
$lang->task->action->autobyparentpaused    = array('main' => '$date, paused parent task by <strong>$actor</strong>, this task automatically pause.');
$lang->task->action->autobyparentcanceled  = array('main' => '$date, cancelled parent task by <strong>$actor</strong>, this task automatically cancel.');
$lang->task->action->autobyparentclosed    = array('main' => '$date, closed parent task by <strong>$actor</strong>, this task automatically close.');
$lang->task->action->autobychildstarted    = array('main' => '$date, started child task by <strong>$actor</strong>, this task automatically start.');
$lang->task->action->autobychildfinished   = array('main' => '$date, finished child task by <strong>$actor</strong>, this task automatically finish.');
$lang->task->action->autobychildactivated  = array('main' => '$date, activated child task by <strong>$actor</strong>, this task automatically activate.');

$lang->task->confirmDelete             = "Voulez-vous réellement supprimer cette tâche ?";
$lang->task->confirmDeleteEffort       = "Voulez-vous la supprimer ?";
$lang->task->confirmDeleteLastEffort   = "Do you want to delete the log? After deleting the last work log, the task status will be adjusted to Not Started.";
$lang->task->copyStoryTitle            = '<span style="color:blue;">StoryModel</span>';
$lang->task->afterSubmit               = "Suiv. ";
$lang->task->successSaved              = "Créée !";
$lang->task->delayWarning              = "Retard %s jours";
$lang->task->remindBug                 = "Cette tâche a été convertie depuis un bug. Voulez-vous mettre à jour le Bug : %s ?";
$lang->task->remindIssue               = "This task is converted from a issue. Do you want to update the Issue:%s?";
$lang->task->confirmChangeExecution    = "Si vous changez {$lang->executionCommon}, Module, Story et AssignedTo seront également changés. Voulez-vous le faire ?";
$lang->task->confirmFinish             = '"Heures Restantes" à 0. Voulez-vous passer le statut à "Terminée" ?';
$lang->task->confirmRecord             = '"Heures Restantes" à 0. Voulez-vous passer le statut de la tâche à "Finie" ?';
$lang->task->confirmTransfer           = '"Left Hour" is 0，Do you want to assign to <strong>%s</strong> task?';
$lang->task->noticeTaskStart           = '"Cost Hour" and "Left Hour" cannot be 0 at the same time.';
$lang->task->noticeLinkStory           = "No story has been linked. You can for this %s";
$lang->task->noticeLinkStoryNoProduct  = "No story has been linked.";
$lang->task->noticeSaveRecord          = "Votre temps n'a pas été sauvé. Enregistrez-le d'abord.";
$lang->task->noticeManageTeam          = 'Task status is %s, can not manage team.';
$lang->task->teamNotEmpty              = 'Team can not be empty.';
$lang->task->commentActions            = '%s. %s, commenté par <strong>%s</strong>.';
$lang->task->deniedNotice              = 'Seulement le %s peut %s la tâche.';
$lang->task->deniedStatusNotice        = 'The task status is %s, the effort cannot be maintained.';
$lang->task->transferNotice            = 'Linear task cannot be transferred.';
$lang->task->noTask                    = "Pas de tâche pour l'instant. ";
$lang->task->noModule                  = '<div>You have no modules.</div><div>Manage now</div>';
$lang->task->createDenied              = "La création de tâches est interdite dans ce %s";
$lang->task->cannotDeleteParent        = 'Impossible de supprimer la tâche parente';
$lang->task->addChildTask              = 'Because the task has already consumed consumption, to ensure data consistency, we will help you create a subtask with the same name to record the consumption.';
$lang->task->selectTestStoryTip        = "The following {$lang->SRCommon} will be subtasks of this task";
$lang->task->effortOperateTips         = 'Only the project manager, the executive supervisor, and the department head have the authority to %s logs belonging to others.';
$lang->task->syncStoryToChildrenTip    = "Child tasks of %s do not have {$lang->SRCommon}, will {$lang->SRCommon} be synchronised with these child tasks?";

$lang->task->error = new stdclass();
$lang->task->error->totalNumber       = '"Coût Total" doit être numérique.';
$lang->task->error->consumedNumber    = '"Coût" doit être numérique.';
$lang->task->error->estimateNumber    = '"Les estimations" doivent toujours être un nombre positif.';
$lang->task->error->leftNumber        = '"Entrez" doit être numérique.';
$lang->task->error->recordMinus       = '%s should not be negative number.';
$lang->task->error->consumedSmall     = '"Coût Total" doit être > au dernier chiffre.';
$lang->task->error->dateEmpty         = 'Please enter "Date"';
$lang->task->error->consumedThisTime  = 'Entrez le "Coût en Heures"';
$lang->task->error->left              = 'Entrez les "Heures Restantes"';
$lang->task->error->work              = '"Commentaire" doit être <  %d caractères.';
$lang->task->error->teamMember        = 'Team members must be at least 2 people';
$lang->task->error->teamCantOperate   = 'Please activate the closed, suspended, and canceled tasks before setting the team.';
$lang->task->error->skipClose         = 'Tâche: %s non "Finie" ou "Annulée". Voulez-vous la fermer malgré tout ?';
$lang->task->error->closeParent       = 'Task: %s is the Parent Task, which is automatically closed after all subtasks under the Parent Task are closed and cannot be closed manually.';
$lang->task->error->consumed          = 'Tâche: %s heures doivent être < 0. Ignorer changements de cette tâche.';
$lang->task->error->assignedTo        = "Tâche Multi-user dans le statut courant ne peut pas être assignée à un membre qui ne fait pas partie de l'équipe.";
$lang->task->error->consumedEmpty     = 'Lorsque la "Coût Total" est de 0, la tâche ne peut pas être terminée. Veuillez d\'abord remplir "Coût Actuel".';
$lang->task->error->consumedEmptyAB   = '"Coût Actuel" est de 0, veuillez confirmer si vous souhaitez continuer à soumettre.';
$lang->task->error->deadlineSmall     = '"Date Limite" doit être supérieur à la "Date de Départ".';
$lang->task->error->alreadyStarted    = 'You cannot start this task, because it is started.';
$lang->task->error->realStartedEmpty  = '"Real Started" should not be empty.';
$lang->task->error->finishedDateEmpty = '"Finished Date" should not be empty.';
$lang->task->error->finishedDateSmall = '"Finished Date" should be > "Real Started"';
$lang->task->error->date              = 'The date should be <= today.';
$lang->task->error->leftEmptyAB       = 'When the task status is %s, "Hours Left" cannot be 0';
$lang->task->error->leftEmpty         = 'Task#%sWhen the task status is %s, "Left" cannot be 0';
$lang->task->error->notempty          = '%s must be > 0.';
$lang->task->error->teamLeftEmpty     = 'Please maintain team hours.';
$lang->task->error->beginLtExecution  = "The 'StartDate' of the task must be greater than or equal the 'Planned Begin' of %s to %s.";
$lang->task->error->beginGtExecution  = "The 'StartDate' of the task must be less than or equal the 'Planned End' of %s to %s.";
$lang->task->error->endGtExecution    = "The 'Deadline' of the task must be less than or equal the 'Planned End' of %s to %s.";
$lang->task->error->endLtExecution    = "The 'Deadline' of the task must be greater than or equal the 'Planned Begin' of %s to %s.";
$lang->task->error->dateExceed        = "Because the scheduled date of task %s exceeds the scheduled date of {$lang->execution->common}, it is automatically changed to the scheduled date of {$lang->execution->common}";
$lang->task->error->length            = "Length exceeds the limit of %d characters, cannot be saved. Please modify it again.";
$lang->task->error->emptyParentName   = "Contains subtasks, task names cannot be empty.";
$lang->task->error->noTestTask        = "Please select at least one {$lang->SRCommon}.";

/* Report. */
$lang->task->report = new stdclass();
$lang->task->report->common = 'Rapport';
$lang->task->report->select = 'Choix Type de Rapport';
$lang->task->report->create = 'Créer Rapport';
$lang->task->report->value  = 'Tâches';

$lang->task->report->charts['tasksPerExecution']    = 'Regroupé par ' . $lang->executionCommon . ' Tâche';
$lang->task->report->charts['tasksPerModule']       = 'Regroupé par Module';
$lang->task->report->charts['tasksPerAssignedTo']   = 'Regroupé par Assignation';
$lang->task->report->charts['tasksPerType']         = 'Regroupé par Type de Tâche';
$lang->task->report->charts['tasksPerPri']          = 'Regroupé par Priorité Tâche';
$lang->task->report->charts['tasksPerStatus']       = 'Regroupé par Statut de Tâche';
$lang->task->report->charts['tasksPerDeadline']     = 'Regroupé par date butoir';
$lang->task->report->charts['tasksPerEstimate']     = 'Regroupé par Estimé';
$lang->task->report->charts['tasksPerLeft']         = 'Regroupé par H. Restantes';
$lang->task->report->charts['tasksPerConsumed']     = 'Regroupé par H. Consommées';
$lang->task->report->charts['tasksPerFinishedBy']   = 'Regroupé par Finisseur';
$lang->task->report->charts['tasksPerClosedReason'] = 'Par Raison de Fermeture';
$lang->task->report->charts['finishedTasksPerDay']  = 'Par Tâches Finies/Jour';

$lang->task->report->options = new stdclass();
$lang->task->report->options->graph = new stdclass();
$lang->task->report->options->type   = 'pie';
$lang->task->report->options->width  = 500;
$lang->task->report->options->height = 140;

$lang->task->report->tasksPerExecution    = new stdclass();
$lang->task->report->tasksPerModule       = new stdclass();
$lang->task->report->tasksPerAssignedTo   = new stdclass();
$lang->task->report->tasksPerType         = new stdclass();
$lang->task->report->tasksPerPri          = new stdclass();
$lang->task->report->tasksPerStatus       = new stdclass();
$lang->task->report->tasksPerDeadline     = new stdclass();
$lang->task->report->tasksPerEstimate     = new stdclass();
$lang->task->report->tasksPerLeft         = new stdclass();
$lang->task->report->tasksPerConsumed     = new stdclass();
$lang->task->report->tasksPerFinishedBy   = new stdclass();
$lang->task->report->tasksPerClosedReason = new stdclass();
$lang->task->report->finishedTasksPerDay  = new stdclass();

$lang->task->report->tasksPerExecution->item    = $lang->executionCommon;
$lang->task->report->tasksPerModule->item       = 'Module';
$lang->task->report->tasksPerAssignedTo->item   = 'Détenteur';
$lang->task->report->tasksPerType->item         = 'Type';
$lang->task->report->tasksPerPri->item          = 'Priorité';
$lang->task->report->tasksPerStatus->item       = 'Statut';
$lang->task->report->tasksPerDeadline->item     = 'Date';
$lang->task->report->tasksPerEstimate->item     = 'Estimé';
$lang->task->report->tasksPerLeft->item         = 'Reste à faire (H)';
$lang->task->report->tasksPerConsumed->item     = 'Consommé';
$lang->task->report->tasksPerFinishedBy->item   = 'Finisseur';
$lang->task->report->tasksPerClosedReason->item = 'Raison';
$lang->task->report->finishedTasksPerDay->item  = 'Date';

$lang->task->report->tasksPerExecution->graph    = new stdclass();
$lang->task->report->tasksPerModule->graph       = new stdclass();
$lang->task->report->tasksPerAssignedTo->graph   = new stdclass();
$lang->task->report->tasksPerType->graph         = new stdclass();
$lang->task->report->tasksPerPri->graph          = new stdclass();
$lang->task->report->tasksPerStatus->graph       = new stdclass();
$lang->task->report->tasksPerDeadline->graph     = new stdclass();
$lang->task->report->tasksPerEstimate->graph     = new stdclass();
$lang->task->report->tasksPerLeft->graph         = new stdclass();
$lang->task->report->tasksPerConsumed->graph     = new stdclass();
$lang->task->report->tasksPerFinishedBy->graph   = new stdclass();
$lang->task->report->tasksPerClosedReason->graph = new stdclass();
$lang->task->report->finishedTasksPerDay->graph  = new stdclass();

$lang->task->report->tasksPerExecution->graph->xAxisName    = $lang->executionCommon;
$lang->task->report->tasksPerModule->graph->xAxisName       = 'Module';
$lang->task->report->tasksPerAssignedTo->graph->xAxisName   = 'Utilisateur';
$lang->task->report->tasksPerType->graph->xAxisName         = 'Type';
$lang->task->report->tasksPerPri->graph->xAxisName          = 'Priorité';
$lang->task->report->tasksPerStatus->graph->xAxisName       = 'Statut';
$lang->task->report->tasksPerDeadline->graph->xAxisName     = 'Date';
$lang->task->report->tasksPerEstimate->graph->xAxisName     = 'Estimé';
$lang->task->report->tasksPerLeft->graph->xAxisName         = 'R.à.Faire(h)';
$lang->task->report->tasksPerConsumed->graph->xAxisName     = 'Consommé (h)';
$lang->task->report->tasksPerFinishedBy->graph->xAxisName   = 'Utilisateur';
$lang->task->report->tasksPerClosedReason->graph->xAxisName = 'Raison Fermeture';

$lang->task->report->finishedTasksPerDay->type             = 'bar';
$lang->task->report->finishedTasksPerDay->graph->xAxisName = 'Date';

$lang->taskestimate = new stdclass();
$lang->taskestimate->consumed = 'Estimés';

$lang->task->overEsStartDate = 'The %s schedule start time has exceeded, please modify the %s schedule start time first';
$lang->task->overEsEndDate   = 'The %s schedule end time has exceeded, please modify the %s schedule end time first';

$lang->task->overParentEsStarted = 'StartDate is less than the parent task\'s startDate: %s';
$lang->task->overParentDeadline  = 'Deadline is greater than the parent task\'s deadline: %s';
$lang->task->overChildEstStarted = "Existed child task's startDate is less than the task's startDate: %s";
$lang->task->overChildDeadline   = "Existed child task's deadline is greater than the task's deadline: %s";

$lang->task->disabledHint = new stdclass();
$lang->task->disabledHint->assignedConfirmStoryChange = 'Changes can only be confirmed by the assignee.';
$lang->task->disabledHint->memberConfirmStoryChange   = 'Changes can only be confirmed by the task team member.';
