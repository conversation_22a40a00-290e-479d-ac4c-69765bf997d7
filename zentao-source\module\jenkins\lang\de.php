<?php
$lang->jenkins->common        = '<PERSON>';
$lang->jenkins->browse        = 'Browse Jenkins';
$lang->jenkins->create        = 'Create Jenkins';
$lang->jenkins->edit          = '<PERSON> Jenkins';
$lang->jenkins->delete        = 'Delete';
$lang->jenkins->confirmDelete = 'Do you want to delete this Jenkins server?';

$lang->jenkins->browseAction = 'Jenkins List';
$lang->jenkins->deleteAction = 'Delete Jenkins';

$lang->jenkins->id       = 'ID';
$lang->jenkins->name     = 'Name';
$lang->jenkins->url      = 'Service URL';
$lang->jenkins->token    = 'Token';
$lang->jenkins->account  = 'UserName';
$lang->jenkins->password = 'Password';

$lang->jenkins->lblCreate  = 'Create Jenkins Server';
$lang->jenkins->desc       = 'Description';
$lang->jenkins->tokenFirst = 'Use token if not empty.';
$lang->jenkins->tips       = 'Cancel "Prevent Cross Site Request Forgery exploits" when using password.';
$lang->jenkins->serverList = 'Server List';

$lang->jenkins->error = new stdclass();
$lang->jenkins->error->linkedJob    = 'Failed. This jenkins has associated with the compile.';
$lang->jenkins->error->unauthorized = 'Permission verification failed, please check account information';
