#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复完成时限字段格式
将数字格式的日期转换为标准的DATE格式
"""

import pymysql
import os
from dotenv import load_dotenv
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def excel_date_to_python_date(excel_date):
    """
    将Excel日期序列号转换为Python日期对象
    Excel的日期基准是1900年1月1日，但实际上是1899年12月30日
    """
    try:
        # Excel日期序列号转换
        if isinstance(excel_date, str):
            excel_date = float(excel_date)
        
        # Excel的基准日期是1899年12月30日
        base_date = datetime(1899, 12, 30)
        target_date = base_date + timedelta(days=excel_date)
        
        return target_date.strftime('%Y-%m-%d')
    except:
        return None

def get_db_connection():
    """获取数据库连接"""
    load_dotenv()
    
    return pymysql.connect(
        host=os.getenv('DB_HOST', 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com'),
        port=int(os.getenv('DB_PORT', 3306)),
        user=os.getenv('DB_USER', 'cyh'),
        password=os.getenv('DB_PASSWORD', 'Qq188788'),
        database=os.getenv('DB_NAME', 'kanban2'),
        charset='utf8mb4'
    )

def fix_completion_deadline():
    """修复完成时限字段格式"""
    try:
        connection = get_db_connection()
        logger.info("✅ 数据库连接成功")
        
        with connection.cursor() as cursor:
            # 1. 先查看当前数据
            logger.info("🔍 查看当前完成时限数据...")
            cursor.execute("SELECT id, work_theme, completion_deadline FROM supervision_items LIMIT 10")
            current_data = cursor.fetchall()
            
            logger.info("当前数据样本:")
            for row in current_data:
                logger.info(f"  ID: {row[0]}, 主题: {row[1][:30]}..., 完成时限: {row[2]}")
            
            # 2. 添加临时字段存储转换后的日期
            logger.info("\n🔧 添加临时日期字段...")
            try:
                cursor.execute("ALTER TABLE supervision_items ADD COLUMN completion_deadline_temp DATE")
                logger.info("✅ 临时字段添加成功")
            except Exception as e:
                if "Duplicate column name" in str(e):
                    logger.info("✅ 临时字段已存在")
                else:
                    raise e
            
            # 3. 转换现有数据
            logger.info("\n🔄 转换现有数据...")
            cursor.execute("SELECT id, completion_deadline FROM supervision_items")
            all_data = cursor.fetchall()
            
            converted_count = 0
            failed_count = 0
            
            for row in all_data:
                item_id, deadline = row
                
                # 尝试转换日期
                converted_date = None
                
                # 如果是数字格式（Excel日期序列号）
                try:
                    if deadline and deadline.strip():
                        # 尝试作为Excel日期序列号处理
                        converted_date = excel_date_to_python_date(deadline)
                        
                        if converted_date:
                            # 更新临时字段
                            cursor.execute(
                                "UPDATE supervision_items SET completion_deadline_temp = %s WHERE id = %s",
                                (converted_date, item_id)
                            )
                            converted_count += 1
                            logger.info(f"  转换成功: {deadline} -> {converted_date}")
                        else:
                            failed_count += 1
                            logger.warning(f"  转换失败: ID={item_id}, 原值={deadline}")
                except Exception as e:
                    failed_count += 1
                    logger.warning(f"  转换失败: ID={item_id}, 原值={deadline}, 错误={str(e)}")
            
            logger.info(f"\n📊 转换结果: 成功 {converted_count} 条, 失败 {failed_count} 条")
            
            # 4. 删除原字段，重命名临时字段
            if converted_count > 0:
                logger.info("\n🔄 替换原字段...")
                
                # 删除原字段
                cursor.execute("ALTER TABLE supervision_items DROP COLUMN completion_deadline")
                logger.info("✅ 删除原字段成功")
                
                # 重命名临时字段
                cursor.execute("ALTER TABLE supervision_items CHANGE completion_deadline_temp completion_deadline DATE COMMENT '完成时限'")
                logger.info("✅ 重命名字段成功")
                
                # 提交更改
                connection.commit()
                logger.info("✅ 所有更改已提交")
            else:
                logger.warning("⚠️  没有成功转换的数据，跳过字段替换")
            
            # 5. 验证结果
            logger.info("\n🔍 验证转换结果...")
            cursor.execute("SELECT id, work_theme, completion_deadline FROM supervision_items LIMIT 10")
            new_data = cursor.fetchall()
            
            logger.info("转换后数据样本:")
            for row in new_data:
                logger.info(f"  ID: {row[0]}, 主题: {row[1][:30]}..., 完成时限: {row[2]}")
        
        connection.close()
        logger.info("\n🎉 完成时限字段格式修复完成！")
        
    except Exception as e:
        logger.error(f"❌ 修复失败: {str(e)}")
        raise e

if __name__ == "__main__":
    logger.info("🚀 开始修复完成时限字段格式...")
    fix_completion_deadline()
