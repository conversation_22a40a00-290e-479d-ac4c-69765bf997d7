#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import sys

def check_excel_structure():
    """检查Excel文件结构"""
    excel_path = r"E:\HuaweiMoveData\Users\cyh\Desktop\督办管理表_20250731_061252.xlsx"
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        
        print(f"Excel文件路径: {excel_path}")
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print("\n列名:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1}. {col}")
        
        print("\n前5行数据:")
        print(df.head().to_string())
        
        # 检查操作类型列
        if '操作类型' in df.columns:
            print(f"\n操作类型列的值:")
            operation_types = df['操作类型'].value_counts(dropna=False)
            print(operation_types)
            
            print(f"\n操作类型列的空值数量: {df['操作类型'].isna().sum()}")
            print(f"操作类型列的非空值数量: {df['操作类型'].notna().sum()}")
        else:
            print("\n❌ 没有找到'操作类型'列")
            
        # 检查ID列
        if 'ID' in df.columns:
            print(f"\nID列的值:")
            id_values = df['ID'].value_counts(dropna=False)
            print(id_values.head(10))
            
            print(f"\nID列的空值数量: {df['ID'].isna().sum()}")
            print(f"ID列的非空值数量: {df['ID'].notna().sum()}")
        else:
            print("\n❌ 没有找到'ID'列")
            
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")

if __name__ == "__main__":
    check_excel_structure()
