title: table zt_programactivity
desc: "过程活动"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: project
    note: "所属项目"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: process
    note: "过程ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: activity
    note: "活动ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: name
    range: 0
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: content
    note: ""
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: reason
    note: ""
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: result
    note: "是否执行"
    range: yes,no
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: linkedBy
    note: "裁剪者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: "创建者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
