#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PMO系统全功能测试脚本
测试所有API接口和功能模块
"""

import asyncio
import aiohttp
import json
import os
import time
from datetime import datetime
import traceback

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_PROJECT_CODE = "TEST_001"
TEST_USER_ID = "na10000014"
# 模拟认证token（实际项目中应该通过登录获取）
AUTH_TOKEN = "test_token"

class PMOTester:
    def __init__(self):
        self.session = None
        self.test_results = []
        self.failed_tests = []
        self.auth_headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {AUTH_TOKEN}",
            "X-User-ID": TEST_USER_ID,
            "X-User-Name": "测试用户"
        }
        
    async def setup(self):
        """初始化测试环境"""
        self.session = aiohttp.ClientSession()
        print("🚀 PMO系统功能测试开始")
        print("=" * 60)
        
    async def cleanup(self):
        """清理测试环境"""
        if self.session:
            await self.session.close()
        print("\n" + "=" * 60)
        print(f"✅ 测试完成，共 {len(self.test_results)} 个测试")
        print(f"✅ 成功：{len([r for r in self.test_results if r['success']])} 个")
        print(f"❌ 失败：{len(self.failed_tests)} 个")
        
        if self.failed_tests:
            print("\n❌ 失败的测试：")
            for test in self.failed_tests:
                print(f"  - {test['name']}: {test['error']}")
    
    async def test_api(self, name, method, url, data=None, headers=None, use_auth=True):
        """通用API测试方法"""
        try:
            print(f"🧪 测试 {name}...")

            if headers is None:
                headers = self.auth_headers if use_auth else {"Content-Type": "application/json"}

            async with self.session.request(method, f"{BASE_URL}{url}",
                                          json=data, headers=headers) as response:
                response_data = await response.text()
                
                if response.status == 200:
                    print(f"  ✅ {name} - 成功")
                    self.test_results.append({
                        "name": name,
                        "success": True,
                        "status": response.status,
                        "response": response_data[:200] + "..." if len(response_data) > 200 else response_data
                    })
                    return json.loads(response_data) if response_data else {}
                else:
                    print(f"  ❌ {name} - 失败 (状态码: {response.status})")
                    error_msg = f"HTTP {response.status}: {response_data}"
                    self.failed_tests.append({"name": name, "error": error_msg})
                    self.test_results.append({
                        "name": name,
                        "success": False,
                        "status": response.status,
                        "error": error_msg
                    })
                    return None
                    
        except Exception as e:
            print(f"  ❌ {name} - 异常: {str(e)}")
            self.failed_tests.append({"name": name, "error": str(e)})
            self.test_results.append({
                "name": name,
                "success": False,
                "error": str(e)
            })
            return None
    
    async def test_dashboard_apis(self):
        """测试仪表板相关API"""
        print("\n📊 测试仪表板功能")
        print("-" * 40)

        # 测试红黑榜
        await self.test_api("红黑榜数据", "GET", "/api/v1/dashboard/redblack")
        
    async def test_project_apis(self):
        """测试项目管理相关API"""
        print("\n📋 测试项目管理功能")
        print("-" * 40)

        # 测试项目列表
        await self.test_api("项目列表", "GET", "/api/v1/project/list")

        # 测试所有项目
        await self.test_api("所有项目", "GET", "/api/v1/project/all")

        # 测试项目选项
        await self.test_api("项目选项", "GET", "/api/v1/project/options")

        # 测试分类选项
        await self.test_api("分类选项", "GET", "/api/v1/project/categories/options")
        
    async def test_ai_assistant_apis(self):
        """测试AI助手相关API"""
        print("\n🤖 测试AI助手功能")
        print("-" * 40)

        # 测试AI状态
        await self.test_api("AI状态", "GET", "/api/v1/ai/status", use_auth=False)

        # 测试知识库状态
        await self.test_api("知识库状态", "GET", "/api/v1/ai/knowledge/status", use_auth=False)

        # 测试数据库AI聊天
        chat_data = {
            "message": "你好，请介绍一下PMO系统的功能",
            "conversation_id": "test_conversation"
        }
        await self.test_api("数据库AI聊天", "POST", "/api/v1/ai/db-chat", chat_data, use_auth=False)
        
    async def test_archive_apis(self):
        """测试档案管理相关API"""
        print("\n📁 测试档案管理功能")
        print("-" * 40)

        # 测试档案清单
        await self.test_api("档案清单", "GET", "/api/v1/archive-management/checklist", use_auth=False)

        # 测试档案文件
        await self.test_api("档案文件", "GET", "/api/v1/archive-management/files", use_auth=False)

        # 测试项目档案
        await self.test_api("项目档案", "GET", f"/api/v1/archive/projects/{TEST_PROJECT_CODE}", use_auth=False)
        
    async def test_auth_apis(self):
        """测试认证相关API"""
        print("\n🔐 测试认证功能")
        print("-" * 40)

        # 测试用户信息
        await self.test_api("用户信息", "GET", "/api/v1/auth/me", use_auth=False)

        # 测试用户列表
        await self.test_api("用户列表", "GET", "/api/v1/users/", use_auth=False)
        
    async def test_ai_update_function(self):
        """测试AI更新功能"""
        print("\n🔄 测试AI更新功能")
        print("-" * 40)
        
        # 创建测试档案文件
        await self.create_test_archive_files()
        
        # 测试AI更新
        result = await self.test_api("AI更新分析", "POST", f"/api/v1/project/{TEST_PROJECT_CODE}/ai-update")
        
        if result and result.get("code") == 200:
            print("  📝 AI更新分析成功，检查步骤...")
            steps = result.get("data", {}).get("steps", {})
            
            for step_key, step_info in steps.items():
                status = step_info.get("status", "unknown")
                message = step_info.get("message", "")
                print(f"    {step_key}: {status} - {message}")
            
            # 测试确认更新（模拟）
            confirm_data = {
                "confirmed_updates": {
                    "project_name": "AI更新测试项目",
                    "current_progress": "实施阶段"
                }
            }
            await self.test_api("AI更新确认", "POST", f"/api/v1/project/{TEST_PROJECT_CODE}/ai-update/confirm", confirm_data)
        
    async def create_test_archive_files(self):
        """创建测试档案文件"""
        try:
            archive_dir = f"project_archive_materials/{TEST_PROJECT_CODE}"
            os.makedirs(archive_dir, exist_ok=True)
            
            # 创建测试markdown文件
            test_content = """# 项目立项文档

## 项目基本信息
- 项目名称：AI更新测试项目
- 项目编号：TEST_001
- 负责人：张三
- 负责部门：技术部
- 开始日期：2024-01-01
- 结束日期：2024-12-31
- 投资金额：1000000

## 项目描述
这是一个用于测试AI更新功能的示例项目。

## 项目进度
当前项目处于实施阶段，已完成需求分析和系统设计。
"""
            
            with open(os.path.join(archive_dir, "项目立项.md"), "w", encoding="utf-8") as f:
                f.write(test_content)
            
            print(f"  📄 创建测试档案文件: {archive_dir}/项目立项.md")
            
        except Exception as e:
            print(f"  ❌ 创建测试档案文件失败: {str(e)}")
    
    async def test_database_connection(self):
        """测试数据库连接"""
        print("\n💾 测试数据库连接")
        print("-" * 40)

        await self.test_api("数据库状态", "GET", "/api/v1/db/status", use_auth=False)
        
    async def test_options_apis(self):
        """测试选项数据API"""
        print("\n⚙️ 测试选项数据功能")
        print("-" * 40)

        await self.test_api("投资主体选项", "GET", "/api/v1/investment-entities", use_auth=False)
        await self.test_api("负责部门选项", "GET", "/api/v1/responsible-departments", use_auth=False)
        await self.test_api("分类选项", "GET", "/api/v1/categories", use_auth=False)

    async def test_team_apis(self):
        """测试团队管理API"""
        print("\n👥 测试团队管理功能")
        print("-" * 40)

        await self.test_api("团队成员", "GET", "/api/v1/team/members", use_auth=False)
        
    async def run_all_tests(self):
        """运行所有测试"""
        await self.setup()
        
        try:
            # 基础功能测试
            await self.test_database_connection()
            await self.test_auth_apis()
            await self.test_options_apis()
            
            # 核心功能测试
            await self.test_dashboard_apis()
            await self.test_project_apis()
            await self.test_archive_apis()
            await self.test_ai_assistant_apis()
            await self.test_team_apis()

            # 高级功能测试
            await self.test_ai_update_function()
            
        except Exception as e:
            print(f"❌ 测试过程中发生异常: {str(e)}")
            traceback.print_exc()
        
        finally:
            await self.cleanup()

async def main():
    """主函数"""
    tester = PMOTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
