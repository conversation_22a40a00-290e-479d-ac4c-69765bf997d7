@echo off

echo ===================================
echo   PMO System Startup Script
echo ===================================
echo.

echo Checking for running processes...

REM Kill any node processes running vite
taskkill /f /im node.exe /fi "WINDOWTITLE eq *vite*" >nul 2>&1
taskkill /f /im node.exe /fi "IMAGENAME eq node.exe" /fi "MEMUSAGE gt 50000" >nul 2>&1

REM Kill any python processes running main.py
taskkill /f /im python.exe /fi "WINDOWTITLE eq *PMO*" >nul 2>&1
taskkill /f /im python.exe /fi "IMAGENAME eq python.exe" /fi "MEMUSAGE gt 50000" >nul 2>&1

echo.
echo Starting PMO system...
echo.

REM Get current directory
set "CURRENT_DIR=%cd%"

REM Start backend
echo Starting backend service...
cd /d "%CURRENT_DIR%\pmo-backend"
start cmd /c "title PMO Backend & python main.py"
echo Backend startup command executed

REM Wait a few seconds to ensure backend has started
echo Waiting for backend service to start...
timeout /t 5 /nobreak >nul

REM Start frontend
echo Starting frontend service...
cd /d "%CURRENT_DIR%\pmo-web"
start cmd /c "title PMO Frontend & npm run dev"
echo Frontend startup command executed

REM Return to current directory
cd /d "%CURRENT_DIR%"

echo.
echo ===================================
echo   PMO system startup complete!
echo   - Backend: http://localhost:8000
echo   - Frontend: http://localhost:3000
echo ===================================

echo.
echo Press any key to exit this window...
pause >nul 