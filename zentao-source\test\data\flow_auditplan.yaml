title: table zt_flow_auditplan
desc: "质量保证计划"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: dateType
    note: "检查时间类型"
    range: 1,2,3
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: config
    note: "周期配置"
    range: 1-10000
    prefix: "这是json格式配置"
    postfix: ""
    loop: 0
    format: ""
  - field: objectID
    note: "检查对象ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: objectType
    note: "对象类型"
    range: activity,output
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: process
    note: "所属过程"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: processType
    note: "过程分类"
    range: project
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: checkDate
    note: "检查时间"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: checkedBy
    note: "检查人"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: realCheckDate
    note: "重新检查日期"
    from: common.date.v1.yaml
    use: dateB
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: result
    note: "检查结果"
    range: 1-10000
    prefix: "这是检查结果"
    postfix: ""
    loop: 0
    format: ""
  - field: program
    note: "所属项目"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: parent
    note: "父流程ID"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedTo
    note: "指派给"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: "状态"
    range: wait,checking,checked,delay
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: subStatus
    note: "子状态"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: "由谁创建"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdDate
    note: "创建日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedBy
    note: "由谁编辑"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedDate
    note: "编辑日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedBy
    note: "由谁指派"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedDate
    note: "指派日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: checkBy
    note: "由谁检查"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
