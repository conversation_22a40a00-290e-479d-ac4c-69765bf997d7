#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化督办管理API
"""

import requests
import json

def test_simple_supervision_api():
    """测试简化督办管理API"""
    base_url = "http://127.0.0.1:8001/api/v1/simple-supervision"
    
    try:
        # 测试获取督办事项列表
        print("🔍 测试获取督办事项列表...")
        response = requests.get(f"{base_url}/items")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            items = data.get('data', [])
            print(f"✅ 成功获取 {len(items)} 条督办事项")
            
            if items:
                first_item = items[0]
                print(f"第一条数据: {first_item['work_theme']}")
                
                # 测试获取进度详情
                print(f"\n🔍 测试获取进度详情...")
                work_theme = first_item['work_theme']
                company_name = "财险"
                
                detail_response = requests.get(f"{base_url}/progress-detail/{work_theme}/{company_name}")
                print(f"进度详情状态码: {detail_response.status_code}")
                
                if detail_response.status_code == 200:
                    detail_data = detail_response.json()
                    print(f"✅ 进度详情获取成功")
                    print(f"详情数据: {json.dumps(detail_data, ensure_ascii=False, indent=2)}")
                else:
                    print(f"❌ 进度详情获取失败: {detail_response.text}")
            
        else:
            print(f"❌ 获取督办事项失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    test_simple_supervision_api()
