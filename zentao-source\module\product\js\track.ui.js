window.getItem = function(info)
{
    const col   = info.col;
    const color = info.item.color ? " style='color:" + info.item.color + "'" : '';
    const title = info.item.title;

    let titleHtml = `<span${color}>${title}</span>`;
    info.item.content = [];
    if(col.indexOf('epic') != -1 || col.indexOf('requirement') != -1 || col.indexOf('story') != -1)
    {
        let storyPriList    = langStoryPriList[info.item.storyType];
        let storyStatusList = langStoryStatusList[info.item.storyType];
        let storyStageList  = langStoryStageList[info.item.storyType];

        if(privs[info.item.storyType]) titleHtml = "<a href='" + $.createLink(info.item.storyType, 'view', `storyID=${info.item.id}`) + "' data-toggle='modal' data-size='lg'" + color + ">" + title + "</a>";
        info.item.title      = {html: `<div class="line-clamp-2"><span class="align-sub pri-${info.item.pri}">${storyPriList[info.item.pri]}</span> ${titleHtml}</div>`}
        info.item.titleAttrs = {'title' : title};

        if(storyStatusList[info.item.status]) info.item.content.push({html: `<div class="status-${info.item.status}">${storyStatusList[info.item.status]}</div>`});
        info.item.content.push({html: `<div style="color:var(--color-gray-600)">${storyStageList[info.item.stage]}</div>`})
    }
    else if(col == 'project' || col == 'execution')
    {
        let delayHtml = '';
        if(info.item.delay > 0) delayHtml = `<span class='label danger-pale delayed nowrap'>${langProjectStatusList['delay']}</span>`;

        titleHtml = `<span${color} class="title">${title}</span>`;
        if(col == 'project'   && privs['project'])   titleHtml = "<a class='title' href='" + $.createLink('project', 'view', `projectID=${info.item.id}`) + "'>" + title + "</a>";
        if(col == 'execution' && privs['execution']) titleHtml = "<a class='title' href='" + $.createLink('execution', 'task', `executionID=${info.item.id}`) + "'>" + title + "</a>";
        info.item.title = [];
        info.item.title.push({html: `<div class="line-clamp-2">${titleHtml}</div>`});
        info.item.title.push({html: `<div>${delayHtml}</div>`, 'class': 'delayBox gap-x-2'});
        info.item.titleAttrs = {'title' : title, 'class': 'card-title flex'};

        info.item.content.push({html: `<div class="status-${info.item.status}">${langProjectStatusList[info.item.status]}</div>`});
        info.item.content.push({component: 'ProgressCircle', props: {percent: info.item.progress, size: 24}});
    }
    else if(col == 'task')
    {
        if(privs['task']) titleHtml = "<a href='" + $.createLink('task', 'view', `taskID=${info.item.id}`) + "' data-toggle='modal' data-size='lg'" + color + ">" + title + "</a>";
        if(info.item.isParent == '1') titleHtml = "<span class='label size-sm'>" + langParent + "</span> " + titleHtml;
        if(info.item.parent > 0 && info.item.isParent == '0') titleHtml = "<span class='label size-sm'>" + langChildren + "</span> " + titleHtml;
        info.item.title = {html: `<div class='line-clamp-2'><span class="align-sub pri-${info.item.pri}">${langTaskPriList[info.item.pri]}</span> ${titleHtml}</div>`}
        info.item.titleAttrs = {'title' : title};

        if(info.item.isParent == '1') info.item.content.push({html: `<span class="label cursor-pointer primary rounded-xl toggle-btn is-collapsed" data-id="${info.item.id}" onclick="toggleChildren(this, ${info.item.id})">${langChildren} <span class="toggle-icon ml-1"></span></span>`});
        info.item.content.push({html: `<div class="status-${info.item.status}" style='white-space:nowrap'>${langTaskStatusList[info.item.status]}</div>`});
        if(info.item.assignedTo) info.item.content.push({html: "<div style='white-space:nowrap'><i class='icon icon-hand-right'></i> " + (users[info.item.assignedTo] ? users[info.item.assignedTo] : info.item.assignedTo) + "</div>"});
        info.item.content.push({component: 'ProgressCircle', props: {percent: info.item.progress, size: 24}});
    }
    else if(col == 'bug')
    {
        if(privs['bug']) titleHtml = "<a href='" + $.createLink('bug', 'view', `bugID=${info.item.id}`) + "' data-toggle='modal' data-size='lg'" + color + ">" + title + "</a>";
        info.item.title      = {html: `<div class="line-clamp-2"><span class="align-sub pri-${info.item.pri}">${langBugPriList[info.item.pri]}</span> ${titleHtml}</div>`}
        info.item.titleAttrs = {'title' : title};

        severity     = info.item.severity;
        severityHtml = `<div class="severity" data-severity="${severity}"></div>`;
        if(!langBugSeverityList[severity] || langBugSeverityList[severity] != severity) severityHtml = `<div class='severity severity-label' data-severity="${severity}">${langBugSeverityList[severity]}</div>`;

        info.item.content.push({html: severityHtml});
        if(info.item.assignedTo) info.item.content.push({html: "<i class='icon icon-hand-right'></i> " + (users[info.item.assignedTo] ? users[info.item.assignedTo] : info.item.assignedTo)});
    }
    else if(col == 'case')
    {
        if(privs['case']) titleHtml = "<a href='" + $.createLink('testcase', 'view', `caseID=${info.item.id}`) + "' data-toggle='modal' data-size='lg'" + color + ">" + title + "</a>";
        info.item.title      = {html: `<div class="line-clamp-2"><span class="align-sub pri-${info.item.pri}">${langCasePriList[info.item.pri]}</span> ${titleHtml}</div>`}
        info.item.titleAttrs = {'title' : title};

        info.item.content.push({html: "<div class='status-" + info.item.lastRunResult + "'>" + (langCaseResultList[info.item.lastRunResult] ? langCaseResultList[info.item.lastRunResult] : langUnexecuted) + "</div>"});
        info.item.content.push({html: (users[info.item.lastRunner] ? users[info.item.lastRunner] : info.item.lastRunner)});
    }
    else if(col == 'design')
    {
        info.item.titleAttrs = {'class': 'line-clamp-3', 'title' : title};
        if(privs['design'])
        {
            info.item.titleUrl = $.createLink('design', 'view', `id=${info.item.id}`);
            info.item.titleAttrs = {'class': 'line-clamp-3', 'title' : title, 'data-toggle': 'modal', 'data-size': 'lg'};
        }
    }
    else if(col == 'commit')
    {
        if(privs['commit']) info.item.titleUrl = $.createLink('repo', 'revision', `repoID=${info.item.repo}&objectID=0&revision=${info.item.revision}`);
        info.item.titleAttrs = {'class': 'line-clamp-3', 'title' : title};
    }
}

window.getLaneCol = function(lane, col)
{
    if(mergeCells[lane.name])
    {
        if(mergeCells[lane.name][col.name]) return {laneColClass: 'lane-col-join-with-above'};
        return {laneColClass: 'lane-col-shrink-with-above'};
    }
}

window.getCol = function(col)
{
    if(col.name == storyType) col.subtitle = {html: `<span class="icon ml-1 cursor-pointer orderByIcon icon-swap" title="${orderByTitle}"></span>`};
}

window.itemRender = function(info)
{
    const col = info.col;
    if(col == 'task')
    {
        if(info.item.parent > '0' && tasks[info.item.parent] !== undefined) info.item.className.push('hidden childTask parent-' + info.item.parent);
        if(info.item.isParent == '1') info.item.className.push('parentTask');
    }

    if(config.rawModule == 'projectstory' && (col.indexOf('epic') != -1 || col.indexOf('requirement') != -1 || col.indexOf('story') != -1))
    {
        if(!storyIdList.includes(parseInt(info.item.id))) info.item.className.push('hidden');
    }
}

window.afterRender = function()
{
    $orderByIcon = $('.orderByIcon');
    if($orderByIcon.length > 0 && !$orderByIcon.hasClass('dropdownInited'))
    {
        new zui.Dropdown($('.orderByIcon'), {menu: {items: orderByItems}});
        $orderByIcon.addClass('dropdownInited');
    }

    $hasSubCols = $('.kanban-header-cols .kanban-header-col.has-subs');
    if($hasSubCols.length > 0)
    {
        $hasSubCols.each(function()
        {
            $subCols = $(this).find(".kanban-header-sub-cols .kanban-header-col.is-sub");
            if($subCols.length == 1)
            {
                style = $subCols.attr('style');
                $subCols.parent().addClass('hidden');
                $(this).removeClass('has-subs').attr('style', style);
            }
        });
    }
}

window.toggleChildren = function(obj, parentID, isExpanded)
{
    if(!isExpanded) isExpanded = false;
    if(isExpanded || $(obj).hasClass('is-expanded'))
    {
        $(obj).removeClass('is-expanded').addClass('is-collapsed');

        let $parent = $('.parent-' + parentID);
        $parent.addClass('hidden');
        if($parent.length == 0) return;

        let $toggleBtn = $parent.find('.toggle-btn');
        if($toggleBtn.length == 0) return;
        $toggleBtn.each(function(){ toggleChildren(this, $(this).data('id'), true); });
    }
    else
    {
        $(obj).removeClass('is-collapsed').addClass('is-expanded');
        $('.parent-' + parentID).removeClass('hidden')
    }
}

window.changeProduct = function(e)
{
    if(config.rawModule == 'projectstory') loadPage($.createLink(config.rawModule, config.rawMethod, "projectID=" + projectID + "&productID=" + $(e.target).val()));
}
