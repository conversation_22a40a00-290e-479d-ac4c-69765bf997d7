#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建company_progress表的脚本
"""

import pymysql
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

# 数据库配置
DB_CONFIG = {
    'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'cyh',
    'password': 'Qq188788',
    'database': 'kanban2',
    'charset': 'utf8mb4'
}

def create_company_progress_table():
    """创建company_progress表"""
    try:
        # 连接数据库
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 检查表是否已存在
        check_table_sql = """
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = 'kanban2' AND table_name = 'company_progress'
        """
        cursor.execute(check_table_sql)
        table_exists = cursor.fetchone()[0] > 0
        
        if table_exists:
            logging.info("✅ company_progress表已存在")
            return True
        
        # 创建表的SQL
        create_table_sql = """
        CREATE TABLE company_progress (
            id INT AUTO_INCREMENT PRIMARY KEY,
            supervision_item_id INT NOT NULL COMMENT '督办事项ID',
            company_id INT NOT NULL COMMENT '公司ID',
            status VARCHAR(50) NOT NULL DEFAULT '未开始' COMMENT '状态',
            progress_description TEXT COMMENT '进展描述',
            completion_date DATE COMMENT '完成日期',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            UNIQUE KEY unique_item_company (supervision_item_id, company_id),
            FOREIGN KEY (supervision_item_id) REFERENCES supervision_items(id) ON DELETE CASCADE,
            FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
            INDEX idx_supervision_item (supervision_item_id),
            INDEX idx_company (company_id),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司进度表'
        """
        
        cursor.execute(create_table_sql)
        connection.commit()
        logging.info("✅ company_progress表创建成功")
        
        # 为现有的督办事项和公司创建默认进度记录
        logging.info("🔄 为现有数据创建默认进度记录...")
        
        # 获取所有督办事项
        cursor.execute("SELECT id FROM supervision_items")
        items = cursor.fetchall()
        
        # 获取所有活跃公司
        cursor.execute("SELECT id FROM companies WHERE is_active = 1")
        companies = cursor.fetchall()
        
        # 为每个督办事项和公司组合创建默认记录
        insert_count = 0
        for item in items:
            for company in companies:
                try:
                    insert_sql = """
                    INSERT IGNORE INTO company_progress (supervision_item_id, company_id, status)
                    VALUES (%s, %s, '未开始')
                    """
                    cursor.execute(insert_sql, (item[0], company[0]))
                    if cursor.rowcount > 0:
                        insert_count += 1
                except Exception as e:
                    logging.warning(f"创建进度记录失败 (item_id={item[0]}, company_id={company[0]}): {e}")
        
        connection.commit()
        logging.info(f"✅ 创建了 {insert_count} 条默认进度记录")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        logging.error(f"❌ 创建company_progress表失败: {e}")
        return False

def main():
    """主函数"""
    logging.info("🚀 开始创建company_progress表")
    logging.info("=" * 50)
    
    success = create_company_progress_table()
    
    logging.info("=" * 50)
    if success:
        logging.info("🎉 company_progress表创建完成")
    else:
        logging.error("❌ company_progress表创建失败")
    
    logging.info("💡 现在可以正常使用公司管理功能了")

if __name__ == "__main__":
    main()
