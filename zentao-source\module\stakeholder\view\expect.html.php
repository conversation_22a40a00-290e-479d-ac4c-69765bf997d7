<?php
/**
 * The complete file of task module of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     task
 * @version     $Id: complete.html.php 935 2010-07-06 07:49:24Z <EMAIL> $
 * @link        https://www.zentao.net
 */
?>
<?php include '../../common/view/header.html.php';?>
<?php include '../../common/view/kindeditor.html.php';?>
<div id='mainContent' class='main-content'>
  <div class='center-block'>
    <div class='main-header'>
      <h2>
        <span class='label label-id'><?php echo $user->id;?></span>
        <?php echo isonlybody() ? ("<span title='$user->name'>" . $user->name . '</span>') : html::a($this->createLink('task', 'view', 'task=' . $user->id), $user->name);?>
      </div>
    </div>
    <form method='post' enctype='multipart/form-data' target='hiddenwin'>
      <table class='table table-form'>
        <tr>
          <th><?php echo $lang->stakeholder->expect;?></th>
          <td colspan='2'><?php echo html::textarea('expect', '', "rows='6' class='w-p98'");?></td>
        </tr>
        <tr>
          <th><?php echo $lang->stakeholder->progress;?></th>
          <td colspan='2'><?php echo html::textarea('progress', '', "rows='6' class='w-p98'");?></td>
        </tr>
        <tr>
          <td colspan='3' class='text-center form-actions'>
            <?php echo html::submitButton();?>
          </td>
        </tr>
      </table>
    </form>
  </div>
</div>
<?php include '../../common/view/footer.html.php';?>
