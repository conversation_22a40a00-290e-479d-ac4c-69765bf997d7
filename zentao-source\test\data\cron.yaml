title: table zt_cron
desc: "定时任务"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: m
    note: "分"
    range: "*,0-59:R,*/5"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: h
    note: "小时"
    range: "*,0-23:R,*/5"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: dom
    note: "天"
    range: "*"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: mon
    note: "月"
    range: "*"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: dow
    note: "周"
    range: "*"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: command
    note: "命令"
    range: 1-10000
    prefix: "这是执行的命令"
    postfix: ""
    loop: 0
    format: ""
  - field: remark
    note: "备注"
    range: 1-10000
    prefix: "这是备注"
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "任务类型"
    range: zentao,system
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: buildin
    note: "内置"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: "状态"
    range: normal,stop
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: lastTime
    range: "(-1M)-(+1w):-1D"
    type: timestamp
    format: "YYYY-MM-DD hh:mm:ss"
