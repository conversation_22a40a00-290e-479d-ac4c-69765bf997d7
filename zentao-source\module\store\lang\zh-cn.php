<?php
$lang->store->appType          = '组件类别';
$lang->store->appVersion       = '版本';
$lang->store->releaseDate      = '发布日期';
$lang->store->author           = '发布者';
$lang->store->empty            = '暂无组件';
$lang->store->install          = '安装';
$lang->store->customInstall    = '自定义安装';
$lang->store->searchApp        = '请输入组件名称';
$lang->store->testChannel      = '测试版';
$lang->store->stableChannel    = '稳定版';
$lang->store->appBasicInfo     = '基本信息';
$lang->store->screenshots      = '组件截图';
$lang->store->appDynamic       = '组件动态';
$lang->store->noDynamicArticle = '暂无动态';
$lang->store->noScreenshot     = '暂无截图';
$lang->store->cloudStore       = '组件市场';
$lang->store->browse           = '组件市场';
$lang->store->appView          = '组件详情';

$lang->store->support          = '支持';
$lang->store->gitUrl           = '组件源码';
$lang->store->dockerfileUrl    = 'Dockerfile';
$lang->store->forumUrl         = '论坛';
$lang->store->alreadyInstalled = '平台已安装过该组件，确定要继续安装么？';

$lang->store->featureBar['browse']['id']          = '默认';
$lang->store->featureBar['browse']['create_time'] = '最新上架';
$lang->store->featureBar['browse']['update_time'] = '最近更新';
