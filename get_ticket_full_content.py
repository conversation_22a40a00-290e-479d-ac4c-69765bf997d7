#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取工单完整内容脚本 - 修复版
只读模式 - 获取工单的完整内容包括子公司需求信息
"""

import pymysql
import json
from datetime import datetime

class TicketFullContentGetter:
    """工单完整内容获取器 - 只读模式"""
    
    def __init__(self):
        self.connection = None
        self.config = {
            'host': '**********',
            'user': 'qyuser', 
            'password': 'C~w9d4kaWS',
            'database': 'ticket',
            'charset': 'utf8mb4',
            'autocommit': True
        }
    
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = pymysql.connect(**self.config)
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False
    
    def execute_query(self, sql, params=None):
        """执行查询 - 只读"""
        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            print(f"查询执行失败: {e}")
            return []
    
    def get_ticket_detail_content(self, ticket_id):
        """获取工单详细内容"""
        print(f"\n🎫 获取工单 {ticket_id} 的详细内容...")
        
        # 1. 获取工单基本信息
        sql = """
        SELECT 
            feelec_ticket_id,
            feelec_ticket_no,
            feelec_title,
            feelec_template_id,
            feelec_template_type_id,
            feelec_publisher_id,
            feelec_source
        FROM feelec_ticket 
        WHERE feelec_ticket_id = %s AND feelec_delete = 20
        """
        
        ticket_info = self.execute_query(sql, (ticket_id,))
        if not ticket_info:
            print(f"   ❌ 未找到工单 {ticket_id}")
            return None
        
        ticket = ticket_info[0]
        print(f"   📋 工单基本信息:")
        print(f"      编号: {ticket['feelec_ticket_no']}")
        print(f"      标题: {ticket['feelec_title']}")
        print(f"      模板ID: {ticket['feelec_template_id']}")
        print(f"      发布人ID: {ticket['feelec_publisher_id']}")
        
        # 2. 获取工单详细内容 - 这是关键！
        detail_sql = """
        SELECT * FROM feelec_ticket_detail 
        WHERE feelec_ticket_id = %s
        """
        
        detail_data = self.execute_query(detail_sql, (ticket_id,))
        
        if detail_data:
            print(f"   📄 工单详细内容 ({len(detail_data)} 条记录):")
            for i, detail in enumerate(detail_data, 1):
                print(f"      记录 {i}:")
                for key, value in detail.items():
                    if isinstance(value, str) and len(value) > 100:
                        display_value = value[:100] + "..."
                    else:
                        display_value = value
                    print(f"        {key}: {display_value}")
                print()
        
        # 3. 获取模板表单字段（修复字段名）
        template_forms = []
        if ticket['feelec_template_id']:
            template_sql = """
            SELECT 
                feelec_form_id,
                form_identify,
                feelec_form_type,
                feelec_name,
                feelec_default_value,
                feelec_enable,
                feelec_require,
                sort
            FROM feelec_ticket_template_form 
            WHERE feelec_template_id = %s 
            AND delete_time = 0
            ORDER BY sort ASC
            """
            
            template_forms = self.execute_query(template_sql, (ticket['feelec_template_id'],))
            
            if template_forms:
                print(f"   📋 模板表单字段 ({len(template_forms)} 个):")
                for form in template_forms:
                    print(f"      • {form['feelec_name']} ({form['form_identify']}) - {form['feelec_form_type']}")
        
        # 4. 获取发布人信息（修复字段名）
        publisher_info = None
        if ticket['feelec_publisher_id']:
            # 先检查用户表结构
            user_structure_sql = "DESCRIBE feelec_user"
            user_fields = self.execute_query(user_structure_sql)
            
            if user_fields:
                print(f"   🔍 用户表字段:")
                available_fields = [field['Field'] for field in user_fields]
                print(f"      可用字段: {', '.join(available_fields[:10])}...")
                
                # 构建动态查询
                basic_fields = ['feelec_user_id']
                if 'feelec_name' in available_fields:
                    basic_fields.append('feelec_name')
                if 'feelec_real_name' in available_fields:
                    basic_fields.append('feelec_real_name')
                if 'feelec_mobile' in available_fields:
                    basic_fields.append('feelec_mobile')
                if 'feelec_email' in available_fields:
                    basic_fields.append('feelec_email')
                if 'feelec_company_id' in available_fields:
                    basic_fields.append('feelec_company_id')
                
                user_sql = f"""
                SELECT {', '.join(basic_fields)}
                FROM feelec_user 
                WHERE feelec_user_id = %s
                """
                
                publisher_info = self.execute_query(user_sql, (ticket['feelec_publisher_id'],))
                
                if publisher_info:
                    user = publisher_info[0]
                    print(f"   👤 发布人信息:")
                    for key, value in user.items():
                        print(f"      {key}: {value}")
        
        # 5. 获取处理记录（检查实际存在的表）
        process_records = []
        
        # 检查工作流记录表
        workflow_tables = ['feelec_workflow_process_record', 'feelec_process_record', 'feelec_ticket_process']
        
        for table in workflow_tables:
            try:
                check_sql = f"SHOW TABLES LIKE '{table}'"
                table_exists = self.execute_query(check_sql)
                
                if table_exists:
                    print(f"   📋 找到处理记录表: {table}")
                    
                    # 获取表结构
                    structure_sql = f"DESCRIBE {table}"
                    table_fields = self.execute_query(structure_sql)
                    
                    if table_fields:
                        available_fields = [field['Field'] for field in table_fields]
                        
                        # 查找工单ID字段
                        ticket_id_field = None
                        for field in ['feelec_ticket_id', 'ticket_id', 'feelec_id']:
                            if field in available_fields:
                                ticket_id_field = field
                                break
                        
                        if ticket_id_field:
                            record_sql = f"SELECT * FROM {table} WHERE {ticket_id_field} = %s LIMIT 5"
                            records = self.execute_query(record_sql, (ticket_id,))
                            
                            if records:
                                print(f"      ✅ 找到 {len(records)} 条处理记录")
                                process_records.extend(records)
                                break
            except:
                continue
        
        return {
            'ticket_info': ticket,
            'detail_data': detail_data,
            'template_forms': template_forms,
            'publisher_info': publisher_info,
            'process_records': process_records
        }
    
    def test_specific_tickets(self):
        """测试特定工单的内容获取"""
        print("\n🎯 测试特定工单的完整内容获取...")
        
        # 获取有详细数据的工单
        sql = """
        SELECT DISTINCT t.feelec_ticket_id, t.feelec_ticket_no, t.feelec_title
        FROM feelec_ticket t
        JOIN feelec_ticket_detail td ON t.feelec_ticket_id = td.feelec_ticket_id
        WHERE t.feelec_delete = 20 
        ORDER BY t.feelec_ticket_id DESC
        LIMIT 3
        """
        
        tickets = self.execute_query(sql)
        
        if not tickets:
            print("   ❌ 未找到有详细数据的工单")
            return
        
        print(f"   📋 找到 {len(tickets)} 个有详细数据的工单:")
        
        results = []
        for ticket in tickets:
            print(f"\n{'='*80}")
            print(f"测试工单: {ticket['feelec_ticket_no']} - {ticket['feelec_title']}")
            print(f"{'='*80}")
            
            content = self.get_ticket_detail_content(ticket['feelec_ticket_id'])
            if content:
                results.append({
                    'ticket_id': ticket['feelec_ticket_id'],
                    'ticket_no': ticket['feelec_ticket_no'],
                    'content': content
                })
        
        return results
    
    def close(self):
        """关闭连接"""
        if self.connection:
            self.connection.close()

def main():
    """主函数"""
    print("🎫 工单完整内容获取测试 - 修复版")
    print("=" * 80)
    print("⚠️  只读模式 - 不会修改任何数据")
    print("=" * 80)
    
    getter = TicketFullContentGetter()
    
    try:
        # 连接数据库
        if not getter.connect():
            print("❌ 数据库连接失败")
            return
        
        print("✅ 数据库连接成功")
        
        # 测试特定工单的内容获取
        results = getter.test_specific_tickets()
        
        if results:
            print(f"\n🎉 成功获取 {len(results)} 个工单的完整内容！")
            print("\n📊 内容获取总结:")
            for result in results:
                content = result['content']
                print(f"\n工单 {result['ticket_no']}:")
                print(f"  • 详细数据: {len(content.get('detail_data', []))} 条记录")
                print(f"  • 模板字段: {len(content.get('template_forms', []))} 个")
                print(f"  • 发布人信息: {'✅' if content.get('publisher_info') else '❌'}")
                print(f"  • 处理记录: {len(content.get('process_records', []))} 条")
        
        print("\n🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    finally:
        getter.close()

if __name__ == "__main__":
    main()
