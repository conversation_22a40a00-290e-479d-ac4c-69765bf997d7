#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门修复状态数据
"""

import pymysql
import os

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host=os.environ.get('DB_HOST', 'localhost'),
        port=int(os.environ.get('DB_PORT', '3306')),
        user=os.environ.get('DB_USER', 'root'),
        password=os.environ.get('DB_PASSWORD', ''),
        database=os.environ.get('DB_NAME', 'kanban2'),
        charset='utf8mb4'
    )

def fix_status_data():
    """修复状态数据"""
    print("🔧 开始修复状态数据...")
    
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # 1. 检查现有数据
        cursor.execute("SELECT COUNT(*) FROM supervision_items")
        items_count = cursor.fetchone()[0]
        print(f"督办事项数量: {items_count}")
        
        cursor.execute("SELECT COUNT(*) FROM companies WHERE is_active = TRUE")
        companies_count = cursor.fetchone()[0]
        print(f"活跃公司数量: {companies_count}")
        
        cursor.execute("SELECT COUNT(*) FROM company_supervision_status")
        status_count = cursor.fetchone()[0]
        print(f"现有状态记录数量: {status_count}")
        
        # 2. 如果没有状态数据，创建默认状态
        if status_count == 0:
            print("📊 创建默认状态数据...")
            
            # 获取所有督办事项
            cursor.execute("SELECT id, sequence_number FROM supervision_items ORDER BY sequence_number")
            items = cursor.fetchall()
            
            # 获取所有公司
            cursor.execute("SELECT id, company_code FROM companies WHERE is_active = TRUE ORDER BY display_order")
            companies = cursor.fetchall()
            
            # 为每个督办事项和每家公司创建默认状态
            insert_count = 0
            for item in items:
                item_id = item[0]
                sequence_number = item[1]
                
                for company in companies:
                    company_id = company[0]
                    company_code = company[1]
                    
                    # 根据序号设置不同的默认状态进行测试
                    if sequence_number <= 5:
                        default_status = '√'  # 前5个设为已完成
                    elif sequence_number <= 10:
                        default_status = 'O'  # 6-10设为进行中
                    elif sequence_number <= 15:
                        default_status = 'X'  # 11-15设为未启动
                    else:
                        default_status = '—'  # 其他设为不需要执行
                    
                    cursor.execute("""
                        INSERT INTO company_supervision_status 
                        (supervision_item_id, company_id, status, updated_by, created_at, updated_at)
                        VALUES (%s, %s, %s, 'system', NOW(), NOW())
                    """, (item_id, company_id, default_status))
                    
                    insert_count += 1
            
            print(f"✅ 插入了 {insert_count} 条默认状态记录")
            connection.commit()
        else:
            print("✅ 状态数据已存在，无需创建")
        
        # 3. 验证数据
        cursor.execute("""
            SELECT css.supervision_item_id, c.company_code, css.status
            FROM company_supervision_status css
            JOIN companies c ON css.company_id = c.id
            WHERE c.is_active = TRUE
            LIMIT 5
        """)
        sample_data = cursor.fetchall()
        
        print("📋 状态数据样例:")
        for row in sample_data:
            print(f"  事项ID: {row[0]}, 公司: {row[1]}, 状态: {row[2]}")
        
        connection.close()
        print("\n🎉 状态数据修复完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 状态数据修复工具")
    print("=" * 50)
    
    success = fix_status_data()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 状态数据修复成功！")
        print("现在可以正常导出Excel了")
    else:
        print("❌ 状态数据修复失败！")
    
    print("🏁 修复完成")

if __name__ == "__main__":
    main()
