.actions-menu {bottom: 12px;}

.flex-col {flex-direction: column;}
.chart > div {margin: auto;}
.pie-chart {position: relative;}
.pie-chart .pie-chart-title {position: relative; top: -60%; left: 2%; height: 1rem;}
.m-project-view #mainContent {display: flex; margin-bottom: 60px;}
.m-project-view #mainContent .main {flex: 1 0 65%; overflow-x: hidden;}
.m-project-view #mainContent .side {flex: 1 1 35%; overflow: hidden;}
#dynamicBlock {display: flex; flex-direction: column;}
#dynamicBlock .panel-body {flex: 1 1 auto; overflow-y: auto;}
#historyBlock {overflow-y: auto;}

.member-list > .center-y {width: 12.5%}
.maxh-80{max-height: 20rem;}

#mainContent .desc-box {max-height: 6rem;}
