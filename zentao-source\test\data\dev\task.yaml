title: table zt_task
desc: "任务"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-500000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: parent
    note: "父任务"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: project
    note: "所属项目"
    range: 1001-20000{20},1001-13000{10}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: execution
    note: "所属执行"
    range: 20001-50000{10},20001-40000{10}
  - field: module
    note: "所属模块"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: story
    note: "相关需求"
    range: 1-50000{10}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: design
    note: "相关设计"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: storyVersion
    note: "需求版本"
    range: 1
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: designVersion
    note: "设计版本"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: fromBug
    note: "来源Bug"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: name
    note: "任务名称"
    fields:
    - field: name1
      range: "开发任务"
    - field: name2
      range: 1-1000000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "任务类型"
    range: design,devel,test,study,discuss,ui,affair,misc
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: pri
    note: "优先级"
    range: 1-4
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: estimate
    note: "最初预计"
    range: 0-10
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: consumed
    note: "总计消耗"
    range: 3-12
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: left
    note: "预计剩余"
    range: 0-10
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deadline
    note: "截止日期"
    range: "(+1w)-(-1M):-1D"
    prefix: ""
    postfix: ""
    loop: 0
    type: timestamp
    format: "YY/MM/DD"
  - field: status
    note: "任务状态"
    range: wait,doing,done,pause,cancel,closed
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: subStatus
    note: "子状态"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: color
    note: "标题颜色"
    from: common.color.v1.yaml
    use: color
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: mailto
    note: "抄送给"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: desc
    note: "任务描述"
    range: 1-10000
    prefix: "这里是任务描述"
    postfix: ""
    loop: 0
    format: ""
  - field: version
    note: "版本号"
    range: 1
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: openedBy
    note: "由谁创建"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: openedDate
    note: "创建日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedTo
    note: "指派给"
    range: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedDate
    note: "指派日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: estStarted
    note: "预计开始"
    range: "2022-01-27"
    prefix: ""
    postfix: ""
    loop: 0
    type: timestamp
    format: "YY/MM/DD"
  - field: realStarted
    note: "实际开始"
    from: common.date.v1.yaml
    use: dateB
    prefix: ""
    postfix: ""
    loop: 0
    format: "YY/MM/DD"
    type: timestamp
  - field: finishedBy
    note: "由谁完成"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: finishedDate
    note: "完成日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: "YY/MM/DD"
    type: timestamp
  - field: finishedList
    note: "完成者列表"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: canceledBy
    note: "由谁取消"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: canceledDate
    note: "取消日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: "YY/MM/DD"
    type: timestamp
  - field: closedBy
    note: "由谁关闭"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: closedDate
    note: "关闭日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: "YY/MM/DD"
    type: timestamp
  - field: realDuration
    note: "实际持续时长"
    range: 1
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: planDuration
    note: "计划持续时长"
    range: 1
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: closedReason
    note: "关闭原因"
    range: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: lastEditedBy
    note: "最后修改者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: lastEditedDate
    note: "最后修改时间"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: "YY/MM/DD"
    type: timestamp
  - field: activatedDate
    note: "重新激活日期"
    from: common.date.v1.yaml
    use: dateB
    prefix: ""
    postfix: ""
    loop: 0
    format: "YY/MM/DD"
    type: timestamp
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
