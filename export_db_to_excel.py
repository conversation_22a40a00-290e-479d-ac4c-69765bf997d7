#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接从数据库导出督办数据到Excel
"""

import pymysql
import pandas as pd
import os
from datetime import datetime

def export_supervision_data():
    """导出督办数据"""
    print("📊 从数据库导出督办数据...")
    
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            database='kanban2',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 1. 获取督办事项数据
        print("📋 获取督办事项...")
        cursor.execute("""
            SELECT sequence_number, work_dimension, work_theme, supervision_source,
                   work_content, is_annual_assessment, completion_deadline, overall_progress
            FROM supervision_items 
            ORDER BY sequence_number
        """)
        items = cursor.fetchall()
        print(f"获取到 {len(items)} 个督办事项")
        
        # 2. 获取公司列表
        print("🏢 获取公司列表...")
        cursor.execute("""
            SELECT company_code, company_name 
            FROM companies 
            WHERE is_active = TRUE 
            ORDER BY display_order
        """)
        companies = cursor.fetchall()
        print(f"获取到 {len(companies)} 家公司")
        
        # 3. 获取状态数据
        print("📈 获取状态数据...")
        cursor.execute("""
            SELECT si.sequence_number, c.company_name, css.status
            FROM company_supervision_status css
            JOIN supervision_items si ON css.supervision_item_id = si.id
            JOIN companies c ON css.company_id = c.id
            WHERE c.is_active = TRUE
            ORDER BY si.sequence_number, c.display_order
        """)
        statuses = cursor.fetchall()
        print(f"获取到 {len(statuses)} 条状态记录")
        
        # 4. 构建Excel数据
        print("📝 构建Excel数据...")
        
        # 创建基础数据
        excel_data = []
        for item in items:
            row = {
                '序号': item['sequence_number'],
                '工作维度': item['work_dimension'],
                '工作主题': item['work_theme'],
                '督办来源': item['supervision_source'],
                '工作内容和完成标志': item['work_content'],
                '是否年度绩效考核指标': item['is_annual_assessment'],
                '完成时限': str(item['completion_deadline']) if item['completion_deadline'] else '',
                '整体进度': item['overall_progress']
            }
            
            # 初始化所有公司状态为'X'
            for company in companies:
                row[company['company_name']] = 'X'
            
            excel_data.append(row)
        
        # 填入实际状态数据
        for status in statuses:
            seq_num = status['sequence_number']
            company_name = status['company_name']
            status_value = status['status']
            
            # 找到对应的行并更新状态
            for row in excel_data:
                if row['序号'] == seq_num:
                    row[company_name] = status_value
                    break
        
        # 5. 创建DataFrame并导出
        print("📄 创建Excel文件...")
        df = pd.DataFrame(excel_data)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"督办管理表_{timestamp}.xlsx"
        
        # 导出到Excel
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='督办管理', index=False)
            
            # 设置列宽
            worksheet = writer.sheets['督办管理']
            worksheet.column_dimensions['A'].width = 8   # 序号
            worksheet.column_dimensions['B'].width = 20  # 工作维度
            worksheet.column_dimensions['C'].width = 35  # 工作主题
            worksheet.column_dimensions['D'].width = 15  # 督办来源
            worksheet.column_dimensions['E'].width = 50  # 工作内容
            worksheet.column_dimensions['F'].width = 18  # 是否考核指标
            worksheet.column_dimensions['G'].width = 12  # 完成时限
            worksheet.column_dimensions['H'].width = 12  # 整体进度
            
            # 设置公司列宽
            start_col = ord('I')
            for i, company in enumerate(companies):
                col_letter = chr(start_col + i)
                if ord(col_letter) <= ord('Z'):
                    worksheet.column_dimensions[col_letter].width = 8
        
        connection.close()
        
        print(f"✅ 导出成功: {filename}")
        print(f"文件大小: {os.path.getsize(filename)} 字节")
        print(f"数据行数: {len(df)}")
        print(f"数据列数: {len(df.columns)}")
        
        return filename
        
    except Exception as e:
        print(f"❌ 导出失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("🚀 数据库督办数据导出工具")
    print("=" * 50)
    
    filename = export_supervision_data()
    
    print("\n" + "=" * 50)
    if filename:
        print("🎉 导出成功！")
        print(f"📄 文件名: {filename}")
        print("\n📋 导出内容:")
        print("   • 29个督办事项的完整信息")
        print("   • 14家公司的状态数据")
        print("   • 中文表头，便于编辑")
        print("   • 优化的列宽设置")
        
        print("\n📝 下一步操作:")
        print("   1. 打开Excel文件进行编辑")
        print("   2. 修改需要更新的数据")
        print("   3. 保存文件")
        print("   4. 使用导入功能更新数据库")
    else:
        print("❌ 导出失败！")
    
    print("\n🏁 导出完成")

if __name__ == "__main__":
    main()
