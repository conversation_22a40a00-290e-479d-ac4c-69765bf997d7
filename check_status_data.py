#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查状态数据
"""

from app.db.utils import execute_query

def check_status_data():
    """检查状态数据"""
    print("🔍 检查状态数据...")
    
    try:
        # 检查督办事项表
        items_query = "SELECT COUNT(*) as count FROM supervision_items"
        items_result = execute_query(items_query)
        print(f"督办事项数量: {items_result[0]['count']}")
        
        # 检查公司表
        companies_query = "SELECT COUNT(*) as count FROM companies WHERE is_active = TRUE"
        companies_result = execute_query(companies_query)
        print(f"活跃公司数量: {companies_result[0]['count']}")
        
        # 检查公司状态表
        status_query = "SELECT COUNT(*) as count FROM company_supervision_status"
        status_result = execute_query(status_query)
        print(f"公司状态记录数量: {status_result[0]['count']}")
        
        # 检查具体的状态数据
        if status_result[0]['count'] > 0:
            sample_query = """
            SELECT css.supervision_item_id, c.company_code, css.status
            FROM company_supervision_status css
            JOIN companies c ON css.company_id = c.id
            WHERE c.is_active = TRUE
            LIMIT 5
            """
            sample_result = execute_query(sample_query)
            print("状态数据样例:")
            for row in sample_result:
                print(f"  事项ID: {row['supervision_item_id']}, 公司: {row['company_code']}, 状态: {row['status']}")
        else:
            print("❌ 没有状态数据！")
            
            # 检查是否有公司数据
            companies_detail_query = "SELECT id, company_code, company_name, is_active FROM companies LIMIT 5"
            companies_detail = execute_query(companies_detail_query)
            print("公司数据样例:")
            for row in companies_detail:
                print(f"  ID: {row['id']}, 代码: {row['company_code']}, 名称: {row['company_name']}, 活跃: {row['is_active']}")
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_status_data()
