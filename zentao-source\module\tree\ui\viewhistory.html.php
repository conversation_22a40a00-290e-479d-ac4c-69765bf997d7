<?php
declare(strict_types=1);
/**
 * The view history view file of tree module of ZenTaoPMS.
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.zentao.net)
 * @license     ZPL(https://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> Guangming<<EMAIL>>
 * @package     tree
 * @link        https://www.zentao.net
 */
namespace zin;
history(set::commentBtn(false), set::objectType('module'), set::objectID($productID));
h::css('.history-panel {box-shadow: none}');
