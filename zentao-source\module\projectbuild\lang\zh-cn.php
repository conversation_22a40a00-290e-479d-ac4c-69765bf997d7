<?php
/**
 * The project build module zh-cn file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     projectrelease
 * @version     $Id: zh-cn.php 4129 2020-11-27 01:58:14Z wwccss $
 * @link        https://www.zentao.net
 */
$lang->projectbuild->common           = $lang->projectCommon . '构建';
$lang->projectbuild->browse           = '构建列表';
$lang->projectbuild->create           = "创建构建";
$lang->projectbuild->edit             = "编辑构建";
$lang->projectbuild->delete           = "删除构建";
$lang->projectbuild->view             = "构建详情";
$lang->projectbuild->linkStory        = "关联{$lang->SRCommon}";
$lang->projectbuild->linkBug          = "关联Bug";
$lang->projectbuild->unlinkStory      = "移除{$lang->SRCommon}";
$lang->projectbuild->unlinkBug        = "移除Bug";
$lang->projectbuild->batchUnlink      = '批量移除';
$lang->projectbuild->batchUnlinkStory = "批量移除{$lang->SRCommon}";
$lang->projectbuild->batchUnlinkBug   = '批量移除Bug';
$lang->projectbuild->name             = '名称编号';
$lang->projectbuild->execution        = $lang->executionCommon;
$lang->projectbuild->product          = '所属' . $lang->productCommon;
$lang->projectbuild->scmPath          = '源代码地址';
$lang->projectbuild->filePath         = '下载地址';
$lang->projectbuild->desc             = '描述';
$lang->projectbuild->systemName       = '所属' . $lang->product->system;
$lang->projectbuild->system           = '所属' . $lang->product->system;
