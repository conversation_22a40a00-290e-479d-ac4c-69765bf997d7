import{r as t,h as s,F as e}from"./p-7900c24a.js";const c=class{constructor(s){t(this,s),this.editor=void 0,this.forceUpdateCounter=void 0,this.toggleFullscreen=null,this.showCharCount=!0}render(){const t=this.editor.storage.characterCount.characters();return s(e,{key:"97b6f094e36b1fa0fe77a760befc28f04a3af8b1"},this.showCharCount?s("span",null,t," character",t>1?"s":"","."):s("div",null),this.toggleFullscreen&&s("button",{key:"c9f1c5f1027373f157c06f7b93378c2082005b88",id:"fullscreen-button",onClick:this.toggleFullscreen},"Toggle Fullscreen"))}};export{c as zen_editor_footer}