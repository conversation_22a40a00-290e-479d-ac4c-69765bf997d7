title: zt_getpairs
author: shenshulong
version: '1.0'
fields:
    - field: id
      range: 11-20
    - field: project
      range: 11-15
    - field: name
      range: 11-20
      prefix: '项目'
    - field: code
      range: 11-20
      prefix: 'project'
    - field: model
      range: 'scrum,waterfallplus'
    - field: parent
      range: 0-3
    - field: path
      range: 11-20
      prefix: '1,'
    - field: milestone
      range: 0-1
    - field: auth
      range: '[]'
    - field: type
      range: 'stage,project'
    - field: grade
      range: 1
    - field: path
      range: 11-20
      prefix: ',1,'
      postfix: ','
    - field: days
      range: 1
    - field: status
      range: 'wait'
    - field: desc
      range: 11-20
      prefix: '项目描述'
    - field: budget
      range: '100000,200000'
    - field: budgetUnit
      range: 'CNY'
    - field: percent
      range: '0-0'
