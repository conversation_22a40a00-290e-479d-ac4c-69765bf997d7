#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的公司管理功能测试脚本
测试创建、更新、删除、数据库持久化等所有功能
"""

import requests
import json
import logging
import random
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

BASE_URL = 'http://localhost:8000/api/v1'

def test_complete_workflow():
    """测试完整的公司管理工作流程"""
    try:
        logging.info("🚀 开始完整的公司管理功能测试")
        logging.info("=" * 60)
        
        # 1. 获取初始公司列表
        logging.info("步骤1: 获取初始公司列表")
        response = requests.get(f'{BASE_URL}/new-supervision/companies')
        if response.status_code != 200:
            logging.error("❌ 获取公司列表失败")
            return False
        
        initial_data = response.json()
        initial_companies = initial_data.get('data', [])
        initial_count = len(initial_companies)
        logging.info(f"✅ 初始公司数量: {initial_count}")
        
        # 2. 创建新公司
        logging.info("步骤2: 创建新公司")
        random_suffix = random.randint(10000, 99999)
        new_company = {
            "company_code": f"TEST_{random_suffix}",
            "company_name": f"测试公司_{random_suffix}",
            "display_order": 888
        }
        
        response = requests.post(f'{BASE_URL}/supervision/companies', json=new_company)
        if response.status_code != 200:
            logging.error("❌ 创建公司失败")
            return False
        
        create_data = response.json()
        if not create_data.get('success'):
            logging.error(f"❌ 创建公司失败: {create_data.get('message')}")
            return False
        
        company_id = create_data.get('data', {}).get('id')
        logging.info(f"✅ 创建公司成功，ID: {company_id}")
        
        # 3. 验证公司已创建
        logging.info("步骤3: 验证公司已创建")
        response = requests.get(f'{BASE_URL}/new-supervision/companies')
        after_create_data = response.json()
        after_create_companies = after_create_data.get('data', [])
        after_create_count = len(after_create_companies)
        
        if after_create_count != initial_count + 1:
            logging.error(f"❌ 公司数量不正确，期望: {initial_count + 1}，实际: {after_create_count}")
            return False
        
        created_company = next((c for c in after_create_companies if c['id'] == company_id), None)
        if not created_company:
            logging.error("❌ 新创建的公司未找到")
            return False
        
        logging.info(f"✅ 验证成功：公司 {created_company['company_name']} 已创建")
        
        # 4. 更新公司信息
        logging.info("步骤4: 更新公司信息")
        update_data = {
            "company_name": f"更新后的测试公司_{random_suffix}",
            "display_order": 777
        }
        
        response = requests.put(f'{BASE_URL}/supervision/companies/{company_id}', json=update_data)
        if response.status_code != 200:
            logging.error("❌ 更新公司失败")
            return False
        
        update_response = response.json()
        if not update_response.get('success'):
            logging.error(f"❌ 更新公司失败: {update_response.get('message')}")
            return False
        
        logging.info("✅ 更新公司成功")
        
        # 5. 验证公司已更新
        logging.info("步骤5: 验证公司已更新")
        response = requests.get(f'{BASE_URL}/new-supervision/companies')
        after_update_data = response.json()
        after_update_companies = after_update_data.get('data', [])
        
        updated_company = next((c for c in after_update_companies if c['id'] == company_id), None)
        if not updated_company:
            logging.error("❌ 更新后的公司未找到")
            return False
        
        if updated_company['company_name'] != update_data['company_name']:
            logging.error(f"❌ 公司名称未更新，期望: {update_data['company_name']}，实际: {updated_company['company_name']}")
            return False
        
        if updated_company['display_order'] != update_data['display_order']:
            logging.error(f"❌ 显示顺序未更新，期望: {update_data['display_order']}，实际: {updated_company['display_order']}")
            return False
        
        logging.info(f"✅ 验证成功：公司信息已更新为 {updated_company['company_name']}")
        
        # 6. 删除公司
        logging.info("步骤6: 删除公司")
        response = requests.delete(f'{BASE_URL}/supervision/companies/{company_id}')
        if response.status_code != 200:
            logging.error("❌ 删除公司失败")
            return False
        
        delete_response = response.json()
        if not delete_response.get('success'):
            logging.error(f"❌ 删除公司失败: {delete_response.get('message')}")
            return False
        
        logging.info("✅ 删除公司成功")
        
        # 7. 验证公司已删除
        logging.info("步骤7: 验证公司已删除")
        response = requests.get(f'{BASE_URL}/new-supervision/companies')
        after_delete_data = response.json()
        after_delete_companies = after_delete_data.get('data', [])
        after_delete_count = len(after_delete_companies)
        
        if after_delete_count != initial_count:
            logging.error(f"❌ 删除后公司数量不正确，期望: {initial_count}，实际: {after_delete_count}")
            return False
        
        deleted_company = next((c for c in after_delete_companies if c['id'] == company_id), None)
        if deleted_company:
            logging.error("❌ 公司未被删除")
            return False
        
        logging.info("✅ 验证成功：公司已从数据库中删除")
        
        logging.info("=" * 60)
        logging.info("🎉 所有公司管理功能测试通过！")
        logging.info("=" * 60)
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 测试过程中发生异常: {e}")
        return False

def main():
    """主函数"""
    success = test_complete_workflow()
    
    if success:
        logging.info("✅ 公司管理功能完全正常！")
        logging.info("")
        logging.info("🎯 现在你可以在前端安全地使用以下功能：")
        logging.info("   ✅ 添加新公司 - 数据会保存到数据库")
        logging.info("   ✅ 编辑公司信息 - 修改会持久化到数据库")
        logging.info("   ✅ 删除公司 - 会真正从数据库中删除")
        logging.info("   ✅ 调整公司显示顺序 - 顺序会保存到数据库")
        logging.info("   ✅ 刷新页面后设置依然保持")
        logging.info("")
        logging.info("🌐 前端测试地址: http://localhost:3000/#/new-supervision")
    else:
        logging.error("❌ 公司管理功能存在问题，请检查日志")

if __name__ == "__main__":
    main()
