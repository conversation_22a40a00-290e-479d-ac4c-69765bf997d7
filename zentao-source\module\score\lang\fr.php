<?php
$lang->score->common       = 'Mes Points';
$lang->score->record       = 'Points';
$lang->score->current      = 'Mes Points';
$lang->score->level        = 'Niveau de Points';
$lang->score->reset        = 'R?initialiser';
$lang->score->tips         = 'Points obtenus hier : <strong>%d</strong><br/> Total Points: <strong>%d</strong>';
$lang->score->resetTips    = 'Ca va prendre un moment. <strong>Ne fermez pas la fen?tre.</strong>';
$lang->score->resetStart   = 'D?marrer';
$lang->score->resetLoading = 'Calcul en cours : ';
$lang->score->resetFinish  = 'Termin?';

$lang->score->id      = 'ID';
$lang->score->userID  = 'UserID';
$lang->score->account = 'User';
$lang->score->module  = 'Module';
$lang->score->method  = 'Actions';
$lang->score->before  = 'Avant';
$lang->score->score   = '+/-';
$lang->score->after   = 'Apr?s';
$lang->score->time    = 'Heure';
$lang->score->desc    = 'Description';
$lang->score->noLimit = 'Sans limite';
$lang->score->times   = 'Heures';
$lang->score->hour    = 'Heures';

$lang->score->modules['task']        = 'T?che';
$lang->score->modules['tutorial']    = 'Tutoriel';
$lang->score->modules['user']        = 'User';
$lang->score->modules['ajax']        = 'Autre';
$lang->score->modules['doc']         = 'Document';
$lang->score->modules['todo']        = 'Agenda';
$lang->score->modules['story']       = 'Story';
$lang->score->modules['bug']         = 'Bug';
$lang->score->modules['testcase']    = 'CasTest';
$lang->score->modules['testtask']    = 'Recette';
$lang->score->modules['build']       = 'Build';
$lang->score->modules['execution']   = $lang->executionCommon;
$lang->score->modules['productplan'] = 'Plan';
$lang->score->modules['release']     = 'Release';
$lang->score->modules['block']       = 'Bloc';
$lang->score->modules['search']      = 'Recherche';

$lang->score->methods['task']['create']              = 'Cr?er T?che';
$lang->score->methods['task']['close']               = 'Fermer T?che';
$lang->score->methods['task']['finish']              = 'Terminer T?che';
$lang->score->methods['tutorial']['finish']          = 'Terminer Tutoriel';
$lang->score->methods['user']['login']               = 'Connexion';
$lang->score->methods['user']['changePassword']      = 'Changer Password';
$lang->score->methods['user']['editProfile']         = 'Editer Profil';
$lang->score->methods['ajax']['selectTheme']         = 'Changer Th?me';
$lang->score->methods['ajax']['selectLang']          = 'Changer Langue';
$lang->score->methods['ajax']['showSearchMenu']      = 'Recherche Avanc?e';
$lang->score->methods['ajax']['customMenu']          = 'Menu Personnalis?';
$lang->score->methods['ajax']['dragSelected']        = 'D?placez sur Page de Liste';
$lang->score->methods['ajax']['lastNext']            = 'Raccourci Page Suivante';
$lang->score->methods['ajax']['switchToDataTable']   = 'Switch Data Table';
$lang->score->methods['ajax']['submitPage']          = 'Changer Pagination Nb#';
$lang->score->methods['ajax']['quickJump']           = 'Saut Rapide';
$lang->score->methods['ajax']['batchCreate']         = 'Cr?er par Lot Premier';
$lang->score->methods['ajax']['batchEdit']           = 'Editer par Lot Premier';
$lang->score->methods['ajax']['batchOther']          = 'Autres Actions par Lot';
$lang->score->methods['doc']['create']               = 'Cr?er Document';
$lang->score->methods['todo']['create']              = 'Cr?er Entr?e Agenda';
$lang->score->methods['story']['create']             = 'Cr?er Story';
$lang->score->methods['story']['close']              = 'Fermer Story';
$lang->score->methods['bug']['create']               = 'Signaler un Bug';
$lang->score->methods['bug']['confirm']              = 'Confirmer Bug';
$lang->score->methods['bug']['createFormCase']       = 'Cr?er Formulaire Bug';
$lang->score->methods['bug']['resolve']              = 'R?soudre Bug';
$lang->score->methods['bug']['saveTplModal']         = 'Sauver Mod?le';
$lang->score->methods['testtask']['runCase']         = 'Jouer CasTest';
$lang->score->methods['testcase']['create']          = 'Cr?er CasTest';
$lang->score->methods['build']['create']             = 'Cr?er Build';
$lang->score->methods['execution']['create']         = "Cr?er {$lang->executionCommon}";
$lang->score->methods['execution']['close']          = "Terminer {$lang->executionCommon}";
$lang->score->methods['productplan']['create']       = 'Cr?er Plan';
$lang->score->methods['release']['create']           = 'Cr?er Release';
$lang->score->methods['block']['set']                = 'Personnaliser Bloc';
$lang->score->methods['search']['saveQuery']         = 'Sauver Query';
$lang->score->methods['search']['saveQueryAdvanced'] = 'Recherche Avanc?e';

$lang->score->extended['user']['changePassword'] = "Obtenez ##strength,1## points, si le mot de passe est moyen. Obtenez ##strength,2## points, s'il est fort.";
$lang->score->extended['execution']['close']     = "Apr?s que le projet soit ferm?, le Project Manager obtient ##manager,close## points et les membres de l'?quipe obtiennent ##member,close## points. S'il a ?t? r?alis? dans les temps ou plus vite, le Project Manager obtient ##manager,onTime## points et les membres de l'?quipe obtiennent ##member,onTime## points.";
$lang->score->extended['bug']['resolve']         = 'Apr?s un bug r?solu, obtenez des extra-points en fonction de sa s?v?rit?. S1, + ##severity,3##; S2 + ##severity,2##, S3 + ##severity,1##.';
$lang->score->extended['bug']['confirm']         = 'Apr?s un bug confirm?, obtenez des extra-points en fonction de sa s?v?rit?. S1, + ##severity,3##; S2 + ##severity,2##, S3 + ##severity,1##.';
$lang->score->extended['task']['finish']         = 'Apr?s une t?che accomplie, obtenez des extra-points par arrondi(heures-homme / 10  Estim?s / Co?t) + Points de Priorit? (p1 ##pri,1##, p2 ##pri,2##).';
$lang->score->extended['story']['close']         = "Apr?s la fermeture d'une Story, son cr?ateur recevra extra ##createID## points.";

$lang->score->featureBar['rule']['all'] = 'Score Rules';
