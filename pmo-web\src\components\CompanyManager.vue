<template>
  <div class="company-manager">
    <el-dialog
      v-model="visible"
      title="公司列管理"
      width="600px"
      :before-close="handleClose"
    >
      <div class="manager-content">
        <!-- 添加新公司 -->
        <div class="add-company">
          <h4>添加新公司</h4>
          <el-form :model="newCompany" inline>
            <el-form-item label="公司名称">
              <el-input
                v-model="newCompany.company_name"
                placeholder="请输入公司名称"
                style="width: 200px"
                @keyup.enter="addCompany"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="addCompany" :loading="adding">
                添加
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-divider />

        <!-- 现有公司列表 -->
        <div class="company-list">
          <h4>现有公司列表</h4>
          <el-table :data="companies" style="width: 100%">
            <el-table-column prop="display_order" label="序号" width="80" />
            <el-table-column prop="company_name" label="公司名称" />
            <el-table-column prop="column_name" label="数据库字段" />
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button
                  type="danger"
                  size="small"
                  @click="deleteCompany(row.company_name)"
                  :loading="deleting === row.company_name"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="primary" @click="refreshAndClose">
            刷新页面
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import request from '@/utils/request'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'refresh'])

// 响应式数据
const visible = ref(false)
const companies = ref([])
const newCompany = ref({
  company_name: '',
  display_order: 0
})
const adding = ref(false)
const deleting = ref('')
const loading = ref(false)

// 监听props变化
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    loadCompanies()
  }
})

// 监听visible变化
watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 方法
const loadCompanies = async () => {
  try {
    loading.value = true
    const response = await request({
      url: '/supervision/companies',
      method: 'get'
    })
    
    if (response.success) {
      companies.value = response.data
    } else {
      ElMessage.error('获取公司列表失败')
    }
  } catch (error) {
    console.error('获取公司列表失败:', error)
    ElMessage.error('获取公司列表失败')
  } finally {
    loading.value = false
  }
}

const addCompany = async () => {
  if (!newCompany.value.company_name.trim()) {
    ElMessage.warning('请输入公司名称')
    return
  }

  try {
    adding.value = true
    const response = await request({
      url: '/supervision/companies',
      method: 'post',
      data: newCompany.value
    })
    
    if (response.success) {
      ElMessage.success(response.message)
      newCompany.value.company_name = ''
      loadCompanies()
    } else {
      ElMessage.error(response.message || '添加公司失败')
    }
  } catch (error) {
    console.error('添加公司失败:', error)
    ElMessage.error('添加公司失败')
  } finally {
    adding.value = false
  }
}

const deleteCompany = async (companyName) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除公司 "${companyName}" 吗？这将删除数据库中对应的列，操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    deleting.value = companyName
    const response = await request({
      url: `/supervision/companies/${companyName}`,
      method: 'delete'
    })
    
    if (response.success) {
      ElMessage.success(response.message)
      loadCompanies()
    } else {
      ElMessage.error(response.message || '删除公司失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除公司失败:', error)
      ElMessage.error('删除公司失败')
    }
  } finally {
    deleting.value = ''
  }
}

const handleClose = () => {
  visible.value = false
}

const refreshAndClose = () => {
  emit('refresh')
  handleClose()
}
</script>

<style scoped>
.company-manager {
  .manager-content {
    .add-company {
      margin-bottom: 20px;
      
      h4 {
        margin-bottom: 15px;
        color: #303133;
      }
    }
    
    .company-list {
      h4 {
        margin-bottom: 15px;
        color: #303133;
      }
    }
  }
}
</style>
