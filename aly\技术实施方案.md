# 工单系统集成技术实施方案

## 一、数据库集成设计

### 1. 核心映射表设计

```sql
-- 项目任务与工单映射表
CREATE TABLE project_ticket_mapping (
    id VARCHAR(30) PRIMARY KEY,
    project_id VARCHAR(30) NOT NULL COMMENT '项目ID',
    task_id VARCHAR(30) NOT NULL COMMENT '任务ID', 
    ticket_id VARCHAR(30) NOT NULL COMMENT '工单ID',
    mapping_type ENUM('auto', 'manual') DEFAULT 'auto' COMMENT '映射类型',
    sync_status ENUM('synced', 'pending', 'failed') DEFAULT 'pending' COMMENT '同步状态',
    create_time INT UNSIGNED NOT NULL COMMENT '创建时间',
    update_time INT UNSIGNED NOT NULL COMMENT '更新时间',
    INDEX idx_project_task (project_id, task_id),
    INDEX idx_ticket (ticket_id)
);

-- 状态映射表
CREATE TABLE status_mapping (
    id VARCHAR(30) PRIMARY KEY,
    project_status VARCHAR(50) NOT NULL COMMENT '项目管理系统状态',
    ticket_status_id VARCHAR(30) NOT NULL COMMENT '工单系统状态ID',
    mapping_direction ENUM('both', 'to_ticket', 'to_project') DEFAULT 'both' COMMENT '映射方向'
);
```

### 2. API接口设计

#### 工单数据获取接口
```python
class TicketSystemAPI:
    def __init__(self):
        self.db_config = {
            'host': '**********',
            'user': 'qyuser', 
            'password': 'C~w9d4kaWS',
            'database': 'ticket'
        }
    
    def get_ticket_by_id(self, ticket_id):
        """获取工单详情"""
        sql = """
        SELECT t.*, s.feelec_name as status_name, p.feelec_name as project_name
        FROM feelec_ticket t
        LEFT JOIN feelec_ticket_status s ON t.feelec_status_id = s.feelec_status_id
        LEFT JOIN feelec_project p ON t.feelec_project_id = p.feelec_project_id
        WHERE t.feelec_ticket_id = %s
        """
        return self.execute_query(sql, (ticket_id,))
    
    def get_project_tickets(self, project_id):
        """获取项目下所有工单"""
        sql = """
        SELECT t.*, s.feelec_name as status_name
        FROM feelec_ticket t
        LEFT JOIN feelec_ticket_status s ON t.feelec_status_id = s.feelec_status_id
        WHERE t.feelec_project_id = %s AND t.feelec_delete = 20
        ORDER BY t.create_time DESC
        """
        return self.execute_query(sql, (project_id,))
    
    def get_ticket_process_history(self, ticket_id):
        """获取工单处理历史"""
        sql = """
        SELECT tp.*, e.feelec_name as processor_name
        FROM feelec_ticket_process tp
        LEFT JOIN feelec_employee e ON tp.feelec_processor_id = e.feelec_employee_id
        WHERE tp.feelec_ticket_id = %s
        ORDER BY tp.create_time ASC
        """
        return self.execute_query(sql, (ticket_id,))
```

#### 数据同步接口
```python
class DataSyncService:
    def __init__(self):
        self.ticket_api = TicketSystemAPI()
        self.project_db = ProjectDatabase()
    
    def sync_task_to_ticket(self, task_id):
        """将项目任务同步为工单"""
        task = self.project_db.get_task(task_id)
        
        ticket_data = {
            'feelec_title': task['title'],
            'feelec_project_id': task['project_id'],
            'feelec_publisher_id': task['creator_id'],
            'feelec_processor_id': task['assignee_id'],
            'feelec_department_id': task['department_id'],
            'feelec_priority': self.map_priority(task['priority']),
            'deadlines': task['due_date'],
            'feelec_content': task['description']
        }
        
        ticket_id = self.ticket_api.create_ticket(ticket_data)
        
        # 保存映射关系
        self.save_mapping(task['project_id'], task_id, ticket_id, 'auto')
        
        return ticket_id
    
    def sync_ticket_status_to_task(self, ticket_id):
        """同步工单状态到任务"""
        ticket = self.ticket_api.get_ticket_by_id(ticket_id)
        mapping = self.get_mapping_by_ticket(ticket_id)
        
        if mapping:
            task_status = self.map_ticket_status_to_task(ticket['feelec_status_id'])
            self.project_db.update_task_status(mapping['task_id'], task_status)
```

## 二、核心功能集成

### 1. 任务工单化管理

#### 任务创建时自动生成工单
```python
def create_task_with_ticket(task_data):
    """创建任务并自动生成对应工单"""
    # 1. 创建项目任务
    task_id = create_project_task(task_data)
    
    # 2. 自动创建对应工单
    ticket_data = {
        'title': task_data['title'],
        'project_id': task_data['project_id'],
        'assignee_id': task_data['assignee_id'],
        'priority': task_data['priority'],
        'due_date': task_data['due_date'],
        'description': task_data['description']
    }
    
    sync_service = DataSyncService()
    ticket_id = sync_service.sync_task_to_ticket(task_id)
    
    return {
        'task_id': task_id,
        'ticket_id': ticket_id,
        'status': 'success'
    }
```

#### 工单状态变更同步
```python
def handle_ticket_status_change(ticket_id, new_status):
    """处理工单状态变更"""
    mapping = get_mapping_by_ticket(ticket_id)
    if mapping:
        # 同步状态到项目任务
        task_status = map_ticket_status_to_task_status(new_status)
        update_task_status(mapping['task_id'], task_status)
        
        # 记录同步日志
        log_sync_event(ticket_id, mapping['task_id'], 'status_sync', new_status)
```

### 2. 项目进度可视化

#### 基于工单计算项目进度
```python
def calculate_project_progress(project_id):
    """基于工单完成情况计算项目进度"""
    ticket_api = TicketSystemAPI()
    tickets = ticket_api.get_project_tickets(project_id)
    
    total_tickets = len(tickets)
    completed_tickets = len([t for t in tickets if t['feelec_status_id'] in COMPLETED_STATUS_IDS])
    
    progress = (completed_tickets / total_tickets * 100) if total_tickets > 0 else 0
    
    return {
        'progress_percentage': progress,
        'total_tickets': total_tickets,
        'completed_tickets': completed_tickets,
        'pending_tickets': total_tickets - completed_tickets
    }
```

#### 工单延期预警
```python
def check_ticket_delays(project_id):
    """检查项目工单延期情况"""
    ticket_api = TicketSystemAPI()
    tickets = ticket_api.get_project_tickets(project_id)
    
    current_time = int(time.time())
    delayed_tickets = []
    
    for ticket in tickets:
        if (ticket['deadlines'] > 0 and 
            ticket['deadlines'] < current_time and 
            ticket['feelec_status_id'] not in COMPLETED_STATUS_IDS):
            
            delay_days = (current_time - ticket['deadlines']) // 86400
            delayed_tickets.append({
                'ticket_id': ticket['feelec_ticket_id'],
                'title': ticket['feelec_title'],
                'delay_days': delay_days,
                'assignee_id': ticket['feelec_processor_id']
            })
    
    return delayed_tickets
```

### 3. 团队工作负荷分析

#### 基于工单分析团队负荷
```python
def analyze_team_workload(department_id, start_date, end_date):
    """分析团队工作负荷"""
    sql = """
    SELECT 
        t.feelec_processor_id,
        e.feelec_name as processor_name,
        COUNT(*) as total_tickets,
        SUM(CASE WHEN t.feelec_status_id IN %s THEN 1 ELSE 0 END) as completed_tickets,
        AVG(t.complete_time - t.first_process_time) as avg_process_time
    FROM feelec_ticket t
    LEFT JOIN feelec_employee e ON t.feelec_processor_id = e.feelec_employee_id
    WHERE t.feelec_department_id = %s 
    AND t.create_time BETWEEN %s AND %s
    AND t.feelec_delete = 20
    GROUP BY t.feelec_processor_id, e.feelec_name
    """
    
    return execute_query(sql, (COMPLETED_STATUS_IDS, department_id, start_date, end_date))
```

## 三、实施步骤

### 第一阶段：基础数据同步（1-2周）
1. 建立数据库连接和基础API
2. 创建映射表和同步机制
3. 实现用户、部门、项目基础数据同步

### 第二阶段：核心功能集成（2-3周）
1. 实现任务-工单映射
2. 开发状态同步机制
3. 集成工单创建和更新功能

### 第三阶段：高级功能开发（2-3周）
1. 项目进度可视化
2. 团队负荷分析
3. 延期预警和通知

### 第四阶段：测试和优化（1-2周）
1. 功能测试和性能优化
2. 用户培训和文档编写
3. 上线部署和监控

## 四、技术要点

### 1. 数据一致性保证
- 使用事务确保数据同步的原子性
- 实现数据校验和冲突解决机制
- 建立数据同步日志和回滚机制

### 2. 性能优化
- 使用连接池管理数据库连接
- 实现增量同步减少数据传输
- 添加缓存机制提升查询性能

### 3. 错误处理
- 完善的异常捕获和处理
- 同步失败重试机制
- 详细的错误日志记录

这个技术方案可以确保工单系统与项目管理系统的有效集成。你觉得这个实施方案怎么样？需要我详细说明某个部分吗？
