import{p as s,d as c,h as r,e,w as n,r as t,o as p,f as u,aa as d,m as i}from"./index.js";const l={class:"go-project-my-template"},m=c({__name:"index",setup(f){return(v,x)=>{const a=t("n-image"),o=t("n-h3"),_=t("n-space");return p(),r("div",l,[e(_,{vertical:""},{default:n(()=>[e(a,{"object-fit":"contain",height:"300","preview-disabled":"",src:u(d)()},null,8,["src"]),e(o,null,{default:n(()=>[i("\u6682\u65F6\u8FD8\u6CA1\u6709\u4E1C\u897F\u5462")]),_:1})]),_:1})])}}});var g=s(m,[["__scopeId","data-v-4d4b5d3b"]]);export{g as default};
