#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本 - 测试工单集成API
"""

import requests
import json

def test_api():
    """测试API"""
    try:
        # 测试仪表盘统计
        url = "http://localhost:8000/api/ticket-integration/dashboard-stats"
        headers = {"Authorization": "Bearer test_token"}
        
        print("🔍 测试仪表盘统计...")
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API响应成功")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ API响应失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_api()
