#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的API测试脚本
"""

import requests
import json
import time

def test_complete_api():
    """完整的API测试"""
    print("🧪 完整的API测试")
    print("=" * 60)
    
    base_url = "http://localhost:8000/api/v1"
    
    # 步骤1：登录获取token
    print("🔐 步骤1：登录获取token...")
    try:
        login_response = requests.post(f"{base_url}/auth/login", json={
            "username": "admin",
            "password": "admin123"
        })
        
        print(f"登录状态码: {login_response.status_code}")
        
        if login_response.status_code == 200:
            token = login_response.json().get('access_token')
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败")
            print(f"响应: {login_response.text}")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        return False
    
    # 步骤2：测试新督办管理API
    print("\n📋 步骤2：测试新督办管理API...")
    try:
        response = requests.get(f"{base_url}/new-supervision/items", headers=headers)
        
        print(f"API状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            
            if 'data' in data and 'companies' in data:
                items = data['data']
                companies = data['companies']
                
                print(f"   📊 督办事项数量: {len(items)}")
                print(f"   🏢 公司数量: {len(companies)}")
                
                # 显示前3个督办事项
                print(f"   📝 前3个督办事项:")
                for i, item in enumerate(items[:3]):
                    print(f"      {item.get('sequence_number', 'N/A')}. {item.get('work_theme', 'N/A')}")
                    print(f"         维度: {item.get('work_dimension', 'N/A')}")
                    print(f"         考核指标: {item.get('is_annual_assessment', 'N/A')}")
                    print(f"         进度: {item.get('overall_progress', 'N/A')}")
                
                # 显示公司列表
                print(f"   🏢 公司列表:")
                for company in companies[:5]:
                    print(f"      {company.get('company_code', 'N/A')}: {company.get('company_name', 'N/A')}")
                
                return True
            else:
                print("❌ API返回数据格式错误")
                print(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                return False
        else:
            print(f"❌ API调用失败")
            print(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试异常: {str(e)}")
        return False
    
    # 步骤3：测试公司列表API
    print("\n🏢 步骤3：测试公司列表API...")
    try:
        response = requests.get(f"{base_url}/new-supervision/companies", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 公司列表API调用成功")
            
            if 'data' in data:
                companies = data['data']
                print(f"   🏢 公司数量: {len(companies)}")
                return True
            else:
                print("❌ 公司列表API返回数据格式错误")
                return False
        else:
            print(f"❌ 公司列表API调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 公司列表API测试异常: {str(e)}")
        return False

def test_frontend_access():
    """测试前端访问"""
    print("\n🌐 步骤4：测试前端访问...")
    try:
        # 测试前端页面是否可访问
        response = requests.get("http://localhost:3000/new-supervision")
        
        if response.status_code == 200:
            print("✅ 前端页面可访问")
            return True
        else:
            print(f"❌ 前端页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 前端访问测试异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 督办管理功能完整测试")
    
    # 等待后端服务完全启动
    print("⏳ 等待后端服务完全启动...")
    time.sleep(3)
    
    # 测试API
    api_success = test_complete_api()
    
    # 测试前端访问
    frontend_success = test_frontend_access()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    
    if api_success and frontend_success:
        print("🎉 所有测试通过！督办管理功能完全可用")
        print("\n✅ 功能特色:")
        print("   • 基于督办表文件完全重新设计")
        print("   • 支持完整的增删查改功能")
        print("   • 不分页显示所有数据")
        print("   • 点击单元格编辑状态")
        print("   • 状态变更历史记录")
        print("   • 13家公司状态管理")
        
        print("\n🌐 访问地址:")
        print("   新督办管理: http://localhost:3000/new-supervision")
        print("   旧页面自动跳转: http://localhost:3000/supervision")
        
        print("\n🎯 使用说明:")
        print("   1. 登录系统")
        print("   2. 访问督办管理页面")
        print("   3. 查看督办事项列表")
        print("   4. 点击公司状态单元格编辑")
        print("   5. 使用新增/编辑/删除功能")
        
    else:
        print("❌ 测试失败，功能不可用")
        if not api_success:
            print("   • API测试失败")
        if not frontend_success:
            print("   • 前端访问失败")
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
