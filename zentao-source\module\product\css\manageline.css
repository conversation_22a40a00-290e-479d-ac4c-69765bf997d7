.row-module {max-width: 600px; min-width: 400px;}
.row-module + .row-module {margin-top: 5px;}
.col-module {min-width: 200px;}
.col-module .form-control {border-radius: 2px 0 0 2px;}
.col-module select.form-control {border-radius: 0 2px 2px 0; border-left-width: 0;} 
.col-module select.form-control:focus {margin-left: -1px; border-left-width: 1px;}
.col-module {padding-right: 10px;}
.col-programs .form-control {border-radius: 0 2px 2px 0; border-left-width: 0;} 
.col-programs .form-control:focus {margin-left: -1px; border-left-width: 1px;}
.col-actions {width: 80px; padding-left: 5px;}

#modulesTree li {padding-left: 20px;}
#modulesTree li > .list-toggle {top: 0;}

#lineForm table tr > td.text-middle span {display: block; float: left;}
.tree li> .module-name {display: inline-block; max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}
