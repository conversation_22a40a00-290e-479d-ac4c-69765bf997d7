<?php
$lang->jenkins->common        = 'Jenkins';
$lang->jenkins->browse        = '浏览Jenkins';
$lang->jenkins->create        = '添加Jenkins';
$lang->jenkins->edit          = '编辑Jenkins';
$lang->jenkins->delete        = '删除';
$lang->jenkins->confirmDelete = '确认删除该Jenkins吗？';

$lang->jenkins->browseAction = 'Jenkins列表';
$lang->jenkins->deleteAction = '删除Jenkins';

$lang->jenkins->id       = 'ID';
$lang->jenkins->name     = '应用名称';
$lang->jenkins->url      = '服务器地址';
$lang->jenkins->token    = 'Token';
$lang->jenkins->account  = '用户名';
$lang->jenkins->password = '密码';

$lang->jenkins->lblCreate  = '添加Jenkins服务器';
$lang->jenkins->desc       = '描述';
$lang->jenkins->tokenFirst = 'Token不为空时，优先使用Token。';
$lang->jenkins->tips       = '使用密码时，请在Jenkins全局安全设置中禁用"防止跨站点请求伪造"选项。';
$lang->jenkins->serverList = '服务器列表';

$lang->jenkins->error = new stdclass();
$lang->jenkins->error->linkedJob    = '删除失败，该Jenkins跟构建有关联，请取消关联或删除构建。';
$lang->jenkins->error->unauthorized = 'Jenkins权限验证失败，请检查账号信息是否正确';
