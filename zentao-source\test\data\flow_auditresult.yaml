title: table zt_flow_auditresult
desc: "检查结果"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: auditplan
    note: "检查计划ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: listID
    note: "QA对象ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: result
    note: "检查结果"
    range: pass,fail,useless
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: checkedBy
    note: "检查人"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: checkedDate
    note: "检查时间"
    from: common.date.v1.yaml
    use: dateB
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: comment
    note: "检查备注"
    range: 1-10000
    prefix: "检查备注"
    postfix: ""
    loop: 0
    format: ""
  - field: assignedTo
    note: "指派给"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: "状态"
    range: normal
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: "由谁创建"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdDate
    note: "创建日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedBy
    note: "由谁编辑"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedDate
    note: "编辑日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedBy
    note: "由谁指派"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedDate
    note: "指派日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
