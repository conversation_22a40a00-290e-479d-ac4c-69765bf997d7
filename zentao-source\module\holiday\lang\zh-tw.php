<?php
if(!isset($lang->holiday)) $lang->holiday = new stdclass();
$lang->holiday->common = '節假日';
$lang->holiday->browse = '瀏覽';
$lang->holiday->create = '新建';
$lang->holiday->edit   = '編輯';
$lang->holiday->delete = '刪除';

$lang->holiday->createAction  = '創建節假日';
$lang->holiday->editAction    = '編輯節假日';
$lang->holiday->deleteAction  = '刪除節假日';

$lang->holiday->id    = '編號';
$lang->holiday->name  = '名稱';
$lang->holiday->desc  = '描述';
$lang->holiday->type  = '類型';
$lang->holiday->begin = '開始日期';
$lang->holiday->end   = '結束日期';
$lang->holiday->all   = '所有';

$lang->holiday->holiday = '假期';

$lang->holiday->typeList['holiday'] = '假期';
$lang->holiday->typeList['working'] = '補班';

$lang->holiday->emptyTip      = '暫時沒有節假日。';
$lang->holiday->confirmDelete = '確認刪除節假日？';
