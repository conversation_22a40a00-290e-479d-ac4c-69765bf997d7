#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查PMO系统的所有API路由
"""

import asyncio
import aiohttp
import json

BASE_URL = "http://localhost:8000"

async def check_routes():
    """检查所有可用的路由"""
    async with aiohttp.ClientSession() as session:
        print("🔍 检查PMO系统API路由")
        print("=" * 50)
        
        # 检查OpenAPI文档
        try:
            async with session.get(f"{BASE_URL}/docs") as response:
                if response.status == 200:
                    print("✅ Swagger文档可访问: http://localhost:8000/docs")
                else:
                    print(f"❌ Swagger文档不可访问: {response.status}")
        except Exception as e:
            print(f"❌ 无法访问Swagger文档: {e}")
        
        # 检查OpenAPI JSON
        try:
            async with session.get(f"{BASE_URL}/openapi.json") as response:
                if response.status == 200:
                    openapi_data = await response.json()
                    print(f"✅ OpenAPI JSON可访问")
                    
                    # 解析所有路由
                    paths = openapi_data.get("paths", {})
                    print(f"\n📋 发现 {len(paths)} 个API路径:")
                    
                    for path, methods in paths.items():
                        for method, details in methods.items():
                            summary = details.get("summary", "无描述")
                            tags = details.get("tags", ["未分类"])
                            print(f"  {method.upper()} {path} - {summary} [{', '.join(tags)}]")
                    
                else:
                    print(f"❌ OpenAPI JSON不可访问: {response.status}")
        except Exception as e:
            print(f"❌ 无法访问OpenAPI JSON: {e}")
        
        # 测试一些基本路由
        print(f"\n🧪 测试基本路由:")
        
        test_routes = [
            ("GET", "/", "根路径"),
            ("GET", "/health", "健康检查"),
            ("GET", "/api/v1/docs", "API文档"),
        ]
        
        for method, path, desc in test_routes:
            try:
                async with session.request(method, f"{BASE_URL}{path}") as response:
                    status = "✅" if response.status < 400 else "❌"
                    print(f"  {status} {method} {path} - {desc} ({response.status})")
            except Exception as e:
                print(f"  ❌ {method} {path} - {desc} (异常: {e})")

if __name__ == "__main__":
    asyncio.run(check_routes())
