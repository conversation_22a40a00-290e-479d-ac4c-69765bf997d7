<?php
$lang->jenkins->common        = 'Jenkins';
$lang->jenkins->browse        = '瀏覽Jenkins';
$lang->jenkins->create        = '添加Jenkins';
$lang->jenkins->edit          = '編輯Jenkins';
$lang->jenkins->delete        = '刪除';
$lang->jenkins->confirmDelete = '確認刪除該Jenkins嗎？';

$lang->jenkins->browseAction = 'Jenkins列表';
$lang->jenkins->deleteAction = '刪除Jenkins';

$lang->jenkins->id       = 'ID';
$lang->jenkins->name     = '名稱';
$lang->jenkins->url      = '服務地址';
$lang->jenkins->token    = 'Token';
$lang->jenkins->account  = '用戶名';
$lang->jenkins->password = '密碼';

$lang->jenkins->lblCreate  = '添加Jenkins伺服器';
$lang->jenkins->desc       = '描述';
$lang->jenkins->tokenFirst = 'Token不為空時，優先使用Token。';
$lang->jenkins->tips       = '使用密碼時，請在Jenkins全局安全設置中禁用"防止跨站點請求偽造"選項。';
$lang->jenkins->serverList = '伺服器列表';

$lang->jenkins->error = new stdclass();
$lang->jenkins->error->linkedJob = '刪除失敗，該Jenkins跟構建有關聯，請取消關聯或刪除構建。';
