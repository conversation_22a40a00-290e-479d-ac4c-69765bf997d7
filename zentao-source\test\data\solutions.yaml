title: table zt_solutions
desc: "里程碑报告问题"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: program
    note: "所属项目"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: project
    note: "所属阶段"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: contents
    note: "问题描述"
    range: 1-10000
    prefix: "问题描述"
    postfix: ""
    loop: 0
    format: ""
  - field: support
    note: "需要高层支持"
    range: yes,no
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: measures
    note: "解决建议"
    range: 1-10000
    prefix: "这里是解决建议"
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "类型"
    range: otherproblem
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: addedBy
    note: "添加者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: addedDate
    note: "添加日期"
    from: common.date.v1.yaml
    use: dateB
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedBy
    note: "编辑者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedDate
    note: "编辑日期"
    from: common.date.v1.yaml
    use: dateB
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
