#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API端点是否存在
"""

import requests

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:8000/api/v1/ticket-integration"
    
    # 测试的端点
    endpoints = [
        "/dashboard-stats",
        "/projects", 
        "/projects/87657135068024832/tickets",
        "/tickets/87825666896171008/full-content"
    ]
    
    headers = {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json'
    }
    
    print("🔍 测试API端点...")
    print("=" * 50)
    
    for endpoint in endpoints:
        url = base_url + endpoint
        print(f"\n📡 测试: {endpoint}")
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 成功")
            elif response.status_code == 401:
                print(f"   🔐 认证失败（正常，需要有效token）")
            elif response.status_code == 404:
                print(f"   ❌ 端点不存在")
            else:
                print(f"   ⚠️  其他错误: {response.text[:100]}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接失败，服务器可能未启动")
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")

if __name__ == "__main__":
    test_api_endpoints()
