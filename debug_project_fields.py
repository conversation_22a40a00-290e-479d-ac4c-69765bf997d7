#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试项目ID和项目名称字段问题
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'pmo-backend'))

from app.core.database import TicketSystemConnector
from datetime import datetime

def debug_project_fields():
    """调试项目ID和项目名称字段"""
    print("🔍 调试项目ID和项目名称字段")
    print("=" * 60)
    
    connector = TicketSystemConnector()
    
    try:
        # 1. 先查看工单表结构，确认项目ID字段
        print("\n📋 1. 查看工单表中的项目ID字段...")
        structure_sql = """
        SELECT feelec_ticket_id, feelec_ticket_no, feelec_title, feelec_project_id
        FROM feelec_ticket 
        WHERE feelec_delete = 20 
        AND feelec_project_id IS NOT NULL 
        AND feelec_project_id != 0
        LIMIT 5
        """
        
        tickets_with_project = connector.execute_query(structure_sql)
        print(f"找到 {len(tickets_with_project)} 个有项目ID的工单:")
        
        for ticket in tickets_with_project:
            print(f"  工单ID: {ticket['feelec_ticket_id']}")
            print(f"  工单编号: {ticket['feelec_ticket_no']}")
            print(f"  工单标题: {ticket['feelec_title'][:30]}...")
            print(f"  项目ID: {ticket['feelec_project_id']}")
            print("  ---")
        
        # 2. 查看项目表，确认项目名称
        print("\n📋 2. 查看项目表中的项目信息...")
        if tickets_with_project:
            project_ids = [str(ticket['feelec_project_id']) for ticket in tickets_with_project]
            project_sql = f"""
            SELECT feelec_project_id, feelec_name
            FROM feelec_project 
            WHERE feelec_project_id IN ({','.join(project_ids)})
            AND feelec_delete = 20
            """
            
            projects = connector.execute_query(project_sql)
            print(f"找到 {len(projects)} 个项目:")
            
            for project in projects:
                print(f"  项目ID: {project['feelec_project_id']}")
                print(f"  项目名称: {project['feelec_name']}")
                print("  ---")
        
        # 3. 测试完整的JOIN查询（项目工单列表的SQL）
        print("\n📋 3. 测试项目工单列表的完整JOIN查询...")
        if tickets_with_project:
            project_id = tickets_with_project[0]['feelec_project_id']
            
            project_tickets_sql = """
            SELECT
                t.feelec_ticket_id,
                t.feelec_ticket_no,
                t.feelec_title,
                t.feelec_project_id,
                s.feelec_name as status_name,
                u1.feelec_name as publisher_name,
                u2.feelec_name as processor_name,
                d.feelec_name as department_name,
                c.feelec_name as company_name,
                tt.feelec_name as template_name,
                p.feelec_name as project_name
            FROM feelec_ticket t
            LEFT JOIN feelec_ticket_status s ON t.feelec_status_id = s.feelec_status_id AND s.delete_time = 0
            LEFT JOIN feelec_user u1 ON t.feelec_publisher_id = u1.feelec_user_id AND u1.delete_time = 0
            LEFT JOIN feelec_user u2 ON t.feelec_processor_id = u2.feelec_user_id AND u2.delete_time = 0
            LEFT JOIN feelec_member_department d ON t.feelec_department_id = d.feelec_department_id AND d.delete_time = 0
            LEFT JOIN feelec_company c ON t.feelec_company_id = c.feelec_company_id AND c.delete_time = 0
            LEFT JOIN feelec_ticket_template tt ON t.feelec_template_id = tt.feelec_template_id AND tt.delete_time = 0
            LEFT JOIN feelec_project p ON t.feelec_project_id = p.feelec_project_id AND p.feelec_delete = 20
            WHERE t.feelec_project_id = %s AND t.feelec_delete = 20
            ORDER BY t.create_time DESC
            LIMIT 3
            """
            
            project_tickets = connector.execute_query(project_tickets_sql, (project_id,))
            print(f"项目 {project_id} 的工单 ({len(project_tickets)} 个):")
            
            for ticket in project_tickets:
                print(f"  工单编号: {ticket['feelec_ticket_no']}")
                print(f"  工单标题: {ticket['feelec_title'][:30]}...")
                print(f"  项目ID: {ticket['feelec_project_id']}")
                print(f"  项目名称: {ticket['project_name']}")
                print(f"  状态: {ticket['status_name']}")
                print("  ---")
        
        # 4. 测试按状态获取工单的JOIN查询
        print("\n📋 4. 测试按状态获取工单的完整JOIN查询...")
        
        status_tickets_sql = """
        SELECT
            t.feelec_ticket_id,
            t.feelec_ticket_no,
            t.feelec_title,
            t.feelec_project_id,
            s.feelec_name as status_name,
            u1.feelec_name as publisher_name,
            u2.feelec_name as processor_name,
            d.feelec_name as department_name,
            c.feelec_name as company_name,
            tt.feelec_name as template_name,
            p.feelec_name as project_name
        FROM feelec_ticket t
        LEFT JOIN feelec_ticket_status s ON t.feelec_status_id = s.feelec_status_id AND s.delete_time = 0
        LEFT JOIN feelec_user u1 ON t.feelec_publisher_id = u1.feelec_user_id AND u1.delete_time = 0
        LEFT JOIN feelec_user u2 ON t.feelec_processor_id = u2.feelec_user_id AND u2.delete_time = 0
        LEFT JOIN feelec_member_department d ON t.feelec_department_id = d.feelec_department_id AND d.delete_time = 0
        LEFT JOIN feelec_company c ON t.feelec_company_id = c.feelec_company_id AND c.delete_time = 0
        LEFT JOIN feelec_ticket_template tt ON t.feelec_template_id = tt.feelec_template_id AND tt.delete_time = 0
        LEFT JOIN feelec_project p ON t.feelec_project_id = p.feelec_project_id AND p.feelec_delete = 20
        WHERE t.feelec_delete = 20
        ORDER BY t.create_time DESC
        LIMIT 5
        """
        
        status_tickets = connector.execute_query(status_tickets_sql)
        print(f"按状态获取的工单 ({len(status_tickets)} 个):")
        
        for ticket in status_tickets:
            print(f"  工单编号: {ticket['feelec_ticket_no']}")
            print(f"  工单标题: {ticket['feelec_title'][:30]}...")
            print(f"  项目ID: {ticket['feelec_project_id']}")
            print(f"  项目名称: {ticket['project_name']}")
            print(f"  状态: {ticket['status_name']}")
            
            # 检查项目字段是否为空
            if not ticket['feelec_project_id'] or ticket['feelec_project_id'] in [None, '', 0]:
                print(f"  ⚠️  项目ID为空或无效！")
            else:
                print(f"  ✅ 项目ID正常: {ticket['feelec_project_id']}")
                
            if not ticket['project_name'] or ticket['project_name'] in [None, '']:
                print(f"  ⚠️  项目名称为空！可能项目不存在或已删除")
            else:
                print(f"  ✅ 项目名称正常: {ticket['project_name']}")
            print("  ---")
        
        # 5. 统计分析
        print("\n📊 5. 数据统计分析...")
        
        stats_sql = """
        SELECT 
            COUNT(*) as total_tickets,
            COUNT(CASE WHEN t.feelec_project_id IS NOT NULL AND t.feelec_project_id != 0 THEN 1 END) as tickets_with_project_id,
            COUNT(CASE WHEN p.feelec_name IS NOT NULL THEN 1 END) as tickets_with_project_name
        FROM feelec_ticket t
        LEFT JOIN feelec_project p ON t.feelec_project_id = p.feelec_project_id AND p.feelec_delete = 20
        WHERE t.feelec_delete = 20
        """
        
        stats = connector.execute_query(stats_sql)
        if stats:
            stat = stats[0]
            print(f"  总工单数: {stat['total_tickets']}")
            print(f"  有项目ID的工单: {stat['tickets_with_project_id']}")
            print(f"  有项目名称的工单: {stat['tickets_with_project_name']}")
            
            if stat['tickets_with_project_id'] > stat['tickets_with_project_name']:
                print(f"  ⚠️  有 {stat['tickets_with_project_id'] - stat['tickets_with_project_name']} 个工单的项目已被删除或不存在")
            else:
                print(f"  ✅ 项目数据完整性良好")
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        connector.close()

def main():
    """主函数"""
    print("🚀 开始调试项目ID和项目名称字段问题")
    print(f"⏰ 调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    debug_project_fields()
    
    print(f"\n" + "=" * 60)
    print("📝 调试总结:")
    print("1. 检查工单表中是否有项目ID字段")
    print("2. 检查项目表中是否有对应的项目记录")
    print("3. 验证JOIN查询是否正确关联项目信息")
    print("4. 对比项目工单列表和按状态获取工单的查询结果")
    print("5. 分析数据完整性问题")

if __name__ == "__main__":
    main()
