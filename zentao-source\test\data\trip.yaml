title: table zt_trip
desc: ""
author: automated export
version: "1.0"
fields:
  - field: id
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: type
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: customers
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: name
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: desc
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: year
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: begin
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: end
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: start
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: finish
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: from
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: to
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: createdBy
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: createdDate
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
