title: table zt_testrun
desc: "测试执行"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: task
    note: "测试单任务ID"
    range: 1-10{4},51-60{3}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: case
    note: "用例ID"
    range: 1-40,411-440
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: version
    note: "版本"
    range: 1
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedTo
    note: "指派给"
    fields:
      - field: openedBy1
        range: admin,user,test,dev
      - field: openedBy2
        range: [],2-4
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: lastRunner
    note: "最后执行人"
    range: []{40},admin{30}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: lastRunDate
    note: "最后执行时间"
    range: "(M)-(w):60"
    type: timestamp
    prefix: ""
    postfix: ""
    loop: 0
    format: "YYYY-MM-DD"
  - field: lastRunResult
    note: "最后执行结果"
    range: [pass,fail]{20!},[pass,pass,fail]{15!}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: "状态"
    range: normal{40},done{30}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
