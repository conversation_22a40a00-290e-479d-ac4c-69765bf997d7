.panel-actions {display: flex; width: 100%; flex-direction: row-reverse; justify-content: space-between;}
menu .panel-actions {width: auto;}
td .check-list {flex-direction:row;}
th.form-batch-head[data-name=ACTIONS]{width:120px;}
tr.disabled td[data-name="enabled"] .switch label{filter: unset;}
.max-w-100px {max-width: 100px; overflow: hidden;}
.disabled [data-name="enabled"] {pointer-events: all; cursor: pointer !important;}
button.disabled {opacity: 0.5;}
td[data-name="enabled"] {vertical-align: top !important;}
td[data-name="enabled"] .switch {margin-top: 5px;}