import{d as c,r,o as l,h as u,z as f,j as a,c as n,w as s,e as i,ad as d,p as h}from"./index.js";const m=c({__name:"index",props:{title:String,showTop:{type:Boolean,default:!0},showBottom:{type:Boolean,default:!1},flex:{type:Boolean,default:!1},backIcon:{type:Boolean,default:!0},depth:{type:Number,default:1},xScroll:{type:Boolean,default:!1},disabledScroll:{type:Boolean,default:!1}},emits:["back"],setup(e,{emit:p}){return(t,B)=>{const o=r("n-scrollbar");return l(),u("div",{class:d(["go-content-box",[`bg-depth${e.depth}`,e.flex&&"flex"]])},[f("div",{class:d(["content",{"content-height-show-top-bottom":e.showBottom||e.showTop,"content-height-show-both":e.showBottom&&e.showTop}])},[e.disabledScroll?a(t.$slots,"default",{key:0},void 0,!0):e.xScroll?(l(),n(o,{key:1,"x-scrollable":""},{default:s(()=>[i(o,null,{default:s(()=>[a(t.$slots,"default",{},void 0,!0)]),_:3})]),_:3})):(l(),n(o,{key:2},{default:s(()=>[a(t.$slots,"default",{},void 0,!0)]),_:3}))],2)],2)}}});var x=h(m,[["__scopeId","data-v-633d4f12"]]);export{x as C};
