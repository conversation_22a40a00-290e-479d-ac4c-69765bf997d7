#!/usr/bin/env php
<?php
declare(strict_types=1);
include dirname(__FILE__, 5) . '/test/lib/init.php';
include dirname(__FILE__, 2) . '/lib/report.unittest.class.php';

zenData('user')->gen(1);

su('admin');

/**

title=测试 reportModel->getYearMonths();
cid=1
pid=1

测试获取 2020 的月份 >> 2020-01,2020-02,2020-03,2020-04,2020-05,2020-06,2020-07,2020-08,2020-09,2020-10,2020-11,2020-12
测试获取 2021 的月份 >> 2021-01,2021-02,2021-03,2021-04,2021-05,2021-06,2021-07,2021-08,2021-09,2021-10,2021-11,2021-12
测试获取 2022 的月份 >> 2022-01,2022-02,2022-03,2022-04,2022-05,2022-06,2022-07,2022-08,2022-09,2022-10,2022-11,2022-12
测试获取 2023 的月份 >> 2023-01,2023-02,2023-03,2023-04,2023-05,2023-06,2023-07,2023-08,2023-09,2023-10,2023-11,2023-12
测试获取 2024 的月份 >> 2024-01,2024-02,2024-03,2024-04,2024-05,2024-06,2024-07,2024-08,2024-09,2024-10,2024-11,2024-12

*/
$year = array('2020', '2021', '2022', '2023', '2024');

$report = new reportTest();

r($report->getYearMonthsTest($year[0])) && p() && e('2020-01,2020-02,2020-03,2020-04,2020-05,2020-06,2020-07,2020-08,2020-09,2020-10,2020-11,2020-12'); // 测试获取 2020 的月份
r($report->getYearMonthsTest($year[1])) && p() && e('2021-01,2021-02,2021-03,2021-04,2021-05,2021-06,2021-07,2021-08,2021-09,2021-10,2021-11,2021-12'); // 测试获取 2021 的月份
r($report->getYearMonthsTest($year[2])) && p() && e('2022-01,2022-02,2022-03,2022-04,2022-05,2022-06,2022-07,2022-08,2022-09,2022-10,2022-11,2022-12'); // 测试获取 2022 的月份
r($report->getYearMonthsTest($year[3])) && p() && e('2023-01,2023-02,2023-03,2023-04,2023-05,2023-06,2023-07,2023-08,2023-09,2023-10,2023-11,2023-12'); // 测试获取 2023 的月份
r($report->getYearMonthsTest($year[4])) && p() && e('2024-01,2024-02,2024-03,2024-04,2024-05,2024-06,2024-07,2024-08,2024-09,2024-10,2024-11,2024-12'); // 测试获取 2024 的月份
