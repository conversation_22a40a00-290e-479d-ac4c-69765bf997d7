#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工单系统集成完整功能测试脚本
测试所有新增的功能：项目清单、工单详情、工单完整内容
"""

import asyncio
import json
import requests
import time
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:8001/api/v1/ticket-integration"

# 测试用的认证头（简单实现）
HEADERS = {
    "Authorization": "Bearer test_token",
    "Content-Type": "application/json"
}

async def test_api_endpoints():
    """测试所有API端点"""
    print("🚀 开始测试工单系统集成完整功能")
    print("=" * 60)
    
    # 1. 测试获取仪表盘统计
    print("📊 测试获取仪表盘统计...")
    try:
        response = requests.get(f"{BASE_URL}/dashboard-stats", headers=HEADERS)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                stats = data.get('data', {})
                print(f"✅ 仪表盘统计获取成功:")
                print(f"   总项目数: {stats.get('project_stats', {}).get('total_projects', 0)}")
                print(f"   总工单数: {stats.get('ticket_stats', {}).get('total_tickets', 0)}")
                print(f"   已完成工单: {stats.get('ticket_stats', {}).get('completed_tickets', 0)}")
                print(f"   紧急工单: {stats.get('ticket_stats', {}).get('urgent_tickets', 0)}")
            else:
                print(f"❌ 仪表盘统计获取失败: {data.get('message')}")
        else:
            print(f"❌ 仪表盘统计API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 仪表盘统计测试异常: {e}")
    
    # 2. 测试获取项目列表（模拟点击总项目数）
    print("\n📋 测试获取项目列表（模拟点击总项目数）...")
    try:
        response = requests.get(f"{BASE_URL}/projects?limit=100", headers=HEADERS)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                projects = data.get('data', {}).get('projects', [])
                print(f"✅ 项目列表获取成功，共 {len(projects)} 个项目:")
                
                # 显示前5个项目
                for i, project in enumerate(projects[:5]):
                    print(f"   项目 {i+1}: {project['feelec_name']} (ID: {project['feelec_project_id']})")
                    print(f"      工单总数: {project.get('total_tickets', 0)}")
                    print(f"      完成率: {project.get('completion_rate', 0)}%")
                    print(f"      状态: {'已完成' if project.get('is_completed') else '进行中'}")
                
                # 选择第一个项目进行详细测试
                if projects:
                    test_project = projects[0]
                    project_id = test_project['feelec_project_id']
                    
                    # 3. 测试获取项目工单详情（模拟点击项目）
                    print(f"\n🎫 测试获取项目工单详情（项目: {test_project['feelec_name']}）...")
                    try:
                        response = requests.get(f"{BASE_URL}/projects/{project_id}/tickets", headers=HEADERS)
                        if response.status_code == 200:
                            data = response.json()
                            if data.get('success'):
                                tickets = data.get('data', {}).get('tickets', [])
                                print(f"✅ 项目工单获取成功，共 {len(tickets)} 个工单:")
                                
                                # 显示工单列表
                                for i, ticket in enumerate(tickets[:3]):
                                    print(f"   工单 {i+1}: {ticket['feelec_title']}")
                                    print(f"      编号: {ticket['feelec_ticket_no']}")
                                    print(f"      优先级: {ticket.get('priority_text', '普通')}")
                                    print(f"      状态: {ticket.get('status_name', '未知')}")
                                
                                # 选择第一个工单进行完整内容测试
                                if tickets:
                                    test_ticket = tickets[0]
                                    ticket_id = test_ticket['feelec_ticket_id']
                                    
                                    # 4. 测试获取工单完整内容（模拟点击工单）
                                    print(f"\n📄 测试获取工单完整内容（工单: {test_ticket['feelec_title']}）...")
                                    try:
                                        response = requests.get(f"{BASE_URL}/tickets/{ticket_id}/full-content", headers=HEADERS)
                                        if response.status_code == 200:
                                            data = response.json()
                                            if data.get('success'):
                                                ticket_content = data.get('data', {})
                                                print(f"✅ 工单完整内容获取成功:")
                                                print(f"   工单编号: {ticket_content.get('feelec_ticket_no')}")
                                                print(f"   工单标题: {ticket_content.get('feelec_title')}")
                                                print(f"   项目名称: {ticket_content.get('project_name')}")
                                                print(f"   发布人: {ticket_content.get('publisher_name')}")
                                                print(f"   处理人: {ticket_content.get('processor_name')}")
                                                print(f"   优先级: {ticket_content.get('priority_text')}")
                                                print(f"   当前状态: {ticket_content.get('status_name')}")
                                                print(f"   创建时间: {ticket_content.get('create_time_formatted')}")
                                                print(f"   完成时间: {ticket_content.get('complete_time_formatted')}")
                                                print(f"   处理时长: {ticket_content.get('process_duration_text')}")
                                                print(f"   是否逾期: {'是' if ticket_content.get('is_overdue') else '否'}")
                                                
                                                # 显示工单内容
                                                content = ticket_content.get('feelec_content', '').strip()
                                                if content:
                                                    print(f"   工单内容: {content[:100]}{'...' if len(content) > 100 else ''}")
                                                
                                                # 显示处理记录
                                                process_records = ticket_content.get('process_records', [])
                                                if process_records:
                                                    print(f"   处理记录数: {len(process_records)} 条")
                                                    for record in process_records[:2]:
                                                        print(f"      - {record.get('action_name', '未知操作')} ({record.get('create_time_formatted')})")
                                                
                                            else:
                                                print(f"❌ 工单完整内容获取失败: {data.get('message')}")
                                        else:
                                            print(f"❌ 工单完整内容API请求失败: {response.status_code}")
                                    except Exception as e:
                                        print(f"❌ 工单完整内容测试异常: {e}")
                                
                            else:
                                print(f"❌ 项目工单获取失败: {data.get('message')}")
                        else:
                            print(f"❌ 项目工单API请求失败: {response.status_code}")
                    except Exception as e:
                        print(f"❌ 项目工单测试异常: {e}")
                
            else:
                print(f"❌ 项目列表获取失败: {data.get('message')}")
        else:
            print(f"❌ 项目列表API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 项目列表测试异常: {e}")
    
    # 5. 测试工作负荷分析
    print("\n📈 测试工作负荷分析...")
    try:
        response = requests.get(f"{BASE_URL}/workload-analysis?days=30", headers=HEADERS)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                workload = data.get('data', {})
                total_stats = workload.get('total_stats', {})
                print(f"✅ 工作负荷分析获取成功:")
                print(f"   分析周期: {total_stats.get('analysis_period', '未知')}")
                print(f"   处理人数: {total_stats.get('total_processors', 0)}")
                print(f"   总工单数: {total_stats.get('total_tickets', 0)}")
                print(f"   整体完成率: {total_stats.get('overall_completion_rate', 0)}%")
                
                workload_data = workload.get('workload_data', [])
                if workload_data:
                    print(f"   团队负荷详情（前3名）:")
                    for i, item in enumerate(workload_data[:3]):
                        print(f"      处理人 {i+1}: {item.get('feelec_processor_id', '未知')}")
                        print(f"         总工单: {item.get('total_tickets', 0)}")
                        print(f"         完成率: {item.get('completion_rate', 0)}%")
                        print(f"         负荷等级: {item.get('workload_text', '未知')}")
            else:
                print(f"❌ 工作负荷分析获取失败: {data.get('message')}")
        else:
            print(f"❌ 工作负荷分析API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 工作负荷分析测试异常: {e}")
    
    # 6. 测试集成建议
    print("\n💡 测试集成建议...")
    try:
        response = requests.get(f"{BASE_URL}/integration-suggestions", headers=HEADERS)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                suggestions = data.get('data', {}).get('suggestions', [])
                print(f"✅ 集成建议获取成功，共 {len(suggestions)} 条建议:")
                
                for i, suggestion in enumerate(suggestions):
                    print(f"   建议 {i+1}: {suggestion.get('title')}")
                    print(f"      类别: {suggestion.get('category')}")
                    print(f"      优先级: {suggestion.get('priority')}")
                    print(f"      描述: {suggestion.get('description')}")
                    benefits = suggestion.get('benefits', [])
                    if benefits:
                        print(f"      预期收益: {', '.join(benefits[:2])}")
            else:
                print(f"❌ 集成建议获取失败: {data.get('message')}")
        else:
            print(f"❌ 集成建议API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 集成建议测试异常: {e}")

def test_frontend_functionality():
    """测试前端功能"""
    print("\n🌐 前端功能测试指南:")
    print("=" * 60)
    print("请在浏览器中访问: http://localhost:3000/ticket-integration")
    print("\n📋 测试步骤:")
    print("1. 点击 '总项目数' 卡片 -> 应该弹出项目清单对话框")
    print("2. 在项目清单中点击任意项目行 -> 应该弹出工单详情对话框")
    print("3. 在工单详情中点击任意工单行 -> 应该弹出工单完整内容对话框")
    print("4. 验证工单完整内容包含:")
    print("   - 基本信息（编号、标题、项目、类型、发布人、处理人等）")
    print("   - 时间信息（创建、分配、处理、完成、截止时间等）")
    print("   - 工单内容（详细描述）")
    print("   - 附加信息（来源、逾期状态等）")
    print("   - 处理记录（如果有的话）")
    print("\n✅ 预期结果:")
    print("- 所有对话框都能正常打开和关闭")
    print("- 数据显示完整且格式正确")
    print("- 点击操作响应迅速")
    print("- 界面美观且用户体验良好")

def generate_test_report():
    """生成测试报告"""
    print("\n📊 生成测试报告...")
    
    report = {
        "test_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "test_scope": "工单系统集成完整功能测试",
        "test_items": {
            "dashboard_stats": "仪表盘统计数据获取",
            "project_list": "项目清单展示（点击总项目数）",
            "project_tickets": "项目工单详情（点击具体项目）",
            "ticket_full_content": "工单完整内容（点击工单明细）",
            "workload_analysis": "团队工作负荷分析",
            "integration_suggestions": "集成建议"
        },
        "api_endpoints": [
            "GET /ticket-integration/dashboard-stats",
            "GET /ticket-integration/projects",
            "GET /ticket-integration/projects/{project_id}/tickets",
            "GET /ticket-integration/tickets/{ticket_id}/full-content",
            "GET /ticket-integration/workload-analysis",
            "GET /ticket-integration/integration-suggestions"
        ],
        "frontend_features": [
            "可点击的统计卡片",
            "项目清单对话框",
            "工单详情对话框", 
            "工单完整内容对话框",
            "响应式表格设计",
            "完整的数据展示"
        ],
        "next_steps": [
            "在浏览器中测试前端交互功能",
            "验证所有对话框的打开和关闭",
            "确认数据显示的完整性和准确性",
            "测试不同项目和工单的数据展示",
            "验证错误处理和异常情况"
        ]
    }
    
    # 保存测试报告
    report_file = f"ticket_integration_complete_test_report_{int(datetime.now().timestamp())}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"📄 测试报告已保存到: {report_file}")
    print("\n🎉 工单系统集成完整功能测试完成!")
    print("📋 测试总结:")
    for key, value in report["test_items"].items():
        print(f"   {key}: {value}")

async def main():
    """主测试函数"""
    print("🎯 开始工单系统集成完整功能测试")
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 测试API端点
    await test_api_endpoints()
    
    # 前端功能测试指南
    test_frontend_functionality()
    
    # 生成测试报告
    generate_test_report()

if __name__ == "__main__":
    asyncio.run(main())
