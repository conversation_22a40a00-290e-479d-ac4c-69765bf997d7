{% extends "base.html" %}

{% block content %}
<h2>导入 Excel 到表: {{ table_name }}</h2>
<a href="/table/{{ db_name }}/{{ table_name }}" class="btn btn-secondary">返回</a>

<div class="card mt-3">
    <div class="card-header">
        上传 Excel 文件
    </div>
    <div class="card-body">
        <form action="/import_excel/{{ db_name }}/{{ table_name }}" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label for="file">选择 Excel 文件</label>
                <input type="file" class="form-control-file" id="file" name="file" accept=".xlsx,.xls" required>
            </div>
            <small class="text-muted">注意：Excel 文件的列名必须与表结构匹配，空值将被视为 NULL。</small>
            <div class="mt-3">
                <button type="submit" class="btn btn-primary">导入数据</button>
            </div>
        </form>
    </div>
</div>
{% endblock %} 