title: repo.
desc: repo data.
author: Zeng Gang
version: "1.0"
fields:
- field: id
  range: 1-5
- field: product
  range: 1-4
- field: projects
  range: 11-14
- field: name
  range: testHtml,project1,unittest,testSvn
- field: path
  range: "[http://gitlabdev.qc.oop.cc/gitlab-instance-76af86df/testhtml],[],[/var/www/html/gitlab/test2/zentaopms/www/data/repo/unittest_gitea],[https://svn.qc.oop.cc/svn/unittest/]"
- field: SCM
  range: Gitlab{2},Gitea,Subversion
- field: client
  range: "git{3},svn{1}"
- field: serviceHost
  range: 1{2},4,0
- field: serviceProject
  range: "2,1,[gitea/unittest],[]"
- field: commits
  range: 0{2},2
- field: account
  range: "[]{3},admin"
- field: password
  range: "[]{3},[S1hkT2k4emdUY1VxRUZYMkh4OEI=]"
- field: encrypt
  range: base64
- field: synced
  range: 1
- field: extra
  range: 9
- field: preMerge
  range: 0
- field: job
  range: 0
- field: deleted
  range: 0
