<?php
$orData = array('ipdproductplan' => '产品规划人员', 'ipddemand' => '需求分析人员', 'ipdpmt' => 'PMT团队人员', 'ipdadmin' => '管理人员');

$ipdproductplanPriv = "(GROUPID, 'admin', 'index'), (GROUPID, 'admin', 'register'), (GROUPID, 'backup', 'backup'), (GROUPID, 'backup', 'change'), (GROUPID, 'backup', 'delete'), (GROUPID, 'backup', 'index'), (GROUPID, 'backup', 'restore'), (GROUPID, 'backup', 'rmPHPHeader'), (GROUPID, 'backup', 'setting'), (GROUPID, 'branch', 'activate'), (GROUPID, 'branch', 'batchEdit'), (GROUPID, 'branch', 'close'), (GROUPID, 'branch', 'create'), (GROUPID, 'branch', 'edit'), (GROUPID, 'branch', 'manage'), (GROUPID, 'branch', 'mergeBranch'), (GROUPID, 'branch', 'sort'), (GROUPID, 'charter', 'browse'), (GROUPID, 'charter', 'create'), (GROUPID, 'charter', 'edit'), (GROUPID, 'charter', 'loadRoadmapStories'), (GROUPID, 'charter', 'review'), (GROUPID, 'charter', 'view'), (GROUPID, 'company', 'dynamic'), (GROUPID, 'company', 'edit'), (GROUPID, 'company', 'view'), (GROUPID, 'custom', 'browseStoryConcept'), (GROUPID, 'custom', 'deleteStoryConcept'), (GROUPID, 'custom', 'editStoryConcept'), (GROUPID, 'custom', 'product'), (GROUPID, 'custom', 'required'), (GROUPID, 'custom', 'restore'), (GROUPID, 'custom', 'set'), (GROUPID, 'custom', 'setDefaultConcept'), (GROUPID, 'custom', 'setStoryConcept'), (GROUPID, 'demand', 'activate'), (GROUPID, 'demand', 'assignTo'), (GROUPID, 'demand', 'batchCreate'), (GROUPID, 'demand', 'browse'), (GROUPID, 'demand', 'change'), (GROUPID, 'demand', 'close'), (GROUPID, 'demand', 'create'), (GROUPID, 'demand', 'distribute'), (GROUPID, 'demand', 'edit'), (GROUPID, 'demand', 'recall'), (GROUPID, 'demand', 'review'), (GROUPID, 'demand', 'submitReview'), (GROUPID, 'demand', 'track'), (GROUPID, 'demand', 'view'), (GROUPID, 'demandpool', 'activate'), (GROUPID, 'demandpool', 'browse'), (GROUPID, 'demandpool', 'close'), (GROUPID, 'demandpool', 'create'), (GROUPID, 'demandpool', 'edit'), (GROUPID, 'demandpool', 'view'), (GROUPID, 'doc', 'addCatalog'), (GROUPID, 'doc', 'collect'), (GROUPID, 'doc', 'create'), (GROUPID, 'doc', 'createLib'), (GROUPID, 'doc', 'delete'), (GROUPID, 'doc', 'deleteCatalog'), (GROUPID, 'doc', 'deleteFile'), (GROUPID, 'doc', 'displaySetting'), (GROUPID, 'doc', 'edit'), (GROUPID, 'doc', 'editCatalog'), (GROUPID, 'doc', 'editLib'), (GROUPID, 'doc', 'index'), (GROUPID, 'doc', 'myCollection'), (GROUPID, 'doc', 'myCreation'), (GROUPID, 'doc', 'myEdited'), (GROUPID, 'doc', 'mySpace'), (GROUPID, 'doc', 'myView'), (GROUPID, 'doc', 'productSpace'), (GROUPID, 'doc', 'projectSpace'), (GROUPID, 'doc', 'showFiles'), (GROUPID, 'doc', 'sortCatalog')";

$ipddemandPriv = "(GROUPID, 'doc', 'teamSpace'), (GROUPID, 'doc', 'view'), (GROUPID, 'effort', 'batchCreate'), (GROUPID, 'effort', 'batchEdit'), (GROUPID, 'effort', 'calendar'), (GROUPID, 'effort', 'createForObject'), (GROUPID, 'effort', 'delete'), (GROUPID, 'effort', 'edit'), (GROUPID, 'effort', 'export'), (GROUPID, 'effort', 'view'), (GROUPID, 'file', 'delete'), (GROUPID, 'file', 'download'), (GROUPID, 'file', 'edit'), (GROUPID, 'file', 'setPublic'), (GROUPID, 'file', 'uploadImages'), (GROUPID, 'index', 'index'), (GROUPID, 'mail', 'batchDelete'), (GROUPID, 'mail', 'browse'), (GROUPID, 'mail', 'delete'), (GROUPID, 'mail', 'detect'), (GROUPID, 'mail', 'edit'), (GROUPID, 'mail', 'index'), (GROUPID, 'mail', 'resend'), (GROUPID, 'mail', 'reset'), (GROUPID, 'mail', 'save'), (GROUPID, 'mail', 'test'), (GROUPID, 'message', 'browser'), (GROUPID, 'message', 'index'), (GROUPID, 'message', 'setting'), (GROUPID, 'my', 'audit'), (GROUPID, 'my', 'calendar'), (GROUPID, 'my', 'changePassword'), (GROUPID, 'my', 'dynamic'), (GROUPID, 'my', 'editProfile'), (GROUPID, 'my', 'index'), (GROUPID, 'my', 'profile'), (GROUPID, 'my', 'team'), (GROUPID, 'my', 'todo'), (GROUPID, 'my', 'uploadAvatar'), (GROUPID, 'my', 'work'), (GROUPID, 'product', 'all'), (GROUPID, 'product', 'browse'), (GROUPID, 'product', 'dashboard'), (GROUPID, 'product', 'dynamic'), (GROUPID, 'product', 'requirement'), (GROUPID, 'product', 'view'), (GROUPID, 'requirement', 'report'), (GROUPID, 'requirement', 'view'), (GROUPID, 'roadmap', 'browse'), (GROUPID, 'roadmap', 'view'), (GROUPID, 'search', 'buildForm'), (GROUPID, 'search', 'deleteQuery'), (GROUPID, 'search', 'index'), (GROUPID, 'search', 'saveQuery'), (GROUPID, 'search', 'select'), (GROUPID, 'sms', 'index'), (GROUPID, 'sms', 'reset'), (GROUPID, 'sms', 'test'), (GROUPID, 'todo', 'activate'), (GROUPID, 'todo', 'assignTo'), (GROUPID, 'todo', 'batchClose'), (GROUPID, 'todo', 'batchCreate'), (GROUPID, 'todo', 'batchEdit'), (GROUPID, 'todo', 'batchFinish'), (GROUPID, 'todo', 'close'), (GROUPID, 'todo', 'create'), (GROUPID, 'todo', 'createcycle'), (GROUPID, 'todo', 'delete'), (GROUPID, 'todo', 'edit'), (GROUPID, 'todo', 'export'), (GROUPID, 'todo', 'finish'), (GROUPID, 'todo', 'import2Today'), (GROUPID, 'todo', 'start'), (GROUPID, 'todo', 'view'), (GROUPID, 'tree', 'browse'), (GROUPID, 'tree', 'delete'), (GROUPID, 'tree', 'edit'), (GROUPID, 'tree', 'fix'), (GROUPID, 'tree', 'manageChild'), (GROUPID, 'tree', 'updateOrder'), (GROUPID, 'user', 'bug'), (GROUPID, 'user', 'dynamic'), (GROUPID, 'user', 'execution'), (GROUPID, 'user', 'profile'), (GROUPID, 'user', 'setPublicTemplate'), (GROUPID, 'user', 'story'), (GROUPID, 'user', 'task'), (GROUPID, 'user', 'testCase'), (GROUPID, 'user', 'testTask'), (GROUPID, 'user', 'todo'), (GROUPID, 'webhook', 'bind'), (GROUPID, 'webhook', 'browse'), (GROUPID, 'webhook', 'chooseDept'), (GROUPID, 'webhook', 'create'), (GROUPID, 'webhook', 'delete'), (GROUPID, 'webhook', 'edit'), (GROUPID, 'webhook', 'log')";

$ipdpmtPriv = "(GROUPID, 'admin', 'index'), (GROUPID, 'admin', 'register'), (GROUPID, 'branch', 'activate'), (GROUPID, 'branch', 'batchEdit'), (GROUPID, 'branch', 'close'), (GROUPID, 'branch', 'create'), (GROUPID, 'branch', 'edit'), (GROUPID, 'branch', 'manage'), (GROUPID, 'branch', 'mergeBranch'), (GROUPID, 'branch', 'sort'), (GROUPID, 'charter', 'browse'), (GROUPID, 'charter', 'create'), (GROUPID, 'charter', 'delete'), (GROUPID, 'charter', 'edit'), (GROUPID, 'charter', 'loadRoadmapStories'), (GROUPID, 'charter', 'review'), (GROUPID, 'charter', 'view'), (GROUPID, 'company', 'dynamic'), (GROUPID, 'company', 'edit'), (GROUPID, 'company', 'view'), (GROUPID, 'custom', 'browseStoryConcept'), (GROUPID, 'custom', 'code'), (GROUPID, 'custom', 'deleteStoryConcept'), (GROUPID, 'custom', 'editStoryConcept'), (GROUPID, 'custom', 'flow'), (GROUPID, 'custom', 'hours'), (GROUPID, 'custom', 'percent'), (GROUPID, 'custom', 'product'), (GROUPID, 'custom', 'required'), (GROUPID, 'custom', 'restore'), (GROUPID, 'custom', 'set'), (GROUPID, 'custom', 'setDefaultConcept'), (GROUPID, 'custom', 'setStoryConcept'), (GROUPID, 'demand', 'activate'), (GROUPID, 'demand', 'assignTo'), (GROUPID, 'demand', 'batchCreate'), (GROUPID, 'demand', 'browse'), (GROUPID, 'demand', 'change'), (GROUPID, 'demand', 'close'), (GROUPID, 'demand', 'create'), (GROUPID, 'demand', 'distribute'), (GROUPID, 'demand', 'edit'), (GROUPID, 'demand', 'recall'), (GROUPID, 'demand', 'review'), (GROUPID, 'demand', 'submitReview'), (GROUPID, 'demand', 'track'), (GROUPID, 'demand', 'view'), (GROUPID, 'demandpool', 'activate'), (GROUPID, 'demandpool', 'browse'), (GROUPID, 'demandpool', 'close'), (GROUPID, 'demandpool', 'create'), (GROUPID, 'demandpool', 'edit'), (GROUPID, 'demandpool', 'view'), (GROUPID, 'doc', 'addCatalog'), (GROUPID, 'doc', 'collect'), (GROUPID, 'doc', 'create'), (GROUPID, 'doc', 'createLib'), (GROUPID, 'doc', 'delete'), (GROUPID, 'doc', 'deleteCatalog'), (GROUPID, 'doc', 'deleteFile'), (GROUPID, 'doc', 'deleteLib'), (GROUPID, 'doc', 'displaySetting'), (GROUPID, 'doc', 'edit'), (GROUPID, 'doc', 'editCatalog'), (GROUPID, 'doc', 'editLib'), (GROUPID, 'doc', 'index'), (GROUPID, 'doc', 'myCollection'), (GROUPID, 'doc', 'myCreation'), (GROUPID, 'doc', 'myEdited'), (GROUPID, 'doc', 'mySpace'), (GROUPID, 'doc', 'myView'), (GROUPID, 'doc', 'productSpace'), (GROUPID, 'doc', 'projectSpace'), (GROUPID, 'doc', 'showFiles'), (GROUPID, 'doc', 'sortCatalog'), (GROUPID, 'doc', 'teamSpace'), (GROUPID, 'doc', 'view'), (GROUPID, 'effort', 'batchCreate'), (GROUPID, 'effort', 'batchEdit'), (GROUPID, 'effort', 'calendar'), (GROUPID, 'effort', 'createForObject'), (GROUPID, 'effort', 'delete'), (GROUPID, 'effort', 'edit'), (GROUPID, 'effort', 'export'), (GROUPID, 'effort', 'view'), (GROUPID, 'file', 'delete'), (GROUPID, 'file', 'download'), (GROUPID, 'file', 'edit'), (GROUPID, 'file', 'setPublic'), (GROUPID, 'file', 'uploadImages'), (GROUPID, 'index', 'index'), (GROUPID, 'my', 'audit'), (GROUPID, 'my', 'calendar'), (GROUPID, 'my', 'changePassword'), (GROUPID, 'my', 'dynamic'), (GROUPID, 'my', 'editProfile'), (GROUPID, 'my', 'index'), (GROUPID, 'my', 'profile'), (GROUPID, 'my', 'team'), (GROUPID, 'my', 'todo'), (GROUPID, 'my', 'uploadAvatar'), (GROUPID, 'my', 'work'), (GROUPID, 'product', 'activate'), (GROUPID, 'product', 'addWhitelist'), (GROUPID, 'product', 'all'), (GROUPID, 'product', 'batchEdit'), (GROUPID, 'product', 'browse'), (GROUPID, 'product', 'close'), (GROUPID, 'product', 'create'), (GROUPID, 'product', 'dashboard'), (GROUPID, 'product', 'dynamic'), (GROUPID, 'product', 'edit'), (GROUPID, 'product', 'export'), (GROUPID, 'product', 'requirement'), (GROUPID, 'product', 'unbindWhitelist'), (GROUPID, 'product', 'updateOrder'), (GROUPID, 'product', 'view'), (GROUPID, 'product', 'whitelist'), (GROUPID, 'requirement', 'activate'), (GROUPID, 'requirement', 'assignTo'), (GROUPID, 'requirement', 'batchAssignTo'), (GROUPID, 'requirement', 'batchChangeBranch'), (GROUPID, 'requirement', 'batchChangeModule'), (GROUPID, 'requirement', 'batchClose'), (GROUPID, 'requirement', 'batchCreate'), (GROUPID, 'requirement', 'batchEdit'), (GROUPID, 'requirement', 'batchReview'), (GROUPID, 'requirement', 'change'), (GROUPID, 'requirement', 'close'), (GROUPID, 'requirement', 'create'), (GROUPID, 'requirement', 'delete'), (GROUPID, 'requirement', 'edit'), (GROUPID, 'requirement', 'export'), (GROUPID, 'requirement', 'linkRequirements'), (GROUPID, 'requirement', 'linkStory'), (GROUPID, 'requirement', 'recall'), (GROUPID, 'requirement', 'report'), (GROUPID, 'requirement', 'review'), (GROUPID, 'requirement', 'submitReview'), (GROUPID, 'requirement', 'view'), (GROUPID, 'roadmap', 'activate'), (GROUPID, 'roadmap', 'batchUnlinkUR'), (GROUPID, 'roadmap', 'browse'), (GROUPID, 'roadmap', 'close'), (GROUPID, 'roadmap', 'create'), (GROUPID, 'roadmap', 'edit'), (GROUPID, 'roadmap', 'linkUR'), (GROUPID, 'roadmap', 'unlinkUR'), (GROUPID, 'roadmap', 'view'), (GROUPID, 'search', 'buildForm'), (GROUPID, 'search', 'deleteQuery'), (GROUPID, 'search', 'index'), (GROUPID, 'search', 'saveQuery'), (GROUPID, 'search', 'select'), (GROUPID, 'todo', 'activate'), (GROUPID, 'todo', 'assignTo'), (GROUPID, 'todo', 'batchClose'), (GROUPID, 'todo', 'batchCreate'), (GROUPID, 'todo', 'batchEdit'), (GROUPID, 'todo', 'batchFinish'), (GROUPID, 'todo', 'close'), (GROUPID, 'todo', 'create'), (GROUPID, 'todo', 'createcycle'), (GROUPID, 'todo', 'delete'), (GROUPID, 'todo', 'edit'), (GROUPID, 'todo', 'export'), (GROUPID, 'todo', 'finish'), (GROUPID, 'todo', 'import2Today'), (GROUPID, 'todo', 'start'), (GROUPID, 'todo', 'view'), (GROUPID, 'tree', 'browse'), (GROUPID, 'tree', 'delete'), (GROUPID, 'tree', 'edit'), (GROUPID, 'tree', 'fix'), (GROUPID, 'tree', 'manageChild'), (GROUPID, 'tree', 'updateOrder'), (GROUPID, 'user', 'bug'), (GROUPID, 'user', 'dynamic'), (GROUPID, 'user', 'execution'), (GROUPID, 'user', 'profile'), (GROUPID, 'user', 'setPublicTemplate'), (GROUPID, 'user', 'story'), (GROUPID, 'user', 'task'), (GROUPID, 'user', 'testCase'), (GROUPID, 'user', 'testTask'), (GROUPID, 'user', 'todo')";

$ipdadminPriv = "(GROUPID, 'admin', 'checkWeak'), (GROUPID, 'admin', 'index'), (GROUPID, 'admin', 'register'), (GROUPID, 'admin', 'resetPWDSetting'), (GROUPID, 'admin', 'safe'), (GROUPID, 'admin', 'tableEngine'), (GROUPID, 'backup', 'backup'), (GROUPID, 'backup', 'change'), (GROUPID, 'backup', 'delete'), (GROUPID, 'backup', 'index'), (GROUPID, 'backup', 'restore'), (GROUPID, 'backup', 'rmPHPHeader'), (GROUPID, 'backup', 'setting'), (GROUPID, 'branch', 'activate'), (GROUPID, 'branch', 'batchEdit'), (GROUPID, 'branch', 'close'), (GROUPID, 'branch', 'create'), (GROUPID, 'branch', 'edit'), (GROUPID, 'branch', 'manage'), (GROUPID, 'branch', 'mergeBranch'), (GROUPID, 'branch', 'sort'), (GROUPID, 'charter', 'browse'), (GROUPID, 'charter', 'create'), (GROUPID, 'charter', 'delete'), (GROUPID, 'charter', 'edit'), (GROUPID, 'charter', 'loadRoadmapStories'), (GROUPID, 'charter', 'review'), (GROUPID, 'charter', 'view'), (GROUPID, 'company', 'browse'), (GROUPID, 'company', 'dynamic'), (GROUPID, 'company', 'edit'), (GROUPID, 'company', 'view'), (GROUPID, 'custom', 'browseStoryConcept'), (GROUPID, 'custom', 'code'), (GROUPID, 'custom', 'deleteStoryConcept'), (GROUPID, 'custom', 'editStoryConcept'), (GROUPID, 'custom', 'flow'), (GROUPID, 'custom', 'hours'), (GROUPID, 'custom', 'percent'), (GROUPID, 'custom', 'product'), (GROUPID, 'custom', 'required'), (GROUPID, 'custom', 'restore'), (GROUPID, 'custom', 'set'), (GROUPID, 'custom', 'setDefaultConcept'), (GROUPID, 'custom', 'setStoryConcept'), (GROUPID, 'custom', 'timezone'), (GROUPID, 'demand', 'activate'), (GROUPID, 'demand', 'assignTo'), (GROUPID, 'demand', 'batchCreate'), (GROUPID, 'demand', 'browse'), (GROUPID, 'demand', 'change'), (GROUPID, 'demand', 'close'), (GROUPID, 'demand', 'create'), (GROUPID, 'demand', 'delete'), (GROUPID, 'demand', 'distribute'), (GROUPID, 'demand', 'edit'), (GROUPID, 'demand', 'recall'), (GROUPID, 'demand', 'review'), (GROUPID, 'demand', 'submitReview'), (GROUPID, 'demand', 'track'), (GROUPID, 'demand', 'view'), (GROUPID, 'demandpool', 'activate'), (GROUPID, 'demandpool', 'browse'), (GROUPID, 'demandpool', 'close'), (GROUPID, 'demandpool', 'create'), (GROUPID, 'demandpool', 'delete'), (GROUPID, 'demandpool', 'edit'), (GROUPID, 'demandpool', 'view'), (GROUPID, 'dept', 'browse'), (GROUPID, 'dept', 'delete'), (GROUPID, 'dept', 'edit'), (GROUPID, 'dept', 'manageChild'), (GROUPID, 'dept', 'updateOrder'), (GROUPID, 'doc', 'addCatalog'), (GROUPID, 'doc', 'collect'), (GROUPID, 'doc', 'create'), (GROUPID, 'doc', 'createLib'), (GROUPID, 'doc', 'delete'), (GROUPID, 'doc', 'deleteCatalog'), (GROUPID, 'doc', 'deleteFile'), (GROUPID, 'doc', 'deleteLib'), (GROUPID, 'doc', 'displaySetting'), (GROUPID, 'doc', 'edit'), (GROUPID, 'doc', 'editCatalog'), (GROUPID, 'doc', 'editLib'), (GROUPID, 'doc', 'index'), (GROUPID, 'doc', 'myCollection'), (GROUPID, 'doc', 'myCreation'), (GROUPID, 'doc', 'myEdited'), (GROUPID, 'doc', 'mySpace'), (GROUPID, 'doc', 'myView'), (GROUPID, 'doc', 'productSpace'), (GROUPID, 'doc', 'projectSpace'), (GROUPID, 'doc', 'showFiles'), (GROUPID, 'doc', 'sortCatalog'), (GROUPID, 'doc', 'teamSpace'), (GROUPID, 'doc', 'view'), (GROUPID, 'effort', 'batchCreate'), (GROUPID, 'effort', 'batchEdit'), (GROUPID, 'effort', 'calendar'), (GROUPID, 'effort', 'createForObject'), (GROUPID, 'effort', 'delete'), (GROUPID, 'effort', 'edit'), (GROUPID, 'effort', 'export'), (GROUPID, 'effort', 'view'), (GROUPID, 'file', 'delete'), (GROUPID, 'file', 'download'), (GROUPID, 'file', 'edit'), (GROUPID, 'file', 'setPublic'), (GROUPID, 'file', 'uploadImages'), (GROUPID, 'group', 'browse'), (GROUPID, 'group', 'copy'), (GROUPID, 'group', 'create'), (GROUPID, 'group', 'delete'), (GROUPID, 'group', 'edit'), (GROUPID, 'group', 'manageMember'), (GROUPID, 'group', 'managePriv'), (GROUPID, 'group', 'manageProjectAdmin'), (GROUPID, 'group', 'manageView'), (GROUPID, 'index', 'index'), (GROUPID, 'mail', 'batchDelete'), (GROUPID, 'mail', 'browse'), (GROUPID, 'mail', 'delete'), (GROUPID, 'mail', 'detect'), (GROUPID, 'mail', 'edit'), (GROUPID, 'mail', 'index'), (GROUPID, 'mail', 'resend'), (GROUPID, 'mail', 'reset'), (GROUPID, 'mail', 'save'), (GROUPID, 'mail', 'test'), (GROUPID, 'message', 'browser'), (GROUPID, 'message', 'index'), (GROUPID, 'message', 'setting'), (GROUPID, 'my', 'audit'), (GROUPID, 'my', 'calendar'), (GROUPID, 'my', 'changePassword'), (GROUPID, 'my', 'dynamic'), (GROUPID, 'my', 'editProfile'), (GROUPID, 'my', 'index'), (GROUPID, 'my', 'profile'), (GROUPID, 'my', 'team'), (GROUPID, 'my', 'todo'), (GROUPID, 'my', 'uploadAvatar'), (GROUPID, 'my', 'work'), (GROUPID, 'product', 'activate'), (GROUPID, 'product', 'addWhitelist'), (GROUPID, 'product', 'all'), (GROUPID, 'product', 'batchEdit'), (GROUPID, 'product', 'browse'), (GROUPID, 'product', 'close'), (GROUPID, 'product', 'create'), (GROUPID, 'product', 'dashboard'), (GROUPID, 'product', 'delete'), (GROUPID, 'product', 'dynamic'), (GROUPID, 'product', 'edit'), (GROUPID, 'product', 'export'), (GROUPID, 'product', 'requirement'), (GROUPID, 'product', 'unbindWhitelist'), (GROUPID, 'product', 'updateOrder'), (GROUPID, 'product', 'view'), (GROUPID, 'product', 'whitelist'), (GROUPID, 'requirement', 'activate'), (GROUPID, 'requirement', 'assignTo'), (GROUPID, 'requirement', 'batchAssignTo'), (GROUPID, 'requirement', 'batchChangeBranch'), (GROUPID, 'requirement', 'batchChangeModule'), (GROUPID, 'requirement', 'batchClose'), (GROUPID, 'requirement', 'batchCreate'), (GROUPID, 'requirement', 'batchEdit'), (GROUPID, 'requirement', 'batchReview'), (GROUPID, 'requirement', 'change'), (GROUPID, 'requirement', 'close'), (GROUPID, 'requirement', 'create'), (GROUPID, 'requirement', 'delete'), (GROUPID, 'requirement', 'edit'), (GROUPID, 'requirement', 'export'), (GROUPID, 'requirement', 'linkRequirements'), (GROUPID, 'requirement', 'linkStory'), (GROUPID, 'requirement', 'recall'), (GROUPID, 'requirement', 'report'), (GROUPID, 'requirement', 'review'), (GROUPID, 'requirement', 'submitReview'), (GROUPID, 'requirement', 'view'), (GROUPID, 'roadmap', 'activate'), (GROUPID, 'roadmap', 'batchUnlinkUR'), (GROUPID, 'roadmap', 'browse'), (GROUPID, 'roadmap', 'close'), (GROUPID, 'roadmap', 'create'), (GROUPID, 'roadmap', 'delete'), (GROUPID, 'roadmap', 'edit'), (GROUPID, 'roadmap', 'linkUR'), (GROUPID, 'roadmap', 'unlinkUR'), (GROUPID, 'roadmap', 'view'), (GROUPID, 'search', 'buildForm'), (GROUPID, 'search', 'buildIndex'), (GROUPID, 'search', 'deleteQuery'), (GROUPID, 'search', 'index'), (GROUPID, 'search', 'saveQuery'), (GROUPID, 'search', 'select'), (GROUPID, 'sms', 'index'), (GROUPID, 'sms', 'reset'), (GROUPID, 'sms', 'test'), (GROUPID, 'todo', 'activate'), (GROUPID, 'todo', 'assignTo'), (GROUPID, 'todo', 'batchClose'), (GROUPID, 'todo', 'batchCreate'), (GROUPID, 'todo', 'batchEdit'), (GROUPID, 'todo', 'batchFinish'), (GROUPID, 'todo', 'close'), (GROUPID, 'todo', 'create'), (GROUPID, 'todo', 'createcycle'), (GROUPID, 'todo', 'delete'), (GROUPID, 'todo', 'edit'), (GROUPID, 'todo', 'export'), (GROUPID, 'todo', 'finish'), (GROUPID, 'todo', 'import2Today'), (GROUPID, 'todo', 'start'), (GROUPID, 'todo', 'view'), (GROUPID, 'tree', 'browse'), (GROUPID, 'tree', 'delete'), (GROUPID, 'tree', 'edit'), (GROUPID, 'tree', 'fix'), (GROUPID, 'tree', 'manageChild'), (GROUPID, 'tree', 'updateOrder'), (GROUPID, 'user', 'batchCreate'), (GROUPID, 'user', 'batchEdit'), (GROUPID, 'user', 'bug'), (GROUPID, 'user', 'create'), (GROUPID, 'user', 'delete'), (GROUPID, 'user', 'dynamic'), (GROUPID, 'user', 'edit'), (GROUPID, 'user', 'execution'), (GROUPID, 'user', 'profile'), (GROUPID, 'user', 'setPublicTemplate'), (GROUPID, 'user', 'story'), (GROUPID, 'user', 'task'), (GROUPID, 'user', 'testCase'), (GROUPID, 'user', 'testTask'), (GROUPID, 'user', 'todo'), (GROUPID, 'user', 'unlock'), (GROUPID, 'user', 'view'), (GROUPID, 'webhook', 'bind'), (GROUPID, 'webhook', 'browse'), (GROUPID, 'webhook', 'chooseDept'), (GROUPID, 'webhook', 'create'), (GROUPID, 'webhook', 'delete'), (GROUPID, 'webhook', 'edit'), (GROUPID, 'webhook', 'log')";
