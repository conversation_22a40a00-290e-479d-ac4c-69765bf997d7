#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试档案数量跳转功能
"""

def test_jump_url_generation():
    """测试跳转URL的生成逻辑"""
    print("🧪 测试档案数量跳转功能...")
    
    # 模拟项目数据
    test_project = {
        'project_code': 'TEST_001',
        'project_name': '测试项目档案数量功能',
        'archive_count': {
            'total_files': 4,
            'markdown_files': 2,
            'archive_summary': '共4个文件(md:2)'
        }
    }
    
    # 模拟前端路由生成逻辑
    def generate_archive_url(project):
        base_url = "http://localhost:3000"
        route_path = "/project-archive"
        
        query_params = {
            'projectCode': project['project_code'],
            'projectName': project['project_name'],
            'autoSelect': 'true',
            'showFiles': 'true'
        }
        
        # 构建查询字符串
        query_string = '&'.join([f"{key}={value}" for key, value in query_params.items()])
        full_url = f"{base_url}{route_path}?{query_string}"
        
        return full_url
    
    # 生成跳转URL
    jump_url = generate_archive_url(test_project)
    
    print("📋 项目信息:")
    print(f"   项目编号: {test_project['project_code']}")
    print(f"   项目名称: {test_project['project_name']}")
    print(f"   档案摘要: {test_project['archive_count']['archive_summary']}")
    
    print("\n🔗 生成的跳转URL:")
    print(f"   {jump_url}")
    
    print("\n📊 URL参数说明:")
    print("   ✅ projectCode: 项目编号，用于自动选择项目")
    print("   ✅ projectName: 项目名称，用于显示")
    print("   ✅ autoSelect: 自动选择项目")
    print("   ✅ showFiles: 自动展开文件夹列表")
    
    return jump_url

def test_archive_folder_structure():
    """测试档案文件夹结构"""
    print("\n📁 测试档案文件夹结构...")
    
    import os
    
    archive_base_dir = "project_archive_materials"
    project_code = "TEST_001"
    project_dir = os.path.join(archive_base_dir, project_code)
    
    if not os.path.exists(project_dir):
        print(f"❌ 项目档案目录不存在: {project_dir}")
        return False
    
    print(f"✅ 项目档案目录存在: {project_dir}")
    
    # 分析文件夹结构
    folder_structure = {}
    total_files = 0
    
    for root, dirs, files in os.walk(project_dir):
        rel_path = os.path.relpath(root, project_dir)
        
        if rel_path == ".":
            folder_name = "根目录"
        else:
            folder_name = rel_path.split(os.sep)[0]
        
        if folder_name not in folder_structure:
            folder_structure[folder_name] = {
                'files': [],
                'count': 0
            }
        
        for file in files:
            if file.endswith(('.pdf', '.doc', '.docx', '.md', '.txt', '.xlsx', '.xls', '.jpg', '.png')):
                folder_structure[folder_name]['files'].append(file)
                folder_structure[folder_name]['count'] += 1
                total_files += 1
    
    print(f"\n📊 文件夹结构分析:")
    print(f"   总文件数: {total_files}")
    
    for folder_name, info in folder_structure.items():
        print(f"   📂 {folder_name}: {info['count']} 个文件")
        for file in info['files']:
            file_type = "📝 Markdown" if file.endswith('.md') else "📄 其他"
            print(f"      {file_type}: {file}")
    
    return total_files > 0

def main():
    print("🚀 档案数量跳转功能测试")
    print("=" * 60)
    
    # 测试URL生成
    jump_url = test_jump_url_generation()
    
    # 测试档案文件夹结构
    has_files = test_archive_folder_structure()
    
    if has_files:
        print("\n🎉 测试通过！档案跳转功能已完善")
        print("\n📋 功能说明:")
        print("   ✅ 点击档案数量会跳转到项目档案页面")
        print("   ✅ 自动选择对应的项目")
        print("   ✅ 自动展开项目的文件夹列表")
        print("   ✅ 用户可以直接看到项目的所有档案文件")
        
        print("\n🌐 测试步骤:")
        print("   1. 打开浏览器访问: http://localhost:3000")
        print("   2. 登录系统")
        print("   3. 点击'项目管理'菜单")
        print("   4. 找到TEST_001项目，点击档案数量")
        print("   5. 应该会自动跳转并展开文件夹列表")
        
        print(f"\n🔗 或者直接访问测试URL:")
        print(f"   {jump_url}")
    else:
        print("\n❌ 测试失败，请检查档案文件是否存在")
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
