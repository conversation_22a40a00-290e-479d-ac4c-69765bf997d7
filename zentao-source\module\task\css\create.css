#copyButton {width: 80px; background-color: #f1f1f1; opacity: 1;} /* Adjust for task #4151; */

.title-group.required:after {display: none;}
.title-group.required > .required:after {display: block; right: 29px; top: 5px;}
#copyStory-input:after {display: block; right: 110px; top: 5px;}
#pri + .chosen-container > .chosen-single {border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important;}

.pri-selector > .btn {padding: 5px 8px !important; width: 100%;}
.pri-selector > .dropdown-menu {padding: 10px;}

#testStoryBox table tr td {padding: 0px;}
#testStoryBox table tr td .chosen-container .chosen-single {border: 0px;}
#testStoryBox table tr td .chosen-compact.chosen-container-single .chosen-single>.chosen-search {top: 0; right: 0; left: 0;}
#testStoryBox table tr td input {border: 0px;}
#testStoryBox table tr td .input-group .input-group-addon {border-left: 1px solid #dcdcdc; border-right: 1px solid #dcdcdc;}
#testStoryBox table button.btn {border: 0px;}
#testStoryBox .c-name {width: 120px;}
#testStoryBox .c-pri, #testStoryBox .c-estimate, #testStoryBox .c-actions {width: 70px;}
#testStoryBox .c-date {width: 140px;}
#testStoryBox .c-assignedTo {width: 100px; padding: 7px;}

.chosen-results > li.has-test,
.chosen-results > li.has-new-test {position: relative; color: #388E3C;}
.chosen-results > li.has-test.highlighted,
.chosen-results > li.has-new-test.highlighted {color: #fff;}

.c-modulel {width: 110px;}

.keep-row-height {height: 32px;}
#modalTeam .modal-dialog {min-height: 120px; width: 570px; color: #3c4353;}
#taskTeamEditor .hourBox {padding-bottom: 7px; width: 135px;}
#taskTeamEditor .estimateBox {width: 120px;}
#modalTeam .modal-content {padding: 0;}
