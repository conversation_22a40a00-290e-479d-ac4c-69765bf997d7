#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析当前数据库结构，设计新的统一表结构
"""

import pymysql
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

# 数据库配置
DB_CONFIG = {
    'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'cyh',
    'password': 'Qq188788',
    'database': 'kanban2',
    'charset': 'utf8mb4'
}

def analyze_database():
    """分析当前数据库结构"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        logging.info("🔍 分析当前数据库结构")
        logging.info("=" * 80)
        
        # 1. 分析督办事项表
        logging.info("1. 督办事项表 (supervision_items) 结构:")
        cursor.execute("DESCRIBE supervision_items")
        columns = cursor.fetchall()
        for col in columns:
            logging.info(f"   - {col['Field']}: {col['Type']} {col['Null']} {col['Key']} {col['Default']}")
        
        # 2. 分析公司表
        logging.info("\n2. 公司表 (companies) 结构:")
        cursor.execute("DESCRIBE companies")
        columns = cursor.fetchall()
        for col in columns:
            logging.info(f"   - {col['Field']}: {col['Type']} {col['Null']} {col['Key']} {col['Default']}")
        
        # 3. 分析 company_progress 表
        logging.info("\n3. company_progress 表结构:")
        try:
            cursor.execute("DESCRIBE company_progress")
            columns = cursor.fetchall()
            for col in columns:
                logging.info(f"   - {col['Field']}: {col['Type']} {col['Null']} {col['Key']} {col['Default']}")
            
            cursor.execute("SELECT COUNT(*) as count FROM company_progress")
            count = cursor.fetchone()
            logging.info(f"   数据量: {count['count']} 条")
            
        except Exception as e:
            logging.warning(f"   company_progress 表问题: {e}")
        
        # 4. 分析 company_supervision_status 表
        logging.info("\n4. company_supervision_status 表结构:")
        try:
            cursor.execute("DESCRIBE company_supervision_status")
            columns = cursor.fetchall()
            for col in columns:
                logging.info(f"   - {col['Field']}: {col['Type']} {col['Null']} {col['Key']} {col['Default']}")
            
            cursor.execute("SELECT COUNT(*) as count FROM company_supervision_status")
            count = cursor.fetchone()
            logging.info(f"   数据量: {count['count']} 条")
            
        except Exception as e:
            logging.warning(f"   company_supervision_status 表问题: {e}")
        
        # 5. 检查数据一致性
        logging.info("\n5. 数据一致性检查:")
        
        # 检查督办事项数量
        cursor.execute("SELECT COUNT(*) as count FROM supervision_items WHERE deleted_at IS NULL")
        items_count = cursor.fetchone()['count']
        logging.info(f"   有效督办事项: {items_count} 个")
        
        # 检查公司数量
        cursor.execute("SELECT COUNT(*) as count FROM companies WHERE is_active = 1")
        companies_count = cursor.fetchone()['count']
        logging.info(f"   活跃公司: {companies_count} 家")
        
        # 理论上应该有的状态记录数
        expected_records = items_count * companies_count
        logging.info(f"   理论状态记录数: {expected_records} 条")
        
        # 实际状态记录数
        try:
            cursor.execute("SELECT COUNT(*) as count FROM company_progress")
            progress_count = cursor.fetchone()['count']
            logging.info(f"   company_progress 实际记录: {progress_count} 条")
        except:
            progress_count = 0
            logging.info(f"   company_progress 实际记录: 0 条 (表不存在)")
        
        try:
            cursor.execute("SELECT COUNT(*) as count FROM company_supervision_status")
            status_count = cursor.fetchone()['count']
            logging.info(f"   company_supervision_status 实际记录: {status_count} 条")
        except:
            status_count = 0
            logging.info(f"   company_supervision_status 实际记录: 0 条 (表不存在)")
        
        # 6. 检查状态值分布
        logging.info("\n6. 状态值分布:")
        
        if progress_count > 0:
            cursor.execute("SELECT status, COUNT(*) as count FROM company_progress GROUP BY status")
            progress_status = cursor.fetchall()
            logging.info("   company_progress 状态分布:")
            for status in progress_status:
                logging.info(f"     - {status['status']}: {status['count']} 条")
        
        if status_count > 0:
            cursor.execute("SELECT status, COUNT(*) as count FROM company_supervision_status GROUP BY status")
            supervision_status = cursor.fetchall()
            logging.info("   company_supervision_status 状态分布:")
            for status in supervision_status:
                logging.info(f"     - {status['status']}: {status['count']} 条")
        
        cursor.close()
        connection.close()
        
        logging.info("=" * 80)
        logging.info("🎯 分析完成")
        
        # 7. 设计建议
        logging.info("\n📋 数据库重设计建议:")
        logging.info("=" * 80)
        logging.info("问题:")
        logging.info("1. 存在两个状态表，数据不一致")
        logging.info("2. supervision_items 表有冗余的公司状态字段")
        logging.info("3. 状态值格式不统一（符号 vs 中文）")
        logging.info("")
        logging.info("解决方案:")
        logging.info("1. 统一使用一个状态表: company_supervision_progress")
        logging.info("2. 移除 supervision_items 表中的公司状态字段")
        logging.info("3. 统一状态值为符号格式: √, O, ！, X, —")
        logging.info("4. 添加完整的审计字段和索引")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 分析失败: {e}")
        return False

def main():
    """主函数"""
    logging.info("🚀 开始分析数据库结构")
    
    if analyze_database():
        logging.info("🎯 分析完成！")
    else:
        logging.error("❌ 分析失败")

if __name__ == "__main__":
    main()
