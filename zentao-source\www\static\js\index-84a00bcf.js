import{d as h,a5 as c,h as l,F as b,A as x,ac as t,r as z,o,ad as u,ae as B,e as F,w,c as I,L as R,p as q}from"./index.js";import{i as L}from"./icon-bb3d09e7.js";const g={class:"go-apple-control-btn"},E=["onClick"],M=h({__name:"index",props:{mini:{request:!1,type:<PERSON><PERSON>an,default:!1},disabled:{request:!1,type:Boolean,default:!1},hidden:{request:!1,type:Array,default(){return[]}},narrow:{request:!1,type:Boolean,default:!1}},emits:["close","remove","resize","fullResize"],setup(s,{emit:d}){const f=d,a=s,{CloseIcon:m,RemoveIcon:_,ResizeIcon:p}=L.ionicons5,v=c(()=>k.filter(r=>a.hidden.findIndex(i=>r.key==i)===-1)),y=c(()=>a.narrow&&t(!0)),k=[{title:"\u5173\u95ED",key:"close",icon:m},{title:"\u7F29\u5C0F",key:"remove",icon:_},{title:y.value?"\u7F29\u5C0F":"\u653E\u5927",key:a.narrow?"fullResize":"resize",icon:p}],C=e=>{e==="fullResize"&&t(),e==="remove"&&t(!0)&&t(),f(e)};return(e,r)=>{const i=z("n-icon");return o(),l("div",g,[(o(!0),l(b,null,x(v.value,n=>(o(),l("div",{key:n.key,class:u(["btn",[n.key,s.disabled&&"disabled",s.mini&&"mini"]]),onClick:B(A=>C(n.key),["stop"])},[F(i,{size:"10",class:u(["icon-base",{hover:!s.disabled}])},{default:w(()=>[(o(),I(R(n.icon)))]),_:2},1032,["class"])],10,E))),128))])}}});var O=q(M,[["__scopeId","data-v-56375fab"]]);export{O as M};
