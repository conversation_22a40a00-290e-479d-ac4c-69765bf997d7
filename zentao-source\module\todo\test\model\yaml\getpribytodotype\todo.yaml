title: Get pri for todoModel::getPriByTodoType unit test case.
desc: Get pri for todoModel::getPriByTodoType unit test case.
author: xushenjie
version: 1.0.0

fields:
  - field: account
    range: 'admin'
  - field: name
    range: 1-10
    prefix: '待办'
  - field: date
    range: 1-9
    prefix: '2023-05-0'
  - field: type
    range: 'custom'
  - field: begin
    range: 1000-1100
  - field: end
    range: 1200-1300
  - field: assignedTo
    range: 'admin'
  - field: desc
    range: 1-100
    prefix: '待办描述'
  - field: status
    range: 'wait,doing,doing,doing,wait'
  - field: objectID
    range: 0
  - field: assignedBy
    range: 'admin'
  - field: type
    range: 'custom,bug,task,story,testtask'
  - field: objectID
    range: 1-10
