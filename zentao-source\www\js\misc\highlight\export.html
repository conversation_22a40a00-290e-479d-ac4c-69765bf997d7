<!-- saved from url=(0013)about:internet -->
<!-- ^^^ This is for IE not to show security warning for local files,
see http://www.microsoft.com/technet/prodtechnol/winxppro/maintain/sp2brows.mspx-->

<!--
Highlighted code export
Copyright (c) <PERSON> <<EMAIL>>
-->

<html>
    <head>
        <title>Highlited code export</title>
        <link rel="stylesheet" href="styles/default.css">
        <meta charset="utf-8">
        <style type="text/css">
            #t1, #t2 { width: 100%;}
            tr { vertical-align: top; }
            address { margin-top: 4em; }
        </style>
        <script src="highlight.pack.js"></script>
        <script>hljs.initHighlightingOnLoad();</script>
    </head>
    <body>
        <script type="text/javascript">
            String.prototype.escape = function() {
                return this.replace(/&/gm, '&amp;').replace(/</gm, '&lt;').replace(/>/gm, '&gt;');
            }

            function doIt() {
                var viewDiv = document.getElementById("highlight-view");
                var t1 = document.getElementById("t1");
                var t2 = document.getElementById("t2");
                var selector = document.getElementById("langSelector");
                var selectedLang = selector.options[selector.selectedIndex].value.toLowerCase();
                if(selectedLang) {
                    viewDiv.innerHTML = '<pre><code class="'+selectedLang+'">'+t1.value.escape()+"</code></pre>";
                } else { // try auto
                    viewDiv.innerHTML = '<pre><code>' + t1.value.escape() + "</code></pre>";
                }
                hljs.highlightBlock(viewDiv.firstChild.firstChild);
                t2.value = viewDiv.innerHTML;
            }

            function copyToBuffer(textToCopy) {
                if (window.clipboardData) { // IE
                    window.clipboardData.setData("Text", textToCopy);
                } else if (window.netscape) { // FF
                    // from http://developer.mozilla.org/en/docs/Using_the_Clipboard
                    netscape.security.PrivilegeManager.enablePrivilege('UniversalXPConnect');
                    var gClipboardHelper = Components.classes["@mozilla.org/widget/clipboardhelper;1"].getService(Components.interfaces.nsIClipboardHelper);
                    gClipboardHelper.copyString(textToCopy);
                }
            }
        </script>
        <script type="text/javascript">
            var langSelectorHtml = '<label>Language <select id="langSelector">';
            langSelectorHtml += '<option value="">Auto</option>';
            for (var i in hljs.LANGUAGES) {
                if (hljs.LANGUAGES.hasOwnProperty(i))
                    langSelectorHtml += '<option value=\"'+i+'\">'+i.charAt(0).toUpperCase()+i.substr(1)+'</option>';
            }
            langSelectorHtml += '</select></label>';
            document.write(langSelectorHtml);
        </script>
        <table width="100%">
            <tr>
                <td><textarea rows="20" cols="50" id="t1"></textarea></td>
                <td><textarea rows="20" cols="50" id="t2"></textarea></td>
            </tr>
            <tr>
                <td>Write a code snippet</td>
                <td>Get HTML to paste anywhere (for actual styles and colors see sample.css)</td>
            </tr>
        </table>
        <table width="98%">
            <tr>
                <td><input type="button" value="Export &rarr;" onclick="doIt()"/></td>
                <td align="right"><input type="button" value="Copy to buffer" onclick="copyToBuffer(document.getElementById('t2').value);"/></td>
            </tr>
        </table>
        <div id="highlight-view"></div>
        <address>
          Export script: <a href="mailto:<EMAIL>">Vladimir Gubarkov</a><br>
          Highlighting: <a href="http://softwaremaniacs.org/soft/highlight/">highlight.js</a>
        </address>
    </body>
</html>
