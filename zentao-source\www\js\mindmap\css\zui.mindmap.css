/*!
 * ZUI: 思维导图 - v1.10.0 - 2022-05-20
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2022 cnezsoft.com; Licensed MIT
 */

.mindmap 
  {
  position: relative;
  z-index: 0;
  overflow: hidden;
  }
.mindmap-container 
  {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background: #fff;
  border: 1px solid #e5e5e5;
  }
.mindmap-container.dragging 
  {
  cursor: pointer;
  }
.mindmap-bg 
  {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5;
  max-width: inherit;
  }
.mindmap-desktop 
  {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  height: 100%;
  overflow: hidden;
  -webkit-user-select: none;
     -moz-user-select: none;
  }
.mindmap-node 
  {
  position: absolute;
  border-radius: 24px;
  }
.mindmap-node > .wrapper 
  {
  min-height: 28px;
  background: #ebf2f9;
  border: 4px solid #aaa;
  border-radius: 22px;
  -webkit-transition: all .2s cubic-bezier(.175, .885, .32, 1);
       -o-transition: all .2s cubic-bezier(.175, .885, .32, 1);
          transition: all .2s cubic-bezier(.175, .885, .32, 1);
  }
.mindmap-node > .wrapper > .text 
  {
  padding: 8px 12px;
  font-size: 14px;
  font-weight: bold;
  cursor: default;
  }
.mindmap-node > .wrapper > .text * 
  {
  display: inline;
  }
.mindmap-node > .wrapper > .text br 
  {
  display: none;
  }
.mindmap-node > .wrapper > .text:focus 
  {
  outline: none;
  }
.mindmap-node > .wrapper > .text:empty:not(:focus) 
  {
  position: relative;
  top: 7px;
  min-width: 10px;
  min-height: 10px;
  background: #508dee;
  border-radius: 50%;
  }
.mindmap-node > .wrapper > .caption 
  {
  display: none;
  }
.mindmap-node:hover 
  {
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .4);
          box-shadow: 0 0 5px rgba(0, 0, 0, .4);
  }
.mindmap-node:hover > .wrapper 
  {
  border-color: #999;
  }
.mindmap-node[data-type="root"] > .wrapper 
  {
  min-width: 45px;
  min-height: 45px;
  }
.mindmap-node[data-type="root"] > .wrapper > .text 
  {
  text-align: center;
  }
.mindmap-node[data-type="root"] > .wrapper > .text:empty:not(:focus) 
  {
  top: 0;
  min-width: 37px;
  min-height: 37px;
  background: none;
  border-radius: 50%;
  }
.mindmap-node[data-type="sub"] 
  {
  border-radius: 6px;
  }
.mindmap-node[data-type="sub"] > .wrapper 
  {
  background: #fff;
  border-width: 2px;
  border-radius: 5px;
  }
.mindmap-node[data-type="sub"] > .wrapper > .text 
  {
  padding: 3px 5px;
  font-size: 13px;
  font-weight: normal;
  }
.mindmap-node[data-type="sub"] > .wrapper > .text:empty:not(:focus) 
  {
  background: none;
  }
.mindmap-node[data-type="node"] 
  {
  border-radius: 4px;
  }
.mindmap-node[data-type="node"] > .wrapper 
  {
  background: transparent;
  border: 2px solid transparent;
  border-radius: 3px;
  }
.mindmap-node[data-type="node"] > .wrapper > .text 
  {
  padding: 3px 3px;
  font-size: 13px;
  font-weight: normal;
  }
.mindmap-node[data-type="node"]:hover > .wrapper 
  {
  color: #353535;
  background: #fff;
  border-color: #999;
  }
.mindmap-node.active,
.mindmap-node.active:hover,
.mindmap-node.drag-shadow 
  {
  z-index: 900;
  -webkit-box-shadow: none;
          box-shadow: none;
  }
.mindmap-node.active > .wrapper,
.mindmap-node.active:hover > .wrapper,
.mindmap-node.drag-shadow > .wrapper 
  {
  color: #fff;
  background: #508dee;
  border-color: #2a74ea;
  }
.mindmap-node.focus,
.mindmap-node.focus:hover,
.mindmap-node.drop-to 
  {
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(20, 92, 205, .6);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(20, 92, 205, .6);
  }
.mindmap-node.focus > .wrapper,
.mindmap-node.focus:hover > .wrapper,
.mindmap-node.drop-to > .wrapper 
  {
  color: #353535;
  background: #fff;
  border-color: #145ccd;
  }
.mindmap-node.focus > .wrapper > .text,
.mindmap-node.focus:hover > .wrapper > .text,
.mindmap-node.drop-to > .wrapper > .text 
  {
  cursor: text;
  }
.mindmap-node .btn-toggle 
  {
  position: absolute;
  top: 50%;
  right: -14px;
  display: none;
  width: 16px;
  height: 16px;
  margin-top: -9px;
  font-size: 16px;
  line-height: 12px;
  color: #fff;
  text-align: center;
  cursor: pointer;
  visibility: hidden;
  background: #808080;
  border: 1px solid transparent;
  border-radius: 50%;
  opacity: 0;
  -webkit-transition-duration: .2s;
       -o-transition-duration: .2s;
          transition-duration: .2s;
  -webkit-transition-property: visibility, opacity, border-color, -webkit-box-shadow;
       -o-transition-property: visibility, opacity, border-color, box-shadow;
          transition-property: visibility, opacity, border-color, -webkit-box-shadow;
          transition-property: visibility, opacity, border-color, box-shadow;
          transition-property: visibility, opacity, border-color, box-shadow, -webkit-box-shadow;
  }
.mindmap-node .btn-toggle:hover 
  {
  background: #3280fc;
  border-color: rgba(0, 0, 0, .1);
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .4);
          box-shadow: 0 0 5px rgba(0, 0, 0, .4);
  }
.mindmap-node .btn-toggle:before 
  {
  content: '-';
  }
.mindmap-node.mindmap-side-left .btn-toggle 
  {
  right: auto;
  left: -14px;
  }
.mindmap-node.mindmap-collapesed .btn-toggle:before 
  {
  content: '+';
  }
.mindmap-node[data-type="root"] .btn-toggle 
  {
  display: none!important;
  }
.mindmap-show-toggle-btn .mindmap-has-child .btn-toggle 
  {
  display: block;
  }
.mindmap-show-toggle-btn .mindmap-node.focus .btn-toggle 
  {
  display: none;
  }
.mindmap-show-toggle-btn .mindmap-node.mindmap-collapesed .btn-toggle,
.mindmap-show-toggle-btn .mindmap-node:hover .btn-toggle 
  {
  visibility: visible;
  opacity: 1;
  }
.mindmap-shadow 
  {
  position: absolute;
  z-index: 1000;
  width: 100%;
  height: 100%;
  -webkit-transition: all .4s cubic-bezier(.175, .885, .32, 1);
       -o-transition: all .4s cubic-bezier(.175, .885, .32, 1);
          transition: all .4s cubic-bezier(.175, .885, .32, 1);
  }
.mindmap-shadow.shadow-left,
.mindmap-shadow.shadow-right 
  {
  top: 0;
  width: 20px;
  }
.mindmap-shadow.shadow-left 
  {
  left: -20px;
  }
.mindmap-shadow.shadow-right 
  {
  right: -20px;
  }
.mindmap-shadow.shadow-top,
.mindmap-shadow.shadow-bottom 
  {
  left: 0;
  height: 20px;
  }
.mindmap-shadow.shadow-top 
  {
  top: -20px;
  }
.mindmap-shadow.shadow-bottom 
  {
  bottom: -20px;
  }
.shadow-left > .shadow-left,
.shadow-right > .shadow-right,
.shadow-top > .shadow-top,
.shadow-bottom > .shadow-bottom 
  {
  -webkit-box-shadow: 0 0 8px rgba(0, 0, 0, .8);
          box-shadow: 0 0 8px rgba(0, 0, 0, .8);
  }
