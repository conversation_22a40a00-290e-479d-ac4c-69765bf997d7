<?php
/* Every version of new privilege. */
$lang->changelog['1.0.1'][] = 'project-computeBurn';

$lang->changelog['1.1'][]   = 'search-saveQuery';
$lang->changelog['1.1'][]   = 'search-deleteQuery';

$lang->changelog['1.2'][]   = 'product-doc';
$lang->changelog['1.2'][]   = 'project-doc';
$lang->changelog['1.2'][]   = 'bug-saveTemplate';
$lang->changelog['1.2'][]   = 'bug-deleteTemplate';
$lang->changelog['1.2'][]   = 'doc-index';
$lang->changelog['1.2'][]   = 'doc-browse';
$lang->changelog['1.2'][]   = 'doc-createLib';
$lang->changelog['1.2'][]   = 'doc-editLib';
$lang->changelog['1.2'][]   = 'doc-deleteLib';
$lang->changelog['1.2'][]   = 'doc-create';
$lang->changelog['1.2'][]   = 'doc-view';
$lang->changelog['1.2'][]   = 'doc-edit';
$lang->changelog['1.2'][]   = 'doc-delete';
$lang->changelog['1.2'][]   = 'doc-deleteFile';

$lang->changelog['1.3'][]   = 'task-start';
$lang->changelog['1.3'][]   = 'task-complete';
$lang->changelog['1.3'][]   = 'task-cancel';
$lang->changelog['1.3'][]   = 'file-delete';

$lang->changelog['1.4'][]   = 'my-testTask';
$lang->changelog['1.4'][]   = 'my-testCase';
$lang->changelog['1.4'][]   = 'task-finish';
$lang->changelog['1.4'][]   = 'task-close';
$lang->changelog['1.4'][]   = 'task-activate';
$lang->changelog['1.4'][]   = 'search-select';

$lang->changelog['1.5'][]   = 'task-batchClose';

$lang->changelog['2.0'][]   = 'my-dynamic';
$lang->changelog['2.0'][]   = 'bug-export';
$lang->changelog['2.0'][]   = 'story-export';
$lang->changelog['2.0'][]   = 'story-reportChart';
$lang->changelog['2.0'][]   = 'task-export';
$lang->changelog['2.0'][]   = 'task-reportChart';
$lang->changelog['2.0'][]   = 'taskcase-export';
$lang->changelog['2.0'][]   = 'company-dynamic';
$lang->changelog['2.0'][]   = 'user-dynamic';
$lang->changelog['2.0'][]   = 'extension-browse';
$lang->changelog['2.0'][]   = 'extension-obtain';
$lang->changelog['2.0'][]   = 'extension-install';
$lang->changelog['2.0'][]   = 'extension-uninstall';
$lang->changelog['2.0'][]   = 'extension-activate';
$lang->changelog['2.0'][]   = 'extension-deactivate';
$lang->changelog['2.0'][]   = 'extension-upload';
$lang->changelog['2.0'][]   = 'extension-erase';

$lang->changelog['2.1'][]   = 'extension-upgrade';

$lang->changelog['2.2'][]   = 'file-edit';

$lang->changelog['2.3'][]   = 'product-dynamic';
$lang->changelog['2.3'][]   = 'project-dynamic';
$lang->changelog['2.3'][]   = 'project-importBug';
$lang->changelog['2.3'][]   = 'story-batchCreate';
$lang->changelog['2.3'][]   = 'task-batchCreate';
$lang->changelog['2.3'][]   = 'testcase-batchCreate';
$lang->changelog['2.3'][]   = 'bug-confirmBug';
$lang->changelog['2.3'][]   = 'svn-diff';
$lang->changelog['2.3'][]   = 'svn-cat';
$lang->changelog['2.3'][]   = 'svn-apiSync';

$lang->changelog['2.4'][]   = 'task-assign';
$lang->changelog['2.4'][]   = 'project-testtask';
$lang->changelog['2.4'][]   = 'todo-export';
$lang->changelog['2.4'][]   = 'product-project';

$lang->changelog['3.0.beta2'][] = 'extension-structure';
$lang->changelog['3.0.beta2'][] = 'product-order';
$lang->changelog['3.0.beta2'][] = 'project-order';

$lang->changelog['3.1'][] = 'todo-batchCreate';

$lang->changelog['3.2'][] = 'my-changePassword';
$lang->changelog['3.2'][] = 'story-batchClose';
$lang->changelog['3.2'][] = 'task-batchEdit';
$lang->changelog['3.2'][] = 'release-export';
$lang->changelog['3.2'][] = 'report-index';
$lang->changelog['3.2'][] = 'report-projectDeviation';
$lang->changelog['3.2'][] = 'report-productSummary';
$lang->changelog['3.2'][] = 'report-bugCreate';
$lang->changelog['3.2'][] = 'report-workload';
$lang->changelog['3.2'][] = 'tree-fix';

$lang->changelog['3.3'][] = 'report-bugAssign';

$lang->changelog['4.0.beta1'][] = 'user-batchCreate';
$lang->changelog['4.0.beta1'][] = 'user-unlock';
$lang->changelog['4.0.beta1'][] = 'admin-checkDB';

$lang->changelog['4.0.beta2'][] = 'todo-batchEdit';
$lang->changelog['4.0.beta2'][] = 'story-batchEdit';
$lang->changelog['4.0.beta2'][] = 'bug-batchEdit';
$lang->changelog['4.0.beta2'][] = 'testcase-batchEdit';
$lang->changelog['4.0.beta2'][] = 'testtask-batchRun';
$lang->changelog['4.0.beta2'][] = 'user-batchEdit';
$lang->changelog['4.0.beta2'][] = 'user-manageContacts';
$lang->changelog['4.0.beta2'][] = 'user-deleteContacts';

$lang->changelog['4.0'][] = 'todo-finish';
$lang->changelog['4.0'][] = 'product-close';
$lang->changelog['4.0'][] = 'project-start';
$lang->changelog['4.0'][] = 'project-activate';
$lang->changelog['4.0'][] = 'project-putoff';
$lang->changelog['4.0'][] = 'project-suspend';
$lang->changelog['4.0'][] = 'project-close';
$lang->changelog['4.0'][] = 'task-record';
$lang->changelog['4.0'][] = 'testtask-start';
$lang->changelog['4.0'][] = 'testtask-close';
$lang->changelog['4.0'][] = 'action-hideOne';
$lang->changelog['4.0'][] = 'action-hideAll';
$lang->changelog['4.0'][] = 'task-editEstimate';
$lang->changelog['4.0'][] = 'task-deleteEstimate';

$lang->changelog['4.1'][] = 'todo-batchFinish';
$lang->changelog['4.1'][] = 'productplan-batchUnlinkStory';
$lang->changelog['4.1'][] = 'company-view';
$lang->changelog['4.1'][] = 'user-story';
$lang->changelog['4.1'][] = 'user-testTask';
$lang->changelog['4.1'][] = 'user-testCase';

$lang->changelog['4.2.beta'][] = 'tree-browseTask';

$lang->changelog['4.3.beta'][] = 'product-batchEdit';
$lang->changelog['4.3.beta'][] = 'project-batchEdit';
$lang->changelog['4.3.beta'][] = 'story-batchReview';
$lang->changelog['4.3.beta'][] = 'story-batchChangePlan';
$lang->changelog['4.3.beta'][] = 'story-batchChangeStage';
$lang->changelog['4.3.beta'][] = 'productplan-linkBug';
$lang->changelog['4.3.beta'][] = 'productplan-unlinkBug';
$lang->changelog['4.3.beta'][] = 'productplan-batchUnlinkBug';
$lang->changelog['4.3.beta'][] = 'bug-batchCreate';
$lang->changelog['4.3.beta'][] = 'testcase-exportTemplate';
$lang->changelog['4.3.beta'][] = 'testcase-import';
$lang->changelog['4.3.beta'][] = 'testcase-showImport';
$lang->changelog['4.3.beta'][] = 'testcase-confirmChange';
$lang->changelog['4.3.beta'][] = 'mail-reset';
$lang->changelog['4.3.beta'][] = 'api-debug';
$lang->changelog['4.3.beta'][] = 'action-editComment';

$lang->changelog['5.0.beta1'][] = 'bug-batchConfirm';
$lang->changelog['5.0.beta1'][] = 'bug-batchResolve';
$lang->changelog['5.0.beta1'][] = 'custom-index';
$lang->changelog['5.0.beta1'][] = 'custom-set';
$lang->changelog['5.0.beta1'][] = 'custom-restore';

$lang->changelog['5.0.beta2'][] = 'git-diff';
$lang->changelog['5.0.beta2'][] = 'git-cat';
$lang->changelog['5.0.beta2'][] = 'git-apiSync';

$lang->changelog['5.3'][] = 'bug-batchClose';

$lang->changelog['6.1'][] = 'story-zeroCase';
$lang->changelog['6.1'][] = 'testcase-groupCase';
$lang->changelog['6.1'][] = 'testtask-groupCase';

$lang->changelog['6.2'][] = 'task-pause';
$lang->changelog['6.2'][] = 'task-restart';
$lang->changelog['6.2'][] = 'testcase-createBug';

$lang->changelog['6.3'][] = 'bug-batchAssignTo';
$lang->changelog['6.3'][] = 'task-batchAssignTo';
$lang->changelog['6.3'][] = 'file-uploadImages';
$lang->changelog['6.3'][] = 'project-batchUnlinkStory';

$lang->changelog['6.4'][] = 'api-sql';
$lang->changelog['6.4'][] = 'backup-index';
$lang->changelog['6.4'][] = 'backup-backup';
$lang->changelog['6.4'][] = 'backup-restore';
$lang->changelog['6.4'][] = 'backup-delete';
$lang->changelog['6.4'][] = 'build-linkStory';
$lang->changelog['6.4'][] = 'build-unlinkStory';
$lang->changelog['6.4'][] = 'build-batchUnlinkStory';
$lang->changelog['6.4'][] = 'build-linkBug';
$lang->changelog['6.4'][] = 'build-unlinkBug';
$lang->changelog['6.4'][] = 'build-batchUnlinkBug';
$lang->changelog['6.4'][] = 'dept-edit';
$lang->changelog['6.4'][] = 'release-linkStory';
$lang->changelog['6.4'][] = 'release-unlinkStory';
$lang->changelog['6.4'][] = 'release-batchUnlinkStory';
$lang->changelog['6.4'][] = 'release-linkBug';
$lang->changelog['6.4'][] = 'release-unlinkBug';
$lang->changelog['6.4'][] = 'release-batchUnlinkBug';
$lang->changelog['6.4'][] = 'story-batchAssignTo';

$lang->changelog['7.1'][] = 'cron-index';
$lang->changelog['7.1'][] = 'cron-turnon';
$lang->changelog['7.1'][] = 'cron-create';
$lang->changelog['7.1'][] = 'cron-edit';
$lang->changelog['7.1'][] = 'cron-toggle';
$lang->changelog['7.1'][] = 'cron-delete';
$lang->changelog['7.1'][] = 'mail-browse';
$lang->changelog['7.1'][] = 'mail-delete';
$lang->changelog['7.1'][] = 'mail-batchDelete';
$lang->changelog['7.1'][] = 'dev-api';
$lang->changelog['7.1'][] = 'dev-db';

$lang->changelog['7.2'][] = 'admin-safeIndex';
$lang->changelog['7.2'][] = 'admin-checkWeak';
$lang->changelog['7.2'][] = 'backup-change';
$lang->changelog['7.2'][] = 'custom-flow';
$lang->changelog['7.2'][] = 'group-manageView';
$lang->changelog['7.2'][] = 'product-updateOrder';
$lang->changelog['7.2'][] = 'project-updateOrder';

$lang->changelog['7.3'][] = 'project-fixFirst';
$lang->changelog['7.3'][] = 'productplan-batchEdit';
$lang->changelog['7.3'][] = 'admin-sso';
$lang->changelog['7.3'][] = 'cron-openProcess';
$lang->changelog['7.3'][] = 'mail-sendCloud';
$lang->changelog['7.3'][] = 'mail-sendcloudUser';

$lang->changelog['7.4.beta'][] = 'release-changeStatus';
$lang->changelog['7.4.beta'][] = 'user-unbind';
$lang->changelog['7.4.beta'][] = 'branch-manage';
$lang->changelog['7.4.beta'][] = 'branch-delete';
$lang->changelog['7.4.beta'][] = 'my-unbind';

$lang->changelog['8.0'][] = 'story-batchChangeBranch';

$lang->changelog['8.0.1'][] = 'bug-linkBugs';
$lang->changelog['8.0.1'][] = 'story-linkStory';
$lang->changelog['8.0.1'][] = 'testcase-linkCases';

$lang->changelog['8.1.3'][] = 'story-batchChangeModule';
$lang->changelog['8.1.3'][] = 'task-batchChangeModule';
$lang->changelog['8.1.3'][] = 'bug-batchChangeModule';
$lang->changelog['8.1.3'][] = 'testcase-batchChangeModule';
$lang->changelog['8.1.3'][] = 'my-manageContacts';
$lang->changelog['8.1.3'][] = 'my-deleteContacts';

$lang->changelog['8.2.beta'][] = 'product-all';
$lang->changelog['8.2.beta'][] = 'project-tree';
$lang->changelog['8.2.beta'][] = 'project-all';
$lang->changelog['8.2.beta'][] = 'project-kanban';
$lang->changelog['8.2.beta'][] = 'project-tree';

$lang->changelog['8.3'][] = 'doc-allLibs';
$lang->changelog['8.3'][] = 'doc-objectLibs';
$lang->changelog['8.3'][] = 'doc-showFiles';

$lang->changelog['8.4'][] = 'branch-sort';
$lang->changelog['8.4'][] = 'story-bugs';
$lang->changelog['8.4'][] = 'story-cases';

$lang->changelog['9.0'][] = 'testcase-bugs';
$lang->changelog['9.0'][] = 'mail-resend';

$lang->changelog['9.1'][] = 'testcase-review';
$lang->changelog['9.1'][] = 'testcase-batchReview';
$lang->changelog['9.1'][] = 'testcase-importFromLib';
$lang->changelog['9.1'][] = 'testcase-batchCaseTypeChange';
$lang->changelog['9.1'][] = 'testcase-batchConfirmStoryChange';
$lang->changelog['9.1'][] = 'testreport-browse';
$lang->changelog['9.1'][] = 'testreport-create';
$lang->changelog['9.1'][] = 'testreport-view';
$lang->changelog['9.1'][] = 'testreport-delete';
$lang->changelog['9.1'][] = 'testreport-edit';
$lang->changelog['9.1'][] = 'testsuite-index';
$lang->changelog['9.1'][] = 'testsuite-browse';
$lang->changelog['9.1'][] = 'testsuite-create';
$lang->changelog['9.1'][] = 'testsuite-view';
$lang->changelog['9.1'][] = 'testsuite-edit';
$lang->changelog['9.1'][] = 'testsuite-delete';
$lang->changelog['9.1'][] = 'testsuite-linkCase';
$lang->changelog['9.1'][] = 'testsuite-unlinkCase';
$lang->changelog['9.1'][] = 'testsuite-batchUnlinkCases';
$lang->changelog['9.1'][] = 'testsuite-library';
$lang->changelog['9.1'][] = 'testsuite-createLib';
$lang->changelog['9.1'][] = 'testsuite-createCase';
$lang->changelog['9.1'][] = 'testsuite-libView';
$lang->changelog['9.1'][] = 'caselib-library';
$lang->changelog['9.1'][] = 'caselib-createLib';
$lang->changelog['9.1'][] = 'caselib-edit';
$lang->changelog['9.1'][] = 'caselib-createCase';
$lang->changelog['9.1'][] = 'caselib-libView';
$lang->changelog['9.1'][] = 'testtask-activate';
$lang->changelog['9.1'][] = 'testtask-block';
$lang->changelog['9.1'][] = 'testtask-report';

$lang->changelog['9.2'][] = 'custom-working';
$lang->changelog['9.2'][] = 'doc-sort';
$lang->changelog['9.2'][] = 'product-build';
$lang->changelog['9.2'][] = 'testsuite-batchCreateCase';
$lang->changelog['9.2'][] = 'testsuite-exportTemplate';
$lang->changelog['9.2'][] = 'testsuite-import';
$lang->changelog['9.2'][] = 'testsuite-showImport';
$lang->changelog['9.5'][] = 'bug-batchActivate';

$lang->changelog['9.6'][] = 'custom-setPublic';
$lang->changelog['9.6'][] = 'datatable-setGlobal';
$lang->changelog['9.6'][] = 'product-export';
$lang->changelog['9.6'][] = 'project-export';
$lang->changelog['9.6'][] = 'project-storyKanban';
$lang->changelog['9.6'][] = 'project-storySort';

$lang->changelog['9.8'][] = 'message-index';
$lang->changelog['9.8'][] = 'message-setting';
$lang->changelog['9.8'][] = 'todo-createCycle';
$lang->changelog['9.8'][] = 'project-importPlanStories';
$lang->changelog['9.8'][] = 'todo-assignTo';
$lang->changelog['9.8'][] = 'todo-activate';
$lang->changelog['9.8'][] = 'todo-close';

$lang->changelog['10.0.alpha'][] = 'my-calendar';
$lang->changelog['10.0.alpha'][] = 'doc-collect';

$lang->changelog['10.1'][] = 'todo-batchClose';
$lang->changelog['10.1'][] = 'project-treeTask';
$lang->changelog['10.1'][] = 'project-treeStory';

$lang->changelog['10.6'][] = 'backup-setting';
$lang->changelog['10.6'][] = 'backup-rmPHPHeader';

$lang->changelog['11.6.2'][] = 'message-browser';

$lang->changelog['12.3'][] = 'testtask-browseUnits';
$lang->changelog['12.3'][] = 'testtask-unitCases';
$lang->changelog['12.3'][] = 'testtask-importUnitResult';
$lang->changelog['12.3'][] = 'job-view';
$lang->changelog['12.3'][] = 'ci-commitResult';

$lang->changelog['12.5'][] = 'story-batchToTask';
$lang->changelog['12.5'][] = 'custom-product';
$lang->changelog['12.5'][] = 'custom-project';

$lang->changelog['15.0.rc1'][] = 'program-browse';
$lang->changelog['15.0.rc1'][] = 'program-view';
$lang->changelog['15.0.rc1'][] = 'program-product';
$lang->changelog['15.0.rc1'][] = 'program-create';
$lang->changelog['15.0.rc1'][] = 'program-edit';
$lang->changelog['15.0.rc1'][] = 'program-start';
$lang->changelog['15.0.rc1'][] = 'program-suspend';
$lang->changelog['15.0.rc1'][] = 'program-activate';
$lang->changelog['15.0.rc1'][] = 'program-close';
$lang->changelog['15.0.rc1'][] = 'program-delete';
$lang->changelog['15.0.rc1'][] = 'program-project';
$lang->changelog['15.0.rc1'][] = 'program-stakeholder';
$lang->changelog['15.0.rc1'][] = 'program-createStakeholder';
$lang->changelog['15.0.rc1'][] = 'program-unlinkStakeholder';
$lang->changelog['15.0.rc1'][] = 'program-batchUnlinkStakeholders';
$lang->changelog['15.0.rc1'][] = 'program-unbindWhitelist';
$lang->changelog['15.0.rc1'][] = 'program-export';
$lang->changelog['15.0.rc1'][] = 'project-execution';
$lang->changelog['15.0.rc1'][] = 'project-group';
$lang->changelog['15.0.rc1'][] = 'project-createGroup';
$lang->changelog['15.0.rc1'][] = 'project-manageView';
$lang->changelog['15.0.rc1'][] = 'project-managePriv';
$lang->changelog['15.0.rc1'][] = 'project-manageGroupMember';
$lang->changelog['15.0.rc1'][] = 'project-copyGroup';
$lang->changelog['15.0.rc1'][] = 'project-editGroup';
$lang->changelog['15.0.rc1'][] = 'project-whitelist';
$lang->changelog['15.0.rc1'][] = 'project-addWhitelist';
$lang->changelog['15.0.rc1'][] = 'project-unbindWhitelist';
$lang->changelog['15.0.rc1'][] = 'project-qa';
$lang->changelog['15.0.rc1'][] = 'project-createGuide';
$lang->changelog['15.0.rc1'][] = 'project-moduleOpen';
$lang->changelog['15.0.rc1'][] = 'personnel-accessible';
$lang->changelog['15.0.rc1'][] = 'personnel-invest';
$lang->changelog['15.0.rc1'][] = 'personnel-whitelist';
$lang->changelog['15.0.rc1'][] = 'personnel-addWhitelist';
$lang->changelog['15.0.rc1'][] = 'personnel-unbindWhitelist';
$lang->changelog['15.0.rc1'][] = 'my-workAction';
$lang->changelog['15.0.rc1'][] = 'my-contributeAction';
$lang->changelog['15.0.rc1'][] = 'my-testtask';
$lang->changelog['15.0.rc1'][] = 'my-execution';
$lang->changelog['15.0.rc1'][] = 'my-doc';
$lang->changelog['15.0.rc1'][] = 'my-calendarAction';
$lang->changelog['15.0.rc1'][] = 'story-trackAB';
$lang->changelog['15.0.rc1'][] = 'story-processStoryChange';
$lang->changelog['15.0.rc1'][] = 'projectstory-story';
$lang->changelog['15.0.rc1'][] = 'projectstory-trackAction';
$lang->changelog['15.0.rc1'][] = 'projectstory-view';
$lang->changelog['15.0.rc1'][] = 'projectstory-linkStory';
$lang->changelog['15.0.rc1'][] = 'projectstory-unlinkStory';
$lang->changelog['15.0.rc1'][] = 'projectstory-importplanstories';
$lang->changelog['15.0.rc1'][] = 'execution-whitelist';
$lang->changelog['15.0.rc1'][] = 'execution-addWhitelist';
$lang->changelog['15.0.rc1'][] = 'execution-unbindWhitelist';
$lang->changelog['15.0.rc1'][] = 'automation-browse';
$lang->changelog['15.0.rc1'][] = 'group-manageProjectAdmin';
$lang->changelog['15.0.rc1'][] = 'user-execution';
$lang->changelog['15.0.rc1'][] = 'custom-setStoryConcept';
$lang->changelog['15.0.rc1'][] = 'custom-editStoryConcept';
$lang->changelog['15.0.rc1'][] = 'custom-browseStoryConcept';
$lang->changelog['15.0.rc1'][] = 'custom-setDefaultConcept';
$lang->changelog['15.0.rc1'][] = 'custom-deleteStoryConcept';
$lang->changelog['15.0.rc1'][] = 'search-index';
$lang->changelog['15.0.rc1'][] = 'search-buildIndex';
$lang->changelog['15.0.rc1'][] = 'projectbuild-browse';
$lang->changelog['15.0.rc1'][] = 'projectrelease-browseAction';
$lang->changelog['15.0.rc1'][] = 'projectrelease-create';
$lang->changelog['15.0.rc1'][] = 'projectrelease-edit';
$lang->changelog['15.0.rc1'][] = 'projectrelease-delete';
$lang->changelog['15.0.rc1'][] = 'projectrelease-view';
$lang->changelog['15.0.rc1'][] = 'projectrelease-export';
$lang->changelog['15.0.rc1'][] = 'projectrelease-linkStory';
$lang->changelog['15.0.rc1'][] = 'projectrelease-unlinkStory';
$lang->changelog['15.0.rc1'][] = 'projectrelease-batchUnlinkStory';
$lang->changelog['15.0.rc1'][] = 'projectrelease-linkBug';
$lang->changelog['15.0.rc1'][] = 'projectrelease-unlinkBug';
$lang->changelog['15.0.rc1'][] = 'projectrelease-batchUnlinkBug';
$lang->changelog['15.0.rc1'][] = 'projectrelease-changeStatus';
$lang->changelog['15.0.rc1'][] = 'stakeholder-browse';
$lang->changelog['15.0.rc1'][] = 'stakeholder-create';
$lang->changelog['15.0.rc1'][] = 'stakeholder-batchCreate';
$lang->changelog['15.0.rc1'][] = 'stakeholder-edit';
$lang->changelog['15.0.rc1'][] = 'stakeholder-delete';
$lang->changelog['15.0.rc1'][] = 'stakeholder-viewAction';
$lang->changelog['15.0.rc1'][] = 'stakeholder-issue';
$lang->changelog['15.0.rc1'][] = 'stakeholder-viewIssueAction';
$lang->changelog['15.0.rc1'][] = 'stakeholder-communicate';
$lang->changelog['15.0.rc1'][] = 'stakeholder-expect';
$lang->changelog['15.0.rc1'][] = 'stakeholder-expectation';
$lang->changelog['15.0.rc1'][] = 'stakeholder-deleteExpect';
$lang->changelog['15.0.rc1'][] = 'stakeholder-createExpect';
$lang->changelog['15.0.rc1'][] = 'stakeholder-editExpect';
$lang->changelog['15.0.rc1'][] = 'stakeholder-viewExpect';
$lang->changelog['15.0.rc1'][] = 'stakeholder-userIssue';

$lang->changelog['15.7'][] = 'api-index';
$lang->changelog['15.7'][] = 'api-createLib';
$lang->changelog['15.7'][] = 'api-editLib';
$lang->changelog['15.7'][] = 'api-deleteLib';
$lang->changelog['15.7'][] = 'api-struct';
$lang->changelog['15.7'][] = 'api-createStruct';
$lang->changelog['15.7'][] = 'api-editStruct';
$lang->changelog['15.7'][] = 'api-deleteStruct';
$lang->changelog['15.7'][] = 'api-create';
$lang->changelog['15.7'][] = 'api-edit';
$lang->changelog['15.7'][] = 'api-delete';
$lang->changelog['15.7'][] = 'api-releases';
$lang->changelog['15.7'][] = 'api-createRelease';
$lang->changelog['15.7'][] = 'api-deleteRelease';

$lang->changelog['15.8'][] = 'branch-createAction';
$lang->changelog['15.8'][] = 'branch-editAction';
$lang->changelog['15.8'][] = 'branch-closeAction';
$lang->changelog['15.8'][] = 'branch-activateAction';
$lang->changelog['15.8'][] = 'branch-batchEdit';
$lang->changelog['15.8'][] = 'branch-setDefaultAction';

$lang->changelog['16.0.beta1'][] = 'programplan-create';
$lang->changelog['16.0.beta1'][] = 'programplan-edit';
$lang->changelog['16.0.beta1'][] = 'projectrelease-notify';
$lang->changelog['16.0.beta1'][] = 'stage-browse';
$lang->changelog['16.0.beta1'][] = 'stage-create';
$lang->changelog['16.0.beta1'][] = 'stage-batchCreate';
$lang->changelog['16.0.beta1'][] = 'stage-edit';
$lang->changelog['16.0.beta1'][] = 'stage-setType';
$lang->changelog['16.0.beta1'][] = 'stage-delete';
$lang->changelog['16.0.beta1'][] = 'release-notify';
$lang->changelog['16.0.beta1'][] = 'kanban-setLane';
$lang->changelog['16.0.beta1'][] = 'kanban-setColumn';
$lang->changelog['16.0.beta1'][] = 'kanban-setWIP';
$lang->changelog['16.0.beta1'][] = 'kanban-laneMove';
$lang->changelog['16.0.beta1'][] = 'kanban-cardsSort';
$lang->changelog['16.0.beta1'][] = 'bug-batchChangePlan';
$lang->changelog['16.0.beta1'][] = 'gitlab-browseProject';
$lang->changelog['16.0.beta1'][] = 'gitlab-createProject';
$lang->changelog['16.0.beta1'][] = 'gitlab-editProject';
$lang->changelog['16.0.beta1'][] = 'gitlab-deleteProject';
$lang->changelog['16.0.beta1'][] = 'gitlab-browseGroup';
$lang->changelog['16.0.beta1'][] = 'gitlab-createGroup';
$lang->changelog['16.0.beta1'][] = 'gitlab-editGroup';
$lang->changelog['16.0.beta1'][] = 'gitlab-deleteGroup';
$lang->changelog['16.0.beta1'][] = 'gitlab-manageGroupMembers';
$lang->changelog['16.0.beta1'][] = 'gitlab-browseUser';
$lang->changelog['16.0.beta1'][] = 'gitlab-createUser';
$lang->changelog['16.0.beta1'][] = 'gitlab-editUser';
$lang->changelog['16.0.beta1'][] = 'gitlab-deleteUser';
$lang->changelog['16.0.beta1'][] = 'gitlab-webhook';
$lang->changelog['16.0.beta1'][] = 'gitlab-createWebhook';
$lang->changelog['16.0.beta1'][] = 'gitlab-********************';
$lang->changelog['16.0.beta1'][] = 'mr-viewDiff';
$lang->changelog['16.0.beta1'][] = 'mr-linkList';
$lang->changelog['16.0.beta1'][] = 'mr-linkStory';
$lang->changelog['16.0.beta1'][] = 'mr-linkBug';
$lang->changelog['16.0.beta1'][] = 'mr-linkTask';
$lang->changelog['16.0.beta1'][] = 'mr-unlink';
$lang->changelog['16.0.beta1'][] = 'mr-approval';
$lang->changelog['16.0.beta1'][] = 'mr-close';
$lang->changelog['16.0.beta1'][] = 'mr-reopen';
$lang->changelog['16.0.beta1'][] = 'mr-addBug';
$lang->changelog['16.0.beta1'][] = 'mr-viewDiff';
$lang->changelog['16.0.beta1'][] = 'report-annualData';

$lang->changelog['16.0'][] = 'my-requirement';
$lang->changelog['16.0'][] = 'branch-mergeBranchAction';
$lang->changelog['16.0'][] = 'kanban-sortSpace';
$lang->changelog['16.0'][] = 'api-apiGetRepoByUrl';
$lang->changelog['16.0'][] = 'gitlab-createBranch';
$lang->changelog['16.0'][] = 'gitlab-browseBranch';
$lang->changelog['16.0'][] = 'gitlab-browseBranchPriv';
$lang->changelog['16.0'][] = 'gitlab-createBranchPriv';
$lang->changelog['16.0'][] = 'gitlab-editBranchPriv';
$lang->changelog['16.0'][] = 'gitlab-deleteBranchPriv';
$lang->changelog['16.0'][] = 'gitlab-browseTag';
$lang->changelog['16.0'][] = 'gitlab-createTag';
$lang->changelog['16.0'][] = 'gitlab-deleteTag';
$lang->changelog['16.0'][] = 'mr-apiCreate';
$lang->changelog['16.0'][] = 'mr-addReview';

$lang->changelog['16.1'][] = 'productplan-start';
$lang->changelog['16.1'][] = 'productplan-finish';
$lang->changelog['16.1'][] = 'productplan-close';
$lang->changelog['16.1'][] = 'productplan-activate';
$lang->changelog['16.1'][] = 'gitlab-browseTagPriv';
$lang->changelog['16.1'][] = 'gitlab-createTagPriv';
$lang->changelog['16.1'][] = 'gitlab-editTagPriv';
$lang->changelog['16.1'][] = 'gitlab-editTagPriv';
$lang->changelog['16.1'][] = 'mr-view';

$lang->changelog['16.2'][] = 'kanban-setLaneHeight';
$lang->changelog['16.2'][] = 'execution-taskKanban';
$lang->changelog['16.2'][] = 'execution-RDKanban';

$lang->changelog['16.3'][] = 'kanban-performable';
$lang->changelog['16.3'][] = 'kanban-setColumnWidth';
$lang->changelog['16.3'][] = 'kanban-batchCreateCard';
$lang->changelog['16.3'][] = 'kanban-import';
$lang->changelog['16.3'][] = 'kanban-enableArchived';
$lang->changelog['16.3'][] = 'sonarqube-browse';
$lang->changelog['16.3'][] = 'sonarqube-create';
$lang->changelog['16.3'][] = 'sonarqube-edit';
$lang->changelog['16.3'][] = 'sonarqube-delete';
$lang->changelog['16.3'][] = 'sonarqube-browseProject';
$lang->changelog['16.3'][] = 'sonarqube-deleteProject';
$lang->changelog['16.3'][] = 'sonarqube-execJob';
$lang->changelog['16.3'][] = 'sonarqube-reportView';

$lang->changelog['16.4'][] = 'sonarqube-createProject';
$lang->changelog['16.4'][] = 'sonarqube-browseIssue';

$lang->changelog['16.5.beta1'][] = 'productplan-batchChangeStatus';
$lang->changelog['16.5.beta1'][] = 'custom-required';

$lang->changelog['17.0.beta1'][] = 'doc-updateOrder';

$lang->changelog['17.0.beta2'][] = 'kanban-activateSpace';
$lang->changelog['17.0.beta2'][] = 'kanban-activate';
$lang->changelog['17.0.beta2'][] = 'custom-kanban';

$lang->changelog['17.1'][] = 'kanban-editLaneColor';
$lang->changelog['17.1'][] = 'kanban-editLaneName';
$lang->changelog['17.1'][] = 'kanban-editColumn';
$lang->changelog['17.1'][] = 'testcase-linkBugs';

$lang->changelog['17.2'][] = 'execution-storyView';
$lang->changelog['17.2'][] = 'testcase-importToLib';
$lang->changelog['17.2'][] = 'doc-sortLibs';
$lang->changelog['17.2'][] = 'admin-resetPWDSetting';

$lang->changelog['17.3'][] = 'execution-CFD';
$lang->changelog['17.3'][] = 'execution-computeCFD';
$lang->changelog['17.3'][] = 'repo-downloadCode';
$lang->changelog['17.3'][] = 'gitea-browse';
$lang->changelog['17.3'][] = 'gitea-create';
$lang->changelog['17.3'][] = 'gitea-edit';
$lang->changelog['17.3'][] = 'gitea-view';
$lang->changelog['17.3'][] = 'gitea-delete';
$lang->changelog['17.3'][] = 'app-serverLink';

$lang->changelog['17.4'][] = 'execution-setKanban';
$lang->changelog['17.4'][] = 'custom-code';
$lang->changelog['17.4'][] = 'gitlab-browseBranchPriv';
$lang->changelog['17.4'][] = 'gitlab-browseTagPriv';
$lang->changelog['17.4'][] = 'gitea-bindUser';

$lang->changelog['17.5'][] = 'story-linkStoriesAB';
$lang->changelog['17.5'][] = 'kanban-setting';
$lang->changelog['17.5'][] = 'testcase-exportTemplate';
$lang->changelog['17.5'][] = 'caselib-exportTemplate';
$lang->changelog['17.5'][] = 'admin-tableEngine';

$lang->changelog['17.6'][] = 'product-track';
$lang->changelog['17.6'][] = 'story-submitReview';
$lang->changelog['17.6'][] = 'story-recallAction';
$lang->changelog['17.6'][] = 'testcase-zeroCase';
$lang->changelog['17.6'][] = 'compile-syncCompile';
$lang->changelog['17.6'][] = 'gogs-browse';
$lang->changelog['17.6'][] = 'gogs-create';
$lang->changelog['17.6'][] = 'gogs-edit';
$lang->changelog['17.6'][] = 'gogs-view';
$lang->changelog['17.6'][] = 'gogs-delete';
$lang->changelog['17.6'][] = 'gogs-bindUser';

$lang->changelog['17.6.2'][] = 'report-viewEveryoneAnnual';

$lang->changelog['18.0.beta1'][] = 'my-audit';
$lang->changelog['18.0.beta1'][] = 'project-manageRepo';
$lang->changelog['18.0.beta1'][] = 'projectplan-browse';
$lang->changelog['18.0.beta1'][] = 'projectplan-create';
$lang->changelog['18.0.beta1'][] = 'projectplan-edit';
$lang->changelog['18.0.beta1'][] = 'projectplan-view';
$lang->changelog['18.0.beta1'][] = 'execution-treeViewTask';
$lang->changelog['18.0.beta1'][] = 'execution-treeViewStory';
$lang->changelog['18.0.beta1'][] = 'execution-kanbanAction';

$lang->changelog['18.0.beta2'][] = 'story-relievedTwins';

$lang->changelog['18.0.beta3'][] = 'screen-browse';
$lang->changelog['18.0.beta3'][] = 'screen-view';
$lang->changelog['18.0.beta3'][] = 'report-preview';

$lang->changelog['18.0'][] = 'testcase-showScript';
$lang->changelog['18.0'][] = 'testcase-automation';
$lang->changelog['18.0'][] = 'zahost-browse';
$lang->changelog['18.0'][] = 'zahost-create';
$lang->changelog['18.0'][] = 'zahost-editAction';
$lang->changelog['18.0'][] = 'zahost-deleteAction';
$lang->changelog['18.0'][] = 'zahost-view';
$lang->changelog['18.0'][] = 'zahost-browseImage';
$lang->changelog['18.0'][] = 'zahost-downloadImage';
$lang->changelog['18.0'][] = 'zahost-cancel';
$lang->changelog['18.0'][] = 'zanode-browse';
$lang->changelog['18.0'][] = 'zanode-create';
$lang->changelog['18.0'][] = 'zanode-edit';
$lang->changelog['18.0'][] = 'zanode-destroy';
$lang->changelog['18.0'][] = 'zanode-reboot';
$lang->changelog['18.0'][] = 'zanode-suspend';
$lang->changelog['18.0'][] = 'zanode-resume';
$lang->changelog['18.0'][] = 'zanode-getVNC';
$lang->changelog['18.0'][] = 'zanode-boot';
$lang->changelog['18.0'][] = 'zanode-shutdown';
$lang->changelog['18.0'][] = 'zanode-view';
$lang->changelog['18.0'][] = 'zanode-createImage';
$lang->changelog['18.0'][] = 'repo-linkStory';
$lang->changelog['18.0'][] = 'repo-linkBug';
$lang->changelog['18.0'][] = 'repo-linkTask';
$lang->changelog['18.0'][] = 'repo-unlink';

$lang->changelog['18.1'][] = 'zanode-browseSnapshot';
$lang->changelog['18.1'][] = 'zanode-createSnapshot';
$lang->changelog['18.1'][] = 'zanode-editSnapshot';
$lang->changelog['18.1'][] = 'zanode-restoreSnapshot';
$lang->changelog['18.1'][] = 'zanode-deleteSnapshot';

$lang->changelog['18.2'][] = 'projectbuild-create';
$lang->changelog['18.2'][] = 'stage-plusBrowse';
$lang->changelog['18.2'][] = 'execution-batchChangeStatus';
$lang->changelog['18.2'][] = 'custom-productName';
$lang->changelog['18.2'][] = 'custom-executionCommon';
$lang->changelog['18.2'][] = 'custom-hours';
$lang->changelog['18.2'][] = 'holiday-createAction';
$lang->changelog['18.2'][] = 'holiday-editAction';
$lang->changelog['18.2'][] = 'holiday-deleteAction';
$lang->changelog['18.2'][] = 'holiday-browse';
$lang->changelog['18.2'][] = 'holiday-importAction';

$lang->changelog['18.3'][] = 'custom-percent';
$lang->changelog['18.3'][] = 'custom-beginAndEndDate';
$lang->changelog['18.3'][] = 'editor-index';
$lang->changelog['18.3'][] = 'editor-extend';
$lang->changelog['18.3'][] = 'editor-edit';
$lang->changelog['18.3'][] = 'editor-newPage';
$lang->changelog['18.3'][] = 'editor-save';
$lang->changelog['18.3'][] = 'editor-delete';

$lang->changelog['18.4.alpha1'][] = 'doc-mySpace';
$lang->changelog['18.4.alpha1'][] = 'doc-myView';
$lang->changelog['18.4.alpha1'][] = 'doc-myCollection';
$lang->changelog['18.4.alpha1'][] = 'doc-myCreation';
$lang->changelog['18.4.alpha1'][] = 'doc-productSpace';
$lang->changelog['18.4.alpha1'][] = 'doc-projectSpace';
$lang->changelog['18.4.alpha1'][] = 'doc-teamSpace';
$lang->changelog['18.4.alpha1'][] = 'doc-addCatalog';
$lang->changelog['18.4.alpha1'][] = 'doc-editCatalog';
$lang->changelog['18.4.alpha1'][] = 'doc-deleteCatalog';
$lang->changelog['18.4.alpha1'][] = 'doc-displaySetting';
$lang->changelog['18.4.alpha1'][] = 'group-batchChangePackage';
$lang->changelog['18.4.alpha1'][] = 'screen-annualData';
$lang->changelog['18.4.alpha1'][] = 'screen-allAnnualData';
$lang->changelog['18.4.alpha1'][] = 'pivot-preview';
$lang->changelog['18.4.alpha1'][] = 'pivot-productSummary';
$lang->changelog['18.4.alpha1'][] = 'pivot-projectDeviation';
$lang->changelog['18.4.alpha1'][] = 'pivot-bugCreate';
$lang->changelog['18.4.alpha1'][] = 'pivot-bugAssign';
$lang->changelog['18.4.alpha1'][] = 'pivot-workload';
$lang->changelog['18.4.alpha1'][] = 'chart-preview';
$lang->changelog['18.4.alpha1'][] = 'api-addCatalog';
$lang->changelog['18.4.alpha1'][] = 'api-editCatalog';
$lang->changelog['18.4.alpha1'][] = 'api-deleteCatalog';

$lang->changelog['18.6'][] = 'file-preview';
$lang->changelog['18.6'][] = 'product-activate';

$lang->changelog['18.7'][] = 'ai-models';
$lang->changelog['18.7'][] = 'ai-promptPublish';
$lang->changelog['18.7'][] = 'ai-promptUnpublish';
$lang->changelog['18.7'][] = 'host-browse';
$lang->changelog['18.7'][] = 'host-create';
$lang->changelog['18.7'][] = 'host-edit';
$lang->changelog['18.7'][] = 'host-delete';
$lang->changelog['18.7'][] = 'host-view';
$lang->changelog['18.7'][] = 'host-changeStatus';
$lang->changelog['18.7'][] = 'host-treemap';
$lang->changelog['18.7'][] = 'instance-manage';
$lang->changelog['18.7'][] = 'serverroom-browse';
$lang->changelog['18.7'][] = 'serverroom-create';
$lang->changelog['18.7'][] = 'serverroom-edit';
$lang->changelog['18.7'][] = 'serverroom-delete';
$lang->changelog['18.7'][] = 'serverroom-view';
$lang->changelog['18.7'][] = 'space-browse';
$lang->changelog['18.7'][] = 'store-browse';
$lang->changelog['18.7'][] = 'store-appview';
$lang->changelog['18.7'][] = 'system-dashboard';
$lang->changelog['18.7'][] = 'system-dblist';
$lang->changelog['18.7'][] = 'system-configdomain';
$lang->changelog['18.7'][] = 'system-ossview';
$lang->changelog['18.7'][] = 'system-browse';
$lang->changelog['18.7'][] = 'system-create';
$lang->changelog['18.7'][] = 'system-edit';
$lang->changelog['18.7'][] = 'system-delete';
$lang->changelog['18.7'][] = 'system-active';
$lang->changelog['18.7'][] = 'system-inactive';

$lang->changelog['18.8'][] = 'metric-preview';
$lang->changelog['18.8'][] = 'metric-details';

$lang->changelog['18.9'][] = 'ai-chat';

$lang->changelog['18.11'][] = 'ai-publishMiniProgram';
$lang->changelog['18.11'][] = 'ai-unpublishMiniProgram';
$lang->changelog['18.11'][] = 'aiapp-square';
$lang->changelog['18.11'][] = 'aiapp-view';
$lang->changelog['18.11'][] = 'aiapp-miniProgramChat';

$lang->changelog['20.0.beta1'][] = 'program-productView';
$lang->changelog['20.0.beta1'][] = 'story-relation';
$lang->changelog['20.0.beta1'][] = 'testcase-batchChangeType';

$lang->changelog['20.0'][] = 'bug-createBranch';
$lang->changelog['20.0'][] = 'bug-unlinkBranch';
$lang->changelog['20.0'][] = 'story-createBranch';
$lang->changelog['20.0'][] = 'story-unlinkBranch';
$lang->changelog['20.0'][] = 'task-createBranch';
$lang->changelog['20.0'][] = 'task-unlinkBranch';

$lang->changelog['20.0.1'][] = 'doc-deleteFile';

$lang->changelog['20.2'][] = 'doc-deleteFile';
$lang->changelog['20.2'][] = 'epic-create';
$lang->changelog['20.2'][] = 'epic-batchCreate';
$lang->changelog['20.2'][] = 'epic-edit';
$lang->changelog['20.2'][] = 'epic-batchEdit';
$lang->changelog['20.2'][] = 'epic-linkStory';
$lang->changelog['20.2'][] = 'epic-export';
$lang->changelog['20.2'][] = 'epic-delete';
$lang->changelog['20.2'][] = 'epic-view';
$lang->changelog['20.2'][] = 'epic-change';
$lang->changelog['20.2'][] = 'epic-review';
$lang->changelog['20.2'][] = 'epic-submitReview';
$lang->changelog['20.2'][] = 'epic-batchReview';
$lang->changelog['20.2'][] = 'epic-recall';
$lang->changelog['20.2'][] = 'epic-assignTo';
$lang->changelog['20.2'][] = 'epic-close';
$lang->changelog['20.2'][] = 'epic-batchClose';
$lang->changelog['20.2'][] = 'epic-activate';
$lang->changelog['20.2'][] = 'epic-report';
$lang->changelog['20.2'][] = 'epic-batchChangeBranch';
$lang->changelog['20.2'][] = 'epic-batchAssignTo';
$lang->changelog['20.2'][] = 'epic-batchChangeModule';
$lang->changelog['20.2'][] = 'epic-batchChangeParent';
$lang->changelog['20.2'][] = 'epic-batchChangeGrade';
$lang->changelog['20.2'][] = 'epic-batchChangePlan';
$lang->changelog['20.2'][] = 'epic-processStoryChange';
$lang->changelog['20.2'][] = 'requirement-create';
$lang->changelog['20.2'][] = 'requirement-batchCreate';
$lang->changelog['20.2'][] = 'requirement-edit';
$lang->changelog['20.2'][] = 'requirement-linkStory';
$lang->changelog['20.2'][] = 'requirement-batchEdit';
$lang->changelog['20.2'][] = 'requirement-export';
$lang->changelog['20.2'][] = 'requirement-delete';
$lang->changelog['20.2'][] = 'requirement-view';
$lang->changelog['20.2'][] = 'requirement-change';
$lang->changelog['20.2'][] = 'requirement-review';
$lang->changelog['20.2'][] = 'requirement-submitReview';
$lang->changelog['20.2'][] = 'requirement-batchReview';
$lang->changelog['20.2'][] = 'requirement-recall';
$lang->changelog['20.2'][] = 'requirement-assignTo';
$lang->changelog['20.2'][] = 'requirement-close';
$lang->changelog['20.2'][] = 'requirement-batchClose';
$lang->changelog['20.2'][] = 'requirement-activate';
$lang->changelog['20.2'][] = 'requirement-report';
$lang->changelog['20.2'][] = 'requirement-batchChangeBranch';
$lang->changelog['20.2'][] = 'requirement-batchAssignTo';
$lang->changelog['20.2'][] = 'requirement-batchChangeModule';
$lang->changelog['20.2'][] = 'requirement-batchChangeParent';
$lang->changelog['20.2'][] = 'requirement-batchChangeGrade';
$lang->changelog['20.2'][] = 'requirement-batchChangePlan';
$lang->changelog['20.2'][] = 'requirement-processStoryChange';
$lang->changelog['20.2'][] = 'projectstory-report';
$lang->changelog['20.2'][] = 'projectstory-import';
$lang->changelog['20.2'][] = 'projectstory-exportTemplate';
$lang->changelog['20.2'][] = 'projectstory-export';
$lang->changelog['20.2'][] = 'projectstory-batchReview';
$lang->changelog['20.2'][] = 'projectstory-batchEdit';
$lang->changelog['20.2'][] = 'projectstory-batchClose';
$lang->changelog['20.2'][] = 'projectstory-batchChangePlan';
$lang->changelog['20.2'][] = 'projectstory-batchAssignTo';
$lang->changelog['20.2'][] = 'story-batchChangeGrade';
$lang->changelog['20.2'][] = 'story-batchChangeParent';

$lang->changelog['20.4'][] = 'projectrelease-publish';
$lang->changelog['20.4'][] = 'release-publish';
$lang->changelog['20.4'][] = 'repo-browseBranch';
$lang->changelog['20.4'][] = 'repo-browseTag';

$lang->changelog['20.5'][] = 'doc-createSpace';
$lang->changelog['20.5'][] = 'doc-moveDoc';
$lang->changelog['20.5'][] = 'doc-moveLib';
$lang->changelog['20.5'][] = 'doc-sortDoc';
$lang->changelog['20.5'][] = 'doc-sortDoclib';
$lang->changelog['20.5'][] = 'zanode-instruction';

$lang->changelog['20.7'][] = 'caselib-batchEditCase';
$lang->changelog['20.7'][] = 'caselib-editCase';
$lang->changelog['20.7'][] = 'caselib-viewCase';

$lang->changelog['20.8'][] = 'doc-batchMoveDoc';
$lang->changelog['20.8'][] = 'doc-deleteSpace';
$lang->changelog['20.8'][] = 'doc-editSpace';
$lang->changelog['20.8'][] = 'job-trigger';

$lang->changelog['21.0'][] = 'aselib-exportCase';
$lang->changelog['21.0'][] = 'doc-quick';

$lang->changelog['21.2'][] = 'cache-flush';
$lang->changelog['21.2'][] = 'cache-setting';
$lang->changelog['21.2'][] = 'design-confirmStoryChange';
$lang->changelog['21.2'][] = 'system-active';
$lang->changelog['21.2'][] = 'system-browse';
$lang->changelog['21.2'][] = 'system-create';
$lang->changelog['21.2'][] = 'system-delete';
$lang->changelog['21.2'][] = 'system-edit';
$lang->changelog['21.2'][] = 'system-inactive';

$lang->changelog['21.4'][] = 'testcase-exportFreeMind';
$lang->changelog['21.4'][] = 'testtask-assignCase';