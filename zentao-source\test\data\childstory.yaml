title: table zt_story
desc: "需求"
author: automated export
version: "1.0"
fields:
  - field: id
    range: 401-10000
  - field: parent
    note: "父需求ID"
    range: 351-400
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: product
    note: "所属产品"
    range: 88{2},89-100{4}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: branch
    note: "分支/平台"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: module
    note: "所属模块"
    range: 2171-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: plan
    note: "所属计划"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: source
    note: "需求来源"
    range: customer,user,po,market,service,operation,support,competitor,partner,dev,tester,bug,forum,other
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: sourceNote
    note: "来源备注"
    range: 1-10000
    prefix: "这里是需求来源备注"
    postfix: ""
    loop: 0
    format: ""
  - field: fromBug
    note: "来源Bug"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: title
    note: "需求名称"
    fields:
      - field: field1
        range: [用户子需求,软件子需求]
      - field: field2
        range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: keywords
    note: "关键词"
    range: 1-10000
    prefix: "关键词"
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "需求类型"
    range: requirement,story
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: pri
    note: "优先级"
    range: 1-4
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: estimate
    note: "预计工时"
    range: 0-20:2
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: "当前状态"
    range: draft,active,closed,changed
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: subStatus
    note: "子状态"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: color
    note: "标题颜色"
    from: common.color.v1.yaml
    use: color
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: stage
    note: "所处阶段"
    range: wait,planned,projected,developing,developed,testing,tested,verified,released,closed
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: stagedBy
    note: "设置阶段者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: mailto
    note: "抄送给"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: openedBy
    note: "由谁创建"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: openedDate
    note: "创建日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedTo
    note: "指派给"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedDate
    note: "指派日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: lastEditedBy
    note: "最后修改者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: lastEditedDate
    note: "最后修改日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: reviewedBy
    note: "评审者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: reviewedDate
    note: "评审日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: closedBy
    note: "关闭者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: closedDate
    note: "关闭日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: closedReason
    note: "关闭原因"
    range: done,subdivided,duplicate,postponed,willnotdo,cancel,bydesign
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: toBug
    note: "转Bug"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: linkStories
    note: "相关需求"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: duplicateStory
    note: "重复需求ID"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: version
    note: "版本号"
    range: 1{50}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: URChanged
    note: "用户需求变更"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
