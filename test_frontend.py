#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PMO系统前端功能测试脚本
使用Selenium测试前端页面功能
"""

import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class FrontendTester:
    def __init__(self):
        self.driver = None
        self.base_url = "http://localhost:3001"
        self.test_results = []
        self.failed_tests = []
        
    def setup(self):
        """初始化浏览器"""
        print("🚀 PMO系统前端功能测试开始")
        print("=" * 60)
        
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # 无头模式
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            print("✅ 浏览器初始化成功")
        except Exception as e:
            print(f"❌ 浏览器初始化失败: {e}")
            print("💡 请确保已安装Chrome浏览器和ChromeDriver")
            return False
        
        return True
    
    def cleanup(self):
        """清理资源"""
        if self.driver:
            self.driver.quit()
        
        print("\n" + "=" * 60)
        print(f"✅ 前端测试完成，共 {len(self.test_results)} 个测试")
        print(f"✅ 成功：{len([r for r in self.test_results if r['success']])} 个")
        print(f"❌ 失败：{len(self.failed_tests)} 个")
        
        if self.failed_tests:
            print("\n❌ 失败的测试：")
            for test in self.failed_tests:
                print(f"  - {test['name']}: {test['error']}")
    
    def test_page(self, name, url, check_elements=None):
        """通用页面测试方法"""
        try:
            print(f"🧪 测试 {name}...")
            
            # 访问页面
            full_url = f"{self.base_url}{url}"
            self.driver.get(full_url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 检查页面标题
            title = self.driver.title
            if not title:
                raise Exception("页面标题为空")
            
            # 检查特定元素（如果提供）
            if check_elements:
                for element_selector in check_elements:
                    try:
                        element = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, element_selector))
                        )
                        if not element:
                            raise Exception(f"未找到元素: {element_selector}")
                    except TimeoutException:
                        raise Exception(f"元素加载超时: {element_selector}")
            
            print(f"  ✅ {name} - 成功 (标题: {title})")
            self.test_results.append({
                "name": name,
                "success": True,
                "url": full_url,
                "title": title
            })
            return True
            
        except Exception as e:
            print(f"  ❌ {name} - 失败: {str(e)}")
            self.failed_tests.append({"name": name, "error": str(e)})
            self.test_results.append({
                "name": name,
                "success": False,
                "error": str(e)
            })
            return False
    
    def test_dashboard_page(self):
        """测试仪表板页面"""
        print("\n📊 测试仪表板页面")
        print("-" * 40)
        
        # 测试主页面
        self.test_page(
            "仪表板主页", 
            "/", 
            [".dashboard-container", ".chart-container"]
        )
        
        # 测试红黑榜页面
        self.test_page(
            "红黑榜页面", 
            "/redblack", 
            [".redblack-container"]
        )
    
    def test_project_pages(self):
        """测试项目管理页面"""
        print("\n📋 测试项目管理页面")
        print("-" * 40)
        
        # 测试项目列表页面
        self.test_page(
            "项目列表页面", 
            "/project-list", 
            [".project-list-container", ".el-table"]
        )
        
        # 测试项目详情页面（如果存在）
        self.test_page(
            "项目详情页面", 
            "/project-detail", 
            [".project-detail-container"]
        )
    
    def test_ai_pages(self):
        """测试AI助手页面"""
        print("\n🤖 测试AI助手页面")
        print("-" * 40)
        
        # 测试AI助手页面
        self.test_page(
            "AI助手页面", 
            "/ai-assistant", 
            [".ai-assistant-container", ".chat-container"]
        )
        
        # 测试数据库AI页面
        self.test_page(
            "数据库AI页面", 
            "/database-ai", 
            [".database-ai-container"]
        )
    
    def test_archive_pages(self):
        """测试档案管理页面"""
        print("\n📁 测试档案管理页面")
        print("-" * 40)
        
        # 测试档案管理页面
        self.test_page(
            "档案管理页面", 
            "/archive-management", 
            [".archive-management-container"]
        )
    
    def test_team_pages(self):
        """测试团队管理页面"""
        print("\n👥 测试团队管理页面")
        print("-" * 40)
        
        # 测试团队管理页面
        self.test_page(
            "团队管理页面", 
            "/team-management", 
            [".team-management-container"]
        )
    
    def test_navigation(self):
        """测试导航功能"""
        print("\n🧭 测试导航功能")
        print("-" * 40)
        
        try:
            # 访问主页
            self.driver.get(self.base_url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 检查导航菜单
            nav_items = [
                "仪表板",
                "项目管理", 
                "AI助手",
                "档案管理",
                "团队管理"
            ]
            
            for item in nav_items:
                try:
                    # 查找导航项
                    nav_element = self.driver.find_element(By.XPATH, f"//*[contains(text(), '{item}')]")
                    if nav_element:
                        print(f"  ✅ 找到导航项: {item}")
                    else:
                        print(f"  ❌ 未找到导航项: {item}")
                except NoSuchElementException:
                    print(f"  ❌ 未找到导航项: {item}")
            
            self.test_results.append({
                "name": "导航功能",
                "success": True
            })
            
        except Exception as e:
            print(f"  ❌ 导航测试失败: {str(e)}")
            self.failed_tests.append({"name": "导航功能", "error": str(e)})
    
    def run_all_tests(self):
        """运行所有前端测试"""
        if not self.setup():
            return
        
        try:
            # 测试各个页面
            self.test_dashboard_page()
            self.test_project_pages()
            self.test_ai_pages()
            self.test_archive_pages()
            self.test_team_pages()
            
            # 测试导航功能
            self.test_navigation()
            
        except Exception as e:
            print(f"❌ 前端测试过程中发生异常: {str(e)}")
        
        finally:
            self.cleanup()

def main():
    """主函数"""
    tester = FrontendTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
