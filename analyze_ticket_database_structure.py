#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析工单数据库结构脚本
只查看，不修改任何数据
"""

import pymysql
import json
from datetime import datetime

class TicketDatabaseAnalyzer:
    """工单数据库分析器 - 只读模式"""
    
    def __init__(self):
        self.connection = None
        self.config = {
            'host': '**********',
            'user': 'qyuser', 
            'password': 'C~w9d4kaWS',
            'database': 'ticket',
            'charset': 'utf8mb4',
            'autocommit': True
        }
    
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = pymysql.connect(**self.config)
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False
    
    def execute_query(self, sql, params=None):
        """执行查询 - 只读"""
        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            print(f"查询执行失败: {e}")
            return []
    
    def get_all_tables(self):
        """获取所有表名"""
        print("🔍 获取数据库中的所有表...")
        sql = "SHOW TABLES"
        tables = self.execute_query(sql)
        
        table_names = []
        for table in tables:
            table_name = list(table.values())[0]
            table_names.append(table_name)
        
        print(f"✅ 找到 {len(table_names)} 个表:")
        for i, table_name in enumerate(table_names, 1):
            print(f"   {i:2d}. {table_name}")
        
        return table_names
    
    def analyze_table_structure(self, table_name):
        """分析表结构"""
        print(f"\n📋 分析表 '{table_name}' 的结构:")
        
        # 获取表结构
        sql = f"DESCRIBE {table_name}"
        columns = self.execute_query(sql)
        
        if not columns:
            print(f"   ❌ 无法获取表 {table_name} 的结构")
            return None
        
        print(f"   📊 字段信息 ({len(columns)} 个字段):")
        for col in columns:
            field = col['Field']
            type_info = col['Type']
            null_info = col['Null']
            key_info = col['Key']
            default_info = col['Default']
            extra_info = col['Extra']
            
            key_symbol = ""
            if key_info == 'PRI':
                key_symbol = " 🔑"
            elif key_info == 'UNI':
                key_symbol = " 🔐"
            elif key_info == 'MUL':
                key_symbol = " 🔗"
            
            print(f"      {field:<30} {type_info:<20} {null_info:<5} {key_symbol}")
        
        return columns
    
    def sample_table_data(self, table_name, limit=3):
        """查看表的样本数据"""
        print(f"\n📄 查看表 '{table_name}' 的样本数据 (前{limit}条):")
        
        sql = f"SELECT * FROM {table_name} LIMIT {limit}"
        data = self.execute_query(sql)
        
        if not data:
            print(f"   ❌ 表 {table_name} 没有数据或查询失败")
            return None
        
        print(f"   📊 样本数据 ({len(data)} 条记录):")
        for i, row in enumerate(data, 1):
            print(f"      记录 {i}:")
            for key, value in row.items():
                # 限制显示长度
                if isinstance(value, str) and len(value) > 50:
                    display_value = value[:50] + "..."
                else:
                    display_value = value
                print(f"        {key:<25}: {display_value}")
            print()
        
        return data
    
    def find_content_related_tables(self):
        """查找与内容相关的表"""
        print("\n🔍 查找与工单内容、需求相关的表...")
        
        # 获取所有表名
        tables = self.get_all_tables()
        
        # 查找可能包含内容的表
        content_keywords = ['content', 'template', 'detail', 'record', 'process', 'comment', 'description', 'requirement']
        
        content_tables = []
        for table in tables:
            table_lower = table.lower()
            for keyword in content_keywords:
                if keyword in table_lower:
                    content_tables.append(table)
                    break
        
        print(f"\n📋 可能包含内容的表 ({len(content_tables)} 个):")
        for table in content_tables:
            print(f"   • {table}")
        
        return content_tables
    
    def analyze_ticket_content_sources(self):
        """分析工单内容来源"""
        print("\n🎯 深度分析工单内容来源...")
        
        # 1. 分析 feelec_ticket 表中的模板相关字段
        print("\n1️⃣ 分析工单表中的模板字段:")
        sql = """
        SELECT 
            feelec_template_id,
            feelec_template_type_id,
            COUNT(*) as count
        FROM feelec_ticket 
        WHERE feelec_delete = 20 
        GROUP BY feelec_template_id, feelec_template_type_id
        ORDER BY count DESC
        LIMIT 10
        """
        
        template_stats = self.execute_query(sql)
        if template_stats:
            print("   📊 模板使用统计:")
            for stat in template_stats:
                print(f"      模板ID: {stat['feelec_template_id']}, 类型ID: {stat['feelec_template_type_id']}, 使用次数: {stat['count']}")
        
        # 2. 查找模板表
        print("\n2️⃣ 查找模板相关表:")
        template_tables = []
        all_tables = self.get_all_tables()
        
        for table in all_tables:
            if 'template' in table.lower():
                template_tables.append(table)
                print(f"   📋 找到模板表: {table}")
                self.analyze_table_structure(table)
                self.sample_table_data(table, 2)
        
        # 3. 查找处理记录表
        print("\n3️⃣ 查找处理记录相关表:")
        process_tables = []
        
        for table in all_tables:
            table_lower = table.lower()
            if any(keyword in table_lower for keyword in ['process', 'record', 'log', 'history']):
                process_tables.append(table)
                print(f"   📋 找到处理记录表: {table}")
                self.analyze_table_structure(table)
                self.sample_table_data(table, 2)
        
        # 4. 查找评论或详情表
        print("\n4️⃣ 查找评论/详情相关表:")
        detail_tables = []
        
        for table in all_tables:
            table_lower = table.lower()
            if any(keyword in table_lower for keyword in ['comment', 'detail', 'content', 'description']):
                detail_tables.append(table)
                print(f"   📋 找到详情表: {table}")
                self.analyze_table_structure(table)
                self.sample_table_data(table, 2)
        
        return {
            'template_tables': template_tables,
            'process_tables': process_tables,
            'detail_tables': detail_tables
        }
    
    def find_subsidiary_requirement_data(self):
        """查找子公司需求数据"""
        print("\n🏢 查找子公司需求相关数据...")
        
        # 1. 分析工单发布人和处理人
        print("\n1️⃣ 分析工单发布人分布:")
        sql = """
        SELECT 
            feelec_publisher_id,
            COUNT(*) as ticket_count
        FROM feelec_ticket 
        WHERE feelec_delete = 20 
        GROUP BY feelec_publisher_id
        ORDER BY ticket_count DESC
        LIMIT 10
        """
        
        publisher_stats = self.execute_query(sql)
        if publisher_stats:
            print("   📊 发布人统计 (前10名):")
            for stat in publisher_stats:
                print(f"      发布人ID: {stat['feelec_publisher_id']}, 工单数: {stat['ticket_count']}")
        
        # 2. 查找用户或部门表
        print("\n2️⃣ 查找用户/部门相关表:")
        all_tables = self.get_all_tables()
        
        user_tables = []
        for table in all_tables:
            table_lower = table.lower()
            if any(keyword in table_lower for keyword in ['user', 'department', 'company', 'subsidiary', 'branch']):
                user_tables.append(table)
                print(f"   📋 找到用户/部门表: {table}")
                self.analyze_table_structure(table)
                self.sample_table_data(table, 3)
        
        # 3. 分析工单来源
        print("\n3️⃣ 分析工单来源分布:")
        sql = """
        SELECT 
            feelec_source,
            COUNT(*) as count
        FROM feelec_ticket 
        WHERE feelec_delete = 20 
        GROUP BY feelec_source
        ORDER BY count DESC
        """
        
        source_stats = self.execute_query(sql)
        if source_stats:
            print("   📊 工单来源统计:")
            source_map = {1: '电话', 2: '邮件', 3: '系统', 4: '其他'}
            for stat in source_stats:
                source_text = source_map.get(stat['feelec_source'], '未知')
                print(f"      来源: {source_text} (ID: {stat['feelec_source']}), 数量: {stat['count']}")
        
        return user_tables
    
    def analyze_specific_ticket_content(self, ticket_id):
        """分析特定工单的详细内容"""
        print(f"\n🎫 分析工单 ID {ticket_id} 的详细内容...")
        
        # 1. 获取工单基本信息
        sql = """
        SELECT * FROM feelec_ticket 
        WHERE feelec_ticket_id = %s AND feelec_delete = 20
        """
        
        ticket_info = self.execute_query(sql, (ticket_id,))
        if not ticket_info:
            print(f"   ❌ 未找到工单 ID {ticket_id}")
            return None
        
        ticket = ticket_info[0]
        print(f"   📋 工单基本信息:")
        print(f"      编号: {ticket['feelec_ticket_no']}")
        print(f"      标题: {ticket['feelec_title']}")
        print(f"      模板ID: {ticket['feelec_template_id']}")
        print(f"      模板类型ID: {ticket['feelec_template_type_id']}")
        
        # 2. 查找相关的模板内容
        if ticket['feelec_template_id']:
            print(f"\n   🔍 查找模板 ID {ticket['feelec_template_id']} 的内容...")
            
            # 尝试查找模板表
            all_tables = self.get_all_tables()
            for table in all_tables:
                if 'template' in table.lower():
                    template_sql = f"SELECT * FROM {table} WHERE feelec_template_id = %s"
                    template_data = self.execute_query(template_sql, (ticket['feelec_template_id'],))
                    if template_data:
                        print(f"      📄 在表 {table} 中找到模板内容:")
                        for key, value in template_data[0].items():
                            if isinstance(value, str) and len(value) > 100:
                                display_value = value[:100] + "..."
                            else:
                                display_value = value
                            print(f"        {key}: {display_value}")
        
        # 3. 查找处理记录
        print(f"\n   🔍 查找工单 {ticket_id} 的处理记录...")
        all_tables = self.get_all_tables()
        
        for table in all_tables:
            table_lower = table.lower()
            if any(keyword in table_lower for keyword in ['process', 'record', 'log', 'history']):
                # 尝试不同的字段名
                possible_fields = ['feelec_ticket_id', 'ticket_id', 'feelec_id']
                for field in possible_fields:
                    try:
                        record_sql = f"SELECT * FROM {table} WHERE {field} = %s LIMIT 5"
                        record_data = self.execute_query(record_sql, (ticket_id,))
                        if record_data:
                            print(f"      📄 在表 {table} 中找到处理记录:")
                            for record in record_data:
                                for key, value in record.items():
                                    if isinstance(value, str) and len(value) > 100:
                                        display_value = value[:100] + "..."
                                    else:
                                        display_value = value
                                    print(f"        {key}: {display_value}")
                                print("        ---")
                            break
                    except:
                        continue
        
        return ticket
    
    def close(self):
        """关闭连接"""
        if self.connection:
            self.connection.close()

def main():
    """主函数"""
    print("🔍 工单数据库结构深度分析")
    print("=" * 60)
    print("⚠️  只读模式 - 不会修改任何数据")
    print("=" * 60)
    
    analyzer = TicketDatabaseAnalyzer()
    
    try:
        # 连接数据库
        if not analyzer.connect():
            print("❌ 数据库连接失败")
            return
        
        print("✅ 数据库连接成功")
        
        # 1. 获取所有表
        tables = analyzer.get_all_tables()
        
        # 2. 查找内容相关表
        content_tables = analyzer.find_content_related_tables()
        
        # 3. 分析工单内容来源
        content_sources = analyzer.analyze_ticket_content_sources()
        
        # 4. 查找子公司需求数据
        user_tables = analyzer.find_subsidiary_requirement_data()
        
        # 5. 分析具体工单内容
        print("\n🎯 分析具体工单内容...")
        
        # 获取一个有模板ID的工单
        sql = """
        SELECT feelec_ticket_id 
        FROM feelec_ticket 
        WHERE feelec_delete = 20 AND feelec_template_id IS NOT NULL 
        LIMIT 3
        """
        
        sample_tickets = analyzer.execute_query(sql)
        if sample_tickets:
            for ticket in sample_tickets:
                analyzer.analyze_specific_ticket_content(ticket['feelec_ticket_id'])
        
        print("\n🎉 数据库结构分析完成！")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
    finally:
        analyzer.close()

if __name__ == "__main__":
    main()
