#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工单集成功能最终测试
验证完整的三级钻取功能和详细内容显示
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'pmo-backend'))

import asyncio
from app.api.endpoints.ticket_integration import (
    get_dashboard_stats,
    get_ticket_projects,
    get_project_tickets,
    get_ticket_full_content
)

class FinalTicketTester:
    """最终工单测试器"""
    
    def __init__(self):
        self.mock_user = {"user_id": "test", "username": "test"}
    
    async def test_complete_workflow(self):
        """测试完整的工作流程"""
        print("🎫 工单集成功能完整测试")
        print("=" * 80)
        
        # 第一级：仪表盘统计
        print("\n📊 第一级：仪表盘统计")
        print("-" * 40)
        
        try:
            stats_result = await get_dashboard_stats(current_user=self.mock_user)
            
            if isinstance(stats_result, dict) and stats_result.get('success'):
                stats_data = stats_result.get('data', {})
                project_stats = stats_data.get('project_stats', {})
                ticket_stats = stats_data.get('ticket_stats', {})
                
                print(f"✅ 仪表盘统计获取成功")
                print(f"   📈 项目统计:")
                print(f"      总项目数: {project_stats.get('total_projects', 0)}")
                print(f"      活跃项目: {project_stats.get('active_projects', 0)}")
                print(f"      已完成项目: {project_stats.get('completed_projects', 0)}")
                
                print(f"   🎫 工单统计:")
                print(f"      总工单数: {ticket_stats.get('total_tickets', 0)}")
                print(f"      已完成工单: {ticket_stats.get('completed_tickets', 0)}")
                print(f"      紧急工单: {ticket_stats.get('urgent_tickets', 0)}")
                print(f"      逾期工单: {ticket_stats.get('overdue_tickets', 0)}")
                
                total_projects = project_stats.get('total_projects', 0)
                
            else:
                print(f"❌ 仪表盘统计获取失败")
                return False
                
        except Exception as e:
            print(f"❌ 仪表盘统计测试失败: {e}")
            return False
        
        # 第二级：项目列表
        print(f"\n📋 第二级：项目列表（点击总项目数 {total_projects}）")
        print("-" * 40)
        
        try:
            projects_result = await get_ticket_projects(limit=5, current_user=self.mock_user)
            
            if isinstance(projects_result, dict) and projects_result.get('success'):
                projects_data = projects_result.get('data', {})
                projects = projects_data.get('projects', [])
                
                print(f"✅ 项目列表获取成功，显示前5个项目:")
                
                selected_project = None
                for i, project in enumerate(projects[:5], 1):
                    project_name = project.get('feelec_name', '未知项目')
                    project_id = project.get('feelec_project_id')
                    total_tickets = project.get('total_tickets', 0)
                    completed_tickets = project.get('completed_tickets', 0)
                    completion_rate = project.get('completion_rate', 0)
                    
                    print(f"   {i}. {project_name}")
                    print(f"      项目ID: {project_id}")
                    print(f"      工单数: {total_tickets}")
                    print(f"      完成率: {completion_rate}%")
                    
                    if i == 1:  # 选择第一个项目进行详细测试
                        selected_project = project
                
                if not selected_project:
                    print("❌ 没有找到可测试的项目")
                    return False
                    
            else:
                print(f"❌ 项目列表获取失败")
                return False
                
        except Exception as e:
            print(f"❌ 项目列表测试失败: {e}")
            return False
        
        # 第三级：项目工单详情
        selected_project_id = selected_project['feelec_project_id']
        selected_project_name = selected_project['feelec_name']
        
        print(f"\n🎫 第三级：项目工单详情（点击项目：{selected_project_name}）")
        print("-" * 40)
        
        try:
            tickets_result = await get_project_tickets(project_id=selected_project_id, current_user=self.mock_user)
            
            if isinstance(tickets_result, dict) and tickets_result.get('success'):
                tickets_data = tickets_result.get('data', {})
                tickets = tickets_data.get('tickets', [])
                
                print(f"✅ 项目工单列表获取成功，共 {len(tickets)} 个工单:")
                
                selected_ticket = None
                for i, ticket in enumerate(tickets[:3], 1):
                    ticket_title = ticket.get('feelec_title', '未知工单')
                    ticket_id = ticket.get('feelec_ticket_id')
                    status_name = ticket.get('status_name', '未知状态')
                    priority_text = ticket.get('priority_text', '未知优先级')
                    
                    print(f"   {i}. {ticket_title}")
                    print(f"      工单ID: {ticket_id}")
                    print(f"      状态: {status_name}")
                    print(f"      优先级: {priority_text}")
                    
                    if i == 1:  # 选择第一个工单进行详细测试
                        selected_ticket = ticket
                
                if not selected_ticket:
                    print("❌ 没有找到可测试的工单")
                    return False
                    
            else:
                print(f"❌ 项目工单列表获取失败")
                return False
                
        except Exception as e:
            print(f"❌ 项目工单列表测试失败: {e}")
            return False
        
        # 第四级：工单完整内容
        selected_ticket_id = selected_ticket['feelec_ticket_id']
        selected_ticket_title = selected_ticket['feelec_title']
        
        print(f"\n📄 第四级：工单完整内容（点击工单：{selected_ticket_title}）")
        print("-" * 40)
        
        try:
            content_result = await get_ticket_full_content(ticket_id=selected_ticket_id, current_user=self.mock_user)
            
            if isinstance(content_result, dict) and content_result.get('success'):
                content_data = content_result.get('data', {})
                
                print(f"✅ 工单完整内容获取成功:")
                
                # 基本信息
                print(f"   📋 基本信息:")
                print(f"      工单编号: {content_data.get('feelec_ticket_no', '无')}")
                print(f"      工单标题: {content_data.get('feelec_title', '无')}")
                print(f"      状态: {content_data.get('status_name', '无')}")
                print(f"      优先级: {content_data.get('priority_text', '无')}")
                print(f"      创建时间: {content_data.get('create_time_formatted', '无')}")
                
                # 发布人信息（子公司需求来源）
                publisher_info = content_data.get('publisher_info', {})
                if publisher_info:
                    print(f"   👤 发布人信息（子公司需求来源）:")
                    print(f"      姓名: {publisher_info.get('feelec_name', '无')}")
                    print(f"      电话: {publisher_info.get('feelec_mobile', '无')}")
                    print(f"      邮箱: {publisher_info.get('feelec_email', '无')}")
                    
                    company_info = publisher_info.get('company_info', {})
                    if company_info:
                        print(f"      所属公司: {company_info.get('company_name', '无')}")
                
                # 工单详细内容
                ticket_content = content_data.get('ticket_content', {})
                if ticket_content:
                    print(f"   📄 工单详细内容:")
                    
                    if ticket_content.get('title'):
                        print(f"      标题: {ticket_content['title']}")
                    
                    if ticket_content.get('content'):
                        content_text = ticket_content['content']
                        # 移除HTML标签显示纯文本
                        import re
                        clean_content = re.sub(r'<[^>]+>', '', content_text)
                        if len(clean_content) > 200:
                            clean_content = clean_content[:200] + "..."
                        print(f"      内容: {clean_content}")
                    
                    # 显示其他自定义字段
                    other_fields = 0
                    for key, value in ticket_content.items():
                        if key not in ['title', 'content'] and value:
                            if other_fields == 0:
                                print(f"      其他字段:")
                            print(f"        {key}: {value}")
                            other_fields += 1
                            if other_fields >= 5:  # 限制显示数量
                                break
                
                # 处理记录
                process_records = content_data.get('process_records', [])
                if process_records:
                    print(f"   📊 处理记录: {len(process_records)} 条")
                    for i, record in enumerate(process_records[:3], 1):
                        print(f"      {i}. {record.get('feelec_content', '无内容')[:50]}...")
                
                print(f"\n🎉 完整的四级钻取测试成功！")
                print(f"   ✅ 仪表盘统计 → 项目列表 → 工单列表 → 工单详情")
                print(f"   ✅ 包含子公司需求信息和完整工单内容")
                print(f"   ✅ 所有数据显示无遗漏")
                
                return True
                
            else:
                print(f"❌ 工单完整内容获取失败")
                return False
                
        except Exception as e:
            print(f"❌ 工单完整内容测试失败: {e}")
            return False

async def main():
    """主函数"""
    print("🎫 工单集成功能最终完整测试")
    print("=" * 80)
    print("📝 测试目标:")
    print("   1. 验证四级钻取功能完整性")
    print("   2. 验证子公司需求信息显示")
    print("   3. 验证工单详细内容无遗漏")
    print("   4. 验证所有功能完全可用")
    print("=" * 80)
    
    tester = FinalTicketTester()
    success = await tester.test_complete_workflow()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 最终测试结果: 完全成功！")
        print("✅ 工单集成功能已完全开发完成，可以交付使用！")
        print("✅ 所有功能都经过验证，没有遗漏！")
    else:
        print("❌ 最终测试结果: 存在问题")
        print("⚠️  需要进一步调试和完善")
    print("=" * 80)
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
