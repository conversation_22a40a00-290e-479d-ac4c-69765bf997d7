title: table zt_flow_auditcl
desc: "QA检查单"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: practiceArea
    note: "实践域"
    range: TS,PI,PR,VV,PQA,RDM,PCM,PAD,MPM,RSK,OT,MC,PLAN,EST,CAR,DAR,CM,SAM
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "类别"
    range: process,result
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: title
    note: "检查内容"
    range: 1-10000
    prefix: "这是QA检查内容"
    postfix: ""
    loop: 0
    format: ""
  - field: objectType
    note: "对象类型"
    range: activity,output
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: objectID
    note: "检查对象"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: parent
    note: "父流程ID"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedTo
    note: "指派给"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: "状态"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: subStatus
    note: "子状态"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: "由谁创建"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdDate
    note: "创建日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedBy
    note: "由谁编辑"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedDate
    note: "编辑日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedBy
    note: "由谁指派"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedDate
    note: "指派日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
