title: table zt_flow_activity
desc: "过程活动"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: process
    note: "标准过程ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: name
    note: "活动名称"
    range: 1-10000
    prefix: "这是活动名称"
    postfix: ""
    loop: 0
    format: ""
  - field: optional
    note: "裁剪说明"
    range: yes,no
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: content
    note: "描述"
    range: 1-10000
    prefix: "这是活动描述"
    postfix: ""
    loop: 0
    format: ""
  - field: assignedTo
    note: "指派给"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: "状态"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: "由谁创建"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedBy
    note: "编辑者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedBy
    note: "由谁指派"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: order
    note: "排序"
    range: 1-100:5
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
