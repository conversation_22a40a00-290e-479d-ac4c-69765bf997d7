<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>数据库 - {{ db_name }}</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <style>
        .action-buttons {
            margin-bottom: 20px;
        }
        .action-buttons button {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>数据库: {{ db_name }}</h2>
        <p class="text-muted">当前连接: {{ '阿里云数据库' if current_connection == 'aliyun' else '工单系统数据库' }}</p>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    {% if category == 'error' %}
                        <div class="alert alert-danger">
                            {{ message }}
                        </div>
                    {% elif category == 'warning' %}
                        <div class="alert alert-warning">
                            {{ message }}
                        </div>
                    {% elif category == 'success' %}
                        <div class="alert alert-success">
                            {{ message }}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            {{ message }}
                        </div>
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="mb-3">
            <a href="/" class="btn btn-secondary">返回首页</a>
            <a href="/export_tables/{{ db_name }}" class="btn btn-success">导出表列表</a>
        </div>
        
        <!-- 批量操作按钮 -->
        <div class="action-buttons">
            <form id="batch-copy-form" action="/batch_copy_tables" method="post">
                <input type="hidden" name="source_db" value="{{ db_name }}">
                <button type="button" class="btn btn-primary" id="batch-copy-btn" disabled data-bs-toggle="modal" data-bs-target="#copyTablesModal">批量复制表格</button>
                <button type="button" class="btn btn-secondary" id="select-all">全选</button>
                <button type="button" class="btn btn-secondary" id="deselect-all">取消全选</button>
                <span id="selected-count" class="ms-3">已选择: 0 个表</span>
            </form>
            <form id="batch-delete-form" action="/batch_delete_tables/{{ db_name }}" method="post">
                <button type="button" class="btn btn-danger" id="batch-delete-btn" disabled>批量删除表格</button>
            </form>
        </div>
        
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">创建新表</h5>
                <form action="/create_table/{{ db_name }}" method="post" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="table_name" placeholder="表名" required>
                    </div>
                    <div class="col-md-6">
                        <input type="text" class="form-control" name="columns" placeholder="字段定义，例如：id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(255)" required>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary">创建表</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">从Excel导入表</h5>
                <form action="/import_table/{{ db_name }}" method="post" enctype="multipart/form-data" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="table_name" placeholder="表名" required>
                    </div>
                    <div class="col-md-6">
                        <input type="file" class="form-control" name="file" required accept=".xlsx,.xls">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary">导入</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">表列表</h5>
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th><input type="checkbox" id="select-all-checkbox"></th>
                                <th>表名</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for table in tables %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="table-checkbox" name="table_names" value="{{ table }}" form="batch-copy-form">
                                    <input type="checkbox" class="table-checkbox-delete" name="table_names" value="{{ table }}" form="batch-delete-form" style="display:none;">
                                </td>
                                <td>{{ table }}</td>
                                <td>
                                    <a href="/table/{{ db_name }}/{{ table }}" class="btn btn-sm btn-primary">查看</a>
                                    <a href="/delete_table/{{ db_name }}/{{ table }}" class="btn btn-sm btn-danger" onclick="return confirm('确定要删除该表吗？')">删除</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 批量复制表格模态框 -->
    <div class="modal fade" id="copyTablesModal" tabindex="-1" aria-labelledby="copyTablesModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="copyTablesModalLabel">批量复制表格</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="target_db" class="form-label">目标数据库:</label>
                        <select class="form-control" id="target_db" name="target_db" form="batch-copy-form" required>
                            <option value="">请选择目标数据库</option>
                            {% for db in all_databases %}
                                {% if db != db_name %}
                                    <option value="{{ db }}">{{ db }}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-check mb-3">
                        <input type="checkbox" class="form-check-input" id="copy_data" name="copy_data" form="batch-copy-form" checked>
                        <label class="form-check-label" for="copy_data">同时复制数据</label>
                    </div>
                    <div class="selected-tables-list mb-3">
                        <p>已选择表格:</p>
                        <ul id="selected-tables-list"></ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary" form="batch-copy-form">开始复制</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 全选/取消全选功能
            const selectAllCheckbox = document.getElementById('select-all-checkbox');
            const tableCheckboxes = document.querySelectorAll('.table-checkbox');
            const tableCheckboxesDelete = document.querySelectorAll('.table-checkbox-delete');
            const batchCopyBtn = document.getElementById('batch-copy-btn');
            const batchDeleteBtn = document.getElementById('batch-delete-btn');
            const selectedCountSpan = document.getElementById('selected-count');
            const selectAllBtn = document.getElementById('select-all');
            const deselectAllBtn = document.getElementById('deselect-all');
            const batchDeleteForm = document.getElementById('batch-delete-form');
            
            // 更新选中计数和按钮状态
            function updateSelectedCount() {
                const selectedCount = document.querySelectorAll('.table-checkbox:checked').length;
                selectedCountSpan.textContent = `已选择: ${selectedCount} 个表`;
                batchCopyBtn.disabled = selectedCount === 0;
                batchDeleteBtn.disabled = selectedCount === 0;

                // 同步删除复选框的状态
                tableCheckboxesDelete.forEach((deleteCheckbox, index) => {
                    deleteCheckbox.checked = tableCheckboxes[index].checked;
                });

                // 更新模态框中的已选表格列表
                const selectedTablesList = document.getElementById('selected-tables-list');
                selectedTablesList.innerHTML = '';

                document.querySelectorAll('.table-checkbox:checked').forEach(checkbox => {
                    const li = document.createElement('li');
                    li.textContent = checkbox.value;
                    selectedTablesList.appendChild(li);
                });
            }
            
            // 复选框变化事件
            tableCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateSelectedCount();
                    
                    // 检查是否所有复选框都被选中
                    const allChecked = Array.from(tableCheckboxes).every(cb => cb.checked);
                    selectAllCheckbox.checked = allChecked;
                });
            });
            
            // 全选复选框
            selectAllCheckbox.addEventListener('change', function() {
                tableCheckboxes.forEach(cb => {
                    cb.checked = this.checked;
                });
                updateSelectedCount();
            });
            
            // 全选按钮
            selectAllBtn.addEventListener('click', function() {
                tableCheckboxes.forEach(cb => {
                    cb.checked = true;
                });
                selectAllCheckbox.checked = true;
                updateSelectedCount();
            });
            
            // 取消全选按钮
            deselectAllBtn.addEventListener('click', function() {
                tableCheckboxes.forEach(cb => {
                    cb.checked = false;
                });
                selectAllCheckbox.checked = false;
                updateSelectedCount();
            });
            
            // 批量删除按钮
            batchDeleteBtn.addEventListener('click', function() {
                const selectedCount = document.querySelectorAll('.table-checkbox:checked').length;
                if (selectedCount > 0) {
                    if (confirm(`确定要删除选中的 ${selectedCount} 个表格吗？此操作不可撤销！`)) {
                        batchDeleteForm.submit();
                    }
                }
            });

            // 初始化
            updateSelectedCount();

            // 模态框显示前更新已选表格列表
            const copyTablesModal = document.getElementById('copyTablesModal');
            copyTablesModal.addEventListener('show.bs.modal', function() {
                updateSelectedCount();
            });
        });
    </script>
</body>
</html>