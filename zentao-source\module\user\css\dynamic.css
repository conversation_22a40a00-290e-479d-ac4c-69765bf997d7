#dynamics {padding: 20px;}
#dynamics.hasData {padding-bottom: 1px;}
.dynamic {position: relative; padding-left: 170px;}
.dynamic-date {position: absolute; left: 0; top: 0; width: 150px; border: 2px solid #eee; border-radius: 4px; padding: 14px 20px; height: 58px;}
.dynamic-date:before {content: ' '; display: block; position: absolute; right: -22px; top: 26px; height: 2px; width: 20px; background-color: #eee;}
.dynamic-date > .date-label,
.dynamic-date > .date-text {font-size: 18px; display: block;}
.dynamic-date > .date-label + .date-text {font-size: 14px; line-height: 14px;}
.dynamic-date > .date-label {margin-top: -8px;}
.dynamic-date > .btn {position: absolute; right: 10px; top: 15px;}
.dynamic .timeline {border: 2px solid #eee; border-radius: 4px; margin-bottom: 20px; padding: 10px 20px 10px 100px; transition: max-height .2s; overflow: hidden;}
.dynamic .timeline-tag {left: -75px; font-size: 14px; color: #838A9D;}
.dynamic .timeline-text {color: #3C4353;}
.dynamic .text-muted {color: #838A9D;}
.dynamic .label-id {top: -1px; border: 1px solid rgba(131,138,157,0.25); color: #838A9D;}
.dynamic.active .timeline,
.dynamic.active .dynamic-date {border-color: #00a9fc;}
.dynamic.active .dynamic-date:before {background-color: #00a9fc;}
.dynamic.collapsed .timeline {max-height: 58px;}
.dynamic.collapsed .timeline > li + li {display: none;}
.dynamic.collapsed .dynamic-btn > .icon:before {content: '\f0d8';}

.timeline > li:before {left: -26px;}
.timeline > li + li:after {left: -23px;}
.timeline-text {margin-left: -18px;}
.label-action {padding: 0 4px;}
.label-id {margin-left: 4px;}
.timeline > li.active:before {left: -30px;}
.timeline > li > div:after {left: -27px;}
.timeline .timeline-text {display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}
.timeline > li > div > .timeline-tag, .timeline > li > div > .timeline-text > .label-action {color: #838A9D;}
.timeline > li > div > .timeline-text > a {color: #313C52;}
