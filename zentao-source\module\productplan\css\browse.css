.table td.content div {height: 25px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; float: left; max-width: calc(100% - 20px);}
.table td.content .more {position: absolute; right: 16px;}
.table td.content .more .icon {color: #838a9d;}
td.c-branch {overflow: hidden; text-align: left !important; white-space: nowrap;}

.table-children {border-left: 2px solid #cbd0db; border-right: 2px solid #cbd0db;}
.table tbody > tr.table-children.table-child-top {border-top: 2px solid #cbd0db;}
.table tbody > tr.table-children.table-child-bottom {border-bottom: 2px solid #cbd0db;}
.table td.has-child > a {max-width: 90%; max-width: calc(100% - 30px); display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}
.table td.has-child > .plan-name > .task-toggle {color: #838a9d; position: relative; top: 1px; line-height: 16px;}
.table td.has-child > .plan-name > .task-toggle:hover {color: #006af1; cursor: pointer;}
.table td.has-child > .plan-name > .task-toggle > .icon, .table td.content .more .icon {font-size: 16px; display: inline-block; transition: transform .2s; -ms-transform: rotate(-90deg); -moz-transform: rotate(-90deg); -o-transform: rotate(-90deg); -webkit-transform: rotate(-90deg); transform: rotate(-90deg);}
.table td.has-child > .plan-name > .task-toggle > .icon:before {text-align: left;}
.table td.has-child > .plan-name > .task-toggle.collapsed {top: 0;}
.table td.has-child > .plan-name > .task-toggle.collapsed > .icon, .table td.content .more .icon.rotate-down {-ms-transform: rotate(90deg); -moz-transform: rotate(90deg); -o-transform: rotate(90deg); -webkit-transform: rotate(90deg); transform: rotate(90deg);}
.main-table tbody > tr.table-children > td:first-child::before {width: 3px;}
@-moz-document url-prefix() {.main-table tbody > tr.table-children > td:first-child::before {width: 4px;};}

.c-title {width: 160px;}
.c-branch {width: 130px;}
.c-story, .c-status {width: 80px;}
.c-execution {width: 60px !important;}
.c-bug, .c-hour {width: 60px;}
.c-desc {width: 140px;}
.c-date {width: 90px;}

.plan-name {position: relative; display: flex; align-items: center;}
.plan-name > span {flex: none;}

[lang^='zh-'] #productplanList .c-title.has-child > .plan-name.expired > a {overflow:hidden; max-width: calc(100% - 62px);}
[lang^='en']  #productplanList .c-title.has-child > .plan-name.expired > a {overflow:hidden; max-width: calc(100% - 67px);}
#productplanList .c-title.has-child > .plan-name > a {overflow:hidden; max-width: calc(100% - 16px);}
#productplanList .c-title > .plan-name.expired > a {overflow:hidden; max-width: calc(100% - 46px);}
#productplanList .plan-name.expired > .label.label-danger {margin-left:3px; }
#productplanList .plan-name > .label.label-light {margin-right:3px; }

.switchButton {background: #fff !important;}
.panel-actions {position: relative; padding: 0 0; padding-top: 1px;}
#kanbanContainer {background: #efefef; box-shadow: none;}
#kanbanContainer {padding-bottom: 0; margin-bottom: 0; border: unset;}
#kanbanContainer.fullscreen {overflow: auto;}
.icon-list {padding-left: 7px;}
.titleBox .icon-delay {padding-right: 8px;}

.kanban-card .productplanInfo .user {float: right;display: block;}
.kanban-card a[disabled] {color: #313c52;}
.kanban-card .header .actions {position: absolute;top: 4px;right: 4px;opacity: 0;}
.kanban-card .productplanDesc {min-width: 100%;color: #838a9d;overflow: hidden;white-space: nowrap;text-overflow: clip;}
.kanban-card {height: auto !important;padding: 8px 14px !important;min-height: 60px;}
.icon-kanban {padding-left: 7px; font-size: 16px;}
.label-wait {background: #EFEFEF !important;color: #838A9D;}
.label-future {background: #EFEFEF !important;color: #838A9D;border-radius: 9px;}
.label-doing {background: #f8d2d2 !important;color: #e64b4c;}
.label-done {background: #dfe9d8 !important;color: #429b16;}
.label-closed, .label-terminate {background: #e5e5e5 !important;color: #b8b8b8;}

#productplanList thead th.c-title {width: 100%;}
#productplanList .c-actions {width: 265px;}
#productplanList .c-actions .btn+.btn {margin-left: -1px;}
#productplanList .c-actions .btn {display: block; float: left;}
#productplanList td.c-actions .dividing-line {width: 1px; height: 16px; display: inline-block; vertical-align: middle; background: #F4F5F7; margin: 6px 0 0 0; float: left;}

.popover.right {left: 40px; top: -5px; white-space: nowrap;}
.popover.right .popover-content {padding: 5px 20px 0px 0px;}
.execution-tip {list-style: none;}
.popover.right .arrow {margin-top: -30px;}
.execution-tip li {overflow: hidden; text-overflow: unset; white-space: nowrap;}
td.execution-links {overflow: visible;}
