#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
督办管理页面新设计测试脚本
测试重新设计的页面布局、固定表头、公司列管理等功能
"""

import requests
import time
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

def test_backend_service():
    """测试后端服务是否正常"""
    try:
        response = requests.get('http://localhost:8000/api/v1/new-supervision/items', timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                items_count = len(data.get('data', []))
                companies_count = len(data.get('companies', []))
                logging.info(f"✅ 后端服务正常，有 {items_count} 条督办事项，{companies_count} 个公司")
                return True, data
            else:
                logging.error(f"❌ 后端返回错误: {data.get('message', '未知错误')}")
                return False, None
        else:
            logging.error(f"❌ 后端服务异常，状态码: {response.status_code}")
            return False, None
    except Exception as e:
        logging.error(f"❌ 后端服务连接失败: {e}")
        return False, None

def test_frontend_service():
    """测试前端服务是否正常"""
    try:
        response = requests.get('http://localhost:3000', timeout=5)
        if response.status_code == 200:
            logging.info("✅ 前端服务正常")
            return True
        else:
            logging.error(f"❌ 前端服务异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        logging.error(f"❌ 前端服务连接失败: {e}")
        return False

def check_page_features():
    """检查页面新功能特性"""
    logging.info("检查页面新功能特性...")
    
    # 读取Vue文件内容
    try:
        with open('pmo-web/src/views/NewSupervision.vue', 'r', encoding='utf-8') as f:
            content = f.read()
        
        features = [
            ("isCompactMode", "紧凑模式功能"),
            ("companyManageDialogVisible", "公司管理对话框"),
            ("visibleCompanies", "可见公司列表"),
            ("fixed=\"left\"", "固定左侧列"),
            ("fixed=\"right\"", "固定右侧列"),
            ("toolbar-left", "工具栏左侧布局"),
            ("toolbar-right", "工具栏右侧布局"),
            ("管理公司列", "公司列管理按钮"),
            ("紧凑模式", "紧凑模式开关"),
            ("supervision-table-wrapper", "表格包装器"),
            ("compact-mode", "紧凑模式样式")
        ]
        
        for feature, description in features:
            if feature in content:
                logging.info(f"✅ {description}")
            else:
                logging.warning(f"⚠️ 缺少 {description}")
        
        return True
    except Exception as e:
        logging.error(f"❌ 检查页面功能失败: {e}")
        return False

def test_responsive_design():
    """测试响应式设计"""
    logging.info("检查响应式设计...")
    
    try:
        with open('pmo-web/src/views/NewSupervision.vue', 'r', encoding='utf-8') as f:
            content = f.read()
        
        responsive_features = [
            ("@media (max-width: 1200px)", "中等屏幕适配"),
            ("@media (max-width: 768px)", "小屏幕适配"),
            ("flex-wrap: wrap", "弹性布局换行"),
            ("overflow: auto", "滚动处理"),
            ("::-webkit-scrollbar", "滚动条样式")
        ]
        
        for feature, description in responsive_features:
            if feature in content:
                logging.info(f"✅ {description}")
            else:
                logging.warning(f"⚠️ 缺少 {description}")
        
        return True
    except Exception as e:
        logging.error(f"❌ 检查响应式设计失败: {e}")
        return False

def main():
    """主测试函数"""
    logging.info("🚀 开始督办管理页面新设计测试")
    logging.info("=" * 60)
    
    # 测试后端服务
    logging.info("步骤1: 测试后端服务")
    backend_ok, backend_data = test_backend_service()
    if not backend_ok:
        logging.error("❌ 后端服务测试失败，请检查后端服务")
        return
    
    # 测试前端服务
    logging.info("步骤2: 测试前端服务")
    frontend_ok = test_frontend_service()
    if not frontend_ok:
        logging.error("❌ 前端服务测试失败，请检查前端服务")
        return
    
    # 检查页面功能特性
    logging.info("步骤3: 检查页面新功能")
    features_ok = check_page_features()
    
    # 检查响应式设计
    logging.info("步骤4: 检查响应式设计")
    responsive_ok = test_responsive_design()
    
    # 打开页面进行手动测试
    logging.info("步骤5: 打开页面进行手动测试")
    import webbrowser
    webbrowser.open('http://localhost:3000/#/new-supervision')
    logging.info("✅ 督办管理页面已打开")
    
    logging.info("=" * 60)
    logging.info("📊 测试结果总结")
    logging.info("=" * 60)
    
    if backend_ok and frontend_ok and features_ok and responsive_ok:
        logging.info("🎉🎉🎉 督办管理页面新设计测试全部通过！🎉🎉🎉")
        
        logging.info("\n✅ 新设计特性：")
        logging.info("- ✅ 重新设计的页面布局，解决表格太长问题")
        logging.info("- ✅ 固定左侧工作主题列和右侧操作列")
        logging.info("- ✅ 紧凑模式和标准模式切换")
        logging.info("- ✅ 公司列动态管理功能")
        logging.info("- ✅ 响应式设计，适配不同屏幕")
        logging.info("- ✅ 优化的列宽和表格样式")
        logging.info("- ✅ 改进的工具栏布局")
        
        logging.info("\n📋 手动测试项目：")
        logging.info("1. 点击'紧凑模式'开关，查看表格样式变化")
        logging.info("2. 点击'管理公司列'按钮，测试公司列管理功能")
        logging.info("3. 拖拽表格，验证固定列功能")
        logging.info("4. 调整浏览器窗口大小，测试响应式设计")
        logging.info("5. 测试Excel导入导出功能是否正常")
        
        logging.info("\n🎯 新设计完成！页面更加美观实用！")
    else:
        logging.error("❌ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
