/**
 * 工单系统集成API
 */
import request from '@/utils/request'

// 获取工单系统项目列表
export function getTicketProjects(params = {}) {
  return request({
    url: '/ticket-integration/projects',
    method: 'get',
    params
  })
}

// 获取项目工单详情
export function getProjectTickets(projectId) {
  return request({
    url: `/ticket-integration/projects/${projectId}/tickets`,
    method: 'get'
  })
}

// 获取团队工作负荷分析
export function getWorkloadAnalysis(params = {}) {
  return request({
    url: '/ticket-integration/workload-analysis',
    method: 'get',
    params
  })
}

// 获取工单系统仪表盘统计
export function getDashboardStats() {
  return request({
    url: '/ticket-integration/dashboard-stats',
    method: 'get'
  })
}

// 获取集成建议
export function getIntegrationSuggestions() {
  return request({
    url: '/ticket-integration/integration-suggestions',
    method: 'get'
  })
}

// 获取工单完整内容
export function getTicketFullContent(ticketId) {
  return request({
    url: `/ticket-integration/tickets/${ticketId}/full-content`,
    method: 'get'
  })
}

// 按状态获取工单列表
export function getTicketsByStatus(status, limit = 50) {
  return request({
    url: '/ticket-integration/tickets/by-status',
    method: 'get',
    params: { status, limit }
  })
}

// 获取用户列表
export const getUsersList = (limit = 50) => {
  return request({
    url: `/api/ticket-integration/users`,
    method: 'get',
    params: { limit }
  })
}

// 获取主体列表
export const getCompaniesList = (limit = 50) => {
  return request({
    url: `/api/ticket-integration/companies`,
    method: 'get',
    params: { limit }
  })
}

// 获取用户工单列表
export const getUserTickets = (userId) => {
  return request({
    url: `/api/ticket-integration/users/${userId}/tickets`,
    method: 'get'
  })
}

// 获取主体工单列表
export const getCompanyTickets = (companyId) => {
  return request({
    url: `/api/ticket-integration/companies/${companyId}/tickets`,
    method: 'get'
  })
}
