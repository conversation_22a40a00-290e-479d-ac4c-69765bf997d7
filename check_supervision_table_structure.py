#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查督办管理表结构
"""

import pymysql

def check_table_structure():
    """检查表结构"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        print("✅ 数据库连接成功")
        
        with connection.cursor() as cursor:
            # 检查supervision_items表结构
            print("\n📋 检查 supervision_items 表结构:")
            cursor.execute("DESCRIBE supervision_items")
            columns = cursor.fetchall()
            
            if columns:
                print("   表结构:")
                for column in columns:
                    print(f"      {column[0]} ({column[1]}) - {column[2] if column[2] else 'NULL'}")
                
                # 检查是否有is_annual_assessment字段
                column_names = [col[0] for col in columns]
                if 'is_annual_assessment' in column_names:
                    print("   ✅ is_annual_assessment 字段存在")
                else:
                    print("   ❌ is_annual_assessment 字段不存在")
                    print("   🔧 需要添加此字段")
            else:
                print("   ❌ supervision_items 表不存在")
            
            # 检查数据量
            cursor.execute("SELECT COUNT(*) FROM supervision_items")
            count = cursor.fetchone()[0]
            print(f"   📊 数据量: {count} 条")
            
            # 显示前几条数据
            if count > 0:
                cursor.execute("SELECT * FROM supervision_items LIMIT 3")
                rows = cursor.fetchall()
                print(f"   📝 前3条数据:")
                for i, row in enumerate(rows):
                    print(f"      {i+1}. ID:{row[0]}, 序号:{row[1]}, 主题:{row[3] if len(row) > 3 else 'N/A'}")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()

def fix_table_structure():
    """修复表结构"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        print("\n🔧 开始修复表结构...")
        
        with connection.cursor() as cursor:
            # 添加缺失的字段
            try:
                cursor.execute("""
                    ALTER TABLE supervision_items 
                    ADD COLUMN is_annual_assessment ENUM('是', '否') DEFAULT '否' COMMENT '是否年度绩效考核指标'
                    AFTER work_content
                """)
                print("✅ 添加 is_annual_assessment 字段成功")
            except Exception as e:
                if "Duplicate column name" in str(e):
                    print("✅ is_annual_assessment 字段已存在")
                else:
                    print(f"❌ 添加 is_annual_assessment 字段失败: {str(e)}")
            
            # 检查并添加其他可能缺失的字段
            cursor.execute("DESCRIBE supervision_items")
            columns = cursor.fetchall()
            column_names = [col[0] for col in columns]
            
            # 检查progress_description字段
            if 'progress_description' not in column_names:
                try:
                    cursor.execute("""
                        ALTER TABLE supervision_items 
                        ADD COLUMN progress_description TEXT COMMENT '7月28日进度情况'
                        AFTER completion_deadline
                    """)
                    print("✅ 添加 progress_description 字段成功")
                except Exception as e:
                    print(f"❌ 添加 progress_description 字段失败: {str(e)}")
            else:
                print("✅ progress_description 字段已存在")
            
            # 检查overall_progress字段类型
            overall_progress_found = False
            for column in columns:
                if column[0] == 'overall_progress':
                    overall_progress_found = True
                    if 'enum' not in column[1].lower():
                        try:
                            cursor.execute("""
                                ALTER TABLE supervision_items 
                                MODIFY COLUMN overall_progress ENUM('X 未启动', 'O进行中', '！逾期', '√ 已完成', '— 不需要执行') 
                                DEFAULT 'X 未启动' COMMENT '整体进度'
                            """)
                            print("✅ 修改 overall_progress 字段类型成功")
                        except Exception as e:
                            print(f"❌ 修改 overall_progress 字段类型失败: {str(e)}")
                    else:
                        print("✅ overall_progress 字段类型正确")
                    break
            
            if not overall_progress_found:
                try:
                    cursor.execute("""
                        ALTER TABLE supervision_items 
                        ADD COLUMN overall_progress ENUM('X 未启动', 'O进行中', '！逾期', '√ 已完成', '— 不需要执行') 
                        DEFAULT 'X 未启动' COMMENT '整体进度'
                    """)
                    print("✅ 添加 overall_progress 字段成功")
                except Exception as e:
                    print(f"❌ 添加 overall_progress 字段失败: {str(e)}")
            
            connection.commit()
            print("✅ 表结构修复完成")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 督办管理表结构检查和修复")
    print("=" * 60)
    
    # 检查表结构
    check_table_structure()
    
    # 修复表结构
    fix_table_structure()
    
    # 再次检查表结构
    print("\n🔍 修复后再次检查:")
    check_table_structure()
    
    print("\n" + "=" * 60)
    print("🏁 检查完成")

if __name__ == "__main__":
    main()
