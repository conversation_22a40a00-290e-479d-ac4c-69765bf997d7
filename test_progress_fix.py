#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试整体进度计算修复
"""

import requests

BASE_URL = 'http://127.0.0.1:8001/api/v1/supervision'

def test_progress_fix():
    """测试整体进度计算修复"""
    print("🚀 开始测试整体进度计算修复...")
    
    # 1. 获取督办事项列表（这会触发重新计算）
    print("\n1. 获取督办事项列表...")
    items = get_supervision_items()
    if not items:
        print("❌ 无法获取督办事项列表")
        return
    
    print(f"✅ 获取到 {len(items)} 个督办事项")
    
    # 2. 获取公司列表
    companies = get_companies()
    if not companies:
        print("❌ 无法获取公司列表")
        return
    
    company_names = [c['company_name'] for c in companies]
    print(f"✅ 当前有 {len(company_names)} 个公司")
    
    # 3. 检查前几个督办事项的整体进度
    print(f"\n3. 检查督办事项的整体进度计算:")
    for i, item in enumerate(items[:3]):  # 只检查前3个
        work_theme = item['work_theme']
        overall_progress = item['overall_progress']
        
        print(f"\n   督办事项 {i+1}: {work_theme}")
        print(f"   整体进度: {overall_progress}")
        
        # 统计各公司状态
        status_counts = {'X': 0, 'O': 0, '√': 0, '！': 0, '—': 0}
        valid_statuses = []
        
        for company in company_names:
            status_field = f"{company}_status"
            status = item.get(status_field, 'X')
            if status in status_counts:
                status_counts[status] += 1
            if status != '—':
                valid_statuses.append(status)
        
        print(f"   状态统计: X={status_counts['X']}, O={status_counts['O']}, √={status_counts['√']}, ！={status_counts['！']}, —={status_counts['—']}")
        
        # 手动计算应该的整体进度
        if not valid_statuses:
            expected = '— 不需要执行'
        elif status_counts['！'] > 0:
            expected = '！逾期'
        elif status_counts['√'] == len(valid_statuses):
            expected = '√ 已完成'
        elif status_counts['O'] > 0:
            expected = 'O 进行中'
        else:
            expected = 'X 未启动'
        
        print(f"   预期整体进度: {expected}")
        
        if expected == overall_progress:
            print("   ✅ 整体进度计算正确")
        else:
            print(f"   ❌ 整体进度计算错误")
    
    print("\n🎉 测试完成！")

def get_supervision_items():
    """获取督办事项列表"""
    try:
        response = requests.get(f'{BASE_URL}/items')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                return result.get('data', [])
    except Exception as e:
        print(f"获取督办事项异常: {e}")
    return None

def get_companies():
    """获取公司列表"""
    try:
        response = requests.get(f'{BASE_URL}/companies')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                return result.get('data', [])
    except Exception as e:
        print(f"获取公司列表异常: {e}")
    return None

if __name__ == "__main__":
    test_progress_fix()
