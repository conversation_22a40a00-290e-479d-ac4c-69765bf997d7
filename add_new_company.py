#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加新公司测试
"""

import requests
import random

def add_new_company():
    """添加新公司"""
    # 生成随机公司名
    company_name = f"新公司{random.randint(100, 999)}"
    
    print(f"🚀 添加新公司: {company_name}")
    
    try:
        response = requests.post('http://127.0.0.1:8001/api/v1/supervision/companies', 
                               json={'company_name': company_name})
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 添加成功: {result}")
            
            # 验证添加结果
            print("\n验证添加结果...")
            response = requests.get('http://127.0.0.1:8001/api/v1/supervision/companies')
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    companies = result.get('data', [])
                    new_company_found = any(c['company_name'] == company_name for c in companies)
                    if new_company_found:
                        print(f"✅ 验证成功，新公司 '{company_name}' 已添加")
                        print(f"当前公司列表:")
                        for company in companies:
                            print(f"   - {company['company_name']}")
                    else:
                        print(f"❌ 验证失败，未找到新公司")
        else:
            print(f"❌ 添加失败: {response.text}")
    except Exception as e:
        print(f"❌ 异常: {str(e)}")

if __name__ == "__main__":
    add_new_company()
