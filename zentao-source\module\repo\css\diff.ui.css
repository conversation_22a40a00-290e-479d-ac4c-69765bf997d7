.diff-back-btn {border: none; --tw-ring-shadow: none}
.diff-back-btn::after {border: none;}
.diff-back-btn::before {border: none;}
.diff-label {border: none; --tw-ring-shadow: none; margin-left: 5px; margin-right: 10px}
.label-exchange {background-color: #565F7C; cursor: pointer;}

#fileTabs .tab-pane {display: none;}
#fileTabs .tab-pane.active {display: block;}
#fileTabs .nav-item {max-width: none !important;}
.sidebar .tree{background-color: white;}
.container .sidebar{width: 240px;}
.tree li.tree-item.selected{color: #438EFF !important;}
#monacoTree{overflow: auto;}
.monaco-dropmenu{background: #efefef;}

#featureBar .dropmenu-btn .icon-angle-right {display: none;}
#featureBar .dropmenu-btn {padding-left: 0.3rem;}
