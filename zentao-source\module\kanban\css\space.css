#spaceList {margin: 0 0; margin-bottom: 15px;}
#spaceList .spaceTitle {margin-bottom: 15px;}
#spaceList .spaceTitle > div {display: inline-block;}
#spaceList .spaceTitle .label-closed {background: #AAA !important; color: #FFF;}
#spaceList .spaceTitle .icon-zone {color: #838a9d; margin-top:100px;}
#spaceList .spaceActions {margin-top: -4px;}
#spaceList .spaceActions > a{margin-left: 10px;}
.space .menu {margin-left: 5px; margin-right:5px;}
.space .table-empty-tip {padding: 40px 10px;}
.space .spaceTitle.pull-left {overflow: hidden; white-space: nowrap; max-width: calc(100% - 220px)}
.spaceName {font-size: 16px; font-weight: bold;}
.spaceTitle .icon-cube {font-size: 16px;}
.spaceDesc {font-size: 13px; color: #838A9D; margin: -5px 0 5px 0;}
#setting .setting {margin-left: -25px; min-width: 80px}

.kanbans > .col {width: 25% !important;}
@media screen and (max-width: 1500px) {.kanbans > .col {width: 25%;}}
.kanbans .panel {margin: 0 0;  border: 1px solid #DCDCDC; border-radius: 4px; box-shadow: none; cursor: pointer; height: 160px; margin-bottom: 5px;}
.kanbans .panel:hover {border-color: #006AF1; box-shadow: 0 0 10px 0 rgba(0,0,100,.25);}
.kanbans .panel-heading {padding: 4px 2px 10px 16px;}
.kanbans .kanban-name {overflow: hidden; white-space:nowrap; width: 90%; float: left; padding-top: 8px;}
.kanbans .panel .label-closed {background: #AAA !important; color: #FFF; margin-top:-1px !important;}
.kanbans .kanban-actions {float: right; visibility: hidden;}
.kanbans .kanban-actions .dropdown-menu.pull-right {top:31px; right: -84px; left: auto;}
.kanbans .kanban-actions .dropdown-menu.pull-left {top:31px; right:-1px; left: auto;}
.kanbans .panel .label-closed {background: #AAA !important; color: #FFF; margin-top:-6px;}
.kanbans .panel-body {padding: 0 16px 16px;}
.kanbans .kanban-desc {color: #838a9d; word-break:break-all; height: 70px; overflow: hidden; float:left;}
.kanbans .kanban-desc > * {float: left;}
.kanbans .kanban-footer {position: absolute; bottom: 10px; right: 10px; left: 15px;}
.kanbans .kanban-members {float: left; height: 24px; line-height: 24px;}
.kanbans .kanban-members > div {display: inline-block; height: 24px;}
.kanbans .kanban-members > div + div {margin-left: -5px;}
.kanbans .kanban-members > div > .avatar {display: inline-block; margin-right: 1px;}
.kanbans .kanban-members > span {display: inline-block; color: transparent; width: 2px; height: 2px; background-color: #8990a2; position: relative; border-radius: 50%; top: 3px; margin: 0 3px;}
.kanbans .kanban-members > span:before,
.kanbans .kanban-members > span:after {content: ''; display: block; position: absolute; width: 2px; height: 2px; background-color: #8990a2; top: 0; border-radius: 50%}
.kanbans .kanban-members > span:before {left: -4px;}
.kanbans .kanban-members > span:after {right: -4px;}
.kanbans .kanban-members-total {display: inline-block; margin-left: 6px; position: relative; top: 3px}
.kanbans .kanbanAcl {position: absolute; right: 0px; bottom: 2px; color: #838a9d;}
.kanbans .kanban-label {margin: 8px 6px 0px 0px; padding: 2.5px 4px; float: left; color: #ff8a53; border: 0.8px solid #ff8a53; background: none;}
@media screen and (max-width: 1366px){.kanbans .kanban-name {width: 66%;}}

a.disabled {pointer-events: none;}

.noBorder {border: 0px;}
.position {padding-right: 5px;}
