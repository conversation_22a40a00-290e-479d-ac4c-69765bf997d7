#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公司管理功能测试脚本
测试公司列的数据库持久化功能
"""

import requests
import json
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

BASE_URL = 'http://localhost:8000/api/v1'

def test_get_companies():
    """测试获取公司列表"""
    try:
        response = requests.get(f'{BASE_URL}/new-supervision/companies')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                companies = data.get('data', [])
                logging.info(f"✅ 获取公司列表成功，共 {len(companies)} 家公司")
                for company in companies:
                    logging.info(f"   - {company['company_name']} ({company['company_code']}) 顺序:{company['display_order']}")
                return True, companies
            else:
                logging.error(f"❌ 获取公司列表失败: {data.get('message')}")
                return False, []
        else:
            logging.error(f"❌ 获取公司列表失败，状态码: {response.status_code}")
            return False, []
    except Exception as e:
        logging.error(f"❌ 获取公司列表异常: {e}")
        return False, []

def test_create_company():
    """测试创建新公司"""
    try:
        new_company = {
            "company_code": "TEST001",
            "company_name": "测试公司001",
            "display_order": 99
        }
        
        response = requests.post(f'{BASE_URL}/supervision/companies', json=new_company)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                logging.info(f"✅ 创建公司成功: {new_company['company_name']}")
                return True, data
            else:
                logging.error(f"❌ 创建公司失败: {data.get('message')}")
                return False, None
        else:
            logging.error(f"❌ 创建公司失败，状态码: {response.status_code}")
            logging.error(f"   响应内容: {response.text}")
            return False, None
    except Exception as e:
        logging.error(f"❌ 创建公司异常: {e}")
        return False, None

def test_update_company(company_id):
    """测试更新公司信息"""
    try:
        update_data = {
            "company_name": "测试公司001-已更新",
            "display_order": 88
        }
        
        response = requests.put(f'{BASE_URL}/supervision/companies/{company_id}', json=update_data)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                logging.info(f"✅ 更新公司成功: ID {company_id}")
                return True
            else:
                logging.error(f"❌ 更新公司失败: {data.get('message')}")
                return False
        else:
            logging.error(f"❌ 更新公司失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        logging.error(f"❌ 更新公司异常: {e}")
        return False

def test_toggle_company_status(company_id):
    """测试启用/禁用公司"""
    try:
        response = requests.patch(f'{BASE_URL}/supervision/companies/{company_id}/toggle')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                logging.info(f"✅ 切换公司状态成功: ID {company_id}")
                return True
            else:
                logging.error(f"❌ 切换公司状态失败: {data.get('message')}")
                return False
        else:
            logging.error(f"❌ 切换公司状态失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        logging.error(f"❌ 切换公司状态异常: {e}")
        return False

def test_frontend_integration():
    """测试前端集成"""
    try:
        response = requests.get('http://localhost:3000')
        if response.status_code == 200:
            logging.info("✅ 前端服务正常")
            return True
        else:
            logging.error(f"❌ 前端服务异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        logging.error(f"❌ 前端服务连接失败: {e}")
        return False

def main():
    """主测试函数"""
    logging.info("🚀 开始公司管理功能测试")
    logging.info("=" * 60)
    
    # 测试1: 获取公司列表
    logging.info("步骤1: 测试获取公司列表")
    success, companies = test_get_companies()
    if not success:
        logging.error("❌ 获取公司列表失败，终止测试")
        return
    
    # 测试2: 创建新公司
    logging.info("步骤2: 测试创建新公司")
    success, create_result = test_create_company()
    if success and create_result:
        # 假设返回了新创建的公司ID
        new_company_id = create_result.get('data', {}).get('id')
        if new_company_id:
            logging.info(f"   新公司ID: {new_company_id}")
            
            # 测试3: 更新公司信息
            logging.info("步骤3: 测试更新公司信息")
            test_update_company(new_company_id)
            
            # 测试4: 切换公司状态
            logging.info("步骤4: 测试切换公司状态")
            test_toggle_company_status(new_company_id)
    
    # 测试5: 再次获取公司列表验证变更
    logging.info("步骤5: 验证变更结果")
    test_get_companies()
    
    # 测试6: 前端集成测试
    logging.info("步骤6: 测试前端集成")
    test_frontend_integration()
    
    logging.info("=" * 60)
    logging.info("📊 测试完成")
    logging.info("=" * 60)
    
    logging.info("🎯 手动测试步骤：")
    logging.info("1. 打开 http://localhost:3000/#/new-supervision")
    logging.info("2. 点击'管理公司列'按钮")
    logging.info("3. 尝试添加新公司")
    logging.info("4. 尝试编辑现有公司")
    logging.info("5. 尝试调整公司显示顺序")
    logging.info("6. 点击'保存设置'")
    logging.info("7. 刷新页面，验证设置是否持久化")
    logging.info("8. 检查数据库中的companies表是否正确更新")
    
    logging.info("\n💡 数据库验证SQL：")
    logging.info("SELECT * FROM companies ORDER BY display_order;")

if __name__ == "__main__":
    main()
