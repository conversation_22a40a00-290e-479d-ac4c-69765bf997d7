#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接创建督办管理表
"""

import pymysql

def create_tables_direct():
    """直接创建表"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        print("✅ 数据库连接成功")
        
        with connection.cursor() as cursor:
            print("🚀 开始创建督办管理表...")
            
            # 1. 创建督办事项表
            print("📋 创建 supervision_items 表...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS supervision_items (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    sequence_number INT NOT NULL COMMENT '序号',
                    work_dimension VARCHAR(100) NOT NULL COMMENT '工作维度',
                    work_theme VARCHAR(200) NOT NULL COMMENT '工作主题',
                    supervision_source VARCHAR(100) NOT NULL COMMENT '督办来源',
                    work_content TEXT NOT NULL COMMENT '工作内容和完成标志',
                    completion_deadline VARCHAR(50) NOT NULL COMMENT '完成时限',
                    completion_date VARCHAR(50) NULL COMMENT '完成日期',
                    overall_progress VARCHAR(50) NOT NULL DEFAULT '未开始' COMMENT '整体进度',
                    is_overdue TINYINT DEFAULT 0 COMMENT '是否逾期',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='督办事项表'
            """)
            print("✅ supervision_items 表创建成功")
            
            # 2. 创建公司表
            print("🏢 创建 companies 表...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS companies (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    company_code VARCHAR(50) NOT NULL UNIQUE COMMENT '公司代码',
                    company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
                    display_order INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
                    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司表'
            """)
            print("✅ companies 表创建成功")
            
            # 3. 创建公司进度表
            print("📊 创建 company_progress 表...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS company_progress (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    supervision_item_id INT NOT NULL COMMENT '督办事项ID',
                    company_id INT NOT NULL COMMENT '公司ID',
                    status VARCHAR(50) NOT NULL DEFAULT '未开始' COMMENT '状态',
                    progress_description TEXT COMMENT '进展描述',
                    existing_problems TEXT COMMENT '存在问题',
                    next_plan TEXT COMMENT '下步计划',
                    updated_by VARCHAR(50) COMMENT '更新人',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY uk_item_company (supervision_item_id, company_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司进度表'
            """)
            print("✅ company_progress 表创建成功")
            
            # 提交创建表的操作
            connection.commit()
            
            # 4. 插入公司数据
            print("🏢 插入公司数据...")
            companies_data = [
                ('CXBX', '财险', 1),
                ('SXBX', '寿险', 2),
                ('JINZU', '金租', 3),
                ('ZICHAN', '资管', 4),
                ('GUANGZU', '广租', 5),
                ('TONGSHENG', '通盛', 6),
                ('DANBAO', '担保', 7),
                ('XIAODAI', '小贷', 8),
                ('BAOLI', '保理', 9),
                ('BUDONGCHAN', '不动产', 10),
                ('ZHENGXIN', '征信', 11),
                ('JINFU', '金服', 12),
                ('BENBU', '本部', 13)
            ]
            
            for company_code, company_name, display_order in companies_data:
                cursor.execute("""
                    INSERT IGNORE INTO companies (company_code, company_name, display_order)
                    VALUES (%s, %s, %s)
                """, (company_code, company_name, display_order))
            
            # 5. 插入督办事项数据
            print("📋 插入督办事项数据...")
            items_data = [
                (1, '一、监管和制度', '建立条线ITBP团队管理办法', '3月科技例会', 
                 '各单位及部门明确对接人，以条线形式建立ITBP服务团队。', 
                 '5月末', '已完成'),
                (2, '一、监管和制度', '建立项目红绿灯管理办法', '3月科技例会',
                 '开展重点项目周跟踪机制，推行"亮黄牌"机制。',
                 '4月末', '已完成'),
                (3, '一、监管和制度', '印发数据分级分类制度', '3月科技例会',
                 '各单位需对数据进行分级分类管理，保障基础数据的准确性和安全性。',
                 '7月末', '进行中')
            ]
            
            for item_data in items_data:
                cursor.execute("""
                    INSERT IGNORE INTO supervision_items 
                    (sequence_number, work_dimension, work_theme, supervision_source, work_content, completion_deadline, overall_progress)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, item_data)
            
            # 6. 创建公司进度记录
            print("📊 创建公司进度记录...")
            cursor.execute("SELECT id FROM supervision_items")
            items = cursor.fetchall()
            
            cursor.execute("SELECT id FROM companies WHERE is_active = 1")
            companies = cursor.fetchall()
            
            for item in items:
                for company in companies:
                    cursor.execute("""
                        INSERT IGNORE INTO company_progress (supervision_item_id, company_id, status)
                        VALUES (%s, %s, '未开始')
                    """, (item[0], company[0]))
            
            # 提交所有操作
            connection.commit()
            print("✅ 所有数据插入完成")
            
            # 验证创建结果
            cursor.execute("SELECT COUNT(*) FROM supervision_items")
            items_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM companies")
            companies_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM company_progress")
            progress_count = cursor.fetchone()[0]
            
            print(f"📊 创建结果:")
            print(f"   督办事项: {items_count} 条")
            print(f"   公司: {companies_count} 个")
            print(f"   进度记录: {progress_count} 条")
            
        connection.close()
        print("🎉 督办管理表创建完成！")
        
    except Exception as e:
        print(f"❌ 创建失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_tables_direct()
