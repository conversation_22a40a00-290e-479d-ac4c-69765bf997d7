body {padding-left: 96px;}
#menu {position: fixed; left: 0; top: 0; bottom: 0; width: 96px; z-index: 1015;}
.menu-show .menu-tip {display: none!important;}
#menuNav {position: absolute; top: 5px; bottom: 40px; width: 100%;}
#menu .nav > li {float: none; padding: 2px 8px;}
#menu .nav > li > a {color: #fff; line-height: 24px; padding: 6px; border-radius: 2px; white-space: nowrap; height: 36px;}
#menu .nav > li > a > .icon {display: inline-block; font-size: 16px; position: relative; top: -1px}
#menu .nav > li > a > .icon + .text {margin-left: 4px; position: relative; top: -1px}
#menu .nav > li.active > a, #menu .nav > li > a:hover, #menu .nav > li > a:focus {background-color: #3785ff;}
#menu .nav > li.divider {margin: 8px; background-color: #fff; opacity: 0.24; height: 1px; padding: 0 !important;}
#menuFooter {position: absolute; bottom: 0; left: 0; right: 0;}
#menu .nav > li.has-avatar {padding: 1px 6px;}
#menu .nav > li.has-avatar > a {padding: 4px 0; text-align: center;}
body.menu-hide {padding-left: 0;}
.menu-hide #menu {width: 40px;}
.menu-hide .menu-toggle .icon:before {content: '\e9f1';}
.menu-toggle .is-unfold, .menu-hide .menu-toggle .is-collapse {display: none;}
.menu-hide .menu-toggle .is-unfold {display: inline;}
#menuToggleItem,
.menu-hide #menuToggleItem {display: list-item;}
.menu-hide #menu .nav > li {padding: 2px 4px;}
.menu-hide #menu .nav > li > a {padding: 6px 4px; text-align: center;}
.menu-hide #menu .nav > li > a > .icon {top: 1px}
.menu-hide #menu .nav > li > a > .text {display: none;}
.menu-hide #menu .nav > li.has-avatar > a {padding: 2px 0;}
#menuMoreNav {display: none;}
.show-more-nav #menuMoreNav {display: block;}
#menuMoreList {left: 100%; margin-left: -2px; top: 0; background-color: #3c495c;}
#menuMoreList > li {padding: 2px 6px;}
#menuMoreList > li > a {color: #fff; padding: 4px 6px 4px 12px; border-radius: 2px; margin: 0;}
#menuMoreList > li > a > .icon {opacity: 1; font-size: 16px;}
#menuMoreList > li.divider {opacity: .2; margin: 6px; padding: 0;}
#menu .dropdown-hover:hover > a, #menu .dropdown.open > a {background-color: rgba(0,0,0,.3);}

#moreExecution {display: none; max-height: 520px; min-height: 520px; max-width: 360px; min-width: 150px; position: fixed; bottom: 40px; z-index: 999; background: white; padding: 20px 10px; border: 1px solid #efefef;}
.more-execution-show {left: 96px;}
.more-execution-hide {left: 40px;}
#moreExecution .icon-search {opacity: 0.5;}
#executionList {max-height: 450px; min-height: 450px; max-width: 360px; min-width: 150px; margin-top: 5px;}

@media screen and (max-height: 768px)
{
  #moreExecution {max-height: 340px; min-height: 340px;}
  #executionList {max-height: 270px; min-height: 270px;}
}

#apps {position: fixed; left: 96px; bottom: 0; right: 0; top: 0;}
.app-container {position: absolute; left: 0; bottom: 40px; right: 0; top: 0; background-color: #efefef;}
.menu-hide #apps {left: 40px;}
.app-container.loading:before {background-color: rgba(0,0,0,.1);}
.app-container.loading:before, .app-container.loading::after {transition-delay: 3s;}
.app-container.loading.open-from-hidden {transition-delay: 0s;}
.app-container.loading.open-from-hidden > iframe {opacity: 0;}
.app-container.loading.no-delay:before, .app-container.loading.no-delay::after {transition-delay: 0s !important;}

#appsBar {position: fixed; left: 96px; bottom: 0; right: 0; height: 40px; z-index: 1012; background: #fff; border-top: 1px solid #eff1f7;}
.menu-hide #appsBar {left: 40px;}
#bars {position: absolute; left: 0; top: 0; bottom: 0; padding: 0 5px;}
#bars > li {padding: 8px 2px;}
#bars > li > a {height: 24px; color: #000; padding: 2px 8px; line-height: 20px; border-radius: 2px;}
#bars > li.active > a {background: #fff!important;}
#bars > li > a:hover, #bars > li > a:active, #bars > li > a:focus {background: #16a8f8!important; color: #fff;}
#bars > li.active > a > span, #bars > li.active > a:active > span, #bars > li.active > a:focus > span {padding-bottom: 2px; font-weight: 600; border-bottom: 2px solid #16a8f8;}
#bars > li.active:after {content: ' '; display: block; position: absolute; height: 3px; left: 5px; right: 5px; bottom: 0; border-radius: 2px;}
#bars > li.divider{margin-right: 5px; margin-top: 10px; height: 20px; border-right: 1px solid #d9d9d9;}

#flodNav {float: none; margin: 0;}
#flodNav>li {text-align: center;}
#flodNav>li>.dropdown-menu {left: 100%; bottom: 0; top: auto; width: 140px; text-align: left; background: #3c495c;}
#flodNav>li>.dropdown-menu a {color: #fff;}
#flodNav>li>.dropdown-menu .dropdown-menu {left: 100%; text-align: left; background: #3c495c;}
#flodNav>li .top .dropdown-menu {top: -60px;}
#flodNav .dropdown-menu>li>a>.icon {position: relative; top: 0; display: inline;}
#flodNav li.open>a {background: #0c64eb;}
#flodNav li.divider {opacity: .24; border-top: 1px solid; height: 2px;}
#flodNav .dropdown-submenu:focus > a, #flodNav .dropdown-submenu:hover > a {background-color: rgba(0,0,0,.2);}
#poweredBy {width: 40%; position: absolute; top: 4px; right: 0; padding: 0;}

#globalSearchDiv {width: 200px; float: right; margin-right: 4px;}
#globalSearchDiv .input-group {width: 194px; float: right; margin-top: 2px;}
#searchbox .dropdown-menu.show-quick-go.with-active {min-width: 270px;}
#searchbox .dropdown-menu.show-quick-go > li {padding: 0 5px;}
#searchbox .dropdown-menu {min-width: 150px;}

#globalSearchInput {height: 28px; padding-left: 10px; border-right: 0px;}
#globalSearchButton {height: 28px;}
#globalSearchButton > i {position: relative; top: -2px}

#globalBarLogo {width: 100%; float: right;}
#globalBarLogo a:first-child {margin-right: 10px;}
#globalBarLogo a {float: right; margin: 0px 2px;}
#globalBarLogo a .icon {font-size: 24px; color: #16a8f8;}
#globalBarLogo a .version {font-size: 13px; position: relative; bottom: 3px;}

#searchbox a {float: left; width: 100%;}
#searchbox a:hover {color: white;}
#searchbox {width: 100%;}
#searchbox .dropdown-menu {margin-top: 0px; left: 1px; position: absolute; top: -50px;}
#searchbox .dropdown-menu > li > a {padding: 5px 0px 5px 2px;}
#searchbox .dropdown-menu > li {display: none;}
#searchbox .dropdown-menu > li.search-type-all {width: 100%; display: block;}
#searchbox .dropdown-menu > li:hover {position: relative;}
#searchbox .dropdown-menu.show-quick-go.with-active {position: absolute; top: initial; bottom: 4px; min-height: 318px; padding-top: 30px;}
#searchbox .dropdown-menu.show-quick-go > li {display: block;}
#searchbox .dropdown-menu.show-quick-go > li.active {width: 100%; position: absolute; top: 0; left: 0; right: 0;}
#searchbox .dropdown-menu>li.active>a, #searchbox .dropdown-menu>li.selected>a {position: relative; color: #fff; background-color: #16a8f8;}
#searchbox .dropdown-menu>li.selected>a:after {position: absolute; top: 2px; right: 4px; display: block; font-family: ZentaoIcon; font-size: 18px; font-size: 14px; font-style: normal; font-weight: 400; font-variant: normal; line-height: 1; line-height: 20px; text-transform: none; content: "\e5ca"; speak: none; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}

#upgradeContent {display: none; position: absolute; top: -330px; right: 10px; width: 65%; height: 322px; background-color: #fff; padding: 5px 0; border: 1px solid rgba(0,0,0,.15); border-color: rgba(0,0,0,.1); opacity: 1; border-radius: 4px; box-shadow: 0 6px 12px rgba(0,0,0,.12), 0 1px 3px rgba(0,0,0,.1);}
#latestVersionList {height: 270px; overflow: auto;}

.version-upgrade {width: 14px; height: 16px; float: left; background-repeat: no-repeat; background-position: center ; background-size: cover; display: block;}
.version-content {padding: 10px 15px;}
.version-footer {padding-top: 5px;}
.version-detail {color: #8a8a8a;}
.upgrade-now {float: right;}
.color-primary {color: #3280fc;}

@media screen and (max-height: 768px)
{
  #upgradeContent{width: 85%;}
}
#searchbox .dropdown-menu > li.search-type-all{max-width: 250px}
#searchbox .dropdown-menu > li.search-type-all a{width: 100%; overflow: hidden; text-overflow: ellipsis;}
