title: table zt_testtask
author: <PERSON>
version: "1.0"
fields:
  - field: product
    note: "所属产品"
    range: 1-10
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: begin
    note: "开始日期"
    range: "(M)-(w):60"
    type: timestamp
    prefix: ""
    postfix: ""
    loop: 0
    format: "YYYY-MM-DD"
  - field: end
    note: "结束日期"
    range: "(M)-(w):60"
    type: timestamp
    prefix: ""
    postfix: ""
    loop: 0
    format: "YYYY-MM-DD"
