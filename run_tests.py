#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
督办管理Excel功能完整测试套件
运行此脚本来执行所有测试并生成测试报告
"""

import subprocess
import sys
import os
import time
import json
from datetime import datetime
import webbrowser
from pathlib import Path

class TestRunner:
    def __init__(self):
        self.test_results = []
        self.start_time = datetime.now()
        
    def log(self, message, level="INFO"):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        
    def run_command(self, command, description, cwd=None):
        """运行命令并记录结果"""
        self.log(f"开始执行: {description}")
        self.log(f"命令: {command}")
        
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                cwd=cwd,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0:
                self.log(f"✅ {description} - 成功", "SUCCESS")
                self.test_results.append({
                    "name": description,
                    "success": True,
                    "output": result.stdout,
                    "error": result.stderr
                })
                return True
            else:
                self.log(f"❌ {description} - 失败 (退出码: {result.returncode})", "ERROR")
                self.log(f"错误输出: {result.stderr}", "ERROR")
                self.test_results.append({
                    "name": description,
                    "success": False,
                    "output": result.stdout,
                    "error": result.stderr
                })
                return False
                
        except subprocess.TimeoutExpired:
            self.log(f"❌ {description} - 超时", "ERROR")
            self.test_results.append({
                "name": description,
                "success": False,
                "output": "",
                "error": "测试超时"
            })
            return False
        except Exception as e:
            self.log(f"❌ {description} - 异常: {str(e)}", "ERROR")
            self.test_results.append({
                "name": description,
                "success": False,
                "output": "",
                "error": str(e)
            })
            return False
    
    def check_prerequisites(self):
        """检查测试前提条件"""
        self.log("检查测试前提条件...")
        
        # 检查Python环境
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
            self.log("❌ Python版本过低，需要Python 3.7+", "ERROR")
            return False
        
        self.log(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查必要的Python包
        required_packages = ['requests', 'pandas', 'openpyxl']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                self.log(f"✅ {package} 已安装")
            except ImportError:
                missing_packages.append(package)
                self.log(f"❌ {package} 未安装", "ERROR")
        
        if missing_packages:
            self.log(f"请安装缺失的包: pip install {' '.join(missing_packages)}", "ERROR")
            return False
        
        # 检查测试文件是否存在
        test_files = [
            'pmo-backend/test_excel_import_export.py',
            'test_frontend_excel.html'
        ]
        
        for file_path in test_files:
            if not os.path.exists(file_path):
                self.log(f"❌ 测试文件不存在: {file_path}", "ERROR")
                return False
            else:
                self.log(f"✅ 测试文件存在: {file_path}")
        
        return True
    
    def check_services(self):
        """检查服务是否运行"""
        self.log("检查服务状态...")
        
        import requests
        
        # 检查后端服务
        try:
            response = requests.get("http://localhost:8000/api/v1/new-supervision/items", timeout=5)
            if response.status_code == 200:
                self.log("✅ 后端服务运行正常")
                return True
            else:
                self.log(f"❌ 后端服务响应异常: {response.status_code}", "ERROR")
                return False
        except requests.exceptions.RequestException as e:
            self.log(f"❌ 无法连接到后端服务: {str(e)}", "ERROR")
            self.log("请确保后端服务已启动 (python -m uvicorn app.main:app --reload)", "ERROR")
            return False
    
    def run_backend_tests(self):
        """运行后端测试"""
        self.log("=" * 50)
        self.log("开始运行后端测试")
        self.log("=" * 50)
        
        return self.run_command(
            "python test_excel_import_export.py",
            "后端Excel导入导出功能测试",
            cwd="pmo-backend"
        )
    
    def run_database_update(self):
        """运行数据库更新脚本"""
        self.log("=" * 50)
        self.log("更新数据库结构")
        self.log("=" * 50)
        
        # 这里应该连接到数据库执行SQL，简化处理
        self.log("⚠️  请手动执行数据库更新脚本: pmo-backend/update_supervision_tables.sql", "WARNING")
        return True
    
    def open_frontend_test(self):
        """打开前端测试页面"""
        self.log("=" * 50)
        self.log("打开前端测试页面")
        self.log("=" * 50)
        
        test_file = Path("test_frontend_excel.html").absolute()
        if test_file.exists():
            try:
                webbrowser.open(f"file://{test_file}")
                self.log("✅ 前端测试页面已在浏览器中打开")
                self.log("请在浏览器中手动执行前端测试")
                return True
            except Exception as e:
                self.log(f"❌ 无法打开浏览器: {str(e)}", "ERROR")
                self.log(f"请手动打开文件: {test_file}")
                return False
        else:
            self.log("❌ 前端测试文件不存在", "ERROR")
            return False
    
    def generate_report(self):
        """生成测试报告"""
        self.log("=" * 50)
        self.log("生成测试报告")
        self.log("=" * 50)
        
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['success'])
        failed_tests = total_tests - passed_tests
        
        report = {
            "test_summary": {
                "start_time": self.start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration_seconds": duration.total_seconds(),
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0
            },
            "test_results": self.test_results
        }
        
        # 保存JSON报告
        with open("test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 生成HTML报告
        html_report = self.generate_html_report(report)
        with open("test_report.html", "w", encoding="utf-8") as f:
            f.write(html_report)
        
        # 打印控制台报告
        print("\n" + "=" * 60)
        print("📊 测试报告")
        print("=" * 60)
        print(f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总耗时: {duration.total_seconds():.1f} 秒")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {report['test_summary']['success_rate']:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['name']}")
                    if result['error']:
                        print(f"    错误: {result['error'][:100]}...")
        
        print(f"\n📄 详细报告已保存到: test_report.html")
        
        return failed_tests == 0
    
    def generate_html_report(self, report):
        """生成HTML格式的测试报告"""
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>督办管理Excel功能测试报告</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', sans-serif; margin: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }}
        .summary {{ background: #f9f9f9; padding: 20px; border-radius: 6px; margin-bottom: 20px; }}
        .test-result {{ margin: 10px 0; padding: 15px; border-radius: 6px; }}
        .success {{ background: #f0f9ff; border-left: 4px solid #67c23a; }}
        .failure {{ background: #fef0f0; border-left: 4px solid #f56c6c; }}
        .details {{ margin-top: 10px; font-family: monospace; background: #f5f5f5; padding: 10px; border-radius: 4px; }}
        h1, h2 {{ color: #333; }}
        .success-rate {{ font-size: 24px; font-weight: bold; color: #67c23a; }}
        .failure-rate {{ font-size: 24px; font-weight: bold; color: #f56c6c; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 督办管理Excel功能测试报告</h1>
        
        <div class="summary">
            <h2>测试概要</h2>
            <p><strong>开始时间:</strong> {report['test_summary']['start_time']}</p>
            <p><strong>结束时间:</strong> {report['test_summary']['end_time']}</p>
            <p><strong>总耗时:</strong> {report['test_summary']['duration_seconds']:.1f} 秒</p>
            <p><strong>总测试数:</strong> {report['test_summary']['total_tests']}</p>
            <p><strong>通过:</strong> {report['test_summary']['passed_tests']}</p>
            <p><strong>失败:</strong> {report['test_summary']['failed_tests']}</p>
            <p><strong>成功率:</strong> <span class="{'success-rate' if report['test_summary']['success_rate'] == 100 else 'failure-rate'}">{report['test_summary']['success_rate']:.1f}%</span></p>
        </div>
        
        <h2>详细结果</h2>
        """
        
        for result in report['test_results']:
            status_class = 'success' if result['success'] else 'failure'
            status_icon = '✅' if result['success'] else '❌'
            
            html += f"""
        <div class="test-result {status_class}">
            <h3>{status_icon} {result['name']}</h3>
            """
            
            if result['output']:
                html += f'<div class="details"><strong>输出:</strong><br>{result["output"]}</div>'
            
            if result['error']:
                html += f'<div class="details"><strong>错误:</strong><br>{result["error"]}</div>'
            
            html += "</div>"
        
        html += """
    </div>
</body>
</html>
        """
        
        return html
    
    def run_all_tests(self):
        """运行所有测试"""
        self.log("🚀 开始督办管理Excel功能完整测试")
        self.log("=" * 60)
        
        # 检查前提条件
        if not self.check_prerequisites():
            self.log("❌ 前提条件检查失败，测试终止", "ERROR")
            return False
        
        # 检查服务状态
        if not self.check_services():
            self.log("❌ 服务状态检查失败，测试终止", "ERROR")
            return False
        
        # 更新数据库结构
        self.run_database_update()
        
        # 运行后端测试
        backend_success = self.run_backend_tests()
        
        # 打开前端测试页面
        self.open_frontend_test()
        
        # 生成测试报告
        success = self.generate_report()
        
        if success and backend_success:
            self.log("🎉 所有自动化测试通过！请在浏览器中完成前端测试。", "SUCCESS")
        else:
            self.log("💥 部分测试失败，请查看测试报告。", "ERROR")
        
        return success

if __name__ == "__main__":
    runner = TestRunner()
    success = runner.run_all_tests()
    
    if success:
        print("\n🎉 测试完成！")
        exit(0)
    else:
        print("\n💥 测试失败！")
        exit(1)
