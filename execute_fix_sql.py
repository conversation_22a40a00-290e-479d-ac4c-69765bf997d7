#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行督办管理表修复SQL
"""

import pymysql

def execute_fix_sql():
    """执行修复SQL"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        print("✅ 数据库连接成功")
        
        with connection.cursor() as cursor:
            print("🔧 开始修复表结构...")
            
            # 1. 添加is_annual_assessment字段
            try:
                cursor.execute("""
                    ALTER TABLE supervision_items 
                    ADD COLUMN is_annual_assessment ENUM('是', '否') DEFAULT '否' COMMENT '是否年度绩效考核指标'
                """)
                print("✅ 添加 is_annual_assessment 字段成功")
            except Exception as e:
                if "Duplicate column name" in str(e):
                    print("✅ is_annual_assessment 字段已存在")
                else:
                    print(f"❌ 添加 is_annual_assessment 字段失败: {str(e)}")
            
            # 2. 添加progress_description字段
            try:
                cursor.execute("""
                    ALTER TABLE supervision_items 
                    ADD COLUMN progress_description TEXT COMMENT '7月28日进度情况'
                """)
                print("✅ 添加 progress_description 字段成功")
            except Exception as e:
                if "Duplicate column name" in str(e):
                    print("✅ progress_description 字段已存在")
                else:
                    print(f"❌ 添加 progress_description 字段失败: {str(e)}")
            
            # 3. 修改overall_progress字段为ENUM类型
            try:
                cursor.execute("""
                    ALTER TABLE supervision_items 
                    MODIFY COLUMN overall_progress ENUM('X 未启动', 'O进行中', '！逾期', '√ 已完成', '— 不需要执行') 
                    DEFAULT 'X 未启动' COMMENT '整体进度'
                """)
                print("✅ 修改 overall_progress 字段类型成功")
            except Exception as e:
                print(f"❌ 修改 overall_progress 字段类型失败: {str(e)}")
            
            # 4. 修改company_supervision_status表的status字段
            try:
                cursor.execute("""
                    ALTER TABLE company_supervision_status 
                    MODIFY COLUMN status ENUM('√', 'O', '！', 'X', '—') DEFAULT 'X' 
                    COMMENT '状态：√已完成 O进行中 ！逾期 X未启动 —不需要执行'
                """)
                print("✅ 修改 company_supervision_status.status 字段类型成功")
            except Exception as e:
                print(f"❌ 修改 company_supervision_status.status 字段类型失败: {str(e)}")
            
            # 5. 更新现有数据
            try:
                # 更新is_annual_assessment字段
                cursor.execute("UPDATE supervision_items SET is_annual_assessment = '是' WHERE sequence_number IN (5, 6, 7, 8)")
                cursor.execute("UPDATE supervision_items SET is_annual_assessment = '否' WHERE is_annual_assessment IS NULL")
                print("✅ 更新 is_annual_assessment 数据成功")
                
                # 更新overall_progress字段
                cursor.execute("UPDATE supervision_items SET overall_progress = '√ 已完成' WHERE overall_progress IN ('已完成', '√已完成')")
                cursor.execute("UPDATE supervision_items SET overall_progress = 'O进行中' WHERE overall_progress IN ('进行中', 'O进行中')")
                cursor.execute("UPDATE supervision_items SET overall_progress = '！逾期' WHERE overall_progress IN ('逾期', '！逾期')")
                cursor.execute("UPDATE supervision_items SET overall_progress = 'X 未启动' WHERE overall_progress IN ('未启动', 'X未启动', '') OR overall_progress IS NULL")
                cursor.execute("UPDATE supervision_items SET overall_progress = '— 不需要执行' WHERE overall_progress IN ('不需要执行', '—不需要执行')")
                print("✅ 更新 overall_progress 数据成功")
                
            except Exception as e:
                print(f"❌ 更新数据失败: {str(e)}")
            
            connection.commit()
            print("✅ 所有修复操作完成")
            
            # 检查修复结果
            print("\n📋 检查修复结果:")
            cursor.execute("DESCRIBE supervision_items")
            columns = cursor.fetchall()
            
            required_fields = ['is_annual_assessment', 'progress_description', 'overall_progress']
            for field in required_fields:
                found = any(col[0] == field for col in columns)
                if found:
                    print(f"   ✅ {field} 字段存在")
                else:
                    print(f"   ❌ {field} 字段不存在")
            
            # 显示数据样本
            cursor.execute("SELECT id, sequence_number, work_theme, is_annual_assessment, overall_progress FROM supervision_items LIMIT 5")
            rows = cursor.fetchall()
            
            print(f"\n📝 数据样本:")
            for row in rows:
                print(f"   {row[1]}. {row[2]} - 考核指标:{row[3]} - 进度:{row[4]}")
        
        connection.close()
        print("🎉 表结构修复完成！")
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    execute_fix_sql()
