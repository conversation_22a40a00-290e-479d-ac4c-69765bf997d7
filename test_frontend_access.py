#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端页面是否正常访问
"""

import requests
import time

def test_frontend_access():
    """测试前端页面访问"""
    print("🌐 测试前端页面访问...")
    print("=" * 50)
    
    # 测试前端页面
    frontend_url = "http://localhost:3000"
    
    try:
        print(f"📡 测试前端首页: {frontend_url}")
        response = requests.get(frontend_url, timeout=10)
        
        if response.status_code == 200:
            print(f"   ✅ 前端首页访问成功 (状态码: {response.status_code})")
            
            # 检查是否包含Vue应用的标识
            if 'id="app"' in response.text or 'vite' in response.text.lower():
                print(f"   ✅ 检测到Vue应用结构")
            else:
                print(f"   ⚠️  未检测到Vue应用结构")
                
        else:
            print(f"   ❌ 前端首页访问失败 (状态码: {response.status_code})")
            
    except requests.exceptions.ConnectionError:
        print(f"   ❌ 无法连接到前端服务，请检查服务是否启动")
    except Exception as e:
        print(f"   ❌ 前端访问测试失败: {e}")
    
    # 测试工单集成页面
    ticket_url = f"{frontend_url}/ticket-integration"
    
    try:
        print(f"\n📡 测试工单集成页面: {ticket_url}")
        response = requests.get(ticket_url, timeout=10)
        
        if response.status_code == 200:
            print(f"   ✅ 工单集成页面访问成功 (状态码: {response.status_code})")
        else:
            print(f"   ❌ 工单集成页面访问失败 (状态码: {response.status_code})")
            
    except requests.exceptions.ConnectionError:
        print(f"   ❌ 无法连接到前端服务")
    except Exception as e:
        print(f"   ❌ 工单集成页面访问测试失败: {e}")

def test_backend_health():
    """测试后端健康状态"""
    print("\n🔧 测试后端健康状态...")
    print("=" * 50)
    
    backend_url = "http://localhost:8000"
    
    try:
        print(f"📡 测试后端健康检查: {backend_url}")
        response = requests.get(backend_url, timeout=10)
        
        if response.status_code == 200:
            print(f"   ✅ 后端服务正常 (状态码: {response.status_code})")
        else:
            print(f"   ⚠️  后端响应异常 (状态码: {response.status_code})")
            
    except requests.exceptions.ConnectionError:
        print(f"   ❌ 无法连接到后端服务，请检查服务是否启动")
    except Exception as e:
        print(f"   ❌ 后端健康检查失败: {e}")
    
    # 测试API文档
    docs_url = f"{backend_url}/docs"
    
    try:
        print(f"\n📡 测试API文档: {docs_url}")
        response = requests.get(docs_url, timeout=10)
        
        if response.status_code == 200:
            print(f"   ✅ API文档访问成功 (状态码: {response.status_code})")
        else:
            print(f"   ❌ API文档访问失败 (状态码: {response.status_code})")
            
    except Exception as e:
        print(f"   ❌ API文档访问测试失败: {e}")

def main():
    """主函数"""
    print("🎯 前后端服务状态检查")
    print("=" * 80)
    
    # 等待服务完全启动
    print("⏳ 等待服务完全启动...")
    time.sleep(2)
    
    # 测试后端
    test_backend_health()
    
    # 测试前端
    test_frontend_access()
    
    print("\n" + "=" * 80)
    print("🎉 服务状态检查完成！")
    print("💡 如果所有测试都通过，可以在浏览器中访问:")
    print("   前端应用: http://localhost:3000")
    print("   工单集成: http://localhost:3000/ticket-integration")
    print("   API文档: http://localhost:8000/docs")
    print("=" * 80)

if __name__ == "__main__":
    main()
