title: table zt_casestep
desc: "用例步骤"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: "1-400"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: parent
    note: "上级ID"
    range: "0"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: case
    note: "所属用例ID"
    range: "1-400"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: version
    note: "用例版本"
    range: "1"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "用例类型"
    range: "step"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: desc
    note: "用例步骤"
    range: "1-10000"
    prefix: "用例步骤描述"
    postfix: ""
    loop: 0
    format: ""
  - field: expect
    note: "预期"
    range: "1-10000"
    prefix: "这是用例预期结果"
    postfix: ""
    loop: 0
    format: ""
