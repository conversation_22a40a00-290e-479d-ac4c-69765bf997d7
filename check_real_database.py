#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查真实的数据库结构和数据
"""

import pymysql
import os

def check_database_config():
    """检查数据库配置"""
    print("🔍 检查数据库配置...")
    
    db_config = {
        'host': os.environ.get('DB_HOST', 'localhost'),
        'port': int(os.environ.get('DB_PORT', '3306')),
        'user': os.environ.get('DB_USER', 'root'),
        'password': os.environ.get('DB_PASSWORD', ''),
        'database': os.environ.get('DB_NAME', 'kanban2'),
        'charset': 'utf8mb4'
    }
    
    print(f"数据库配置:")
    for key, value in db_config.items():
        if key == 'password':
            print(f"   {key}: {'*' * len(str(value)) if value else '(空)'}")
        else:
            print(f"   {key}: {value}")
    
    return db_config

def check_all_tables(db_config):
    """检查所有表"""
    print("\n📋 检查所有表...")
    
    try:
        connection = pymysql.connect(**db_config)
        
        with connection.cursor() as cursor:
            # 显示所有表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            print(f"数据库中共有 {len(tables)} 个表:")
            for table in tables:
                table_name = table[0]
                print(f"   📝 {table_name}")
                
                # 检查表的数据量
                cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                count = cursor.fetchone()['count']
                print(f"      数据量: {count} 条")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查表失败: {str(e)}")
        return False

def check_supervision_tables(db_config):
    """检查督办相关表"""
    print("\n🔍 检查督办相关表...")
    
    try:
        connection = pymysql.connect(**db_config)
        
        with connection.cursor() as cursor:
            # 检查督办相关表
            supervision_tables = [
                'supervision_items', 'companies', 'company_progress', 
                'company_supervision_status', 'supervision_status_history'
            ]
            
            for table_name in supervision_tables:
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                if cursor.fetchone():
                    print(f"✅ {table_name} 表存在")
                    
                    # 显示表结构
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = cursor.fetchall()
                    print(f"   表结构:")
                    for column in columns:
                        print(f"      {column['Field']} ({column['Type']}) - {column['Null']} - {column['Default']}")
                    
                    # 显示数据量
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    count = cursor.fetchone()['count']
                    print(f"   数据量: {count} 条")
                    
                    # 显示前几条数据
                    if count > 0:
                        cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                        rows = cursor.fetchall()
                        print(f"   前3条数据:")
                        for i, row in enumerate(rows):
                            print(f"      {i+1}. {dict(row)}")
                    
                    print()
                else:
                    print(f"❌ {table_name} 表不存在")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查督办表失败: {str(e)}")
        return False

def check_existing_supervision_data(db_config):
    """检查现有的督办数据"""
    print("\n📊 检查现有的督办数据...")
    
    try:
        connection = pymysql.connect(**db_config)
        
        with connection.cursor() as cursor:
            # 查找可能存在的督办相关表
            cursor.execute("SHOW TABLES")
            all_tables = cursor.fetchall()
            
            supervision_related = []
            for table in all_tables:
                table_name = table[0]
                if any(keyword in table_name.lower() for keyword in ['supervision', 'company', 'progress', 'item']):
                    supervision_related.append(table_name)
            
            if supervision_related:
                print(f"找到可能相关的表:")
                for table_name in supervision_related:
                    print(f"   📝 {table_name}")
                    
                    # 检查表结构
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = cursor.fetchall()
                    print(f"      字段: {[col['Field'] for col in columns]}")
                    
                    # 检查数据量
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    count = cursor.fetchone()['count']
                    print(f"      数据量: {count} 条")
                    
                    if count > 0 and count <= 10:
                        cursor.execute(f"SELECT * FROM {table_name}")
                        rows = cursor.fetchall()
                        print(f"      所有数据:")
                        for i, row in enumerate(rows):
                            print(f"         {i+1}. {dict(row)}")
                    
                    print()
            else:
                print("没有找到督办相关的表")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查现有督办数据失败: {str(e)}")
        return False

def test_database_connection(db_config):
    """测试数据库连接"""
    print("\n🔗 测试数据库连接...")
    
    try:
        connection = pymysql.connect(**db_config)
        print("✅ 数据库连接成功")
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"   MySQL版本: {version}")
            
            cursor.execute("SELECT DATABASE()")
            database = cursor.fetchone()
            print(f"   当前数据库: {database}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 检查真实的数据库结构和数据")
    print("=" * 60)
    
    # 检查数据库配置
    db_config = check_database_config()
    
    # 测试数据库连接
    if not test_database_connection(db_config):
        print("❌ 数据库连接失败，无法继续检查")
        return
    
    # 检查所有表
    check_all_tables(db_config)
    
    # 检查督办相关表
    check_supervision_tables(db_config)
    
    # 检查现有的督办数据
    check_existing_supervision_data(db_config)
    
    print("\n" + "=" * 60)
    print("🏁 检查完成")
    print("\n📋 下一步:")
    print("   1. 根据实际的数据库结构调整API")
    print("   2. 如果表不存在，创建正确的表结构")
    print("   3. 如果表存在但结构不对，修改表结构")
    print("   4. 插入测试数据")
    print("   5. 测试API功能")

if __name__ == "__main__":
    main()
