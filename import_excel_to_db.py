#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从Excel导入督办数据到数据库
"""

import pymysql
import pandas as pd
import os
import sys
from datetime import datetime

def import_supervision_data(filename):
    """导入督办数据"""
    print(f"📊 从Excel导入督办数据: {filename}")
    
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return False
    
    try:
        # 读取Excel文件
        print("📄 读取Excel文件...")
        df = pd.read_excel(filename)
        
        print(f"读取到 {len(df)} 行数据")
        print(f"表头: {list(df.columns)}")
        
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            database='kanban2',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 1. 获取公司映射
        print("🏢 获取公司映射...")
        cursor.execute("SELECT id, company_name FROM companies WHERE is_active = TRUE")
        companies = cursor.fetchall()
        company_map = {company[1]: company[0] for company in companies}
        
        # 2. 更新督办事项
        print("📋 更新督办事项...")
        updated_items = 0
        
        for _, row in df.iterrows():
            seq_num = int(row['序号'])
            
            # 检查督办事项是否存在
            cursor.execute("SELECT id FROM supervision_items WHERE sequence_number = %s", (seq_num,))
            item_result = cursor.fetchone()
            
            if item_result:
                # 更新现有督办事项
                item_id = item_result[0]
                
                update_query = """
                UPDATE supervision_items 
                SET work_dimension = %s, 
                    work_theme = %s, 
                    supervision_source = %s, 
                    work_content = %s, 
                    is_annual_assessment = %s, 
                    completion_deadline = %s, 
                    overall_progress = %s,
                    updated_at = NOW()
                WHERE id = %s
                """
                
                cursor.execute(update_query, (
                    row['工作维度'],
                    row['工作主题'],
                    row['督办来源'],
                    row['工作内容和完成标志'],
                    row['是否年度绩效考核指标'],
                    row['完成时限'],
                    row['整体进度'],
                    item_id
                ))
                
                updated_items += 1
                
                # 3. 更新公司状态
                for company_name, company_id in company_map.items():
                    if company_name in df.columns:
                        status = str(row[company_name])
                        
                        # 检查状态是否存在
                        cursor.execute("""
                            SELECT id FROM company_supervision_status 
                            WHERE supervision_item_id = %s AND company_id = %s
                        """, (item_id, company_id))
                        
                        status_result = cursor.fetchone()
                        
                        if status_result:
                            # 更新现有状态
                            cursor.execute("""
                                UPDATE company_supervision_status 
                                SET status = %s, updated_by = 'system', updated_at = NOW()
                                WHERE supervision_item_id = %s AND company_id = %s
                            """, (status, item_id, company_id))
                        else:
                            # 创建新状态
                            cursor.execute("""
                                INSERT INTO company_supervision_status 
                                (supervision_item_id, company_id, status, updated_by, created_at, updated_at)
                                VALUES (%s, %s, %s, 'system', NOW(), NOW())
                            """, (item_id, company_id, status))
            else:
                print(f"⚠️ 督办事项不存在: 序号 {seq_num}")
        
        connection.commit()
        connection.close()
        
        print(f"✅ 更新了 {updated_items} 个督办事项")
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 Excel督办数据导入工具")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("❌ 请提供Excel文件路径")
        print("用法: python import_excel_to_db.py <Excel文件路径>")
        return
    
    filename = sys.argv[1]
    success = import_supervision_data(filename)
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 导入成功！")
        print("\n📋 导入内容:")
        print("   • 督办事项基本信息")
        print("   • 各公司状态数据")
        
        print("\n📝 下一步操作:")
        print("   1. 刷新督办管理页面")
        print("   2. 验证数据是否正确更新")
    else:
        print("❌ 导入失败！")
    
    print("\n🏁 导入完成")

if __name__ == "__main__":
    main()
