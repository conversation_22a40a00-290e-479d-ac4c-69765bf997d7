<?php
if(!isset($lang->holiday)) $lang->holiday = new stdclass();
$lang->holiday->common = 'Holiday';
$lang->holiday->browse = 'Browse';
$lang->holiday->create = 'Create';
$lang->holiday->edit   = 'Edit';
$lang->holiday->delete = 'Delete';

$lang->holiday->createAction = 'Create Holiday';
$lang->holiday->editAction   = 'Edit Holiday';
$lang->holiday->deleteAction = 'Delete Holiday';
$lang->holiday->importAction = 'Import Holiday';

$lang->holiday->id    = 'ID';
$lang->holiday->name  = 'Name';
$lang->holiday->desc  = 'Description';
$lang->holiday->type  = 'Type';
$lang->holiday->begin = 'Begin';
$lang->holiday->end   = 'End';
$lang->holiday->all   = 'All';

$lang->holiday->holiday   = 'Holiday';
$lang->holiday->checkYear = 'Year selection';

$lang->holiday->typeList['holiday'] = 'Holiday';
$lang->holiday->typeList['working'] = 'Working Day';

$lang->holiday->emptyTip      = 'No Holiday';
$lang->holiday->confirmDelete = 'Confirm removal of holidays?';
$lang->holiday->importTip     = '';
