<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Treemap - Zentao</title>
<link rel="stylesheet" href="../../../theme/zui/css/min.css" />
<script src="../../jquery/lib.js"></script>
<script src="../min.js"></script>
<script src="./min.js"></script>
<link rel="stylesheet" href="./min.css" />
<style>
#treemap,body,html {width: 100%; height: 100%; background: none;}
#treemap .treemap-node-fold-icon {font-size: 12px;}
#treemap .treemap-node-fold-icon:before {content: '\e6f1'; left: 0;}
#treemap .treemap-node.collapsed .treemap-node-fold-icon:before {content: '\e6f2'}
</style>
</head>
<body>
<div id="treemap"></div>
<script>
window.parent.$(function()
{
    $('#treemap').treemap($.extend({}, window.parent[$.getSearchParam('options')]));
    const treemap = $('#treemap').data('zui.treemap');
    window.parent.getTreemap = function() {return treemap;};
    if(treemap.options.onReady) treemap.options.onReady(treemap, window);
    $('body').on('click', () => window.parent.$(window.frameElement).trigger('click'));
    if(treemap.options.onContextMenu)
    {
        treemap.$nodes.on('contextmenu', function(e)
        {
            const $node = $(e.target).closest('.treemap-node');
            const node = $node.data('node');
            const frameBounding = window.frameElement.getBoundingClientRect();
            const clientX = e.clientX + frameBounding.left;
            const clientY = e.clientY + frameBounding.top;
            let result = treemap.options.onContextMenu({node: node, clientX: clientX, clientY: clientY, treemap: treemap, event: e});
            if(!result) return;
            if(Array.isArray(result)) result = {items: result};
            window.parent.zui.ContextMenu.show($.extend(
            {
                element: {getBoundingClientRect: () => ({x: clientX, y: clientY, width: 0, height: 0, left: clientX, top: clientY})},
            }, result));
            e.preventDefault();
            return false;
        });
    }
});
</script>
</body>
</html>
