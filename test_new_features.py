#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工单集成新功能测试脚本
测试所有新增的API端点和功能
"""

import requests
import json
import sys
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/ticket-integration"

# 测试用的JWT token (需要根据实际情况调整)
TEST_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0X3VzZXIiLCJleHAiOjk5OTk5OTk5OTl9.test"

def make_request(endpoint, method="GET", data=None, params=None):
    """发送HTTP请求"""
    url = f"{API_BASE}{endpoint}"
    headers = {
        "Authorization": f"Bearer {TEST_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers, params=params)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")
        
        return response
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_dashboard_stats():
    """测试仪表盘统计"""
    print("\n🔍 测试仪表盘统计...")
    response = make_request("/dashboard-stats")
    
    if response and response.status_code == 200:
        data = response.json()
        if data.get('success'):
            stats = data.get('data', {})
            print(f"✅ 仪表盘统计获取成功")
            print(f"   项目统计: {stats.get('project_stats', {})}")
            print(f"   工单统计: {stats.get('ticket_stats', {})}")
            return True
        else:
            print(f"❌ 仪表盘统计获取失败: {data.get('message')}")
    else:
        print(f"❌ 仪表盘统计请求失败: {response.status_code if response else 'No response'}")
    
    return False

def test_tickets_by_status():
    """测试按状态获取工单"""
    print("\n🔍 测试按状态获取工单...")
    
    statuses = [
        ("all", "全部工单"),
        ("completed", "已完成工单"),
        ("urgent", "紧急工单")
    ]
    
    success_count = 0
    for status, desc in statuses:
        print(f"   测试 {desc}...")
        response = make_request("/tickets/by-status", params={"status": status, "limit": 10})
        
        if response and response.status_code == 200:
            data = response.json()
            if data.get('success'):
                tickets = data.get('data', {}).get('tickets', [])
                print(f"   ✅ {desc} 获取成功，共 {len(tickets)} 条记录")
                if tickets:
                    ticket = tickets[0]
                    print(f"      示例: {ticket.get('feelec_ticket_no')} - {ticket.get('feelec_title', '')[:30]}...")
                    print(f"      发布人: {ticket.get('publisher_name', '未知')}")
                    print(f"      处理人: {ticket.get('processor_name', '未知')}")
                success_count += 1
            else:
                print(f"   ❌ {desc} 获取失败: {data.get('message')}")
        else:
            print(f"   ❌ {desc} 请求失败: {response.status_code if response else 'No response'}")
    
    return success_count == len(statuses)

def test_users_list():
    """测试用户列表"""
    print("\n🔍 测试用户列表...")
    response = make_request("/users", params={"limit": 10})
    
    if response and response.status_code == 200:
        data = response.json()
        if data.get('success'):
            users = data.get('data', {}).get('users', [])
            print(f"✅ 用户列表获取成功，共 {len(users)} 个用户")
            if users:
                user = users[0]
                print(f"   示例用户: {user.get('feelec_name')} - 工单总数: {user.get('total_tickets')}")
                print(f"   完成率: {user.get('completion_rate')}%")
                print(f"   所属公司: {user.get('company_name', '未知')}")
                return user.get('feelec_user_id')  # 返回用户ID用于后续测试
            return True
        else:
            print(f"❌ 用户列表获取失败: {data.get('message')}")
    else:
        print(f"❌ 用户列表请求失败: {response.status_code if response else 'No response'}")
    
    return False

def test_companies_list():
    """测试主体列表"""
    print("\n🔍 测试主体列表...")
    response = make_request("/companies", params={"limit": 10})
    
    if response and response.status_code == 200:
        data = response.json()
        if data.get('success'):
            companies = data.get('data', {}).get('companies', [])
            print(f"✅ 主体列表获取成功，共 {len(companies)} 个主体")
            if companies:
                company = companies[0]
                print(f"   示例主体: {company.get('feelec_name')} - 工单总数: {company.get('total_tickets')}")
                print(f"   完成率: {company.get('completion_rate')}%")
                print(f"   联系人: {company.get('feelec_contact', '未知')}")
                return company.get('feelec_company_id')  # 返回主体ID用于后续测试
            return True
        else:
            print(f"❌ 主体列表获取失败: {data.get('message')}")
    else:
        print(f"❌ 主体列表请求失败: {response.status_code if response else 'No response'}")
    
    return False

def test_user_tickets(user_id):
    """测试用户工单列表"""
    if not user_id:
        print("\n⚠️  跳过用户工单测试 - 没有有效的用户ID")
        return False
    
    print(f"\n🔍 测试用户工单列表 (用户ID: {user_id})...")
    response = make_request(f"/users/{user_id}/tickets")
    
    if response and response.status_code == 200:
        data = response.json()
        if data.get('success'):
            tickets = data.get('data', {}).get('tickets', [])
            print(f"✅ 用户工单列表获取成功，共 {len(tickets)} 条工单")
            if tickets:
                ticket = tickets[0]
                print(f"   示例工单: {ticket.get('feelec_ticket_no')} - {ticket.get('feelec_title', '')[:30]}...")
                print(f"   状态: {ticket.get('status_name', '未知')}")
                print(f"   优先级: {ticket.get('priority_text', '未知')}")
            return True
        else:
            print(f"❌ 用户工单列表获取失败: {data.get('message')}")
    else:
        print(f"❌ 用户工单列表请求失败: {response.status_code if response else 'No response'}")
    
    return False

def test_company_tickets(company_id):
    """测试主体工单列表"""
    if not company_id:
        print("\n⚠️  跳过主体工单测试 - 没有有效的主体ID")
        return False
    
    print(f"\n🔍 测试主体工单列表 (主体ID: {company_id})...")
    response = make_request(f"/companies/{company_id}/tickets")
    
    if response and response.status_code == 200:
        data = response.json()
        if data.get('success'):
            tickets = data.get('data', {}).get('tickets', [])
            print(f"✅ 主体工单列表获取成功，共 {len(tickets)} 条工单")
            if tickets:
                ticket = tickets[0]
                print(f"   示例工单: {ticket.get('feelec_ticket_no')} - {ticket.get('feelec_title', '')[:30]}...")
                print(f"   发布人: {ticket.get('publisher_name', '未知')}")
                print(f"   处理人: {ticket.get('processor_name', '未知')}")
                print(f"   状态: {ticket.get('status_name', '未知')}")
            return True
        else:
            print(f"❌ 主体工单列表获取失败: {data.get('message')}")
    else:
        print(f"❌ 主体工单列表请求失败: {response.status_code if response else 'No response'}")
    
    return False

def test_projects_with_names():
    """测试项目列表（包含名称显示）"""
    print("\n🔍 测试项目列表（验证名称显示）...")
    response = make_request("/projects", params={"limit": 5})
    
    if response and response.status_code == 200:
        data = response.json()
        if data.get('success'):
            projects = data.get('data', {}).get('projects', [])
            print(f"✅ 项目列表获取成功，共 {len(projects)} 个项目")
            if projects:
                project = projects[0]
                print(f"   示例项目: {project.get('feelec_name')}")
                print(f"   项目经理: {project.get('manager_name', '未指定')}")
                print(f"   所属部门: {project.get('department_name', '未指定')}")
                print(f"   创建者: {project.get('creator_name', '未指定')}")
                print(f"   工单总数: {project.get('total_tickets', 0)}")
                print(f"   完成率: {project.get('completion_rate', 0)}%")
            return True
        else:
            print(f"❌ 项目列表获取失败: {data.get('message')}")
    else:
        print(f"❌ 项目列表请求失败: {response.status_code if response else 'No response'}")
    
    return False

def main():
    """主测试函数"""
    print("🚀 开始测试工单集成新功能...")
    print(f"📍 测试目标: {BASE_URL}")
    print("=" * 60)
    
    # 测试计数
    total_tests = 0
    passed_tests = 0
    
    # 1. 测试仪表盘统计
    total_tests += 1
    if test_dashboard_stats():
        passed_tests += 1
    
    # 2. 测试项目列表（包含名称显示）
    total_tests += 1
    if test_projects_with_names():
        passed_tests += 1
    
    # 3. 测试按状态获取工单
    total_tests += 1
    if test_tickets_by_status():
        passed_tests += 1
    
    # 4. 测试用户列表
    total_tests += 1
    user_id = test_users_list()
    if user_id:
        passed_tests += 1
    
    # 5. 测试主体列表
    total_tests += 1
    company_id = test_companies_list()
    if company_id:
        passed_tests += 1
    
    # 6. 测试用户工单列表
    total_tests += 1
    if test_user_tickets(user_id):
        passed_tests += 1
    
    # 7. 测试主体工单列表
    total_tests += 1
    if test_company_tickets(company_id):
        passed_tests += 1
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   失败测试: {total_tests - passed_tests}")
    print(f"   成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！工单集成新功能正常工作。")
        return 0
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 个测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
