import request from '@/utils/request'

// 新督办管理API

/**
 * 获取督办事项列表
 */
export function getSupervisionItems() {
  return request({
    url: '/new-supervision/items',
    method: 'get'
  })
}

/**
 * 创建督办事项
 * @param {Object} data - 督办事项数据
 */
export function createSupervisionItem(data) {
  return request({
    url: '/new-supervision/items',
    method: 'post',
    data
  })
}

/**
 * 更新督办事项
 * @param {number} id - 督办事项ID
 * @param {Object} data - 更新数据
 */
export function updateSupervisionItem(id, data) {
  return request({
    url: `/new-supervision/items/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除督办事项
 * @param {number} id - 督办事项ID
 */
export function deleteSupervisionItem(id) {
  return request({
    url: `/new-supervision/items/${id}`,
    method: 'delete'
  })
}

/**
 * 更新公司状态
 * @param {Object} data - 状态更新数据
 */
export function updateCompanyStatus(data) {
  return request({
    url: '/new-supervision/status',
    method: 'put',
    data
  })
}

/**
 * 获取公司列表
 */
export function getCompanies() {
  return request({
    url: '/new-supervision/companies',
    method: 'get'
  })
}

/**
 * 获取进度详情
 * @param {number} itemId - 督办事项ID
 * @param {number} companyId - 公司ID
 */
export function getProgressDetail(itemId, companyId) {
  return request({
    url: `/new-supervision/progress-detail/${itemId}/${companyId}`,
    method: 'get'
  })
}

/**
 * 获取状态变更历史
 * @param {number} itemId - 督办事项ID
 * @param {number} companyId - 公司ID
 */
export function getStatusHistory(itemId, companyId) {
  return request({
    url: `/new-supervision/status-history/${itemId}/${companyId}`,
    method: 'get'
  })
}

/**
 * 导出Excel表格
 */
export function exportSupervisionExcel() {
  return request({
    url: '/new-supervision/export-excel',
    method: 'get',
    responseType: 'blob',
    timeout: 30000  // 30秒超时
  })
}

/**
 * 导入Excel表格
 * @param {File} file - Excel文件
 */
export function importSupervisionExcel(file) {
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: '/new-supervision/import-excel',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// ==================== 公司管理API ====================

/**
 * 创建新公司
 * @param {Object} data - 公司数据
 */
export function createCompany(data) {
  return request({
    url: '/supervision/companies',
    method: 'post',
    data
  })
}

/**
 * 更新公司信息
 * @param {number} id - 公司ID
 * @param {Object} data - 更新数据
 */
export function updateCompany(id, data) {
  return request({
    url: `/supervision/companies/${id}`,
    method: 'put',
    data
  })
}

/**
 * 启用/禁用公司
 * @param {number} id - 公司ID
 */
export function toggleCompanyStatus(id) {
  return request({
    url: `/supervision/companies/${id}/toggle`,
    method: 'patch'
  })
}

/**
 * 删除公司
 * @param {number} id - 公司ID
 */
export function deleteCompany(id) {
  return request({
    url: `/supervision/companies/${id}`,
    method: 'delete'
  })
}

// 导出API对象
export const newSupervisionApi = {
  getItems: getSupervisionItems,
  createItem: createSupervisionItem,
  updateItem: updateSupervisionItem,
  deleteItem: deleteSupervisionItem,
  updateStatus: updateCompanyStatus,
  getCompanies: getCompanies,
  getProgressDetail: getProgressDetail,
  getStatusHistory: getStatusHistory,
  exportExcel: exportSupervisionExcel,
  importExcel: importSupervisionExcel,
  // 公司管理
  createCompany: createCompany,
  updateCompany: updateCompany,
  toggleCompanyStatus: toggleCompanyStatus,
  deleteCompany: deleteCompany
}
