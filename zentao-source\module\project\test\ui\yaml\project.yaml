title: project.
desc: project data.
author: <PERSON>
version: "1.0"
fields:
  - field: id
    range: 1
  - field: name
    note: "名称"
    fields:
    - field: name
      range: 敏捷项目1
  - field: project
    range: 0
  - field: code
    prefix: "code"
    range: 1
  - field: model
    range: scrum
  - field: type
    range: project
  - field: budget
    range: 800000-1:100
  - field: status
    range: wait
  - field: begin
    range: "(-2M)-(+M):1D"
    type: timestamp
    format: "YYYY-MM-DD"
    postfix: "\t"
  - field: end
    range: "(+1w)-(+2M):1D"
    type: timestamp
    format: "YYYY-MM-DD"
    postfix: "\t"
  - field: grade
    range: 1
  - field: parent
    range: 0
  - field: path
    prefix: ","
    range: 1
    postfix: ","
  - field: multiple
    range: 1
