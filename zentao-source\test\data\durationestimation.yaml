title: table zt_durationestimation
desc: ""
author: automated export
version: "1.0"
fields:
  - field: id
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: program
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: stage
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: workload
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: worktimeRate
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: people
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: startDate
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: endDate
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: createdBy
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: createdDate
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: editedBy
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: editedDate
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: deleted
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
