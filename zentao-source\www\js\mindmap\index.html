<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Mindmap - Zentao</title>
<link rel="stylesheet" href="../../theme/zui/css/min.css" />
<script src="../jquery/lib.js"></script>
<script src="../zui/min.js"></script>
<script src="./js/hotkey.min.js"></script>
<script src="./js/zui.mindmap.js"></script>
<link rel="stylesheet" href="./css/zui.mindmap.css" />
<link rel="stylesheet" href="./css/zui.node.css" />
<style>
#mindmap,body,html {width: 100%; height: 100%;}#mindmap{border: 1px solid rgba(0,0,0,.05)}
#mindmap {position: fixed; top: 0; right: 0; bottom: 0; left: 0;}
#mindmap .icon-plus-sign::before {content: '\e974'}
#mindmap .icon-minus-sign::before {content: '\e9b6'}
#mindmap .icon-expand-full::before {content: '\e94e'}
#mindmap .icon-dot-circle::before {content: '\e94c'}
#mindmap .mindmap-node[data-type="sub"] > .wrapper {min-height: 24px;}
#mindmap .mindmap-node[data-type="sub"] > .wrapper > .text,
#mindmap .mindmap-node[data-type="node"] > .wrapper > .text {padding: 1px 4px;}
</style>
</head>
<body>
<div id="mindmap"></div>
<script>
const options = $.extend({minimap: false, toolbar: true, lineCurvature: 20, hSpace: 30, vSpace: 0}, window.parent[$.getSearchParam('options')]);
if(!options.manual) $('#mindmap').mindmap(options);
parent.createMindmap = function(userOptions){return $('#mindmap').mindmap($.extend({}, options, userOptions)).data('zui.mindmap');};
parent.getMindmap = function(){return $('#mindmap').data('zui.mindmap');};
parent.mindmap$ = $;
$('body').on('click', () => window.parent.$(window.frameElement).trigger('click'));
</script>
</body>
</html>
