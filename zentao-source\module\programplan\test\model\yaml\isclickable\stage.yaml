title: table zt_project
author: xushenjie
version: "1.0"
fields:
  - field: id
    range: 6-10
  - field: project
    range: 1
  - field: model
    range:
  - field: type
    range: 'stage'
  - field: name
    range: 需求,设计,开发,测试,发布,总结评审
    prefix: 阶段-
  - field: desc
    prefix: '阶段描述'
    range: 1-100
  - field: status
    range: wait,doing,suspended
  - field: lifetime
    range:
  - field: budget
    range: 900000-1:100
  - field: budgetUnit
    range: CNY,USD
  - field: attribute
    range: request,design,dev,qa,release,review
  - field: percent
    range: 1-100:R
  - field: milestone
    range: 1
  - field: output
    range:
  - field: auth
    range: "extend"
  - field: parent
    range: 1-10
  - field: path
    fields:
      - field: path1
        prefix: ","
        range: 5-10
      - field: path2
        prefix: ","
        range: 6-10
        postfix: ","
  - field: grade
    range: "2"
  - field: code
    range:
  - field: begin
    range: "(-3M)-(+M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: end
    range: "(+5w)-(+2M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: PO
    range:
  - field: PM
    range:
  - field: QD
    range:
  - field: RD
    range:
  - field: team
    range: 需求,设计,开发,测试,发布,总结评审
  - field: version
    range: 1
  - field: acl
    range: open,private,program
  - field: order
    range: 5-10000:5
  - field: openedBy
    range: "admin"
  - field: openedVersion
    range: "18.4"
  - field: openedDate
    range: "(-3M)-(+M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: deleted
    range: 0