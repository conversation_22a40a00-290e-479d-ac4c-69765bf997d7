import mysql.connector

# 数据库配置
config = {
    'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'cyh',
    'password': 'Qq188788',
    'database': 'kanban'
}

try:
    # 建立连接
    conn = mysql.connector.connect(**config)
    cursor = conn.cursor()
    
    # 1. 先检查表的所有索引
    print("检查当前索引状态...")
    cursor.execute("""
        SELECT CONSTRAINT_NAME, CONSTRAINT_TYPE 
        FROM information_schema.TABLE_CONSTRAINTS 
        WHERE TABLE_SCHEMA = 'kanban' 
        AND TABLE_NAME = 'weekly_reports';
    """)
    constraints = cursor.fetchall()
    print("当前约束：")
    for constraint in constraints:
        print(f"约束名称: {constraint[0]}, 类型: {constraint[1]}")
    
    # 2. 删除所有主键约束
    print("\n删除现有主键约束...")
    try:
        cursor.execute("""
            SELECT CONSTRAINT_NAME
            FROM information_schema.TABLE_CONSTRAINTS 
            WHERE TABLE_SCHEMA = 'kanban' 
            AND TABLE_NAME = 'weekly_reports'
            AND CONSTRAINT_TYPE = 'PRIMARY KEY';
        """)
        pk_constraints = cursor.fetchall()
        
        for pk in pk_constraints:
            cursor.execute(f"ALTER TABLE weekly_reports DROP PRIMARY KEY")
            print(f"已删除主键约束: {pk[0]}")
    except Exception as e:
        print(f"删除主键约束时出错: {str(e)}")
    
    # 3. 重新设置id为自增主键
    print("\n重新设置id为自增主键...")
    try:
        # 先确保id列存在且为INT类型
        cursor.execute("ALTER TABLE weekly_reports MODIFY COLUMN id INT")
        # 然后设置为自增主键
        cursor.execute("ALTER TABLE weekly_reports MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY")
        print("成功将id设置为自增主键")
    except Exception as e:
        print(f"设置自增主键时出错: {str(e)}")
    
    # 提交更改
    conn.commit()
    
    # 4. 验证更改
    print("\n验证当前表结构...")
    cursor.execute("SHOW CREATE TABLE weekly_reports")
    table_info = cursor.fetchone()
    print("\n当前表结构：")
    print(table_info[1])
    
except Exception as e:
    print(f"发生错误: {str(e)}")
finally:
    if 'cursor' in locals():
        cursor.close()
    if 'conn' in locals():
        conn.close() 