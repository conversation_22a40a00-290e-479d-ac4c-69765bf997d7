#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的数据库结构和API功能
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

# API配置
API_BASE_URL = 'http://localhost:8001/api/v1'

def test_api_endpoint(endpoint, method='GET', data=None, description=""):
    """测试API端点"""
    try:
        url = f"{API_BASE_URL}{endpoint}"
        
        if method == 'GET':
            response = requests.get(url)
        elif method == 'POST':
            response = requests.post(url, json=data)
        elif method == 'PUT':
            response = requests.put(url, json=data)
        elif method == 'DELETE':
            response = requests.delete(url)
        
        if response.status_code == 200:
            logging.info(f"✅ {description}: 成功")
            return response.json()
        else:
            logging.error(f"❌ {description}: 失败 - {response.status_code} {response.text}")
            return None
            
    except Exception as e:
        logging.error(f"❌ {description}: 异常 - {e}")
        return None

def main():
    """主测试函数"""
    logging.info("🚀 开始测试新的数据库结构和API")
    logging.info("=" * 80)
    
    # 1. 测试获取督办事项列表
    logging.info("测试1: 获取督办事项列表")
    logging.info("-" * 40)
    
    items_data = test_api_endpoint(
        "/new-supervision/items",
        description="获取督办事项列表"
    )
    
    if items_data and 'data' in items_data:
        items = items_data['data']
        logging.info(f"✅ 获取到 {len(items)} 个督办事项")
        
        # 显示前3个督办事项的信息
        for i, item in enumerate(items[:3]):
            logging.info(f"  督办{item.get('sequence_number', i+1)}: {item.get('work_theme', 'N/A')}")
            logging.info(f"    整体进度: {item.get('overall_progress', 'N/A')} ({item.get('completion_rate', 0)}%)")
    
    # 2. 测试获取公司列表
    logging.info("\n测试2: 获取公司列表")
    logging.info("-" * 40)
    
    companies_data = test_api_endpoint(
        "/new-supervision/companies",
        description="获取公司列表"
    )
    
    if companies_data and 'data' in companies_data:
        companies = companies_data['data']
        logging.info(f"✅ 获取到 {len(companies)} 家公司")
        
        # 显示公司信息
        for company in companies[:5]:
            logging.info(f"  {company.get('company_name', 'N/A')} (代码: {company.get('company_code', 'N/A')})")
    
    # 3. 测试Excel导出功能
    logging.info("\n测试3: Excel导出功能")
    logging.info("-" * 40)
    
    try:
        export_url = f"{API_BASE_URL}/new-supervision/export"
        response = requests.get(export_url)
        
        if response.status_code == 200:
            logging.info("✅ Excel导出: 成功")
            logging.info(f"  文件大小: {len(response.content)} 字节")
            logging.info(f"  Content-Type: {response.headers.get('content-type', 'N/A')}")
        else:
            logging.error(f"❌ Excel导出: 失败 - {response.status_code}")
            
    except Exception as e:
        logging.error(f"❌ Excel导出: 异常 - {e}")
    
    # 4. 测试动态进度计算
    logging.info("\n测试4: 动态进度计算验证")
    logging.info("-" * 40)
    
    if items_data and 'data' in items_data:
        items = items_data['data']
        
        # 验证进度计算逻辑
        progress_stats = {}
        for item in items:
            progress = item.get('overall_progress', 'X')
            if progress not in progress_stats:
                progress_stats[progress] = 0
            progress_stats[progress] += 1
        
        logging.info("✅ 进度状态分布:")
        for status, count in progress_stats.items():
            status_name = {
                '√': '已完成',
                'O': '进行中', 
                '！': '逾期',
                'X': '未启动',
                '—': '不需要执行'
            }.get(status, status)
            logging.info(f"  {status} {status_name}: {count} 个")
    
    # 5. 测试公司状态更新
    logging.info("\n测试5: 公司状态更新")
    logging.info("-" * 40)
    
    if items_data and companies_data:
        items = items_data['data']
        companies = companies_data['data']
        
        if items and companies:
            # 选择第一个督办事项和第一家公司进行测试
            test_item = items[0]
            test_company = companies[0]
            
            update_data = {
                "supervision_item_id": test_item['id'],
                "company_id": test_company['id'],
                "status": "O",  # 设置为进行中
                "existing_problems": "测试问题",
                "next_plan": "测试计划"
            }
            
            result = test_api_endpoint(
                "/new-supervision/status",
                method="PUT",
                data=update_data,
                description=f"更新{test_company['company_name']}的状态"
            )
            
            if result:
                logging.info(f"✅ 状态更新成功: {test_company['company_name']} -> 进行中")
    
    logging.info("=" * 80)
    logging.info("🎯 数据库结构和API测试完成！")

if __name__ == "__main__":
    main()
