#!/usr/bin/env php
<?php
include dirname(__FILE__, 5) . '/test/lib/init.php';
include dirname(__FILE__, 2) . '/lib/gitlab.unittest.class.php';
su('admin');

/**

title=测试 gitlabModel::apiPost();
timeout=0
cid=1

- 用host url 发送一个请求 @success
- 用host ID 发送一个请求 @success
- 用不合规范的host url 发送一个请求 @return null
- 用不存在host ID 发送一个请求 @return null

*/

zenData('pipeline')->gen(5);
zenData('repo')->loadYaml('repo')->gen(5);

$gitlab = $tester->loadModel('gitlab');

$gitlabID = 1;
/* Create user. */
$user = new stdclass();
$user->name     = 'apiCreatedUser';
$user->username = 'apiuser17';
$user->email    = '<EMAIL>';
$user->password = '********';
$gitlab->apiCreateUser($gitlabID, $user);

/* Get userID. */
$gitlabUsers = $gitlab->apiGetUsers($gitlabID);
foreach($gitlabUsers as $gitlabUser)
{
    if($gitlabUser->account == 'apiuser17')
    {
        $userID = $gitlabUser->id;
        break;
    }
}

$user = new stdclass();
$user->name = 'apiUpdatedUser';

$gitlabTest = new gitlabTest();

$hostID  = 1;
$host    = 'https://gitlabdev.qc.oop.cc/api/v4%s?private_token=**************************';
$host2   = 'abc.com';
$api     = '/user';
$options = array(CURLOPT_CUSTOMREQUEST => 'PUT');

r($gitlabTest->apiPostTest($host, $api, $user, $options))   && p() && e('success'); //用host url 发送一个请求
r($gitlabTest->apiPostTest($hostID, $api, $user, $options)) && p() && e('success'); //用host ID 发送一个请求
r($gitlabTest->apiPostTest($host2, $api, $user, $options))  && p() && e('return null'); //用不合规范的host url 发送一个请求
r($gitlabTest->apiPostTest(0, $api, $user, $options))       && p() && e('return null'); //用不存在host ID 发送一个请求