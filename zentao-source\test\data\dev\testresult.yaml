title: table zt_testresult
desc: "测试结果"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-70
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: run
    note: "运行"
    range: 1-70
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: case
    note: "用例ID"
    range: 1-40,411-440
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: version
    note: "用例版本"
    range: 1
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: job
    note: "工作"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: compile
    note: "编译"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: caseResult
    note: "测试结果"
    range: [pass,fail]{20!},[pass,pass,fail]{15!}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: stepResults
    note: "步骤结果"
    range: "php处理"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: lastRunner
    note: "最后执行人"
    range: admin
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: date
    note: "测试时间"
    range: "(M)-(w):60"
    type: timestamp
    prefix: ""
    postfix: ""
    loop: 0
    format: "YYYY-MM-DD hh:mm:ss"
  - field: duration
    note: "持续时间"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: xml
    note: "XMl格式"
    range: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deploy
    note: "部署"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
