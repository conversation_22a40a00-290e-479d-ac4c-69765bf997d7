<?php
$lang->serverroom->common      = '机房';
$lang->serverroom->browse      = '机房列表';
$lang->serverroom->create      = '添加机房';
$lang->serverroom->edit        = '编辑';
$lang->serverroom->editAction  = '编辑机房';
$lang->serverroom->delete      = '删除机房';
$lang->serverroom->view        = '机房详情';
$lang->serverroom->all         = '全部';
$lang->serverroom->byQuery     = '搜索';
$lang->serverroom->name        = '名称';
$lang->serverroom->city        = '所在城市';
$lang->serverroom->line        = '线路类型';
$lang->serverroom->bandwidth   = '带宽';
$lang->serverroom->provider    = '服务商';
$lang->serverroom->owner       = '负责人';
$lang->serverroom->region      = '区域';
$lang->serverroom->createdBy   = '由谁创建';
$lang->serverroom->createdDate = '创建时间';
$lang->serverroom->editedBy    = '由谁编辑';
$lang->serverroom->editedDate  = '编辑时间';
$lang->serverroom->noneCity    = '无城市';

$lang->serverroom->empty = '暂时没有机房';

$lang->serverroom->lineList['']        = '';
$lang->serverroom->lineList['telecom'] = '电信';
$lang->serverroom->lineList['unicom']  = '联通';
$lang->serverroom->lineList['mobile']  = '移动';
$lang->serverroom->lineList['double']  = '双线';
$lang->serverroom->lineList['bgp']     = 'BGP';

$lang->serverroom->confirmDelete = '是否删除该机房记录？';

$lang->serverroom->providerList['']        = '';
$lang->serverroom->providerList['aliyun']  = '阿里云';
$lang->serverroom->providerList['qingyun'] = '青云';

$lang->serverroom->cityList['']          = '';
$lang->serverroom->cityList['beijing']   = '北京';
$lang->serverroom->cityList['hangzhou']  = '杭州';
$lang->serverroom->cityList['guangdong'] = '广东';

$lang->serverroom->featureBar['browse']['all'] = $lang->serverroom->all;
