title: table zt_team
desc: "团队"
author: <PERSON>
version: "1.0"
fields:
  - field: root
    note: "项目/执行ID"
    range: 101-110
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "项目类型"
    range: execution{5}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: account
    note: "用户账号"
    fields:
      - field: account1
        range: admin,user{100}
      - field: account2
        range: "[],1-100"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
