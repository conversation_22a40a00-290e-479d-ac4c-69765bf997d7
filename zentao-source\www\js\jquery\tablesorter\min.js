/*!
* TableSorter 2.31.3 min - Client-side table sorting with ease!
* Copyright (c) 2007 <PERSON>
*/
!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&"object"==typeof module.exports?module.exports=e(require("jquery")):e(jQuery)}(function(e){"use strict";e.extend({tablesorter:new function(){function t(){var e=arguments[0],t=arguments.length>1?Array.prototype.slice.call(arguments):e;"undefined"!=typeof console&&"undefined"!=typeof console.log?console[/error/i.test(e)?"error":/warn/i.test(e)?"warn":"log"](t):alert(t)}function r(e,r){t(e+" ("+((new Date).getTime()-r.getTime())+"ms)")}function s(e){for(var t in e)return!1;return!0}function a(r,s,a,n){for(var o,i,d=r.config,c=v.parsers.length,l=!1,u="",p=!0;""===u&&p;)a++,s[a]?(l=s[a].cells[n],u=v.getElementText(d,l,n),i=e(l),r.config.debug&&t("Checking if value was empty on row "+a+", column: "+n+': "'+u+'"')):p=!1;for(;--c>=0;)if(o=v.parsers[c],o&&"text"!==o.id&&o.is&&o.is(u,r,l,i))return o;return v.getParserById("text")}function n(e){var s,n,o,i,d,c,l,u,p,g,f=e.config,m=f.$tbodies=f.$table.children("tbody:not(."+f.cssInfoBlock+")"),h=0,b="",y=m.length;if(0===y)return f.debug?t("Warning: *Empty table!* Not building a parser cache"):"";for(f.debug&&(g=new Date,t("Detecting parsers for each column")),n={extractors:[],parsers:[]};y>h;){if(s=m[h].rows,s.length)for(o=f.columns,i=0;o>i;i++)d=f.$headers.filter('[data-column="'+i+'"]:last'),c=v.getColumnData(e,f.headers,i),p=v.getParserById(v.getData(d,c,"extractor")),u=v.getParserById(v.getData(d,c,"sorter")),l="false"===v.getData(d,c,"parser"),f.empties[i]=(v.getData(d,c,"empty")||f.emptyTo||(f.emptyToBottom?"bottom":"top")).toLowerCase(),f.strings[i]=(v.getData(d,c,"string")||f.stringTo||"max").toLowerCase(),l&&(u=v.getParserById("no-parser")),p||(p=!1),u||(u=a(e,s,-1,i)),f.debug&&(b+="column:"+i+"; extractor:"+p.id+"; parser:"+u.id+"; string:"+f.strings[i]+"; empty: "+f.empties[i]+"\n"),n.parsers[i]=u,n.extractors[i]=p;h+=n.parsers.length?y:1}f.debug&&(t(b?b:"No parsers detected"),r("Completed detecting parsers",g)),f.parsers=n.parsers,f.extractors=n.extractors}function o(s){var a,n,o,i,d,c,l,u,p,g,f,m,h,b=s.config,y=b.$tbodies,w=b.extractors,x=b.parsers;if(b.cache={},b.totalRows=0,!x)return b.debug?t("Warning: *Empty table!* Not building a cache"):"";for(b.debug&&(g=new Date),b.showProcessing&&v.isProcessing(s,!0),l=0;l<y.length;l++){for(h=[],a=b.cache[l]={normalized:[]},f=y[l]&&y[l].rows.length||0,d=0;f>d;++d)if(m={child:[],raw:[]},u=e(y[l].rows[d]),p=[],u.hasClass(b.cssChildRow)&&0!==d)n=a.normalized.length-1,a.normalized[n][b.columns].$row=a.normalized[n][b.columns].$row.add(u),u.prev().hasClass(b.cssChildRow)||u.prev().addClass(v.css.cssHasChild),m.child[n]=e.trim(u[0].textContent||u.text()||"");else{for(m.$row=u,m.order=d,c=0;c<b.columns;++c)"undefined"!=typeof x[c]?(n=v.getElementText(b,u[0].cells[c],c),m.raw.push(n),o="undefined"==typeof w[c].id?n:w[c].format(n,s,u[0].cells[c],c),i="no-parser"===x[c].id?"":x[c].format(o,s,u[0].cells[c],c),p.push(b.ignoreCase&&"string"==typeof i?i.toLowerCase():i),"numeric"===(x[c].type||"").toLowerCase()&&(h[c]=Math.max(Math.abs(i)||0,h[c]||0))):b.debug&&t("No parser found for cell:",u[0].cells[c],"does it have a header?");p[b.columns]=m,a.normalized.push(p)}a.colMax=h,b.totalRows+=a.normalized.length}b.showProcessing&&v.isProcessing(s),b.debug&&r("Building cache for "+f+" rows",g)}function i(e,t){var a,n,o,i,d,c,l,u=e.config,p=u.widgetOptions,g=u.$tbodies,f=[],m=u.cache;if(s(m))return u.appender?u.appender(e,f):e.isUpdating?u.$table.trigger("updateComplete",e):"";for(u.debug&&(l=new Date),c=0;c<g.length;c++)if(o=g.eq(c),o.length){for(i=v.processTbody(e,o,!0),a=m[c].normalized,n=a.length,d=0;n>d;d++)f.push(a[d][u.columns].$row),u.appender&&(!u.pager||u.pager.removeRows&&p.pager_removeRows||u.pager.ajax)||i.append(a[d][u.columns].$row);v.processTbody(e,i,!1)}u.appender&&u.appender(e,f),u.debug&&r("Rebuilt table",l),t||u.appender||v.applyWidget(e),e.isUpdating&&u.$table.trigger("updateComplete",e)}function d(e){return/^d/i.test(e)||1===e}function c(s){var a,n,o,i,c,l,p,g=s.config;g.headerList=[],g.headerContent=[],g.debug&&(p=new Date),g.columns=v.computeColumnIndex(g.$table.children("thead, tfoot").children("tr")),i=g.cssIcon?'<i class="'+(g.cssIcon===v.css.icon?v.css.icon:g.cssIcon+" "+v.css.icon)+'"></i>':"",g.$headers=e(e.map(e(s).find(g.selectorHeaders),function(t,r){return n=e(t),n.parent().hasClass(g.cssIgnoreRow)?void 0:(a=v.getColumnData(s,g.headers,r,!0),g.headerContent[r]=n.html(),""===g.headerTemplate||n.find("."+v.css.headerIn).length||(c=g.headerTemplate.replace(/\{content\}/g,n.html()).replace(/\{icon\}/g,n.find("."+v.css.icon).length?"":i),g.onRenderTemplate&&(o=g.onRenderTemplate.apply(n,[r,c]),o&&"string"==typeof o&&(c=o)),n.html('<div class="'+v.css.headerIn+'">'+c+"</div>")),g.onRenderHeader&&g.onRenderHeader.apply(n,[r,g,g.$table]),t.column=parseInt(n.attr("data-column"),10),t.order=d(v.getData(n,a,"sortInitialOrder")||g.sortInitialOrder)?[1,0,2]:[0,1,2],t.count=-1,t.lockedOrder=!1,l=v.getData(n,a,"lockedOrder")||!1,"undefined"!=typeof l&&l!==!1&&(t.order=t.lockedOrder=d(l)?[1,1,1]:[0,0,0]),n.addClass(v.css.header+" "+g.cssHeader),g.headerList[r]=t,n.parent().addClass(v.css.headerRow+" "+g.cssHeaderRow).attr("role","row"),g.tabIndex&&n.attr("tabindex",0),t)})),e(s).find(g.selectorHeaders).attr({scope:"col",role:"columnheader"}),u(s),g.debug&&(r("Built headers:",p),t(g.$headers))}function l(e,t,r){var s=e.config;s.$table.find(s.selectorRemove).remove(),n(e),o(e),y(s,t,r)}function u(t){var r,s,a,n=t.config;n.$headers.each(function(o,i){s=e(i),a=v.getColumnData(t,n.headers,o,!0),r="false"===v.getData(i,a,"sorter")||"false"===v.getData(i,a,"parser"),i.sortDisabled=r,s[r?"addClass":"removeClass"]("sorter-false").attr("aria-disabled",""+r),t.id&&(r?s.removeAttr("aria-controls"):s.attr("aria-controls",t.id))})}function p(t){var r,s,a,n=t.config,o=n.sortList,i=o.length,d=v.css.sortNone+" "+n.cssNone,c=[v.css.sortAsc+" "+n.cssAsc,v.css.sortDesc+" "+n.cssDesc],l=[n.cssIconAsc,n.cssIconDesc,n.cssIconNone],u=["ascending","descending"],p=e(t).find("tfoot tr").children().add(n.$extraHeaders).removeClass(c.join(" "));for(n.$headers.removeClass(c.join(" ")).addClass(d).attr("aria-sort","none").find("."+n.cssIcon).removeClass(l.join(" ")).addClass(l[2]),s=0;i>s;s++)if(2!==o[s][1]&&(r=n.$headers.not(".sorter-false").filter('[data-column="'+o[s][0]+'"]'+(1===i?":last":"")),r.length)){for(a=0;a<r.length;a++)r[a].sortDisabled||r.eq(a).removeClass(d).addClass(c[o[s][1]]).attr("aria-sort",u[o[s][1]]).find("."+n.cssIcon).removeClass(l[2]).addClass(l[o[s][1]]);p.length&&p.filter('[data-column="'+o[s][0]+'"]').removeClass(d).addClass(c[o[s][1]])}n.$headers.not(".sorter-false").each(function(){var t=e(this),r=this.order[(this.count+1)%(n.sortReset?3:2)],s=e.trim(t.text())+": "+v.language[t.hasClass(v.css.sortAsc)?"sortAsc":t.hasClass(v.css.sortDesc)?"sortDesc":"sortNone"]+v.language[0===r?"nextAsc":1===r?"nextDesc":"nextNone"];t.attr("aria-label",s)})}function g(t,r){var s,a,n,o,i,d=t.config,c=r||d.sortList;d.sortList=[],e.each(c,function(t,r){if(o=parseInt(r[0],10),n=d.$headers.filter('[data-column="'+o+'"]:last')[0]){switch(a=(""+r[1]).match(/^(1|d|s|o|n)/),a=a?a[0]:""){case"1":case"d":a=1;break;case"s":a=i||0;break;case"o":s=n.order[(i||0)%(d.sortReset?3:2)],a=0===s?1:1===s?0:2;break;case"n":n.count=n.count+1,a=n.order[n.count%(d.sortReset?3:2)];break;default:a=0}i=0===t?a:i,s=[o,parseInt(a,10)||0],d.sortList.push(s),a=e.inArray(s[1],n.order),n.count=a>=0?a:s[1]%(d.sortReset?3:2)}})}function f(e,t){return e&&e[t]?e[t].type||"":""}function m(t,r,s){if(t.isUpdating)return setTimeout(function(){m(t,r,s)},50);var a,n,o,d,c,l=t.config,u=!s[l.sortMultiSortKey],g=l.$table;if(g.trigger("sortStart",t),r.count=s[l.sortResetKey]?2:(r.count+1)%(l.sortReset?3:2),l.sortRestart&&(n=r,l.$headers.each(function(){this===n||!u&&e(this).is("."+v.css.sortDesc+",."+v.css.sortAsc)||(this.count=-1)})),n=parseInt(e(r).attr("data-column"),10),u){if(l.sortList=[],null!==l.sortForce)for(a=l.sortForce,o=0;o<a.length;o++)a[o][0]!==n&&l.sortList.push(a[o]);if(d=r.order[r.count],2>d&&(l.sortList.push([n,d]),r.colSpan>1))for(o=1;o<r.colSpan;o++)l.sortList.push([n+o,d])}else{if(l.sortAppend&&l.sortList.length>1)for(o=0;o<l.sortAppend.length;o++)c=v.isValueInArray(l.sortAppend[o][0],l.sortList),c>=0&&l.sortList.splice(c,1);if(v.isValueInArray(n,l.sortList)>=0)for(o=0;o<l.sortList.length;o++)c=l.sortList[o],d=l.$headers.filter('[data-column="'+c[0]+'"]:last')[0],c[0]===n&&(c[1]=d.order[r.count],2===c[1]&&(l.sortList.splice(o,1),d.count=-1));else if(d=r.order[r.count],2>d&&(l.sortList.push([n,d]),r.colSpan>1))for(o=1;o<r.colSpan;o++)l.sortList.push([n+o,d])}if(null!==l.sortAppend)for(a=l.sortAppend,o=0;o<a.length;o++)a[o][0]!==n&&l.sortList.push(a[o]);g.trigger("sortBegin",t),setTimeout(function(){p(t),h(t),i(t),g.trigger("sortEnd",t)},1)}function h(e){var t,a,n,o,i,d,c,l,u,p,g,m=0,h=e.config,b=h.textSorter||"",y=h.sortList,w=y.length,x=h.$tbodies.length;if(!h.serverSideSorting&&!s(h.cache)){for(h.debug&&(i=new Date),a=0;x>a;a++)d=h.cache[a].colMax,c=h.cache[a].normalized,c.sort(function(r,s){for(t=0;w>t;t++){if(o=y[t][0],l=y[t][1],m=0===l,h.sortStable&&r[o]===s[o]&&1===w)return r[h.columns].order-s[h.columns].order;if(n=/n/i.test(f(h.parsers,o)),n&&h.strings[o]?(n="boolean"==typeof h.string[h.strings[o]]?(m?1:-1)*(h.string[h.strings[o]]?-1:1):h.strings[o]?h.string[h.strings[o]]||0:0,u=h.numberSorter?h.numberSorter(r[o],s[o],m,d[o],e):v["sortNumeric"+(m?"Asc":"Desc")](r[o],s[o],n,d[o],o,e)):(p=m?r:s,g=m?s:r,u="function"==typeof b?b(p[o],g[o],m,o,e):"object"==typeof b&&b.hasOwnProperty(o)?b[o](p[o],g[o],m,o,e):v["sortNatural"+(m?"Asc":"Desc")](r[o],s[o],o,e,h)),u)return u}return r[h.columns].order-s[h.columns].order});h.debug&&r("Sorting on "+y.toString()+" and dir "+l+" time",i)}}function b(t,r){t.table.isUpdating&&t.$table.trigger("updateComplete",t.table),e.isFunction(r)&&r(t.table)}function y(t,r,s){var a=e.isArray(r)?r:t.sortList,n="undefined"==typeof r?t.resort:r;n===!1||t.serverSideSorting||t.table.isProcessing?(b(t,s),v.applyWidget(t.table,!1)):a.length?t.$table.trigger("sorton",[a,function(){b(t,s)},!0]):t.$table.trigger("sortReset",[function(){b(t,s),v.applyWidget(t.table,!1)}])}function w(t){var r=t.config,a=r.$table,d="sortReset update updateRows updateCell updateAll addRows updateComplete sorton appendCache updateCache applyWidgetId applyWidgets refreshWidgets destroy mouseup mouseleave ".split(" ").join(r.namespace+" ");a.unbind(d.replace(/\s+/g," ")).bind("sortReset"+r.namespace,function(s,a){s.stopPropagation(),r.sortList=[],p(t),h(t),i(t),e.isFunction(a)&&a(t)}).bind("updateAll"+r.namespace,function(e,s,a){e.stopPropagation(),t.isUpdating=!0,v.refreshWidgets(t,!0,!0),c(t),v.bindEvents(t,r.$headers,!0),w(t),l(t,s,a)}).bind("update"+r.namespace+" updateRows"+r.namespace,function(e,r,s){e.stopPropagation(),t.isUpdating=!0,u(t),l(t,r,s)}).bind("updateCell"+r.namespace,function(s,n,o,i){s.stopPropagation(),t.isUpdating=!0,a.find(r.selectorRemove).remove();var d,c,l,u,p=r.$tbodies,g=e(n),f=p.index(e.fn.closest?g.closest("tbody"):g.parents("tbody").filter(":first")),m=e.fn.closest?g.closest("tr"):g.parents("tr").filter(":first");n=g[0],p.length&&f>=0&&(l=p.eq(f).find("tr").index(m),u=g.index(),r.cache[f].normalized[l][r.columns].$row=m,c="undefined"==typeof r.extractors[u].id?v.getElementText(r,n,u):r.extractors[u].format(v.getElementText(r,n,u),t,n,u),d="no-parser"===r.parsers[u].id?"":r.parsers[u].format(c,t,n,u),r.cache[f].normalized[l][u]=r.ignoreCase&&"string"==typeof d?d.toLowerCase():d,"numeric"===(r.parsers[u].type||"").toLowerCase()&&(r.cache[f].colMax[u]=Math.max(Math.abs(d)||0,r.cache[f].colMax[u]||0)),d="undefined"!==o?o:r.resort,d!==!1?y(r,d,i):(e.isFunction(i)&&i(t),r.$table.trigger("updateComplete",r.table)))}).bind("addRows"+r.namespace,function(a,o,i,d){if(a.stopPropagation(),t.isUpdating=!0,s(r.cache))u(t),l(t,i,d);else{o=e(o).attr("role","row");var c,p,g,f,m,h,b,w=o.filter("tr").length,x=r.$tbodies.index(o.parents("tbody").filter(":first"));for(r.parsers&&r.parsers.length||n(t),c=0;w>c;c++){for(g=o[c].cells.length,b=[],h={child:[],$row:o.eq(c),order:r.cache[x].normalized.length},p=0;g>p;p++)f="undefined"==typeof r.extractors[p].id?v.getElementText(r,o[c].cells[p],p):r.extractors[p].format(v.getElementText(r,o[c].cells[p],p),t,o[c].cells[p],p),m="no-parser"===r.parsers[p].id?"":r.parsers[p].format(f,t,o[c].cells[p],p),b[p]=r.ignoreCase&&"string"==typeof m?m.toLowerCase():m,"numeric"===(r.parsers[p].type||"").toLowerCase()&&(r.cache[x].colMax[p]=Math.max(Math.abs(b[p])||0,r.cache[x].colMax[p]||0));b.push(h),r.cache[x].normalized.push(b)}y(r,i,d)}}).bind("updateComplete"+r.namespace,function(){t.isUpdating=!1}).bind("sorton"+r.namespace,function(r,n,d,c){var l=t.config;r.stopPropagation(),a.trigger("sortStart",this),g(t,n),p(t),l.delayInit&&s(l.cache)&&o(t),a.trigger("sortBegin",this),h(t),i(t,c),a.trigger("sortEnd",this),v.applyWidget(t),e.isFunction(d)&&d(t)}).bind("appendCache"+r.namespace,function(r,s,a){r.stopPropagation(),i(t,a),e.isFunction(s)&&s(t)}).bind("updateCache"+r.namespace,function(s,a){r.parsers&&r.parsers.length||n(t),o(t),e.isFunction(a)&&a(t)}).bind("applyWidgetId"+r.namespace,function(e,s){e.stopPropagation(),v.getWidgetById(s).format(t,r,r.widgetOptions)}).bind("applyWidgets"+r.namespace,function(e,r){e.stopPropagation(),v.applyWidget(t,r)}).bind("refreshWidgets"+r.namespace,function(e,r,s){e.stopPropagation(),v.refreshWidgets(t,r,s)}).bind("destroy"+r.namespace,function(e,r,s){e.stopPropagation(),v.destroy(t,r,s)}).bind("resetToLoadState"+r.namespace,function(){v.removeWidget(t,!0,!1),r=e.extend(!0,v.defaults,r.originalSettings),t.hasInitialized=!1,v.setup(t,r)})}var v=this;v.version="{{version}}",v.parsers=[],v.widgets=[],v.defaults={theme:"default",widthFixed:!1,showProcessing:!1,headerTemplate:"{content}",onRenderTemplate:null,onRenderHeader:null,cancelSelection:!0,tabIndex:!0,dateFormat:"mmddyyyy",sortMultiSortKey:"shiftKey",sortResetKey:"ctrlKey",usNumberFormat:!0,delayInit:!1,serverSideSorting:!1,resort:!0,headers:{},ignoreCase:!0,sortForce:null,sortList:[],sortAppend:null,sortStable:!1,sortInitialOrder:"asc",sortLocaleCompare:!1,sortReset:!1,sortRestart:!1,emptyTo:"bottom",stringTo:"max",textExtraction:"basic",textAttribute:"data-text",textSorter:null,numberSorter:null,widgets:[],widgetOptions:{zebra:["even","odd"]},initWidgets:!0,widgetClass:"widget-{name}",initialized:null,tableClass:"",cssAsc:"",cssDesc:"",cssNone:"",cssHeader:"",cssHeaderRow:"",cssProcessing:"",cssChildRow:"tablesorter-childRow",cssIcon:"tablesorter-icon",cssIconNone:"",cssIconAsc:"",cssIconDesc:"",cssInfoBlock:"tablesorter-infoOnly",cssNoSort:"tablesorter-noSort",cssIgnoreRow:"tablesorter-ignoreRow",selectorHeaders:"> thead th, > thead td",selectorSort:"th, td",selectorRemove:".remove-me",debug:!1,headerList:[],empties:{},strings:{},parsers:[]},v.css={table:"tablesorter",cssHasChild:"tablesorter-hasChildRow",childRow:"tablesorter-childRow",colgroup:"tablesorter-colgroup",header:"tablesorter-header",headerRow:"tablesorter-headerRow",headerIn:"tablesorter-header-inner",icon:"tablesorter-icon",processing:"tablesorter-processing",sortAsc:"tablesorter-headerAsc",sortDesc:"tablesorter-headerDesc",sortNone:"tablesorter-headerUnSorted"},v.language={sortAsc:"Ascending sort applied, ",sortDesc:"Descending sort applied, ",sortNone:"No sort applied, ",nextAsc:"activate to apply an ascending sort",nextDesc:"activate to apply a descending sort",nextNone:"activate to remove the sort"},v.log=t,v.benchmark=r,v.getElementText=function(t,r,s){if(!r)return"";var a,n=t.textExtraction||"",o=r.jquery?r:e(r);return e.trim("string"==typeof n?("basic"===n?o.attr(t.textAttribute)||r.textContent:r.textContent)||o.text()||"":"function"==typeof n?n(o[0],t.table,s):"function"==typeof(a=v.getColumnData(t.table,n,s))?a(o[0],t.table,s):o[0].textContent||o.text()||"")},v.construct=function(t){return this.each(function(){var r=this,s=e.extend(!0,{},v.defaults,t);s.originalSettings=t,!r.hasInitialized&&v.buildTable&&"TABLE"!==this.tagName?v.buildTable(r,s):v.setup(r,s)})},v.setup=function(r,s){if(!r||!r.tHead||0===r.tBodies.length||r.hasInitialized===!0)return s.debug?t("ERROR: stopping initialization! No table, thead, tbody or tablesorter has already been initialized"):"";var a="",i=e(r),d=e.metadata;r.hasInitialized=!1,r.isProcessing=!0,r.config=s,e.data(r,"tablesorter",s),s.debug&&e.data(r,"startoveralltimer",new Date),s.supportsDataObject=function(e){return e[0]=parseInt(e[0],10),e[0]>1||1===e[0]&&parseInt(e[1],10)>=4}(e.fn.jquery.split(".")),s.string={max:1,min:-1,emptymin:1,emptymax:-1,zero:0,none:0,"null":0,top:!0,bottom:!1},s.emptyTo=s.emptyTo.toLowerCase(),s.stringTo=s.stringTo.toLowerCase(),/tablesorter\-/.test(i.attr("class"))||(a=""!==s.theme?" tablesorter-"+s.theme:""),s.table=r,s.$table=i.addClass(v.css.table+" "+s.tableClass+a).attr("role","grid"),s.$headers=i.find(s.selectorHeaders),s.namespace=s.namespace?"."+s.namespace.replace(/\W/g,""):".tablesorter"+Math.random().toString(16).slice(2),s.$table.children().children("tr").attr("role","row"),s.$tbodies=i.children("tbody:not(."+s.cssInfoBlock+")").attr({"aria-live":"polite","aria-relevant":"all"}),s.$table.children("caption").length&&(a=s.$table.children("caption")[0],a.id||(a.id=s.namespace.slice(1)+"caption"),s.$table.attr("aria-labelledby",a.id)),s.widgetInit={},s.textExtraction=s.$table.attr("data-text-extraction")||s.textExtraction||"basic",c(r),v.fixColumnWidth(r),n(r),s.totalRows=0,s.delayInit||o(r),v.bindEvents(r,s.$headers,!0),w(r),s.supportsDataObject&&"undefined"!=typeof i.data().sortlist?s.sortList=i.data().sortlist:d&&i.metadata()&&i.metadata().sortlist&&(s.sortList=i.metadata().sortlist),v.applyWidget(r,!0),s.sortList.length>0?i.trigger("sorton",[s.sortList,{},!s.initWidgets,!0]):(p(r),s.initWidgets&&v.applyWidget(r,!1)),s.showProcessing&&i.unbind("sortBegin"+s.namespace+" sortEnd"+s.namespace).bind("sortBegin"+s.namespace+" sortEnd"+s.namespace,function(e){clearTimeout(s.processTimer),v.isProcessing(r),"sortBegin"===e.type&&(s.processTimer=setTimeout(function(){v.isProcessing(r,!0)},500))}),r.hasInitialized=!0,r.isProcessing=!1,s.debug&&v.benchmark("Overall initialization time",e.data(r,"startoveralltimer")),i.trigger("tablesorter-initialized",r),"function"==typeof s.initialized&&s.initialized(r)},v.fixColumnWidth=function(t){t=e(t)[0];var r,s,a=t.config,n=a.$table.children("colgroup");n.length&&n.hasClass(v.css.colgroup)&&n.remove(),a.widthFixed&&0===a.$table.children("colgroup").length&&(n=e('<colgroup class="'+v.css.colgroup+'">'),r=a.$table.width(),a.$tbodies.find("tr:first").children(":visible").each(function(){s=parseInt(e(this).width()/r*1e3,10)/10+"%",n.append(e("<col>").css("width",s))}),a.$table.prepend(n))},v.getColumnData=function(t,r,s,a,n){if("undefined"!=typeof r&&null!==r){t=e(t)[0];var o,i,d=t.config,c=n||d.$headers;if(r[s])return a?r[s]:r[c.index(c.filter('[data-column="'+s+'"]:last'))];for(i in r)if("string"==typeof i&&(o=c.filter('[data-column="'+s+'"]:last').filter(i).add(c.filter('[data-column="'+s+'"]:last').find(i)),o.length))return r[i]}},v.computeColumnIndex=function(t){var r,s,a,n,o,i,d,c,l,u,p,g,f,m=[],h={},b=0;for(r=0;r<t.length;r++)for(d=t[r].cells,s=0;s<d.length;s++){for(i=d[s],o=e(i),c=i.parentNode.rowIndex,l=c+"-"+o.index(),u=i.rowSpan||1,p=i.colSpan||1,"undefined"==typeof m[c]&&(m[c]=[]),a=0;a<m[c].length+1;a++)if("undefined"==typeof m[c][a]){g=a;break}for(h[l]=g,b=Math.max(g,b),o.attr({"data-column":g}),a=c;c+u>a;a++)for("undefined"==typeof m[a]&&(m[a]=[]),f=m[a],n=g;g+p>n;n++)f[n]="x"}return b+1},v.isProcessing=function(t,r,s){t=e(t);var a=t[0].config,n=s||t.find("."+v.css.header);r?("undefined"!=typeof s&&a.sortList.length>0&&(n=n.filter(function(){return this.sortDisabled?!1:v.isValueInArray(parseFloat(e(this).attr("data-column")),a.sortList)>=0})),t.add(n).addClass(v.css.processing+" "+a.cssProcessing)):t.add(n).removeClass(v.css.processing+" "+a.cssProcessing)},v.processTbody=function(t,r,s){t=e(t)[0];var a;return s?(t.isProcessing=!0,r.before('<span class="tablesorter-savemyplace"/>'),a=e.fn.detach?r.detach():r.remove()):(a=e(t).find("span.tablesorter-savemyplace"),r.insertAfter(a),a.remove(),void(t.isProcessing=!1))},v.clearTableBody=function(t){e(t)[0].config.$tbodies.children().detach()},v.bindEvents=function(t,r,a){t=e(t)[0];var n,i=t.config;a!==!0&&(i.$extraHeaders=i.$extraHeaders?i.$extraHeaders.add(r):r),r.find(i.selectorSort).add(r.filter(i.selectorSort)).unbind("mousedown mouseup sort keyup ".split(" ").join(i.namespace+" ").replace(/\s+/g," ")).bind("mousedown mouseup sort keyup ".split(" ").join(i.namespace+" "),function(a,d){var c,l=e(a.target),u=a.type;if(!(1!==(a.which||a.button)&&!/sort|keyup/.test(u)||"keyup"===u&&13!==a.which||"mouseup"===u&&d!==!0&&(new Date).getTime()-n>250)){if("mousedown"===u)return void(n=(new Date).getTime());if(c=e.fn.closest?l.closest("td,th"):l.parents("td,th").filter(":first"),/(input|select|button|textarea)/i.test(a.target.tagName)||l.hasClass(i.cssNoSort)||l.parents("."+i.cssNoSort).length>0||l.parents("button").length>0)return!i.cancelSelection;i.delayInit&&s(i.cache)&&o(t),c=e.fn.closest?e(this).closest("th, td")[0]:/TH|TD/.test(this.tagName)?this:e(this).parents("th, td")[0],c=i.$headers[r.index(c)],c.sortDisabled||m(t,c,a)}}),i.cancelSelection&&r.attr("unselectable","on").bind("selectstart",!1).css({"user-select":"none",MozUserSelect:"none"})},v.restoreHeaders=function(t){var r,s=e(t)[0].config;s.$table.find(s.selectorHeaders).each(function(t){r=e(this),r.find("."+v.css.headerIn).length&&r.html(s.headerContent[t])})},v.destroy=function(t,r,s){if(t=e(t)[0],t.hasInitialized){v.removeWidget(t,!0,!1);var a,n=e(t),o=t.config,i=n.find("thead:first"),d=i.find("tr."+v.css.headerRow).removeClass(v.css.headerRow+" "+o.cssHeaderRow),c=n.find("tfoot:first > tr").children("th, td");r===!1&&e.inArray("uitheme",o.widgets)>=0&&(n.trigger("applyWidgetId",["uitheme"]),n.trigger("applyWidgetId",["zebra"])),i.find("tr").not(d).remove(),a="sortReset update updateAll updateRows updateCell addRows updateComplete sorton appendCache updateCache "+"applyWidgetId applyWidgets refreshWidgets destroy mouseup mouseleave keypress sortBegin sortEnd resetToLoadState ".split(" ").join(o.namespace+" "),n.removeData("tablesorter").unbind(a.replace(/\s+/g," ")),o.$headers.add(c).removeClass([v.css.header,o.cssHeader,o.cssAsc,o.cssDesc,v.css.sortAsc,v.css.sortDesc,v.css.sortNone].join(" ")).removeAttr("data-column").removeAttr("aria-label").attr("aria-disabled","true"),d.find(o.selectorSort).unbind("mousedown mouseup keypress ".split(" ").join(o.namespace+" ").replace(/\s+/g," ")),v.restoreHeaders(t),n.toggleClass(v.css.table+" "+o.tableClass+" tablesorter-"+o.theme,r===!1),t.hasInitialized=!1,delete t.config.cache,"function"==typeof s&&s(t)}},v.regex={chunk:/(^([+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?)?$|^0x[0-9a-f]+$|\d+)/gi,chunks:/(^\\0|\\0$)/,hex:/^0x[0-9a-f]+$/i},v.sortNatural=function(e,t){if(e===t)return 0;var r,s,a,n,o,i,d,c,l=v.regex;if(l.hex.test(t)){if(s=parseInt(e.match(l.hex),16),n=parseInt(t.match(l.hex),16),n>s)return-1;if(s>n)return 1}for(r=e.replace(l.chunk,"\\0$1\\0").replace(l.chunks,"").split("\\0"),a=t.replace(l.chunk,"\\0$1\\0").replace(l.chunks,"").split("\\0"),c=Math.max(r.length,a.length),d=0;c>d;d++){if(o=isNaN(r[d])?r[d]||0:parseFloat(r[d])||0,i=isNaN(a[d])?a[d]||0:parseFloat(a[d])||0,isNaN(o)!==isNaN(i))return isNaN(o)?1:-1;if(typeof o!=typeof i&&(o+="",i+=""),i>o)return-1;if(o>i)return 1}return 0},v.sortNaturalAsc=function(e,t,r,s,a){if(e===t)return 0;var n=a.string[a.empties[r]||a.emptyTo];return""===e&&0!==n?"boolean"==typeof n?n?-1:1:-n||-1:""===t&&0!==n?"boolean"==typeof n?n?1:-1:n||1:v.sortNatural(e,t)},v.sortNaturalDesc=function(e,t,r,s,a){if(e===t)return 0;var n=a.string[a.empties[r]||a.emptyTo];return""===e&&0!==n?"boolean"==typeof n?n?-1:1:n||1:""===t&&0!==n?"boolean"==typeof n?n?1:-1:-n||-1:v.sortNatural(t,e)},v.sortText=function(e,t){return e>t?1:t>e?-1:0},v.getTextValue=function(e,t,r){if(r){var s,a=e?e.length:0,n=r+t;for(s=0;a>s;s++)n+=e.charCodeAt(s);return t*n}return 0},v.sortNumericAsc=function(e,t,r,s,a,n){if(e===t)return 0;var o=n.config,i=o.string[o.empties[a]||o.emptyTo];return""===e&&0!==i?"boolean"==typeof i?i?-1:1:-i||-1:""===t&&0!==i?"boolean"==typeof i?i?1:-1:i||1:(isNaN(e)&&(e=v.getTextValue(e,r,s)),isNaN(t)&&(t=v.getTextValue(t,r,s)),e-t)},v.sortNumericDesc=function(e,t,r,s,a,n){if(e===t)return 0;var o=n.config,i=o.string[o.empties[a]||o.emptyTo];return""===e&&0!==i?"boolean"==typeof i?i?-1:1:i||1:""===t&&0!==i?"boolean"==typeof i?i?1:-1:-i||-1:(isNaN(e)&&(e=v.getTextValue(e,r,s)),isNaN(t)&&(t=v.getTextValue(t,r,s)),t-e)},v.sortNumeric=function(e,t){return e-t},v.characterEquivalents={a:"áàâãäąå",A:"ÁÀÂÃÄĄÅ",c:"çćč",C:"ÇĆČ",e:"éèêëěę",E:"ÉÈÊËĚĘ",i:"íìİîïı",I:"ÍÌİÎÏ",o:"óòôõö",O:"ÓÒÔÕÖ",ss:"ß",SS:"ẞ",u:"úùûüů",U:"ÚÙÛÜŮ"},v.replaceAccents=function(e){var t,r="[",s=v.characterEquivalents;if(!v.characterRegex){v.characterRegexArray={};for(t in s)"string"==typeof t&&(r+=s[t],v.characterRegexArray[t]=new RegExp("["+s[t]+"]","g"));v.characterRegex=new RegExp(r+"]")}if(v.characterRegex.test(e))for(t in s)"string"==typeof t&&(e=e.replace(v.characterRegexArray[t],t));return e},v.isValueInArray=function(e,t){var r,s=t.length;for(r=0;s>r;r++)if(t[r][0]===e)return r;return-1},v.addParser=function(e){var t,r=v.parsers.length,s=!0;for(t=0;r>t;t++)v.parsers[t].id.toLowerCase()===e.id.toLowerCase()&&(s=!1);s&&v.parsers.push(e)},v.getParserById=function(e){if("false"==e)return!1;var t,r=v.parsers.length;for(t=0;r>t;t++)if(v.parsers[t].id.toLowerCase()===e.toString().toLowerCase())return v.parsers[t];return!1},v.addWidget=function(e){v.widgets.push(e)},v.hasWidget=function(t,r){return t=e(t),t.length&&t[0].config&&t[0].config.widgetInit[r]||!1},v.getWidgetById=function(e){var t,r,s=v.widgets.length;for(t=0;s>t;t++)if(r=v.widgets[t],r&&r.hasOwnProperty("id")&&r.id.toLowerCase()===e.toLowerCase())return r},v.applyWidget=function(t,s,a){t=e(t)[0];var n,o,i,d,c=t.config,l=c.widgetOptions,u=" "+c.table.className+" ",p=[];s!==!1&&t.hasInitialized&&(t.isApplyingWidgets||t.isUpdating)||(c.debug&&(n=new Date),d=new RegExp("\\s"+c.widgetClass.replace(/\{name\}/i,"([\\w-]+)")+"\\s","g"),u.match(d)&&(i=u.match(d),i&&e.each(i,function(e,t){c.widgets.push(t.replace(d,"$1"))})),c.widgets.length&&(t.isApplyingWidgets=!0,c.widgets=e.grep(c.widgets,function(t,r){return e.inArray(t,c.widgets)===r}),e.each(c.widgets||[],function(e,t){d=v.getWidgetById(t),d&&d.id&&(d.priority||(d.priority=10),p[e]=d)}),p.sort(function(e,t){return e.priority<t.priority?-1:e.priority===t.priority?0:1}),e.each(p,function(r,a){a&&((s||!c.widgetInit[a.id])&&(c.widgetInit[a.id]=!0,a.hasOwnProperty("options")&&(l=t.config.widgetOptions=e.extend(!0,{},a.options,l)),a.hasOwnProperty("init")&&(c.debug&&(o=new Date),a.init(t,a,c,l),c.debug&&v.benchmark("Initializing "+a.id+" widget",o))),!s&&a.hasOwnProperty("format")&&(c.debug&&(o=new Date),a.format(t,c,l,!1),c.debug&&v.benchmark((s?"Initializing ":"Applying ")+a.id+" widget",o)))}),s||"function"!=typeof a||a(t)),setTimeout(function(){t.isApplyingWidgets=!1,e.data(t,"lastWidgetApplication",new Date)},0),c.debug&&(i=c.widgets.length,r("Completed "+(s===!0?"initializing ":"applying ")+i+" widget"+(1!==i?"s":""),n)))},v.removeWidget=function(r,s,a){r=e(r)[0],s===!0?(s=[],e.each(v.widgets,function(e,t){t&&t.id&&s.push(t.id)})):s=(e.isArray(s)?s.join(","):s||"").toLowerCase().split(/[\s,]+/);var n,o,i,d=r.config,c=s.length;for(n=0;c>n;n++)o=v.getWidgetById(s[n]),i=e.inArray(s[n],d.widgets),o&&"remove"in o&&(d.debug&&i>=0&&t('Removing "'+s[n]+'" widget'),o.remove(r,d,d.widgetOptions,a),d.widgetInit[s[n]]=!1),i>=0&&a!==!0&&d.widgets.splice(i,1)},v.refreshWidgets=function(t,r,s){t=e(t)[0];var a=t.config,n=a.widgets,o=[],i=function(t){e(t).trigger("refreshComplete")};e.each(v.widgets,function(t,s){s&&s.id&&(r||e.inArray(s.id,n)<0)&&o.push(s.id)}),v.removeWidget(t,o.join(","),!0),s!==!0?(v.applyWidget(t,r||!1,i),r&&v.applyWidget(t,!1,i)):i(t)},v.getData=function(t,r,s){var a,n,o="",i=e(t);return i.length?(a=e.metadata?i.metadata():!1,n=" "+(i.attr("class")||""),"undefined"!=typeof i.data(s)||"undefined"!=typeof i.data(s.toLowerCase())?o+=i.data(s)||i.data(s.toLowerCase()):a&&"undefined"!=typeof a[s]?o+=a[s]:r&&"undefined"!=typeof r[s]?o+=r[s]:" "!==n&&n.match(" "+s+"-")&&(o=n.match(new RegExp("\\s"+s+"-([\\w-]+)"))[1]||""),e.trim(o)):""},v.formatFloat=function(t,r){if("string"!=typeof t||""===t)return t;var s,a=r&&r.config?r.config.usNumberFormat!==!1:"undefined"!=typeof r?r:!0;return t=a?t.replace(/,/g,""):t.replace(/[\s|\.]/g,"").replace(/,/g,"."),/^\s*\([.\d]+\)/.test(t)&&(t=t.replace(/^\s*\(([.\d]+)\)/,"-$1")),s=parseFloat(t),isNaN(s)?e.trim(t):s},v.isDigit=function(e){return isNaN(e)?/^[\-+(]?\d+[)]?$/.test(e.toString().replace(/[,.'"\s]/g,"")):!0}}});var t=e.tablesorter;return e.fn.extend({tablesorter:t.construct}),t.addParser({id:"no-parser",is:function(){return!1},format:function(){return""},type:"text"}),t.addParser({id:"text",is:function(){return!0},format:function(r,s){var a=s.config;return r&&(r=e.trim(a.ignoreCase?r.toLocaleLowerCase():r),r=a.sortLocaleCompare?t.replaceAccents(r):r),r},type:"text"}),t.addParser({id:"digit",is:function(e){return t.isDigit(e)},format:function(r,s){var a=t.formatFloat((r||"").replace(/[^\w,. \-()]/g,""),s);return r&&"number"==typeof a?a:r?e.trim(r&&s.config.ignoreCase?r.toLocaleLowerCase():r):r},type:"numeric"}),t.addParser({id:"currency",is:function(e){return/^\(?\d+[\u00a3$\u20ac\u00a4\u00a5\u00a2?.]|[\u00a3$\u20ac\u00a4\u00a5\u00a2?.]\d+\)?$/.test((e||"").replace(/[+\-,. ]/g,""))},format:function(r,s){var a=t.formatFloat((r||"").replace(/[^\w,. \-()]/g,""),s);return r&&"number"==typeof a?a:r?e.trim(r&&s.config.ignoreCase?r.toLocaleLowerCase():r):r},type:"numeric"}),t.addParser({id:"url",is:function(e){return/^(https?|ftp|file):\/\//.test(e)},format:function(t){return t?e.trim(t.replace(/(https?|ftp|file):\/\//,"")):t},parsed:!0,type:"text"}),t.addParser({id:"isoDate",is:function(e){return/^\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2}/.test(e)},format:function(e){var t=e?new Date(e.replace(/-/g,"/")):e;return t instanceof Date&&isFinite(t)?t.getTime():e},type:"numeric"}),t.addParser({id:"percent",is:function(e){return/(\d\s*?%|%\s*?\d)/.test(e)&&e.length<15},format:function(e,r){return e?t.formatFloat(e.replace(/%/g,""),r):e},type:"numeric"}),t.addParser({id:"image",is:function(e,t,r,s){return s.find("img").length>0},format:function(t,r,s){return e(s).find("img").attr(r.config.imgAttr||"alt")||t},parsed:!0,type:"text"}),t.addParser({id:"usLongDate",is:function(e){return/^[A-Z]{3,10}\.?\s+\d{1,2},?\s+(\d{4})(\s+\d{1,2}:\d{2}(:\d{2})?(\s+[AP]M)?)?$/i.test(e)||/^\d{1,2}\s+[A-Z]{3,10}\s+\d{4}/i.test(e)},format:function(e){var t=e?new Date(e.replace(/(\S)([AP]M)$/i,"$1 $2")):e;return t instanceof Date&&isFinite(t)?t.getTime():e},type:"numeric"}),t.addParser({id:"shortDate",is:function(e){return/(^\d{1,2}[\/\s]\d{1,2}[\/\s]\d{4})|(^\d{4}[\/\s]\d{1,2}[\/\s]\d{1,2})/.test((e||"").replace(/\s+/g," ").replace(/[\-.,]/g,"/"))},format:function(e,r,s,a){if(e){var n,o,i=r.config,d=i.$headers.filter('[data-column="'+a+'"]:last'),c=d.length&&d[0].dateFormat||t.getData(d,t.getColumnData(r,i.headers,a),"dateFormat")||i.dateFormat;return o=e.replace(/\s+/g," ").replace(/[\-.,]/g,"/"),"mmddyyyy"===c?o=o.replace(/(\d{1,2})[\/\s](\d{1,2})[\/\s](\d{4})/,"$3/$1/$2"):"ddmmyyyy"===c?o=o.replace(/(\d{1,2})[\/\s](\d{1,2})[\/\s](\d{4})/,"$3/$2/$1"):"yyyymmdd"===c&&(o=o.replace(/(\d{4})[\/\s](\d{1,2})[\/\s](\d{1,2})/,"$1/$2/$3")),n=new Date(o),n instanceof Date&&isFinite(n)?n.getTime():e}return e},type:"numeric"}),t.addParser({id:"time",is:function(e){return/^(([0-2]?\d:[0-5]\d)|([0-1]?\d:[0-5]\d\s?([AP]M)))$/i.test(e)},format:function(e){var t=e?new Date("2000/01/01 "+e.replace(/(\S)([AP]M)$/i,"$1 $2")):e;return t instanceof Date&&isFinite(t)?t.getTime():e},type:"numeric"}),t.addParser({id:"metadata",is:function(){return!1
},format:function(t,r,s){var a=r.config,n=a.parserMetadataName?a.parserMetadataName:"sortValue";return e(s).metadata()[n]},type:"numeric"}),t.addWidget({id:"zebra",priority:90,format:function(t,r,s){var a,n,o,i,d,c,l,u=new RegExp(r.cssChildRow,"i"),p=r.$tbodies;for(r.debug&&(c=new Date),l=0;l<p.length;l++)i=0,a=p.eq(l),n=a.children("tr:visible").not(r.selectorRemove),n.each(function(){o=e(this),u.test(this.className)||i++,d=i%2===0,o.removeClass(s.zebra[d?1:0]).addClass(s.zebra[d?0:1])})},remove:function(e,r,s,a){if(!a){var n,o,i=r.$tbodies,d=(s.zebra||["even","odd"]).join(" ");for(n=0;n<i.length;n++)o=t.processTbody(e,i.eq(n),!0),o.children().removeClass(d),t.processTbody(e,o,!1)}}}),t});
