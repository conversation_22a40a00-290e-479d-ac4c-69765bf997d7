#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试督办管理功能修复
"""

import requests
import json

def test_supervision_api():
    """测试督办管理API"""
    print("🧪 测试督办管理API...")
    
    base_url = "http://localhost:8000/api/v1"
    
    # 测试获取督办事项列表
    try:
        print("📋 测试获取督办事项列表...")
        response = requests.get(f"{base_url}/supervision/items")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 督办事项列表获取成功")
            print(f"   📊 返回数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            if 'data' in data and len(data['data']) > 0:
                print(f"   📈 督办事项数量: {len(data['data'])}")
                for item in data['data'][:3]:  # 显示前3个
                    print(f"   📝 {item.get('sequence_number', 'N/A')}. {item.get('work_theme', 'N/A')}")
            else:
                print("   ⚠️  没有督办事项数据")
        else:
            print(f"❌ 督办事项列表获取失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试督办事项API失败: {str(e)}")
    
    # 测试获取公司列表
    try:
        print("\n🏢 测试获取公司列表...")
        response = requests.get(f"{base_url}/supervision/companies")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 公司列表获取成功")
            if 'data' in data and len(data['data']) > 0:
                print(f"   🏢 公司数量: {len(data['data'])}")
                for company in data['data'][:5]:  # 显示前5个
                    print(f"   🏢 {company.get('company_code', 'N/A')}: {company.get('company_name', 'N/A')}")
            else:
                print("   ⚠️  没有公司数据")
        else:
            print(f"❌ 公司列表获取失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试公司API失败: {str(e)}")

def test_database_tables():
    """测试数据库表"""
    print("\n🗄️  测试数据库表...")
    
    import pymysql
    
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 检查表是否存在
            tables_to_check = ['supervision_items', 'companies', 'company_progress']
            
            for table in tables_to_check:
                cursor.execute(f"SHOW TABLES LIKE '{table}'")
                if cursor.fetchone():
                    print(f"✅ 表 {table} 存在")
                    
                    # 检查数据数量
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"   📊 数据量: {count} 条")
                else:
                    print(f"❌ 表 {table} 不存在")
            
            # 检查表结构
            print("\n📋 检查 supervision_items 表结构:")
            cursor.execute("DESCRIBE supervision_items")
            columns = cursor.fetchall()
            for column in columns:
                print(f"   📝 {column[0]} ({column[1]})")
            
            print("\n🏢 检查 companies 表数据:")
            cursor.execute("SELECT company_code, company_name FROM companies LIMIT 5")
            companies = cursor.fetchall()
            for company in companies:
                print(f"   🏢 {company[0]}: {company[1]}")
        
        connection.close()
        print("✅ 数据库检查完成")
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {str(e)}")

def main():
    print("🚀 督办管理功能修复测试")
    print("=" * 60)
    
    # 测试数据库表
    test_database_tables()
    
    # 测试API
    test_supervision_api()
    
    print("\n📋 修复总结:")
    print("   ❌ 原问题: 数据库缺少督办管理相关表")
    print("   ✅ 解决方案: 创建 supervision_items、companies、company_progress 表")
    print("   ✅ 数据初始化: 插入默认公司和示例督办事项")
    print("   ✅ 服务重启: 重新启动后端服务")
    
    print("\n🌐 测试方法:")
    print("   1. 打开浏览器访问: http://localhost:3000")
    print("   2. 登录系统")
    print("   3. 点击'督办管理'菜单")
    print("   4. 应该能看到督办事项列表")
    print("   5. 测试各种功能:")
    print("      - 查看督办事项列表")
    print("      - 筛选功能")
    print("      - 进度更新")
    print("      - 公司状态管理")
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
