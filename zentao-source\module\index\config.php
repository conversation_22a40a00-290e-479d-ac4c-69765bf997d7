<?php
$config->index->appGroup = array();
$config->index->appGroup['program']     = 'program';
$config->index->appGroup['project']     = 'project';
$config->index->appGroup['risk']        = 'project';
$config->index->appGroup['issue']       = 'project';
$config->index->appGroup['task']        = 'execution';
$config->index->appGroup['build']       = 'execution';
$config->index->appGroup['execution']   = 'execution';
$config->index->appGroup['product']     = 'product';
$config->index->appGroup['release']     = 'product';
$config->index->appGroup['productplan'] = 'product';
$config->index->appGroup['story']       = 'product';
$config->index->appGroup['bug']         = 'qa';
$config->index->appGroup['testcase']    = 'qa';
$config->index->appGroup['caselib']     = 'qa';
$config->index->appGroup['testtask']    = 'qa';
$config->index->appGroup['testsuite']   = 'qa';
$config->index->appGroup['testreport']  = 'qa';
$config->index->appGroup['doc']         = 'doc';
$config->index->appGroup['todo']        = 'my';
$config->index->appGroup['effort']      = 'my';
$config->index->appGroup['user']        = 'admin';

$config->index->oldPages = array();
$config->index->oldPages[] = 'screen-burn';
$config->index->oldPages[] = 'screen-viewold';
$config->index->oldPages[] = 'report-annualdata';
$config->index->oldPages[] = 'ai-adminindex';
$config->index->oldPages[] = 'ai-chat';
$config->index->oldPages[] = 'ai-editminiprogramcategory';
$config->index->oldPages[] = 'ai-miniprograms';
$config->index->oldPages[] = 'ai-prompts';
$config->index->oldPages[] = 'ai-promptview';
$config->index->oldPages[] = 'api-debug';
$config->index->oldPages[] = 'datatable-ajaxoldcustom';
$config->index->oldPages[] = 'weekly-index';
$config->index->oldPages[] = 'kanban-importticket';
$config->index->oldPages[] = 'message-ajaxgetdropmenuforold';
$config->index->oldPages[] = 'product-ajaxgetolddropmenu';
$config->index->oldPages[] = 'project-ajaxgetolddropmenu';
$config->index->oldPages[] = 'webhook-choosedept';
