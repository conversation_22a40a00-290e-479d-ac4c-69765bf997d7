@echo off
chcp 65001 >nul
echo 正在启动 MySQL 数据库管理工具...
echo.

REM 检查Python是否可用
where python >nul 2>&1
if %errorlevel% neq 0 (
    echo Python未找到！请确保已安装Python并添加到PATH环境变量中。
    pause
    exit /b 1
)

REM 设置环境变量
set "HOST=0.0.0.0"
set "PORT=5000"
set "FLASK_DEBUG=False"

REM 检查是否已安装所需的依赖
echo 检查依赖...
pip show flask >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装依赖，请稍候...
    call pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
    if %errorlevel% neq 0 (
        echo 安装依赖失败！请检查网络连接或尝试以管理员身份运行。
        pause
        exit /b 1
    )
)

REM 启动应用
echo 启动应用中...
echo.
echo 应用将在 http://localhost:5000 运行
echo 按 Ctrl+C 可以停止应用
echo.
call python app.py

REM 如果应用停止运行
echo.
echo 应用已停止运行
pause 