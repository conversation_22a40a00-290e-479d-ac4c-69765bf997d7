#mainContent .nav-tabs li>a::before {height: 0;}
.instance-name h3 {display: inline-block; padding-left: 10px;}
.instance-name > span {font-size: 14px; font-weight: bolder; padding: 3px 3px; margin: 0 5px;}
.instance-panel .btn-group .btn {width: 60px;}
.instance-panel .btn-group i {font-size: 24px;}
.instance-status span {display: inline-block; margin-right: 10px; padding-bottom: 1px;}
.instance-status i {font-size: 18px;}
.instance-source span {display: inline-block; margin-right: 10px; width: 100px;}
#senior-app-desc a {color: #61be68}
a#serialDiff {color: #ff9800;}
#seniorAppPanel span.text-info:hover {color: #2196f3;}
.instance-log .panel-body {padding-bottom: 8px; padding-left: 10px;}
.instance-log .panel-body table {margin-bottom: 0;}
.usage-box {border-radius: 3px;}
.c-title {text-align: center; padding-top: 10px; font-size: 15px}
.progress-pie {margin: 10px auto;}
.instance-panel > div > a:last-child { margin-left: 8px;}
.solution-link {display: inline-block; margin-left: 20px;}

#backup .panel {padding: 10px;}
#backup .panel .btn-toolbar{padding: 0 0 10px 0;}
#backup .panel .panel-heading {height: 50px;}
#backup table.table {margin-bottom: 20px; width: auto;}
#backup table th.actions {padding-left: 15px;}
#backup table td>.btn {padding-left: 5px; padding-right: 5px;}
#backup .btn.btn-info[disabled] {color: rgba(255,255,255,.7); background-color: #61be68;}

#advance .panel-title span {padding-left: 10px;}
#advance hr {margin: 5px 20px;}
#advance .scalable-input{height: 26px;}
#advance .center{text-align: center;}
#advance .hide{display: none}
