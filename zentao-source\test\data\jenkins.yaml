title: table zt_jenkins
desc: "<PERSON>集成"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: name
    note: "名称"
    range: 1-10000
    prefix: "这是一个Jenkins集成"
    postfix: ""
    loop: 0
    format: ""
  - field: url
    note: "服务地址"
    range: "qcmmi"
    prefix: "http://"
    postfix: ".com"
    loop: 0
    format: ""
  - field: account
    note: "用户名"
    range: a-z
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: password
    note: "密码"
    range: a-z
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: token
    note: "Token"
    range: a-z
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: "创建者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdDate
    note: "创建日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedBy
    note: "编辑者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedDate
    note: "编辑日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
