import{m as t}from"./p-aa688caf.js";import"./p-7900c24a.js";import"./p-986e5fe7.js";
/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/var r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,e=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o=(t,o,u,s)=>{if(o&&"object"==typeof o||"function"==typeof o)for(let a of e(o))i.call(t,a)||a===u||r(t,a,{get:()=>o[a],enumerable:!(s=n(o,a))||s.enumerable});return t},u={};o(u,t,"default");var s,a,c,f,h,v,d,l,g,b,m,w,p,k,j,x,S,I,y,T,E,O,C,_,D,R,N,L,W,A,M,z,F,P,q,U,H,V,$,B,G,J,K,Q,X,Y,Z,tt,rt,nt=class{_defaults;_idleCheckInterval;_lastUsedTime;_configChangeListener;_worker;_client;constructor(t){this._defaults=t,this._worker=null,this._client=null,this._idleCheckInterval=window.setInterval((()=>this._checkIfIdle()),3e4),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange((()=>this._stopWorker()))}_stopWorker(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null}dispose(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()}_checkIfIdle(){this._worker&&Date.now()-this._lastUsedTime>12e4&&this._stopWorker()}_getClient(){return this._lastUsedTime=Date.now(),this._client||(this._worker=u.editor.createWebWorker({moduleId:"vs/language/json/jsonWorker",label:this._defaults.languageId,createData:{languageSettings:this._defaults.diagnosticsOptions,languageId:this._defaults.languageId,enableSchemaRequest:this._defaults.diagnosticsOptions.enableSchemaRequest}}),this._client=this._worker.getProxy()),this._client}getLanguageServiceWorker(...t){let r;return this._getClient().then((t=>{r=t})).then((()=>{if(this._worker)return this._worker.withSyncedResources(t)})).then((()=>r))}};(a=s||(s={})).MIN_VALUE=-2147483648,a.MAX_VALUE=2147483647,(f=c||(c={})).MIN_VALUE=0,f.MAX_VALUE=2147483647,(v=h||(h={})).create=function(t,r){return t===Number.MAX_VALUE&&(t=c.MAX_VALUE),r===Number.MAX_VALUE&&(r=c.MAX_VALUE),{line:t,character:r}},v.is=function(t){var r=t;return nr.objectLiteral(r)&&nr.uinteger(r.line)&&nr.uinteger(r.character)},(l=d||(d={})).create=function(t,r,n,e){if(nr.uinteger(t)&&nr.uinteger(r)&&nr.uinteger(n)&&nr.uinteger(e))return{start:h.create(t,r),end:h.create(n,e)};if(h.is(t)&&h.is(r))return{start:t,end:r};throw new Error("Range#create called with invalid arguments["+t+", "+r+", "+n+", "+e+"]")},l.is=function(t){var r=t;return nr.objectLiteral(r)&&h.is(r.start)&&h.is(r.end)},(b=g||(g={})).create=function(t,r){return{uri:t,range:r}},b.is=function(t){var r=t;return nr.defined(r)&&d.is(r.range)&&(nr.string(r.uri)||nr.undefined(r.uri))},(w=m||(m={})).create=function(t,r,n,e){return{targetUri:t,targetRange:r,targetSelectionRange:n,originSelectionRange:e}},w.is=function(t){var r=t;return nr.defined(r)&&d.is(r.targetRange)&&nr.string(r.targetUri)&&(d.is(r.targetSelectionRange)||nr.undefined(r.targetSelectionRange))&&(d.is(r.originSelectionRange)||nr.undefined(r.originSelectionRange))},(k=p||(p={})).create=function(t,r,n,e){return{red:t,green:r,blue:n,alpha:e}},k.is=function(t){var r=t;return nr.numberRange(r.red,0,1)&&nr.numberRange(r.green,0,1)&&nr.numberRange(r.blue,0,1)&&nr.numberRange(r.alpha,0,1)},(x=j||(j={})).create=function(t,r){return{range:t,color:r}},x.is=function(t){var r=t;return d.is(r.range)&&p.is(r.color)},(I=S||(S={})).create=function(t,r,n){return{label:t,textEdit:r,additionalTextEdits:n}},I.is=function(t){var r=t;return nr.string(r.label)&&(nr.undefined(r.textEdit)||P.is(r))&&(nr.undefined(r.additionalTextEdits)||nr.typedArray(r.additionalTextEdits,P.is))},(T=y||(y={})).Comment="comment",T.Imports="imports",T.Region="region",(O=E||(E={})).create=function(t,r,n,e,i){var o={startLine:t,endLine:r};return nr.defined(n)&&(o.startCharacter=n),nr.defined(e)&&(o.endCharacter=e),nr.defined(i)&&(o.kind=i),o},O.is=function(t){var r=t;return nr.uinteger(r.startLine)&&nr.uinteger(r.startLine)&&(nr.undefined(r.startCharacter)||nr.uinteger(r.startCharacter))&&(nr.undefined(r.endCharacter)||nr.uinteger(r.endCharacter))&&(nr.undefined(r.kind)||nr.string(r.kind))},(_=C||(C={})).create=function(t,r){return{location:t,message:r}},_.is=function(t){var r=t;return nr.defined(r)&&g.is(r.location)&&nr.string(r.message)},(R=D||(D={})).Error=1,R.Warning=2,R.Information=3,R.Hint=4,(L=N||(N={})).Unnecessary=1,L.Deprecated=2,(W||(W={})).is=function(t){return null!=t&&nr.string(t.href)},(M=A||(A={})).create=function(t,r,n,e,i,o){var u={range:t,message:r};return nr.defined(n)&&(u.severity=n),nr.defined(e)&&(u.code=e),nr.defined(i)&&(u.source=i),nr.defined(o)&&(u.relatedInformation=o),u},M.is=function(t){var r,n=t;return nr.defined(n)&&d.is(n.range)&&nr.string(n.message)&&(nr.number(n.severity)||nr.undefined(n.severity))&&(nr.integer(n.code)||nr.string(n.code)||nr.undefined(n.code))&&(nr.undefined(n.codeDescription)||nr.string(null===(r=n.codeDescription)||void 0===r?void 0:r.href))&&(nr.string(n.source)||nr.undefined(n.source))&&(nr.undefined(n.relatedInformation)||nr.typedArray(n.relatedInformation,C.is))},(F=z||(z={})).create=function(t,r){for(var n=[],e=2;e<arguments.length;e++)n[e-2]=arguments[e];var i={title:t,command:r};return nr.defined(n)&&n.length>0&&(i.arguments=n),i},F.is=function(t){var r=t;return nr.defined(r)&&nr.string(r.title)&&nr.string(r.command)},(q=P||(P={})).replace=function(t,r){return{range:t,newText:r}},q.insert=function(t,r){return{range:{start:t,end:t},newText:r}},q.del=function(t){return{range:t,newText:""}},q.is=function(t){var r=t;return nr.objectLiteral(r)&&nr.string(r.newText)&&d.is(r.range)},(H=U||(U={})).create=function(t,r,n){var e={label:t};return void 0!==r&&(e.needsConfirmation=r),void 0!==n&&(e.description=n),e},H.is=function(t){var r=t;return void 0!==r&&nr.objectLiteral(r)&&nr.string(r.label)&&(nr.boolean(r.needsConfirmation)||void 0===r.needsConfirmation)&&(nr.string(r.description)||void 0===r.description)},(V||(V={})).is=function(t){return"string"==typeof t},(B=$||($={})).replace=function(t,r,n){return{range:t,newText:r,annotationId:n}},B.insert=function(t,r,n){return{range:{start:t,end:t},newText:r,annotationId:n}},B.del=function(t,r){return{range:t,newText:"",annotationId:r}},B.is=function(t){var r=t;return P.is(r)&&(U.is(r.annotationId)||V.is(r.annotationId))},(J=G||(G={})).create=function(t,r){return{textDocument:t,edits:r}},J.is=function(t){var r=t;return nr.defined(r)&&st.is(r.textDocument)&&Array.isArray(r.edits)},(Q=K||(K={})).create=function(t,r,n){var e={kind:"create",uri:t};return void 0===r||void 0===r.overwrite&&void 0===r.ignoreIfExists||(e.options=r),void 0!==n&&(e.annotationId=n),e},Q.is=function(t){var r=t;return r&&"create"===r.kind&&nr.string(r.uri)&&(void 0===r.options||(void 0===r.options.overwrite||nr.boolean(r.options.overwrite))&&(void 0===r.options.ignoreIfExists||nr.boolean(r.options.ignoreIfExists)))&&(void 0===r.annotationId||V.is(r.annotationId))},(Y=X||(X={})).create=function(t,r,n,e){var i={kind:"rename",oldUri:t,newUri:r};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(i.options=n),void 0!==e&&(i.annotationId=e),i},Y.is=function(t){var r=t;return r&&"rename"===r.kind&&nr.string(r.oldUri)&&nr.string(r.newUri)&&(void 0===r.options||(void 0===r.options.overwrite||nr.boolean(r.options.overwrite))&&(void 0===r.options.ignoreIfExists||nr.boolean(r.options.ignoreIfExists)))&&(void 0===r.annotationId||V.is(r.annotationId))},(tt=Z||(Z={})).create=function(t,r,n){var e={kind:"delete",uri:t};return void 0===r||void 0===r.recursive&&void 0===r.ignoreIfNotExists||(e.options=r),void 0!==n&&(e.annotationId=n),e},tt.is=function(t){var r=t;return r&&"delete"===r.kind&&nr.string(r.uri)&&(void 0===r.options||(void 0===r.options.recursive||nr.boolean(r.options.recursive))&&(void 0===r.options.ignoreIfNotExists||nr.boolean(r.options.ignoreIfNotExists)))&&(void 0===r.annotationId||V.is(r.annotationId))},(rt||(rt={})).is=function(t){return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((function(t){return nr.string(t.kind)?K.is(t)||X.is(t)||Z.is(t):G.is(t)})))};var et,it,ot,ut,st,at,ct,ft,ht,vt,dt,lt,gt,bt,mt,wt,pt,kt,jt,xt,St,It,yt,Tt,Et,Ot,Ct,_t,Dt,Rt,Nt,Lt,Wt,At,Mt,zt,Ft,Pt,qt,Ut,Ht,Vt,$t,Bt,Gt,Jt,Kt,Qt,Xt,Yt,Zt,tr=function(){function t(t,r){this.edits=t,this.changeAnnotations=r}return t.prototype.insert=function(t,r,n){var e,i;if(void 0===n?e=P.insert(t,r):V.is(n)?(i=n,e=$.insert(t,r,n)):(this.assertChangeAnnotations(this.changeAnnotations),i=this.changeAnnotations.manage(n),e=$.insert(t,r,i)),this.edits.push(e),void 0!==i)return i},t.prototype.replace=function(t,r,n){var e,i;if(void 0===n?e=P.replace(t,r):V.is(n)?(i=n,e=$.replace(t,r,n)):(this.assertChangeAnnotations(this.changeAnnotations),i=this.changeAnnotations.manage(n),e=$.replace(t,r,i)),this.edits.push(e),void 0!==i)return i},t.prototype.delete=function(t,r){var n,e;if(void 0===r?n=P.del(t):V.is(r)?(e=r,n=$.del(t,r)):(this.assertChangeAnnotations(this.changeAnnotations),e=this.changeAnnotations.manage(r),n=$.del(t,e)),this.edits.push(n),void 0!==e)return e},t.prototype.add=function(t){this.edits.push(t)},t.prototype.all=function(){return this.edits},t.prototype.clear=function(){this.edits.splice(0,this.edits.length)},t.prototype.assertChangeAnnotations=function(t){if(void 0===t)throw new Error("Text edit change is not configured to manage change annotations.")},t}(),rr=function(){function t(t){this._annotations=void 0===t?Object.create(null):t,this._counter=0,this._size=0}return t.prototype.all=function(){return this._annotations},Object.defineProperty(t.prototype,"size",{get:function(){return this._size},enumerable:!1,configurable:!0}),t.prototype.manage=function(t,r){var n;if(V.is(t)?n=t:(n=this.nextId(),r=t),void 0!==this._annotations[n])throw new Error("Id "+n+" is already in use.");if(void 0===r)throw new Error("No annotation provided for id "+n);return this._annotations[n]=r,this._size++,n},t.prototype.nextId=function(){return this._counter++,this._counter.toString()},t}();!function(){function t(t){var r=this;this._textEditChanges=Object.create(null),void 0!==t?(this._workspaceEdit=t,t.documentChanges?(this._changeAnnotations=new rr(t.changeAnnotations),t.changeAnnotations=this._changeAnnotations.all(),t.documentChanges.forEach((function(t){if(G.is(t)){var n=new tr(t.edits,r._changeAnnotations);r._textEditChanges[t.textDocument.uri]=n}}))):t.changes&&Object.keys(t.changes).forEach((function(n){var e=new tr(t.changes[n]);r._textEditChanges[n]=e}))):this._workspaceEdit={}}Object.defineProperty(t.prototype,"edit",{get:function(){return this.initDocumentChanges(),void 0!==this._changeAnnotations&&(this._workspaceEdit.changeAnnotations=0===this._changeAnnotations.size?void 0:this._changeAnnotations.all()),this._workspaceEdit},enumerable:!1,configurable:!0}),t.prototype.getTextEditChange=function(t){if(st.is(t)){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var r={uri:t.uri,version:t.version};return(n=this._textEditChanges[r.uri])||(this._workspaceEdit.documentChanges.push({textDocument:r,edits:e=[]}),n=new tr(e,this._changeAnnotations),this._textEditChanges[r.uri]=n),n}if(this.initChanges(),void 0===this._workspaceEdit.changes)throw new Error("Workspace edit is not configured for normal text edit changes.");var n,e;return(n=this._textEditChanges[t])||(this._workspaceEdit.changes[t]=e=[],n=new tr(e),this._textEditChanges[t]=n),n},t.prototype.initDocumentChanges=function(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._changeAnnotations=new rr,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())},t.prototype.initChanges=function(){void 0===this._workspaceEdit.documentChanges&&void 0===this._workspaceEdit.changes&&(this._workspaceEdit.changes=Object.create(null))},t.prototype.createFile=function(t,r,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var e,i,o;if(U.is(r)||V.is(r)?e=r:n=r,void 0===e?i=K.create(t,n):(o=V.is(e)?e:this._changeAnnotations.manage(e),i=K.create(t,n,o)),this._workspaceEdit.documentChanges.push(i),void 0!==o)return o},t.prototype.renameFile=function(t,r,n,e){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var i,o,u;if(U.is(n)||V.is(n)?i=n:e=n,void 0===i?o=X.create(t,r,e):(u=V.is(i)?i:this._changeAnnotations.manage(i),o=X.create(t,r,e,u)),this._workspaceEdit.documentChanges.push(o),void 0!==u)return u},t.prototype.deleteFile=function(t,r,n){if(this.initDocumentChanges(),void 0===this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var e,i,o;if(U.is(r)||V.is(r)?e=r:n=r,void 0===e?i=Z.create(t,n):(o=V.is(e)?e:this._changeAnnotations.manage(e),i=Z.create(t,n,o)),this._workspaceEdit.documentChanges.push(i),void 0!==o)return o}}(),(it=et||(et={})).create=function(t){return{uri:t}},it.is=function(t){var r=t;return nr.defined(r)&&nr.string(r.uri)},(ut=ot||(ot={})).create=function(t,r){return{uri:t,version:r}},ut.is=function(t){var r=t;return nr.defined(r)&&nr.string(r.uri)&&nr.integer(r.version)},(at=st||(st={})).create=function(t,r){return{uri:t,version:r}},at.is=function(t){var r=t;return nr.defined(r)&&nr.string(r.uri)&&(null===r.version||nr.integer(r.version))},(ft=ct||(ct={})).create=function(t,r,n,e){return{uri:t,languageId:r,version:n,text:e}},ft.is=function(t){var r=t;return nr.defined(r)&&nr.string(r.uri)&&nr.string(r.languageId)&&nr.integer(r.version)&&nr.string(r.text)},(vt=ht||(ht={})).PlainText="plaintext",vt.Markdown="markdown",function(t){t.is=function(r){return r===t.PlainText||r===t.Markdown}}(ht||(ht={})),(dt||(dt={})).is=function(t){var r=t;return nr.objectLiteral(t)&&ht.is(r.kind)&&nr.string(r.value)},(gt=lt||(lt={})).Text=1,gt.Method=2,gt.Function=3,gt.Constructor=4,gt.Field=5,gt.Variable=6,gt.Class=7,gt.Interface=8,gt.Module=9,gt.Property=10,gt.Unit=11,gt.Value=12,gt.Enum=13,gt.Keyword=14,gt.Snippet=15,gt.Color=16,gt.File=17,gt.Reference=18,gt.Folder=19,gt.EnumMember=20,gt.Constant=21,gt.Struct=22,gt.Event=23,gt.Operator=24,gt.TypeParameter=25,(mt=bt||(bt={})).PlainText=1,mt.Snippet=2,(wt||(wt={})).Deprecated=1,(kt=pt||(pt={})).create=function(t,r,n){return{newText:t,insert:r,replace:n}},kt.is=function(t){var r=t;return r&&nr.string(r.newText)&&d.is(r.insert)&&d.is(r.replace)},(xt=jt||(jt={})).asIs=1,xt.adjustIndentation=2,(St||(St={})).create=function(t){return{label:t}},(It||(It={})).create=function(t,r){return{items:t||[],isIncomplete:!!r}},(Tt=yt||(yt={})).fromPlainText=function(t){return t.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},Tt.is=function(t){var r=t;return nr.string(r)||nr.objectLiteral(r)&&nr.string(r.language)&&nr.string(r.value)},(Et||(Et={})).is=function(t){var r=t;return!!r&&nr.objectLiteral(r)&&(dt.is(r.contents)||yt.is(r.contents)||nr.typedArray(r.contents,yt.is))&&(void 0===t.range||d.is(t.range))},(Ot||(Ot={})).create=function(t,r){return r?{label:t,documentation:r}:{label:t}},(Ct||(Ct={})).create=function(t,r){for(var n=[],e=2;e<arguments.length;e++)n[e-2]=arguments[e];var i={label:t};return nr.defined(r)&&(i.documentation=r),i.parameters=nr.defined(n)?n:[],i},(Dt=_t||(_t={})).Text=1,Dt.Read=2,Dt.Write=3,(Rt||(Rt={})).create=function(t,r){var n={range:t};return nr.number(r)&&(n.kind=r),n},(Lt=Nt||(Nt={})).File=1,Lt.Module=2,Lt.Namespace=3,Lt.Package=4,Lt.Class=5,Lt.Method=6,Lt.Property=7,Lt.Field=8,Lt.Constructor=9,Lt.Enum=10,Lt.Interface=11,Lt.Function=12,Lt.Variable=13,Lt.Constant=14,Lt.String=15,Lt.Number=16,Lt.Boolean=17,Lt.Array=18,Lt.Object=19,Lt.Key=20,Lt.Null=21,Lt.EnumMember=22,Lt.Struct=23,Lt.Event=24,Lt.Operator=25,Lt.TypeParameter=26,(Wt||(Wt={})).Deprecated=1,(At||(At={})).create=function(t,r,n,e,i){var o={name:t,kind:r,location:{uri:e,range:n}};return i&&(o.containerName=i),o},(zt=Mt||(Mt={})).create=function(t,r,n,e,i,o){var u={name:t,detail:r,kind:n,range:e,selectionRange:i};return void 0!==o&&(u.children=o),u},zt.is=function(t){var r=t;return r&&nr.string(r.name)&&nr.number(r.kind)&&d.is(r.range)&&d.is(r.selectionRange)&&(void 0===r.detail||nr.string(r.detail))&&(void 0===r.deprecated||nr.boolean(r.deprecated))&&(void 0===r.children||Array.isArray(r.children))&&(void 0===r.tags||Array.isArray(r.tags))},(Pt=Ft||(Ft={})).Empty="",Pt.QuickFix="quickfix",Pt.Refactor="refactor",Pt.RefactorExtract="refactor.extract",Pt.RefactorInline="refactor.inline",Pt.RefactorRewrite="refactor.rewrite",Pt.Source="source",Pt.SourceOrganizeImports="source.organizeImports",Pt.SourceFixAll="source.fixAll",(Ut=qt||(qt={})).create=function(t,r){var n={diagnostics:t};return null!=r&&(n.only=r),n},Ut.is=function(t){var r=t;return nr.defined(r)&&nr.typedArray(r.diagnostics,A.is)&&(void 0===r.only||nr.typedArray(r.only,nr.string))},(Vt=Ht||(Ht={})).create=function(t,r,n){var e={title:t},i=!0;return"string"==typeof r?(i=!1,e.kind=r):z.is(r)?e.command=r:e.edit=r,i&&void 0!==n&&(e.kind=n),e},Vt.is=function(t){var r=t;return r&&nr.string(r.title)&&(void 0===r.diagnostics||nr.typedArray(r.diagnostics,A.is))&&(void 0===r.kind||nr.string(r.kind))&&(void 0!==r.edit||void 0!==r.command)&&(void 0===r.command||z.is(r.command))&&(void 0===r.isPreferred||nr.boolean(r.isPreferred))&&(void 0===r.edit||rt.is(r.edit))},(Bt=$t||($t={})).create=function(t,r){var n={range:t};return nr.defined(r)&&(n.data=r),n},Bt.is=function(t){var r=t;return nr.defined(r)&&d.is(r.range)&&(nr.undefined(r.command)||z.is(r.command))},(Jt=Gt||(Gt={})).create=function(t,r){return{tabSize:t,insertSpaces:r}},Jt.is=function(t){var r=t;return nr.defined(r)&&nr.uinteger(r.tabSize)&&nr.boolean(r.insertSpaces)},(Qt=Kt||(Kt={})).create=function(t,r,n){return{range:t,target:r,data:n}},Qt.is=function(t){var r=t;return nr.defined(r)&&d.is(r.range)&&(nr.undefined(r.target)||nr.string(r.target))},(Yt=Xt||(Xt={})).create=function(t,r){return{range:t,parent:r}},Yt.is=function(t){var r=t;return void 0!==r&&d.is(r.range)&&(void 0===r.parent||Yt.is(r.parent))},function(t){function r(t,n){if(t.length<=1)return t;var e=t.length/2|0,i=t.slice(0,e),o=t.slice(e);r(i,n),r(o,n);for(var u=0,s=0,a=0;u<i.length&&s<o.length;){var c=n(i[u],o[s]);t[a++]=c<=0?i[u++]:o[s++]}for(;u<i.length;)t[a++]=i[u++];for(;s<o.length;)t[a++]=o[s++];return t}t.create=function(t,r,n,e){return new or(t,r,n,e)},t.is=function(t){var r=t;return!!(nr.defined(r)&&nr.string(r.uri)&&(nr.undefined(r.languageId)||nr.string(r.languageId))&&nr.uinteger(r.lineCount)&&nr.func(r.getText)&&nr.func(r.positionAt)&&nr.func(r.offsetAt))},t.applyEdits=function(t,n){for(var e=t.getText(),i=r(n,(function(t,r){var n=t.range.start.line-r.range.start.line;return 0===n?t.range.start.character-r.range.start.character:n})),o=e.length,u=i.length-1;u>=0;u--){var s=i[u],a=t.offsetAt(s.range.start),c=t.offsetAt(s.range.end);if(!(c<=o))throw new Error("Overlapping edit");e=e.substring(0,a)+s.newText+e.substring(c,e.length),o=a}return e}}(Zt||(Zt={}));var nr,er,ir,or=function(){function t(t,r,n,e){this._uri=t,this._languageId=r,this._version=n,this._content=e,this._lineOffsets=void 0}return Object.defineProperty(t.prototype,"uri",{get:function(){return this._uri},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"languageId",{get:function(){return this._languageId},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"version",{get:function(){return this._version},enumerable:!1,configurable:!0}),t.prototype.getText=function(t){if(t){var r=this.offsetAt(t.start),n=this.offsetAt(t.end);return this._content.substring(r,n)}return this._content},t.prototype.update=function(t,r){this._content=t.text,this._version=r,this._lineOffsets=void 0},t.prototype.getLineOffsets=function(){if(void 0===this._lineOffsets){for(var t=[],r=this._content,n=!0,e=0;e<r.length;e++){n&&(t.push(e),n=!1);var i=r.charAt(e);n="\r"===i||"\n"===i,"\r"===i&&e+1<r.length&&"\n"===r.charAt(e+1)&&e++}n&&r.length>0&&t.push(r.length),this._lineOffsets=t}return this._lineOffsets},t.prototype.positionAt=function(t){t=Math.max(Math.min(t,this._content.length),0);var r=this.getLineOffsets(),n=0,e=r.length;if(0===e)return h.create(0,t);for(;n<e;){var i=Math.floor((n+e)/2);r[i]>t?e=i:n=i+1}var o=n-1;return h.create(o,t-r[o])},t.prototype.offsetAt=function(t){var r=this.getLineOffsets();if(t.line>=r.length)return this._content.length;if(t.line<0)return 0;var n=r[t.line];return Math.max(Math.min(n+t.character,t.line+1<r.length?r[t.line+1]:this._content.length),n)},Object.defineProperty(t.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!1,configurable:!0}),t}();er=nr||(nr={}),ir=Object.prototype.toString,er.defined=function(t){return void 0!==t},er.undefined=function(t){return void 0===t},er.boolean=function(t){return!0===t||!1===t},er.string=function(t){return"[object String]"===ir.call(t)},er.number=function(t){return"[object Number]"===ir.call(t)},er.numberRange=function(t,r,n){return"[object Number]"===ir.call(t)&&r<=t&&t<=n},er.integer=function(t){return"[object Number]"===ir.call(t)&&-2147483648<=t&&t<=2147483647},er.uinteger=function(t){return"[object Number]"===ir.call(t)&&0<=t&&t<=2147483647},er.func=function(t){return"[object Function]"===ir.call(t)},er.objectLiteral=function(t){return null!==t&&"object"==typeof t},er.typedArray=function(t,r){return Array.isArray(t)&&t.every(r)};var ur=class{constructor(t,r,n){this._languageId=t,this._worker=r;const e=t=>{let r,n=t.getLanguageId();n===this._languageId&&(this._listener[t.uri.toString()]=t.onDidChangeContent((()=>{window.clearTimeout(r),r=window.setTimeout((()=>this._doValidate(t.uri,n)),500)})),this._doValidate(t.uri,n))},i=t=>{u.editor.setModelMarkers(t,this._languageId,[]);let r=t.uri.toString(),n=this._listener[r];n&&(n.dispose(),delete this._listener[r])};this._disposables.push(u.editor.onDidCreateModel(e)),this._disposables.push(u.editor.onWillDisposeModel(i)),this._disposables.push(u.editor.onDidChangeModelLanguage((t=>{i(t.model),e(t.model)}))),this._disposables.push(n((()=>{u.editor.getModels().forEach((t=>{t.getLanguageId()===this._languageId&&(i(t),e(t))}))}))),this._disposables.push({dispose:()=>{u.editor.getModels().forEach(i);for(let t in this._listener)this._listener[t].dispose()}}),u.editor.getModels().forEach(e)}_disposables=[];_listener=Object.create(null);dispose(){this._disposables.forEach((t=>t&&t.dispose())),this._disposables.length=0}_doValidate(t,r){this._worker(t).then((r=>r.doValidation(t.toString()))).then((n=>{const e=n.map((t=>function(t,r){let n="number"==typeof r.code?String(r.code):r.code;return{severity:sr(r.severity),startLineNumber:r.range.start.line+1,startColumn:r.range.start.character+1,endLineNumber:r.range.end.line+1,endColumn:r.range.end.character+1,message:r.message,code:n,source:r.source}}(0,t)));let i=u.editor.getModel(t);i&&i.getLanguageId()===r&&u.editor.setModelMarkers(i,r,e)})).then(void 0,(t=>{console.error(t)}))}};function sr(t){switch(t){case D.Error:return u.MarkerSeverity.Error;case D.Warning:return u.MarkerSeverity.Warning;case D.Information:return u.MarkerSeverity.Info;case D.Hint:return u.MarkerSeverity.Hint;default:return u.MarkerSeverity.Info}}var ar=class{constructor(t,r){this._worker=t,this._triggerCharacters=r}get triggerCharacters(){return this._triggerCharacters}provideCompletionItems(t,r,n,e){const i=t.uri;return this._worker(i).then((t=>t.doComplete(i.toString(),cr(r)))).then((n=>{if(!n)return;const e=t.getWordUntilPosition(r),i=new u.Range(r.lineNumber,e.startColumn,r.lineNumber,e.endColumn),o=n.items.map((t=>{const r={label:t.label,insertText:t.insertText||t.label,sortText:t.sortText,filterText:t.filterText,documentation:t.documentation,detail:t.detail,command:(n=t.command,n&&"editor.action.triggerSuggest"===n.command?{id:n.command,title:n.title,arguments:n.arguments}:void 0),range:i,kind:vr(t.kind)};var n,e;return t.textEdit&&(r.range=void 0!==(e=t.textEdit).insert&&void 0!==e.replace?{insert:hr(t.textEdit.insert),replace:hr(t.textEdit.replace)}:hr(t.textEdit.range),r.insertText=t.textEdit.newText),t.additionalTextEdits&&(r.additionalTextEdits=t.additionalTextEdits.map(dr)),t.insertTextFormat===bt.Snippet&&(r.insertTextRules=u.languages.CompletionItemInsertTextRule.InsertAsSnippet),r}));return{isIncomplete:n.isIncomplete,suggestions:o}}))}};function cr(t){if(t)return{character:t.column-1,line:t.lineNumber-1}}function fr(t){if(t)return{start:{line:t.startLineNumber-1,character:t.startColumn-1},end:{line:t.endLineNumber-1,character:t.endColumn-1}}}function hr(t){if(t)return new u.Range(t.start.line+1,t.start.character+1,t.end.line+1,t.end.character+1)}function vr(t){const r=u.languages.CompletionItemKind;switch(t){case lt.Text:return r.Text;case lt.Method:return r.Method;case lt.Function:return r.Function;case lt.Constructor:return r.Constructor;case lt.Field:return r.Field;case lt.Variable:return r.Variable;case lt.Class:return r.Class;case lt.Interface:return r.Interface;case lt.Module:return r.Module;case lt.Property:return r.Property;case lt.Unit:return r.Unit;case lt.Value:return r.Value;case lt.Enum:return r.Enum;case lt.Keyword:return r.Keyword;case lt.Snippet:return r.Snippet;case lt.Color:return r.Color;case lt.File:return r.File;case lt.Reference:return r.Reference}return r.Property}function dr(t){if(t)return{range:hr(t.range),text:t.newText}}var lr=class{constructor(t){this._worker=t}provideHover(t,r,n){let e=t.uri;return this._worker(e).then((t=>t.doHover(e.toString(),cr(r)))).then((t=>{if(t)return{range:hr(t.range),contents:br(t.contents)}}))}};function gr(t){return"string"==typeof t?{value:t}:(r=t)&&"object"==typeof r&&"string"==typeof r.kind?"plaintext"===t.kind?{value:t.value.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}:{value:t.value}:{value:"```"+t.language+"\n"+t.value+"\n```\n"};var r}function br(t){if(t)return Array.isArray(t)?t.map(gr):[gr(t)]}var mr=class{constructor(t){this._worker=t}provideDocumentHighlights(t,r,n){const e=t.uri;return this._worker(e).then((t=>t.findDocumentHighlights(e.toString(),cr(r)))).then((t=>{if(t)return t.map((t=>({range:hr(t.range),kind:wr(t.kind)})))}))}};function wr(t){switch(t){case _t.Read:return u.languages.DocumentHighlightKind.Read;case _t.Write:return u.languages.DocumentHighlightKind.Write;case _t.Text:return u.languages.DocumentHighlightKind.Text}return u.languages.DocumentHighlightKind.Text}var pr=class{constructor(t){this._worker=t}provideDefinition(t,r,n){const e=t.uri;return this._worker(e).then((t=>t.findDefinition(e.toString(),cr(r)))).then((t=>{if(t)return[kr(t)]}))}};function kr(t){return{uri:u.Uri.parse(t.uri),range:hr(t.range)}}var jr=class{constructor(t){this._worker=t}provideReferences(t,r,n,e){const i=t.uri;return this._worker(i).then((t=>t.findReferences(i.toString(),cr(r)))).then((t=>{if(t)return t.map(kr)}))}},xr=class{constructor(t){this._worker=t}provideRenameEdits(t,r,n,e){const i=t.uri;return this._worker(i).then((t=>t.doRename(i.toString(),cr(r),n))).then((t=>function(t){if(!t||!t.changes)return;let r=[];for(let n in t.changes){const e=u.Uri.parse(n);for(let i of t.changes[n])r.push({resource:e,versionId:void 0,textEdit:{range:hr(i.range),text:i.newText}})}return{edits:r}}(t)))}},Sr=class{constructor(t){this._worker=t}provideDocumentSymbols(t,r){const n=t.uri;return this._worker(n).then((t=>t.findDocumentSymbols(n.toString()))).then((t=>{if(t)return t.map((t=>({name:t.name,detail:"",containerName:t.containerName,kind:Ir(t.kind),range:hr(t.location.range),selectionRange:hr(t.location.range),tags:[]})))}))}};function Ir(t){let r=u.languages.SymbolKind;switch(t){case Nt.File:return r.Array;case Nt.Module:return r.Module;case Nt.Namespace:return r.Namespace;case Nt.Package:return r.Package;case Nt.Class:return r.Class;case Nt.Method:return r.Method;case Nt.Property:return r.Property;case Nt.Field:return r.Field;case Nt.Constructor:return r.Constructor;case Nt.Enum:return r.Enum;case Nt.Interface:return r.Interface;case Nt.Function:return r.Function;case Nt.Variable:return r.Variable;case Nt.Constant:return r.Constant;case Nt.String:return r.String;case Nt.Number:return r.Number;case Nt.Boolean:return r.Boolean;case Nt.Array:return r.Array}return r.Function}var yr=class{constructor(t){this._worker=t}provideLinks(t,r){const n=t.uri;return this._worker(n).then((t=>t.findDocumentLinks(n.toString()))).then((t=>{if(t)return{links:t.map((t=>({range:hr(t.range),url:t.target})))}}))}},Tr=class{constructor(t){this._worker=t}provideDocumentFormattingEdits(t,r,n){const e=t.uri;return this._worker(e).then((t=>t.format(e.toString(),null,Or(r)).then((t=>{if(t&&0!==t.length)return t.map(dr)}))))}},Er=class{constructor(t){this._worker=t}canFormatMultipleRanges=!1;provideDocumentRangeFormattingEdits(t,r,n,e){const i=t.uri;return this._worker(i).then((t=>t.format(i.toString(),fr(r),Or(n)).then((t=>{if(t&&0!==t.length)return t.map(dr)}))))}};function Or(t){return{tabSize:t.tabSize,insertSpaces:t.insertSpaces}}var Cr,_r=class{constructor(t){this._worker=t}provideDocumentColors(t,r){const n=t.uri;return this._worker(n).then((t=>t.findDocumentColors(n.toString()))).then((t=>{if(t)return t.map((t=>({color:t.color,range:hr(t.range)})))}))}provideColorPresentations(t,r,n){const e=t.uri;return this._worker(e).then((t=>t.getColorPresentations(e.toString(),r.color,fr(r.range)))).then((t=>{if(t)return t.map((t=>{let r={label:t.label};return t.textEdit&&(r.textEdit=dr(t.textEdit)),t.additionalTextEdits&&(r.additionalTextEdits=t.additionalTextEdits.map(dr)),r}))}))}},Dr=class{constructor(t){this._worker=t}provideFoldingRanges(t,r,n){const e=t.uri;return this._worker(e).then((t=>t.getFoldingRanges(e.toString(),r))).then((t=>{if(t)return t.map((t=>{const r={start:t.startLine+1,end:t.endLine+1};return void 0!==t.kind&&(r.kind=function(t){switch(t){case y.Comment:return u.languages.FoldingRangeKind.Comment;case y.Imports:return u.languages.FoldingRangeKind.Imports;case y.Region:return u.languages.FoldingRangeKind.Region}}(t.kind)),r}))}))}},Rr=class{constructor(t){this._worker=t}provideSelectionRanges(t,r,n){const e=t.uri;return this._worker(e).then((t=>t.getSelectionRanges(e.toString(),r.map(cr)))).then((t=>{if(t)return t.map((t=>{const r=[];for(;t;)r.push({range:hr(t.range)}),t=t.parent;return r}))}))}};function Nr(t){return 32===t||9===t||11===t||12===t||160===t||5760===t||t>=8192&&t<=8203||8239===t||8287===t||12288===t||65279===t}function Lr(t){return 10===t||13===t||8232===t||8233===t}function Wr(t){return t>=48&&t<=57}(Cr||(Cr={})).DEFAULT={allowTrailingComma:!1};var Ar=function(t,r){void 0===r&&(r=!1);var n=t.length,e=0,i="",o=0,u=16,s=0,a=0,c=0,f=0,h=0;function v(r,n){for(var i=0,o=0;i<r||!n;){var u=t.charCodeAt(e);if(u>=48&&u<=57)o=16*o+u-48;else if(u>=65&&u<=70)o=16*o+u-65+10;else{if(!(u>=97&&u<=102))break;o=16*o+u-97+10}e++,i++}return i<r&&(o=-1),o}function d(){if(i="",h=0,o=e,a=s,f=c,e>=n)return o=n,u=17;var r=t.charCodeAt(e);if(Nr(r)){do{e++,i+=String.fromCharCode(r),r=t.charCodeAt(e)}while(Nr(r));return u=15}if(Lr(r))return e++,i+=String.fromCharCode(r),13===r&&10===t.charCodeAt(e)&&(e++,i+="\n"),s++,c=e,u=14;switch(r){case 123:return e++,u=1;case 125:return e++,u=2;case 91:return e++,u=3;case 93:return e++,u=4;case 58:return e++,u=6;case 44:return e++,u=5;case 34:return e++,i=function(){for(var r="",i=e;;){if(e>=n){r+=t.substring(i,e),h=2;break}var o=t.charCodeAt(e);if(34===o){r+=t.substring(i,e),e++;break}if(92!==o){if(o>=0&&o<=31){if(Lr(o)){r+=t.substring(i,e),h=2;break}h=6}e++}else{if(r+=t.substring(i,e),++e>=n){h=2;break}switch(t.charCodeAt(e++)){case 34:r+='"';break;case 92:r+="\\";break;case 47:r+="/";break;case 98:r+="\b";break;case 102:r+="\f";break;case 110:r+="\n";break;case 114:r+="\r";break;case 116:r+="\t";break;case 117:var u=v(4,!0);u>=0?r+=String.fromCharCode(u):h=4;break;default:h=5}i=e}}return r}(),u=10;case 47:var d=e-1;if(47===t.charCodeAt(e+1)){for(e+=2;e<n&&!Lr(t.charCodeAt(e));)e++;return i=t.substring(d,e),u=12}if(42===t.charCodeAt(e+1)){e+=2;for(var g=n-1,b=!1;e<g;){var m=t.charCodeAt(e);if(42===m&&47===t.charCodeAt(e+1)){e+=2,b=!0;break}e++,Lr(m)&&(13===m&&10===t.charCodeAt(e)&&e++,s++,c=e)}return b||(e++,h=1),i=t.substring(d,e),u=13}return i+=String.fromCharCode(r),e++,u=16;case 45:if(i+=String.fromCharCode(r),++e===n||!Wr(t.charCodeAt(e)))return u=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return i+=function(){var r=e;if(48===t.charCodeAt(e))e++;else for(e++;e<t.length&&Wr(t.charCodeAt(e));)e++;if(e<t.length&&46===t.charCodeAt(e)){if(!(++e<t.length&&Wr(t.charCodeAt(e))))return h=3,t.substring(r,e);for(e++;e<t.length&&Wr(t.charCodeAt(e));)e++}var n=e;if(e<t.length&&(69===t.charCodeAt(e)||101===t.charCodeAt(e)))if((++e<t.length&&43===t.charCodeAt(e)||45===t.charCodeAt(e))&&e++,e<t.length&&Wr(t.charCodeAt(e))){for(e++;e<t.length&&Wr(t.charCodeAt(e));)e++;n=e}else h=3;return t.substring(r,n)}(),u=11;default:for(;e<n&&l(r);)e++,r=t.charCodeAt(e);if(o!==e){switch(i=t.substring(o,e)){case"true":return u=8;case"false":return u=9;case"null":return u=7}return u=16}return i+=String.fromCharCode(r),e++,u=16}}function l(t){if(Nr(t)||Lr(t))return!1;switch(t){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}return{setPosition:function(t){e=t,i="",o=0,u=16,h=0},getPosition:function(){return e},scan:r?function(){var t;do{t=d()}while(t>=12&&t<=15);return t}:d,getToken:function(){return u},getTokenValue:function(){return i},getTokenOffset:function(){return o},getTokenLength:function(){return e-o},getTokenStartLine:function(){return a},getTokenStartCharacter:function(){return o-f},getTokenError:function(){return h}}};var Mr="delimiter.bracket.json",zr="delimiter.array.json",Fr="delimiter.colon.json",Pr="delimiter.comma.json",qr="keyword.json",Ur="keyword.json",Hr="string.value.json",Vr="number.json",$r="string.key.json",Br="comment.block.json",Gr="comment.line.json",Jr=class{constructor(t,r){this.parent=t,this.type=r}static pop(t){return t?t.parent:null}static push(t,r){return new Jr(t,r)}static equals(t,r){if(!t&&!r)return!0;if(!t||!r)return!1;for(;t&&r;){if(t===r)return!0;if(t.type!==r.type)return!1;t=t.parent,r=r.parent}return!0}},Kr=class{_state;scanError;lastWasColon;parents;constructor(t,r,n,e){this._state=t,this.scanError=r,this.lastWasColon=n,this.parents=e}clone(){return new Kr(this._state,this.scanError,this.lastWasColon,this.parents)}equals(t){return t===this||!!(t&&t instanceof Kr)&&this.scanError===t.scanError&&this.lastWasColon===t.lastWasColon&&Jr.equals(this.parents,t.parents)}getStateData(){return this._state}setStateData(t){this._state=t}},Qr=class extends ur{constructor(t,r,n){super(t,r,n.onDidChange),this._disposables.push(u.editor.onWillDisposeModel((t=>{this._resetSchema(t.uri)}))),this._disposables.push(u.editor.onDidChangeModelLanguage((t=>{this._resetSchema(t.model.uri)})))}_resetSchema(t){this._worker().then((r=>{r.resetSchema(t.toString())}))}};function Xr(t){const r=[],n=[],e=new nt(t);r.push(e);const i=(...t)=>e.getLanguageServiceWorker(...t);function o(){const{languageId:r,modeConfiguration:e}=t;var o;Zr(n),e.documentFormattingEdits&&n.push(u.languages.registerDocumentFormattingEditProvider(r,new Tr(i))),e.documentRangeFormattingEdits&&n.push(u.languages.registerDocumentRangeFormattingEditProvider(r,new Er(i))),e.completionItems&&n.push(u.languages.registerCompletionItemProvider(r,new ar(i,[" ",":",'"']))),e.hovers&&n.push(u.languages.registerHoverProvider(r,new lr(i))),e.documentSymbols&&n.push(u.languages.registerDocumentSymbolProvider(r,new Sr(i))),e.tokens&&n.push(u.languages.setTokensProvider(r,(o=!0,{getInitialState:()=>new Kr(null,null,!1,null),tokenize:(t,r)=>function(t,r,n,e=0){let i=0,o=!1;switch(n.scanError){case 2:r='"'+r,i=1;break;case 1:r="/*"+r,i=2}const u=Ar(r);let s=n.lastWasColon,a=n.parents;const c={tokens:[],endState:n.clone()};for(;;){let f=e+u.getPosition(),h="";const v=u.scan();if(17===v)break;if(f===e+u.getPosition())throw new Error("Scanner did not advance, next 3 characters are: "+r.substr(u.getPosition(),3));switch(o&&(f-=i),o=i>0,v){case 1:a=Jr.push(a,0),h=Mr,s=!1;break;case 2:a=Jr.pop(a),h=Mr,s=!1;break;case 3:a=Jr.push(a,1),h=zr,s=!1;break;case 4:a=Jr.pop(a),h=zr,s=!1;break;case 6:h=Fr,s=!0;break;case 5:h=Pr,s=!1;break;case 8:case 9:h=qr,s=!1;break;case 7:h=Ur,s=!1;break;case 10:h=s||1===(a?a.type:0)?Hr:$r,s=!1;break;case 11:h=Vr,s=!1}if(t)switch(v){case 12:h=Gr;break;case 13:h=Br}c.endState=new Kr(n.getStateData(),u.getTokenError(),s,a),c.tokens.push({startIndex:f,scopes:h})}return c}(o,t,r)}))),e.colors&&n.push(u.languages.registerColorProvider(r,new _r(i))),e.foldingRanges&&n.push(u.languages.registerFoldingRangeProvider(r,new Dr(i))),e.diagnostics&&n.push(new Qr(r,i,t)),e.selectionRanges&&n.push(u.languages.registerSelectionRangeProvider(r,new Rr(i)))}o(),r.push(u.languages.setLanguageConfiguration(t.languageId,tn));let s=t.modeConfiguration;return t.onDidChange((t=>{t.modeConfiguration!==s&&(s=t.modeConfiguration,o())})),r.push(Yr(n)),Yr(r)}function Yr(t){return{dispose:()=>Zr(t)}}function Zr(t){for(;t.length;)t.pop().dispose()}var tn={wordPattern:/(-?\d*\.\d\w*)|([^\[\{\]\}\:\"\,\s]+)/g,comments:{lineComment:"//",blockComment:["/*","*/"]},brackets:[["{","}"],["[","]"]],autoClosingPairs:[{open:"{",close:"}",notIn:["string"]},{open:"[",close:"]",notIn:["string"]},{open:'"',close:'"',notIn:["string"]}]};export{ar as CompletionAdapter,pr as DefinitionAdapter,ur as DiagnosticsAdapter,_r as DocumentColorAdapter,Tr as DocumentFormattingEditProvider,mr as DocumentHighlightAdapter,yr as DocumentLinkAdapter,Er as DocumentRangeFormattingEditProvider,Sr as DocumentSymbolAdapter,Dr as FoldingRangeAdapter,lr as HoverAdapter,jr as ReferenceAdapter,xr as RenameAdapter,Rr as SelectionRangeAdapter,nt as WorkerManager,cr as fromPosition,fr as fromRange,Xr as setupMode,hr as toRange,dr as toTextEdit}