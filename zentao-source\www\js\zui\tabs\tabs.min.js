/*!
 * ZUI: Standard edition - v1.10.0 - 2022-08-17
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2022 cnezsoft.com; Licensed MIT
 */
/*! Some code copy from Bootstrap v3.0.0 by @fat and @mdo. (Copyright 2013 Twitter, Inc. Licensed under http://www.apache.org/licenses/)*/
!function(t,e,a){"use strict";if("undefined"==typeof t)throw new Error("ZUI requires jQuery");Number.isNaN||"function"!=typeof isNaN||(Number.isNaN=isNaN),Number.parseInt||"function"!=typeof parseInt||(Number.parseInt=parseInt),Number.parseFloat||"function"!=typeof parseFloat||(Number.parseFloat=parseFloat),t.zui||(t.zui=function(e){t.isPlainObject(e)&&t.extend(t.zui,e)});var n={all:-1,left:0,middle:1,right:2},o=0;t.zui({uuid:function(t){var e=1e5*(Date.now()-1580890015292)+10*Math.floor(1e4*Math.random())+o++%10;return t?e:e.toString(36)},callEvent:function(t,e,n){if("function"==typeof t){n!==a&&(t=t.bind(n));var o=t(e);return e&&(e.result=o),!(o!==a&&!o)}return 1},strCode:function(t){var e=0;if("string"!=typeof t&&(t=String(t)),t&&t.length)for(var a=0;a<t.length;++a)e+=(a+1)*t.charCodeAt(a);return e},getMouseButtonCode:function(t){return"number"!=typeof t&&(t=n[t]),t!==a&&null!==t||(t=-1),t},defaultLang:"en",clientLang:function(){var a,n=e.config;if("undefined"!=typeof n&&n.clientLang&&(a=n.clientLang),!a){var o=t("html").attr("lang");a=o?o:navigator.userLanguage||navigator.userLanguage||t.zui.defaultLang}return a.replace("-","_").toLowerCase()},langDataMap:{},addLangData:function(e,a,n){var o={};n&&a&&e?(o[a]={},o[a][e]=n):e&&a&&!n?(n=a,t.each(n,function(t){o[t]={},o[t][e]=n[t]})):!e||a||n||t.each(e,function(e){var a=n[e];t.each(a,function(t){o[t]||(o[t]={}),o[t][e]=a[t]})}),t.extend(!0,t.zui.langDataMap,o)},getLangData:function(e,a,n){if(!arguments.length)return t.extend({},t.zui.langDataMap);if(1===arguments.length)return t.extend({},t.zui.langDataMap[e]);if(2===arguments.length){var o=t.zui.langDataMap[e];return o?a?o[a]:o:{}}if(3===arguments.length){a=a||t.zui.clientLang();var o=t.zui.langDataMap[e],i=o?o[a]:{};return t.extend(!0,{},n[a]||n.en||n.zh_cn,i)}return null},lang:function(){return arguments.length&&t.isPlainObject(arguments[arguments.length-1])?t.zui.addLangData.apply(null,arguments):t.zui.getLangData.apply(null,arguments)},_scrollbarWidth:0,getScrollbarSize:function(){var e=t.zui._scrollbarWidth;if(!e){var a=document.createElement("div");a.className="scrollbar-measure",document.body.appendChild(a),t.zui._scrollbarWidth=e=a.offsetWidth-a.clientWidth,document.body.removeChild(a)}return e},checkBodyScrollbar:function(){return document.body.clientWidth>=e.innerWidth?0:t.zui.getScrollbarSize()},fixBodyScrollbar:function(){if(t.zui.checkBodyScrollbar()){var e=t("body"),a=parseInt(e.css("padding-right")||0,10);return t.zui._scrollbarWidth&&e.css({paddingRight:a+t.zui._scrollbarWidth,overflowY:"hidden"}),!0}},resetBodyScrollbar:function(){t("body").css({paddingRight:"",overflowY:""})}}),t.fn.callEvent=function(e,n,o){var i=t(this),r=e.indexOf(".zui."),s=r<0?e:e.substring(0,r),l=t.Event(s,n);if(o===a&&r>0&&(o=i.data(e.substring(r+1))),o&&o.options){var c=o.options[s];"function"==typeof c&&(l.result=t.zui.callEvent(c,l,o))}return i.trigger(l),l},t.fn.callComEvent=function(t,e,n){n===a||Array.isArray(n)||(n=[n]);var o,i=this;i.trigger(e,n);var r=t.options[e];return r&&(o=r.apply(t,n)),o}}(jQuery,window,void 0),function(){"use strict";function t(t,e){return a&&!e?requestAnimationFrame(t):setTimeout(t,e||0)}function e(t){return a?cancelAnimationFrame(t):void clearTimeout(t)}var a="function"==typeof window.requestAnimationFrame;$.zui({asap:t,clearAsap:e})}(),function(t){"use strict";t.fn.fixOlPd=function(e){return e=e||10,this.each(function(){var a=t(this);a.css("paddingLeft",Math.ceil(Math.log10(a.children().length))*e+10)})},t(function(){t(".ol-pd-fix,.article ol").fixOlPd()})}(jQuery),+function(t){"use strict";var e="zui.tab",a=function(e){this.element=t(e)};a.prototype.show=function(){var a=this.element,n=a.closest("ul:not(.dropdown-menu)"),o=a.attr("data-target")||a.attr("data-tab");if(o||(o=a.attr("href"),o=o&&o.replace(/.*(?=#[^\s]*$)/,"")),!a.parent("li").hasClass("active")){var i=n.find(".active:last a")[0],r=t.Event("show."+e,{relatedTarget:i});if(a.trigger(r),!r.isDefaultPrevented()){var s=t(o);this.activate(a.parent("li"),n),this.activate(s,s.parent(),function(){a.trigger({type:"shown."+e,relatedTarget:i})})}}},a.prototype.activate=function(e,a,n){function o(){i.removeClass("active").find("> .dropdown-menu > .active").removeClass("active"),e.addClass("active"),r?(e[0].offsetWidth,e.addClass("in")):e.removeClass("fade"),e.parent(".dropdown-menu")&&e.closest("li.dropdown").addClass("active"),n&&n()}var i=a.find("> .active"),r=n&&t.support.transition&&i.hasClass("fade");r?i.one(t.support.transition.end,o).emulateTransitionEnd(150):o(),i.removeClass("in")};var n=t.fn.tab;t.fn.tab=function(n){return this.each(function(){var o=t(this),i=o.data(e);i||o.data(e,i=new a(this)),"string"==typeof n&&i[n]()})},t.fn.tab.Constructor=a,t.fn.tab.noConflict=function(){return t.fn.tab=n,this},t(document).on("click.zui.tab.data-api",'[data-toggle="tab"], [data-tab]',function(e){e.preventDefault(),t(this).tab("show")})}(window.jQuery),/*!
 * jQuery resize event - v1.1
 * http://benalman.com/projects/jquery-resize-plugin/
 * Copyright (c) 2010 "Cowboy" Ben Alman
 * MIT & GPL http://benalman.com/about/license/
 */
function(t,e,a){"$:nomunge";function n(){o=e[s](function(){i.each(function(){var e=t(this),a=e.width(),n=e.height(),o=t.data(this,c);a===o.w&&n===o.h||e.trigger(l,[o.w=a,o.h=n])}),n()},r[d])}var o,i=t([]),r=t.resize=t.extend(t.resize,{}),s="setTimeout",l="resize",c=l+"-special-event",d="delay",u="throttleWindow";r[d]=250,r[u]=!0,t.event.special[l]={setup:function(){if(!r[u]&&this[s])return!1;var e=t(this);i=i.add(e),t.data(this,c,{w:e.width(),h:e.height()}),1===i.length&&n()},teardown:function(){if(!r[u]&&this[s])return!1;var e=t(this);i=i.not(e),e.removeData(c),i.length||clearTimeout(o)},add:function(e){function n(e,n,i){var r=t(this),s=t.data(this,c)||{};s.w=n!==a?n:r.width(),s.h=i!==a?i:r.height(),o.apply(this,arguments)}if(!r[u]&&this[s])return!1;var o;return"function"==typeof e?(o=e,n):(o=e.handler,void(e.handler=n))}}}(jQuery,this),function(){"use strict";var t=function(t,e){if(arguments.length>1){var a;if(2==arguments.length&&"object"==typeof e)for(var n in e)void 0!==e[n]&&(a=new RegExp("({"+n+"})","g"),t=t.replace(a,e[n]));else for(var o=1;o<arguments.length;o++)void 0!==arguments[o]&&(a=new RegExp("({["+(o-1)+"]})","g"),t=t.replace(a,arguments[o]))}return t},e=function(t){if(null!==t){var e,a;return a=/\d*/i,e=t.match(a),e==t}return!1},a={formatString:t,string:{format:t,isNum:e}};window.$&&window.$.zui?$.zui(a):window.stringHelper=a.string,window.noStringPrototypeHelper||(String.prototype.format||(String.prototype.format=function(){var e=[].slice.call(arguments);return e.unshift(this),t.apply(this,e)}),String.prototype.isNum||(String.prototype.isNum=function(){return e(this)}),String.prototype.endsWith||(String.prototype.endsWith=function(t,e){return(void 0===e||e>this.length)&&(e=this.length),this.substring(e-t.length,e)===t}),String.prototype.startsWith||Object.defineProperty(String.prototype,"startsWith",{value:function(t,e){return e=!e||e<0?0:+e,this.substring(e,e+t.length)===t}}),String.prototype.includes||(String.prototype.includes=function(){return String.prototype.indexOf.apply(this,arguments)!==-1}))}(),function(t,e,a){"use strict";var n="zui.droppable",o={target:".droppable-target",deviation:5,sensorOffsetX:0,sensorOffsetY:0,dropToClass:"drop-to",dropTargetClass:"drop-target"},i=0,r=function(e,a){var n=this;n.id=i++,n.$=t(e),n.options=t.extend({},o,n.$.data(),a),n.init()};r.DEFAULTS=o,r.NAME=n,r.prototype.trigger=function(e,a){return t.zui.callEvent(this.options[e],a,this)},r.prototype.init=function(){var o,i,r,s,l,c,d,u,f,p,v,g,h,m,b=this,y=b.$,C=b.options,w=C.deviation,T="."+n+"."+b.id,z="mousedown"+T,x="mouseup"+T,$="mousemove"+T,j=C.selector,M=C.handle,N=C.flex,O=C.canMoveHere,S=C.dropToClass,D=C.noShadow,L=y,E=!1;C.dropOnMouseleave&&(x+=" mouseleave"+T);var I=function(e){if(E){if(g={left:e.pageX,top:e.pageY},!s){if(a.abs(g.left-f.left)<w&&a.abs(g.top-f.top)<w)return;var n=o.css("position");"absolute"!=n&&"relative"!=n&&"fixed"!=n&&(d=n,o.css("position","relative")),s=D?{}:L.clone().removeClass("drag-from").addClass("drag-shadow").css({position:"absolute",width:L.outerWidth(),transition:"none"}).appendTo(o),L.addClass("dragging"),i.addClass(C.dropTargetClass),b.trigger("start",{event:e,element:L,shadowElement:D?null:s,targets:i,mouseOffset:g})}var u={left:g.left-v.left,top:g.top-v.top},m={left:u.left-p.left,top:u.top-p.top};D||s.css(m);var y=!1;l=!1,N||i.removeClass(S);var T=null;if(i.each(function(){var e=t(this),a=e.offset(),n=e.outerWidth(),o=e.outerHeight(),i=a.left+C.sensorOffsetX,r=a.top+C.sensorOffsetY;if(g.left>i&&g.top>r&&g.left<i+n&&g.top<r+o&&(T&&T.removeClass(S),T=e,!C.nested))return!1}),T){l=!0;var z=T.data("id");L.data("id")==z&&L.closest(".kanban-lane").data("id")==T.closest(".kanban-lane").data("id")||(c=!1),(null===r||r.data("id")!==z&&!c)&&(y=!0),r=T,N&&i.removeClass(S),r.addClass(S)}N?null!==r&&r.length&&(l=!0):(L.toggleClass("drop-in",l),D||s.toggleClass("drop-in",l)),O&&O(L,r)===!1||b.trigger("drag",{event:e,isIn:l,target:r,element:L,isNew:y,selfTarget:c,clickOffset:v,offset:u,position:m,mouseOffset:g,lastMouseOffset:h}),t.extend(h,g),e.preventDefault()}},R=0,k=function(e){R&&(t.zui.clearAsap||clearTimeout)(R),R=(t.zui.asap||setTimeout)(function(){R=0,I(e)},0)},A=function(a){if(t(e).off(T),clearTimeout(m),E){if(E=!1,d&&o.css("position",d),null===s)return L.removeClass("drag-from"),void b.trigger("always",{target:r,event:a,cancel:!0});l||(r=null);var n=!0;g=a?{left:a.pageX,top:a.pageY}:h;var u={left:g.left-v.left,top:g.top-v.top},f={left:g.left-h.left,top:g.top-h.top};h.left=g.left,h.top=g.top;var y={event:a,isIn:l,target:r,element:L,isNew:!c&&null!==r,selfTarget:c,offset:u,mouseOffset:g,position:{left:u.left-p.left,top:u.top-p.top},lastMouseOffset:h,moveOffset:f};n=b.trigger("beforeDrop",y),n&&l&&b.trigger("drop",y),i.removeClass(S).removeClass(C.dropTargetClass),L.removeClass("dragging").removeClass("drag-from"),D||s.remove(),s=null,b.trigger("finish",y),b.trigger("always",y),a&&a.preventDefault()}},W=function(a){var n=t.zui.getMouseButtonCode(C.mouseButton);if(!(n>-1&&a.button!==n)){var g=t(this);j&&(L=M?g.closest(j):g),L.hasClass("drag-shadow")||C.before&&C.before({event:a,element:L})===!1||(E=!0,o=C.container?"function"==typeof C.container?C.container(L,y):t(C.container).first():j?y:t("body"),i="function"==typeof C.target?C.target(L,y):o.find(C.target),r=null,s=null,l=!1,c=!0,d=null,u=L.offset(),p=o.offset(),p.top=p.top-o.scrollTop(),p.left=p.left-o.scrollLeft(),f={left:a.pageX,top:a.pageY},h=t.extend({},f),v={left:f.left-u.left,top:f.top-u.top},L.addClass("drag-from"),t(e).on($,k).on(x,A),m=setTimeout(function(){t(e).on(z,A)},10),a.preventDefault(),C.stopPropagation&&a.stopPropagation())}};M?y.on(z,M,W):j?y.on(z,j,W):y.on(z,W)},r.prototype.destroy=function(){var a="."+n+"."+this.id;this.$.off(a),t(e).off(a),this.$.data(n,null)},r.prototype.reset=function(){this.destroy(),this.init()},t.fn.droppable=function(e){return this.each(function(){var a=t(this),o=a.data(n),i="object"==typeof e&&e;o||a.data(n,o=new r(this,i)),"string"==typeof e&&o[e]()})},t.fn.droppable.Constructor=r}(jQuery,document,Math),+function(t,e,a){"use strict";if(!t.fn.droppable)return void console.error("Sortable requires droppable.js");var n="zui.sortable",o={selector:"li,div",dragCssClass:"invisible",sortingClass:"sortable-sorting"},i="order",r=function(e,a){var n=this;n.$=t(e),n.options=t.extend({},o,n.$.data(),a),n.init()};r.DEFAULTS=o,r.NAME=n,r.prototype.init=function(){var e,a,n=this,o=n.$,r=n.options,s=r.selector,l=r.containerSelector,c=r.sortingClass,d=r.dragCssClass,u=r.targetSelector,f=r.reverse,p=r.moveDirection,v=function(e){e=e||n.getItems(1);var a=e.length;a&&e.each(function(e){var n=f?a-e:e;t(this).attr("data-"+i,n).data(i,n)})};u||v(),o.droppable({handle:r.trigger,target:u?u:l?s+","+l:s,selector:s,container:r.container||o,always:r.always,flex:!0,lazy:r.lazy,canMoveHere:r.canMoveHere,dropToClass:r.dropToClass,before:r.before,nested:!!l,mouseButton:r.mouseButton,noShadow:r.noShadow,dropOnMouseleave:r.dropOnMouseleave,stopPropagation:r.stopPropagation,start:function(t){if(d&&t.element.addClass(d),e=!1,n.$element=t.element,!p&&t.targets.length>1){var a=t.targets.eq(0).offset(),o=t.targets.eq(1).offset();p=Math.abs(a.left-o.left)>Math.abs(a.top-o.top)?"h":"v"}v(),n.trigger("start",t)},drag:function(t){if(o.addClass(c),t.isIn){var r=t.target,d=t.element,u=l&&r.is(l);if(u)return void(r.children(s).filter(".dragging").length||(r.append(d),v(C),n.trigger(i,{list:C,element:d})));var g=d.data(i),h=r.data(i);if(g!==h){var m="h"===p?"left":"top",b=t.mouseOffset[m]-t.lastMouseOffset[m];if(0!==b){var y=g>h?f:!f;if(!(b<0&&y||b>0&&!y)){a=y?"after":"before",r[a](d),e=!0,n.$target=r,n.$element=d;var C=n.getItems(1);v(C),n.trigger(i,{insert:a,target:r,list:C,element:d})}}}}},finish:function(t){d&&t.element&&t.element.removeClass(d),o.removeClass(c),n.trigger("finish",{insert:a,target:n.$target,list:n.getItems(),element:n.$element,changed:e}),n.$element=null,n.$target=null}})},r.prototype.destroy=function(){this.$.droppable("destroy"),this.$.data(n,null)},r.prototype.reset=function(){this.destroy(),this.init()},r.prototype.getItems=function(e){var a,n=this,o=n.options.targetSelector;return a=o?"function"==typeof o?o(n.$element,n.$):n.$.find(o):n.$.find(n.options.selector),a=a.not(".drag-shadow"),e?a:a.map(function(){var e=t(this);return{item:e,order:e.data("order")}})},r.prototype.trigger=function(e,a){return t.zui.callEvent(this.options[e],a,this)},t.fn.sortable=function(e){return this.each(function(){var a=t(this),o=a.data(n),i="object"==typeof e&&e;o?"object"==typeof e&&o.reset():a.data(n,o=new r(this,i)),"string"==typeof e&&o[e]()})},t.fn.sortable.Constructor=r}(jQuery,window,document),function(t){"use strict";var e=function(e){var a=this;"string"==typeof e?a.url=e:t.isPlainObject(e)&&t.extend(a,e),a.id||(a.id=t.zui.uuid()),a.type||(a.iframe?(a.type="iframe",a.url=a.url||a.iframe):a.ajax?(a.type="ajax",a.url=a.url||(t.isPlainObject(a.ajax)?a.ajax.url:a.ajax)):a.url?a.type=e.ajax?"ajax":"iframe":a.type="custom"),a.createTime=(new Date).getTime(),a.openTime=0,a.onCreate&&a.onCreate.call(a)};e.prototype.open=function(){var t=this;t.openTime=(new Date).getTime(),t.onOpen&&t.onOpen.call(t)},e.prototype.close=function(){var t=this;t.openTime=0,t.onClose&&t.onClose.call(t)},e.create=function(t){return t instanceof e?t:new e(t)};var a="zui.tabs",n={tabs:[],defaultTabIcon:"icon-window",contextMenu:!0,errorTemplate:'<div class="alert alert-block alert-danger with-icon"><i class="icon-warning-sign"></i><div class="content">{0}</div></div>',showMessage:!0,navTemplate:'<nav class="tabs-navbar"></nav>',containerTemplate:'<div class="tabs-container"></div>',autoResizeNavs:!0},o={zh_cn:{reload:"重新加载",close:"关闭",closeOthers:"关闭其他标签页",closeRight:"关闭右侧标签页",reopenLast:"恢复上次关闭的标签页",errorCannotFetchFromRemote:"无法从远程服务器（{0}）获取内容。"},zh_tw:{reload:"重新加載",close:"關閉",closeOthers:"關閉其他標籤頁",closeRight:"關閉右側標籤頁",reopenLast:"恢復上次關閉的標籤頁",errorCannotFetchFromRemote:"無法從遠程服務器（{0}）獲取內容。"},en:{reload:"Reload",close:"Close",closeOthers:"Close others",closeRight:"Close right",reopenLast:"Reopen last",errorCannotFetchFromRemote:"Cannot fetch data from remote server {0}."}},i=function(i,r){var s=this;s.name=a,s.$=t(i),r=s.options=t.extend({},n,this.$.data(),r);var l=t.zui.clientLang(),c=r.lang;t.isPlainObject(c)?s.lang=t.zui.getLangData?t.zui.getLangData(a,l,o):t.extend(!0,{},o[c.lang||l],c):(c=c||l,s.lang=t.zui.getLangData?t.zui.getLangData(a,c,o):o[c]||o.en);var d=s.$.find(".tabs-navbar");d.length||(d=t(r.navTemplate).appendTo(s.$)),s.$navbar=d;var u=d.find(".tabs-nav");u.length||(u=t('<ul class="tabs-nav nav nav-tabs"></ul>').appendTo(d)),s.$nav=u;var f=s.$.find(".tabs-container");f.length||(f=t(r.containerTemplate).appendTo(s.$)),s.$tabs=f,s.activeTabId=r.defaultTab;var p=r.tabs||[];if(s.tabs={},t.each(p,function(t,a){var n=e.create(a);s.tabs[n.id]=n,s.activeTabId||(s.activeTabId=n.id),s.renderTab(n)}),s.closedTabs=[],s.open(s.getActiveTab()),u.on("click."+a,".tab-nav-link",function(){s.open(s.getTab(t(this).data("id")))}).on("click."+a,".tab-nav-close",function(e){s.close(t(this).closest(".tab-nav-link").data("id")),e.stopPropagation()}),r.autoResizeNavs){var v=u;"navbar"===r.autoResizeNavs?v=d:r.autoResizeNavs!==!0&&(v=t(r.autoResizeNavs)),s.$resizeTrigger=v,v.on("resize."+a,function(){s.adjustNavs()})}if(r.contextMenu&&t.fn.contextmenu){var g={selector:".tab-nav-link",items:function(e){return s.createMenuItems(s.getTab(t(this).data("id")))},onShow:function(){s.$.addClass("tabs-show-contextmenu")},onHide:function(){s.$.removeClass("tabs-show-contextmenu")}};"object"==typeof r.contextMenu&&t.extend(g,r.contextMenu),u.contextmenu(g)}};i.prototype.createMenuItems=function(e){var a=this,n=a.lang;return[{label:n.reload,onClick:function(){a.open(e,!0)}},"-",{label:n.close,disabled:e.forbidClose,onClick:function(){a.close(e.id)}},{label:n.closeOthers,disabled:a.$nav.find(".tab-nav-item:not(.hidden)").length<=1,onClick:function(){a.closeOthers(e.id)}},{label:n.closeRight,disabled:!t("#tab-nav-item-"+e.id).next(".tab-nav-item:not(.hidden)").length,onClick:function(){a.closeRight(e.id)}},"-",{label:n.reopenLast,disabled:!a.closedTabs.length,onClick:function(){a.reopen()}}]},i.prototype.adjustNavs=function(e){var a=this;if(!e)return a.adjustNavsTimer&&t.zui.clearAsap(a.adjustNavsTimer),void(a.adjustNavsTimer=t.zui.asap(function(){a.adjustNavs(!0)}));a.adjustNavsTimer&&(a.adjustNavsTimer=null);var n,o=a.$nav,i=o.find(".tab-nav-item:not(.hidden)"),r=a.options.maxNavsWidth;n="function"==typeof r?r(o,a):"number"==typeof r?r:o.width();var s=i.length,l=Math.floor(n/s);l<96&&(l=Math.floor((n-96)/(s-1))),o.toggleClass("tab-nav-condensed",l<=50),i.css("max-width",l)},i.prototype.renderTab=function(e,a){var n=this,o=t("#tab-nav-item-"+e.id);if(!o.length){var i=t('<a class="tab-nav-link" href="javascript:;"><i class="icon"></i><span class="title"></span><i class="close tab-nav-close" title="'+n.lang.close+'">&times;</i></a>').attr({"data-id":e.id});if(o=t('<li class="tab-nav-item" data-id="'+e.id+'" id="tab-nav-item-'+e.id+'" />').append(i).appendTo(n.$nav),a){var r=t("#tab-nav-item-"+a);r.length&&o.insertAfter(r)}n.adjustNavs()}var i=o.find("a").attr("title",e.desc).toggleClass("not-closable",!!e.forbidClose);return i.find(".icon").attr("class","icon "+(e.icon||n.options.defaultTabIcon)),i.find(".title").text(e.title||e.defaultTitle||""),n.$.callComEvent(n,"onRenderTab",[e,i]),o},i.prototype.getActiveTab=function(){var t=this;return t.activeTabId?t.tabs[t.activeTabId]:null},i.prototype.getTab=function(t){var e=this;return t?("object"==typeof t&&(t=t.id),e.tabs[t]):e.getActiveTab()},i.prototype.close=function(e,a){var n=this,o=n.getTab(e);if(o&&(a||!o.forbidClose)){t("#tab-nav-item-"+o.id).remove(),t("#tab-"+o.id).remove(),o.close(),delete n.tabs[o.id],n.closedTabs.push(o),n.$.callComEvent(n,"onClose",o),n.adjustNavs();var i;t.each(n.tabs,function(t,e){(!i||i.openTime<e.openTime)&&(i=e)}),i&&n.open(i)}},i.prototype.open=function(a,n){var o=this;a instanceof e||(a=e.create(a));var i=o.renderTab(a);o.$nav.find(".tab-nav-item.active").removeClass("active"),i.addClass("active");var r=t("#tab-"+a.id);r.length||(r=t('<div class="tab-pane" id="tab-'+a.id+'" />').appendTo(o.$tabs)),o.$tabs.find(".tab-pane.active").removeClass("active"),r.addClass("active"),a.open(),o.activeTabId=a.id,o.tabs[a.id]=a,!n&&a.loaded||o.reload(a),o.$.callComEvent(o,"onOpen",a)},i.prototype.showMessage=function(e,a){t.zui.messager.show(e,t.extend({placement:"center"},this.options.messagerOptions,{type:a}))},i.prototype.reload=function(e){var a=this;if("string"==typeof e?e=a.getTab(e):e||(e=a.getActiveTab()),e){if(!e.openTime)return a.open(e);var n=t("#tab-nav-item-"+e.id).addClass("loading").removeClass("has-error"),o=t("#tab-"+e.id).addClass("loading").removeClass("has-error"),i=function(i,r){if(e.openTime){if(n.removeClass("loading"),o.removeClass("loading"),("string"==typeof i||i instanceof t)&&(e.contentConverter&&(i=e.contentConverter(i,e)),o.empty().append(i),e.title||(i=o.text().replace(/\n/g,""),e.title=i.length>10?i.substr(0,10):i,a.renderTab(e))),r){n.addClass("has-error"),o.addClass("has-error");var s=a.options.showMessage;s&&("function"==typeof s&&(r=s(r)),a.showMessage(r,"danger")),i||o.html(a.options.errorTemplate.format(r))}e.loaded=(new Date).getTime(),a.$.callComEvent(a,"onLoad",e)}};if("ajax"===e.type){var r={type:"get",url:e.url,error:function(t,n,o){i(!1,a.lang.errorCannotFetchFromRemote.format(e.url))},success:function(t){i(t)}};t.isPlainObject(e.ajax)&&(r=t.extend(r,e.ajax)),t.ajax(r)}else if("iframe"===e.type)try{var s="tab-iframe-"+e.id,l=t('<iframe id="'+s+'" name="'+s+'" src="'+e.url+'" frameborder="no"  allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true"  allowtransparency="true" scrolling="auto" style="width: 100%; height: 100%; left: 0px;"></iframe>');l.appendTo(o.empty()),t('<div class="tab-iframe-cover" />').appendTo(o);var c=document.getElementById(s);c.onload=c.onreadystatechange=function(){if(!this.readyState||"complete"==this.readyState){i();var t=c.contentDocument;t&&!e.title&&(e.title=t.title,a.renderTab(e))}}}catch(d){i()}else{var u=e.content||e.custom;"function"==typeof u?(u=u(e,i,a),u!==!0&&i(u)):i(u)}}},i.prototype.closeOthers=function(e){var a=this;a.$nav.find(".tab-nav-link:not(.hidden)").each(function(){var n=t(this).data("id");n!==e&&a.close(n)})},i.prototype.closeRight=function(e){for(var a=t("#tab-nav-item-"+e),n=a.next(".tab-nav-item:not(.hidden)");n.length;)this.close(n.data("id")),n=a.next(".tab-nav-item:not(.hidden)")},i.prototype.closeAll=function(){var e=this;e.$nav.find(".tab-nav-link:not(.hidden)").each(function(){e.close(t(this).data("id"))})},i.prototype.reopen=function(){var t=this;t.closedTabs.length&&t.open(t.closedTabs.pop(),!0)},t.fn.tabs=function(e){return this.each(function(){var n=t(this),o=n.data(a),r="object"==typeof e&&e;o||n.data(a,o=new i(this,r)),"string"==typeof e&&o[e]()})},i.NAME=a,t.fn.tabs.Constructor=i}(jQuery);