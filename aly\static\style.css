/* 全局样式 */
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background-color: #f0f2f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 标题样式 */
h1, h2, h3 {
    color: #333;
    margin-bottom: 20px;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 5px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.3s;
}

.btn-primary {
    background-color: #1890ff;
    color: white;
}

.btn-success {
    background-color: #52c41a;
    color: white;
}

.btn-danger {
    background-color: #ff4d4f;
    color: white;
}

.btn-secondary {
    background-color: #666;
    color: white;
}

.btn:hover {
    opacity: 0.9;
}

/* 表格样式 */
.table-responsive {
    overflow-x: auto;
    margin: 20px 0;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
}

.data-table th,
.data-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.data-table th {
    background-color: #fafafa;
    font-weight: 500;
    color: #333;
}

.data-table tr:hover {
    background-color: #f5f5f5;
}

.data-table tr:last-child td {
    border-bottom: none;
}

/* 索引信息样式 */
.index-info {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin: 20px 0;
}

.index-card {
    border: 1px solid #e8e8e8;
    padding: 16px;
    margin-bottom: 12px;
    border-radius: 6px;
    background-color: #fafafa;
    transition: all 0.3s;
}

.index-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.index-type {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    margin-left: 8px;
    font-weight: 500;
}

.primary-key {
    background-color: #1890ff;
    color: white;
}

.unique-index {
    background-color: #52c41a;
    color: white;
}

.normal-index {
    background-color: #666;
    color: white;
}

/* 表单样式 */
.add-record-form {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin: 20px 0;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s;
}

.form-control:focus {
    border-color: #1890ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
}

/* 表操作区域样式 */
.table-controls {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin: 20px 0;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }
    
    .form-control {
        width: 100%;
    }
    
    .table-responsive {
        margin: 10px 0;
    }
    
    .data-table th,
    .data-table td {
        padding: 8px;
    }
} 