from flask import Flask, render_template, request, redirect, flash, send_file, url_for, session
import mysql.connector
from mysql.connector import pooling
import pandas as pd
from io import StringIO, BytesIO
from dotenv import load_dotenv
import os
import time
import logging
from functools import lru_cache, wraps

# 加载环境变量
load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv('SECRET_KEY', 'your_secret_key')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库配置
# 阿里云数据库配置
ALIYUN_DB_CONFIG = {
    'host': os.getenv('ALIYUN_DB_HOST', 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com'),
    'port': int(os.getenv('ALIYUN_DB_PORT', 3306)),
    'user': os.getenv('ALIYUN_DB_USER', 'cyh'),
    'password': os.getenv('ALIYUN_DB_PASSWORD', 'Qq188788'),
    'connect_timeout': int(os.getenv('ALIYUN_DB_TIMEOUT', 10)),
    'autocommit': True,  # 启用自动提交
    'charset': 'utf8mb4',
    'use_unicode': True,
    'sql_mode': 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'
}

# 工单系统数据库配置
TICKET_DB_CONFIG = {
    'host': os.getenv('TICKET_DB_HOST', '**********'),
    'port': int(os.getenv('TICKET_DB_PORT', 3306)),
    'user': os.getenv('TICKET_DB_USER', 'qyuser'),
    'password': os.getenv('TICKET_DB_PASSWORD', 'C~w9d4kaWS'),
    'connect_timeout': int(os.getenv('TICKET_DB_TIMEOUT', 10)),
    'autocommit': True,  # 启用自动提交
    'charset': 'utf8mb4',
    'use_unicode': True,
    'sql_mode': 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'
}

# 默认数据库配置（兼容旧版本）
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'user': os.getenv('DB_USER', 'cyh'),
    'password': os.getenv('DB_PASSWORD', 'Qq188788'),
    'connect_timeout': int(os.getenv('DB_TIMEOUT', 10)),
    'autocommit': True,  # 启用自动提交
    'charset': 'utf8mb4',
    'use_unicode': True,
    'sql_mode': 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'
}

# 创建连接池字典
connection_pools = {
    'aliyun': pooling.MySQLConnectionPool(
        pool_name="aliyun_pool",
        pool_size=10,  # 连接池大小
        pool_reset_session=False,  # 不重置会话，避免数据库选择被重置
        **ALIYUN_DB_CONFIG
    ),
    'ticket': pooling.MySQLConnectionPool(
        pool_name="ticket_pool",
        pool_size=10,  # 连接池大小
        pool_reset_session=False,  # 不重置会话，避免数据库选择被重置
        **TICKET_DB_CONFIG
    )
}

# 默认连接池（兼容旧版本）
connection_pool = connection_pools['aliyun']

def get_db_connection(pool_name='aliyun'):
    """从指定的连接池获取数据库连接
    
    Args:
        pool_name: 连接池名称，可选值：'aliyun'（阿里云数据库）或 'ticket'（工单系统数据库）
    """
    # 获取指定的连接池，如果不存在则使用默认连接池
    pool = connection_pools.get(pool_name, connection_pools['aliyun'])
    
    retries = 3
    for i in range(retries):
        try:
            return pool.get_connection()
        except mysql.connector.Error as e:
            logger.error(f"数据库连接失败 (池: {pool_name}, 尝试 {i+1}/{retries}): {str(e)}")
            if i == retries - 1:
                raise e
            time.sleep(1)

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            if execution_time > 1.0:  # 记录超过1秒的请求
                logger.warning(f"慢查询警告: {func.__name__} 执行时间: {execution_time:.2f}秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"函数 {func.__name__} 执行失败 (耗时: {execution_time:.2f}秒): {str(e)}")
            raise
    return wrapper

def clear_cache():
    """清理所有缓存"""
    get_databases_cached.cache_clear()
    get_tables_cached.cache_clear()

@lru_cache(maxsize=2)  # 增加缓存大小以支持两种连接类型
def get_databases_cached(connection_type='aliyun'):
    """缓存数据库列表
    
    Args:
        connection_type: 连接类型，可选值：'aliyun'（阿里云数据库）或 'ticket'（工单系统数据库）
    """
    try:
        conn = get_db_connection(connection_type)
        cursor = conn.cursor()
        cursor.execute("SHOW DATABASES")
        databases = [db[0] for db in cursor.fetchall()]
        cursor.close()
        conn.close()
        return databases
    except Exception as e:
        logger.error(f"获取数据库列表失败 (连接: {connection_type}): {str(e)}")
        return []

@app.route('/')
@performance_monitor
def index():
    try:
        # 获取当前连接类型，默认为阿里云
        current_connection = session.get('connection', 'aliyun')
        
        # 使用缓存的数据库列表
        databases = get_databases_cached(current_connection)
        return render_template('index.html', databases=databases, current_connection=current_connection)
    except Exception as e:
        flash(f"Error: {str(e)}")
        return render_template('index.html', databases=[], current_connection=session.get('connection', 'aliyun'))

@app.route('/switch_connection', methods=['POST'])
def switch_connection():
    # 获取选择的连接类型
    connection = request.form.get('connection', 'aliyun')
    
    # 验证连接类型是否有效
    if connection not in connection_pools:
        flash(f"错误：无效的连接类型 '{connection}'")
        return redirect('/')
    
    # 保存连接类型到会话
    session['connection'] = connection
    
    # 清除缓存
    clear_cache()
    
    flash(f"已切换到{'阿里云数据库' if connection == 'aliyun' else '工单系统数据库'}连接")
    return redirect('/')

@app.route('/create_database', methods=['POST'])
def create_database():
    db_name = request.form['db_name']
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        cursor = conn.cursor()
        cursor.execute(f"CREATE DATABASE `{db_name}`")
        conn.commit()
        flash(f"数据库 '{db_name}' 创建成功")
        # 清除缓存
        clear_cache()
    except Exception as e:
        flash(f"Error: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    return redirect('/')

@app.route('/delete_database/<db_name>')
def delete_database(db_name):
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        cursor = conn.cursor()
        cursor.execute(f"DROP DATABASE `{db_name}`")
        conn.commit()
        clear_cache()  # 清理缓存
        flash(f"Database {db_name} deleted successfully!")
    except Exception as e:
        flash(f"Error: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    return redirect('/')

@lru_cache(maxsize=64)  # 增加缓存大小以支持两种连接类型的多个数据库
def get_tables_cached(db_name, connection_type='aliyun'):
    """缓存表列表
    
    Args:
        db_name: 数据库名称
        connection_type: 连接类型，可选值：'aliyun'（阿里云数据库）或 'ticket'（工单系统数据库）
    """
    if not db_name:
        return []
    try:
        conn = get_db_connection(connection_type)
        cursor = conn.cursor()
        # 使用USE语句明确选择数据库
        cursor.execute(f"USE `{db_name}`")
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        cursor.close()
        conn.close()
        return tables
    except Exception as e:
        logger.error(f"获取表列表失败 (数据库: {db_name}, 连接: {connection_type}): {str(e)}")
        return []

@app.route('/database/<db_name>')
@performance_monitor
def database_detail(db_name):
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        # 验证数据库是否存在
        all_databases = get_databases_cached(current_connection)
        if db_name not in all_databases:
            flash(f"错误: 数据库 '{db_name}' 不存在")
            return redirect('/')

        # 使用缓存的表列表
        tables = get_tables_cached(db_name, current_connection)

        return render_template('database.html', db_name=db_name, tables=tables, all_databases=all_databases, current_connection=current_connection)
    except Exception as e:
        logger.error(f"访问数据库详情页面失败: {str(e)}")
        flash(f"错误: {str(e)}")
        return redirect('/')

@app.route('/create_table/<db_name>', methods=['POST'])
def create_table(db_name):
    table_name = request.form['table_name']
    columns = request.form['columns']
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        conn.database = db_name
        cursor = conn.cursor()
        cursor.execute(f"CREATE TABLE {table_name} ({columns})")
        conn.commit()
        flash(f"表 {table_name} 创建成功！")
    except Exception as e:
        flash(f"错误: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    return redirect(f'/database/{db_name}')

@app.route('/import_table/<db_name>', methods=['POST'])
def import_table(db_name):
    file = request.files['file']
    table_name = request.form['table_name']
    conn = None
    cursor = None
    try:
        # 检查文件格式
        if not file.filename.endswith(('.xlsx', '.xls')):
            flash("错误: 仅支持 .xls 和 .xlsx 格式的文件")
            return redirect(f'/database/{db_name}')

        # 读取 Excel 文件
        if file.filename.endswith('.xlsx'):
            df = pd.read_excel(file, engine='openpyxl')
        elif file.filename.endswith('.xls'):
            df = pd.read_excel(file, engine='xlrd')

        # 检查表名是否合法
        if not table_name or not table_name.isidentifier():
            flash("错误: 表名不合法，请使用字母、数字或下划线，且不能以数字开头")
            return redirect(f'/database/{db_name}')

        # 处理列名
        df.columns = df.columns.str.strip()  # 去除列名中的前后空格
        df.columns = [col.replace(' ', '_') for col in df.columns]  # 将空格替换为下划线

        # 处理空值：将 NaN 替换为 None（MySQL 中的 NULL）
        df = df.where(pd.notnull(df), None)  # 将 NaN 替换为 None
        # 将所有数据类型为字符串的列中的 'nan' 和 'NaN' 字符串替换为 None
        for col in df.columns:
            if df[col].dtype == 'object':  # 字符串列
                df[col] = df[col].apply(lambda x: None if isinstance(x, str) and x.lower() == 'nan' else x)

        print(df.isnull().sum())  # 显示每列中 NaN 的数量

        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        conn.database = db_name
        cursor = conn.cursor()

        # 处理时间戳列
        for col in df.columns:
            if pd.api.types.is_datetime64_any_dtype(df[col]):
                df[col] = df[col].dt.strftime('%Y-%m-%d %H:%M:%S')

        # 创建表
        create_table_sql = f"CREATE TABLE `{table_name}` ("
        for col in df.columns:
            safe_col = f"`{col}`"  # 使用反引号包裹列名
            if pd.api.types.is_numeric_dtype(df[col]):
                col_type = 'DOUBLE'
            elif pd.api.types.is_datetime64_any_dtype(df[col]):
                col_type = 'DATETIME'
            else:
                if df[col].str.len().max() <= 255:
                    col_type = 'VARCHAR(255)'
                else:
                    col_type = 'TEXT'
            create_table_sql += f"{safe_col} {col_type}, "
        create_table_sql = create_table_sql.rstrip(", ") + ")"

        cursor.execute(create_table_sql)

        # 批量插入数据 - 性能优化
        columns = list(df.columns)
        safe_columns = [f"`{col}`" for col in columns]
        placeholders = ", ".join(["%s"] * len(columns))
        insert_sql = f"INSERT INTO `{table_name}` ({', '.join(safe_columns)}) VALUES ({placeholders})"

        # 准备批量插入的数据
        batch_data = []
        batch_size = 1000  # 每批插入1000条记录

        for i, row in df.iterrows():
            # 处理每一行数据，将NaN替换为None
            row_values = []
            for col in columns:
                val = row[col]
                if pd.isna(val):
                    row_values.append(None)
                else:
                    row_values.append(val)
            batch_data.append(row_values)

            # 当达到批量大小时执行插入
            if len(batch_data) >= batch_size:
                cursor.executemany(insert_sql, batch_data)
                conn.commit()
                batch_data = []

        # 插入剩余的数据
        if batch_data:
            cursor.executemany(insert_sql, batch_data)
            conn.commit()
        flash(f"表 {table_name} 导入成功！")
    except Exception as e:
        flash(f"错误: {str(e)}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
    return redirect(f'/database/{db_name}')

@app.route('/table/<db_name>/<table_name>')
@performance_monitor
def table_detail(db_name, table_name):
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)  # 每页显示50条记录
    offset = (page - 1) * per_page

    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        cursor = conn.cursor(dictionary=True)
        # 使用USE语句明确选择数据库
        cursor.execute(f"USE `{db_name}`")

        # 获取表的索引信息（缓存优化）
        indexes = get_table_indexes(cursor, db_name, table_name)

        # 获取总记录数
        cursor.execute(f"SELECT COUNT(*) as total FROM `{table_name}`")
        total_records = cursor.fetchone()['total']

        # 分页查询表数据
        cursor.execute(f"SELECT * FROM `{table_name}` LIMIT %s OFFSET %s", (per_page, offset))
        rows = cursor.fetchall()

        # 获取列名
        if rows:
            column_names = list(rows[0].keys())
        else:
            # 如果表为空，获取列名
            cursor.execute(f"DESCRIBE `{table_name}`")
            columns = cursor.fetchall()
            column_names = [col['Field'] for col in columns]

        # 计算分页信息
        total_pages = (total_records + per_page - 1) // per_page
        has_prev = page > 1
        has_next = page < total_pages

        cursor.close()
        conn.close()

        return render_template('table.html',
                            db_name=db_name,
                            table_name=table_name,
                            column_names=column_names,
                            rows=rows,
                            indexes=indexes,
                            pagination={
                                'page': page,
                                'per_page': per_page,
                                'total': total_records,
                                'total_pages': total_pages,
                                'has_prev': has_prev,
                                'has_next': has_next
                            })

    except Exception as e:
        logger.error(f"访问表格时出错: {str(e)}")
        flash(f"错误: {str(e)}")
        return redirect(f'/database/{db_name}')

def get_table_indexes(cursor, db_name, table_name):
    """获取表的索引信息"""
    try:
        # 获取所有索引信息
        cursor.execute("""
            SELECT
                s.INDEX_NAME,
                GROUP_CONCAT(s.COLUMN_NAME ORDER BY s.SEQ_IN_INDEX) as COLUMNS,
                s.INDEX_TYPE,
                s.NON_UNIQUE
            FROM information_schema.STATISTICS s
            WHERE s.TABLE_SCHEMA = %s
            AND s.TABLE_NAME = %s
            GROUP BY s.INDEX_NAME, s.INDEX_TYPE, s.NON_UNIQUE;
        """, (db_name, table_name))

        indexes = []
        for idx in cursor.fetchall():
            # 处理索引类型
            if idx['INDEX_NAME'] == 'PRIMARY':
                index_type = '主键'
            elif idx['NON_UNIQUE'] == 0:
                index_type = '唯一索引'
            else:
                index_type = '非唯一索引'

            indexes.append({
                'name': idx['INDEX_NAME'],
                'columns': idx['COLUMNS'],
                'type': index_type
            })
        return indexes
    except Exception as e:
        logger.error(f"获取索引信息时出错: {str(e)}")
        return []

@app.route('/delete_table/<db_name>/<table_name>')
def delete_table(db_name, table_name):
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        cursor = conn.cursor()
        
        # 明确选择数据库
        cursor.execute(f"USE `{db_name}`")
        
        # 执行删除表操作
        cursor.execute(f"DROP TABLE {table_name}")
        conn.commit()
        
        flash(f"表 {table_name} 删除成功！")
    except Exception as e:
        flash(f"错误: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    return redirect(f'/database/{db_name}')

@app.route('/manage_fields/<db_name>/<table_name>')
def manage_fields(db_name, table_name):
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        conn.database = db_name
        cursor = conn.cursor()
        
        # 获取字段信息
        cursor.execute(f"DESCRIBE {table_name}")
        fields = cursor.fetchall()
    except Exception as e:
        flash(f"错误: {str(e)}")
        fields = []
    finally:
        cursor.close()
        conn.close()
    return render_template('manage_fields.html', db_name=db_name, table_name=table_name, fields=fields)

@app.route('/add_field/<db_name>/<table_name>', methods=['POST'])
def add_field(db_name, table_name):
    field_name = request.form['field_name']
    field_type = request.form['field_type']
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        conn.database = db_name
        cursor = conn.cursor()
        
        # 添加字段
        cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {field_name} {field_type}")
        conn.commit()
        
        flash(f"字段 {field_name} 添加成功！")
    except Exception as e:
        flash(f"错误: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    return redirect(f'/manage_fields/{db_name}/{table_name}')

@app.route('/delete_field/<db_name>/<table_name>', methods=['POST'])
def delete_field(db_name, table_name):
    field = request.form['field']
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        cursor = conn.cursor()
        
        # 明确选择数据库
        cursor.execute(f"USE `{db_name}`")
        
        # 删除字段
        cursor.execute(f"ALTER TABLE {table_name} DROP COLUMN {field}")
        conn.commit()
        
        flash(f"字段 {field} 删除成功！")
    except Exception as e:
        flash(f"错误: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    return redirect(f'/manage_fields/{db_name}/{table_name}')

@app.route('/rename_field/<db_name>/<table_name>', methods=['POST'])
def rename_field(db_name, table_name):
    old_field = request.form['old_field']
    new_field = request.form['new_field']
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        conn.database = db_name
        cursor = conn.cursor()
        
        # 重命名字段
        cursor.execute(f"ALTER TABLE {table_name} RENAME COLUMN {old_field} TO {new_field}")
        conn.commit()
        
        flash(f"字段 {old_field} 重命名为 {new_field} 成功！")
    except Exception as e:
        flash(f"错误: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    return redirect(f'/manage_fields/{db_name}/{table_name}')

@app.route('/add_record/<db_name>/<table_name>', methods=['POST'])
def add_record(db_name, table_name):
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        conn.database = db_name
        cursor = conn.cursor(dictionary=True)
        
        # 获取表字段信息
        cursor.execute(f"DESCRIBE {table_name}")
        columns = [col['Field'] for col in cursor.fetchall()]
        
        # 构建插入语句
        values = [request.form.get(col) for col in columns]
        placeholders = ", ".join(["%s"] * len(columns))
        insert_sql = f"INSERT INTO {table_name} VALUES ({placeholders})"
        
        # 执行插入
        cursor.execute(insert_sql, values)
        conn.commit()
        
        flash("新增记录成功！")
    except Exception as e:
        flash(f"错误: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    return redirect(f'/table/{db_name}/{table_name}')

@app.route('/delete_record/<db_name>/<table_name>', methods=['POST'])
def delete_record(db_name, table_name):
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        cursor = conn.cursor(dictionary=True)
        
        # 明确选择数据库
        cursor.execute(f"USE `{db_name}`")
        
        # 获取主键字段
        cursor.execute(f"SHOW KEYS FROM {table_name} WHERE Key_name = 'PRIMARY'")
        primary_key = cursor.fetchone()['Column_name']
        
        # 删除指定记录
        record_id = request.form['record_id']
        cursor.execute(f"DELETE FROM {table_name} WHERE {primary_key} = %s", (record_id,))
        conn.commit()
        
        flash("删除记录成功！")
    except Exception as e:
        flash(f"错误: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    return redirect(f'/table/{db_name}/{table_name}')

@app.route('/update_record/<db_name>/<table_name>', methods=['POST'])
def update_record(db_name, table_name):
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        conn.database = db_name
        cursor = conn.cursor(dictionary=True)
        
        # 获取主键字段
        cursor.execute(f"SHOW KEYS FROM {table_name} WHERE Key_name = 'PRIMARY'")
        primary_key_info = cursor.fetchone()
        
        if not primary_key_info:
            # 如果没有主键，尝试使用第一个字段作为更新条件
            cursor.execute(f"DESCRIBE {table_name}")
            columns = cursor.fetchall()
            if not columns:
                flash("错误: 表没有字段，无法执行更新操作！")
                return redirect(f'/table/{db_name}/{table_name}')
            primary_key = columns[0]['Field']
            flash("警告: 表没有主键，使用第一个字段作为更新条件。")
        else:
            primary_key = primary_key_info['Column_name']
        
        # 构建更新语句
        record_id = request.form['record_id']
        updates = []
        values = []
        for key, value in request.form.items():
            if key != 'record_id':
                updates.append(f"{key} = %s")
                values.append(value)
        values.append(record_id)
        update_sql = f"UPDATE {table_name} SET {', '.join(updates)} WHERE {primary_key} = %s"
        
        # 执行更新
        cursor.execute(update_sql, values)
        conn.commit()
        
        flash("更新记录成功！")
    except Exception as e:
        flash(f"错误: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    return redirect(f'/table/{db_name}/{table_name}')

@app.route('/batch_delete_records/<db_name>/<table_name>', methods=['POST'])
def batch_delete_records(db_name, table_name):
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        cursor = conn.cursor(dictionary=True)
        
        # 明确选择数据库
        cursor.execute(f"USE `{db_name}`")
        
        # 获取主键字段
        cursor.execute(f"SHOW KEYS FROM {table_name} WHERE Key_name = 'PRIMARY'")
        primary_key_info = cursor.fetchone()
        
        if not primary_key_info:
            # 如果没有主键，尝试使用第一个字段作为删除条件
            cursor.execute(f"DESCRIBE {table_name}")
            columns = cursor.fetchall()
            if not columns:
                flash("错误: 表没有字段，无法执行删除操作！")
                return redirect(f'/table/{db_name}/{table_name}')
            primary_key = columns[0]['Field']
            flash("警告: 表没有主键，使用第一个字段作为删除条件。")
        else:
            primary_key = primary_key_info['Column_name']
        
        # 获取要删除的记录ID
        record_ids = request.form.getlist('record_ids')
        if not record_ids:
            flash("未选择任何记录！")
            return redirect(f'/table/{db_name}/{table_name}')
        
        # 构建批量删除语句
        placeholders = ", ".join(["%s"] * len(record_ids))
        delete_sql = f"DELETE FROM {table_name} WHERE {primary_key} IN ({placeholders})"
        
        # 执行批量删除
        cursor.execute(delete_sql, record_ids)
        conn.commit()
        
        # 打印受影响的行数
        print(f"删除了 {cursor.rowcount} 条记录")
        
        flash(f"成功删除 {len(record_ids)} 条记录！")
    except Exception as e:
        flash(f"错误: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    return redirect(f'/table/{db_name}/{table_name}')

@app.route('/add_primary_key/<db_name>/<table_name>', methods=['POST'])
def add_primary_key(db_name, table_name):
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        conn.database = db_name
        cursor = conn.cursor()
        
        # 检查是否存在 id 字段
        cursor.execute(f"DESCRIBE {table_name}")
        columns = [col[0] for col in cursor.fetchall()]
        if 'id' not in columns:
            # 如果不存在 id 字段，先添加 id 字段
            cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY")
        else:
            # 如果存在 id 字段，直接添加主键
            cursor.execute(f"ALTER TABLE {table_name} ADD PRIMARY KEY (id)")
        
        conn.commit()
        flash("主键添加成功！")
    except Exception as e:
        flash(f"错误: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    return redirect(f'/table/{db_name}/{table_name}')

@app.route('/set_primary_key/<db_name>/<table_name>', methods=['POST'])
def set_primary_key(db_name, table_name):
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        conn.database = db_name
        cursor = conn.cursor()
        
        # 获取用户选择的字段
        primary_key_field = request.form['primary_key_field']
        
        # 检查字段是否存在
        cursor.execute(f"DESCRIBE {table_name}")
        columns = [col[0] for col in cursor.fetchall()]
        if primary_key_field not in columns:
            flash(f"错误: 字段 {primary_key_field} 不存在！")
            return redirect(f'/manage_fields/{db_name}/{table_name}')
        
        # 检查字段是否为数值类型（如 INT）
        cursor.execute(f"DESCRIBE {table_name}")
        field_info = [col for col in cursor.fetchall() if col[0] == primary_key_field][0]
        field_type = field_info[1].upper()
        if 'INT' not in field_type:
            flash(f"错误: 字段 {primary_key_field} 不是数值类型，无法设置自增属性！")
            return redirect(f'/manage_fields/{db_name}/{table_name}')
        
        # 设置主键并添加自增属性
        cursor.execute(f"ALTER TABLE {table_name} MODIFY COLUMN {primary_key_field} {field_type} AUTO_INCREMENT, ADD PRIMARY KEY ({primary_key_field})")
        conn.commit()
        
        flash(f"成功将字段 {primary_key_field} 设置为主键并添加自增属性！")
    except Exception as e:
        flash(f"错误: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    return redirect(f'/manage_fields/{db_name}/{table_name}')

@app.route('/set_auto_increment/<db_name>/<table_name>', methods=['POST'])
def set_auto_increment(db_name, table_name):
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        conn.database = db_name
        cursor = conn.cursor()
        
        # 获取主键字段
        cursor.execute(f"SHOW KEYS FROM {table_name} WHERE Key_name = 'PRIMARY'")
        primary_key_info = cursor.fetchone()
        
        if not primary_key_info:
            flash("错误: 表没有主键字段，无法设置自增属性！")
            return redirect(f'/table/{db_name}/{table_name}')
        
        # 使用整数索引访问元组中的值
        primary_key = primary_key_info[4]  # 第5列是字段名
        
        # 检查主键字段是否为数值类型（如 INT）
        cursor.execute(f"DESCRIBE {table_name}")
        field_info = [col for col in cursor.fetchall() if col[0] == primary_key][0]
        field_type = field_info[1].upper()
        if 'INT' not in field_type:
            flash(f"错误: 主键字段 {primary_key} 不是数值类型，无法设置自增属性！")
            return redirect(f'/table/{db_name}/{table_name}')
        
        # 设置主键字段为自增
        cursor.execute(f"ALTER TABLE {table_name} MODIFY COLUMN {primary_key} {field_type} AUTO_INCREMENT")
        conn.commit()
        
        flash(f"成功将主键字段 {primary_key} 设置为自增属性！")
    except Exception as e:
        flash(f"错误: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    return redirect(f'/table/{db_name}/{table_name}')

@app.route('/modify_field/<db_name>/<table_name>', methods=['POST'])
def modify_field(db_name, table_name):
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        conn.database = db_name
        cursor = conn.cursor()
        
        # 获取表单数据
        field_name = request.form['field_name']
        field_type = request.form['field_type']
        is_nullable = request.form['is_nullable']
        default_value = request.form['default_value']
        
        # 构建 ALTER TABLE 语句
        alter_query = f"ALTER TABLE {table_name} MODIFY COLUMN {field_name} {field_type}"
        if is_nullable == 'NO':
            alter_query += " NOT NULL"
        if default_value:
            alter_query += f" DEFAULT '{default_value}'"
        
        # 执行修改
        cursor.execute(alter_query)
        conn.commit()
        
        flash(f"成功修改字段 {field_name} 的属性！")
    except Exception as e:
        flash(f"错误: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    return redirect(f'/manage_fields/{db_name}/{table_name}')

@app.route('/export_databases')
def export_databases():
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        cursor = conn.cursor()
        
        # 获取数据库列表
        cursor.execute("SHOW DATABASES")
        databases = [db[0] for db in cursor.fetchall()]
        
        # 将数据库列表导出为 Excel
        df = pd.DataFrame(databases, columns=["Database Name"])
        output = BytesIO()
        df.to_excel(output, index=False, engine='openpyxl')
        output.seek(0)
        
        return send_file(output, download_name="databases.xlsx", as_attachment=True)
    except Exception as e:
        flash(f"错误: {str(e)}")
        return redirect('/')
    finally:
        cursor.close()
        conn.close()

@app.route('/export_tables/<db_name>')
def export_tables(db_name):
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        conn.database = db_name
        cursor = conn.cursor()
        
        # 获取表列表
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        
        # 将表列表导出为 Excel
        df = pd.DataFrame(tables, columns=["Table Name"])
        output = BytesIO()
        df.to_excel(output, index=False, engine='openpyxl')
        output.seek(0)
        
        return send_file(output, download_name=f"{db_name}_tables.xlsx", as_attachment=True)
    except Exception as e:
        flash(f"错误: {str(e)}")
        return redirect(f'/database/{db_name}')
    finally:
        cursor.close()
        conn.close()

@app.route('/export_table_content/<db_name>/<table_name>')
def export_table_content(db_name, table_name):
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        conn.database = db_name
        cursor = conn.cursor()
        
        # 获取表内容
        cursor.execute(f"SELECT * FROM {table_name}")
        rows = cursor.fetchall()
        columns = [col[0] for col in cursor.description]
        
        # 将表内容导出为 Excel
        df = pd.DataFrame(rows, columns=columns)
        output = BytesIO()
        df.to_excel(output, index=False, engine='openpyxl')
        output.seek(0)
        
        return send_file(output, download_name=f"{table_name}_content.xlsx", as_attachment=True)
    except Exception as e:
        flash(f"错误: {str(e)}")
        return redirect(f'/table/{db_name}/{table_name}')
    finally:
        cursor.close()
        conn.close()

@app.route('/copy_table_form')
def copy_table_form():
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        cursor = conn.cursor()
        cursor.execute("SHOW DATABASES")
        databases = [db[0] for db in cursor.fetchall()]
        cursor.close()
        conn.close()
        return render_template('copy_table.html', databases=databases, current_connection=current_connection)
    except Exception as e:
        flash(f"错误: {str(e)}")
        return redirect('/')

@app.route('/get_tables/<db_name>')
def get_tables(db_name):
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        conn.database = db_name
        cursor = conn.cursor()
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        cursor.close()
        conn.close()
        return {'tables': tables}
    except Exception as e:
        return {'error': str(e)}

@app.route('/copy_table', methods=['POST'])
def copy_table():
    source_db = request.form['source_db']
    source_table = request.form['source_table']
    target_db = request.form['target_db']
    target_table = request.form.get('target_table', source_table)
    copy_data = request.form.get('copy_data') == 'on'
    
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        cursor = conn.cursor(dictionary=True)
        
        # 获取源表结构
        conn.database = source_db
        cursor.execute(f"SHOW CREATE TABLE {source_table}")
        create_table_result = cursor.fetchone()
        if not create_table_result:
            flash(f"错误: 无法获取表 {source_table} 的结构")
            return redirect('/copy_table_form')
            
        create_table_sql = create_table_result['Create Table']
        
        # 修改创建表的SQL，替换表名
        create_table_sql = create_table_sql.replace(f"CREATE TABLE `{source_table}`", f"CREATE TABLE `{target_table}`")
        
        # 在目标数据库中创建表
        conn.database = target_db
        
        # 检查目标表是否已存在
        cursor.execute("SHOW TABLES")
        existing_tables = [table[f"Tables_in_{target_db}"] for table in cursor.fetchall()]
        if target_table in existing_tables:
            # 如果目标表已存在，先删除它
            cursor.execute(f"DROP TABLE `{target_table}`")
            flash(f"目标表 {target_table} 已存在，已被替换")
        
        # 创建新表
        cursor.execute(create_table_sql)
        
        # 如果需要复制数据
        if copy_data:
            # 获取源表数据
            conn.database = source_db
            cursor.execute(f"SELECT * FROM `{source_table}`")
            rows = cursor.fetchall()
            
            if rows:
                # 准备插入语句
                columns = list(rows[0].keys())
                placeholders = ", ".join(["%s"] * len(columns))
                column_names = ", ".join([f"`{col}`" for col in columns])
                
                # 在目标数据库中插入数据
                conn.database = target_db
                insert_sql = f"INSERT INTO `{target_table}` ({column_names}) VALUES ({placeholders})"
                
                # 批量插入数据
                values = [[row[col] for col in columns] for row in rows]
                cursor.executemany(insert_sql, values)
                
                conn.commit()
                flash(f"已成功复制表结构和 {len(rows)} 条数据")
            else:
                flash("表结构已复制，但源表中没有数据可复制")
        else:
            flash("表结构已成功复制（不包含数据）")
        
        cursor.close()
        conn.close()
        return redirect(f'/database/{target_db}')
        
    except Exception as e:
        flash(f"复制表时出错: {str(e)}")
        return redirect('/copy_table_form')

@app.route('/batch_copy_tables', methods=['POST'])
def batch_copy_tables():
    source_db = request.form['source_db']
    target_db = request.form['target_db']
    table_names = request.form.getlist('table_names')
    copy_data = request.form.get('copy_data') == 'on'
    
    if not table_names:
        flash("请选择要复制的表格")
        return redirect(f'/database/{source_db}')
    
    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        cursor = conn.cursor(dictionary=True)
        
        success_count = 0
        error_count = 0
        
        for table_name in table_names:
            try:
                # 获取源表结构
                cursor.execute(f"USE `{source_db}`")
                cursor.execute(f"SHOW CREATE TABLE `{table_name}`")
                create_table_result = cursor.fetchone()
                if not create_table_result:
                    flash(f"错误: 无法获取表 {table_name} 的结构")
                    error_count += 1
                    continue

                create_table_sql = create_table_result['Create Table']

                # 在目标数据库中创建表
                cursor.execute(f"USE `{target_db}`")

                # 检查目标表是否已存在
                cursor.execute("SHOW TABLES")
                existing_tables = [table[f"Tables_in_{target_db}"] for table in cursor.fetchall()]
                if table_name in existing_tables:
                    # 如果目标表已存在，先删除它
                    cursor.execute(f"DROP TABLE `{table_name}`")

                # 创建新表
                cursor.execute(create_table_sql)
                
                # 如果需要复制数据
                if copy_data:
                    # 获取源表数据
                    cursor.execute(f"USE `{source_db}`")
                    cursor.execute(f"SELECT * FROM `{table_name}`")
                    rows = cursor.fetchall()

                    if rows:
                        # 准备插入语句
                        columns = list(rows[0].keys())
                        placeholders = ", ".join(["%s"] * len(columns))
                        column_names = ", ".join([f"`{col}`" for col in columns])

                        # 在目标数据库中插入数据
                        cursor.execute(f"USE `{target_db}`")
                        insert_sql = f"INSERT INTO `{table_name}` ({column_names}) VALUES ({placeholders})"

                        # 批量插入数据
                        values = [[row[col] for col in columns] for row in rows]
                        cursor.executemany(insert_sql, values)

                        conn.commit()
                
                success_count += 1
            except Exception as e:
                print(f"复制表 {table_name} 时出错: {str(e)}")
                error_count += 1
        
        if success_count > 0:
            if error_count > 0:
                flash(f"已成功复制 {success_count} 个表，{error_count} 个表复制失败")
            else:
                flash(f"已成功复制 {success_count} 个表")
        else:
            flash("所有表复制失败，请检查错误信息")
        
    except Exception as e:
        flash(f"批量复制表格时出错: {str(e)}")
    finally:
        cursor.close()
        conn.close()
    
    return redirect(f'/database/{target_db}')

@app.route('/batch_delete_tables/<db_name>', methods=['POST'])
def batch_delete_tables(db_name):
    table_names = request.form.getlist('table_names')

    if not table_names:
        flash("请选择要删除的表格")
        return redirect(f'/database/{db_name}')

    # 添加强制删除选项
    force_delete = request.form.get('force_delete') == 'on'

    try:
        # 获取当前连接类型
        current_connection = session.get('connection', 'aliyun')
        
        conn = get_db_connection(current_connection)
        cursor = conn.cursor()

        # 使用USE语句明确选择数据库
        cursor.execute(f"USE `{db_name}`")

        success_count = 0
        error_count = 0
        failed_tables = []

        # 首先获取当前数据库中实际存在的表
        cursor.execute("SHOW TABLES")
        existing_tables = [table[0] for table in cursor.fetchall()]

        # 过滤出实际存在的表
        tables_to_delete = [table for table in table_names if table in existing_tables]
        non_existing_tables = [table for table in table_names if table not in existing_tables]

        # 记录不存在的表
        for table in non_existing_tables:
            failed_tables.append(f"{table}: 表不存在")
            error_count += 1

        if tables_to_delete:
            # 临时禁用外键检查
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")

            # 方法1：尝试直接删除表
            remaining_tables = []
            for table_name in tables_to_delete:
                try:
                    cursor.execute(f"DROP TABLE IF EXISTS `{table_name}`")
                    conn.commit()
                    success_count += 1
                except Exception as e:
                    print(f"删除表 {table_name} 时出错: {str(e)}")
                    remaining_tables.append(table_name)

            # 方法2：对于仍然失败的表，尝试删除相关的外键约束
            if remaining_tables:
                print(f"尝试删除 {len(remaining_tables)} 个表的外键约束...")
                for table_name in remaining_tables:
                    try:
                        # 查找并删除指向该表的外键约束
                        cursor.execute(f"""
                            SELECT CONSTRAINT_NAME, TABLE_NAME
                            FROM information_schema.KEY_COLUMN_USAGE
                            WHERE REFERENCED_TABLE_NAME = '{table_name}'
                            AND TABLE_SCHEMA = '{db_name}'
                        """)
                        foreign_keys = cursor.fetchall()

                        for fk in foreign_keys:
                            try:
                                cursor.execute(f"ALTER TABLE `{fk[1]}` DROP FOREIGN KEY `{fk[0]}`")
                                print(f"删除外键约束: {fk[1]}.{fk[0]}")
                            except Exception as fk_e:
                                print(f"删除外键约束失败: {fk_e}")

                        # 再次尝试删除表
                        cursor.execute(f"DROP TABLE IF EXISTS `{table_name}`")
                        conn.commit()
                        success_count += 1
                        print(f"成功删除表: {table_name}")

                    except Exception as e:
                        print(f"最终删除表 {table_name} 失败: {str(e)}")
                        failed_tables.append(f"{table_name}: {str(e)}")
                        error_count += 1

            # 重新启用外键检查
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")

        if success_count > 0:
            if error_count > 0:
                flash(f"已成功删除 {success_count} 个表，{error_count} 个表删除失败", "warning")
                # 显示失败的表格详细信息
                for failed_info in failed_tables[:5]:  # 最多显示5个失败信息
                    flash(f"删除失败: {failed_info}", "error")
                if len(failed_tables) > 5:
                    flash(f"还有 {len(failed_tables) - 5} 个表删除失败，请查看控制台日志", "warning")
            else:
                flash(f"已成功删除 {success_count} 个表", "success")
        else:
            flash("所有表删除失败，请检查错误信息", "error")
            # 显示失败的表格详细信息
            for failed_info in failed_tables[:3]:  # 最多显示3个失败信息
                flash(f"删除失败: {failed_info}", "error")

    except Exception as e:
        flash(f"批量删除表格时出错: {str(e)}")
    finally:
        try:
            # 确保重新启用外键检查
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        except:
            pass
        cursor.close()
        conn.close()

    return redirect(f'/database/{db_name}')

if __name__ == '__main__':
    # 从环境变量获取主机和端口配置，如果没有则使用默认值
    host = os.getenv('HOST', '0.0.0.0')  # 默认监听所有网络接口
    port = int(os.getenv('PORT', 5000))  # 默认端口5000
    
    # 根据环境变量决定是否开启调试模式
    debug_mode = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    
    print(f"应用启动在 http://{host}:{port}")
    print(f"调试模式: {'开启' if debug_mode else '关闭'}")
    
    # 启动应用
    app.run(host=host, port=port, debug=debug_mode)