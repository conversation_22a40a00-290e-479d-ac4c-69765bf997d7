#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PMO系统前端简单测试脚本
使用HTTP请求测试前端页面可访问性
"""

import asyncio
import aiohttp
import time

class SimpleFrontendTester:
    def __init__(self):
        self.base_url = "http://localhost:3001"
        self.test_results = []
        self.failed_tests = []
        
    async def setup(self):
        """初始化测试环境"""
        self.session = aiohttp.ClientSession()
        print("🚀 PMO系统前端简单测试开始")
        print("=" * 60)
        
    async def cleanup(self):
        """清理测试环境"""
        if self.session:
            await self.session.close()
        
        print("\n" + "=" * 60)
        print(f"✅ 前端测试完成，共 {len(self.test_results)} 个测试")
        print(f"✅ 成功：{len([r for r in self.test_results if r['success']])} 个")
        print(f"❌ 失败：{len(self.failed_tests)} 个")
        
        if self.failed_tests:
            print("\n❌ 失败的测试：")
            for test in self.failed_tests:
                print(f"  - {test['name']}: {test['error']}")
    
    async def test_page(self, name, url):
        """测试页面可访问性"""
        try:
            print(f"🧪 测试 {name}...")
            
            full_url = f"{self.base_url}{url}"
            async with self.session.get(full_url) as response:
                if response.status == 200:
                    content = await response.text()
                    
                    # 检查是否是HTML页面
                    if "<!DOCTYPE html>" in content or "<html" in content:
                        print(f"  ✅ {name} - 成功 (状态码: {response.status})")
                        self.test_results.append({
                            "name": name,
                            "success": True,
                            "status": response.status,
                            "url": full_url
                        })
                        return True
                    else:
                        raise Exception("响应不是有效的HTML页面")
                else:
                    raise Exception(f"HTTP状态码: {response.status}")
                    
        except Exception as e:
            print(f"  ❌ {name} - 失败: {str(e)}")
            self.failed_tests.append({"name": name, "error": str(e)})
            self.test_results.append({
                "name": name,
                "success": False,
                "error": str(e)
            })
            return False
    
    async def test_frontend_accessibility(self):
        """测试前端页面可访问性"""
        print("\n🌐 测试前端页面可访问性")
        print("-" * 40)
        
        # 测试主要页面路由
        pages = [
            ("主页", "/"),
            ("仪表板", "/dashboard"),
            ("项目管理", "/project-list"),
            ("AI助手", "/ai-assistant"),
            ("数据库AI", "/database-ai"),
            ("档案管理", "/archive-management"),
            ("团队管理", "/team-management"),
            ("红黑榜", "/redblack")
        ]
        
        for name, url in pages:
            await self.test_page(name, url)
            await asyncio.sleep(0.5)  # 避免请求过快
    
    async def test_static_resources(self):
        """测试静态资源"""
        print("\n📦 测试静态资源")
        print("-" * 40)
        
        # 测试常见的静态资源
        resources = [
            ("Favicon", "/favicon.ico"),
            ("Vite客户端", "/@vite/client"),
        ]
        
        for name, url in resources:
            try:
                print(f"🧪 测试 {name}...")
                full_url = f"{self.base_url}{url}"
                async with self.session.get(full_url) as response:
                    if response.status in [200, 304]:  # 200 OK 或 304 Not Modified
                        print(f"  ✅ {name} - 成功 (状态码: {response.status})")
                        self.test_results.append({
                            "name": name,
                            "success": True,
                            "status": response.status
                        })
                    else:
                        print(f"  ⚠️ {name} - 状态码: {response.status}")
                        self.test_results.append({
                            "name": name,
                            "success": False,
                            "status": response.status
                        })
            except Exception as e:
                print(f"  ⚠️ {name} - 异常: {str(e)}")
                self.test_results.append({
                    "name": name,
                    "success": False,
                    "error": str(e)
                })
    
    async def test_api_connectivity(self):
        """测试前端到后端API的连通性"""
        print("\n🔗 测试API连通性")
        print("-" * 40)
        
        # 测试后端API是否可访问
        backend_url = "http://localhost:8000"
        api_endpoints = [
            ("后端健康检查", "/api/v1/db/status"),
            ("项目列表API", "/api/v1/project/list"),
            ("AI状态API", "/api/v1/ai/status")
        ]
        
        for name, endpoint in api_endpoints:
            try:
                print(f"🧪 测试 {name}...")
                full_url = f"{backend_url}{endpoint}"
                async with self.session.get(full_url) as response:
                    if response.status == 200:
                        print(f"  ✅ {name} - 成功")
                        self.test_results.append({
                            "name": name,
                            "success": True,
                            "status": response.status
                        })
                    else:
                        print(f"  ❌ {name} - 状态码: {response.status}")
                        self.test_results.append({
                            "name": name,
                            "success": False,
                            "status": response.status
                        })
            except Exception as e:
                print(f"  ❌ {name} - 异常: {str(e)}")
                self.test_results.append({
                    "name": name,
                    "success": False,
                    "error": str(e)
                })
    
    async def run_all_tests(self):
        """运行所有测试"""
        await self.setup()
        
        try:
            # 测试前端页面可访问性
            await self.test_frontend_accessibility()
            
            # 测试静态资源
            await self.test_static_resources()
            
            # 测试API连通性
            await self.test_api_connectivity()
            
        except Exception as e:
            print(f"❌ 前端测试过程中发生异常: {str(e)}")
        
        finally:
            await self.cleanup()

async def main():
    """主函数"""
    tester = SimpleFrontendTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
