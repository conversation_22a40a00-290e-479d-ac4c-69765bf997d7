.col-side .chosen-container .chosen-drop {width: 216px !important;}
.col-side .chosen-container {width: 218px !important;}
.table-form > tbody > tr > td .btn.addbutton {display: block; margin: 1px 0px; width: 40px; padding: 1px 6px;}
.table-form > tbody > tr > td .btn.delbutton {display: block; margin: 1px 0px; width: 40px; padding: 1px 6px;}
#branch {width: 95px!important;}
.detail > table > tbody th {width: 100px !important;}
[lang^=en] .detail > table > tbody th, [lang^=fr] .detail > table > tbody th {width: 100px !important;}
[lang^=de] .detail > table > tbody th {width: 125px !important;}
.linkCaseTitle, .linkBugTitle {white-space: nowrap;}
#type_chosen{display: table-cell;}
.autoScript .file-input .file-title{max-width: 70px;}

@media screen and (max-width: 1366px) {
  #module_chosen{min-width: 60px;}
}

