/*!
 * ZUI: ZUI kanban - v1.10.0 - 2022-07-06
 * http://openzui.com
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2022 cnezsoft.com; Licensed MIT
 */.kanban{min-height:300px}.kanban-header{position:relative;z-index:1;background-color:rgba(0,0,0,.07)}.use-flex .kanban-cols{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;min-width:0;flex-direction:row;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-webkit-flex-direction:row;-ms-flex-direction:row;-webkit-flex-wrap:nowrap;-ms-flex-wrap:nowrap;flex-wrap:nowrap;-webkit-box-flex:1;-webkit-flex:auto;-ms-flex:auto;flex:auto}.kanban-col{min-width:0;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.no-flex .kanban-col{position:absolute;top:0;bottom:0}.kanban-col+.kanban-col{border-left:2px solid #fff}.kanban-header-cols{position:absolute;top:0;right:0;bottom:0;left:auto}.kanban-header-col{position:relative}.kanban-header-col>.title>.icon{display:inline-block;vertical-align:middle}.kanban-header-col>.title>.text{display:inline-block;margin:0 5px;overflow:hidden;font-weight:700;text-overflow:clip;white-space:nowrap;vertical-align:middle}.kanban-header-col>.title>.count{position:relative;top:1px;display:inline-block;color:#8b91a2;vertical-align:middle}.kanban-affixed .kanban-header-col>.title>.count{color:#ededed}.use-flex .kanban-header-col{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;min-width:0;min-height:0;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:0 30px;flex-direction:row;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-webkit-flex-direction:row;-ms-flex-direction:row}.use-flex .kanban-header-col>.title{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;max-width:100%;flex-direction:row;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-webkit-flex-direction:row;-ms-flex-direction:row;-webkit-box-flex:1;-webkit-flex:auto;-ms-flex:auto;flex:auto;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.no-flex .kanban-header-col>.title{position:absolute;top:50%;right:30px;left:30px;margin-top:-10px;line-height:20px;text-align:center;white-space:nowrap}.kanban-header-col>.actions{position:absolute;top:0;right:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.kanban-header-col>.actions>.btn,.kanban-header-col>.actions>a{min-width:20px;padding:5px;border-radius:0}.kanban-affixed .kanban-header-col>.actions>.btn,.kanban-affixed .kanban-header-col>.actions>.btn>.icon,.kanban-affixed .kanban-header-col>.actions>a,.kanban-affixed .kanban-header-col>.actions>a>.icon{color:#fff}.kanban-header-col>.actions>.btn-link:hover{background-color:rgba(0,0,0,.07)}.kanban-header-parent-col>.kanban-header-col,.kanban-header-parent-col>.kanban-header-sub-cols{height:50%}.use-flex .kanban-header-parent-col{padding:0;flex-direction:column;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;-webkit-box-align:stretch;-webkit-align-items:stretch;-ms-flex-align:stretch;align-items:stretch}.kanban-header-sub-cols{position:relative;margin-top:-1px;margin-left:-2px;border-top:2px solid #fff;border-right:none}.use-flex .kanban-header-sub-cols{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;flex-direction:row;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-webkit-flex-direction:row;-ms-flex-direction:row}.use-flex .kanban-header-sub-cols>.kanban-col{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.kanban-col:first-child>.kanban-header-sub-cols{margin-left:0}.kanban-lane,.kanban-sub-lane{position:relative;background-color:#f1f3f5}.use-flex .kanban-lane,.use-flex .kanban-sub-lane{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;min-height:0;flex-direction:row;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-webkit-flex-direction:row;-ms-flex-direction:row;-webkit-flex-wrap:nowrap;-ms-flex-wrap:nowrap;flex-wrap:nowrap}.kanban-lane+.kanban-lane{margin-top:2px}.kanban-lane>*{opacity:1;-webkit-transition:opacity .1s;-o-transition:opacity .1s;transition:opacity .1s}.kanban-lane.virtual-pending>*{opacity:0}.kanban-lane .has-sub-lane{background:0 0}.no-flex .kanban-lane-cols{position:absolute;top:0;right:0;bottom:0}.no-flex .kanban-sub-lane-cols{position:absolute;top:0;right:0;bottom:0;left:0}.kanban-sub-lane{-webkit-box-flex:1;-webkit-flex:auto;-ms-flex:auto;flex:auto}.kanban-lane-name{position:relative;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;width:20px;overflow:hidden;color:#fff;text-align:center;background-color:#3dc6fd;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-box-flex:0;-webkit-flex:none;-ms-flex:none;flex:none}.kanban-lane-name>.text{position:absolute;top:5px;bottom:5px;left:0;display:block;overflow:hidden;line-height:20px;text-align:center;white-space:nowrap;-webkit-writing-mode:tb-rl;-ms-writing-mode:tb-rl;writing-mode:tb-rl;-webkit-writing-mode:vertical-rl;writing-mode:vertical-rl}.no-flex .kanban-lane-name{position:absolute;top:0;bottom:0;left:0}.kanban-sub-lanes{min-width:0}.use-flex .kanban-sub-lanes{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;flex-direction:column;-webkit-box-flex:1;-webkit-flex:auto;-ms-flex:auto;flex:auto;-webkit-box-align:stretch;-webkit-align-items:stretch;-ms-flex-align:stretch;align-items:stretch;-webkit-box-pack:stretch;-webkit-justify-content:stretch;-ms-flex-pack:stretch;justify-content:stretch;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column}.no-flex .kanban-sub-lanes{position:absolute;top:0;right:0;bottom:0}.kanban-sub-lanes.no-sub-lane{background-color:#f1f3f5}.kanban-dragging .kanban-lane-col{-webkit-transition:-webkit-box-shadow .1s;-o-transition:box-shadow .1s;transition:-webkit-box-shadow .1s;transition:box-shadow .1s;transition:box-shadow .1s,-webkit-box-shadow .1s}.kanban-lane-col.drop-target{-webkit-box-shadow:inset 0 0 0 3px rgba(255,152,0,.25);box-shadow:inset 0 0 0 3px rgba(255,152,0,.25)}.kanban-lane-col.drop-to{-webkit-box-shadow:inset 0 0 1px 4px rgba(255,152,0,.75);box-shadow:inset 0 0 1px 4px rgba(255,152,0,.75)}.kanban-lane-col.drop-to .kanban-lane-actions>.btn{background-color:rgba(255,152,0,.25);border:1px dotted #ff9800}.kanban-lane-col.drop-to .kanban-lane-actions>.btn>span{opacity:0}.kanban-lane-col[data-type=EMPTY]{background-color:#fff}.kanban-lane-items{height:100%;overflow:auto;overflow:overlay!important}.kanban-lane-actions{padding:10px 15px}.use-flex .kanban-items-grid{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-webkit-flex-flow:row wrap;-ms-flex-flow:row wrap;flex-flow:row wrap;-webkit-align-content:flex-start;-ms-flex-line-pack:start;align-content:flex-start}.kanban-card{position:relative;padding:8px 10px;background:#fff;border:1px solid #fff;border-radius:4px;-webkit-box-shadow:0 1px 3px 0 rgba(0,0,0,.1),0 1px 2px 0 rgba(0,0,0,.06);box-shadow:0 1px 3px 0 rgba(0,0,0,.1),0 1px 2px 0 rgba(0,0,0,.06);-webkit-transition:-webkit-box-shadow .2s,-webkit-transform .2s;-o-transition:box-shadow .2s,-o-transform .2s;transition:-webkit-box-shadow .2s,-webkit-transform .2s;transition:box-shadow .2s,transform .2s;transition:box-shadow .2s,transform .2s,-webkit-box-shadow .2s,-webkit-transform .2s,-o-transform .2s}.dragging>.kanban-card{opacity:.2}.kanban-card:hover{border-color:rgba(0,0,0,.1);-webkit-box-shadow:0 4px 10px 0 rgba(0,0,0,.09);box-shadow:0 4px 10px 0 rgba(0,0,0,.09)}.drag-shadow>.kanban-card{z-index:10;border-color:rgba(0,0,0,.2);-webkit-box-shadow:0 4px 10px 0 rgba(0,0,0,.05),0 4px 20px 0 rgba(0,0,0,.3);box-shadow:0 4px 10px 0 rgba(0,0,0,.05),0 4px 20px 0 rgba(0,0,0,.3);opacity:1!important;-webkit-transition:-webkit-box-shadow .2s,-webkit-transform .4s!important;-o-transition:box-shadow .2s,-o-transform .4s!important;transition:-webkit-box-shadow .2s,-webkit-transform .4s!important;transition:box-shadow .2s,transform .4s!important;transition:box-shadow .2s,transform .4s,-webkit-box-shadow .2s,-webkit-transform .4s,-o-transform .4s!important}.drag-shadow>.kanban-card.in{-webkit-transform:scale(1.1) rotate(5deg);-ms-transform:scale(1.1) rotate(5deg);-o-transform:scale(1.1) rotate(5deg);transform:scale(1.1) rotate(5deg)}.drag-shadow>.kanban-card a{pointer-events:none}.kanban-expired{display:none!important}.kanban-expired.kanban-board,.kanban-expired.kanban-lane{display:block!important}.kanban-dragging{cursor:move}