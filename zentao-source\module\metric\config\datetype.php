<?php
declare(strict_types=1);
$config->metric->dateType = array();
$config->metric->dateType['count_of_annual_created_top_program'] = 'year';
$config->metric->dateType['count_of_annual_closed_top_program'] = 'year';
$config->metric->dateType['count_of_annual_created_product'] = 'year';
$config->metric->dateType['count_of_annual_closed_product'] = 'year';
$config->metric->dateType['count_of_annual_created_project'] = 'year';
$config->metric->dateType['count_of_annual_closed_project'] = 'year';
$config->metric->dateType['count_of_undelayed_finished_project_which_annual_started'] = 'year';
$config->metric->dateType['count_of_delayed_finished_project_which_annual_finished'] = 'year';
$config->metric->dateType['count_of_undelayed_finished_project_which_annual_finished'] = 'year';
$config->metric->dateType['count_of_monthly_created_project'] = 'month';
$config->metric->dateType['count_of_monthly_closed_project'] = 'month';
$config->metric->dateType['count_of_annual_finished_project'] = 'year';
$config->metric->dateType['estimate_of_annual_closed_project'] = 'year';
$config->metric->dateType['consume_of_annual_closed_project'] = 'year';
$config->metric->dateType['consume_of_monthly_closed_project'] = 'month';
$config->metric->dateType['day_of_annual_closed_project'] = 'year';
$config->metric->dateType['rate_of_undelayed_finished_project_which_annual_finished'] = 'year';
$config->metric->dateType['rate_of_delayed_finished_project_which_annual_finished'] = 'year';
$config->metric->dateType['count_of_annual_created_productplan'] = 'year';
$config->metric->dateType['count_of_annual_finished_productplan'] = 'year';
$config->metric->dateType['count_of_annual_closed_productplan'] = 'year';
$config->metric->dateType['count_of_annual_created_execution'] = 'year';
$config->metric->dateType['count_of_annual_closed_execution'] = 'year';
$config->metric->dateType['count_of_monthly_created_execution'] = 'month';
$config->metric->dateType['count_of_monthly_closed_execution'] = 'month';
$config->metric->dateType['count_of_undelayed_finished_execution_which_annual_finished'] = 'year';
$config->metric->dateType['count_of_delayed_finished_execution_which_annual_finished'] = 'year';
$config->metric->dateType['rate_of_undelayed_closed_execution_which_annual_finished'] = 'year';
$config->metric->dateType['rate_of_delayed_closed_execution_which_annual_finished'] = 'year';
$config->metric->dateType['count_of_annual_created_release'] = 'year';
$config->metric->dateType['count_of_monthly_created_release'] = 'month';
$config->metric->dateType['count_of_weekly_created_release'] = 'week';
$config->metric->dateType['count_of_annual_created_story'] = 'year';
$config->metric->dateType['count_of_annual_finished_story'] = 'year';
$config->metric->dateType['count_of_monthly_created_story'] = 'month';
$config->metric->dateType['count_of_monthly_finished_story'] = 'month';
$config->metric->dateType['count_of_annual_delivered_story'] = 'year';
$config->metric->dateType['scale_of_annual_finished_story'] = 'year';
$config->metric->dateType['scale_of_annual_delivered_story'] = 'year';
$config->metric->dateType['scale_of_annual_closed_story'] = 'year';
$config->metric->dateType['scale_of_monthly_finished_story'] = 'month';
$config->metric->dateType['scale_of_monthly_delivered_story'] = 'month';
$config->metric->dateType['scale_of_monthly_closed_story'] = 'month';
$config->metric->dateType['scale_of_weekly_finished_story'] = 'week';
$config->metric->dateType['count_of_weekly_finished_story'] = 'week';
$config->metric->dateType['count_of_daily_created_story'] = 'day';
$config->metric->dateType['rate_of_annual_finished_story'] = 'year';
$config->metric->dateType['rate_of_annual_delivered_story'] = 'year';
$config->metric->dateType['count_of_annual_created_task'] = 'year';
$config->metric->dateType['count_of_annual_finished_task'] = 'year';
$config->metric->dateType['count_of_monthly_created_task'] = 'month';
$config->metric->dateType['count_of_monthly_finished_task'] = 'month';
$config->metric->dateType['count_of_daily_finished_task'] = 'day';
$config->metric->dateType['count_of_annual_created_bug'] = 'year';
$config->metric->dateType['count_of_annual_fixed_bug'] = 'year';
$config->metric->dateType['count_of_monthly_created_bug'] = 'month';
$config->metric->dateType['count_of_monthly_fixed_bug'] = 'month';
$config->metric->dateType['count_of_daily_closed_bug'] = 'day';
$config->metric->dateType['count_of_annual_created_case'] = 'year';
$config->metric->dateType['count_of_daily_run_case'] = 'day';
$config->metric->dateType['count_of_annual_created_user'] = 'year';
$config->metric->dateType['hour_of_annual_effort'] = 'year';
$config->metric->dateType['day_of_annual_effort'] = 'year';
$config->metric->dateType['day_of_daily_effort'] = 'day';
$config->metric->dateType['hour_of_daily_effort'] = 'day';
$config->metric->dateType['count_of_annual_created_doc'] = 'year';
$config->metric->dateType['count_of_annual_created_feedback'] = 'year';
$config->metric->dateType['count_of_annual_closed_feedback'] = 'year';
$config->metric->dateType['count_of_annual_created_productplan_in_product'] = 'year';
$config->metric->dateType['count_of_annual_finished_productplan_in_product'] = 'year';
$config->metric->dateType['count_of_annual_created_release_in_product'] = 'year';
$config->metric->dateType['count_of_monthly_created_release_in_product'] = 'month';
$config->metric->dateType['count_of_annual_created_story_in_product'] = 'year';
$config->metric->dateType['count_of_annual_finished_story_in_product'] = 'year';
$config->metric->dateType['count_of_annual_delivered_story_in_product'] = 'year';
$config->metric->dateType['count_of_annual_closed_story_in_product'] = 'year';
$config->metric->dateType['count_of_monthly_finished_story_in_product'] = 'month';
$config->metric->dateType['count_of_monthly_delivered_story_in_product'] = 'month';
$config->metric->dateType['count_of_monthly_closed_story_in_product'] = 'month';
$config->metric->dateType['count_of_monthly_created_story_in_product'] = 'month';
$config->metric->dateType['scale_of_annual_finished_story_in_product'] = 'year';
$config->metric->dateType['scale_of_annual_delivered_story_in_product'] = 'year';
$config->metric->dateType['scale_of_annual_closed_story_in_product'] = 'year';
$config->metric->dateType['scale_of_monthly_finished_story_in_product'] = 'month';
$config->metric->dateType['count_of_annual_created_requirement_in_product'] = 'year';
$config->metric->dateType['count_of_annual_created_bug_in_product'] = 'year';
$config->metric->dateType['count_of_annual_created_effective_bug_in_product'] = 'year';
$config->metric->dateType['count_of_annual_fixed_bug_in_product'] = 'year';
$config->metric->dateType['count_of_daily_created_bug_in_product'] = 'day';
$config->metric->dateType['count_of_daily_resolved_bug_in_product'] = 'day';
$config->metric->dateType['count_of_daily_closed_bug_in_product'] = 'day';
$config->metric->dateType['count_of_monthly_fixed_bug_in_product'] = 'month';
$config->metric->dateType['count_of_monthly_closed_bug_in_product'] = 'month';
$config->metric->dateType['count_of_monthly_created_bug_in_product'] = 'month';
$config->metric->dateType['count_of_annual_created_case_in_product'] = 'year';
$config->metric->dateType['count_of_annual_created_feedback_in_product'] = 'year';
$config->metric->dateType['count_of_annual_closed_feedback_in_product'] = 'year';
$config->metric->dateType['count_annual_closed_execution_in_project'] = 'year';
$config->metric->dateType['count_of_annual_finished_story_in_project'] = 'year';
$config->metric->dateType['scale_of_annual_finished_story_in_project'] = 'year';
$config->metric->dateType['count_of_daily_finished_task_in_execution'] = 'day';
$config->metric->dateType['count_of_daily_review_story_in_user'] = 'day';
$config->metric->dateType['count_of_daily_finished_task_in_user'] = 'day';
$config->metric->dateType['count_of_daily_fixed_bug_in_user'] = 'day';
$config->metric->dateType['count_of_daily_review_feedback_in_user'] = 'day';
