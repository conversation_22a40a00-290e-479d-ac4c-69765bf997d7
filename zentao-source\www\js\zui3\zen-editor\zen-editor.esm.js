import{p as e,b as o}from"./p-7900c24a.js";export{s as setNonce}from"./p-7900c24a.js";import{g as r}from"./p-c53e41a7.js";(()=>{const o=import.meta.url,r={};return""!==o&&(r.resourcesUrl=new URL(".",o).href),e(r)})().then((async e=>(await r(),o([["p-b74f15a0",[[65,"zen-editor",{name:[1],readonly:[1028],uploadUrl:[1,"upload-url"],placeholder:[1],fullscreenable:[4],resizable:[4],exposeEditor:[4,"expose-editor"],size:[1],hideUI:[4,"hide-ui"],hideMenubar:[4,"hide-menubar"],menubarMode:[1,"menubar-mode"],slashMenu:[4,"slash-menu"],bubbleMenu:[4,"bubble-menu"],preferHardBreak:[4,"prefer-hard-break"],neglectDefaultTextStyle:[4,"neglect-default-text-style"],markdown:[4],locale:[1],css:[1],collaborative:[4],hocuspocus:[1],docName:[1,"doc-name"],username:[1],userColor:[1,"user-color"],value:[1025],editor:[32],isFullscreen:[32],rendered:[32],setHTML:[64],insertHTML:[64],getHTML:[64],getText:[64],focusEditor:[64],blurEditor:[64],setReadonly:[64]},null,{css:["onCSSChange"]}],[1,"zen-editor-core",{name:[1],readonly:[4],uploadUrl:[1,"upload-url"],placeholder:[1],initialContent:[1,"initial-content"],resizable:[4],exposeEditor:[4,"expose-editor"],size:[1],hideUI:[4,"hide-ui"],hideMenubar:[4,"hide-menubar"],menubarMode:[1,"menubar-mode"],extraMenubarItems:[1,"extra-menubar-items"],slashMenu:[4,"slash-menu"],bubbleMenu:[4,"bubble-menu"],preferHardBreak:[4,"prefer-hard-break"],neglectDefaultTextStyle:[4,"neglect-default-text-style"],markdown:[4],locale:[1],styles:[16],collaborative:[4],hocuspocus:[1],docName:[1,"doc-name"],username:[1],userColor:[1,"user-color"],updateInputValue:[16],fullscreenable:[4],toggleFullscreen:[16],isFullscreen:[4,"is-fullscreen"],value:[1],editor:[32],forceUpdateCounter:[32],isMonaco:[32]},null,{locale:["onLocaleChanage"]}],[0,"zen-editor-content",{editor:[16],styles:[16],bubbleMenu:[4,"bubble-menu"]},[[2,"click","handleClick"]]],[0,"zen-editor-menubar",{editor:[16],menubarMode:[2,"menubar-mode"],extraMenubarItems:[1,"extra-menubar-items"],forceUpdateCounter:[2,"force-update-counter"],states:[8],toggleMonaco:[16],toggleFullscreen:[16],styles:[16]}],[0,"monaco-editor",{options:[8],initialValue:[1,"initial-value"],tiptapEditor:[16],updateInputValue:[16],setFocus:[64],getValue:[64]},null,{options:["onOptionsChange"]}],[2,"zen-editor-bubble-menu",{menuProps:[16],editor:[16],disabled:[4],element:[32]}],[0,"zen-editor-menu-item",{itemProps:[8,"item-props"],menubarMode:[2,"menubar-mode"],styles:[16]}]]],["p-d22ddfa9",[[0,"zen-editor-floating-menu",{menuProps:[16],editor:[16],element:[32]}]]],["p-ad0a1235",[[1,"zen-editor-slash-menu",{editor:[16],items:[1040],props:[16],currentSelectionIndex:[32]},null,{editor:["onEditorChange"],items:["onItemsChange"],props:["onPropsChange"]}]]],["p-795d6f9f",[[0,"zen-editor-footer",{editor:[16],forceUpdateCounter:[2,"force-update-counter"],toggleFullscreen:[16],showCharCount:[4,"show-char-count"]}]]]],e))));