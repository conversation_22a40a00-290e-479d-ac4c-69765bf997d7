#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试督办管理的改进功能
1. 完成时限日期格式
2. 整体进度自动计算
3. 双击编辑保存到详情表
"""

import requests
import json

def test_get_items():
    """测试获取督办事项（验证整体进度计算）"""
    try:
        print("🔍 测试获取督办事项...")
        
        response = requests.get('http://127.0.0.1:8001/api/v1/simple-supervision/items')
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            items = result.get('data', [])
            print(f"✅ 获取成功，共 {len(items)} 条督办事项")
            
            if items:
                print("前两条数据的整体进度:")
                for i, item in enumerate(items[:2]):
                    print(f"  {i+1}. {item['work_theme']}")
                    print(f"     整体进度: {item['overall_progress']}")
                    print(f"     财险状态: {item.get('财险_status', 'N/A')}")
                    print(f"     寿险状态: {item.get('寿险_status', 'N/A')}")
                    print(f"     完成时限: {item.get('completion_deadline', 'N/A')}")
        else:
            print(f"❌ 获取失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_progress_update():
    """测试进度更新API（双击编辑功能）"""
    try:
        print("\n🔍 测试进度更新API...")
        
        # 模拟双击编辑后的数据
        progress_data = {
            "work_theme": "测试主题1",
            "company_name": "财险",
            "status": "O",
            "progress_description": "项目正在按计划推进，已完成前期调研工作。",
            "existing_problems": "需要进一步协调各部门资源配置。",
            "next_plan": "下周开始进入实施阶段，预计月底完成第一阶段工作。"
        }
        
        response = requests.put(
            'http://127.0.0.1:8001/api/v1/simple-supervision/progress',
            json=progress_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 进度更新成功: {result.get('message', '无消息')}")
        else:
            print(f"❌ 进度更新失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_progress_detail():
    """测试获取进度详情"""
    try:
        print("\n🔍 测试获取进度详情...")
        
        response = requests.get(
            'http://127.0.0.1:8001/api/v1/simple-supervision/progress-detail/测试主题1/财险'
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 获取进度详情成功")
            if result.get('data'):
                data = result['data']
                print(f"     进度描述: {data.get('progress_description', 'N/A')}")
                print(f"     存在问题: {data.get('existing_problems', 'N/A')}")
                print(f"     下步计划: {data.get('next_plan', 'N/A')}")
            else:
                print("     暂无详情数据")
        else:
            print(f"❌ 获取进度详情失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_overall_progress_calculation():
    """测试整体进度计算逻辑"""
    try:
        print("\n🔍 测试整体进度计算...")

        # 使用真实存在的督办事项进行测试
        test_work_theme = "建立条线ITBP团队管理办法"

        # 先更新几个公司的状态
        test_cases = [
            {"company": "财险", "status": "√"},  # 已完成
            {"company": "寿险", "status": "O"},  # 进行中
            {"company": "金租", "status": "！"}, # 逾期
        ]

        for case in test_cases:
            response = requests.put(
                'http://127.0.0.1:8001/api/v1/simple-supervision/status',
                json={
                    "work_theme": test_work_theme,
                    "company_name": case["company"],
                    "status": case["status"]
                },
                headers={'Content-Type': 'application/json'}
            )

            if response.status_code == 200:
                result = response.json()
                print(f"  ✅ 更新 {case['company']} 状态为 {case['status']}")
                if 'new_overall_progress' in result:
                    print(f"     新的整体进度: {result['new_overall_progress']}")
            else:
                print(f"  ❌ 更新 {case['company']} 状态失败: {response.text}")

        # 再次获取数据，查看整体进度是否正确计算
        print("\n  重新获取数据验证整体进度...")
        response = requests.get('http://127.0.0.1:8001/api/v1/simple-supervision/items')

        if response.status_code == 200:
            result = response.json()
            items = result.get('data', [])

            for item in items:
                if item['work_theme'] == test_work_theme:
                    print(f"  {test_work_theme} 的整体进度: {item['overall_progress']}")
                    print(f"  各公司状态: 财险={item.get('财险_status')}, 寿险={item.get('寿险_status')}, 金租={item.get('金租_status')}")
                    print(f"  完成时限: {item.get('completion_deadline')}")
                    break

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_date_format():
    """测试日期格式显示"""
    try:
        print("\n🔍 测试日期格式...")

        response = requests.get('http://127.0.0.1:8001/api/v1/simple-supervision/items')

        if response.status_code == 200:
            result = response.json()
            items = result.get('data', [])

            print("前5条数据的完成时限格式:")
            for i, item in enumerate(items[:5]):
                print(f"  {i+1}. {item['work_theme'][:30]}...")
                print(f"     完成时限: {item.get('completion_deadline')} (类型: {type(item.get('completion_deadline'))})")
        else:
            print(f"❌ 获取数据失败: {response.text}")

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始测试督办管理改进功能...")
    test_get_items()
    test_date_format()
    test_progress_update()
    test_progress_detail()
    test_overall_progress_calculation()
    print("\n🎉 测试完成！")
