#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构和删除标记字段
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'pmo-backend'))

from app.core.database import TicketSystemConnector

def check_table_structure():
    """检查各个表的结构和删除标记字段"""
    print("🔍 检查数据库表结构和删除标记字段")
    print("=" * 60)
    
    connector = TicketSystemConnector()
    
    try:
        # 要检查的表
        tables = [
            'feelec_ticket',
            'feelec_project', 
            'feelec_ticket_status',
            'feelec_user',
            'feelec_member_department',
            'feelec_company',
            'feelec_ticket_template'
        ]
        
        for table in tables:
            print(f"\n📋 检查表: {table}")
            print("-" * 40)
            
            # 1. 检查表结构
            try:
                structure_sql = f"DESCRIBE {table}"
                columns = connector.execute_query(structure_sql)
                
                print("字段列表:")
                delete_fields = []
                for col in columns:
                    field_name = col['Field']
                    field_type = col['Type']
                    print(f"  {field_name}: {field_type}")
                    
                    # 查找删除相关字段
                    if 'delete' in field_name.lower():
                        delete_fields.append(field_name)
                
                if delete_fields:
                    print(f"🗑️  删除标记字段: {', '.join(delete_fields)}")
                else:
                    print("⚠️  未找到删除标记字段")
                
                # 2. 检查删除标记的实际值
                if delete_fields:
                    for delete_field in delete_fields:
                        try:
                            value_sql = f"""
                            SELECT {delete_field}, COUNT(*) as count 
                            FROM {table} 
                            GROUP BY {delete_field} 
                            ORDER BY count DESC 
                            LIMIT 5
                            """
                            values = connector.execute_query(value_sql)
                            
                            print(f"  {delete_field} 的值分布:")
                            for val in values:
                                print(f"    {val[delete_field]}: {val['count']} 条记录")
                        except Exception as e:
                            print(f"    检查 {delete_field} 值失败: {str(e)}")
                
                # 3. 检查总记录数
                try:
                    count_sql = f"SELECT COUNT(*) as total FROM {table}"
                    total = connector.execute_query(count_sql)
                    if total:
                        print(f"📊 总记录数: {total[0]['total']}")
                except Exception as e:
                    print(f"📊 获取总记录数失败: {str(e)}")
                    
            except Exception as e:
                print(f"❌ 检查表 {table} 失败: {str(e)}")
        
        # 4. 特别检查工单和项目的关联情况
        print(f"\n" + "=" * 60)
        print("🔗 检查工单和项目的关联情况")
        print("=" * 60)
        
        try:
            # 检查有项目ID的工单数量
            project_ticket_sql = """
            SELECT 
                COUNT(*) as total_tickets,
                COUNT(CASE WHEN feelec_project_id IS NOT NULL AND feelec_project_id != 0 THEN 1 END) as tickets_with_project,
                COUNT(CASE WHEN feelec_project_id IS NULL OR feelec_project_id = 0 THEN 1 END) as tickets_without_project
            FROM feelec_ticket 
            WHERE feelec_delete = 20
            """
            
            ticket_stats = connector.execute_query(project_ticket_sql)
            if ticket_stats:
                stat = ticket_stats[0]
                print(f"📊 工单统计:")
                print(f"  总工单数: {stat['total_tickets']}")
                print(f"  有项目ID的工单: {stat['tickets_with_project']}")
                print(f"  无项目ID的工单: {stat['tickets_without_project']}")
            
            # 检查项目表中的有效项目
            project_sql = """
            SELECT 
                COUNT(*) as total_projects,
                COUNT(CASE WHEN feelec_delete = 20 THEN 1 END) as active_projects,
                COUNT(CASE WHEN feelec_delete != 20 THEN 1 END) as deleted_projects
            FROM feelec_project
            """
            
            project_stats = connector.execute_query(project_sql)
            if project_stats:
                stat = project_stats[0]
                print(f"📊 项目统计:")
                print(f"  总项目数: {stat['total_projects']}")
                print(f"  活跃项目数: {stat['active_projects']}")
                print(f"  已删除项目数: {stat['deleted_projects']}")
            
            # 检查JOIN查询的结果
            join_test_sql = """
            SELECT 
                t.feelec_ticket_id,
                t.feelec_ticket_no,
                t.feelec_project_id,
                p.feelec_name as project_name,
                p.feelec_delete as project_delete_status
            FROM feelec_ticket t
            LEFT JOIN feelec_project p ON t.feelec_project_id = p.feelec_project_id AND p.feelec_delete = 20
            WHERE t.feelec_delete = 20 
            AND t.feelec_project_id IS NOT NULL 
            AND t.feelec_project_id != 0
            LIMIT 5
            """
            
            join_results = connector.execute_query(join_test_sql)
            print(f"\n🔗 JOIN查询测试结果:")
            
            if join_results:
                for result in join_results:
                    print(f"  工单: {result['feelec_ticket_no']}")
                    print(f"    项目ID: {result['feelec_project_id']}")
                    print(f"    项目名称: {result['project_name'] or '❌ NULL'}")
                    print(f"    项目删除状态: {result['project_delete_status']}")
                    print("    ---")
            else:
                print("  ❌ 没有找到有效的工单-项目关联数据")
                
        except Exception as e:
            print(f"❌ 检查关联情况失败: {str(e)}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        connector.close()

def main():
    """主函数"""
    print("🚀 开始检查数据库表结构")
    check_table_structure()
    
    print(f"\n" + "=" * 60)
    print("📝 检查总结:")
    print("1. 确认各表的删除标记字段名称和值")
    print("2. 验证工单和项目的关联数据")
    print("3. 检查JOIN查询的正确性")
    print("4. 找出项目ID和项目名称为空的原因")

if __name__ == "__main__":
    main()
