#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试整体进度计算问题
"""

import pymysql
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

# 数据库配置
DB_CONFIG = {
    'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'cyh',
    'password': 'Qq188788',
    'database': 'kanban2',
    'charset': 'utf8mb4'
}

def debug_progress_data():
    """调试进度数据"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        logging.info("🔍 开始调试整体进度计算问题")
        logging.info("=" * 60)
        
        # 1. 检查督办事项表结构
        logging.info("步骤1: 检查督办事项表结构")
        cursor.execute("DESCRIBE supervision_items")
        columns = cursor.fetchall()
        logging.info("supervision_items 表字段:")
        for col in columns:
            logging.info(f"   - {col['Field']}: {col['Type']}")
        
        # 2. 检查公司表
        logging.info("\n步骤2: 检查公司表")
        cursor.execute("SELECT id, company_code, company_name, is_active FROM companies ORDER BY display_order LIMIT 5")
        companies = cursor.fetchall()
        logging.info("前5家公司:")
        for company in companies:
            logging.info(f"   - ID:{company['id']}, 代码:{company['company_code']}, 名称:{company['company_name']}, 状态:{company['is_active']}")
        
        # 3. 检查是否有 company_progress 表
        logging.info("\n步骤3: 检查 company_progress 表")
        try:
            cursor.execute("DESCRIBE company_progress")
            columns = cursor.fetchall()
            logging.info("company_progress 表字段:")
            for col in columns:
                logging.info(f"   - {col['Field']}: {col['Type']}")
            
            # 查看数据样本
            cursor.execute("SELECT * FROM company_progress LIMIT 5")
            progress_data = cursor.fetchall()
            logging.info("company_progress 数据样本:")
            for data in progress_data:
                logging.info(f"   - {data}")
                
        except Exception as e:
            logging.warning(f"company_progress 表不存在或有问题: {e}")
        
        # 4. 检查是否有 company_supervision_status 表
        logging.info("\n步骤4: 检查 company_supervision_status 表")
        try:
            cursor.execute("DESCRIBE company_supervision_status")
            columns = cursor.fetchall()
            logging.info("company_supervision_status 表字段:")
            for col in columns:
                logging.info(f"   - {col['Field']}: {col['Type']}")
            
            # 查看数据样本
            cursor.execute("SELECT * FROM company_supervision_status LIMIT 5")
            status_data = cursor.fetchall()
            logging.info("company_supervision_status 数据样本:")
            for data in status_data:
                logging.info(f"   - {data}")
                
        except Exception as e:
            logging.warning(f"company_supervision_status 表不存在或有问题: {e}")
        
        # 5. 检查督办事项数据
        logging.info("\n步骤5: 检查督办事项数据")
        cursor.execute("SELECT id, sequence_number, work_theme, completion_deadline FROM supervision_items WHERE deleted_at IS NULL LIMIT 3")
        items = cursor.fetchall()
        logging.info("前3个督办事项:")
        for item in items:
            logging.info(f"   - ID:{item['id']}, 序号:{item['sequence_number']}, 主题:{item['work_theme']}, 截止:{item['completion_deadline']}")
        
        # 6. 检查第一个督办事项的公司状态
        if items:
            first_item_id = items[0]['id']
            logging.info(f"\n步骤6: 检查督办事项 {first_item_id} 的公司状态")
            
            # 尝试从 company_progress 查询
            try:
                cursor.execute("""
                SELECT cp.status, c.company_name, c.company_code
                FROM company_progress cp
                JOIN companies c ON cp.company_id = c.id
                WHERE cp.supervision_item_id = %s AND c.is_active = 1
                """, (first_item_id,))
                progress_statuses = cursor.fetchall()
                logging.info(f"从 company_progress 查到 {len(progress_statuses)} 条状态:")
                for status in progress_statuses[:5]:  # 只显示前5条
                    logging.info(f"   - {status['company_name']}: {status['status']}")
            except Exception as e:
                logging.warning(f"从 company_progress 查询失败: {e}")
            
            # 尝试从 company_supervision_status 查询
            try:
                cursor.execute("""
                SELECT css.status, c.company_name, c.company_code
                FROM company_supervision_status css
                JOIN companies c ON css.company_id = c.id
                WHERE css.supervision_item_id = %s AND c.is_active = 1
                """, (first_item_id,))
                supervision_statuses = cursor.fetchall()
                logging.info(f"从 company_supervision_status 查到 {len(supervision_statuses)} 条状态:")
                for status in supervision_statuses[:5]:  # 只显示前5条
                    logging.info(f"   - {status['company_name']}: {status['status']}")
            except Exception as e:
                logging.warning(f"从 company_supervision_status 查询失败: {e}")
        
        cursor.close()
        connection.close()
        
        logging.info("=" * 60)
        logging.info("🎯 调试完成")
        return True
        
    except Exception as e:
        logging.error(f"❌ 调试失败: {e}")
        return False

def main():
    """主函数"""
    logging.info("🚀 开始调试整体进度计算问题")
    logging.info("=" * 80)
    
    if debug_progress_data():
        logging.info("=" * 80)
        logging.info("🎯 调试完成！请查看上面的输出信息")
    else:
        logging.error("❌ 调试失败")

if __name__ == "__main__":
    main()
