title: table zt_product
desc: "产品"
author: automated export
version: "1.0"
fields:
  - field: id
    range: 101-1000
  - field: program
    note: "所属项目集"
    range: 0-10
  - field: name
    note: "产品名称"
    range: 1-100
    prefix: "产品"
  - field: code
    note: "产品代号"
    fields:
    - field: code1
      range: code
    - field: code2
      range: 300-1000
  - field: line
    note: "产品线"
    range: 0-10
  - field: type
    note: "产品类型"
    range: normal{40}
  - field: status
    note: "状态"
    range: normal{20}
  - field: subStatus
    note: "子状态"
    range: 0
  - field: desc
    note: "产品描述"
    fields:
    - field: desc1
      range: []{30},产品描述{20}
    - field: desc2
      range: <div>
               <p><h1>一、禅道项目管理软件是做什么的？</h1>
                  禅道由 青岛易软天创网络科技有限公司开发，国产开源项目管理软件。它集产品管理、项目管理、质量管理、文档管理、组织管理和事务管理于一体，是一款专业的研发项目管理软件，完整覆盖了研发项目管理的核心流程。禅道管理思想注重实效，功能完备丰富，操作简洁高效，界面美观大方，搜索功能强大，统计报表丰富多样，软件架构合理，扩展灵活，有完善的API可以调用。禅道，专注研发项目管理
               </p>
               <p>我是数字符号23@#$%#^$ </p>
               <p>我是英文dashcuscbrewg </p>
             </div>{30},
            1-20
  - field: PO
    note: "产品负责人"
    fields:
    - field: po1
      range: po{120},[]{7}
    - field: po2
      range: 1-10{12!},[]{7}
  - field: QD
    note: "测试负责人"
    fields:
    - field: account1
      range: test{120},[]{7}
    - field: account2
      range: 1-100,1-20,[]{7}
  - field: RD
    note: "发布负责人"
    fields:
    - field: dev1
      range: dev{120},[]{7},dev{80}
    - field: dev2
      range: 1-120,[]{7},121-200
  - field: acl
    note: "访问控制"
    range: open{10},private{10},open{10},private{10},open{10},private{10},open{10},private{10},open{10},private{10},open{10},private{10},open{5},private{5},open{5},private{5},open{5},private{5},open{5},private{5},open{5},private{5},open{5},private{5},open{20}
  - field: whitelist
    note: "分组白名单"
    range: []{15},`,test1,dev1,pm1,td1,pd2,qd2,top1,others1`{5},[]{15},`,test1,dev1,pm1,td1,pd2,qd2,top1,others1`{5},[]{15},`,test1,dev1,pm1,td1,pd2,qd2,top1,others1`{5},[]{15},`,test1,dev1,pm1,td1,pd2,qd2,top1,others1`{5},[]{15},`,test1,dev1,pm1,td1,pd2,qd2,top1,others1`{5},[]{15},`,test1,dev1,pm1,td1,pd2,qd2,top1,others1`{5},[]{7},`,test1,dev1,pm1,td1,pd2,qd2,top1,others1`{3},[]{7},`,test1,dev1,pm1,td1,pd2,qd2,top1,others1`{3},[]{7},`,test1,dev1,pm1,td1,pd2,qd2,top1,others1`{3},[]{7},`,test1,dev1,pm1,td1,pd2,qd2,top1,others1`{3},[]{7},`,test1,dev1,pm1,td1,pd2,qd2,top1,others1`{3},[]{7},`,test1,dev1,pm1,td1,pd2,qd2,top1,others1`{3},[]{20}
  - field: createdBy
    note: "创建者"
    fields:
    - field: createdBy1
      range: po
    - field: createdBy2
      range: 1-10
  - field: createdDate
    note: "创建日期"
    range: "-:60"
    type: timestamp
    format: "YYYY-MM-DD hh:mm:ss"
  - field: createdVersion
    note: "创建版本"
    range: 15.6
  - field: order
    note: "排序"
    range: 5-10000:5
  - field: deleted
    note: "是否删除"
    range: 0
