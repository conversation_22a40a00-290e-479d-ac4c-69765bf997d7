#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的项目工单API
验证是否返回了所有需要的字段
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'pmo-backend'))

import asyncio
from app.api.endpoints.ticket_integration import get_ticket_projects, get_project_tickets

class EnhancedTicketTester:
    """增强工单测试器"""
    
    def __init__(self):
        self.mock_user = {"user_id": "test", "username": "test"}
    
    async def test_enhanced_project_tickets(self):
        """测试增强的项目工单API"""
        print("🎫 测试增强的项目工单API")
        print("=" * 80)
        
        try:
            # 1. 获取项目列表
            print("\n📋 获取项目列表...")
            projects_result = await get_ticket_projects(limit=5, current_user=self.mock_user)
            
            if not projects_result.get('success'):
                print(f"❌ 获取项目列表失败: {projects_result.get('message')}")
                return False
                
            projects = projects_result.get('data', [])
            if not projects:
                print("❌ 没有找到项目")
                return False
                
            print(f"✅ 找到 {len(projects)} 个项目")
            
            # 2. 选择第一个项目测试
            test_project = projects[0]
            project_id = test_project['feelec_project_id']
            project_name = test_project['feelec_name']
            
            print(f"\n🎯 测试项目: {project_name} (ID: {project_id})")
            
            # 3. 获取项目工单详情
            tickets_result = await get_project_tickets(project_id=project_id, current_user=self.mock_user)
            
            if not tickets_result.get('success'):
                print(f"❌ 获取项目工单失败: {tickets_result.get('message')}")
                return False
                
            data = tickets_result.get('data', {})
            tickets = data.get('tickets', [])
            
            if not tickets:
                print("⚠️  该项目没有工单")
                return True
                
            print(f"✅ 找到 {len(tickets)} 个工单")
            
            # 4. 检查第一个工单的字段
            test_ticket = tickets[0]
            print(f"\n🔍 检查工单字段: {test_ticket.get('feelec_title', '无标题')}")
            
            # 基础字段
            basic_fields = [
                'feelec_ticket_id', 'feelec_ticket_no', 'feelec_title',
                'feelec_publisher_id', 'feelec_processor_id', 'feelec_priority'
            ]
            
            # 关联名称字段
            name_fields = [
                'publisher_name', 'processor_name', 'department_name', 
                'company_name', 'template_name', 'status_name'
            ]
            
            # 时间字段
            time_fields = [
                'create_time_formatted', 'first_assign_time_formatted',
                'first_process_time_formatted', 'complete_time_formatted',
                'deadline_formatted', 'process_duration_text'
            ]
            
            # 状态字段
            status_fields = [
                'priority_text', 'priority_color', 'source_text',
                'is_completed', 'is_overdue', 'overdue_days'
            ]
            
            # 内容字段
            content_fields = ['feelec_content']
            
            all_expected_fields = basic_fields + name_fields + time_fields + status_fields + content_fields
            
            print("\n📊 字段检查结果:")
            missing_fields = []
            present_fields = []
            
            for field in all_expected_fields:
                if field in test_ticket:
                    present_fields.append(field)
                    value = test_ticket[field]
                    if value is not None and value != '':
                        print(f"  ✅ {field}: {str(value)[:50]}{'...' if len(str(value)) > 50 else ''}")
                    else:
                        print(f"  ⚪ {field}: (空值)")
                else:
                    missing_fields.append(field)
                    print(f"  ❌ {field}: 缺失")
            
            print(f"\n📈 统计:")
            print(f"  - 预期字段总数: {len(all_expected_fields)}")
            print(f"  - 存在字段数量: {len(present_fields)}")
            print(f"  - 缺失字段数量: {len(missing_fields)}")
            
            if missing_fields:
                print(f"\n⚠️  缺失的字段: {', '.join(missing_fields)}")
            else:
                print(f"\n🎉 所有预期字段都存在！")
            
            # 5. 显示完整的工单信息示例
            print(f"\n📋 完整工单信息示例:")
            print(f"  工单编号: {test_ticket.get('feelec_ticket_no', 'N/A')}")
            print(f"  工单标题: {test_ticket.get('feelec_title', 'N/A')}")
            print(f"  发布人: {test_ticket.get('publisher_name', 'N/A')}")
            print(f"  处理人: {test_ticket.get('processor_name', 'N/A')}")
            print(f"  所属部门: {test_ticket.get('department_name', 'N/A')}")
            print(f"  主体公司: {test_ticket.get('company_name', 'N/A')}")
            print(f"  工单模板: {test_ticket.get('template_name', 'N/A')}")
            print(f"  优先级: {test_ticket.get('priority_text', 'N/A')}")
            print(f"  状态: {test_ticket.get('status_name', 'N/A')}")
            print(f"  来源: {test_ticket.get('source_text', 'N/A')}")
            print(f"  是否逾期: {'是' if test_ticket.get('is_overdue') else '否'}")
            print(f"  创建时间: {test_ticket.get('create_time_formatted', 'N/A')}")
            print(f"  截止时间: {test_ticket.get('deadline_formatted', 'N/A')}")
            print(f"  处理时长: {test_ticket.get('process_duration_text', 'N/A')}")
            
            return len(missing_fields) == 0
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

async def main():
    """主函数"""
    tester = EnhancedTicketTester()
    success = await tester.test_enhanced_project_tickets()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 测试通过！项目工单API已增强，包含所有需要的字段。")
        print("\n前端现在可以在项目工单列表中显示完整信息，无需点击查看详情！")
    else:
        print("⚠️  测试发现问题，请检查API实现。")

if __name__ == "__main__":
    asyncio.run(main())
