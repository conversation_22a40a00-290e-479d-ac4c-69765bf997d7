title: table zt_object
desc: "项目对象"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: project
    note: "所属项目"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: product
    range: 1-10000
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: from
    range: 0{1},1-100:R{1}
    note: "来源评审"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: title
    note: "标题"
    range: 1-10000
    prefix: "这个是评审或基线的标题"
    postfix: ""
    loop: 0
    format: ""
  - field: category
    note: "对象类别"
    range: PP,QAP,CMP,ITP,URS,SRS,HLDS,DDS,DBDS,ADS,Code,ITTC,STP,STTC,UM
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: version
    note: "版本号"
    range: "1-10000"
    prefix: "版本号"
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "评审/基线"
    range: reviewed,taged
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: range
    note: "范围"
    range: all
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: data
    note: "数据"
    range: 1-10000
    prefix: "这是对象的json数据"
    postfix: ""
    loop: 0
    format: ""
  - field: storyEst
    range: ""
  - field: taskEst
    range: ""
  - field: requestEst
    range: ""
  - field: testEst
    range: ""
  - field: devEst
    range: ""
  - field: designEst
    range: ""
  - field: end
    range: "(M)-(w)"
    type: timestamp
    format: "YYYY-MM-DD"
  - field: createdBy
    note: "创建者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdDate
    range: "(M)-(w)"
    type: timestamp
    format: "YYYY-MM-DD"
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
