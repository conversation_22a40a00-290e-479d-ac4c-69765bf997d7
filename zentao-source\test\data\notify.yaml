title: table zt_notify
desc: "消息通知"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: objectType
    note: "对象类型"
    range: mail,webhook
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: objectID
    note: "对象ID"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: action
    note: "操作记录ID"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: toList
    note: "指派用户"
    range: admin,user10,test10,dev10,top10
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: ccList
    range: 0
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: subject
    note: "主题"
    range: 1-100
    prefix: "主题"
    postfix: ""
    loop: 0
    format: ""
  - field: data
    note: "记录数据"
    range: 1-100
    prefix: "用户创建了任务"
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: "创建者"
    range: dev12,test12,admin
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdDate
    note: "创建日期"
    range: "(1M)-(1w)"
    type: timestamp
    format: "YYYY-MM-DD hh:mm:ss"
    prefix: ""
    postfix: ""
    loop: 0
  - field: sendTime
    note: "发送日期"
    range: "(1M)-(1w)"
    type: timestamp
    format: "YYYY-MM-DD hh:mm:ss"
    prefix: ""
    postfix: ""
    loop: 0
  - field: status
    note: "状态"
    range: wait,fail,seding
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: failReason
    note: "失败原因"
    range: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
