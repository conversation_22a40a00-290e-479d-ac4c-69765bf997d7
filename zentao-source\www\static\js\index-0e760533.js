import{u as R,Q as V,S as d,R as S}from"./useSync.hook-14394fcc.js";import{p as H,d as Y,q as M,b$ as m,R as W,r as u,o as h,h as F,e as a,f as t,w as o,c as q,m as r,a9 as P,F as Q,A as X,t as $}from"./index.js";import{i as x}from"./icon-bb3d09e7.js";import"./plugin-37914809.js";import"./tables_list-f613fa36.js";const j={class:"go-canvas-setting"},G=Y({__name:"index",setup(J){const s=R(),e=s.getEditCanvasConfig,f=s.getEditCanvas,w=M(0),{ScaleIcon:E,FitToScreenIcon:k,FitToWidthIcon:C}=x.carbon,{LockOpenOutlineIcon:A,LockClosedOutlineIcon:b}=x.ionicons5,y=[{label:"1366 \xD7 768",value:1366},{label:"1600 x 900",value:1600},{label:"1920 x 1080",value:1920},{label:"2560 x 1440",value:2560},{label:"3840 x 2160",value:3840},{label:"\u81EA\u5B9A\u4E49",value:0}],B=()=>{e.size===1366?(e.width=1366,e.height=768):e.size===1600?(e.width=1600,e.height=900):e.size===1920?(e.width=1920,e.height=1080):e.size===2560?(e.width=2560,e.height=1440):e.size===3840&&(e.width=3840,e.height=2160),s.computedScale()},z=[{key:m.FIT,title:"\u81EA\u9002\u5E94",icon:E,desc:"\u6309\u5C4F\u5E55\u6BD4\u4F8B\u81EA\u9002\u5E94 (\u7559\u767D\u53EF\u80FD\u53D8\u591A)"},{key:m.FULL,title:"\u94FA\u6EE1",icon:k,desc:"\u5F3A\u5236\u94FA\u6EE1 (\u5143\u7D20\u53EF\u80FD\u6324\u538B\u6216\u62C9\u4F38\u53D8\u5F62)"},{key:m.SCROLL_Y,title:"Y\u8F74\u6EDA\u52A8",icon:C,desc:"X\u8F74\u56FA\u5B9A\uFF0CY\u8F74\u81EA\u9002\u5E94\u6EDA\u52A8"}];W(()=>e.selectColor,c=>{w.value=c?0:1},{immediate:!0});const v=c=>c>50;let i=1;const D=()=>{e.lockScale&&(e.height=Math.round(e.width/i)),s.computedScale()},I=()=>{e.lockScale&&(e.width=Math.round(e.height*i)),s.computedScale()},L=()=>{e.lockScale=!e.lockScale,i=1,e.lockScale&&(i=e.width/e.height)};return(c,n)=>{const N=u("n-select"),p=u("n-text"),g=u("n-input"),T=u("n-icon"),U=u("n-radio"),_=u("n-space"),O=u("n-radio-group");return h(),F("div",j,[a(t(V),{name:"\u753B\u5E03\u5C3A\u5BF8"}),a(t(S),{showName:!1,alone:!0,margin:{top:!1,bottom:!0},itemBoxStyle:{margin:"0px 0px 20px 0px"}},{default:o(()=>[a(t(d),{showName:!1},{default:o(()=>[a(N,{class:"scale-btn",value:t(e).size,"onUpdate:value":[n[0]||(n[0]=l=>t(e).size=l),B],options:y},null,8,["value"])]),_:1})]),_:1}),t(e).size===0?(h(),q(t(S),{key:0,showName:!1,margin:{top:!1,bottom:!0},itemRightStyle:{gridTemplateColumns:"1fr 1fr auto"}},{default:o(()=>[a(t(d),{showName:!1},{default:o(()=>[a(g,{size:"small",value:t(e).width,"onUpdate:value":[n[1]||(n[1]=l=>t(e).width=l),D],disabled:t(f).lockScale,validator:v},{prefix:o(()=>[a(p,{depth:"3"},{default:o(()=>[r("\u5BBD")]),_:1})]),_:1},8,["value","disabled"])]),_:1}),a(t(d),{showName:!1},{default:o(()=>[a(g,{size:"small",value:t(e).height,"onUpdate:value":[n[2]||(n[2]=l=>t(e).height=l),I],disabled:t(f).lockScale,validator:v},{prefix:o(()=>[a(p,{depth:"3"},{default:o(()=>[r("\u9AD8")]),_:1})]),_:1},8,["value","disabled"])]),_:1}),a(t(d),{width:20,showName:!1},{default:o(()=>[a(T,{size:"16",style:{"margin-top":"4px"},component:t(e).lockScale?t(b):t(A),onClick:L},null,8,["component"])]),_:1})]),_:1})):P("",!0),a(_,{class:"detail",vertical:"",size:12},{default:o(()=>[a(_,null,{default:o(()=>[a(p,null,{default:o(()=>[r("\u9002\u914D\u65B9\u5F0F")]),_:1}),a(O,{value:t(e).previewScaleType,"onUpdate:value":n[3]||(n[3]=l=>t(e).previewScaleType=l),name:"radiogroup"},{default:o(()=>[a(_,null,{default:o(()=>[(h(),F(Q,null,X(z,l=>a(U,{key:l.key,value:l.key},{default:o(()=>[r($(l.desc),1)]),_:2},1032,["value"])),64))]),_:1})]),_:1},8,["value"])]),_:1})]),_:1})])}}});var oe=H(G,[["__scopeId","data-v-2de7b268"]]);export{oe as default};
