#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单导出督办数据
"""

import pandas as pd
from datetime import datetime

def create_sample_excel():
    """创建示例Excel文件"""
    print("📊 创建督办管理示例Excel...")
    
    try:
        # 创建示例数据
        data = []
        
        # 29个督办事项的示例数据
        items = [
            (1, "制度建设", "建立条线ITBP团队管理办法", "集团科技委员会", "建立条线ITBP团队管理办法，明确职责分工", "否", "2024-05-31", "√ 已完成"),
            (2, "制度建设", "建立项目红绿灯管理办法", "集团科技委员会", "建立项目红绿灯管理办法，实现项目状态可视化", "否", "2024-05-31", "√ 已完成"),
            (3, "制度建设", "印发8个信息化管理制度", "集团科技委员会", "印发8个信息化管理制度，规范信息化管理", "否", "2024-06-30", "O进行中"),
            (4, "制度建设", "印发非信创采购管理制度", "集团科技委员会", "印发非信创采购管理制度", "否", "2024-07-31", "X 未启动"),
            (5, "数据治理", "第一批次数据治理", "集团科技委员会", "完成第一批次数据治理工作", "是", "2024-08-31", "√ 已完成"),
            (6, "数据治理", "第二批次数据治理", "集团科技委员会", "完成第二批次数据治理工作", "是", "2024-10-31", "O进行中"),
            (7, "数据治理", "100%落实集团本部各管理条线要求", "集团科技委员会", "100%落实集团本部各管理条线要求", "是", "2024-12-31", "O进行中"),
            (8, "系统建设", "业务中台接入", "集团科技委员会", "完成业务中台接入工作", "否", "2024-09-30", "O进行中"),
            (9, "安全管理", "完成数据防泄漏方案并实施", "集团科技委员会", "完成数据防泄漏方案并实施", "是", "2024-08-31", "O进行中"),
            (10, "系统建设", "集团金投云方案的意见征集", "集团科技委员会", "集团金投云方案的意见征集", "否", "2024-06-30", "√ 已完成"),
            (11, "系统建设", "集团金投云方案完成上线", "集团科技委员会", "集团金投云方案完成上线", "是", "2024-12-31", "O进行中"),
            (12, "信创改造", "信创改造计划第一轮摸底", "集团科技委员会", "信创改造计划第一轮摸底", "否", "2024-05-31", "√ 已完成"),
            (13, "信创改造", "明确信创改造计划，完成2025年度改造计划", "集团科技委员会", "明确信创改造计划，完成2025年度改造计划", "是", "2024-07-31", "√ 已完成"),
            (14, "安全管理", "建立异地数据备份", "集团科技委员会", "建立异地数据备份", "是", "2024-09-30", "O进行中"),
            (15, "制度建设", "补例外采购备案流程", "集团科技委员会", "补例外采购备案流程", "否", "2024-08-31", "O进行中"),
            (16, "信创改造", "信创电脑卸载Windows系统", "集团科技委员会", "信创电脑卸载Windows系统", "否", "2024-10-31", "O进行中"),
            (17, "信创改造", "信创设备采购", "集团科技委员会", "信创设备采购", "否", "2024-11-30", "X 未启动"),
            (18, "信创改造", "信创正式报告", "集团科技委员会", "信创正式报告", "是", "2024-12-31", "O进行中"),
            (19, "系统建设", "诉讼案件导入", "集团科技委员会", "诉讼案件导入", "否", "2024-07-31", "√ 已完成"),
            (20, "系统建设", "业财一体", "集团科技委员会", "业财一体", "是", "2024-08-31", "√ 已完成"),
            (21, "系统建设", "集团资产管理专项项目", "集团科技委员会", "集团资产管理专项项目", "是", "2024-10-31", "O进行中"),
            (22, "系统建设", "广投司库对接项目", "集团科技委员会", "广投司库对接项目", "否", "2024-09-30", "O进行中"),
            (23, "系统建设", "集团档案管理系统", "集团科技委员会", "集团档案管理系统", "否", "2024-11-30", "O进行中"),
            (24, "规划管理", "制定集团数智规划", "集团科技委员会", "制定集团数智规划", "是", "2024-06-30", "√ 已完成"),
            (25, "制度建设", "加强外包管理，完成自查", "集团科技委员会", "加强外包管理，完成自查", "否", "2024-07-31", "√ 已完成"),
            (26, "制度建设", "规范招标采购流程，完成自查", "集团科技委员会", "规范招标采购流程，完成自查", "否", "2024-07-31", "√ 已完成"),
            (27, "制度建设", "提高系统应用程度，完成自查", "集团科技委员会", "提高系统应用程度，完成自查", "否", "2024-07-31", "√ 已完成"),
            (28, "制度建设", "按授权要求进行项目备案，完成自查", "集团科技委员会", "按授权要求进行项目备案，完成自查", "否", "2024-07-31", "√ 已完成"),
            (29, "规划管理", "集团科技项目投资计划调整", "集团科技委员会", "集团科技项目投资计划调整", "是", "2024-12-31", "O进行中")
        ]
        
        # 公司列表
        companies = ['财险', '寿险', '金租', '资管', '广租', '通盛', '担保', '小贷', '保理', '不动产', '征信', '金服', '本部']
        
        # 构建数据
        for item in items:
            row = {
                '序号': item[0],
                '工作维度': item[1],
                '工作主题': item[2],
                '督办来源': item[3],
                '工作内容和完成标志': item[4],
                '是否年度绩效考核指标': item[5],
                '完成时限': item[6],
                '整体进度': item[7]
            }
            
            # 添加公司状态（示例数据）
            seq = item[0]
            for company in companies:
                if seq <= 2:
                    row[company] = '√'  # 已完成
                elif seq <= 5:
                    row[company] = 'O'  # 进行中
                elif seq <= 8:
                    row[company] = 'X'  # 未启动
                else:
                    row[company] = '—'  # 不需要执行
            
            data.append(row)
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"督办管理表_{timestamp}.xlsx"
        
        # 导出到Excel
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='督办管理', index=False)
            
            # 设置列宽
            worksheet = writer.sheets['督办管理']
            worksheet.column_dimensions['A'].width = 8   # 序号
            worksheet.column_dimensions['B'].width = 20  # 工作维度
            worksheet.column_dimensions['C'].width = 35  # 工作主题
            worksheet.column_dimensions['D'].width = 15  # 督办来源
            worksheet.column_dimensions['E'].width = 50  # 工作内容
            worksheet.column_dimensions['F'].width = 18  # 是否考核指标
            worksheet.column_dimensions['G'].width = 12  # 完成时限
            worksheet.column_dimensions['H'].width = 12  # 整体进度
            
            # 设置公司列宽
            start_col = ord('I')
            for i, company in enumerate(companies):
                col_letter = chr(start_col + i)
                worksheet.column_dimensions[col_letter].width = 8
        
        print(f"✅ 创建成功: {filename}")
        print(f"数据行数: {len(df)}")
        print(f"数据列数: {len(df.columns)}")
        
        return filename
        
    except Exception as e:
        print(f"❌ 创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("🚀 督办管理Excel创建工具")
    print("=" * 50)
    
    filename = create_sample_excel()
    
    print("\n" + "=" * 50)
    if filename:
        print("🎉 创建成功！")
        print(f"📄 文件名: {filename}")
        print("\n📋 文件内容:")
        print("   • 29个督办事项完整信息")
        print("   • 13家公司状态列")
        print("   • 中文表头，便于编辑")
        print("   • 示例状态数据")
        
        print("\n📝 使用方法:")
        print("   1. 打开Excel文件")
        print("   2. 根据实际情况修改状态")
        print("   3. 保存文件")
        print("   4. 使用导入功能更新到系统")
    else:
        print("❌ 创建失败！")
    
    print("\n🏁 创建完成")

if __name__ == "__main__":
    main()
