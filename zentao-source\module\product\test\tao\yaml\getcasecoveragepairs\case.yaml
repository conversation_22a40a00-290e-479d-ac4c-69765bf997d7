title: table zt_case
desc: "测试用例"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-400
  - field: product
    note: "产品ID"
    range: 1-100{4}
  - field: branch
    note: "分支ID"
    range: 0
  - field: execution
    note: "执行ID"
    range: 101-700{4}
  - field: lib
    note: "所属库"
    range: 0
  - field: module
    note: "所属模块"
    range: 0
  - field: path
    note: ""
    range: "0"
  - field: story
    note: "相关需求"
    range: 1-100:1{4}
  - field: storyVersion
    note: "需求版本"
    range: "1"
  - field: title
    note: "用例标题"
    range: "1-400"
    prefix: "这个是测试用例"
  - field: precondition
    note: "前置条件"
    range: "1-400"
    prefix: "这是前置条件"
  - field: keywords
    note: "关键词"
    range: "1-400"
    prefix: "这是关键词"
  - field: pri
    note: "优先级"
    range: 1-4
  - field: type
    note: "用例类型"
    range: feature,performance,config,install,security,interface,other
  - field: auto
    note: "是否是自动化测试用例"
    range: "no"
  - field: frame
    note: "自动化测试框架"
    range: ""
  - field: stage
    note: "适用阶段"
    range: unittest,feature,intergrate,system,smoke,bvt
  - field: howRun
    note: "测试方式"
    range: ""
  - field: scriptedBy
    note: "脚本由谁创建"
    range: ""
  - field: scriptedDate
    note: "脚本创建日期"
    range: "(M)-(w)"
    type: timestamp
    postfix: ""
    format: "YY/MM/DD"
  - field: scriptStatus
    note: "脚本状态"
    range: ""
  - field: scriptLocation
    note: "脚本地址"
    range: ""
  - field: status
    note: "用例状态"
    range: wait,normal,blocked,investigate
  - field: subStatus
    note: "子状态"
    range: ""
  - field: color
    note: "标题颜色"
  - field: frequency
    note: "使用频率"
    range: 1-3
  - field: order
    note: "排序"
    range: 1-127:1
  - field: openedBy
    note: "由谁创建"
    range: 1-100
    prefix: "test"
  - field: openedDate
    note: "创建日期"
    range: "-:600"
    type: timestamp
    format: "YYYY-MM-DD hh:mm:ss"
  - field: reviewedBy
    note: "由谁评审"
    range: admin
  - field: reviewedDate
    note: "评审时间"
    range: "-:300"
    type: timestamp
    format: "YYYY-MM-DD hh:mm:ss"
  - field: lastEditedBy
    note: "最后修改者"
    range: admin
  - field: lastEditedDate
    note: "修改日期"
    range: "-:300"
    type: timestamp
    format: "YYYY-MM-DD hh:mm:ss"
  - field: version
    note: "用例版本"
    range: "1"
  - field: linkCase
    note: "相关用例"
    range: 0
  - field: fromBug
    note: "来源Bug"
    range: 0
  - field: fromCaseID
    note: ""
    range: 0
  - field: fromCaseVersion
    note: ""
    range: 1
  - field: deleted
    note: "是否删除"
    range: 0{9950},1{50}
  - field: lastRunner
    note: "执行人"
    range: 1-100
    prefix: "test"
  - field: lastRunDate
    note: "执行时间"
    range: "-:500"
    type: timestamp
    format: "YYYY-MM-DD hh:mm:ss"
  - field: lastRunResult
    note: "结果"
    range: "[pass,fail]"
