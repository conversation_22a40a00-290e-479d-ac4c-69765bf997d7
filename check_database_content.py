#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库内容，了解现有数据结构
"""

import pymysql
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

# 数据库配置
DB_CONFIG = {
    'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'cyh',
    'password': 'Qq188788',
    'database': 'kanban2',
    'charset': 'utf8mb4'
}

def check_table_structure():
    """检查表结构"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        logging.info("🔍 检查数据库表结构")
        logging.info("=" * 60)
        
        # 1. 检查督办事项表结构
        logging.info("📋 supervision_items 表结构:")
        cursor.execute("DESCRIBE supervision_items")
        items_columns = cursor.fetchall()
        for col in items_columns:
            logging.info(f"   {col['Field']}: {col['Type']} - {col['Comment'] or '无注释'}")
        
        logging.info("")
        
        # 2. 检查公司表结构
        logging.info("🏢 companies 表结构:")
        cursor.execute("DESCRIBE companies")
        companies_columns = cursor.fetchall()
        for col in companies_columns:
            logging.info(f"   {col['Field']}: {col['Type']} - {col['Comment'] or '无注释'}")
        
        logging.info("")
        
        # 3. 检查公司进度表结构
        logging.info("📊 company_progress 表结构:")
        cursor.execute("DESCRIBE company_progress")
        progress_columns = cursor.fetchall()
        for col in progress_columns:
            logging.info(f"   {col['Field']}: {col['Type']} - {col['Comment'] or '无注释'}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        logging.error(f"❌ 检查表结构失败: {e}")
        return False

def check_data_content():
    """检查数据内容"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        logging.info("📊 检查数据内容")
        logging.info("=" * 60)
        
        # 1. 检查督办事项数据
        cursor.execute("SELECT COUNT(*) as count FROM supervision_items WHERE deleted_at IS NULL")
        items_count = cursor.fetchone()['count']
        logging.info(f"📋 督办事项总数: {items_count}")
        
        if items_count > 0:
            cursor.execute("""
                SELECT id, sequence_number, work_theme, completion_deadline 
                FROM supervision_items 
                WHERE deleted_at IS NULL 
                ORDER BY sequence_number 
                LIMIT 5
            """)
            sample_items = cursor.fetchall()
            logging.info("   前5条督办事项:")
            for item in sample_items:
                logging.info(f"   - {item['sequence_number']}: {item['work_theme']} (截止: {item['completion_deadline']})")
        
        logging.info("")
        
        # 2. 检查公司数据
        cursor.execute("SELECT COUNT(*) as count FROM companies WHERE is_active = 1")
        companies_count = cursor.fetchone()['count']
        logging.info(f"🏢 活跃公司总数: {companies_count}")
        
        cursor.execute("SELECT company_code, company_name, display_order FROM companies WHERE is_active = 1 ORDER BY display_order")
        companies = cursor.fetchall()
        logging.info("   公司列表:")
        for company in companies:
            logging.info(f"   - {company['company_code']}: {company['company_name']} (顺序: {company['display_order']})")
        
        logging.info("")
        
        # 3. 检查公司进度数据
        cursor.execute("SELECT COUNT(*) as count FROM company_progress")
        progress_count = cursor.fetchone()['count']
        logging.info(f"📊 公司进度记录总数: {progress_count}")
        
        # 检查进度状态分布
        cursor.execute("""
            SELECT status, COUNT(*) as count 
            FROM company_progress 
            GROUP BY status 
            ORDER BY count DESC
        """)
        status_distribution = cursor.fetchall()
        logging.info("   进度状态分布:")
        for status in status_distribution:
            logging.info(f"   - {status['status']}: {status['count']} 条记录")
        
        logging.info("")
        
        # 4. 检查具体的进度数据样例
        cursor.execute("""
            SELECT 
                si.work_theme,
                c.company_name,
                cp.status,
                cp.progress_description,
                cp.completion_date
            FROM company_progress cp
            JOIN supervision_items si ON cp.supervision_item_id = si.id
            JOIN companies c ON cp.company_id = c.id
            WHERE si.deleted_at IS NULL
            ORDER BY si.sequence_number, c.display_order
            LIMIT 10
        """)
        progress_samples = cursor.fetchall()
        logging.info("   进度数据样例:")
        for progress in progress_samples:
            logging.info(f"   - {progress['work_theme']} | {progress['company_name']}: {progress['status']}")
            if progress['progress_description']:
                logging.info(f"     描述: {progress['progress_description']}")
            if progress['completion_date']:
                logging.info(f"     完成日期: {progress['completion_date']}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        logging.error(f"❌ 检查数据内容失败: {e}")
        return False

def check_current_issues():
    """检查当前存在的问题"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        logging.info("⚠️  检查当前问题")
        logging.info("=" * 60)
        
        # 1. 检查是否有overall_progress字段
        cursor.execute("SHOW COLUMNS FROM supervision_items LIKE 'overall_progress'")
        overall_progress_exists = cursor.fetchone()
        if overall_progress_exists:
            logging.info("✅ supervision_items 表有 overall_progress 字段")
        else:
            logging.info("❌ supervision_items 表缺少 overall_progress 字段")
        
        # 2. 检查是否有逾期的数据
        today = datetime.now().date()
        cursor.execute("""
            SELECT COUNT(*) as count 
            FROM supervision_items 
            WHERE completion_deadline < %s AND deleted_at IS NULL
        """, (today,))
        overdue_count = cursor.fetchone()['count']
        logging.info(f"⏰ 已逾期的督办事项: {overdue_count} 个")
        
        # 3. 检查进度状态是否规范
        cursor.execute("SELECT DISTINCT status FROM company_progress ORDER BY status")
        unique_statuses = cursor.fetchall()
        logging.info("📊 当前使用的进度状态:")
        for status in unique_statuses:
            logging.info(f"   - '{status['status']}'")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        logging.error(f"❌ 检查当前问题失败: {e}")
        return False

def main():
    """主函数"""
    logging.info("🚀 开始检查数据库内容")
    logging.info("=" * 80)
    
    # 检查表结构
    check_table_structure()
    logging.info("")
    
    # 检查数据内容
    check_data_content()
    logging.info("")
    
    # 检查当前问题
    check_current_issues()
    
    logging.info("=" * 80)
    logging.info("🎯 数据库内容检查完成")

if __name__ == "__main__":
    main()
