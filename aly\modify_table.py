import mysql.connector

# 数据库配置
config = {
    'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'cyh',
    'password': 'Qq188788',
    'database': 'kanban'
}

try:
    # 建立连接
    conn = mysql.connector.connect(**config)
    cursor = conn.cursor()
    
    # 删除现有主键
    cursor.execute("ALTER TABLE weekly_reports DROP PRIMARY KEY")
    
    # 修改id字段为自增主键
    cursor.execute("ALTER TABLE weekly_reports MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY")
    
    conn.commit()
    print("成功设置id为自增主键！")
    
except Exception as e:
    print(f"Error: {str(e)}")
finally:
    if 'cursor' in locals():
        cursor.close()
    if 'conn' in locals():
        conn.close() 