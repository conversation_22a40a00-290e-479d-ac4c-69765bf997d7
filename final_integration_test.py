#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工单集成功能最终综合测试
模拟用户完整操作流程
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'pmo-backend'))

import asyncio
import requests
from app.api.endpoints.ticket_integration import (
    get_dashboard_stats,
    get_ticket_projects,
    get_project_tickets,
    get_ticket_full_content
)

class FinalIntegrationTester:
    """最终集成测试器"""
    
    def __init__(self):
        self.mock_user = {"user_id": "test", "username": "test"}
        self.frontend_url = "http://localhost:3000"
        self.backend_url = "http://localhost:8000"
    
    def test_services_status(self):
        """测试服务状态"""
        print("🔍 检查服务状态...")
        print("-" * 50)
        
        # 检查前端
        try:
            response = requests.get(self.frontend_url, timeout=5)
            if response.status_code == 200:
                print("✅ 前端服务正常运行")
            else:
                print(f"❌ 前端服务异常: {response.status_code}")
                return False
        except:
            print("❌ 前端服务无法访问")
            return False
        
        # 检查后端API文档
        try:
            response = requests.get(f"{self.backend_url}/docs", timeout=5)
            if response.status_code == 200:
                print("✅ 后端服务正常运行")
            else:
                print(f"❌ 后端服务异常: {response.status_code}")
                return False
        except:
            print("❌ 后端服务无法访问")
            return False
        
        return True
    
    async def test_backend_functionality(self):
        """测试后端功能"""
        print("\n🔧 测试后端功能...")
        print("-" * 50)
        
        try:
            # 1. 测试仪表盘统计
            print("📊 测试仪表盘统计...")
            stats_result = await get_dashboard_stats(current_user=self.mock_user)
            
            if stats_result.get('success'):
                stats_data = stats_result.get('data', {})
                project_stats = stats_data.get('project_stats', {})
                ticket_stats = stats_data.get('ticket_stats', {})
                
                print(f"   ✅ 项目总数: {project_stats.get('total_projects', 0)}")
                print(f"   ✅ 工单总数: {ticket_stats.get('total_tickets', 0)}")
                
                total_projects = project_stats.get('total_projects', 0)
            else:
                print("   ❌ 仪表盘统计失败")
                return False
            
            # 2. 测试项目列表
            print("\n📋 测试项目列表...")
            projects_result = await get_ticket_projects(limit=3, current_user=self.mock_user)
            
            if projects_result.get('success'):
                projects_data = projects_result.get('data', {})
                projects = projects_data.get('projects', [])
                
                print(f"   ✅ 获取到 {len(projects)} 个项目")
                
                if projects:
                    selected_project = projects[0]
                    project_id = selected_project['feelec_project_id']
                    project_name = selected_project['feelec_name']
                    print(f"   📌 选择测试项目: {project_name}")
                else:
                    print("   ❌ 没有可测试的项目")
                    return False
            else:
                print("   ❌ 项目列表获取失败")
                return False
            
            # 3. 测试项目工单
            print(f"\n🎫 测试项目工单 ({project_name})...")
            tickets_result = await get_project_tickets(project_id=project_id, current_user=self.mock_user)
            
            if tickets_result.get('success'):
                tickets_data = tickets_result.get('data', {})
                tickets = tickets_data.get('tickets', [])
                
                print(f"   ✅ 获取到 {len(tickets)} 个工单")
                
                if tickets:
                    selected_ticket = tickets[0]
                    ticket_id = selected_ticket['feelec_ticket_id']
                    ticket_title = selected_ticket['feelec_title']
                    print(f"   📌 选择测试工单: {ticket_title}")
                else:
                    print("   ⚠️  该项目暂无工单")
                    return True  # 这不算错误
            else:
                print("   ❌ 项目工单获取失败")
                return False
            
            # 4. 测试工单详情
            if tickets:
                print(f"\n📄 测试工单详情 ({ticket_title})...")
                content_result = await get_ticket_full_content(ticket_id=ticket_id, current_user=self.mock_user)
                
                if content_result.get('success'):
                    content_data = content_result.get('data', {})
                    
                    print(f"   ✅ 工单编号: {content_data.get('feelec_ticket_no', '无')}")
                    print(f"   ✅ 工单状态: {content_data.get('status_name', '无')}")
                    print(f"   ✅ 优先级: {content_data.get('priority_text', '无')}")
                    
                    # 检查发布人信息
                    publisher_info = content_data.get('publisher_info', {})
                    if publisher_info:
                        print(f"   ✅ 发布人: {publisher_info.get('feelec_name', '无')}")
                        company_info = publisher_info.get('company_info', {})
                        if company_info:
                            print(f"   ✅ 所属公司: {company_info.get('company_name', '无')}")
                    
                    # 检查工单内容
                    ticket_content = content_data.get('ticket_content', {})
                    if ticket_content:
                        print(f"   ✅ 详细内容: 已获取")
                    
                    print("   ✅ 工单详情获取完整")
                else:
                    print("   ❌ 工单详情获取失败")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ 后端功能测试失败: {e}")
            return False
    
    def test_frontend_pages(self):
        """测试前端页面"""
        print("\n🌐 测试前端页面...")
        print("-" * 50)
        
        pages = [
            ("/", "首页"),
            ("/ticket-integration", "工单集成页面")
        ]
        
        for path, name in pages:
            try:
                url = f"{self.frontend_url}{path}"
                response = requests.get(url, timeout=10)
                
                if response.status_code == 200:
                    print(f"   ✅ {name}: 访问正常")
                else:
                    print(f"   ❌ {name}: 访问异常 ({response.status_code})")
                    return False
                    
            except Exception as e:
                print(f"   ❌ {name}: 访问失败 ({e})")
                return False
        
        return True
    
    async def run_complete_test(self):
        """运行完整测试"""
        print("🎯 工单集成功能最终综合测试")
        print("=" * 80)
        print("📝 测试目标:")
        print("   1. 验证前后端服务正常运行")
        print("   2. 验证后端API功能完整性")
        print("   3. 验证前端页面正常访问")
        print("   4. 验证完整的四级钻取功能")
        print("=" * 80)
        
        # 1. 检查服务状态
        if not self.test_services_status():
            print("\n❌ 服务状态检查失败，请检查前后端服务是否正常启动")
            return False
        
        # 2. 测试后端功能
        if not await self.test_backend_functionality():
            print("\n❌ 后端功能测试失败")
            return False
        
        # 3. 测试前端页面
        if not self.test_frontend_pages():
            print("\n❌ 前端页面测试失败")
            return False
        
        return True

async def main():
    """主函数"""
    tester = FinalIntegrationTester()
    success = await tester.run_complete_test()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 最终综合测试: 完全成功！")
        print("✅ 前后端服务运行正常")
        print("✅ 所有API功能正常")
        print("✅ 前端页面访问正常")
        print("✅ 四级钻取功能完整")
        print("")
        print("🚀 工单集成功能已完全就绪，可以正常使用！")
        print("")
        print("💡 访问地址:")
        print("   🌐 前端应用: http://localhost:3000")
        print("   🎫 工单集成: http://localhost:3000/ticket-integration")
        print("   📚 API文档: http://localhost:8000/docs")
    else:
        print("❌ 最终综合测试: 存在问题")
        print("⚠️  请检查错误信息并修复问题")
    print("=" * 80)
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
