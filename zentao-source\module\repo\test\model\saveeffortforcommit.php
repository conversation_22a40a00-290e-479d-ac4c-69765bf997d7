#!/usr/bin/env php
<?php
include dirname(__FILE__, 5) . '/test/lib/init.php';
include dirname(__FILE__, 2) . '/lib/repo.unittest.class.php';
su('admin');

/**

title=测试 repoModel->saveEffortForCommit();
timeout=0
cid=1

- 工时计算
 - 属性status @doing
 - 属性consumed @11
 - 属性left @3

*/

zenData('task')->gen(10);
$bug = zenData('bug');
$bug->execution->range('0');
$bug->gen(10);
zenData('repo')->loadYaml('repo')->gen(4);

$repoID   = 1;
$repoRoot = '';
$scm      = 'gitlab';

$log = new stdclass();
$log->revision  = '61e51cadb1aa21ef3d2b51e3f193be3cc19cfef6';
$log->committer = 'root';
$log->time      = '2023-12-29 10:44:36';
$log->comment   = 'Effort Task #8 Cost:1h Left:3h';
$log->author    = 'user4';
$log->msg       = 'Effort Task #8 Cost:1h Left:3h';
$log->date      = '2023-12-29 10:44:36';
$log->files     = array('M' => array('/README.md'));
$log->change    = array('/README.md' => array('action' => 'M', 'kind' => 'file', 'oldPath' => ''));

$action  = new stdclass();
$action->actor  = 'user4';
$action->date   = '2023-12-29 13:14:36';
$action->extra  = $scm == 'svn' ? $log->revision : substr($log->revision, 0, 10);
$action->action = 'gitcommited';

global $app;
include($app->getModuleRoot() . '/repo/control.php');
$app->control = new repo();

$repo = new repoTest();
$repo->saveEffortForCommitTest($log, $action, $repoID);
$result = $tester->loadModel('task')->getById(8);
r($result) && p('status,consumed,left') && e('doing,11,3'); //工时计算