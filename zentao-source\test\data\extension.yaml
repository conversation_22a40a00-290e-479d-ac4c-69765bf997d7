title: table zt_extension
desc: "插件"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: name
    note: "插件名称"
    range: 1-10000
    prefix: "这是插件名称"
    postfix: ""
    loop: 0
    format: ""
  - field: code
    note: "代号"
    range: 1-10000
    prefix: "code"
    postfix: ""
    loop: 0
    format: ""
  - field: version
    note: "版本号"
    range: 1-3
    prefix: "version"
    postfix: ""
    loop: 0
    format: ""
  - field: author
    note: "作者"
    range: 1-10000
    prefix: "author"
    postfix: ""
    loop: 0
    format: ""
  - field: desc
    note: "描述"
    range: 1-10000
    prefix: "这是插件描述"
    postfix: ""
    loop: 0
    format: ""
  - field: license
    note: "授权"
    range: LGPL
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "类型"
    range: extension
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: site
    note: "官网"
    range: zentao
    prefix: "http://"
    postfix: ".net"
    loop: 0
    format: ""
  - field: zentaoCompatible
    note: "适用版本"
    range: all
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: installedTime
    note: "安装时间"
    range: "-:60"
    type: timestamp
    format: "YYYY-MM-DD hh:mm:ss"
  - field: depends
    note: "依赖"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: dirs
    note: "安装目录"
    range: []
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: files
    note: "安装文件"
    range: 1-10000
    prefix: "json格式文件内容"
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: "安装状态"
    range: installed,deactivated
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
