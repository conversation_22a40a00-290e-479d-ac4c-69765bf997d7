title: table zt_project
author: <PERSON>
version: "1.0"
fields:
  - field: id
    range: 1001-20000
  - field: project
    range: 1-1000
  - field: model
    range: scrum{9000},waterfall{5000},kanban{5000}
  - field: name
    note: "名称"
    fields:
    - field: name1
      range: '项目'
    - field: name2
      range: 1-25000
  - field: type
    range: project
  - field: status
    range: wait{2},doing{4},suspended,closed
  - field: lifetime
    range:
  - field: budget
    range: 900000-1:100
  - field: budgetUnit
    range: CNY,USD
  - field: attribute
    note: "Only stage has attribute"
    range:
  - field: percent
    range: 1-100:R
  - field: milestone
    note: "Is it milestone"
    range: 0
  - field: output
    note: "Output document"
    range:
  - field: auth
    note: "Only project has auth"
    range: "extend"
  - field: parent
    range: 1-1000
  - field: path
    fields:
      - field: path1
        prefix: ","
        range: 1-1000
      - field: path2
        prefix: ","
        range: 1001-20000
        postfix: ","
  - field: grade
    range: "2"
  - field: code
    range: 1-25000
    prefix: "project"
  - field: begin
    range: "(-3M)-(+M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: end
    range: "(+5w)-(+2M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: realBegan
    range: "(-3M)-(+M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: realEnd
    range: "(+5w)-(+2M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: PO
    range:
  - field: PM
    range:
  - field: QD
    range:
  - field: RD
    range:
  - field: team
    range:
  - field: acl
    range: open,private,program
  - field: order
    range: 5-100000:5
  - field: openedVersion
    range: "18.3"
  - field: deleted
    range: 0
