#productsBox .product {border: 1px solid #ddd; margin-bottom: 5px; position: relative; height: 40px; border-radius: 4px;}
#productsBox .product:hover {background: #f1f1f1;}
#productsBox .product.checked,
#productsBox .product.checked:hover {background: #E5FFE6; border: 1px solid #229F24;}
#productsBox .product .checkbox-primary {padding: 9px; cursor: pointer;}
#productsBox .product.has-branch {padding-right: 40%;}
#productsBox .product.has-branch > .chosen-container {position: absolute; width: 40% !important; right: 3px; top: 3px;}
#productsBox .product.has-branch > .chosen-container > .chosen-single {background: rgba(0,0,0,.05);}
#productsBox .product.has-branch > .chosen-container > .chosen-single:hover {background: #fff;}
#productsBox .chosen-container .chosen-results{max-height: 180px;}
