title: table zt_workflowfield
desc: ""
author: automated export
version: "1.0"
fields:
  - field: id
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: module
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: field
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: type
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: length
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: name
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: control
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: options
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: default
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: rules
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: placeholder
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: canExport
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: canSearch
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: isValue
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: order
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: buildin
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: desc
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: readonly
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: createdBy
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: createdDate
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: editedBy
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: editedDate
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
