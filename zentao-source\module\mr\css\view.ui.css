.cell {border: 1px solid #efefef; padding: 10px; border-radius: 4px;}
.cell-history {padding: 0px;}

#mrMenu menu.nav {border-bottom: 1px solid #ddd}
#mrMenu .nav-item>a.active {border-bottom-color: var(--color-primary-500) !important; border-bottom-width: 2px; margin-bottom: -1px;}

.detail-title {color: var(--color-gray-900); font-weight: 600; font-size: 1rem; line-height: 1.5rem;}
.detail-content .btn-active-text {padding-top: 5px;}
.detail-content p {margin: 0 0 10px;}
blockquote {padding: 10px 20px; margin: 0 0 20px; font-size: 16.25px; border-left: 5px solid #eee;}
pre {display: block; padding: 9.5px; margin: 0 0 10px; font-size: 12px; line-height: 1.38461538; color: #313c52; word-break: break-all; word-wrap: break-word; background-color: #f5f5f5; border: 1px solid #ccc; border-radius: 4px;}
code {padding: 2px 4px; font-size: 90%; color: #37474f; background-color: #f3f5f7; border-radius: 4px;}

.mr-view-cell {border: none; padding: 0;}
