(()=>{var bn=class{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(t){setTimeout(()=>{throw t.stack?at.isErrorNoTelemetry(t)?new at(t.message+`

`+t.stack):new Error(t.message+`

`+t.stack):t},0)}}emit(t){this.listeners.forEach(n=>{n(t)})}onUnexpectedError(t){this.unexpectedErrorHandler(t),this.emit(t)}onUnexpectedExternalError(t){this.unexpectedErrorHandler(t)}},yi=new bn;function Et(e){Ei(e)||yi.onUnexpectedError(e)}function vn(e){if(e instanceof Error){let{name:t,message:n}=e,r=e.stacktrace||e.stack;return{$isError:!0,name:t,message:n,stack:r,noTelemetry:at.isErrorNoTelemetry(e)}}return e}var xn="Canceled";function Ei(e){return e instanceof _n?!0:e instanceof Error&&e.name===xn&&e.message===xn}var _n=class extends Error{constructor(){super(xn),this.name=this.message}};var at=class e extends Error{constructor(t){super(t),this.name="CodeExpectedError"}static fromError(t){if(t instanceof e)return t;let n=new e;return n.message=t.message,n.stack=t.stack,n}static isErrorNoTelemetry(t){return t.name==="CodeExpectedError"}},se=class e extends Error{constructor(t){super(t||"An unexpected bug occurred."),Object.setPrototypeOf(this,e.prototype)}};function Ln(e,t){let n=this,r=!1,i;return function(){if(r)return i;if(r=!0,t)try{i=e.apply(n,arguments)}finally{t()}else i=e.apply(n,arguments);return i}}var Oe;(function(e){function t(b){return b&&typeof b=="object"&&typeof b[Symbol.iterator]=="function"}e.is=t;let n=Object.freeze([]);function r(){return n}e.empty=r;function*i(b){yield b}e.single=i;function s(b){return t(b)?b:i(b)}e.wrap=s;function o(b){return b||n}e.from=o;function*l(b){for(let x=b.length-1;x>=0;x--)yield b[x]}e.reverse=l;function u(b){return!b||b[Symbol.iterator]().next().done===!0}e.isEmpty=u;function c(b){return b[Symbol.iterator]().next().value}e.first=c;function f(b,x){for(let v of b)if(x(v))return!0;return!1}e.some=f;function h(b,x){for(let v of b)if(x(v))return v}e.find=h;function*d(b,x){for(let v of b)x(v)&&(yield v)}e.filter=d;function*g(b,x){let v=0;for(let w of b)yield x(w,v++)}e.map=g;function*p(...b){for(let x of b)yield*x}e.concat=p;function m(b,x,v){let w=v;for(let R of b)w=x(w,R);return w}e.reduce=m;function*_(b,x,v=b.length){for(x<0&&(x+=b.length),v<0?v+=b.length:v>b.length&&(v=b.length);x<v;x++)yield b[x]}e.slice=_;function N(b,x=Number.POSITIVE_INFINITY){let v=[];if(x===0)return[v,b];let w=b[Symbol.iterator]();for(let R=0;R<x;R++){let y=w.next();if(y.done)return[v,e.empty()];v.push(y.value)}return[v,{[Symbol.iterator](){return w}}]}e.consume=N})(Oe||(Oe={}));var Mi=!1,oe=null;function Fi(e){oe=e}if(Mi){let e="__is_disposable_tracked__";Fi(new class{trackDisposable(t){let n=new Error("Potentially leaked disposable").stack;setTimeout(()=>{t[e]||console.log(n)},3e3)}setParent(t,n){if(t&&t!==ae.None)try{t[e]=!0}catch{}}markAsDisposed(t){if(t&&t!==ae.None)try{t[e]=!0}catch{}}markAsSingleton(t){}})}function Nn(e){return oe?.trackDisposable(e),e}function Sn(e){oe?.markAsDisposed(e)}function wn(e,t){oe?.setParent(e,t)}function ki(e,t){if(oe)for(let n of e)oe.setParent(n,t)}function _r(e){if(Oe.is(e)){let t=[];for(let n of e)if(n)try{n.dispose()}catch(r){t.push(r)}if(t.length===1)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function vr(...e){let t=Pe(()=>_r(e));return ki(e,t),t}function Pe(e){let t=Nn({dispose:Ln(()=>{Sn(t),e()})});return t}var _e=class e{constructor(){this._toDispose=new Set,this._isDisposed=!1,Nn(this)}dispose(){this._isDisposed||(Sn(this),this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){if(this._toDispose.size!==0)try{_r(this._toDispose)}finally{this._toDispose.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return wn(t,this),this._isDisposed?e.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(t),t}deleteAndLeak(t){t&&this._toDispose.has(t)&&(this._toDispose.delete(t),wn(t,null))}};_e.DISABLE_DISPOSED_WARNING=!1;var ae=class{constructor(){this._store=new _e,Nn(this),wn(this._store,this)}dispose(){Sn(this),this._store.dispose()}_register(t){if(t===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(t)}};ae.None=Object.freeze({dispose(){}});var z=class e{constructor(t){this.element=t,this.next=e.Undefined,this.prev=e.Undefined}};z.Undefined=new z(void 0);var lt=class{constructor(){this._first=z.Undefined,this._last=z.Undefined,this._size=0}get size(){return this._size}isEmpty(){return this._first===z.Undefined}clear(){let t=this._first;for(;t!==z.Undefined;){let n=t.next;t.prev=z.Undefined,t.next=z.Undefined,t=n}this._first=z.Undefined,this._last=z.Undefined,this._size=0}unshift(t){return this._insert(t,!1)}push(t){return this._insert(t,!0)}_insert(t,n){let r=new z(t);if(this._first===z.Undefined)this._first=r,this._last=r;else if(n){let s=this._last;this._last=r,r.prev=s,s.next=r}else{let s=this._first;this._first=r,r.next=s,s.prev=r}this._size+=1;let i=!1;return()=>{i||(i=!0,this._remove(r))}}shift(){if(this._first!==z.Undefined){let t=this._first.element;return this._remove(this._first),t}}pop(){if(this._last!==z.Undefined){let t=this._last.element;return this._remove(this._last),t}}_remove(t){if(t.prev!==z.Undefined&&t.next!==z.Undefined){let n=t.prev;n.next=t.next,t.next.prev=n}else t.prev===z.Undefined&&t.next===z.Undefined?(this._first=z.Undefined,this._last=z.Undefined):t.next===z.Undefined?(this._last=this._last.prev,this._last.next=z.Undefined):t.prev===z.Undefined&&(this._first=this._first.next,this._first.prev=z.Undefined);this._size-=1}*[Symbol.iterator](){let t=this._first;for(;t!==z.Undefined;)yield t.element,t=t.next}};var Di=globalThis.performance&&typeof globalThis.performance.now=="function",He=class e{static create(t){return new e(t)}constructor(t){this._now=Di&&t===!1?Date.now:globalThis.performance.now.bind(globalThis.performance),this._startTime=this._now(),this._stopTime=-1}stop(){this._stopTime=this._now()}elapsed(){return this._stopTime!==-1?this._stopTime-this._startTime:this._now()-this._startTime}};var Lr=!1,Pi=!1,Mt;(function(e){e.None=()=>ae.None;function t(A){if(Pi){let{onDidAddListener:L}=A,S=ct.create(),C=0;A.onDidAddListener=()=>{++C===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),S.print()),L?.()}}}function n(A,L){return d(A,()=>{},0,void 0,!0,void 0,L)}e.defer=n;function r(A){return(L,S=null,C)=>{let F=!1,D;return D=A(V=>{if(!F)return D?D.dispose():F=!0,L.call(S,V)},null,C),F&&D.dispose(),D}}e.once=r;function i(A,L,S){return f((C,F=null,D)=>A(V=>C.call(F,L(V)),null,D),S)}e.map=i;function s(A,L,S){return f((C,F=null,D)=>A(V=>{L(V),C.call(F,V)},null,D),S)}e.forEach=s;function o(A,L,S){return f((C,F=null,D)=>A(V=>L(V)&&C.call(F,V),null,D),S)}e.filter=o;function l(A){return A}e.signal=l;function u(...A){return(L,S=null,C)=>{let F=vr(...A.map(D=>D(V=>L.call(S,V))));return h(F,C)}}e.any=u;function c(A,L,S,C){let F=S;return i(A,D=>(F=L(F,D),F),C)}e.reduce=c;function f(A,L){let S,C={onWillAddFirstListener(){S=A(F.fire,F)},onDidRemoveLastListener(){S?.dispose()}};L||t(C);let F=new Z(C);return L?.add(F),F.event}function h(A,L){return L instanceof Array?L.push(A):L&&L.add(A),A}function d(A,L,S=100,C=!1,F=!1,D,V){let X,ee,ze,Rt=0,De,xr={leakWarningThreshold:D,onWillAddFirstListener(){X=A(Ai=>{Rt++,ee=L(ee,Ai),C&&!ze&&(yt.fire(ee),ee=void 0),De=()=>{let Ri=ee;ee=void 0,ze=void 0,(!C||Rt>1)&&yt.fire(Ri),Rt=0},typeof S=="number"?(clearTimeout(ze),ze=setTimeout(De,S)):ze===void 0&&(ze=0,queueMicrotask(De))})},onWillRemoveListener(){F&&Rt>0&&De?.()},onDidRemoveLastListener(){De=void 0,X.dispose()}};V||t(xr);let yt=new Z(xr);return V?.add(yt),yt.event}e.debounce=d;function g(A,L=0,S){return e.debounce(A,(C,F)=>C?(C.push(F),C):[F],L,void 0,!0,void 0,S)}e.accumulate=g;function p(A,L=(C,F)=>C===F,S){let C=!0,F;return o(A,D=>{let V=C||!L(D,F);return C=!1,F=D,V},S)}e.latch=p;function m(A,L,S){return[e.filter(A,L,S),e.filter(A,C=>!L(C),S)]}e.split=m;function _(A,L=!1,S=[],C){let F=S.slice(),D=A(ee=>{F?F.push(ee):X.fire(ee)});C&&C.add(D);let V=()=>{F?.forEach(ee=>X.fire(ee)),F=null},X=new Z({onWillAddFirstListener(){D||(D=A(ee=>X.fire(ee)),C&&C.add(D))},onDidAddFirstListener(){F&&(L?setTimeout(V):V())},onDidRemoveLastListener(){D&&D.dispose(),D=null}});return C&&C.add(X),X.event}e.buffer=_;function N(A,L){return(C,F,D)=>{let V=L(new x);return A(function(X){let ee=V.evaluate(X);ee!==b&&C.call(F,ee)},void 0,D)}}e.chain=N;let b=Symbol("HaltChainable");class x{constructor(){this.steps=[]}map(L){return this.steps.push(L),this}forEach(L){return this.steps.push(S=>(L(S),S)),this}filter(L){return this.steps.push(S=>L(S)?S:b),this}reduce(L,S){let C=S;return this.steps.push(F=>(C=L(C,F),C)),this}latch(L=(S,C)=>S===C){let S=!0,C;return this.steps.push(F=>{let D=S||!L(F,C);return S=!1,C=F,D?F:b}),this}evaluate(L){for(let S of this.steps)if(L=S(L),L===b)break;return L}}function v(A,L,S=C=>C){let C=(...X)=>V.fire(S(...X)),F=()=>A.on(L,C),D=()=>A.removeListener(L,C),V=new Z({onWillAddFirstListener:F,onDidRemoveLastListener:D});return V.event}e.fromNodeEventEmitter=v;function w(A,L,S=C=>C){let C=(...X)=>V.fire(S(...X)),F=()=>A.addEventListener(L,C),D=()=>A.removeEventListener(L,C),V=new Z({onWillAddFirstListener:F,onDidRemoveLastListener:D});return V.event}e.fromDOMEventEmitter=w;function R(A){return new Promise(L=>r(A)(L))}e.toPromise=R;function y(A){let L=new Z;return A.then(S=>{L.fire(S)},()=>{L.fire(void 0)}).finally(()=>{L.dispose()}),L.event}e.fromPromise=y;function M(A,L,S){return L(S),A(C=>L(C))}e.runAndSubscribe=M;function U(A,L){let S=null;function C(D){S?.dispose(),S=new _e,L(D,S)}C(void 0);let F=A(D=>C(D));return Pe(()=>{F.dispose(),S?.dispose()})}e.runAndSubscribeWithStore=U;class j{constructor(L,S){this._observable=L,this._counter=0,this._hasChanged=!1;let C={onWillAddFirstListener:()=>{L.addObserver(this)},onDidRemoveLastListener:()=>{L.removeObserver(this)}};S||t(C),this.emitter=new Z(C),S&&S.add(this.emitter)}beginUpdate(L){this._counter++}handlePossibleChange(L){}handleChange(L,S){this._hasChanged=!0}endUpdate(L){this._counter--,this._counter===0&&(this._observable.reportChanges(),this._hasChanged&&(this._hasChanged=!1,this.emitter.fire(this._observable.get())))}}function B(A,L){return new j(A,L).emitter.event}e.fromObservable=B;function k(A){return(L,S,C)=>{let F=0,D=!1,V={beginUpdate(){F++},endUpdate(){F--,F===0&&(A.reportChanges(),D&&(D=!1,L.call(S)))},handlePossibleChange(){},handleChange(){D=!0}};A.addObserver(V),A.reportChanges();let X={dispose(){A.removeObserver(V)}};return C instanceof _e?C.add(X):Array.isArray(C)&&C.push(X),X}}e.fromObservableLight=k})(Mt||(Mt={}));var ut=class e{constructor(t){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${t}_${e._idPool++}`,e.all.add(this)}start(t){this._stopWatch=new He,this.listenerCount=t}stop(){if(this._stopWatch){let t=this._stopWatch.elapsed();this.durations.push(t),this.elapsedOverall+=t,this.invocationCount+=1,this._stopWatch=void 0}}};ut.all=new Set;ut._idPool=0;var wr=-1,Cn=class{constructor(t,n=Math.random().toString(18).slice(2,5)){this.threshold=t,this.name=n,this._warnCountdown=0}dispose(){var t;(t=this._stacks)===null||t===void 0||t.clear()}check(t,n){let r=this.threshold;if(r<=0||n<r)return;this._stacks||(this._stacks=new Map);let i=this._stacks.get(t.value)||0;if(this._stacks.set(t.value,i+1),this._warnCountdown-=1,this._warnCountdown<=0){this._warnCountdown=r*.5;let s,o=0;for(let[l,u]of this._stacks)(!s||o<u)&&(s=l,o=u);console.warn(`[${this.name}] potential listener LEAK detected, having ${n} listeners already. MOST frequent listener (${o}):`),console.warn(s)}return()=>{let s=this._stacks.get(t.value)||0;this._stacks.set(t.value,s-1)}}},ct=class e{static create(){var t;return new e((t=new Error().stack)!==null&&t!==void 0?t:"")}constructor(t){this.value=t}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},$e=class{constructor(t){this.value=t}},Ii=2,Ti=(e,t)=>{if(e instanceof $e)t(e);else for(let n=0;n<e.length;n++){let r=e[n];r&&t(r)}},Z=class{constructor(t){var n,r,i,s,o;this._size=0,this._options=t,this._leakageMon=wr>0||!((n=this._options)===null||n===void 0)&&n.leakWarningThreshold?new Cn((i=(r=this._options)===null||r===void 0?void 0:r.leakWarningThreshold)!==null&&i!==void 0?i:wr):void 0,this._perfMon=!((s=this._options)===null||s===void 0)&&s._profName?new ut(this._options._profName):void 0,this._deliveryQueue=(o=this._options)===null||o===void 0?void 0:o.deliveryQueue}dispose(){var t,n,r,i;if(!this._disposed){if(this._disposed=!0,((t=this._deliveryQueue)===null||t===void 0?void 0:t.current)===this&&this._deliveryQueue.reset(),this._listeners){if(Lr){let s=this._listeners;queueMicrotask(()=>{Ti(s,o=>{var l;return(l=o.stack)===null||l===void 0?void 0:l.print()})})}this._listeners=void 0,this._size=0}(r=(n=this._options)===null||n===void 0?void 0:n.onDidRemoveLastListener)===null||r===void 0||r.call(n),(i=this._leakageMon)===null||i===void 0||i.dispose()}}get event(){var t;return(t=this._event)!==null&&t!==void 0||(this._event=(n,r,i)=>{var s,o,l,u,c;if(this._leakageMon&&this._size>this._leakageMon.threshold*3)return console.warn(`[${this._leakageMon.name}] REFUSES to accept new listeners because it exceeded its threshold by far`),ae.None;if(this._disposed)return ae.None;r&&(n=n.bind(r));let f=new $e(n),h,d;this._leakageMon&&this._size>=Math.ceil(this._leakageMon.threshold*.2)&&(f.stack=ct.create(),h=this._leakageMon.check(f.stack,this._size+1)),Lr&&(f.stack=d??ct.create()),this._listeners?this._listeners instanceof $e?((c=this._deliveryQueue)!==null&&c!==void 0||(this._deliveryQueue=new An),this._listeners=[this._listeners,f]):this._listeners.push(f):((o=(s=this._options)===null||s===void 0?void 0:s.onWillAddFirstListener)===null||o===void 0||o.call(s,this),this._listeners=f,(u=(l=this._options)===null||l===void 0?void 0:l.onDidAddFirstListener)===null||u===void 0||u.call(l,this)),this._size++;let g=Pe(()=>{h?.(),this._removeListener(f)});return i instanceof _e?i.add(g):Array.isArray(i)&&i.push(g),g}),this._event}_removeListener(t){var n,r,i,s;if((r=(n=this._options)===null||n===void 0?void 0:n.onWillRemoveListener)===null||r===void 0||r.call(n,this),!this._listeners)return;if(this._size===1){this._listeners=void 0,(s=(i=this._options)===null||i===void 0?void 0:i.onDidRemoveLastListener)===null||s===void 0||s.call(i,this),this._size=0;return}let o=this._listeners,l=o.indexOf(t);if(l===-1)throw console.log("disposed?",this._disposed),console.log("size?",this._size),console.log("arr?",JSON.stringify(this._listeners)),new Error("Attempted to dispose unknown listener");this._size--,o[l]=void 0;let u=this._deliveryQueue.current===this;if(this._size*Ii<=o.length){let c=0;for(let f=0;f<o.length;f++)o[f]?o[c++]=o[f]:u&&(this._deliveryQueue.end--,c<this._deliveryQueue.i&&this._deliveryQueue.i--);o.length=c}}_deliver(t,n){var r;if(!t)return;let i=((r=this._options)===null||r===void 0?void 0:r.onListenerError)||Et;if(!i){t.value(n);return}try{t.value(n)}catch(s){i(s)}}_deliverQueue(t){let n=t.current._listeners;for(;t.i<t.end;)this._deliver(n[t.i++],t.value);t.reset()}fire(t){var n,r,i,s;if(!((n=this._deliveryQueue)===null||n===void 0)&&n.current&&(this._deliverQueue(this._deliveryQueue),(r=this._perfMon)===null||r===void 0||r.stop()),(i=this._perfMon)===null||i===void 0||i.start(this._size),this._listeners)if(this._listeners instanceof $e)this._deliver(this._listeners,t);else{let o=this._deliveryQueue;o.enqueue(this,t,this._listeners.length),this._deliverQueue(o)}(s=this._perfMon)===null||s===void 0||s.stop()}hasListeners(){return this._size>0}};var An=class{constructor(){this.i=-1,this.end=0}enqueue(t,n,r){this.i=0,this.end=r,this.current=t,this.value=n}reset(){this.i=this.end,this.current=void 0,this.value=void 0}};function Nr(e){return typeof e=="string"}function Bi(e){let t=[];for(;Object.prototype!==e;)t=t.concat(Object.getOwnPropertyNames(e)),e=Object.getPrototypeOf(e);return t}function ht(e){let t=[];for(let n of Bi(e))typeof e[n]=="function"&&t.push(n);return t}function Sr(e,t){let n=i=>function(){let s=Array.prototype.slice.call(arguments,0);return t(i,s)},r={};for(let i of e)r[i]=n(i);return r}var Vi=typeof document<"u"&&document.location&&document.location.hash.indexOf("pseudo=true")>=0;function qi(e,t){let n;return t.length===0?n=e:n=e.replace(/\{(\d+)\}/g,(r,i)=>{let s=i[0],o=t[s],l=r;return typeof o=="string"?l=o:(typeof o=="number"||typeof o=="boolean"||o===void 0||o===null)&&(l=String(o)),l}),Vi&&(n="\uFF3B"+n.replace(/[aouei]/g,"$&$&")+"\uFF3D"),n}function W(e,t,...n){return qi(t,n)}var Rn,Ge="en",Dt=!1,Pt=!1,kt=!1,Wi=!1,zi=!1,Ar=!1,Oi=!1,Hi=!1,$i=!1,Gi=!1,Ft,yn=Ge,Cr=Ge,ji,le,be=globalThis,K;typeof be.vscode<"u"&&typeof be.vscode.process<"u"?K=be.vscode.process:typeof process<"u"&&(K=process);var Rr=typeof((Rn=K?.versions)===null||Rn===void 0?void 0:Rn.electron)=="string",Qi=Rr&&K?.type==="renderer";if(typeof navigator=="object"&&!Qi)le=navigator.userAgent,Dt=le.indexOf("Windows")>=0,Pt=le.indexOf("Macintosh")>=0,Hi=(le.indexOf("Macintosh")>=0||le.indexOf("iPad")>=0||le.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,kt=le.indexOf("Linux")>=0,Gi=le?.indexOf("Mobi")>=0,Ar=!0,Ft=(W({key:"ensureLoaderPluginIsLoaded",comment:["{Locked}"]},"_"),void 0)||Ge,yn=Ft,Cr=navigator.language;else if(typeof K=="object"){Dt=K.platform==="win32",Pt=K.platform==="darwin",kt=K.platform==="linux",Wi=kt&&!!K.env.SNAP&&!!K.env.SNAP_REVISION,Oi=Rr,$i=!!K.env.CI||!!K.env.BUILD_ARTIFACTSTAGINGDIRECTORY,Ft=Ge,yn=Ge;let e=K.env.VSCODE_NLS_CONFIG;if(e)try{let t=JSON.parse(e),n=t.availableLanguages["*"];Ft=t.locale,Cr=t.osLocale,yn=n||Ge,ji=t._translationsConfigFile}catch{}zi=!0}else console.error("Unable to resolve platform.");var En=0;Pt?En=1:Dt?En=3:kt&&(En=2);var Ie=Dt,yr=Pt;var Ji=Ar&&typeof be.importScripts=="function",Lo=Ji?be.origin:void 0;var fe=le;var Xi=typeof be.postMessage=="function"&&!be.importScripts,wo=(()=>{if(Xi){let e=[];be.addEventListener("message",n=>{if(n.data&&n.data.vscodeScheduleAsyncWork)for(let r=0,i=e.length;r<i;r++){let s=e[r];if(s.id===n.data.vscodeScheduleAsyncWork){e.splice(r,1),s.callback();return}}});let t=0;return n=>{let r=++t;e.push({id:r,callback:n}),be.postMessage({vscodeScheduleAsyncWork:r},"*")}}return e=>setTimeout(e)})();var Yi=!!(fe&&fe.indexOf("Chrome")>=0),No=!!(fe&&fe.indexOf("Firefox")>=0),So=!!(!Yi&&fe&&fe.indexOf("Safari")>=0),Co=!!(fe&&fe.indexOf("Edg/")>=0),Ao=!!(fe&&fe.indexOf("Android")>=0);var It=class{constructor(t){this.fn=t,this.lastCache=void 0,this.lastArgKey=void 0}get(t){let n=JSON.stringify(t);return this.lastArgKey!==n&&(this.lastArgKey=n,this.lastCache=this.fn(t)),this.lastCache}};var ft=class{constructor(t){this.executor=t,this._didRun=!1}get value(){if(!this._didRun)try{this._value=this.executor()}catch(t){this._error=t}finally{this._didRun=!0}if(this._error)throw this._error;return this._value}get rawValue(){return this._value}};var je;function Er(e){return e.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}function Mr(e){return e.split(/\r\n|\r|\n/)}function Fr(e){for(let t=0,n=e.length;t<n;t++){let r=e.charCodeAt(t);if(r!==32&&r!==9)return t}return-1}function kr(e,t=e.length-1){for(let n=t;n>=0;n--){let r=e.charCodeAt(n);if(r!==32&&r!==9)return n}return-1}function Fn(e){return e>=65&&e<=90}function Qe(e){return 55296<=e&&e<=56319}function Tt(e){return 56320<=e&&e<=57343}function kn(e,t){return(e-55296<<10)+(t-56320)+65536}function Dr(e,t,n){let r=e.charCodeAt(n);if(Qe(r)&&n+1<t){let i=e.charCodeAt(n+1);if(Tt(i))return kn(r,i)}return r}var Zi=/^[\t\n\r\x20-\x7E]*$/;function Pr(e){return Zi.test(e)}var Mn=class e{static getInstance(){return e._INSTANCE||(e._INSTANCE=new e),e._INSTANCE}constructor(){this._data=Ki()}getGraphemeBreakType(t){if(t<32)return t===10?3:t===13?2:4;if(t<127)return 0;let n=this._data,r=n.length/3,i=1;for(;i<=r;)if(t<n[3*i])i=2*i;else if(t>n[3*i+1])i=2*i+1;else return n[3*i+2];return 0}};Mn._INSTANCE=null;function Ki(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}var de=class{static getInstance(t){return je.cache.get(Array.from(t))}static getLocales(){return je._locales.value}constructor(t){this.confusableDictionary=t}isAmbiguous(t){return this.confusableDictionary.has(t)}getPrimaryConfusable(t){return this.confusableDictionary.get(t)}getConfusableCodePoints(){return new Set(this.confusableDictionary.keys())}};je=de;de.ambiguousCharacterData=new ft(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,8218,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,8242,96,1370,96,1523,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71922,67,71913,67,65315,67,8557,67,8450,67,8493,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71919,87,71910,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,66293,90,71909,90,65338,90,8484,90,8488,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65297,49,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"cs":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"es":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"fr":[65374,126,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"it":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ja":[8211,45,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65292,44,65307,59],"ko":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pt-BR":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ru":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"zh-hans":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41],"zh-hant":[8211,45,65374,126,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65307,59]}'));de.cache=new It(e=>{function t(c){let f=new Map;for(let h=0;h<c.length;h+=2)f.set(c[h],c[h+1]);return f}function n(c,f){let h=new Map(c);for(let[d,g]of f)h.set(d,g);return h}function r(c,f){if(!c)return f;let h=new Map;for(let[d,g]of c)f.has(d)&&h.set(d,g);return h}let i=je.ambiguousCharacterData.value,s=e.filter(c=>!c.startsWith("_")&&c in i);s.length===0&&(s=["_default"]);let o;for(let c of s){let f=t(i[c]);o=r(o,f)}let l=t(i._common),u=n(l,o);return new je(u)});de._locales=new ft(()=>Object.keys(je.ambiguousCharacterData.value).filter(e=>!e.startsWith("_")));var Te=class e{static getRawData(){return JSON.parse("[9,10,11,12,13,32,127,160,173,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8203,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12288,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999]")}static getData(){return this._data||(this._data=new Set(e.getRawData())),this._data}static isInvisibleCharacter(t){return e.getData().has(t)}static get codePoints(){return e.getData()}};Te._data=void 0;var es="$initialize";var Dn=class{constructor(t,n,r,i){this.vsWorker=t,this.req=n,this.method=r,this.args=i,this.type=0}},Bt=class{constructor(t,n,r,i){this.vsWorker=t,this.seq=n,this.res=r,this.err=i,this.type=1}},Pn=class{constructor(t,n,r,i){this.vsWorker=t,this.req=n,this.eventName=r,this.arg=i,this.type=2}},In=class{constructor(t,n,r){this.vsWorker=t,this.req=n,this.event=r,this.type=3}},Tn=class{constructor(t,n){this.vsWorker=t,this.req=n,this.type=4}},Bn=class{constructor(t){this._workerId=-1,this._handler=t,this._lastSentReq=0,this._pendingReplies=Object.create(null),this._pendingEmitters=new Map,this._pendingEvents=new Map}setWorkerId(t){this._workerId=t}sendMessage(t,n){let r=String(++this._lastSentReq);return new Promise((i,s)=>{this._pendingReplies[r]={resolve:i,reject:s},this._send(new Dn(this._workerId,r,t,n))})}listen(t,n){let r=null,i=new Z({onWillAddFirstListener:()=>{r=String(++this._lastSentReq),this._pendingEmitters.set(r,i),this._send(new Pn(this._workerId,r,t,n))},onDidRemoveLastListener:()=>{this._pendingEmitters.delete(r),this._send(new Tn(this._workerId,r)),r=null}});return i.event}handleMessage(t){!t||!t.vsWorker||this._workerId!==-1&&t.vsWorker!==this._workerId||this._handleMessage(t)}_handleMessage(t){switch(t.type){case 1:return this._handleReplyMessage(t);case 0:return this._handleRequestMessage(t);case 2:return this._handleSubscribeEventMessage(t);case 3:return this._handleEventMessage(t);case 4:return this._handleUnsubscribeEventMessage(t)}}_handleReplyMessage(t){if(!this._pendingReplies[t.seq]){console.warn("Got reply to unknown seq");return}let n=this._pendingReplies[t.seq];if(delete this._pendingReplies[t.seq],t.err){let r=t.err;t.err.$isError&&(r=new Error,r.name=t.err.name,r.message=t.err.message,r.stack=t.err.stack),n.reject(r);return}n.resolve(t.res)}_handleRequestMessage(t){let n=t.req;this._handler.handleMessage(t.method,t.args).then(i=>{this._send(new Bt(this._workerId,n,i,void 0))},i=>{i.detail instanceof Error&&(i.detail=vn(i.detail)),this._send(new Bt(this._workerId,n,void 0,vn(i)))})}_handleSubscribeEventMessage(t){let n=t.req,r=this._handler.handleEvent(t.eventName,t.arg)(i=>{this._send(new In(this._workerId,n,i))});this._pendingEvents.set(n,r)}_handleEventMessage(t){if(!this._pendingEmitters.has(t.req)){console.warn("Got event for unknown req");return}this._pendingEmitters.get(t.req).fire(t.event)}_handleUnsubscribeEventMessage(t){if(!this._pendingEvents.has(t.req)){console.warn("Got unsubscribe for unknown req");return}this._pendingEvents.get(t.req).dispose(),this._pendingEvents.delete(t.req)}_send(t){let n=[];if(t.type===0)for(let r=0;r<t.args.length;r++)t.args[r]instanceof ArrayBuffer&&n.push(t.args[r]);else t.type===1&&t.res instanceof ArrayBuffer&&n.push(t.res);this._handler.sendMessage(t,n)}};function Ir(e){return e[0]==="o"&&e[1]==="n"&&Fn(e.charCodeAt(2))}function Tr(e){return/^onDynamic/.test(e)&&Fn(e.charCodeAt(9))}function ts(e,t,n){let r=o=>function(){let l=Array.prototype.slice.call(arguments,0);return t(o,l)},i=o=>function(l){return n(o,l)},s={};for(let o of e){if(Tr(o)){s[o]=i(o);continue}if(Ir(o)){s[o]=n(o,void 0);continue}s[o]=r(o)}return s}var Vt=class{constructor(t,n){this._requestHandlerFactory=n,this._requestHandler=null,this._protocol=new Bn({sendMessage:(r,i)=>{t(r,i)},handleMessage:(r,i)=>this._handleMessage(r,i),handleEvent:(r,i)=>this._handleEvent(r,i)})}onmessage(t){this._protocol.handleMessage(t)}_handleMessage(t,n){if(t===es)return this.initialize(n[0],n[1],n[2],n[3]);if(!this._requestHandler||typeof this._requestHandler[t]!="function")return Promise.reject(new Error("Missing requestHandler or method: "+t));try{return Promise.resolve(this._requestHandler[t].apply(this._requestHandler,n))}catch(r){return Promise.reject(r)}}_handleEvent(t,n){if(!this._requestHandler)throw new Error("Missing requestHandler");if(Tr(t)){let r=this._requestHandler[t].call(this._requestHandler,n);if(typeof r!="function")throw new Error(`Missing dynamic event ${t} on request handler.`);return r}if(Ir(t)){let r=this._requestHandler[t];if(typeof r!="function")throw new Error(`Missing event ${t} on request handler.`);return r}throw new Error(`Malformed event name ${t}`)}initialize(t,n,r,i){this._protocol.setWorkerId(t);let l=ts(i,(u,c)=>this._protocol.sendMessage(u,c),(u,c)=>this._protocol.listen(u,c));return this._requestHandlerFactory?(this._requestHandler=this._requestHandlerFactory(l),Promise.resolve(ht(this._requestHandler))):(n&&(typeof n.baseUrl<"u"&&delete n.baseUrl,typeof n.paths<"u"&&typeof n.paths.vs<"u"&&delete n.paths.vs,typeof n.trustedTypesPolicy!==void 0&&delete n.trustedTypesPolicy,n.catchError=!0,globalThis.require.config(n)),new Promise((u,c)=>{let f=globalThis.require;f([r],h=>{if(this._requestHandler=h.create(l),!this._requestHandler){c(new Error("No RequestHandler!"));return}u(ht(this._requestHandler))},c)}))}};var ue=class{constructor(t,n,r,i){this.originalStart=t,this.originalLength=n,this.modifiedStart=r,this.modifiedLength=i}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}};function Br(e,t){return(t<<5)-t+e|0}function qr(e,t){t=Br(149417,t);for(let n=0,r=e.length;n<r;n++)t=Br(e.charCodeAt(n),t);return t}function Vn(e,t,n=32){let r=n-t,i=~((1<<r)-1);return(e<<t|(i&e)>>>r)>>>0}function Vr(e,t=0,n=e.byteLength,r=0){for(let i=0;i<n;i++)e[t+i]=r}function ns(e,t,n="0"){for(;e.length<t;)e=n+e;return e}function mt(e,t=32){return e instanceof ArrayBuffer?Array.from(new Uint8Array(e)).map(n=>n.toString(16).padStart(2,"0")).join(""):ns((e>>>0).toString(16),t/4)}var qn=class e{constructor(){this._h0=1732584193,this._h1=4023233417,this._h2=2562383102,this._h3=271733878,this._h4=3285377520,this._buff=new Uint8Array(67),this._buffDV=new DataView(this._buff.buffer),this._buffLen=0,this._totalLen=0,this._leftoverHighSurrogate=0,this._finished=!1}update(t){let n=t.length;if(n===0)return;let r=this._buff,i=this._buffLen,s=this._leftoverHighSurrogate,o,l;for(s!==0?(o=s,l=-1,s=0):(o=t.charCodeAt(0),l=0);;){let u=o;if(Qe(o))if(l+1<n){let c=t.charCodeAt(l+1);Tt(c)?(l++,u=kn(o,c)):u=65533}else{s=o;break}else Tt(o)&&(u=65533);if(i=this._push(r,i,u),l++,l<n)o=t.charCodeAt(l);else break}this._buffLen=i,this._leftoverHighSurrogate=s}_push(t,n,r){return r<128?t[n++]=r:r<2048?(t[n++]=192|(r&1984)>>>6,t[n++]=128|(r&63)>>>0):r<65536?(t[n++]=224|(r&61440)>>>12,t[n++]=128|(r&4032)>>>6,t[n++]=128|(r&63)>>>0):(t[n++]=240|(r&1835008)>>>18,t[n++]=128|(r&258048)>>>12,t[n++]=128|(r&4032)>>>6,t[n++]=128|(r&63)>>>0),n>=64&&(this._step(),n-=64,this._totalLen+=64,t[0]=t[64],t[1]=t[65],t[2]=t[66]),n}digest(){return this._finished||(this._finished=!0,this._leftoverHighSurrogate&&(this._leftoverHighSurrogate=0,this._buffLen=this._push(this._buff,this._buffLen,65533)),this._totalLen+=this._buffLen,this._wrapUp()),mt(this._h0)+mt(this._h1)+mt(this._h2)+mt(this._h3)+mt(this._h4)}_wrapUp(){this._buff[this._buffLen++]=128,Vr(this._buff,this._buffLen),this._buffLen>56&&(this._step(),Vr(this._buff));let t=8*this._totalLen;this._buffDV.setUint32(56,Math.floor(t/4294967296),!1),this._buffDV.setUint32(60,t%4294967296,!1),this._step()}_step(){let t=e._bigBlock32,n=this._buffDV;for(let h=0;h<64;h+=4)t.setUint32(h,n.getUint32(h,!1),!1);for(let h=64;h<320;h+=4)t.setUint32(h,Vn(t.getUint32(h-12,!1)^t.getUint32(h-32,!1)^t.getUint32(h-56,!1)^t.getUint32(h-64,!1),1),!1);let r=this._h0,i=this._h1,s=this._h2,o=this._h3,l=this._h4,u,c,f;for(let h=0;h<80;h++)h<20?(u=i&s|~i&o,c=1518500249):h<40?(u=i^s^o,c=1859775393):h<60?(u=i&s|i&o|s&o,c=2400959708):(u=i^s^o,c=3395469782),f=Vn(r,5)+u+l+c+t.getUint32(h*4,!1)&4294967295,l=o,o=s,s=Vn(i,30),i=r,r=f;this._h0=this._h0+r&4294967295,this._h1=this._h1+i&4294967295,this._h2=this._h2+s&4294967295,this._h3=this._h3+o&4294967295,this._h4=this._h4+l&4294967295}};qn._bigBlock32=new DataView(new ArrayBuffer(320));var qt=class{constructor(t){this.source=t}getElements(){let t=this.source,n=new Int32Array(t.length);for(let r=0,i=t.length;r<i;r++)n[r]=t.charCodeAt(r);return n}};function Ur(e,t,n){return new gt(new qt(e),new qt(t)).ComputeDiff(n).changes}var ve=class{static Assert(t,n){if(!t)throw new Error(n)}},Le=class{static Copy(t,n,r,i,s){for(let o=0;o<s;o++)r[i+o]=t[n+o]}static Copy2(t,n,r,i,s){for(let o=0;o<s;o++)r[i+o]=t[n+o]}},Ut=class{constructor(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}MarkNextChange(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new ue(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}AddOriginalElement(t,n){this.m_originalStart=Math.min(this.m_originalStart,t),this.m_modifiedStart=Math.min(this.m_modifiedStart,n),this.m_originalCount++}AddModifiedElement(t,n){this.m_originalStart=Math.min(this.m_originalStart,t),this.m_modifiedStart=Math.min(this.m_modifiedStart,n),this.m_modifiedCount++}getChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}getReverseChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}},gt=class e{constructor(t,n,r=null){this.ContinueProcessingPredicate=r,this._originalSequence=t,this._modifiedSequence=n;let[i,s,o]=e._getElements(t),[l,u,c]=e._getElements(n);this._hasStrings=o&&c,this._originalStringElements=i,this._originalElementsOrHash=s,this._modifiedStringElements=l,this._modifiedElementsOrHash=u,this.m_forwardHistory=[],this.m_reverseHistory=[]}static _isStringArray(t){return t.length>0&&typeof t[0]=="string"}static _getElements(t){let n=t.getElements();if(e._isStringArray(n)){let r=new Int32Array(n.length);for(let i=0,s=n.length;i<s;i++)r[i]=qr(n[i],0);return[n,r,!0]}return n instanceof Int32Array?[[],n,!1]:[[],new Int32Array(n),!1]}ElementsAreEqual(t,n){return this._originalElementsOrHash[t]!==this._modifiedElementsOrHash[n]?!1:this._hasStrings?this._originalStringElements[t]===this._modifiedStringElements[n]:!0}ElementsAreStrictEqual(t,n){if(!this.ElementsAreEqual(t,n))return!1;let r=e._getStrictElement(this._originalSequence,t),i=e._getStrictElement(this._modifiedSequence,n);return r===i}static _getStrictElement(t,n){return typeof t.getStrictElement=="function"?t.getStrictElement(n):null}OriginalElementsAreEqual(t,n){return this._originalElementsOrHash[t]!==this._originalElementsOrHash[n]?!1:this._hasStrings?this._originalStringElements[t]===this._originalStringElements[n]:!0}ModifiedElementsAreEqual(t,n){return this._modifiedElementsOrHash[t]!==this._modifiedElementsOrHash[n]?!1:this._hasStrings?this._modifiedStringElements[t]===this._modifiedStringElements[n]:!0}ComputeDiff(t){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,t)}_ComputeDiff(t,n,r,i,s){let o=[!1],l=this.ComputeDiffRecursive(t,n,r,i,o);return s&&(l=this.PrettifyChanges(l)),{quitEarly:o[0],changes:l}}ComputeDiffRecursive(t,n,r,i,s){for(s[0]=!1;t<=n&&r<=i&&this.ElementsAreEqual(t,r);)t++,r++;for(;n>=t&&i>=r&&this.ElementsAreEqual(n,i);)n--,i--;if(t>n||r>i){let h;return r<=i?(ve.Assert(t===n+1,"originalStart should only be one more than originalEnd"),h=[new ue(t,0,r,i-r+1)]):t<=n?(ve.Assert(r===i+1,"modifiedStart should only be one more than modifiedEnd"),h=[new ue(t,n-t+1,r,0)]):(ve.Assert(t===n+1,"originalStart should only be one more than originalEnd"),ve.Assert(r===i+1,"modifiedStart should only be one more than modifiedEnd"),h=[]),h}let o=[0],l=[0],u=this.ComputeRecursionPoint(t,n,r,i,o,l,s),c=o[0],f=l[0];if(u!==null)return u;if(!s[0]){let h=this.ComputeDiffRecursive(t,c,r,f,s),d=[];return s[0]?d=[new ue(c+1,n-(c+1)+1,f+1,i-(f+1)+1)]:d=this.ComputeDiffRecursive(c+1,n,f+1,i,s),this.ConcatenateChanges(h,d)}return[new ue(t,n-t+1,r,i-r+1)]}WALKTRACE(t,n,r,i,s,o,l,u,c,f,h,d,g,p,m,_,N,b){let x=null,v=null,w=new Ut,R=n,y=r,M=g[0]-_[0]-i,U=-1073741824,j=this.m_forwardHistory.length-1;do{let B=M+t;B===R||B<y&&c[B-1]<c[B+1]?(h=c[B+1],p=h-M-i,h<U&&w.MarkNextChange(),U=h,w.AddModifiedElement(h+1,p),M=B+1-t):(h=c[B-1]+1,p=h-M-i,h<U&&w.MarkNextChange(),U=h-1,w.AddOriginalElement(h,p+1),M=B-1-t),j>=0&&(c=this.m_forwardHistory[j],t=c[0],R=1,y=c.length-1)}while(--j>=-1);if(x=w.getReverseChanges(),b[0]){let B=g[0]+1,k=_[0]+1;if(x!==null&&x.length>0){let A=x[x.length-1];B=Math.max(B,A.getOriginalEnd()),k=Math.max(k,A.getModifiedEnd())}v=[new ue(B,d-B+1,k,m-k+1)]}else{w=new Ut,R=o,y=l,M=g[0]-_[0]-u,U=1073741824,j=N?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{let B=M+s;B===R||B<y&&f[B-1]>=f[B+1]?(h=f[B+1]-1,p=h-M-u,h>U&&w.MarkNextChange(),U=h+1,w.AddOriginalElement(h+1,p+1),M=B+1-s):(h=f[B-1],p=h-M-u,h>U&&w.MarkNextChange(),U=h,w.AddModifiedElement(h+1,p+1),M=B-1-s),j>=0&&(f=this.m_reverseHistory[j],s=f[0],R=1,y=f.length-1)}while(--j>=-1);v=w.getChanges()}return this.ConcatenateChanges(x,v)}ComputeRecursionPoint(t,n,r,i,s,o,l){let u=0,c=0,f=0,h=0,d=0,g=0;t--,r--,s[0]=0,o[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];let p=n-t+(i-r),m=p+1,_=new Int32Array(m),N=new Int32Array(m),b=i-r,x=n-t,v=t-r,w=n-i,y=(x-b)%2===0;_[b]=t,N[x]=n,l[0]=!1;for(let M=1;M<=p/2+1;M++){let U=0,j=0;f=this.ClipDiagonalBound(b-M,M,b,m),h=this.ClipDiagonalBound(b+M,M,b,m);for(let k=f;k<=h;k+=2){k===f||k<h&&_[k-1]<_[k+1]?u=_[k+1]:u=_[k-1]+1,c=u-(k-b)-v;let A=u;for(;u<n&&c<i&&this.ElementsAreEqual(u+1,c+1);)u++,c++;if(_[k]=u,u+c>U+j&&(U=u,j=c),!y&&Math.abs(k-x)<=M-1&&u>=N[k])return s[0]=u,o[0]=c,A<=N[k]&&M<=1448?this.WALKTRACE(b,f,h,v,x,d,g,w,_,N,u,n,s,c,i,o,y,l):null}let B=(U-t+(j-r)-M)/2;if(this.ContinueProcessingPredicate!==null&&!this.ContinueProcessingPredicate(U,B))return l[0]=!0,s[0]=U,o[0]=j,B>0&&M<=1448?this.WALKTRACE(b,f,h,v,x,d,g,w,_,N,u,n,s,c,i,o,y,l):(t++,r++,[new ue(t,n-t+1,r,i-r+1)]);d=this.ClipDiagonalBound(x-M,M,x,m),g=this.ClipDiagonalBound(x+M,M,x,m);for(let k=d;k<=g;k+=2){k===d||k<g&&N[k-1]>=N[k+1]?u=N[k+1]-1:u=N[k-1],c=u-(k-x)-w;let A=u;for(;u>t&&c>r&&this.ElementsAreEqual(u,c);)u--,c--;if(N[k]=u,y&&Math.abs(k-b)<=M&&u<=_[k])return s[0]=u,o[0]=c,A>=_[k]&&M<=1448?this.WALKTRACE(b,f,h,v,x,d,g,w,_,N,u,n,s,c,i,o,y,l):null}if(M<=1447){let k=new Int32Array(h-f+2);k[0]=b-f+1,Le.Copy2(_,f,k,1,h-f+1),this.m_forwardHistory.push(k),k=new Int32Array(g-d+2),k[0]=x-d+1,Le.Copy2(N,d,k,1,g-d+1),this.m_reverseHistory.push(k)}}return this.WALKTRACE(b,f,h,v,x,d,g,w,_,N,u,n,s,c,i,o,y,l)}PrettifyChanges(t){for(let n=0;n<t.length;n++){let r=t[n],i=n<t.length-1?t[n+1].originalStart:this._originalElementsOrHash.length,s=n<t.length-1?t[n+1].modifiedStart:this._modifiedElementsOrHash.length,o=r.originalLength>0,l=r.modifiedLength>0;for(;r.originalStart+r.originalLength<i&&r.modifiedStart+r.modifiedLength<s&&(!o||this.OriginalElementsAreEqual(r.originalStart,r.originalStart+r.originalLength))&&(!l||this.ModifiedElementsAreEqual(r.modifiedStart,r.modifiedStart+r.modifiedLength));){let c=this.ElementsAreStrictEqual(r.originalStart,r.modifiedStart);if(this.ElementsAreStrictEqual(r.originalStart+r.originalLength,r.modifiedStart+r.modifiedLength)&&!c)break;r.originalStart++,r.modifiedStart++}let u=[null];if(n<t.length-1&&this.ChangesOverlap(t[n],t[n+1],u)){t[n]=u[0],t.splice(n+1,1),n--;continue}}for(let n=t.length-1;n>=0;n--){let r=t[n],i=0,s=0;if(n>0){let h=t[n-1];i=h.originalStart+h.originalLength,s=h.modifiedStart+h.modifiedLength}let o=r.originalLength>0,l=r.modifiedLength>0,u=0,c=this._boundaryScore(r.originalStart,r.originalLength,r.modifiedStart,r.modifiedLength);for(let h=1;;h++){let d=r.originalStart-h,g=r.modifiedStart-h;if(d<i||g<s||o&&!this.OriginalElementsAreEqual(d,d+r.originalLength)||l&&!this.ModifiedElementsAreEqual(g,g+r.modifiedLength))break;let m=(d===i&&g===s?5:0)+this._boundaryScore(d,r.originalLength,g,r.modifiedLength);m>c&&(c=m,u=h)}r.originalStart-=u,r.modifiedStart-=u;let f=[null];if(n>0&&this.ChangesOverlap(t[n-1],t[n],f)){t[n-1]=f[0],t.splice(n,1),n++;continue}}if(this._hasStrings)for(let n=1,r=t.length;n<r;n++){let i=t[n-1],s=t[n],o=s.originalStart-i.originalStart-i.originalLength,l=i.originalStart,u=s.originalStart+s.originalLength,c=u-l,f=i.modifiedStart,h=s.modifiedStart+s.modifiedLength,d=h-f;if(o<5&&c<20&&d<20){let g=this._findBetterContiguousSequence(l,c,f,d,o);if(g){let[p,m]=g;(p!==i.originalStart+i.originalLength||m!==i.modifiedStart+i.modifiedLength)&&(i.originalLength=p-i.originalStart,i.modifiedLength=m-i.modifiedStart,s.originalStart=p+o,s.modifiedStart=m+o,s.originalLength=u-s.originalStart,s.modifiedLength=h-s.modifiedStart)}}}return t}_findBetterContiguousSequence(t,n,r,i,s){if(n<s||i<s)return null;let o=t+n-s+1,l=r+i-s+1,u=0,c=0,f=0;for(let h=t;h<o;h++)for(let d=r;d<l;d++){let g=this._contiguousSequenceScore(h,d,s);g>0&&g>u&&(u=g,c=h,f=d)}return u>0?[c,f]:null}_contiguousSequenceScore(t,n,r){let i=0;for(let s=0;s<r;s++){if(!this.ElementsAreEqual(t+s,n+s))return 0;i+=this._originalStringElements[t+s].length}return i}_OriginalIsBoundary(t){return t<=0||t>=this._originalElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._originalStringElements[t])}_OriginalRegionIsBoundary(t,n){if(this._OriginalIsBoundary(t)||this._OriginalIsBoundary(t-1))return!0;if(n>0){let r=t+n;if(this._OriginalIsBoundary(r-1)||this._OriginalIsBoundary(r))return!0}return!1}_ModifiedIsBoundary(t){return t<=0||t>=this._modifiedElementsOrHash.length-1?!0:this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[t])}_ModifiedRegionIsBoundary(t,n){if(this._ModifiedIsBoundary(t)||this._ModifiedIsBoundary(t-1))return!0;if(n>0){let r=t+n;if(this._ModifiedIsBoundary(r-1)||this._ModifiedIsBoundary(r))return!0}return!1}_boundaryScore(t,n,r,i){let s=this._OriginalRegionIsBoundary(t,n)?1:0,o=this._ModifiedRegionIsBoundary(r,i)?1:0;return s+o}ConcatenateChanges(t,n){let r=[];if(t.length===0||n.length===0)return n.length>0?n:t;if(this.ChangesOverlap(t[t.length-1],n[0],r)){let i=new Array(t.length+n.length-1);return Le.Copy(t,0,i,0,t.length-1),i[t.length-1]=r[0],Le.Copy(n,1,i,t.length,n.length-1),i}else{let i=new Array(t.length+n.length);return Le.Copy(t,0,i,0,t.length),Le.Copy(n,0,i,t.length,n.length),i}}ChangesOverlap(t,n,r){if(ve.Assert(t.originalStart<=n.originalStart,"Left change is not less than or equal to right change"),ve.Assert(t.modifiedStart<=n.modifiedStart,"Left change is not less than or equal to right change"),t.originalStart+t.originalLength>=n.originalStart||t.modifiedStart+t.modifiedLength>=n.modifiedStart){let i=t.originalStart,s=t.originalLength,o=t.modifiedStart,l=t.modifiedLength;return t.originalStart+t.originalLength>=n.originalStart&&(s=n.originalStart+n.originalLength-t.originalStart),t.modifiedStart+t.modifiedLength>=n.modifiedStart&&(l=n.modifiedStart+n.modifiedLength-t.modifiedStart),r[0]=new ue(i,s,o,l),!0}else return r[0]=null,!1}ClipDiagonalBound(t,n,r,i){if(t>=0&&t<i)return t;let s=r,o=i-r-1,l=n%2===0;if(t<0){let u=s%2===0;return l===u?0:1}else{let u=o%2===0;return l===u?i-1:i-2}}};var Je,Un=globalThis.vscode;if(typeof Un<"u"&&typeof Un.process<"u"){let e=Un.process;Je={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof process<"u"?Je={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:Je={get platform(){return Ie?"win32":yr?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};var pt=Je.cwd,Wr=Je.env,zr=Je.platform;var is=65,ss=97,os=90,as=122,Se=46,Y=47,re=92,we=58,ls=63,Wt=class extends Error{constructor(t,n,r){let i;typeof n=="string"&&n.indexOf("not ")===0?(i="must not be",n=n.replace(/^not /,"")):i="must be";let s=t.indexOf(".")!==-1?"property":"argument",o=`The "${t}" ${s} ${i} of type ${n}`;o+=`. Received type ${typeof r}`,super(o),this.code="ERR_INVALID_ARG_TYPE"}};function us(e,t){if(e===null||typeof e!="object")throw new Wt(t,"Object",e)}function $(e,t){if(typeof e!="string")throw new Wt(t,"string",e)}var Ce=zr==="win32";function P(e){return e===Y||e===re}function Wn(e){return e===Y}function Ne(e){return e>=is&&e<=os||e>=ss&&e<=as}function zt(e,t,n,r){let i="",s=0,o=-1,l=0,u=0;for(let c=0;c<=e.length;++c){if(c<e.length)u=e.charCodeAt(c);else{if(r(u))break;u=Y}if(r(u)){if(!(o===c-1||l===1))if(l===2){if(i.length<2||s!==2||i.charCodeAt(i.length-1)!==Se||i.charCodeAt(i.length-2)!==Se){if(i.length>2){let f=i.lastIndexOf(n);f===-1?(i="",s=0):(i=i.slice(0,f),s=i.length-1-i.lastIndexOf(n)),o=c,l=0;continue}else if(i.length!==0){i="",s=0,o=c,l=0;continue}}t&&(i+=i.length>0?`${n}..`:"..",s=2)}else i.length>0?i+=`${n}${e.slice(o+1,c)}`:i=e.slice(o+1,c),s=c-o-1;o=c,l=0}else u===Se&&l!==-1?++l:l=-1}return i}function Or(e,t){us(t,"pathObject");let n=t.dir||t.root,r=t.base||`${t.name||""}${t.ext||""}`;return n?n===t.root?`${n}${r}`:`${n}${e}${r}`:r}var te={resolve(...e){let t="",n="",r=!1;for(let i=e.length-1;i>=-1;i--){let s;if(i>=0){if(s=e[i],$(s,"path"),s.length===0)continue}else t.length===0?s=pt():(s=Wr[`=${t}`]||pt(),(s===void 0||s.slice(0,2).toLowerCase()!==t.toLowerCase()&&s.charCodeAt(2)===re)&&(s=`${t}\\`));let o=s.length,l=0,u="",c=!1,f=s.charCodeAt(0);if(o===1)P(f)&&(l=1,c=!0);else if(P(f))if(c=!0,P(s.charCodeAt(1))){let h=2,d=h;for(;h<o&&!P(s.charCodeAt(h));)h++;if(h<o&&h!==d){let g=s.slice(d,h);for(d=h;h<o&&P(s.charCodeAt(h));)h++;if(h<o&&h!==d){for(d=h;h<o&&!P(s.charCodeAt(h));)h++;(h===o||h!==d)&&(u=`\\\\${g}\\${s.slice(d,h)}`,l=h)}}}else l=1;else Ne(f)&&s.charCodeAt(1)===we&&(u=s.slice(0,2),l=2,o>2&&P(s.charCodeAt(2))&&(c=!0,l=3));if(u.length>0)if(t.length>0){if(u.toLowerCase()!==t.toLowerCase())continue}else t=u;if(r){if(t.length>0)break}else if(n=`${s.slice(l)}\\${n}`,r=c,c&&t.length>0)break}return n=zt(n,!r,"\\",P),r?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){$(e,"path");let t=e.length;if(t===0)return".";let n=0,r,i=!1,s=e.charCodeAt(0);if(t===1)return Wn(s)?"\\":e;if(P(s))if(i=!0,P(e.charCodeAt(1))){let l=2,u=l;for(;l<t&&!P(e.charCodeAt(l));)l++;if(l<t&&l!==u){let c=e.slice(u,l);for(u=l;l<t&&P(e.charCodeAt(l));)l++;if(l<t&&l!==u){for(u=l;l<t&&!P(e.charCodeAt(l));)l++;if(l===t)return`\\\\${c}\\${e.slice(u)}\\`;l!==u&&(r=`\\\\${c}\\${e.slice(u,l)}`,n=l)}}}else n=1;else Ne(s)&&e.charCodeAt(1)===we&&(r=e.slice(0,2),n=2,t>2&&P(e.charCodeAt(2))&&(i=!0,n=3));let o=n<t?zt(e.slice(n),!i,"\\",P):"";return o.length===0&&!i&&(o="."),o.length>0&&P(e.charCodeAt(t-1))&&(o+="\\"),r===void 0?i?`\\${o}`:o:i?`${r}\\${o}`:`${r}${o}`},isAbsolute(e){$(e,"path");let t=e.length;if(t===0)return!1;let n=e.charCodeAt(0);return P(n)||t>2&&Ne(n)&&e.charCodeAt(1)===we&&P(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,n;for(let s=0;s<e.length;++s){let o=e[s];$(o,"path"),o.length>0&&(t===void 0?t=n=o:t+=`\\${o}`)}if(t===void 0)return".";let r=!0,i=0;if(typeof n=="string"&&P(n.charCodeAt(0))){++i;let s=n.length;s>1&&P(n.charCodeAt(1))&&(++i,s>2&&(P(n.charCodeAt(2))?++i:r=!1))}if(r){for(;i<t.length&&P(t.charCodeAt(i));)i++;i>=2&&(t=`\\${t.slice(i)}`)}return te.normalize(t)},relative(e,t){if($(e,"from"),$(t,"to"),e===t)return"";let n=te.resolve(e),r=te.resolve(t);if(n===r||(e=n.toLowerCase(),t=r.toLowerCase(),e===t))return"";let i=0;for(;i<e.length&&e.charCodeAt(i)===re;)i++;let s=e.length;for(;s-1>i&&e.charCodeAt(s-1)===re;)s--;let o=s-i,l=0;for(;l<t.length&&t.charCodeAt(l)===re;)l++;let u=t.length;for(;u-1>l&&t.charCodeAt(u-1)===re;)u--;let c=u-l,f=o<c?o:c,h=-1,d=0;for(;d<f;d++){let p=e.charCodeAt(i+d);if(p!==t.charCodeAt(l+d))break;p===re&&(h=d)}if(d!==f){if(h===-1)return r}else{if(c>f){if(t.charCodeAt(l+d)===re)return r.slice(l+d+1);if(d===2)return r.slice(l+d)}o>f&&(e.charCodeAt(i+d)===re?h=d:d===2&&(h=3)),h===-1&&(h=0)}let g="";for(d=i+h+1;d<=s;++d)(d===s||e.charCodeAt(d)===re)&&(g+=g.length===0?"..":"\\..");return l+=h,g.length>0?`${g}${r.slice(l,u)}`:(r.charCodeAt(l)===re&&++l,r.slice(l,u))},toNamespacedPath(e){if(typeof e!="string"||e.length===0)return e;let t=te.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===re){if(t.charCodeAt(1)===re){let n=t.charCodeAt(2);if(n!==ls&&n!==Se)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(Ne(t.charCodeAt(0))&&t.charCodeAt(1)===we&&t.charCodeAt(2)===re)return`\\\\?\\${t}`;return e},dirname(e){$(e,"path");let t=e.length;if(t===0)return".";let n=-1,r=0,i=e.charCodeAt(0);if(t===1)return P(i)?e:".";if(P(i)){if(n=r=1,P(e.charCodeAt(1))){let l=2,u=l;for(;l<t&&!P(e.charCodeAt(l));)l++;if(l<t&&l!==u){for(u=l;l<t&&P(e.charCodeAt(l));)l++;if(l<t&&l!==u){for(u=l;l<t&&!P(e.charCodeAt(l));)l++;if(l===t)return e;l!==u&&(n=r=l+1)}}}}else Ne(i)&&e.charCodeAt(1)===we&&(n=t>2&&P(e.charCodeAt(2))?3:2,r=n);let s=-1,o=!0;for(let l=t-1;l>=r;--l)if(P(e.charCodeAt(l))){if(!o){s=l;break}}else o=!1;if(s===-1){if(n===-1)return".";s=n}return e.slice(0,s)},basename(e,t){t!==void 0&&$(t,"ext"),$(e,"path");let n=0,r=-1,i=!0,s;if(e.length>=2&&Ne(e.charCodeAt(0))&&e.charCodeAt(1)===we&&(n=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,l=-1;for(s=e.length-1;s>=n;--s){let u=e.charCodeAt(s);if(P(u)){if(!i){n=s+1;break}}else l===-1&&(i=!1,l=s+1),o>=0&&(u===t.charCodeAt(o)?--o===-1&&(r=s):(o=-1,r=l))}return n===r?r=l:r===-1&&(r=e.length),e.slice(n,r)}for(s=e.length-1;s>=n;--s)if(P(e.charCodeAt(s))){if(!i){n=s+1;break}}else r===-1&&(i=!1,r=s+1);return r===-1?"":e.slice(n,r)},extname(e){$(e,"path");let t=0,n=-1,r=0,i=-1,s=!0,o=0;e.length>=2&&e.charCodeAt(1)===we&&Ne(e.charCodeAt(0))&&(t=r=2);for(let l=e.length-1;l>=t;--l){let u=e.charCodeAt(l);if(P(u)){if(!s){r=l+1;break}continue}i===-1&&(s=!1,i=l+1),u===Se?n===-1?n=l:o!==1&&(o=1):n!==-1&&(o=-1)}return n===-1||i===-1||o===0||o===1&&n===i-1&&n===r+1?"":e.slice(n,i)},format:Or.bind(null,"\\"),parse(e){$(e,"path");let t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;let n=e.length,r=0,i=e.charCodeAt(0);if(n===1)return P(i)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(P(i)){if(r=1,P(e.charCodeAt(1))){let h=2,d=h;for(;h<n&&!P(e.charCodeAt(h));)h++;if(h<n&&h!==d){for(d=h;h<n&&P(e.charCodeAt(h));)h++;if(h<n&&h!==d){for(d=h;h<n&&!P(e.charCodeAt(h));)h++;h===n?r=h:h!==d&&(r=h+1)}}}}else if(Ne(i)&&e.charCodeAt(1)===we){if(n<=2)return t.root=t.dir=e,t;if(r=2,P(e.charCodeAt(2))){if(n===3)return t.root=t.dir=e,t;r=3}}r>0&&(t.root=e.slice(0,r));let s=-1,o=r,l=-1,u=!0,c=e.length-1,f=0;for(;c>=r;--c){if(i=e.charCodeAt(c),P(i)){if(!u){o=c+1;break}continue}l===-1&&(u=!1,l=c+1),i===Se?s===-1?s=c:f!==1&&(f=1):s!==-1&&(f=-1)}return l!==-1&&(s===-1||f===0||f===1&&s===l-1&&s===o+1?t.base=t.name=e.slice(o,l):(t.name=e.slice(o,s),t.base=e.slice(o,l),t.ext=e.slice(s,l))),o>0&&o!==r?t.dir=e.slice(0,o-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},cs=(()=>{if(Ce){let e=/\\/g;return()=>{let t=pt().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>pt()})(),ne={resolve(...e){let t="",n=!1;for(let r=e.length-1;r>=-1&&!n;r--){let i=r>=0?e[r]:cs();$(i,"path"),i.length!==0&&(t=`${i}/${t}`,n=i.charCodeAt(0)===Y)}return t=zt(t,!n,"/",Wn),n?`/${t}`:t.length>0?t:"."},normalize(e){if($(e,"path"),e.length===0)return".";let t=e.charCodeAt(0)===Y,n=e.charCodeAt(e.length-1)===Y;return e=zt(e,!t,"/",Wn),e.length===0?t?"/":n?"./":".":(n&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return $(e,"path"),e.length>0&&e.charCodeAt(0)===Y},join(...e){if(e.length===0)return".";let t;for(let n=0;n<e.length;++n){let r=e[n];$(r,"path"),r.length>0&&(t===void 0?t=r:t+=`/${r}`)}return t===void 0?".":ne.normalize(t)},relative(e,t){if($(e,"from"),$(t,"to"),e===t||(e=ne.resolve(e),t=ne.resolve(t),e===t))return"";let n=1,r=e.length,i=r-n,s=1,o=t.length-s,l=i<o?i:o,u=-1,c=0;for(;c<l;c++){let h=e.charCodeAt(n+c);if(h!==t.charCodeAt(s+c))break;h===Y&&(u=c)}if(c===l)if(o>l){if(t.charCodeAt(s+c)===Y)return t.slice(s+c+1);if(c===0)return t.slice(s+c)}else i>l&&(e.charCodeAt(n+c)===Y?u=c:c===0&&(u=0));let f="";for(c=n+u+1;c<=r;++c)(c===r||e.charCodeAt(c)===Y)&&(f+=f.length===0?"..":"/..");return`${f}${t.slice(s+u)}`},toNamespacedPath(e){return e},dirname(e){if($(e,"path"),e.length===0)return".";let t=e.charCodeAt(0)===Y,n=-1,r=!0;for(let i=e.length-1;i>=1;--i)if(e.charCodeAt(i)===Y){if(!r){n=i;break}}else r=!1;return n===-1?t?"/":".":t&&n===1?"//":e.slice(0,n)},basename(e,t){t!==void 0&&$(t,"ext"),$(e,"path");let n=0,r=-1,i=!0,s;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,l=-1;for(s=e.length-1;s>=0;--s){let u=e.charCodeAt(s);if(u===Y){if(!i){n=s+1;break}}else l===-1&&(i=!1,l=s+1),o>=0&&(u===t.charCodeAt(o)?--o===-1&&(r=s):(o=-1,r=l))}return n===r?r=l:r===-1&&(r=e.length),e.slice(n,r)}for(s=e.length-1;s>=0;--s)if(e.charCodeAt(s)===Y){if(!i){n=s+1;break}}else r===-1&&(i=!1,r=s+1);return r===-1?"":e.slice(n,r)},extname(e){$(e,"path");let t=-1,n=0,r=-1,i=!0,s=0;for(let o=e.length-1;o>=0;--o){let l=e.charCodeAt(o);if(l===Y){if(!i){n=o+1;break}continue}r===-1&&(i=!1,r=o+1),l===Se?t===-1?t=o:s!==1&&(s=1):t!==-1&&(s=-1)}return t===-1||r===-1||s===0||s===1&&t===r-1&&t===n+1?"":e.slice(t,r)},format:Or.bind(null,"/"),parse(e){$(e,"path");let t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;let n=e.charCodeAt(0)===Y,r;n?(t.root="/",r=1):r=0;let i=-1,s=0,o=-1,l=!0,u=e.length-1,c=0;for(;u>=r;--u){let f=e.charCodeAt(u);if(f===Y){if(!l){s=u+1;break}continue}o===-1&&(l=!1,o=u+1),f===Se?i===-1?i=u:c!==1&&(c=1):i!==-1&&(c=-1)}if(o!==-1){let f=s===0&&n?1:s;i===-1||c===0||c===1&&i===o-1&&i===s+1?t.base=t.name=e.slice(f,o):(t.name=e.slice(f,i),t.base=e.slice(f,o),t.ext=e.slice(i,o))}return s>0?t.dir=e.slice(0,s-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};ne.win32=te.win32=te;ne.posix=te.posix=ne;var $o=Ce?te.normalize:ne.normalize,Go=Ce?te.resolve:ne.resolve,jo=Ce?te.relative:ne.relative,Qo=Ce?te.dirname:ne.dirname,Jo=Ce?te.basename:ne.basename,Xo=Ce?te.extname:ne.extname,Yo=Ce?te.sep:ne.sep;var fs=/^\w[\w\d+.-]*$/,ds=/^\//,ms=/^\/\//;function gs(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!fs.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!ds.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(ms.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function ps(e,t){return!e&&!t?"file":e}function bs(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==ce&&(t=ce+t):t=ce;break}return t}var O="",ce="/",xs=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,Re=class e{static isUri(t){return t instanceof e?!0:t?typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function":!1}constructor(t,n,r,i,s,o=!1){typeof t=="object"?(this.scheme=t.scheme||O,this.authority=t.authority||O,this.path=t.path||O,this.query=t.query||O,this.fragment=t.fragment||O):(this.scheme=ps(t,o),this.authority=n||O,this.path=bs(this.scheme,r||O),this.query=i||O,this.fragment=s||O,gs(this,o))}get fsPath(){return zn(this,!1)}with(t){if(!t)return this;let{scheme:n,authority:r,path:i,query:s,fragment:o}=t;return n===void 0?n=this.scheme:n===null&&(n=O),r===void 0?r=this.authority:r===null&&(r=O),i===void 0?i=this.path:i===null&&(i=O),s===void 0?s=this.query:s===null&&(s=O),o===void 0?o=this.fragment:o===null&&(o=O),n===this.scheme&&r===this.authority&&i===this.path&&s===this.query&&o===this.fragment?this:new Ae(n,r,i,s,o)}static parse(t,n=!1){let r=xs.exec(t);return r?new Ae(r[2]||O,Ot(r[4]||O),Ot(r[5]||O),Ot(r[7]||O),Ot(r[9]||O),n):new Ae(O,O,O,O,O)}static file(t){let n=O;if(Ie&&(t=t.replace(/\\/g,ce)),t[0]===ce&&t[1]===ce){let r=t.indexOf(ce,2);r===-1?(n=t.substring(2),t=ce):(n=t.substring(2,r),t=t.substring(r)||ce)}return new Ae("file",n,t,O,O)}static from(t,n){return new Ae(t.scheme,t.authority,t.path,t.query,t.fragment,n)}static joinPath(t,...n){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let r;return Ie&&t.scheme==="file"?r=e.file(te.join(zn(t,!0),...n)).path:r=ne.join(t.path,...n),t.with({path:r})}toString(t=!1){return On(this,t)}toJSON(){return this}static revive(t){var n,r;if(t){if(t instanceof e)return t;{let i=new Ae(t);return i._formatted=(n=t.external)!==null&&n!==void 0?n:null,i._fsPath=t._sep===Gr&&(r=t.fsPath)!==null&&r!==void 0?r:null,i}}else return t}},Gr=Ie?1:void 0,Ae=class extends Re{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=zn(this,!1)),this._fsPath}toString(t=!1){return t?On(this,!0):(this._formatted||(this._formatted=On(this,!1)),this._formatted)}toJSON(){let t={$mid:1};return this._fsPath&&(t.fsPath=this._fsPath,t._sep=Gr),this._formatted&&(t.external=this._formatted),this.path&&(t.path=this.path),this.scheme&&(t.scheme=this.scheme),this.authority&&(t.authority=this.authority),this.query&&(t.query=this.query),this.fragment&&(t.fragment=this.fragment),t}},jr={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function Hr(e,t,n){let r,i=-1;for(let s=0;s<e.length;s++){let o=e.charCodeAt(s);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||o===45||o===46||o===95||o===126||t&&o===47||n&&o===91||n&&o===93||n&&o===58)i!==-1&&(r+=encodeURIComponent(e.substring(i,s)),i=-1),r!==void 0&&(r+=e.charAt(s));else{r===void 0&&(r=e.substr(0,s));let l=jr[o];l!==void 0?(i!==-1&&(r+=encodeURIComponent(e.substring(i,s)),i=-1),r+=l):i===-1&&(i=s)}}return i!==-1&&(r+=encodeURIComponent(e.substring(i))),r!==void 0?r:e}function _s(e){let t;for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);r===35||r===63?(t===void 0&&(t=e.substr(0,n)),t+=jr[r]):t!==void 0&&(t+=e[n])}return t!==void 0?t:e}function zn(e,t){let n;return e.authority&&e.path.length>1&&e.scheme==="file"?n=`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?n=e.path.substr(1):n=e.path[1].toLowerCase()+e.path.substr(2):n=e.path,Ie&&(n=n.replace(/\//g,"\\")),n}function On(e,t){let n=t?_s:Hr,r="",{scheme:i,authority:s,path:o,query:l,fragment:u}=e;if(i&&(r+=i,r+=":"),(s||i==="file")&&(r+=ce,r+=ce),s){let c=s.indexOf("@");if(c!==-1){let f=s.substr(0,c);s=s.substr(c+1),c=f.lastIndexOf(":"),c===-1?r+=n(f,!1,!1):(r+=n(f.substr(0,c),!1,!1),r+=":",r+=n(f.substr(c+1),!1,!0)),r+="@"}s=s.toLowerCase(),c=s.lastIndexOf(":"),c===-1?r+=n(s,!1,!0):(r+=n(s.substr(0,c),!1,!0),r+=s.substr(c))}if(o){if(o.length>=3&&o.charCodeAt(0)===47&&o.charCodeAt(2)===58){let c=o.charCodeAt(1);c>=65&&c<=90&&(o=`/${String.fromCharCode(c+32)}:${o.substr(3)}`)}else if(o.length>=2&&o.charCodeAt(1)===58){let c=o.charCodeAt(0);c>=65&&c<=90&&(o=`${String.fromCharCode(c+32)}:${o.substr(2)}`)}r+=n(o,!0,!1)}return l&&(r+="?",r+=n(l,!1,!1)),u&&(r+="#",r+=t?u:Hr(u,!1,!1)),r}function Qr(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+Qr(e.substr(3)):e}}var $r=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function Ot(e){return e.match($r)?e.replace($r,t=>Qr(t)):e}var Q=class e{constructor(t,n){this.lineNumber=t,this.column=n}with(t=this.lineNumber,n=this.column){return t===this.lineNumber&&n===this.column?this:new e(t,n)}delta(t=0,n=0){return this.with(this.lineNumber+t,this.column+n)}equals(t){return e.equals(this,t)}static equals(t,n){return!t&&!n?!0:!!t&&!!n&&t.lineNumber===n.lineNumber&&t.column===n.column}isBefore(t){return e.isBefore(this,t)}static isBefore(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<n.column}isBeforeOrEqual(t){return e.isBeforeOrEqual(this,t)}static isBeforeOrEqual(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<=n.column}static compare(t,n){let r=t.lineNumber|0,i=n.lineNumber|0;if(r===i){let s=t.column|0,o=n.column|0;return s-o}return r-i}clone(){return new e(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(t){return new e(t.lineNumber,t.column)}static isIPosition(t){return t&&typeof t.lineNumber=="number"&&typeof t.column=="number"}toJSON(){return{lineNumber:this.lineNumber,column:this.column}}};var q=class e{constructor(t,n,r,i){t>r||t===r&&n>i?(this.startLineNumber=r,this.startColumn=i,this.endLineNumber=t,this.endColumn=n):(this.startLineNumber=t,this.startColumn=n,this.endLineNumber=r,this.endColumn=i)}isEmpty(){return e.isEmpty(this)}static isEmpty(t){return t.startLineNumber===t.endLineNumber&&t.startColumn===t.endColumn}containsPosition(t){return e.containsPosition(this,t)}static containsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<t.startColumn||n.lineNumber===t.endLineNumber&&n.column>t.endColumn)}static strictContainsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<=t.startColumn||n.lineNumber===t.endLineNumber&&n.column>=t.endColumn)}containsRange(t){return e.containsRange(this,t)}static containsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>t.endColumn)}strictContainsRange(t){return e.strictContainsRange(this,t)}static strictContainsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<=t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>=t.endColumn)}plusRange(t){return e.plusRange(this,t)}static plusRange(t,n){let r,i,s,o;return n.startLineNumber<t.startLineNumber?(r=n.startLineNumber,i=n.startColumn):n.startLineNumber===t.startLineNumber?(r=n.startLineNumber,i=Math.min(n.startColumn,t.startColumn)):(r=t.startLineNumber,i=t.startColumn),n.endLineNumber>t.endLineNumber?(s=n.endLineNumber,o=n.endColumn):n.endLineNumber===t.endLineNumber?(s=n.endLineNumber,o=Math.max(n.endColumn,t.endColumn)):(s=t.endLineNumber,o=t.endColumn),new e(r,i,s,o)}intersectRanges(t){return e.intersectRanges(this,t)}static intersectRanges(t,n){let r=t.startLineNumber,i=t.startColumn,s=t.endLineNumber,o=t.endColumn,l=n.startLineNumber,u=n.startColumn,c=n.endLineNumber,f=n.endColumn;return r<l?(r=l,i=u):r===l&&(i=Math.max(i,u)),s>c?(s=c,o=f):s===c&&(o=Math.min(o,f)),r>s||r===s&&i>o?null:new e(r,i,s,o)}equalsRange(t){return e.equalsRange(this,t)}static equalsRange(t,n){return!t&&!n?!0:!!t&&!!n&&t.startLineNumber===n.startLineNumber&&t.startColumn===n.startColumn&&t.endLineNumber===n.endLineNumber&&t.endColumn===n.endColumn}getEndPosition(){return e.getEndPosition(this)}static getEndPosition(t){return new Q(t.endLineNumber,t.endColumn)}getStartPosition(){return e.getStartPosition(this)}static getStartPosition(t){return new Q(t.startLineNumber,t.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(t,n){return new e(this.startLineNumber,this.startColumn,t,n)}setStartPosition(t,n){return new e(t,n,this.endLineNumber,this.endColumn)}collapseToStart(){return e.collapseToStart(this)}static collapseToStart(t){return new e(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)}collapseToEnd(){return e.collapseToEnd(this)}static collapseToEnd(t){return new e(t.endLineNumber,t.endColumn,t.endLineNumber,t.endColumn)}delta(t){return new e(this.startLineNumber+t,this.startColumn,this.endLineNumber+t,this.endColumn)}static fromPositions(t,n=t){return new e(t.lineNumber,t.column,n.lineNumber,n.column)}static lift(t){return t?new e(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null}static isIRange(t){return t&&typeof t.startLineNumber=="number"&&typeof t.startColumn=="number"&&typeof t.endLineNumber=="number"&&typeof t.endColumn=="number"}static areIntersectingOrTouching(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<t.startColumn)}static areIntersecting(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<=n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<=t.startColumn)}static compareRangesUsingStarts(t,n){if(t&&n){let s=t.startLineNumber|0,o=n.startLineNumber|0;if(s===o){let l=t.startColumn|0,u=n.startColumn|0;if(l===u){let c=t.endLineNumber|0,f=n.endLineNumber|0;if(c===f){let h=t.endColumn|0,d=n.endColumn|0;return h-d}return c-f}return l-u}return s-o}return(t?1:0)-(n?1:0)}static compareRangesUsingEnds(t,n){return t.endLineNumber===n.endLineNumber?t.endColumn===n.endColumn?t.startLineNumber===n.startLineNumber?t.startColumn-n.startColumn:t.startLineNumber-n.startLineNumber:t.endColumn-n.endColumn:t.endLineNumber-n.endLineNumber}static spansMultipleLines(t){return t.endLineNumber>t.startLineNumber}toJSON(){return this}};function Jr(e,t,n=(r,i)=>r===i){if(e===t)return!0;if(!e||!t||e.length!==t.length)return!1;for(let r=0,i=e.length;r<i;r++)if(!n(e[r],t[r]))return!1;return!0}function*Xr(e,t){let n,r;for(let i of e)r!==void 0&&t(r,i)?n.push(i):(n&&(yield n),n=[i]),r=i;n&&(yield n)}function Yr(e,t){for(let n=0;n<=e.length;n++)t(n===0?void 0:e[n-1],n===e.length?void 0:e[n])}function Zr(e,t){for(let n=0;n<e.length;n++)t(n===0?void 0:e[n-1],e[n],n+1===e.length?void 0:e[n+1])}function Kr(e,t){for(let n of t)e.push(n)}var Hn;(function(e){function t(s){return s<0}e.isLessThan=t;function n(s){return s<=0}e.isLessThanOrEqual=n;function r(s){return s>0}e.isGreaterThan=r;function i(s){return s===0}e.isNeitherLessOrGreaterThan=i,e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0})(Hn||(Hn={}));function bt(e,t){return(n,r)=>t(e(n),e(r))}var xt=(e,t)=>e-t;function e1(e){return(t,n)=>-e(t,n)}var Ht=class e{constructor(t){this.iterate=t}toArray(){let t=[];return this.iterate(n=>(t.push(n),!0)),t}filter(t){return new e(n=>this.iterate(r=>t(r)?n(r):!0))}map(t){return new e(n=>this.iterate(r=>n(t(r))))}findLast(t){let n;return this.iterate(r=>(t(r)&&(n=r),!0)),n}findLastMaxBy(t){let n,r=!0;return this.iterate(i=>((r||Hn.isGreaterThan(t(i,n)))&&(r=!1,n=i),!0)),n}};Ht.empty=new Ht(e=>{});function $n(e){return e<0?0:e>255?255:e|0}function Be(e){return e<0?0:e>4294967295?4294967295:e|0}var $t=class{constructor(t){this.values=t,this.prefixSum=new Uint32Array(t.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}insertValues(t,n){t=Be(t);let r=this.values,i=this.prefixSum,s=n.length;return s===0?!1:(this.values=new Uint32Array(r.length+s),this.values.set(r.subarray(0,t),0),this.values.set(r.subarray(t),t+s),this.values.set(n,t),t-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=t-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(i.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}setValue(t,n){return t=Be(t),n=Be(n),this.values[t]===n?!1:(this.values[t]=n,t-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=t-1),!0)}removeValues(t,n){t=Be(t),n=Be(n);let r=this.values,i=this.prefixSum;if(t>=r.length)return!1;let s=r.length-t;return n>=s&&(n=s),n===0?!1:(this.values=new Uint32Array(r.length-n),this.values.set(r.subarray(0,t),0),this.values.set(r.subarray(t+n),t),this.prefixSum=new Uint32Array(this.values.length),t-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=t-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(i.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}getTotalSum(){return this.values.length===0?0:this._getPrefixSum(this.values.length-1)}getPrefixSum(t){return t<0?0:(t=Be(t),this._getPrefixSum(t))}_getPrefixSum(t){if(t<=this.prefixSumValidIndex[0])return this.prefixSum[t];let n=this.prefixSumValidIndex[0]+1;n===0&&(this.prefixSum[0]=this.values[0],n++),t>=this.values.length&&(t=this.values.length-1);for(let r=n;r<=t;r++)this.prefixSum[r]=this.prefixSum[r-1]+this.values[r];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],t),this.prefixSum[t]}getIndexOf(t){t=Math.floor(t),this.getTotalSum();let n=0,r=this.values.length-1,i=0,s=0,o=0;for(;n<=r;)if(i=n+(r-n)/2|0,s=this.prefixSum[i],o=s-this.values[i],t<o)r=i-1;else if(t>=s)n=i+1;else break;return new Gn(i,t-o)}};var Gn=class{constructor(t,n){this.index=t,this.remainder=n,this._prefixSumIndexOfResultBrand=void 0,this.index=t,this.remainder=n}};var Gt=class{constructor(t,n,r,i){this._uri=t,this._lines=n,this._eol=r,this._versionId=i,this._lineStarts=null,this._cachedTextValue=null}dispose(){this._lines.length=0}get version(){return this._versionId}getText(){return this._cachedTextValue===null&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}onEvents(t){t.eol&&t.eol!==this._eol&&(this._eol=t.eol,this._lineStarts=null);let n=t.changes;for(let r of n)this._acceptDeleteRange(r.range),this._acceptInsertText(new Q(r.range.startLineNumber,r.range.startColumn),r.text);this._versionId=t.versionId,this._cachedTextValue=null}_ensureLineStarts(){if(!this._lineStarts){let t=this._eol.length,n=this._lines.length,r=new Uint32Array(n);for(let i=0;i<n;i++)r[i]=this._lines[i].length+t;this._lineStarts=new $t(r)}}_setLineText(t,n){this._lines[t]=n,this._lineStarts&&this._lineStarts.setValue(t,this._lines[t].length+this._eol.length)}_acceptDeleteRange(t){if(t.startLineNumber===t.endLineNumber){if(t.startColumn===t.endColumn)return;this._setLineText(t.startLineNumber-1,this._lines[t.startLineNumber-1].substring(0,t.startColumn-1)+this._lines[t.startLineNumber-1].substring(t.endColumn-1));return}this._setLineText(t.startLineNumber-1,this._lines[t.startLineNumber-1].substring(0,t.startColumn-1)+this._lines[t.endLineNumber-1].substring(t.endColumn-1)),this._lines.splice(t.startLineNumber,t.endLineNumber-t.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(t.startLineNumber,t.endLineNumber-t.startLineNumber)}_acceptInsertText(t,n){if(n.length===0)return;let r=Mr(n);if(r.length===1){this._setLineText(t.lineNumber-1,this._lines[t.lineNumber-1].substring(0,t.column-1)+r[0]+this._lines[t.lineNumber-1].substring(t.column-1));return}r[r.length-1]+=this._lines[t.lineNumber-1].substring(t.column-1),this._setLineText(t.lineNumber-1,this._lines[t.lineNumber-1].substring(0,t.column-1)+r[0]);let i=new Uint32Array(r.length-1);for(let s=1;s<r.length;s++)this._lines.splice(t.lineNumber+s-1,0,r[s]),i[s-1]=r[s].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(t.lineNumber,i)}};var vs="`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?";function Ls(e=""){let t="(-?\\d*\\.\\d\\w*)|([^";for(let n of vs)e.indexOf(n)>=0||(t+="\\"+n);return t+="\\s]+)",new RegExp(t,"g")}var jn=Ls();function Qn(e){let t=jn;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}var t1=new lt;t1.unshift({maxLen:1e3,windowSize:15,timeBudget:150});function _t(e,t,n,r,i){if(t=Qn(t),i||(i=Oe.first(t1)),n.length>i.maxLen){let c=e-i.maxLen/2;return c<0?c=0:r+=c,n=n.substring(c,e+i.maxLen/2),_t(e,t,n,r,i)}let s=Date.now(),o=e-1-r,l=-1,u=null;for(let c=1;!(Date.now()-s>=i.timeBudget);c++){let f=o-i.windowSize*c;t.lastIndex=Math.max(0,f);let h=ws(t,n,o,l);if(!h&&u||(u=h,f<=0))break;l=f}if(u){let c={word:u[0],startColumn:r+1+u.index,endColumn:r+1+u.index+u[0].length};return t.lastIndex=0,c}return null}function ws(e,t,n,r){let i;for(;i=e.exec(t);){let s=i.index||0;if(s<=n&&e.lastIndex>=n)return i;if(r>0&&s>r)return null}return null}var Xe=class e{constructor(t){let n=$n(t);this._defaultValue=n,this._asciiMap=e._createAsciiMap(n),this._map=new Map}static _createAsciiMap(t){let n=new Uint8Array(256);return n.fill(t),n}set(t,n){let r=$n(n);t>=0&&t<256?this._asciiMap[t]=r:this._map.set(t,r)}get(t){return t>=0&&t<256?this._asciiMap[t]:this._map.get(t)||this._defaultValue}clear(){this._asciiMap.fill(this._defaultValue),this._map.clear()}};var Xn=class{constructor(t,n,r){let i=new Uint8Array(t*n);for(let s=0,o=t*n;s<o;s++)i[s]=r;this._data=i,this.rows=t,this.cols=n}get(t,n){return this._data[t*this.cols+n]}set(t,n,r){this._data[t*this.cols+n]=r}},Yn=class{constructor(t){let n=0,r=0;for(let s=0,o=t.length;s<o;s++){let[l,u,c]=t[s];u>n&&(n=u),l>r&&(r=l),c>r&&(r=c)}n++,r++;let i=new Xn(r,n,0);for(let s=0,o=t.length;s<o;s++){let[l,u,c]=t[s];i.set(l,u,c)}this._states=i,this._maxCharCode=n}nextState(t,n){return n<0||n>=this._maxCharCode?0:this._states.get(t,n)}},Jn=null;function Ns(){return Jn===null&&(Jn=new Yn([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),Jn}var vt=null;function Ss(){if(vt===null){vt=new Xe(0);let e=` 	<>'"\u3001\u3002\uFF61\uFF64\uFF0C\uFF0E\uFF1A\uFF1B\u2018\u3008\u300C\u300E\u3014\uFF08\uFF3B\uFF5B\uFF62\uFF63\uFF5D\uFF3D\uFF09\u3015\u300F\u300D\u3009\u2019\uFF40\uFF5E\u2026`;for(let n=0;n<e.length;n++)vt.set(e.charCodeAt(n),1);let t=".,;:";for(let n=0;n<t.length;n++)vt.set(t.charCodeAt(n),2)}return vt}var Zn=class e{static _createLink(t,n,r,i,s){let o=s-1;do{let l=n.charCodeAt(o);if(t.get(l)!==2)break;o--}while(o>i);if(i>0){let l=n.charCodeAt(i-1),u=n.charCodeAt(o);(l===40&&u===41||l===91&&u===93||l===123&&u===125)&&o--}return{range:{startLineNumber:r,startColumn:i+1,endLineNumber:r,endColumn:o+2},url:n.substring(i,o+1)}}static computeLinks(t,n=Ns()){let r=Ss(),i=[];for(let s=1,o=t.getLineCount();s<=o;s++){let l=t.getLineContent(s),u=l.length,c=0,f=0,h=0,d=1,g=!1,p=!1,m=!1,_=!1;for(;c<u;){let N=!1,b=l.charCodeAt(c);if(d===13){let x;switch(b){case 40:g=!0,x=0;break;case 41:x=g?0:1;break;case 91:m=!0,p=!0,x=0;break;case 93:m=!1,x=p?0:1;break;case 123:_=!0,x=0;break;case 125:x=_?0:1;break;case 39:case 34:case 96:h===b?x=1:h===39||h===34||h===96?x=0:x=1;break;case 42:x=h===42?1:0;break;case 124:x=h===124?1:0;break;case 32:x=m?0:1;break;default:x=r.get(b)}x===1&&(i.push(e._createLink(r,l,s,f,c)),N=!0)}else if(d===12){let x;b===91?(p=!0,x=0):x=r.get(b),x===1?N=!0:d=13}else d=n.nextState(d,b),d===0&&(N=!0);N&&(d=1,g=!1,p=!1,_=!1,f=c+1,h=b),c++}d===13&&i.push(e._createLink(r,l,s,f,u))}return i}};function n1(e){return!e||typeof e.getLineCount!="function"||typeof e.getLineContent!="function"?[]:Zn.computeLinks(e)}var Ye=class{constructor(){this._defaultValueSet=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}navigateValueSet(t,n,r,i,s){if(t&&n){let o=this.doNavigateValueSet(n,s);if(o)return{range:t,value:o}}if(r&&i){let o=this.doNavigateValueSet(i,s);if(o)return{range:r,value:o}}return null}doNavigateValueSet(t,n){let r=this.numberReplace(t,n);return r!==null?r:this.textReplace(t,n)}numberReplace(t,n){let r=Math.pow(10,t.length-(t.lastIndexOf(".")+1)),i=Number(t),s=parseFloat(t);return!isNaN(i)&&!isNaN(s)&&i===s?i===0&&!n?null:(i=Math.floor(i*r),i+=n?r:-r,String(i/r)):null}textReplace(t,n){return this.valueSetsReplace(this._defaultValueSet,t,n)}valueSetsReplace(t,n,r){let i=null;for(let s=0,o=t.length;i===null&&s<o;s++)i=this.valueSetReplace(t[s],n,r);return i}valueSetReplace(t,n,r){let i=t.indexOf(n);return i>=0?(i+=r?1:-1,i<0?i=t.length-1:i%=t.length,t[i]):null}};Ye.INSTANCE=new Ye;var r1=Object.freeze(function(e,t){let n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}}),jt;(function(e){function t(n){return n===e.None||n===e.Cancelled||n instanceof Ze?!0:!n||typeof n!="object"?!1:typeof n.isCancellationRequested=="boolean"&&typeof n.onCancellationRequested=="function"}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:Mt.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:r1})})(jt||(jt={}));var Ze=class{constructor(){this._isCancelled=!1,this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?r1:(this._emitter||(this._emitter=new Z),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}},Qt=class{constructor(t){this._token=void 0,this._parentListener=void 0,this._parentListener=t&&t.onCancellationRequested(this.cancel,this)}get token(){return this._token||(this._token=new Ze),this._token}cancel(){this._token?this._token instanceof Ze&&this._token.cancel():this._token=jt.Cancelled}dispose(t=!1){var n;t&&this.cancel(),(n=this._parentListener)===null||n===void 0||n.dispose(),this._token?this._token instanceof Ze&&this._token.dispose():this._token=jt.None}};var Lt=class{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(t,n){this._keyCodeToStr[t]=n,this._strToKeyCode[n.toLowerCase()]=t}keyCodeToStr(t){return this._keyCodeToStr[t]}strToKeyCode(t){return this._strToKeyCode[t.toLowerCase()]||0}},Jt=new Lt,Kn=new Lt,er=new Lt,Cs=new Array(230),As={},Rs=[],ys=Object.create(null),Es=Object.create(null),s1=[],tr=[];for(let e=0;e<=193;e++)s1[e]=-1;for(let e=0;e<=132;e++)tr[e]=-1;(function(){let e="",t=[[1,0,"None",0,"unknown",0,"VK_UNKNOWN",e,e],[1,1,"Hyper",0,e,0,e,e,e],[1,2,"Super",0,e,0,e,e,e],[1,3,"Fn",0,e,0,e,e,e],[1,4,"FnLock",0,e,0,e,e,e],[1,5,"Suspend",0,e,0,e,e,e],[1,6,"Resume",0,e,0,e,e,e],[1,7,"Turbo",0,e,0,e,e,e],[1,8,"Sleep",0,e,0,"VK_SLEEP",e,e],[1,9,"WakeUp",0,e,0,e,e,e],[0,10,"KeyA",31,"A",65,"VK_A",e,e],[0,11,"KeyB",32,"B",66,"VK_B",e,e],[0,12,"KeyC",33,"C",67,"VK_C",e,e],[0,13,"KeyD",34,"D",68,"VK_D",e,e],[0,14,"KeyE",35,"E",69,"VK_E",e,e],[0,15,"KeyF",36,"F",70,"VK_F",e,e],[0,16,"KeyG",37,"G",71,"VK_G",e,e],[0,17,"KeyH",38,"H",72,"VK_H",e,e],[0,18,"KeyI",39,"I",73,"VK_I",e,e],[0,19,"KeyJ",40,"J",74,"VK_J",e,e],[0,20,"KeyK",41,"K",75,"VK_K",e,e],[0,21,"KeyL",42,"L",76,"VK_L",e,e],[0,22,"KeyM",43,"M",77,"VK_M",e,e],[0,23,"KeyN",44,"N",78,"VK_N",e,e],[0,24,"KeyO",45,"O",79,"VK_O",e,e],[0,25,"KeyP",46,"P",80,"VK_P",e,e],[0,26,"KeyQ",47,"Q",81,"VK_Q",e,e],[0,27,"KeyR",48,"R",82,"VK_R",e,e],[0,28,"KeyS",49,"S",83,"VK_S",e,e],[0,29,"KeyT",50,"T",84,"VK_T",e,e],[0,30,"KeyU",51,"U",85,"VK_U",e,e],[0,31,"KeyV",52,"V",86,"VK_V",e,e],[0,32,"KeyW",53,"W",87,"VK_W",e,e],[0,33,"KeyX",54,"X",88,"VK_X",e,e],[0,34,"KeyY",55,"Y",89,"VK_Y",e,e],[0,35,"KeyZ",56,"Z",90,"VK_Z",e,e],[0,36,"Digit1",22,"1",49,"VK_1",e,e],[0,37,"Digit2",23,"2",50,"VK_2",e,e],[0,38,"Digit3",24,"3",51,"VK_3",e,e],[0,39,"Digit4",25,"4",52,"VK_4",e,e],[0,40,"Digit5",26,"5",53,"VK_5",e,e],[0,41,"Digit6",27,"6",54,"VK_6",e,e],[0,42,"Digit7",28,"7",55,"VK_7",e,e],[0,43,"Digit8",29,"8",56,"VK_8",e,e],[0,44,"Digit9",30,"9",57,"VK_9",e,e],[0,45,"Digit0",21,"0",48,"VK_0",e,e],[1,46,"Enter",3,"Enter",13,"VK_RETURN",e,e],[1,47,"Escape",9,"Escape",27,"VK_ESCAPE",e,e],[1,48,"Backspace",1,"Backspace",8,"VK_BACK",e,e],[1,49,"Tab",2,"Tab",9,"VK_TAB",e,e],[1,50,"Space",10,"Space",32,"VK_SPACE",e,e],[0,51,"Minus",88,"-",189,"VK_OEM_MINUS","-","OEM_MINUS"],[0,52,"Equal",86,"=",187,"VK_OEM_PLUS","=","OEM_PLUS"],[0,53,"BracketLeft",92,"[",219,"VK_OEM_4","[","OEM_4"],[0,54,"BracketRight",94,"]",221,"VK_OEM_6","]","OEM_6"],[0,55,"Backslash",93,"\\",220,"VK_OEM_5","\\","OEM_5"],[0,56,"IntlHash",0,e,0,e,e,e],[0,57,"Semicolon",85,";",186,"VK_OEM_1",";","OEM_1"],[0,58,"Quote",95,"'",222,"VK_OEM_7","'","OEM_7"],[0,59,"Backquote",91,"`",192,"VK_OEM_3","`","OEM_3"],[0,60,"Comma",87,",",188,"VK_OEM_COMMA",",","OEM_COMMA"],[0,61,"Period",89,".",190,"VK_OEM_PERIOD",".","OEM_PERIOD"],[0,62,"Slash",90,"/",191,"VK_OEM_2","/","OEM_2"],[1,63,"CapsLock",8,"CapsLock",20,"VK_CAPITAL",e,e],[1,64,"F1",59,"F1",112,"VK_F1",e,e],[1,65,"F2",60,"F2",113,"VK_F2",e,e],[1,66,"F3",61,"F3",114,"VK_F3",e,e],[1,67,"F4",62,"F4",115,"VK_F4",e,e],[1,68,"F5",63,"F5",116,"VK_F5",e,e],[1,69,"F6",64,"F6",117,"VK_F6",e,e],[1,70,"F7",65,"F7",118,"VK_F7",e,e],[1,71,"F8",66,"F8",119,"VK_F8",e,e],[1,72,"F9",67,"F9",120,"VK_F9",e,e],[1,73,"F10",68,"F10",121,"VK_F10",e,e],[1,74,"F11",69,"F11",122,"VK_F11",e,e],[1,75,"F12",70,"F12",123,"VK_F12",e,e],[1,76,"PrintScreen",0,e,0,e,e,e],[1,77,"ScrollLock",84,"ScrollLock",145,"VK_SCROLL",e,e],[1,78,"Pause",7,"PauseBreak",19,"VK_PAUSE",e,e],[1,79,"Insert",19,"Insert",45,"VK_INSERT",e,e],[1,80,"Home",14,"Home",36,"VK_HOME",e,e],[1,81,"PageUp",11,"PageUp",33,"VK_PRIOR",e,e],[1,82,"Delete",20,"Delete",46,"VK_DELETE",e,e],[1,83,"End",13,"End",35,"VK_END",e,e],[1,84,"PageDown",12,"PageDown",34,"VK_NEXT",e,e],[1,85,"ArrowRight",17,"RightArrow",39,"VK_RIGHT","Right",e],[1,86,"ArrowLeft",15,"LeftArrow",37,"VK_LEFT","Left",e],[1,87,"ArrowDown",18,"DownArrow",40,"VK_DOWN","Down",e],[1,88,"ArrowUp",16,"UpArrow",38,"VK_UP","Up",e],[1,89,"NumLock",83,"NumLock",144,"VK_NUMLOCK",e,e],[1,90,"NumpadDivide",113,"NumPad_Divide",111,"VK_DIVIDE",e,e],[1,91,"NumpadMultiply",108,"NumPad_Multiply",106,"VK_MULTIPLY",e,e],[1,92,"NumpadSubtract",111,"NumPad_Subtract",109,"VK_SUBTRACT",e,e],[1,93,"NumpadAdd",109,"NumPad_Add",107,"VK_ADD",e,e],[1,94,"NumpadEnter",3,e,0,e,e,e],[1,95,"Numpad1",99,"NumPad1",97,"VK_NUMPAD1",e,e],[1,96,"Numpad2",100,"NumPad2",98,"VK_NUMPAD2",e,e],[1,97,"Numpad3",101,"NumPad3",99,"VK_NUMPAD3",e,e],[1,98,"Numpad4",102,"NumPad4",100,"VK_NUMPAD4",e,e],[1,99,"Numpad5",103,"NumPad5",101,"VK_NUMPAD5",e,e],[1,100,"Numpad6",104,"NumPad6",102,"VK_NUMPAD6",e,e],[1,101,"Numpad7",105,"NumPad7",103,"VK_NUMPAD7",e,e],[1,102,"Numpad8",106,"NumPad8",104,"VK_NUMPAD8",e,e],[1,103,"Numpad9",107,"NumPad9",105,"VK_NUMPAD9",e,e],[1,104,"Numpad0",98,"NumPad0",96,"VK_NUMPAD0",e,e],[1,105,"NumpadDecimal",112,"NumPad_Decimal",110,"VK_DECIMAL",e,e],[0,106,"IntlBackslash",97,"OEM_102",226,"VK_OEM_102",e,e],[1,107,"ContextMenu",58,"ContextMenu",93,e,e,e],[1,108,"Power",0,e,0,e,e,e],[1,109,"NumpadEqual",0,e,0,e,e,e],[1,110,"F13",71,"F13",124,"VK_F13",e,e],[1,111,"F14",72,"F14",125,"VK_F14",e,e],[1,112,"F15",73,"F15",126,"VK_F15",e,e],[1,113,"F16",74,"F16",127,"VK_F16",e,e],[1,114,"F17",75,"F17",128,"VK_F17",e,e],[1,115,"F18",76,"F18",129,"VK_F18",e,e],[1,116,"F19",77,"F19",130,"VK_F19",e,e],[1,117,"F20",78,"F20",131,"VK_F20",e,e],[1,118,"F21",79,"F21",132,"VK_F21",e,e],[1,119,"F22",80,"F22",133,"VK_F22",e,e],[1,120,"F23",81,"F23",134,"VK_F23",e,e],[1,121,"F24",82,"F24",135,"VK_F24",e,e],[1,122,"Open",0,e,0,e,e,e],[1,123,"Help",0,e,0,e,e,e],[1,124,"Select",0,e,0,e,e,e],[1,125,"Again",0,e,0,e,e,e],[1,126,"Undo",0,e,0,e,e,e],[1,127,"Cut",0,e,0,e,e,e],[1,128,"Copy",0,e,0,e,e,e],[1,129,"Paste",0,e,0,e,e,e],[1,130,"Find",0,e,0,e,e,e],[1,131,"AudioVolumeMute",117,"AudioVolumeMute",173,"VK_VOLUME_MUTE",e,e],[1,132,"AudioVolumeUp",118,"AudioVolumeUp",175,"VK_VOLUME_UP",e,e],[1,133,"AudioVolumeDown",119,"AudioVolumeDown",174,"VK_VOLUME_DOWN",e,e],[1,134,"NumpadComma",110,"NumPad_Separator",108,"VK_SEPARATOR",e,e],[0,135,"IntlRo",115,"ABNT_C1",193,"VK_ABNT_C1",e,e],[1,136,"KanaMode",0,e,0,e,e,e],[0,137,"IntlYen",0,e,0,e,e,e],[1,138,"Convert",0,e,0,e,e,e],[1,139,"NonConvert",0,e,0,e,e,e],[1,140,"Lang1",0,e,0,e,e,e],[1,141,"Lang2",0,e,0,e,e,e],[1,142,"Lang3",0,e,0,e,e,e],[1,143,"Lang4",0,e,0,e,e,e],[1,144,"Lang5",0,e,0,e,e,e],[1,145,"Abort",0,e,0,e,e,e],[1,146,"Props",0,e,0,e,e,e],[1,147,"NumpadParenLeft",0,e,0,e,e,e],[1,148,"NumpadParenRight",0,e,0,e,e,e],[1,149,"NumpadBackspace",0,e,0,e,e,e],[1,150,"NumpadMemoryStore",0,e,0,e,e,e],[1,151,"NumpadMemoryRecall",0,e,0,e,e,e],[1,152,"NumpadMemoryClear",0,e,0,e,e,e],[1,153,"NumpadMemoryAdd",0,e,0,e,e,e],[1,154,"NumpadMemorySubtract",0,e,0,e,e,e],[1,155,"NumpadClear",131,"Clear",12,"VK_CLEAR",e,e],[1,156,"NumpadClearEntry",0,e,0,e,e,e],[1,0,e,5,"Ctrl",17,"VK_CONTROL",e,e],[1,0,e,4,"Shift",16,"VK_SHIFT",e,e],[1,0,e,6,"Alt",18,"VK_MENU",e,e],[1,0,e,57,"Meta",91,"VK_COMMAND",e,e],[1,157,"ControlLeft",5,e,0,"VK_LCONTROL",e,e],[1,158,"ShiftLeft",4,e,0,"VK_LSHIFT",e,e],[1,159,"AltLeft",6,e,0,"VK_LMENU",e,e],[1,160,"MetaLeft",57,e,0,"VK_LWIN",e,e],[1,161,"ControlRight",5,e,0,"VK_RCONTROL",e,e],[1,162,"ShiftRight",4,e,0,"VK_RSHIFT",e,e],[1,163,"AltRight",6,e,0,"VK_RMENU",e,e],[1,164,"MetaRight",57,e,0,"VK_RWIN",e,e],[1,165,"BrightnessUp",0,e,0,e,e,e],[1,166,"BrightnessDown",0,e,0,e,e,e],[1,167,"MediaPlay",0,e,0,e,e,e],[1,168,"MediaRecord",0,e,0,e,e,e],[1,169,"MediaFastForward",0,e,0,e,e,e],[1,170,"MediaRewind",0,e,0,e,e,e],[1,171,"MediaTrackNext",124,"MediaTrackNext",176,"VK_MEDIA_NEXT_TRACK",e,e],[1,172,"MediaTrackPrevious",125,"MediaTrackPrevious",177,"VK_MEDIA_PREV_TRACK",e,e],[1,173,"MediaStop",126,"MediaStop",178,"VK_MEDIA_STOP",e,e],[1,174,"Eject",0,e,0,e,e,e],[1,175,"MediaPlayPause",127,"MediaPlayPause",179,"VK_MEDIA_PLAY_PAUSE",e,e],[1,176,"MediaSelect",128,"LaunchMediaPlayer",181,"VK_MEDIA_LAUNCH_MEDIA_SELECT",e,e],[1,177,"LaunchMail",129,"LaunchMail",180,"VK_MEDIA_LAUNCH_MAIL",e,e],[1,178,"LaunchApp2",130,"LaunchApp2",183,"VK_MEDIA_LAUNCH_APP2",e,e],[1,179,"LaunchApp1",0,e,0,"VK_MEDIA_LAUNCH_APP1",e,e],[1,180,"SelectTask",0,e,0,e,e,e],[1,181,"LaunchScreenSaver",0,e,0,e,e,e],[1,182,"BrowserSearch",120,"BrowserSearch",170,"VK_BROWSER_SEARCH",e,e],[1,183,"BrowserHome",121,"BrowserHome",172,"VK_BROWSER_HOME",e,e],[1,184,"BrowserBack",122,"BrowserBack",166,"VK_BROWSER_BACK",e,e],[1,185,"BrowserForward",123,"BrowserForward",167,"VK_BROWSER_FORWARD",e,e],[1,186,"BrowserStop",0,e,0,"VK_BROWSER_STOP",e,e],[1,187,"BrowserRefresh",0,e,0,"VK_BROWSER_REFRESH",e,e],[1,188,"BrowserFavorites",0,e,0,"VK_BROWSER_FAVORITES",e,e],[1,189,"ZoomToggle",0,e,0,e,e,e],[1,190,"MailReply",0,e,0,e,e,e],[1,191,"MailForward",0,e,0,e,e,e],[1,192,"MailSend",0,e,0,e,e,e],[1,0,e,114,"KeyInComposition",229,e,e,e],[1,0,e,116,"ABNT_C2",194,"VK_ABNT_C2",e,e],[1,0,e,96,"OEM_8",223,"VK_OEM_8",e,e],[1,0,e,0,e,0,"VK_KANA",e,e],[1,0,e,0,e,0,"VK_HANGUL",e,e],[1,0,e,0,e,0,"VK_JUNJA",e,e],[1,0,e,0,e,0,"VK_FINAL",e,e],[1,0,e,0,e,0,"VK_HANJA",e,e],[1,0,e,0,e,0,"VK_KANJI",e,e],[1,0,e,0,e,0,"VK_CONVERT",e,e],[1,0,e,0,e,0,"VK_NONCONVERT",e,e],[1,0,e,0,e,0,"VK_ACCEPT",e,e],[1,0,e,0,e,0,"VK_MODECHANGE",e,e],[1,0,e,0,e,0,"VK_SELECT",e,e],[1,0,e,0,e,0,"VK_PRINT",e,e],[1,0,e,0,e,0,"VK_EXECUTE",e,e],[1,0,e,0,e,0,"VK_SNAPSHOT",e,e],[1,0,e,0,e,0,"VK_HELP",e,e],[1,0,e,0,e,0,"VK_APPS",e,e],[1,0,e,0,e,0,"VK_PROCESSKEY",e,e],[1,0,e,0,e,0,"VK_PACKET",e,e],[1,0,e,0,e,0,"VK_DBE_SBCSCHAR",e,e],[1,0,e,0,e,0,"VK_DBE_DBCSCHAR",e,e],[1,0,e,0,e,0,"VK_ATTN",e,e],[1,0,e,0,e,0,"VK_CRSEL",e,e],[1,0,e,0,e,0,"VK_EXSEL",e,e],[1,0,e,0,e,0,"VK_EREOF",e,e],[1,0,e,0,e,0,"VK_PLAY",e,e],[1,0,e,0,e,0,"VK_ZOOM",e,e],[1,0,e,0,e,0,"VK_NONAME",e,e],[1,0,e,0,e,0,"VK_PA1",e,e],[1,0,e,0,e,0,"VK_OEM_CLEAR",e,e]],n=[],r=[];for(let i of t){let[s,o,l,u,c,f,h,d,g]=i;if(r[o]||(r[o]=!0,Rs[o]=l,ys[l]=o,Es[l.toLowerCase()]=o,s&&(s1[o]=u,u!==0&&u!==3&&u!==5&&u!==4&&u!==6&&u!==57&&(tr[u]=o))),!n[u]){if(n[u]=!0,!c)throw new Error(`String representation missing for key code ${u} around scan code ${l}`);Jt.define(u,c),Kn.define(u,d||c),er.define(u,g||d||c)}f&&(Cs[f]=u),h&&(As[h]=u)}tr[3]=46})();var i1;(function(e){function t(l){return Jt.keyCodeToStr(l)}e.toString=t;function n(l){return Jt.strToKeyCode(l)}e.fromString=n;function r(l){return Kn.keyCodeToStr(l)}e.toUserSettingsUS=r;function i(l){return er.keyCodeToStr(l)}e.toUserSettingsGeneral=i;function s(l){return Kn.strToKeyCode(l)||er.strToKeyCode(l)}e.fromUserSettings=s;function o(l){if(l>=98&&l<=113)return null;switch(l){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return Jt.keyCodeToStr(l)}e.toElectronAccelerator=o})(i1||(i1={}));function o1(e,t){let n=(t&65535)<<16>>>0;return(e|n)>>>0}var Xt=class e extends q{constructor(t,n,r,i){super(t,n,r,i),this.selectionStartLineNumber=t,this.selectionStartColumn=n,this.positionLineNumber=r,this.positionColumn=i}toString(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}equalsSelection(t){return e.selectionsEqual(this,t)}static selectionsEqual(t,n){return t.selectionStartLineNumber===n.selectionStartLineNumber&&t.selectionStartColumn===n.selectionStartColumn&&t.positionLineNumber===n.positionLineNumber&&t.positionColumn===n.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(t,n){return this.getDirection()===0?new e(this.startLineNumber,this.startColumn,t,n):new e(t,n,this.startLineNumber,this.startColumn)}getPosition(){return new Q(this.positionLineNumber,this.positionColumn)}getSelectionStart(){return new Q(this.selectionStartLineNumber,this.selectionStartColumn)}setStartPosition(t,n){return this.getDirection()===0?new e(t,n,this.endLineNumber,this.endColumn):new e(this.endLineNumber,this.endColumn,t,n)}static fromPositions(t,n=t){return new e(t.lineNumber,t.column,n.lineNumber,n.column)}static fromRange(t,n){return n===0?new e(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):new e(t.endLineNumber,t.endColumn,t.startLineNumber,t.startColumn)}static liftSelection(t){return new e(t.selectionStartLineNumber,t.selectionStartColumn,t.positionLineNumber,t.positionColumn)}static selectionsArrEqual(t,n){if(t&&!n||!t&&n)return!1;if(!t&&!n)return!0;if(t.length!==n.length)return!1;for(let r=0,i=t.length;r<i;r++)if(!this.selectionsEqual(t[r],n[r]))return!1;return!0}static isISelection(t){return t&&typeof t.selectionStartLineNumber=="number"&&typeof t.selectionStartColumn=="number"&&typeof t.positionLineNumber=="number"&&typeof t.positionColumn=="number"}static createWithDirection(t,n,r,i,s){return s===0?new e(t,n,r,i):new e(r,i,t,n)}};var a1=Object.create(null);function a(e,t){if(Nr(t)){let n=a1[t];if(n===void 0)throw new Error(`${e} references an unknown codicon: ${t}`);t=n}return a1[e]=t,{id:e}}var E={add:a("add",6e4),plus:a("plus",6e4),gistNew:a("gist-new",6e4),repoCreate:a("repo-create",6e4),lightbulb:a("lightbulb",60001),lightBulb:a("light-bulb",60001),repo:a("repo",60002),repoDelete:a("repo-delete",60002),gistFork:a("gist-fork",60003),repoForked:a("repo-forked",60003),gitPullRequest:a("git-pull-request",60004),gitPullRequestAbandoned:a("git-pull-request-abandoned",60004),recordKeys:a("record-keys",60005),keyboard:a("keyboard",60005),tag:a("tag",60006),tagAdd:a("tag-add",60006),tagRemove:a("tag-remove",60006),gitPullRequestLabel:a("git-pull-request-label",60006),person:a("person",60007),personFollow:a("person-follow",60007),personOutline:a("person-outline",60007),personFilled:a("person-filled",60007),gitBranch:a("git-branch",60008),gitBranchCreate:a("git-branch-create",60008),gitBranchDelete:a("git-branch-delete",60008),sourceControl:a("source-control",60008),mirror:a("mirror",60009),mirrorPublic:a("mirror-public",60009),star:a("star",60010),starAdd:a("star-add",60010),starDelete:a("star-delete",60010),starEmpty:a("star-empty",60010),comment:a("comment",60011),commentAdd:a("comment-add",60011),alert:a("alert",60012),warning:a("warning",60012),search:a("search",60013),searchSave:a("search-save",60013),logOut:a("log-out",60014),signOut:a("sign-out",60014),logIn:a("log-in",60015),signIn:a("sign-in",60015),eye:a("eye",60016),eyeUnwatch:a("eye-unwatch",60016),eyeWatch:a("eye-watch",60016),circleFilled:a("circle-filled",60017),primitiveDot:a("primitive-dot",60017),closeDirty:a("close-dirty",60017),debugBreakpoint:a("debug-breakpoint",60017),debugBreakpointDisabled:a("debug-breakpoint-disabled",60017),debugHint:a("debug-hint",60017),primitiveSquare:a("primitive-square",60018),edit:a("edit",60019),pencil:a("pencil",60019),info:a("info",60020),issueOpened:a("issue-opened",60020),gistPrivate:a("gist-private",60021),gitForkPrivate:a("git-fork-private",60021),lock:a("lock",60021),mirrorPrivate:a("mirror-private",60021),close:a("close",60022),removeClose:a("remove-close",60022),x:a("x",60022),repoSync:a("repo-sync",60023),sync:a("sync",60023),clone:a("clone",60024),desktopDownload:a("desktop-download",60024),beaker:a("beaker",60025),microscope:a("microscope",60025),vm:a("vm",60026),deviceDesktop:a("device-desktop",60026),file:a("file",60027),fileText:a("file-text",60027),more:a("more",60028),ellipsis:a("ellipsis",60028),kebabHorizontal:a("kebab-horizontal",60028),mailReply:a("mail-reply",60029),reply:a("reply",60029),organization:a("organization",60030),organizationFilled:a("organization-filled",60030),organizationOutline:a("organization-outline",60030),newFile:a("new-file",60031),fileAdd:a("file-add",60031),newFolder:a("new-folder",60032),fileDirectoryCreate:a("file-directory-create",60032),trash:a("trash",60033),trashcan:a("trashcan",60033),history:a("history",60034),clock:a("clock",60034),folder:a("folder",60035),fileDirectory:a("file-directory",60035),symbolFolder:a("symbol-folder",60035),logoGithub:a("logo-github",60036),markGithub:a("mark-github",60036),github:a("github",60036),terminal:a("terminal",60037),console:a("console",60037),repl:a("repl",60037),zap:a("zap",60038),symbolEvent:a("symbol-event",60038),error:a("error",60039),stop:a("stop",60039),variable:a("variable",60040),symbolVariable:a("symbol-variable",60040),array:a("array",60042),symbolArray:a("symbol-array",60042),symbolModule:a("symbol-module",60043),symbolPackage:a("symbol-package",60043),symbolNamespace:a("symbol-namespace",60043),symbolObject:a("symbol-object",60043),symbolMethod:a("symbol-method",60044),symbolFunction:a("symbol-function",60044),symbolConstructor:a("symbol-constructor",60044),symbolBoolean:a("symbol-boolean",60047),symbolNull:a("symbol-null",60047),symbolNumeric:a("symbol-numeric",60048),symbolNumber:a("symbol-number",60048),symbolStructure:a("symbol-structure",60049),symbolStruct:a("symbol-struct",60049),symbolParameter:a("symbol-parameter",60050),symbolTypeParameter:a("symbol-type-parameter",60050),symbolKey:a("symbol-key",60051),symbolText:a("symbol-text",60051),symbolReference:a("symbol-reference",60052),goToFile:a("go-to-file",60052),symbolEnum:a("symbol-enum",60053),symbolValue:a("symbol-value",60053),symbolRuler:a("symbol-ruler",60054),symbolUnit:a("symbol-unit",60054),activateBreakpoints:a("activate-breakpoints",60055),archive:a("archive",60056),arrowBoth:a("arrow-both",60057),arrowDown:a("arrow-down",60058),arrowLeft:a("arrow-left",60059),arrowRight:a("arrow-right",60060),arrowSmallDown:a("arrow-small-down",60061),arrowSmallLeft:a("arrow-small-left",60062),arrowSmallRight:a("arrow-small-right",60063),arrowSmallUp:a("arrow-small-up",60064),arrowUp:a("arrow-up",60065),bell:a("bell",60066),bold:a("bold",60067),book:a("book",60068),bookmark:a("bookmark",60069),debugBreakpointConditionalUnverified:a("debug-breakpoint-conditional-unverified",60070),debugBreakpointConditional:a("debug-breakpoint-conditional",60071),debugBreakpointConditionalDisabled:a("debug-breakpoint-conditional-disabled",60071),debugBreakpointDataUnverified:a("debug-breakpoint-data-unverified",60072),debugBreakpointData:a("debug-breakpoint-data",60073),debugBreakpointDataDisabled:a("debug-breakpoint-data-disabled",60073),debugBreakpointLogUnverified:a("debug-breakpoint-log-unverified",60074),debugBreakpointLog:a("debug-breakpoint-log",60075),debugBreakpointLogDisabled:a("debug-breakpoint-log-disabled",60075),briefcase:a("briefcase",60076),broadcast:a("broadcast",60077),browser:a("browser",60078),bug:a("bug",60079),calendar:a("calendar",60080),caseSensitive:a("case-sensitive",60081),check:a("check",60082),checklist:a("checklist",60083),chevronDown:a("chevron-down",60084),dropDownButton:a("drop-down-button",60084),chevronLeft:a("chevron-left",60085),chevronRight:a("chevron-right",60086),chevronUp:a("chevron-up",60087),chromeClose:a("chrome-close",60088),chromeMaximize:a("chrome-maximize",60089),chromeMinimize:a("chrome-minimize",60090),chromeRestore:a("chrome-restore",60091),circle:a("circle",60092),circleOutline:a("circle-outline",60092),debugBreakpointUnverified:a("debug-breakpoint-unverified",60092),circleSlash:a("circle-slash",60093),circuitBoard:a("circuit-board",60094),clearAll:a("clear-all",60095),clippy:a("clippy",60096),closeAll:a("close-all",60097),cloudDownload:a("cloud-download",60098),cloudUpload:a("cloud-upload",60099),code:a("code",60100),collapseAll:a("collapse-all",60101),colorMode:a("color-mode",60102),commentDiscussion:a("comment-discussion",60103),compareChanges:a("compare-changes",60157),creditCard:a("credit-card",60105),dash:a("dash",60108),dashboard:a("dashboard",60109),database:a("database",60110),debugContinue:a("debug-continue",60111),debugDisconnect:a("debug-disconnect",60112),debugPause:a("debug-pause",60113),debugRestart:a("debug-restart",60114),debugStart:a("debug-start",60115),debugStepInto:a("debug-step-into",60116),debugStepOut:a("debug-step-out",60117),debugStepOver:a("debug-step-over",60118),debugStop:a("debug-stop",60119),debug:a("debug",60120),deviceCameraVideo:a("device-camera-video",60121),deviceCamera:a("device-camera",60122),deviceMobile:a("device-mobile",60123),diffAdded:a("diff-added",60124),diffIgnored:a("diff-ignored",60125),diffModified:a("diff-modified",60126),diffRemoved:a("diff-removed",60127),diffRenamed:a("diff-renamed",60128),diff:a("diff",60129),discard:a("discard",60130),editorLayout:a("editor-layout",60131),emptyWindow:a("empty-window",60132),exclude:a("exclude",60133),extensions:a("extensions",60134),eyeClosed:a("eye-closed",60135),fileBinary:a("file-binary",60136),fileCode:a("file-code",60137),fileMedia:a("file-media",60138),filePdf:a("file-pdf",60139),fileSubmodule:a("file-submodule",60140),fileSymlinkDirectory:a("file-symlink-directory",60141),fileSymlinkFile:a("file-symlink-file",60142),fileZip:a("file-zip",60143),files:a("files",60144),filter:a("filter",60145),flame:a("flame",60146),foldDown:a("fold-down",60147),foldUp:a("fold-up",60148),fold:a("fold",60149),folderActive:a("folder-active",60150),folderOpened:a("folder-opened",60151),gear:a("gear",60152),gift:a("gift",60153),gistSecret:a("gist-secret",60154),gist:a("gist",60155),gitCommit:a("git-commit",60156),gitCompare:a("git-compare",60157),gitMerge:a("git-merge",60158),githubAction:a("github-action",60159),githubAlt:a("github-alt",60160),globe:a("globe",60161),grabber:a("grabber",60162),graph:a("graph",60163),gripper:a("gripper",60164),heart:a("heart",60165),home:a("home",60166),horizontalRule:a("horizontal-rule",60167),hubot:a("hubot",60168),inbox:a("inbox",60169),issueClosed:a("issue-closed",60324),issueReopened:a("issue-reopened",60171),issues:a("issues",60172),italic:a("italic",60173),jersey:a("jersey",60174),json:a("json",60175),bracket:a("bracket",60175),kebabVertical:a("kebab-vertical",60176),key:a("key",60177),law:a("law",60178),lightbulbAutofix:a("lightbulb-autofix",60179),linkExternal:a("link-external",60180),link:a("link",60181),listOrdered:a("list-ordered",60182),listUnordered:a("list-unordered",60183),liveShare:a("live-share",60184),loading:a("loading",60185),location:a("location",60186),mailRead:a("mail-read",60187),mail:a("mail",60188),markdown:a("markdown",60189),megaphone:a("megaphone",60190),mention:a("mention",60191),milestone:a("milestone",60192),gitPullRequestMilestone:a("git-pull-request-milestone",60192),mortarBoard:a("mortar-board",60193),move:a("move",60194),multipleWindows:a("multiple-windows",60195),mute:a("mute",60196),noNewline:a("no-newline",60197),note:a("note",60198),octoface:a("octoface",60199),openPreview:a("open-preview",60200),package:a("package",60201),paintcan:a("paintcan",60202),pin:a("pin",60203),play:a("play",60204),run:a("run",60204),plug:a("plug",60205),preserveCase:a("preserve-case",60206),preview:a("preview",60207),project:a("project",60208),pulse:a("pulse",60209),question:a("question",60210),quote:a("quote",60211),radioTower:a("radio-tower",60212),reactions:a("reactions",60213),references:a("references",60214),refresh:a("refresh",60215),regex:a("regex",60216),remoteExplorer:a("remote-explorer",60217),remote:a("remote",60218),remove:a("remove",60219),replaceAll:a("replace-all",60220),replace:a("replace",60221),repoClone:a("repo-clone",60222),repoForcePush:a("repo-force-push",60223),repoPull:a("repo-pull",60224),repoPush:a("repo-push",60225),report:a("report",60226),requestChanges:a("request-changes",60227),rocket:a("rocket",60228),rootFolderOpened:a("root-folder-opened",60229),rootFolder:a("root-folder",60230),rss:a("rss",60231),ruby:a("ruby",60232),saveAll:a("save-all",60233),saveAs:a("save-as",60234),save:a("save",60235),screenFull:a("screen-full",60236),screenNormal:a("screen-normal",60237),searchStop:a("search-stop",60238),server:a("server",60240),settingsGear:a("settings-gear",60241),settings:a("settings",60242),shield:a("shield",60243),smiley:a("smiley",60244),sortPrecedence:a("sort-precedence",60245),splitHorizontal:a("split-horizontal",60246),splitVertical:a("split-vertical",60247),squirrel:a("squirrel",60248),starFull:a("star-full",60249),starHalf:a("star-half",60250),symbolClass:a("symbol-class",60251),symbolColor:a("symbol-color",60252),symbolCustomColor:a("symbol-customcolor",60252),symbolConstant:a("symbol-constant",60253),symbolEnumMember:a("symbol-enum-member",60254),symbolField:a("symbol-field",60255),symbolFile:a("symbol-file",60256),symbolInterface:a("symbol-interface",60257),symbolKeyword:a("symbol-keyword",60258),symbolMisc:a("symbol-misc",60259),symbolOperator:a("symbol-operator",60260),symbolProperty:a("symbol-property",60261),wrench:a("wrench",60261),wrenchSubaction:a("wrench-subaction",60261),symbolSnippet:a("symbol-snippet",60262),tasklist:a("tasklist",60263),telescope:a("telescope",60264),textSize:a("text-size",60265),threeBars:a("three-bars",60266),thumbsdown:a("thumbsdown",60267),thumbsup:a("thumbsup",60268),tools:a("tools",60269),triangleDown:a("triangle-down",60270),triangleLeft:a("triangle-left",60271),triangleRight:a("triangle-right",60272),triangleUp:a("triangle-up",60273),twitter:a("twitter",60274),unfold:a("unfold",60275),unlock:a("unlock",60276),unmute:a("unmute",60277),unverified:a("unverified",60278),verified:a("verified",60279),versions:a("versions",60280),vmActive:a("vm-active",60281),vmOutline:a("vm-outline",60282),vmRunning:a("vm-running",60283),watch:a("watch",60284),whitespace:a("whitespace",60285),wholeWord:a("whole-word",60286),window:a("window",60287),wordWrap:a("word-wrap",60288),zoomIn:a("zoom-in",60289),zoomOut:a("zoom-out",60290),listFilter:a("list-filter",60291),listFlat:a("list-flat",60292),listSelection:a("list-selection",60293),selection:a("selection",60293),listTree:a("list-tree",60294),debugBreakpointFunctionUnverified:a("debug-breakpoint-function-unverified",60295),debugBreakpointFunction:a("debug-breakpoint-function",60296),debugBreakpointFunctionDisabled:a("debug-breakpoint-function-disabled",60296),debugStackframeActive:a("debug-stackframe-active",60297),circleSmallFilled:a("circle-small-filled",60298),debugStackframeDot:a("debug-stackframe-dot",60298),debugStackframe:a("debug-stackframe",60299),debugStackframeFocused:a("debug-stackframe-focused",60299),debugBreakpointUnsupported:a("debug-breakpoint-unsupported",60300),symbolString:a("symbol-string",60301),debugReverseContinue:a("debug-reverse-continue",60302),debugStepBack:a("debug-step-back",60303),debugRestartFrame:a("debug-restart-frame",60304),callIncoming:a("call-incoming",60306),callOutgoing:a("call-outgoing",60307),menu:a("menu",60308),expandAll:a("expand-all",60309),feedback:a("feedback",60310),gitPullRequestReviewer:a("git-pull-request-reviewer",60310),groupByRefType:a("group-by-ref-type",60311),ungroupByRefType:a("ungroup-by-ref-type",60312),account:a("account",60313),gitPullRequestAssignee:a("git-pull-request-assignee",60313),bellDot:a("bell-dot",60314),debugConsole:a("debug-console",60315),library:a("library",60316),output:a("output",60317),runAll:a("run-all",60318),syncIgnored:a("sync-ignored",60319),pinned:a("pinned",60320),githubInverted:a("github-inverted",60321),debugAlt:a("debug-alt",60305),serverProcess:a("server-process",60322),serverEnvironment:a("server-environment",60323),pass:a("pass",60324),stopCircle:a("stop-circle",60325),playCircle:a("play-circle",60326),record:a("record",60327),debugAltSmall:a("debug-alt-small",60328),vmConnect:a("vm-connect",60329),cloud:a("cloud",60330),merge:a("merge",60331),exportIcon:a("export",60332),graphLeft:a("graph-left",60333),magnet:a("magnet",60334),notebook:a("notebook",60335),redo:a("redo",60336),checkAll:a("check-all",60337),pinnedDirty:a("pinned-dirty",60338),passFilled:a("pass-filled",60339),circleLargeFilled:a("circle-large-filled",60340),circleLarge:a("circle-large",60341),circleLargeOutline:a("circle-large-outline",60341),combine:a("combine",60342),gather:a("gather",60342),table:a("table",60343),variableGroup:a("variable-group",60344),typeHierarchy:a("type-hierarchy",60345),typeHierarchySub:a("type-hierarchy-sub",60346),typeHierarchySuper:a("type-hierarchy-super",60347),gitPullRequestCreate:a("git-pull-request-create",60348),runAbove:a("run-above",60349),runBelow:a("run-below",60350),notebookTemplate:a("notebook-template",60351),debugRerun:a("debug-rerun",60352),workspaceTrusted:a("workspace-trusted",60353),workspaceUntrusted:a("workspace-untrusted",60354),workspaceUnspecified:a("workspace-unspecified",60355),terminalCmd:a("terminal-cmd",60356),terminalDebian:a("terminal-debian",60357),terminalLinux:a("terminal-linux",60358),terminalPowershell:a("terminal-powershell",60359),terminalTmux:a("terminal-tmux",60360),terminalUbuntu:a("terminal-ubuntu",60361),terminalBash:a("terminal-bash",60362),arrowSwap:a("arrow-swap",60363),copy:a("copy",60364),personAdd:a("person-add",60365),filterFilled:a("filter-filled",60366),wand:a("wand",60367),debugLineByLine:a("debug-line-by-line",60368),inspect:a("inspect",60369),layers:a("layers",60370),layersDot:a("layers-dot",60371),layersActive:a("layers-active",60372),compass:a("compass",60373),compassDot:a("compass-dot",60374),compassActive:a("compass-active",60375),azure:a("azure",60376),issueDraft:a("issue-draft",60377),gitPullRequestClosed:a("git-pull-request-closed",60378),gitPullRequestDraft:a("git-pull-request-draft",60379),debugAll:a("debug-all",60380),debugCoverage:a("debug-coverage",60381),runErrors:a("run-errors",60382),folderLibrary:a("folder-library",60383),debugContinueSmall:a("debug-continue-small",60384),beakerStop:a("beaker-stop",60385),graphLine:a("graph-line",60386),graphScatter:a("graph-scatter",60387),pieChart:a("pie-chart",60388),bracketDot:a("bracket-dot",60389),bracketError:a("bracket-error",60390),lockSmall:a("lock-small",60391),azureDevops:a("azure-devops",60392),verifiedFilled:a("verified-filled",60393),newLine:a("newline",60394),layout:a("layout",60395),layoutActivitybarLeft:a("layout-activitybar-left",60396),layoutActivitybarRight:a("layout-activitybar-right",60397),layoutPanelLeft:a("layout-panel-left",60398),layoutPanelCenter:a("layout-panel-center",60399),layoutPanelJustify:a("layout-panel-justify",60400),layoutPanelRight:a("layout-panel-right",60401),layoutPanel:a("layout-panel",60402),layoutSidebarLeft:a("layout-sidebar-left",60403),layoutSidebarRight:a("layout-sidebar-right",60404),layoutStatusbar:a("layout-statusbar",60405),layoutMenubar:a("layout-menubar",60406),layoutCentered:a("layout-centered",60407),layoutSidebarRightOff:a("layout-sidebar-right-off",60416),layoutPanelOff:a("layout-panel-off",60417),layoutSidebarLeftOff:a("layout-sidebar-left-off",60418),target:a("target",60408),indent:a("indent",60409),recordSmall:a("record-small",60410),errorSmall:a("error-small",60411),arrowCircleDown:a("arrow-circle-down",60412),arrowCircleLeft:a("arrow-circle-left",60413),arrowCircleRight:a("arrow-circle-right",60414),arrowCircleUp:a("arrow-circle-up",60415),heartFilled:a("heart-filled",60420),map:a("map",60421),mapFilled:a("map-filled",60422),circleSmall:a("circle-small",60423),bellSlash:a("bell-slash",60424),bellSlashDot:a("bell-slash-dot",60425),commentUnresolved:a("comment-unresolved",60426),gitPullRequestGoToChanges:a("git-pull-request-go-to-changes",60427),gitPullRequestNewChanges:a("git-pull-request-new-changes",60428),searchFuzzy:a("search-fuzzy",60429),commentDraft:a("comment-draft",60430),send:a("send",60431),sparkle:a("sparkle",60432),insert:a("insert",60433),mic:a("mic",60434),thumbsDownFilled:a("thumbsdown-filled",60435),thumbsUpFilled:a("thumbsup-filled",60436),coffee:a("coffee",60437),snake:a("snake",60438),game:a("game",60439),vr:a("vr",60440),chip:a("chip",60441),piano:a("piano",60442),music:a("music",60443),micFilled:a("mic-filled",60444),gitFetch:a("git-fetch",60445),copilot:a("copilot",60446),lightbulbSparkle:a("lightbulb-sparkle",60447),lightbulbSparkleAutofix:a("lightbulb-sparkle-autofix",60447),robot:a("robot",60448),sparkleFilled:a("sparkle-filled",60449),diffSingle:a("diff-single",60450),diffMultiple:a("diff-multiple",60451),dialogError:a("dialog-error","error"),dialogWarning:a("dialog-warning","warning"),dialogInfo:a("dialog-info","info"),dialogClose:a("dialog-close","close"),treeItemExpanded:a("tree-item-expanded","chevron-down"),treeFilterOnTypeOn:a("tree-filter-on-type-on","list-filter"),treeFilterOnTypeOff:a("tree-filter-on-type-off","list-selection"),treeFilterClear:a("tree-filter-clear","close"),treeItemLoading:a("tree-item-loading","loading"),menuSelection:a("menu-selection","check"),menuSubmenu:a("menu-submenu","chevron-right"),menuBarMore:a("menubar-more","more"),scrollbarButtonLeft:a("scrollbar-button-left","triangle-left"),scrollbarButtonRight:a("scrollbar-button-right","triangle-right"),scrollbarButtonUp:a("scrollbar-button-up","triangle-up"),scrollbarButtonDown:a("scrollbar-button-down","triangle-down"),toolBarMore:a("toolbar-more","more"),quickInputBack:a("quick-input-back","arrow-left")};var Yt=class{constructor(){this._tokenizationSupports=new Map,this._factories=new Map,this._onDidChange=new Z,this.onDidChange=this._onDidChange.event,this._colorMap=null}handleChange(t){this._onDidChange.fire({changedLanguages:t,changedColorMap:!1})}register(t,n){return this._tokenizationSupports.set(t,n),this.handleChange([t]),Pe(()=>{this._tokenizationSupports.get(t)===n&&(this._tokenizationSupports.delete(t),this.handleChange([t]))})}get(t){return this._tokenizationSupports.get(t)||null}registerFactory(t,n){var r;(r=this._factories.get(t))===null||r===void 0||r.dispose();let i=new nr(this,t,n);return this._factories.set(t,i),Pe(()=>{let s=this._factories.get(t);!s||s!==i||(this._factories.delete(t),s.dispose())})}async getOrCreate(t){let n=this.get(t);if(n)return n;let r=this._factories.get(t);return!r||r.isResolved?null:(await r.resolve(),this.get(t))}isResolved(t){if(this.get(t))return!0;let r=this._factories.get(t);return!!(!r||r.isResolved)}setColorMap(t){this._colorMap=t,this._onDidChange.fire({changedLanguages:Array.from(this._tokenizationSupports.keys()),changedColorMap:!0})}getColorMap(){return this._colorMap}getDefaultBackground(){return this._colorMap&&this._colorMap.length>2?this._colorMap[2]:null}},nr=class extends ae{get isResolved(){return this._isResolved}constructor(t,n,r){super(),this._registry=t,this._languageId=n,this._factory=r,this._isDisposed=!1,this._resolvePromise=null,this._isResolved=!1}dispose(){this._isDisposed=!0,super.dispose()}async resolve(){return this._resolvePromise||(this._resolvePromise=this._create()),this._resolvePromise}async _create(){let t=await this._factory.tokenizationSupport;this._isResolved=!0,t&&!this._isDisposed&&this._register(this._registry.register(this._languageId,t))}};var Zt=class{constructor(t,n,r){this.offset=t,this.type=n,this.language=r,this._tokenBrand=void 0}toString(){return"("+this.offset+", "+this.type+")"}};var l1;(function(e){let t=new Map;t.set(0,E.symbolMethod),t.set(1,E.symbolFunction),t.set(2,E.symbolConstructor),t.set(3,E.symbolField),t.set(4,E.symbolVariable),t.set(5,E.symbolClass),t.set(6,E.symbolStruct),t.set(7,E.symbolInterface),t.set(8,E.symbolModule),t.set(9,E.symbolProperty),t.set(10,E.symbolEvent),t.set(11,E.symbolOperator),t.set(12,E.symbolUnit),t.set(13,E.symbolValue),t.set(15,E.symbolEnum),t.set(14,E.symbolConstant),t.set(15,E.symbolEnum),t.set(16,E.symbolEnumMember),t.set(17,E.symbolKeyword),t.set(27,E.symbolSnippet),t.set(18,E.symbolText),t.set(19,E.symbolColor),t.set(20,E.symbolFile),t.set(21,E.symbolReference),t.set(22,E.symbolCustomColor),t.set(23,E.symbolFolder),t.set(24,E.symbolTypeParameter),t.set(25,E.account),t.set(26,E.issues);function n(s){let o=t.get(s);return o||(console.info("No codicon found for CompletionItemKind "+s),o=E.symbolProperty),o}e.toIcon=n;let r=new Map;r.set("method",0),r.set("function",1),r.set("constructor",2),r.set("field",3),r.set("variable",4),r.set("class",5),r.set("struct",6),r.set("interface",7),r.set("module",8),r.set("property",9),r.set("event",10),r.set("operator",11),r.set("unit",12),r.set("value",13),r.set("constant",14),r.set("enum",15),r.set("enum-member",16),r.set("enumMember",16),r.set("keyword",17),r.set("snippet",27),r.set("text",18),r.set("color",19),r.set("file",20),r.set("reference",21),r.set("customcolor",22),r.set("folder",23),r.set("type-parameter",24),r.set("typeParameter",24),r.set("account",25),r.set("issue",26);function i(s,o){let l=r.get(s);return typeof l>"u"&&!o&&(l=9),l}e.fromString=i})(l1||(l1={}));var u1;(function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"})(u1||(u1={}));var c1;(function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"})(c1||(c1={}));var h1;(function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"})(h1||(h1={}));var Ba={17:W("Array","array"),16:W("Boolean","boolean"),4:W("Class","class"),13:W("Constant","constant"),8:W("Constructor","constructor"),9:W("Enum","enumeration"),21:W("EnumMember","enumeration member"),23:W("Event","event"),7:W("Field","field"),0:W("File","file"),11:W("Function","function"),10:W("Interface","interface"),19:W("Key","key"),5:W("Method","method"),1:W("Module","module"),2:W("Namespace","namespace"),20:W("Null","null"),15:W("Number","number"),18:W("Object","object"),24:W("Operator","operator"),3:W("Package","package"),6:W("Property","property"),14:W("String","string"),22:W("Struct","struct"),25:W("TypeParameter","type parameter"),12:W("Variable","variable")};var f1;(function(e){let t=new Map;t.set(0,E.symbolFile),t.set(1,E.symbolModule),t.set(2,E.symbolNamespace),t.set(3,E.symbolPackage),t.set(4,E.symbolClass),t.set(5,E.symbolMethod),t.set(6,E.symbolProperty),t.set(7,E.symbolField),t.set(8,E.symbolConstructor),t.set(9,E.symbolEnum),t.set(10,E.symbolInterface),t.set(11,E.symbolFunction),t.set(12,E.symbolVariable),t.set(13,E.symbolConstant),t.set(14,E.symbolString),t.set(15,E.symbolNumber),t.set(16,E.symbolBoolean),t.set(17,E.symbolArray),t.set(18,E.symbolObject),t.set(19,E.symbolKey),t.set(20,E.symbolNull),t.set(21,E.symbolEnumMember),t.set(22,E.symbolStruct),t.set(23,E.symbolEvent),t.set(24,E.symbolOperator),t.set(25,E.symbolTypeParameter);function n(r){let i=t.get(r);return i||(console.info("No codicon found for SymbolKind "+r),i=E.symbolProperty),i}e.toIcon=n})(f1||(f1={}));var ye=class e{static fromValue(t){switch(t){case"comment":return e.Comment;case"imports":return e.Imports;case"region":return e.Region}return new e(t)}constructor(t){this.value=t}};ye.Comment=new ye("comment");ye.Imports=new ye("imports");ye.Region=new ye("region");var d1;(function(e){function t(n){return!n||typeof n!="object"?!1:typeof n.id=="string"&&typeof n.title=="string"}e.is=t})(d1||(d1={}));var m1;(function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"})(m1||(m1={}));var Va=new Yt;var g1;(function(e){e[e.Unknown=0]="Unknown",e[e.Disabled=1]="Disabled",e[e.Enabled=2]="Enabled"})(g1||(g1={}));var p1;(function(e){e[e.Invoke=1]="Invoke",e[e.Auto=2]="Auto"})(p1||(p1={}));var b1;(function(e){e[e.None=0]="None",e[e.KeepWhitespace=1]="KeepWhitespace",e[e.InsertAsSnippet=4]="InsertAsSnippet"})(b1||(b1={}));var x1;(function(e){e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Snippet=27]="Snippet"})(x1||(x1={}));var _1;(function(e){e[e.Deprecated=1]="Deprecated"})(_1||(_1={}));var v1;(function(e){e[e.Invoke=0]="Invoke",e[e.TriggerCharacter=1]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"})(v1||(v1={}));var L1;(function(e){e[e.EXACT=0]="EXACT",e[e.ABOVE=1]="ABOVE",e[e.BELOW=2]="BELOW"})(L1||(L1={}));var w1;(function(e){e[e.NotSet=0]="NotSet",e[e.ContentFlush=1]="ContentFlush",e[e.RecoverFromMarkers=2]="RecoverFromMarkers",e[e.Explicit=3]="Explicit",e[e.Paste=4]="Paste",e[e.Undo=5]="Undo",e[e.Redo=6]="Redo"})(w1||(w1={}));var N1;(function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(N1||(N1={}));var S1;(function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"})(S1||(S1={}));var C1;(function(e){e[e.None=0]="None",e[e.Keep=1]="Keep",e[e.Brackets=2]="Brackets",e[e.Advanced=3]="Advanced",e[e.Full=4]="Full"})(C1||(C1={}));var A1;(function(e){e[e.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",e[e.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",e[e.accessibilitySupport=2]="accessibilitySupport",e[e.accessibilityPageSize=3]="accessibilityPageSize",e[e.ariaLabel=4]="ariaLabel",e[e.ariaRequired=5]="ariaRequired",e[e.autoClosingBrackets=6]="autoClosingBrackets",e[e.autoClosingComments=7]="autoClosingComments",e[e.screenReaderAnnounceInlineSuggestion=8]="screenReaderAnnounceInlineSuggestion",e[e.autoClosingDelete=9]="autoClosingDelete",e[e.autoClosingOvertype=10]="autoClosingOvertype",e[e.autoClosingQuotes=11]="autoClosingQuotes",e[e.autoIndent=12]="autoIndent",e[e.automaticLayout=13]="automaticLayout",e[e.autoSurround=14]="autoSurround",e[e.bracketPairColorization=15]="bracketPairColorization",e[e.guides=16]="guides",e[e.codeLens=17]="codeLens",e[e.codeLensFontFamily=18]="codeLensFontFamily",e[e.codeLensFontSize=19]="codeLensFontSize",e[e.colorDecorators=20]="colorDecorators",e[e.colorDecoratorsLimit=21]="colorDecoratorsLimit",e[e.columnSelection=22]="columnSelection",e[e.comments=23]="comments",e[e.contextmenu=24]="contextmenu",e[e.copyWithSyntaxHighlighting=25]="copyWithSyntaxHighlighting",e[e.cursorBlinking=26]="cursorBlinking",e[e.cursorSmoothCaretAnimation=27]="cursorSmoothCaretAnimation",e[e.cursorStyle=28]="cursorStyle",e[e.cursorSurroundingLines=29]="cursorSurroundingLines",e[e.cursorSurroundingLinesStyle=30]="cursorSurroundingLinesStyle",e[e.cursorWidth=31]="cursorWidth",e[e.disableLayerHinting=32]="disableLayerHinting",e[e.disableMonospaceOptimizations=33]="disableMonospaceOptimizations",e[e.domReadOnly=34]="domReadOnly",e[e.dragAndDrop=35]="dragAndDrop",e[e.dropIntoEditor=36]="dropIntoEditor",e[e.emptySelectionClipboard=37]="emptySelectionClipboard",e[e.experimentalWhitespaceRendering=38]="experimentalWhitespaceRendering",e[e.extraEditorClassName=39]="extraEditorClassName",e[e.fastScrollSensitivity=40]="fastScrollSensitivity",e[e.find=41]="find",e[e.fixedOverflowWidgets=42]="fixedOverflowWidgets",e[e.folding=43]="folding",e[e.foldingStrategy=44]="foldingStrategy",e[e.foldingHighlight=45]="foldingHighlight",e[e.foldingImportsByDefault=46]="foldingImportsByDefault",e[e.foldingMaximumRegions=47]="foldingMaximumRegions",e[e.unfoldOnClickAfterEndOfLine=48]="unfoldOnClickAfterEndOfLine",e[e.fontFamily=49]="fontFamily",e[e.fontInfo=50]="fontInfo",e[e.fontLigatures=51]="fontLigatures",e[e.fontSize=52]="fontSize",e[e.fontWeight=53]="fontWeight",e[e.fontVariations=54]="fontVariations",e[e.formatOnPaste=55]="formatOnPaste",e[e.formatOnType=56]="formatOnType",e[e.glyphMargin=57]="glyphMargin",e[e.gotoLocation=58]="gotoLocation",e[e.hideCursorInOverviewRuler=59]="hideCursorInOverviewRuler",e[e.hover=60]="hover",e[e.inDiffEditor=61]="inDiffEditor",e[e.inlineSuggest=62]="inlineSuggest",e[e.letterSpacing=63]="letterSpacing",e[e.lightbulb=64]="lightbulb",e[e.lineDecorationsWidth=65]="lineDecorationsWidth",e[e.lineHeight=66]="lineHeight",e[e.lineNumbers=67]="lineNumbers",e[e.lineNumbersMinChars=68]="lineNumbersMinChars",e[e.linkedEditing=69]="linkedEditing",e[e.links=70]="links",e[e.matchBrackets=71]="matchBrackets",e[e.minimap=72]="minimap",e[e.mouseStyle=73]="mouseStyle",e[e.mouseWheelScrollSensitivity=74]="mouseWheelScrollSensitivity",e[e.mouseWheelZoom=75]="mouseWheelZoom",e[e.multiCursorMergeOverlapping=76]="multiCursorMergeOverlapping",e[e.multiCursorModifier=77]="multiCursorModifier",e[e.multiCursorPaste=78]="multiCursorPaste",e[e.multiCursorLimit=79]="multiCursorLimit",e[e.occurrencesHighlight=80]="occurrencesHighlight",e[e.overviewRulerBorder=81]="overviewRulerBorder",e[e.overviewRulerLanes=82]="overviewRulerLanes",e[e.padding=83]="padding",e[e.pasteAs=84]="pasteAs",e[e.parameterHints=85]="parameterHints",e[e.peekWidgetDefaultFocus=86]="peekWidgetDefaultFocus",e[e.definitionLinkOpensInPeek=87]="definitionLinkOpensInPeek",e[e.quickSuggestions=88]="quickSuggestions",e[e.quickSuggestionsDelay=89]="quickSuggestionsDelay",e[e.readOnly=90]="readOnly",e[e.readOnlyMessage=91]="readOnlyMessage",e[e.renameOnType=92]="renameOnType",e[e.renderControlCharacters=93]="renderControlCharacters",e[e.renderFinalNewline=94]="renderFinalNewline",e[e.renderLineHighlight=95]="renderLineHighlight",e[e.renderLineHighlightOnlyWhenFocus=96]="renderLineHighlightOnlyWhenFocus",e[e.renderValidationDecorations=97]="renderValidationDecorations",e[e.renderWhitespace=98]="renderWhitespace",e[e.revealHorizontalRightPadding=99]="revealHorizontalRightPadding",e[e.roundedSelection=100]="roundedSelection",e[e.rulers=101]="rulers",e[e.scrollbar=102]="scrollbar",e[e.scrollBeyondLastColumn=103]="scrollBeyondLastColumn",e[e.scrollBeyondLastLine=104]="scrollBeyondLastLine",e[e.scrollPredominantAxis=105]="scrollPredominantAxis",e[e.selectionClipboard=106]="selectionClipboard",e[e.selectionHighlight=107]="selectionHighlight",e[e.selectOnLineNumbers=108]="selectOnLineNumbers",e[e.showFoldingControls=109]="showFoldingControls",e[e.showUnused=110]="showUnused",e[e.snippetSuggestions=111]="snippetSuggestions",e[e.smartSelect=112]="smartSelect",e[e.smoothScrolling=113]="smoothScrolling",e[e.stickyScroll=114]="stickyScroll",e[e.stickyTabStops=115]="stickyTabStops",e[e.stopRenderingLineAfter=116]="stopRenderingLineAfter",e[e.suggest=117]="suggest",e[e.suggestFontSize=118]="suggestFontSize",e[e.suggestLineHeight=119]="suggestLineHeight",e[e.suggestOnTriggerCharacters=120]="suggestOnTriggerCharacters",e[e.suggestSelection=121]="suggestSelection",e[e.tabCompletion=122]="tabCompletion",e[e.tabIndex=123]="tabIndex",e[e.unicodeHighlighting=124]="unicodeHighlighting",e[e.unusualLineTerminators=125]="unusualLineTerminators",e[e.useShadowDOM=126]="useShadowDOM",e[e.useTabStops=127]="useTabStops",e[e.wordBreak=128]="wordBreak",e[e.wordSeparators=129]="wordSeparators",e[e.wordWrap=130]="wordWrap",e[e.wordWrapBreakAfterCharacters=131]="wordWrapBreakAfterCharacters",e[e.wordWrapBreakBeforeCharacters=132]="wordWrapBreakBeforeCharacters",e[e.wordWrapColumn=133]="wordWrapColumn",e[e.wordWrapOverride1=134]="wordWrapOverride1",e[e.wordWrapOverride2=135]="wordWrapOverride2",e[e.wrappingIndent=136]="wrappingIndent",e[e.wrappingStrategy=137]="wrappingStrategy",e[e.showDeprecated=138]="showDeprecated",e[e.inlayHints=139]="inlayHints",e[e.editorClassName=140]="editorClassName",e[e.pixelRatio=141]="pixelRatio",e[e.tabFocusMode=142]="tabFocusMode",e[e.layoutInfo=143]="layoutInfo",e[e.wrappingInfo=144]="wrappingInfo",e[e.defaultColorDecorators=145]="defaultColorDecorators",e[e.colorDecoratorsActivatedOn=146]="colorDecoratorsActivatedOn",e[e.inlineCompletionsAccessibilityVerbose=147]="inlineCompletionsAccessibilityVerbose"})(A1||(A1={}));var R1;(function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(R1||(R1={}));var y1;(function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"})(y1||(y1={}));var E1;(function(e){e[e.Left=1]="Left",e[e.Right=2]="Right"})(E1||(E1={}));var M1;(function(e){e[e.None=0]="None",e[e.Indent=1]="Indent",e[e.IndentOutdent=2]="IndentOutdent",e[e.Outdent=3]="Outdent"})(M1||(M1={}));var F1;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(F1||(F1={}));var k1;(function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"})(k1||(k1={}));var D1;(function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"})(D1||(D1={}));var Kt;(function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.Unknown=0]="Unknown",e[e.Backspace=1]="Backspace",e[e.Tab=2]="Tab",e[e.Enter=3]="Enter",e[e.Shift=4]="Shift",e[e.Ctrl=5]="Ctrl",e[e.Alt=6]="Alt",e[e.PauseBreak=7]="PauseBreak",e[e.CapsLock=8]="CapsLock",e[e.Escape=9]="Escape",e[e.Space=10]="Space",e[e.PageUp=11]="PageUp",e[e.PageDown=12]="PageDown",e[e.End=13]="End",e[e.Home=14]="Home",e[e.LeftArrow=15]="LeftArrow",e[e.UpArrow=16]="UpArrow",e[e.RightArrow=17]="RightArrow",e[e.DownArrow=18]="DownArrow",e[e.Insert=19]="Insert",e[e.Delete=20]="Delete",e[e.Digit0=21]="Digit0",e[e.Digit1=22]="Digit1",e[e.Digit2=23]="Digit2",e[e.Digit3=24]="Digit3",e[e.Digit4=25]="Digit4",e[e.Digit5=26]="Digit5",e[e.Digit6=27]="Digit6",e[e.Digit7=28]="Digit7",e[e.Digit8=29]="Digit8",e[e.Digit9=30]="Digit9",e[e.KeyA=31]="KeyA",e[e.KeyB=32]="KeyB",e[e.KeyC=33]="KeyC",e[e.KeyD=34]="KeyD",e[e.KeyE=35]="KeyE",e[e.KeyF=36]="KeyF",e[e.KeyG=37]="KeyG",e[e.KeyH=38]="KeyH",e[e.KeyI=39]="KeyI",e[e.KeyJ=40]="KeyJ",e[e.KeyK=41]="KeyK",e[e.KeyL=42]="KeyL",e[e.KeyM=43]="KeyM",e[e.KeyN=44]="KeyN",e[e.KeyO=45]="KeyO",e[e.KeyP=46]="KeyP",e[e.KeyQ=47]="KeyQ",e[e.KeyR=48]="KeyR",e[e.KeyS=49]="KeyS",e[e.KeyT=50]="KeyT",e[e.KeyU=51]="KeyU",e[e.KeyV=52]="KeyV",e[e.KeyW=53]="KeyW",e[e.KeyX=54]="KeyX",e[e.KeyY=55]="KeyY",e[e.KeyZ=56]="KeyZ",e[e.Meta=57]="Meta",e[e.ContextMenu=58]="ContextMenu",e[e.F1=59]="F1",e[e.F2=60]="F2",e[e.F3=61]="F3",e[e.F4=62]="F4",e[e.F5=63]="F5",e[e.F6=64]="F6",e[e.F7=65]="F7",e[e.F8=66]="F8",e[e.F9=67]="F9",e[e.F10=68]="F10",e[e.F11=69]="F11",e[e.F12=70]="F12",e[e.F13=71]="F13",e[e.F14=72]="F14",e[e.F15=73]="F15",e[e.F16=74]="F16",e[e.F17=75]="F17",e[e.F18=76]="F18",e[e.F19=77]="F19",e[e.F20=78]="F20",e[e.F21=79]="F21",e[e.F22=80]="F22",e[e.F23=81]="F23",e[e.F24=82]="F24",e[e.NumLock=83]="NumLock",e[e.ScrollLock=84]="ScrollLock",e[e.Semicolon=85]="Semicolon",e[e.Equal=86]="Equal",e[e.Comma=87]="Comma",e[e.Minus=88]="Minus",e[e.Period=89]="Period",e[e.Slash=90]="Slash",e[e.Backquote=91]="Backquote",e[e.BracketLeft=92]="BracketLeft",e[e.Backslash=93]="Backslash",e[e.BracketRight=94]="BracketRight",e[e.Quote=95]="Quote",e[e.OEM_8=96]="OEM_8",e[e.IntlBackslash=97]="IntlBackslash",e[e.Numpad0=98]="Numpad0",e[e.Numpad1=99]="Numpad1",e[e.Numpad2=100]="Numpad2",e[e.Numpad3=101]="Numpad3",e[e.Numpad4=102]="Numpad4",e[e.Numpad5=103]="Numpad5",e[e.Numpad6=104]="Numpad6",e[e.Numpad7=105]="Numpad7",e[e.Numpad8=106]="Numpad8",e[e.Numpad9=107]="Numpad9",e[e.NumpadMultiply=108]="NumpadMultiply",e[e.NumpadAdd=109]="NumpadAdd",e[e.NUMPAD_SEPARATOR=110]="NUMPAD_SEPARATOR",e[e.NumpadSubtract=111]="NumpadSubtract",e[e.NumpadDecimal=112]="NumpadDecimal",e[e.NumpadDivide=113]="NumpadDivide",e[e.KEY_IN_COMPOSITION=114]="KEY_IN_COMPOSITION",e[e.ABNT_C1=115]="ABNT_C1",e[e.ABNT_C2=116]="ABNT_C2",e[e.AudioVolumeMute=117]="AudioVolumeMute",e[e.AudioVolumeUp=118]="AudioVolumeUp",e[e.AudioVolumeDown=119]="AudioVolumeDown",e[e.BrowserSearch=120]="BrowserSearch",e[e.BrowserHome=121]="BrowserHome",e[e.BrowserBack=122]="BrowserBack",e[e.BrowserForward=123]="BrowserForward",e[e.MediaTrackNext=124]="MediaTrackNext",e[e.MediaTrackPrevious=125]="MediaTrackPrevious",e[e.MediaStop=126]="MediaStop",e[e.MediaPlayPause=127]="MediaPlayPause",e[e.LaunchMediaPlayer=128]="LaunchMediaPlayer",e[e.LaunchMail=129]="LaunchMail",e[e.LaunchApp2=130]="LaunchApp2",e[e.Clear=131]="Clear",e[e.MAX_VALUE=132]="MAX_VALUE"})(Kt||(Kt={}));var en;(function(e){e[e.Hint=1]="Hint",e[e.Info=2]="Info",e[e.Warning=4]="Warning",e[e.Error=8]="Error"})(en||(en={}));var tn;(function(e){e[e.Unnecessary=1]="Unnecessary",e[e.Deprecated=2]="Deprecated"})(tn||(tn={}));var P1;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(P1||(P1={}));var I1;(function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.TEXTAREA=1]="TEXTAREA",e[e.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",e[e.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",e[e.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",e[e.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",e[e.CONTENT_TEXT=6]="CONTENT_TEXT",e[e.CONTENT_EMPTY=7]="CONTENT_EMPTY",e[e.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",e[e.CONTENT_WIDGET=9]="CONTENT_WIDGET",e[e.OVERVIEW_RULER=10]="OVERVIEW_RULER",e[e.SCROLLBAR=11]="SCROLLBAR",e[e.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",e[e.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR"})(I1||(I1={}));var T1;(function(e){e[e.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",e[e.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",e[e.TOP_CENTER=2]="TOP_CENTER"})(T1||(T1={}));var B1;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(B1||(B1={}));var V1;(function(e){e[e.Left=0]="Left",e[e.Right=1]="Right",e[e.None=2]="None",e[e.LeftOfInjectedText=3]="LeftOfInjectedText",e[e.RightOfInjectedText=4]="RightOfInjectedText"})(V1||(V1={}));var q1;(function(e){e[e.Off=0]="Off",e[e.On=1]="On",e[e.Relative=2]="Relative",e[e.Interval=3]="Interval",e[e.Custom=4]="Custom"})(q1||(q1={}));var U1;(function(e){e[e.None=0]="None",e[e.Text=1]="Text",e[e.Blocks=2]="Blocks"})(U1||(U1={}));var W1;(function(e){e[e.Smooth=0]="Smooth",e[e.Immediate=1]="Immediate"})(W1||(W1={}));var z1;(function(e){e[e.Auto=1]="Auto",e[e.Hidden=2]="Hidden",e[e.Visible=3]="Visible"})(z1||(z1={}));var nn;(function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"})(nn||(nn={}));var O1;(function(e){e.Off="off",e.OnCode="onCode",e.On="on"})(O1||(O1={}));var H1;(function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"})(H1||(H1={}));var $1;(function(e){e[e.File=0]="File",e[e.Module=1]="Module",e[e.Namespace=2]="Namespace",e[e.Package=3]="Package",e[e.Class=4]="Class",e[e.Method=5]="Method",e[e.Property=6]="Property",e[e.Field=7]="Field",e[e.Constructor=8]="Constructor",e[e.Enum=9]="Enum",e[e.Interface=10]="Interface",e[e.Function=11]="Function",e[e.Variable=12]="Variable",e[e.Constant=13]="Constant",e[e.String=14]="String",e[e.Number=15]="Number",e[e.Boolean=16]="Boolean",e[e.Array=17]="Array",e[e.Object=18]="Object",e[e.Key=19]="Key",e[e.Null=20]="Null",e[e.EnumMember=21]="EnumMember",e[e.Struct=22]="Struct",e[e.Event=23]="Event",e[e.Operator=24]="Operator",e[e.TypeParameter=25]="TypeParameter"})($1||($1={}));var G1;(function(e){e[e.Deprecated=1]="Deprecated"})(G1||(G1={}));var j1;(function(e){e[e.Hidden=0]="Hidden",e[e.Blink=1]="Blink",e[e.Smooth=2]="Smooth",e[e.Phase=3]="Phase",e[e.Expand=4]="Expand",e[e.Solid=5]="Solid"})(j1||(j1={}));var Q1;(function(e){e[e.Line=1]="Line",e[e.Block=2]="Block",e[e.Underline=3]="Underline",e[e.LineThin=4]="LineThin",e[e.BlockOutline=5]="BlockOutline",e[e.UnderlineThin=6]="UnderlineThin"})(Q1||(Q1={}));var J1;(function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"})(J1||(J1={}));var X1;(function(e){e[e.None=0]="None",e[e.Same=1]="Same",e[e.Indent=2]="Indent",e[e.DeepIndent=3]="DeepIndent"})(X1||(X1={}));var Ve=class{static chord(t,n){return o1(t,n)}};Ve.CtrlCmd=2048;Ve.Shift=1024;Ve.Alt=512;Ve.WinCtrl=256;function Y1(){return{editor:void 0,languages:void 0,CancellationTokenSource:Qt,Emitter:Z,KeyCode:Kt,KeyMod:Ve,Position:Q,Range:q,Selection:Xt,SelectionDirection:nn,MarkerSeverity:en,MarkerTag:tn,Uri:Re,Token:Zt}}var rr=class extends Xe{constructor(t){super(0);for(let n=0,r=t.length;n<r;n++)this.set(t.charCodeAt(n),2);this.set(32,1),this.set(9,1)}};function Fs(e){let t={};return n=>(t.hasOwnProperty(n)||(t[n]=e(n)),t[n])}var ks=Fs(e=>new rr(e));var Z1;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(Z1||(Z1={}));var K1;(function(e){e[e.Left=1]="Left",e[e.Right=2]="Right"})(K1||(K1={}));var ei;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(ei||(ei={}));var ti;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(ti||(ti={}));function Ds(e,t,n,r,i){if(r===0)return!0;let s=t.charCodeAt(r-1);if(e.get(s)!==0||s===13||s===10)return!0;if(i>0){let o=t.charCodeAt(r);if(e.get(o)!==0)return!0}return!1}function Ps(e,t,n,r,i){if(r+i===n)return!0;let s=t.charCodeAt(r+i);if(e.get(s)!==0||s===13||s===10)return!0;if(i>0){let o=t.charCodeAt(r+i-1);if(e.get(o)!==0)return!0}return!1}function Is(e,t,n,r,i){return Ds(e,t,n,r,i)&&Ps(e,t,n,r,i)}var rn=class{constructor(t,n){this._wordSeparators=t,this._searchRegex=n,this._prevMatchStartIndex=-1,this._prevMatchLength=0}reset(t){this._searchRegex.lastIndex=t,this._prevMatchStartIndex=-1,this._prevMatchLength=0}next(t){let n=t.length,r;do{if(this._prevMatchStartIndex+this._prevMatchLength===n||(r=this._searchRegex.exec(t),!r))return null;let i=r.index,s=r[0].length;if(i===this._prevMatchStartIndex&&s===this._prevMatchLength){if(s===0){Dr(t,n,this._searchRegex.lastIndex)>65535?this._searchRegex.lastIndex+=2:this._searchRegex.lastIndex+=1;continue}return null}if(this._prevMatchStartIndex=i,this._prevMatchLength=s,!this._wordSeparators||Is(this._wordSeparators,t,n,i,s))return r}while(r);return null}};function ni(e,t="Unreachable"){throw new Error(t)}function Ke(e){if(!e()){debugger;e(),Et(new se("Assertion Failed"))}}function sn(e,t){let n=0;for(;n<e.length-1;){let r=e[n],i=e[n+1];if(!t(r,i))return!1;n++}return!0}var on=class{static computeUnicodeHighlights(t,n,r){let i=r?r.startLineNumber:1,s=r?r.endLineNumber:t.getLineCount(),o=new an(n),l=o.getCandidateCodePoints(),u;l==="allNonBasicAscii"?u=new RegExp("[^\\t\\n\\r\\x20-\\x7E]","g"):u=new RegExp(`${Ts(Array.from(l))}`,"g");let c=new rn(null,u),f=[],h=!1,d,g=0,p=0,m=0;e:for(let _=i,N=s;_<=N;_++){let b=t.getLineContent(_),x=b.length;c.reset(0);do if(d=c.next(b),d){let v=d.index,w=d.index+d[0].length;if(v>0){let U=b.charCodeAt(v-1);Qe(U)&&v--}if(w+1<x){let U=b.charCodeAt(w-1);Qe(U)&&w++}let R=b.substring(v,w),y=_t(v+1,jn,b,0);y&&y.endColumn<=v+1&&(y=null);let M=o.shouldHighlightNonBasicASCII(R,y?y.word:null);if(M!==0){if(M===3?g++:M===2?p++:M===1?m++:ni(M),f.length>=1e3){h=!0;break e}f.push(new q(_,v+1,_,w+1))}}while(d)}return{ranges:f,hasMore:h,ambiguousCharacterCount:g,invisibleCharacterCount:p,nonBasicAsciiCharacterCount:m}}static computeUnicodeHighlightReason(t,n){let r=new an(n);switch(r.shouldHighlightNonBasicASCII(t,null)){case 0:return null;case 2:return{kind:1};case 3:{let s=t.codePointAt(0),o=r.ambiguousCharacters.getPrimaryConfusable(s),l=de.getLocales().filter(u=>!de.getInstance(new Set([...n.allowedLocales,u])).isAmbiguous(s));return{kind:0,confusableWith:String.fromCodePoint(o),notAmbiguousInLocales:l}}case 1:return{kind:2}}}};function Ts(e,t){return`[${Er(e.map(r=>String.fromCodePoint(r)).join(""))}]`}var an=class{constructor(t){this.options=t,this.allowedCodePoints=new Set(t.allowedCodePoints),this.ambiguousCharacters=de.getInstance(new Set(t.allowedLocales))}getCandidateCodePoints(){if(this.options.nonBasicASCII)return"allNonBasicAscii";let t=new Set;if(this.options.invisibleCharacters)for(let n of Te.codePoints)ri(String.fromCodePoint(n))||t.add(n);if(this.options.ambiguousCharacters)for(let n of this.ambiguousCharacters.getConfusableCodePoints())t.add(n);for(let n of this.allowedCodePoints)t.delete(n);return t}shouldHighlightNonBasicASCII(t,n){let r=t.codePointAt(0);if(this.allowedCodePoints.has(r))return 0;if(this.options.nonBasicASCII)return 1;let i=!1,s=!1;if(n)for(let o of n){let l=o.codePointAt(0),u=Pr(o);i=i||u,!u&&!this.ambiguousCharacters.isAmbiguous(l)&&!Te.isInvisibleCharacter(l)&&(s=!0)}return!i&&s?0:this.options.invisibleCharacters&&!ri(t)&&Te.isInvisibleCharacter(r)?2:this.options.ambiguousCharacters&&this.ambiguousCharacters.isAmbiguous(r)?3:0}};function ri(e){return e===" "||e===`
`||e==="	"}var Ee=class{constructor(t,n,r){this.changes=t,this.moves=n,this.hitTimeout=r}},ln=class{constructor(t,n){this.lineRangeMapping=t,this.changes=n}};var I=class e{static addRange(t,n){let r=0;for(;r<n.length&&n[r].endExclusive<t.start;)r++;let i=r;for(;i<n.length&&n[i].start<=t.endExclusive;)i++;if(r===i)n.splice(r,0,t);else{let s=Math.min(t.start,n[r].start),o=Math.max(t.endExclusive,n[i-1].endExclusive);n.splice(r,i-r,new e(s,o))}}static tryCreate(t,n){if(!(t>n))return new e(t,n)}static ofLength(t){return new e(0,t)}static ofStartAndLength(t,n){return new e(t,t+n)}constructor(t,n){if(this.start=t,this.endExclusive=n,t>n)throw new se(`Invalid range: ${this.toString()}`)}get isEmpty(){return this.start===this.endExclusive}delta(t){return new e(this.start+t,this.endExclusive+t)}deltaStart(t){return new e(this.start+t,this.endExclusive)}deltaEnd(t){return new e(this.start,this.endExclusive+t)}get length(){return this.endExclusive-this.start}toString(){return`[${this.start}, ${this.endExclusive})`}equals(t){return this.start===t.start&&this.endExclusive===t.endExclusive}containsRange(t){return this.start<=t.start&&t.endExclusive<=this.endExclusive}contains(t){return this.start<=t&&t<this.endExclusive}join(t){return new e(Math.min(this.start,t.start),Math.max(this.endExclusive,t.endExclusive))}intersect(t){let n=Math.max(this.start,t.start),r=Math.min(this.endExclusive,t.endExclusive);if(n<=r)return new e(n,r)}isBefore(t){return this.endExclusive<=t.start}isAfter(t){return this.start>=t.endExclusive}slice(t){return t.slice(this.start,this.endExclusive)}clip(t){if(this.isEmpty)throw new se(`Invalid clipping range: ${this.toString()}`);return Math.max(this.start,Math.min(this.endExclusive-1,t))}clipCyclic(t){if(this.isEmpty)throw new se(`Invalid clipping range: ${this.toString()}`);return t<this.start?this.endExclusive-(this.start-t)%this.length:t>=this.endExclusive?this.start+(t-this.start)%this.length:t}forEach(t){for(let n=this.start;n<this.endExclusive;n++)t(n)}};function xe(e,t){let n=qe(e,t);return n===-1?void 0:e[n]}function qe(e,t,n=0,r=e.length){let i=n,s=r;for(;i<s;){let o=Math.floor((i+s)/2);t(e[o])?i=o+1:s=o}return i-1}function ii(e,t){let n=un(e,t);return n===e.length?void 0:e[n]}function un(e,t,n=0,r=e.length){let i=n,s=r;for(;i<s;){let o=Math.floor((i+s)/2);t(e[o])?s=o:i=o+1}return i}var et=class e{constructor(t){this._array=t,this._findLastMonotonousLastIdx=0}findLastMonotonous(t){if(e.assertInvariants){if(this._prevFindLastPredicate){for(let r of this._array)if(this._prevFindLastPredicate(r)&&!t(r))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this._prevFindLastPredicate=t}let n=qe(this._array,t,this._findLastMonotonousLastIdx);return this._findLastMonotonousLastIdx=n+1,n===-1?void 0:this._array[n]}};et.assertInvariants=!1;var T=class e{static fromRange(t){return new e(t.startLineNumber,t.endLineNumber)}static fromRangeInclusive(t){return new e(t.startLineNumber,t.endLineNumber+1)}static joinMany(t){if(t.length===0)return[];let n=new Ue(t[0].slice());for(let r=1;r<t.length;r++)n=n.getUnion(new Ue(t[r].slice()));return n.ranges}static ofLength(t,n){return new e(t,t+n)}static deserialize(t){return new e(t[0],t[1])}constructor(t,n){if(t>n)throw new se(`startLineNumber ${t} cannot be after endLineNumberExclusive ${n}`);this.startLineNumber=t,this.endLineNumberExclusive=n}contains(t){return this.startLineNumber<=t&&t<this.endLineNumberExclusive}get isEmpty(){return this.startLineNumber===this.endLineNumberExclusive}delta(t){return new e(this.startLineNumber+t,this.endLineNumberExclusive+t)}deltaLength(t){return new e(this.startLineNumber,this.endLineNumberExclusive+t)}get length(){return this.endLineNumberExclusive-this.startLineNumber}join(t){return new e(Math.min(this.startLineNumber,t.startLineNumber),Math.max(this.endLineNumberExclusive,t.endLineNumberExclusive))}toString(){return`[${this.startLineNumber},${this.endLineNumberExclusive})`}intersect(t){let n=Math.max(this.startLineNumber,t.startLineNumber),r=Math.min(this.endLineNumberExclusive,t.endLineNumberExclusive);if(n<=r)return new e(n,r)}intersectsStrict(t){return this.startLineNumber<t.endLineNumberExclusive&&t.startLineNumber<this.endLineNumberExclusive}overlapOrTouch(t){return this.startLineNumber<=t.endLineNumberExclusive&&t.startLineNumber<=this.endLineNumberExclusive}equals(t){return this.startLineNumber===t.startLineNumber&&this.endLineNumberExclusive===t.endLineNumberExclusive}toInclusiveRange(){return this.isEmpty?null:new q(this.startLineNumber,1,this.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER)}toExclusiveRange(){return new q(this.startLineNumber,1,this.endLineNumberExclusive,1)}mapToLineArray(t){let n=[];for(let r=this.startLineNumber;r<this.endLineNumberExclusive;r++)n.push(t(r));return n}forEach(t){for(let n=this.startLineNumber;n<this.endLineNumberExclusive;n++)t(n)}serialize(){return[this.startLineNumber,this.endLineNumberExclusive]}includes(t){return this.startLineNumber<=t&&t<this.endLineNumberExclusive}toOffsetRange(){return new I(this.startLineNumber-1,this.endLineNumberExclusive-1)}},Ue=class e{constructor(t=[]){this._normalizedRanges=t}get ranges(){return this._normalizedRanges}addRange(t){if(t.length===0)return;let n=un(this._normalizedRanges,i=>i.endLineNumberExclusive>=t.startLineNumber),r=qe(this._normalizedRanges,i=>i.startLineNumber<=t.endLineNumberExclusive)+1;if(n===r)this._normalizedRanges.splice(n,0,t);else if(n===r-1){let i=this._normalizedRanges[n];this._normalizedRanges[n]=i.join(t)}else{let i=this._normalizedRanges[n].join(this._normalizedRanges[r-1]).join(t);this._normalizedRanges.splice(n,r-n,i)}}contains(t){let n=xe(this._normalizedRanges,r=>r.startLineNumber<=t);return!!n&&n.endLineNumberExclusive>t}intersects(t){let n=xe(this._normalizedRanges,r=>r.startLineNumber<t.endLineNumberExclusive);return!!n&&n.endLineNumberExclusive>t.startLineNumber}getUnion(t){if(this._normalizedRanges.length===0)return t;if(t._normalizedRanges.length===0)return this;let n=[],r=0,i=0,s=null;for(;r<this._normalizedRanges.length||i<t._normalizedRanges.length;){let o=null;if(r<this._normalizedRanges.length&&i<t._normalizedRanges.length){let l=this._normalizedRanges[r],u=t._normalizedRanges[i];l.startLineNumber<u.startLineNumber?(o=l,r++):(o=u,i++)}else r<this._normalizedRanges.length?(o=this._normalizedRanges[r],r++):(o=t._normalizedRanges[i],i++);s===null?s=o:s.endLineNumberExclusive>=o.startLineNumber?s=new T(s.startLineNumber,Math.max(s.endLineNumberExclusive,o.endLineNumberExclusive)):(n.push(s),s=o)}return s!==null&&n.push(s),new e(n)}subtractFrom(t){let n=un(this._normalizedRanges,o=>o.endLineNumberExclusive>=t.startLineNumber),r=qe(this._normalizedRanges,o=>o.startLineNumber<=t.endLineNumberExclusive)+1;if(n===r)return new e([t]);let i=[],s=t.startLineNumber;for(let o=n;o<r;o++){let l=this._normalizedRanges[o];l.startLineNumber>s&&i.push(new T(s,l.startLineNumber)),s=l.endLineNumberExclusive}return s<t.endLineNumberExclusive&&i.push(new T(s,t.endLineNumberExclusive)),new e(i)}toString(){return this._normalizedRanges.map(t=>t.toString()).join(", ")}getIntersection(t){let n=[],r=0,i=0;for(;r<this._normalizedRanges.length&&i<t._normalizedRanges.length;){let s=this._normalizedRanges[r],o=t._normalizedRanges[i],l=s.intersect(o);l&&!l.isEmpty&&n.push(l),s.endLineNumberExclusive<o.endLineNumberExclusive?r++:i++}return new e(n)}getWithDelta(t){return new e(this._normalizedRanges.map(n=>n.delta(t)))}};var Me=class e{static inverse(t,n,r){let i=[],s=1,o=1;for(let u of t){let c=new he(new T(s,u.original.startLineNumber),new T(o,u.modified.startLineNumber),void 0);c.modified.isEmpty||i.push(c),s=u.original.endLineNumberExclusive,o=u.modified.endLineNumberExclusive}let l=new he(new T(s,n+1),new T(o,r+1),void 0);return l.modified.isEmpty||i.push(l),i}constructor(t,n){this.original=t,this.modified=n}toString(){return`{${this.original.toString()}->${this.modified.toString()}}`}flip(){return new e(this.modified,this.original)}join(t){return new e(this.original.join(t.original),this.modified.join(t.modified))}},he=class e extends Me{constructor(t,n,r){super(t,n),this.innerChanges=r}flip(){var t;return new e(this.modified,this.original,(t=this.innerChanges)===null||t===void 0?void 0:t.map(n=>n.flip()))}},We=class e{constructor(t,n){this.originalRange=t,this.modifiedRange=n}toString(){return`{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`}flip(){return new e(this.modifiedRange,this.originalRange)}};var Bs=3,cn=class{computeDiff(t,n,r){var i;let o=new sr(t,n,{maxComputationTime:r.maxComputationTimeMs,shouldIgnoreTrimWhitespace:r.ignoreTrimWhitespace,shouldComputeCharChanges:!0,shouldMakePrettyDiff:!0,shouldPostProcessCharChanges:!0}).computeDiff(),l=[],u=null;for(let c of o.changes){let f;c.originalEndLineNumber===0?f=new T(c.originalStartLineNumber+1,c.originalStartLineNumber+1):f=new T(c.originalStartLineNumber,c.originalEndLineNumber+1);let h;c.modifiedEndLineNumber===0?h=new T(c.modifiedStartLineNumber+1,c.modifiedStartLineNumber+1):h=new T(c.modifiedStartLineNumber,c.modifiedEndLineNumber+1);let d=new he(f,h,(i=c.charChanges)===null||i===void 0?void 0:i.map(g=>new We(new q(g.originalStartLineNumber,g.originalStartColumn,g.originalEndLineNumber,g.originalEndColumn),new q(g.modifiedStartLineNumber,g.modifiedStartColumn,g.modifiedEndLineNumber,g.modifiedEndColumn))));u&&(u.modified.endLineNumberExclusive===d.modified.startLineNumber||u.original.endLineNumberExclusive===d.original.startLineNumber)&&(d=new he(u.original.join(d.original),u.modified.join(d.modified),u.innerChanges&&d.innerChanges?u.innerChanges.concat(d.innerChanges):void 0),l.pop()),l.push(d),u=d}return Ke(()=>sn(l,(c,f)=>f.original.startLineNumber-c.original.endLineNumberExclusive===f.modified.startLineNumber-c.modified.endLineNumberExclusive&&c.original.endLineNumberExclusive<f.original.startLineNumber&&c.modified.endLineNumberExclusive<f.modified.startLineNumber)),new Ee(l,[],o.quitEarly)}};function oi(e,t,n,r){return new gt(e,t,n).ComputeDiff(r)}var hn=class{constructor(t){let n=[],r=[];for(let i=0,s=t.length;i<s;i++)n[i]=or(t[i],1),r[i]=ar(t[i],1);this.lines=t,this._startColumns=n,this._endColumns=r}getElements(){let t=[];for(let n=0,r=this.lines.length;n<r;n++)t[n]=this.lines[n].substring(this._startColumns[n]-1,this._endColumns[n]-1);return t}getStrictElement(t){return this.lines[t]}getStartLineNumber(t){return t+1}getEndLineNumber(t){return t+1}createCharSequence(t,n,r){let i=[],s=[],o=[],l=0;for(let u=n;u<=r;u++){let c=this.lines[u],f=t?this._startColumns[u]:1,h=t?this._endColumns[u]:c.length+1;for(let d=f;d<h;d++)i[l]=c.charCodeAt(d-1),s[l]=u+1,o[l]=d,l++;!t&&u<r&&(i[l]=10,s[l]=u+1,o[l]=c.length+1,l++)}return new ir(i,s,o)}},ir=class{constructor(t,n,r){this._charCodes=t,this._lineNumbers=n,this._columns=r}toString(){return"["+this._charCodes.map((t,n)=>(t===10?"\\n":String.fromCharCode(t))+`-(${this._lineNumbers[n]},${this._columns[n]})`).join(", ")+"]"}_assertIndex(t,n){if(t<0||t>=n.length)throw new Error("Illegal index")}getElements(){return this._charCodes}getStartLineNumber(t){return t>0&&t===this._lineNumbers.length?this.getEndLineNumber(t-1):(this._assertIndex(t,this._lineNumbers),this._lineNumbers[t])}getEndLineNumber(t){return t===-1?this.getStartLineNumber(t+1):(this._assertIndex(t,this._lineNumbers),this._charCodes[t]===10?this._lineNumbers[t]+1:this._lineNumbers[t])}getStartColumn(t){return t>0&&t===this._columns.length?this.getEndColumn(t-1):(this._assertIndex(t,this._columns),this._columns[t])}getEndColumn(t){return t===-1?this.getStartColumn(t+1):(this._assertIndex(t,this._columns),this._charCodes[t]===10?1:this._columns[t]+1)}},tt=class e{constructor(t,n,r,i,s,o,l,u){this.originalStartLineNumber=t,this.originalStartColumn=n,this.originalEndLineNumber=r,this.originalEndColumn=i,this.modifiedStartLineNumber=s,this.modifiedStartColumn=o,this.modifiedEndLineNumber=l,this.modifiedEndColumn=u}static createFromDiffChange(t,n,r){let i=n.getStartLineNumber(t.originalStart),s=n.getStartColumn(t.originalStart),o=n.getEndLineNumber(t.originalStart+t.originalLength-1),l=n.getEndColumn(t.originalStart+t.originalLength-1),u=r.getStartLineNumber(t.modifiedStart),c=r.getStartColumn(t.modifiedStart),f=r.getEndLineNumber(t.modifiedStart+t.modifiedLength-1),h=r.getEndColumn(t.modifiedStart+t.modifiedLength-1);return new e(i,s,o,l,u,c,f,h)}};function Vs(e){if(e.length<=1)return e;let t=[e[0]],n=t[0];for(let r=1,i=e.length;r<i;r++){let s=e[r],o=s.originalStart-(n.originalStart+n.originalLength),l=s.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(o,l)<Bs?(n.originalLength=s.originalStart+s.originalLength-n.originalStart,n.modifiedLength=s.modifiedStart+s.modifiedLength-n.modifiedStart):(t.push(s),n=s)}return t}var wt=class e{constructor(t,n,r,i,s){this.originalStartLineNumber=t,this.originalEndLineNumber=n,this.modifiedStartLineNumber=r,this.modifiedEndLineNumber=i,this.charChanges=s}static createFromDiffResult(t,n,r,i,s,o,l){let u,c,f,h,d;if(n.originalLength===0?(u=r.getStartLineNumber(n.originalStart)-1,c=0):(u=r.getStartLineNumber(n.originalStart),c=r.getEndLineNumber(n.originalStart+n.originalLength-1)),n.modifiedLength===0?(f=i.getStartLineNumber(n.modifiedStart)-1,h=0):(f=i.getStartLineNumber(n.modifiedStart),h=i.getEndLineNumber(n.modifiedStart+n.modifiedLength-1)),o&&n.originalLength>0&&n.originalLength<20&&n.modifiedLength>0&&n.modifiedLength<20&&s()){let g=r.createCharSequence(t,n.originalStart,n.originalStart+n.originalLength-1),p=i.createCharSequence(t,n.modifiedStart,n.modifiedStart+n.modifiedLength-1);if(g.getElements().length>0&&p.getElements().length>0){let m=oi(g,p,s,!0).changes;l&&(m=Vs(m)),d=[];for(let _=0,N=m.length;_<N;_++)d.push(tt.createFromDiffChange(m[_],g,p))}}return new e(u,c,f,h,d)}},sr=class{constructor(t,n,r){this.shouldComputeCharChanges=r.shouldComputeCharChanges,this.shouldPostProcessCharChanges=r.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=r.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=r.shouldMakePrettyDiff,this.originalLines=t,this.modifiedLines=n,this.original=new hn(t),this.modified=new hn(n),this.continueLineDiff=si(r.maxComputationTime),this.continueCharDiff=si(r.maxComputationTime===0?0:Math.min(r.maxComputationTime,5e3))}computeDiff(){if(this.original.lines.length===1&&this.original.lines[0].length===0)return this.modified.lines.length===1&&this.modified.lines[0].length===0?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:void 0}]};if(this.modified.lines.length===1&&this.modified.lines[0].length===0)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:void 0}]};let t=oi(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),n=t.changes,r=t.quitEarly;if(this.shouldIgnoreTrimWhitespace){let l=[];for(let u=0,c=n.length;u<c;u++)l.push(wt.createFromDiffResult(this.shouldIgnoreTrimWhitespace,n[u],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:r,changes:l}}let i=[],s=0,o=0;for(let l=-1,u=n.length;l<u;l++){let c=l+1<u?n[l+1]:null,f=c?c.originalStart:this.originalLines.length,h=c?c.modifiedStart:this.modifiedLines.length;for(;s<f&&o<h;){let d=this.originalLines[s],g=this.modifiedLines[o];if(d!==g){{let p=or(d,1),m=or(g,1);for(;p>1&&m>1;){let _=d.charCodeAt(p-2),N=g.charCodeAt(m-2);if(_!==N)break;p--,m--}(p>1||m>1)&&this._pushTrimWhitespaceCharChange(i,s+1,1,p,o+1,1,m)}{let p=ar(d,1),m=ar(g,1),_=d.length+1,N=g.length+1;for(;p<_&&m<N;){let b=d.charCodeAt(p-1),x=d.charCodeAt(m-1);if(b!==x)break;p++,m++}(p<_||m<N)&&this._pushTrimWhitespaceCharChange(i,s+1,p,_,o+1,m,N)}}s++,o++}c&&(i.push(wt.createFromDiffResult(this.shouldIgnoreTrimWhitespace,c,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),s+=c.originalLength,o+=c.modifiedLength)}return{quitEarly:r,changes:i}}_pushTrimWhitespaceCharChange(t,n,r,i,s,o,l){if(this._mergeTrimWhitespaceCharChange(t,n,r,i,s,o,l))return;let u;this.shouldComputeCharChanges&&(u=[new tt(n,r,n,i,s,o,s,l)]),t.push(new wt(n,n,s,s,u))}_mergeTrimWhitespaceCharChange(t,n,r,i,s,o,l){let u=t.length;if(u===0)return!1;let c=t[u-1];return c.originalEndLineNumber===0||c.modifiedEndLineNumber===0?!1:c.originalEndLineNumber===n&&c.modifiedEndLineNumber===s?(this.shouldComputeCharChanges&&c.charChanges&&c.charChanges.push(new tt(n,r,n,i,s,o,s,l)),!0):c.originalEndLineNumber+1===n&&c.modifiedEndLineNumber+1===s?(c.originalEndLineNumber=n,c.modifiedEndLineNumber=s,this.shouldComputeCharChanges&&c.charChanges&&c.charChanges.push(new tt(n,r,n,i,s,o,s,l)),!0):!1}};function or(e,t){let n=Fr(e);return n===-1?t:n+1}function ar(e,t){let n=kr(e);return n===-1?t:n+2}function si(e){if(e===0)return()=>!0;let t=Date.now();return()=>Date.now()-t<e}var me=class e{static trivial(t,n){return new e([new J(I.ofLength(t.length),I.ofLength(n.length))],!1)}static trivialTimedOut(t,n){return new e([new J(I.ofLength(t.length),I.ofLength(n.length))],!0)}constructor(t,n){this.diffs=t,this.hitTimeout=n}},J=class e{static invert(t,n){let r=[];return Yr(t,(i,s)=>{r.push(e.fromOffsetPairs(i?i.getEndExclusives():ie.zero,s?s.getStarts():new ie(n,(i?i.seq2Range.endExclusive-i.seq1Range.endExclusive:0)+n)))}),r}static fromOffsetPairs(t,n){return new e(new I(t.offset1,n.offset1),new I(t.offset2,n.offset2))}constructor(t,n){this.seq1Range=t,this.seq2Range=n}swap(){return new e(this.seq2Range,this.seq1Range)}toString(){return`${this.seq1Range} <-> ${this.seq2Range}`}join(t){return new e(this.seq1Range.join(t.seq1Range),this.seq2Range.join(t.seq2Range))}delta(t){return t===0?this:new e(this.seq1Range.delta(t),this.seq2Range.delta(t))}deltaStart(t){return t===0?this:new e(this.seq1Range.deltaStart(t),this.seq2Range.deltaStart(t))}deltaEnd(t){return t===0?this:new e(this.seq1Range.deltaEnd(t),this.seq2Range.deltaEnd(t))}intersect(t){let n=this.seq1Range.intersect(t.seq1Range),r=this.seq2Range.intersect(t.seq2Range);if(!(!n||!r))return new e(n,r)}getStarts(){return new ie(this.seq1Range.start,this.seq2Range.start)}getEndExclusives(){return new ie(this.seq1Range.endExclusive,this.seq2Range.endExclusive)}},ie=class{constructor(t,n){this.offset1=t,this.offset2=n}toString(){return`${this.offset1} <-> ${this.offset2}`}};ie.zero=new ie(0,0);ie.max=new ie(Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER);var ge=class{isValid(){return!0}};ge.instance=new ge;var fn=class{constructor(t){if(this.timeout=t,this.startTime=Date.now(),this.valid=!0,t<=0)throw new se("timeout must be positive")}isValid(){if(!(Date.now()-this.startTime<this.timeout)&&this.valid){this.valid=!1;debugger}return this.valid}};var nt=class{constructor(t,n){this.width=t,this.height=n,this.array=[],this.array=new Array(t*n)}get(t,n){return this.array[t+n*this.width]}set(t,n,r){this.array[t+n*this.width]=r}};function Nt(e){return e===32||e===9}var rt=class e{static getKey(t){let n=this.chrKeys.get(t);return n===void 0&&(n=this.chrKeys.size,this.chrKeys.set(t,n)),n}constructor(t,n,r){this.range=t,this.lines=n,this.source=r,this.histogram=[];let i=0;for(let s=t.startLineNumber-1;s<t.endLineNumberExclusive-1;s++){let o=n[s];for(let u=0;u<o.length;u++){i++;let c=o[u],f=e.getKey(c);this.histogram[f]=(this.histogram[f]||0)+1}i++;let l=e.getKey(`
`);this.histogram[l]=(this.histogram[l]||0)+1}this.totalCount=i}computeSimilarity(t){var n,r;let i=0,s=Math.max(this.histogram.length,t.histogram.length);for(let o=0;o<s;o++)i+=Math.abs(((n=this.histogram[o])!==null&&n!==void 0?n:0)-((r=t.histogram[o])!==null&&r!==void 0?r:0));return 1-i/(this.totalCount+t.totalCount)}};rt.chrKeys=new Map;var dn=class{compute(t,n,r=ge.instance,i){if(t.length===0||n.length===0)return me.trivial(t,n);let s=new nt(t.length,n.length),o=new nt(t.length,n.length),l=new nt(t.length,n.length);for(let p=0;p<t.length;p++)for(let m=0;m<n.length;m++){if(!r.isValid())return me.trivialTimedOut(t,n);let _=p===0?0:s.get(p-1,m),N=m===0?0:s.get(p,m-1),b;t.getElement(p)===n.getElement(m)?(p===0||m===0?b=0:b=s.get(p-1,m-1),p>0&&m>0&&o.get(p-1,m-1)===3&&(b+=l.get(p-1,m-1)),b+=i?i(p,m):1):b=-1;let x=Math.max(_,N,b);if(x===b){let v=p>0&&m>0?l.get(p-1,m-1):0;l.set(p,m,v+1),o.set(p,m,3)}else x===_?(l.set(p,m,0),o.set(p,m,1)):x===N&&(l.set(p,m,0),o.set(p,m,2));s.set(p,m,x)}let u=[],c=t.length,f=n.length;function h(p,m){(p+1!==c||m+1!==f)&&u.push(new J(new I(p+1,c),new I(m+1,f))),c=p,f=m}let d=t.length-1,g=n.length-1;for(;d>=0&&g>=0;)o.get(d,g)===3?(h(d,g),d--,g--):o.get(d,g)===1?d--:g--;return h(-1,-1),u.reverse(),new me(u,!1)}};var it=class{compute(t,n,r=ge.instance){if(t.length===0||n.length===0)return me.trivial(t,n);let i=t,s=n;function o(m,_){for(;m<i.length&&_<s.length&&i.getElement(m)===s.getElement(_);)m++,_++;return m}let l=0,u=new lr;u.set(0,o(0,0));let c=new ur;c.set(0,u.get(0)===0?null:new mn(null,0,0,u.get(0)));let f=0;e:for(;;){if(l++,!r.isValid())return me.trivialTimedOut(i,s);let m=-Math.min(l,s.length+l%2),_=Math.min(l,i.length+l%2);for(f=m;f<=_;f+=2){let N=0,b=f===_?-1:u.get(f+1),x=f===m?-1:u.get(f-1)+1;N++;let v=Math.min(Math.max(b,x),i.length),w=v-f;if(N++,v>i.length||w>s.length)continue;let R=o(v,w);u.set(f,R);let y=v===b?c.get(f+1):c.get(f-1);if(c.set(f,R!==v?new mn(y,v,w,R-v):y),u.get(f)===i.length&&u.get(f)-f===s.length)break e}}let h=c.get(f),d=[],g=i.length,p=s.length;for(;;){let m=h?h.x+h.length:0,_=h?h.y+h.length:0;if((m!==g||_!==p)&&d.push(new J(new I(m,g),new I(_,p))),!h)break;g=h.x,p=h.y,h=h.prev}return d.reverse(),new me(d,!1)}},mn=class{constructor(t,n,r,i){this.prev=t,this.x=n,this.y=r,this.length=i}},lr=class{constructor(){this.positiveArr=new Int32Array(10),this.negativeArr=new Int32Array(10)}get(t){return t<0?(t=-t-1,this.negativeArr[t]):this.positiveArr[t]}set(t,n){if(t<0){if(t=-t-1,t>=this.negativeArr.length){let r=this.negativeArr;this.negativeArr=new Int32Array(r.length*2),this.negativeArr.set(r)}this.negativeArr[t]=n}else{if(t>=this.positiveArr.length){let r=this.positiveArr;this.positiveArr=new Int32Array(r.length*2),this.positiveArr.set(r)}this.positiveArr[t]=n}}},ur=class{constructor(){this.positiveArr=[],this.negativeArr=[]}get(t){return t<0?(t=-t-1,this.negativeArr[t]):this.positiveArr[t]}set(t,n){t<0?(t=-t-1,this.negativeArr[t]=n):this.positiveArr[t]=n}};var ai,li,cr=class{constructor(t,n){this.uri=t,this.value=n}};function qs(e){return Array.isArray(e)}var hr=class e{constructor(t,n){if(this[ai]="ResourceMap",t instanceof e)this.map=new Map(t.map),this.toKey=n??e.defaultToKey;else if(qs(t)){this.map=new Map,this.toKey=n??e.defaultToKey;for(let[r,i]of t)this.set(r,i)}else this.map=new Map,this.toKey=t??e.defaultToKey}set(t,n){return this.map.set(this.toKey(t),new cr(t,n)),this}get(t){var n;return(n=this.map.get(this.toKey(t)))===null||n===void 0?void 0:n.value}has(t){return this.map.has(this.toKey(t))}get size(){return this.map.size}clear(){this.map.clear()}delete(t){return this.map.delete(this.toKey(t))}forEach(t,n){typeof n<"u"&&(t=t.bind(n));for(let[r,i]of this.map)t(i.value,i.uri,this)}*values(){for(let t of this.map.values())yield t.value}*keys(){for(let t of this.map.values())yield t.uri}*entries(){for(let t of this.map.values())yield[t.uri,t.value]}*[(ai=Symbol.toStringTag,Symbol.iterator)](){for(let[,t]of this.map)yield[t.uri,t.value]}};hr.defaultToKey=e=>e.toString();var ui=class{constructor(){this[li]="LinkedMap",this._map=new Map,this._head=void 0,this._tail=void 0,this._size=0,this._state=0}clear(){this._map.clear(),this._head=void 0,this._tail=void 0,this._size=0,this._state++}isEmpty(){return!this._head&&!this._tail}get size(){return this._size}get first(){var t;return(t=this._head)===null||t===void 0?void 0:t.value}get last(){var t;return(t=this._tail)===null||t===void 0?void 0:t.value}has(t){return this._map.has(t)}get(t,n=0){let r=this._map.get(t);if(r)return n!==0&&this.touch(r,n),r.value}set(t,n,r=0){let i=this._map.get(t);if(i)i.value=n,r!==0&&this.touch(i,r);else{switch(i={key:t,value:n,next:void 0,previous:void 0},r){case 0:this.addItemLast(i);break;case 1:this.addItemFirst(i);break;case 2:this.addItemLast(i);break;default:this.addItemLast(i);break}this._map.set(t,i),this._size++}return this}delete(t){return!!this.remove(t)}remove(t){let n=this._map.get(t);if(n)return this._map.delete(t),this.removeItem(n),this._size--,n.value}shift(){if(!this._head&&!this._tail)return;if(!this._head||!this._tail)throw new Error("Invalid list");let t=this._head;return this._map.delete(t.key),this.removeItem(t),this._size--,t.value}forEach(t,n){let r=this._state,i=this._head;for(;i;){if(n?t.bind(n)(i.value,i.key,this):t(i.value,i.key,this),this._state!==r)throw new Error("LinkedMap got modified during iteration.");i=i.next}}keys(){let t=this,n=this._state,r=this._head,i={[Symbol.iterator](){return i},next(){if(t._state!==n)throw new Error("LinkedMap got modified during iteration.");if(r){let s={value:r.key,done:!1};return r=r.next,s}else return{value:void 0,done:!0}}};return i}values(){let t=this,n=this._state,r=this._head,i={[Symbol.iterator](){return i},next(){if(t._state!==n)throw new Error("LinkedMap got modified during iteration.");if(r){let s={value:r.value,done:!1};return r=r.next,s}else return{value:void 0,done:!0}}};return i}entries(){let t=this,n=this._state,r=this._head,i={[Symbol.iterator](){return i},next(){if(t._state!==n)throw new Error("LinkedMap got modified during iteration.");if(r){let s={value:[r.key,r.value],done:!1};return r=r.next,s}else return{value:void 0,done:!0}}};return i}[(li=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}trimOld(t){if(t>=this.size)return;if(t===0){this.clear();return}let n=this._head,r=this.size;for(;n&&r>t;)this._map.delete(n.key),n=n.next,r--;this._head=n,this._size=r,n&&(n.previous=void 0),this._state++}addItemFirst(t){if(!this._head&&!this._tail)this._tail=t;else if(this._head)t.next=this._head,this._head.previous=t;else throw new Error("Invalid list");this._head=t,this._state++}addItemLast(t){if(!this._head&&!this._tail)this._head=t;else if(this._tail)t.previous=this._tail,this._tail.next=t;else throw new Error("Invalid list");this._tail=t,this._state++}removeItem(t){if(t===this._head&&t===this._tail)this._head=void 0,this._tail=void 0;else if(t===this._head){if(!t.next)throw new Error("Invalid list");t.next.previous=void 0,this._head=t.next}else if(t===this._tail){if(!t.previous)throw new Error("Invalid list");t.previous.next=void 0,this._tail=t.previous}else{let n=t.next,r=t.previous;if(!n||!r)throw new Error("Invalid list");n.previous=r,r.next=n}t.next=void 0,t.previous=void 0,this._state++}touch(t,n){if(!this._head||!this._tail)throw new Error("Invalid list");if(!(n!==1&&n!==2)){if(n===1){if(t===this._head)return;let r=t.next,i=t.previous;t===this._tail?(i.next=void 0,this._tail=i):(r.previous=i,i.next=r),t.previous=void 0,t.next=this._head,this._head.previous=t,this._head=t,this._state++}else if(n===2){if(t===this._tail)return;let r=t.next,i=t.previous;t===this._head?(r.previous=void 0,this._head=r):(r.previous=i,i.next=r),t.next=void 0,t.previous=this._tail,this._tail.next=t,this._tail=t,this._state++}}}toJSON(){let t=[];return this.forEach((n,r)=>{t.push([r,n])}),t}fromJSON(t){this.clear();for(let[n,r]of t)this.set(n,r)}};var gn=class{constructor(){this.map=new Map}add(t,n){let r=this.map.get(t);r||(r=new Set,this.map.set(t,r)),r.add(n)}delete(t,n){let r=this.map.get(t);r&&(r.delete(n),r.size===0&&this.map.delete(t))}forEach(t,n){let r=this.map.get(t);r&&r.forEach(n)}get(t){let n=this.map.get(t);return n||new Set}};var Fe=class{constructor(t,n,r){this.lines=t,this.considerWhitespaceChanges=r,this.elements=[],this.firstCharOffsetByLine=[],this.additionalOffsetByLine=[];let i=!1;n.start>0&&n.endExclusive>=t.length&&(n=new I(n.start-1,n.endExclusive),i=!0),this.lineRange=n,this.firstCharOffsetByLine[0]=0;for(let s=this.lineRange.start;s<this.lineRange.endExclusive;s++){let o=t[s],l=0;if(i)l=o.length,o="",i=!1;else if(!r){let u=o.trimStart();l=o.length-u.length,o=u.trimEnd()}this.additionalOffsetByLine.push(l);for(let u=0;u<o.length;u++)this.elements.push(o.charCodeAt(u));s<t.length-1&&(this.elements.push(10),this.firstCharOffsetByLine[s-this.lineRange.start+1]=this.elements.length)}this.additionalOffsetByLine.push(0)}toString(){return`Slice: "${this.text}"`}get text(){return this.getText(new I(0,this.length))}getText(t){return this.elements.slice(t.start,t.endExclusive).map(n=>String.fromCharCode(n)).join("")}getElement(t){return this.elements[t]}get length(){return this.elements.length}getBoundaryScore(t){let n=hi(t>0?this.elements[t-1]:-1),r=hi(t<this.elements.length?this.elements[t]:-1);if(n===7&&r===8)return 0;let i=0;return n!==r&&(i+=10,n===0&&r===1&&(i+=1)),i+=ci(n),i+=ci(r),i}translateOffset(t){if(this.lineRange.isEmpty)return new Q(this.lineRange.start+1,1);let n=qe(this.firstCharOffsetByLine,r=>r<=t);return new Q(this.lineRange.start+n+1,t-this.firstCharOffsetByLine[n]+this.additionalOffsetByLine[n]+1)}translateRange(t){return q.fromPositions(this.translateOffset(t.start),this.translateOffset(t.endExclusive))}findWordContaining(t){if(t<0||t>=this.elements.length||!fr(this.elements[t]))return;let n=t;for(;n>0&&fr(this.elements[n-1]);)n--;let r=t;for(;r<this.elements.length&&fr(this.elements[r]);)r++;return new I(n,r)}countLinesIn(t){return this.translateOffset(t.endExclusive).lineNumber-this.translateOffset(t.start).lineNumber}isStronglyEqual(t,n){return this.elements[t]===this.elements[n]}extendToFullLines(t){var n,r;let i=(n=xe(this.firstCharOffsetByLine,o=>o<=t.start))!==null&&n!==void 0?n:0,s=(r=ii(this.firstCharOffsetByLine,o=>t.endExclusive<=o))!==null&&r!==void 0?r:this.elements.length;return new I(i,s)}};function fr(e){return e>=97&&e<=122||e>=65&&e<=90||e>=48&&e<=57}var Us={0:0,1:0,2:0,3:10,4:2,5:3,6:3,7:10,8:10};function ci(e){return Us[e]}function hi(e){return e===10?8:e===13?7:Nt(e)?6:e>=97&&e<=122?0:e>=65&&e<=90?1:e>=48&&e<=57?2:e===-1?3:e===44||e===59?5:4}function di(e,t,n,r,i,s){let{moves:o,excludedChanges:l}=zs(e,t,n,s);if(!s.isValid())return[];let u=e.filter(f=>!l.has(f)),c=Os(u,r,i,t,n,s);return Kr(o,c),o=Hs(o),o=o.filter(f=>{let h=f.original.toOffsetRange().slice(t).map(g=>g.trim());return h.join(`
`).length>=15&&Ws(h,g=>g.length>=2)>=2}),o=$s(e,o),o}function Ws(e,t){let n=0;for(let r of e)t(r)&&n++;return n}function zs(e,t,n,r){let i=[],s=e.filter(u=>u.modified.isEmpty&&u.original.length>=3).map(u=>new rt(u.original,t,u)),o=new Set(e.filter(u=>u.original.isEmpty&&u.modified.length>=3).map(u=>new rt(u.modified,n,u))),l=new Set;for(let u of s){let c=-1,f;for(let h of o){let d=u.computeSimilarity(h);d>c&&(c=d,f=h)}if(c>.9&&f&&(o.delete(f),i.push(new Me(u.range,f.range)),l.add(u.source),l.add(f.source)),!r.isValid())return{moves:i,excludedChanges:l}}return{moves:i,excludedChanges:l}}function Os(e,t,n,r,i,s){let o=[],l=new gn;for(let d of e)for(let g=d.original.startLineNumber;g<d.original.endLineNumberExclusive-2;g++){let p=`${t[g-1]}:${t[g+1-1]}:${t[g+2-1]}`;l.add(p,{range:new T(g,g+3)})}let u=[];e.sort(bt(d=>d.modified.startLineNumber,xt));for(let d of e){let g=[];for(let p=d.modified.startLineNumber;p<d.modified.endLineNumberExclusive-2;p++){let m=`${n[p-1]}:${n[p+1-1]}:${n[p+2-1]}`,_=new T(p,p+3),N=[];l.forEach(m,({range:b})=>{for(let v of g)if(v.originalLineRange.endLineNumberExclusive+1===b.endLineNumberExclusive&&v.modifiedLineRange.endLineNumberExclusive+1===_.endLineNumberExclusive){v.originalLineRange=new T(v.originalLineRange.startLineNumber,b.endLineNumberExclusive),v.modifiedLineRange=new T(v.modifiedLineRange.startLineNumber,_.endLineNumberExclusive),N.push(v);return}let x={modifiedLineRange:_,originalLineRange:b};u.push(x),N.push(x)}),g=N}if(!s.isValid())return[]}u.sort(e1(bt(d=>d.modifiedLineRange.length,xt)));let c=new Ue,f=new Ue;for(let d of u){let g=d.modifiedLineRange.startLineNumber-d.originalLineRange.startLineNumber,p=c.subtractFrom(d.modifiedLineRange),m=f.subtractFrom(d.originalLineRange).getWithDelta(g),_=p.getIntersection(m);for(let N of _.ranges){if(N.length<3)continue;let b=N,x=N.delta(-g);o.push(new Me(x,b)),c.addRange(b),f.addRange(x)}}o.sort(bt(d=>d.original.startLineNumber,xt));let h=new et(e);for(let d=0;d<o.length;d++){let g=o[d],p=h.findLastMonotonous(R=>R.original.startLineNumber<=g.original.startLineNumber),m=xe(e,R=>R.modified.startLineNumber<=g.modified.startLineNumber),_=Math.max(g.original.startLineNumber-p.original.startLineNumber,g.modified.startLineNumber-m.modified.startLineNumber),N=h.findLastMonotonous(R=>R.original.startLineNumber<g.original.endLineNumberExclusive),b=xe(e,R=>R.modified.startLineNumber<g.modified.endLineNumberExclusive),x=Math.max(N.original.endLineNumberExclusive-g.original.endLineNumberExclusive,b.modified.endLineNumberExclusive-g.modified.endLineNumberExclusive),v;for(v=0;v<_;v++){let R=g.original.startLineNumber-v-1,y=g.modified.startLineNumber-v-1;if(R>r.length||y>i.length||c.contains(y)||f.contains(R)||!fi(r[R-1],i[y-1],s))break}v>0&&(f.addRange(new T(g.original.startLineNumber-v,g.original.startLineNumber)),c.addRange(new T(g.modified.startLineNumber-v,g.modified.startLineNumber)));let w;for(w=0;w<x;w++){let R=g.original.endLineNumberExclusive+w,y=g.modified.endLineNumberExclusive+w;if(R>r.length||y>i.length||c.contains(y)||f.contains(R)||!fi(r[R-1],i[y-1],s))break}w>0&&(f.addRange(new T(g.original.endLineNumberExclusive,g.original.endLineNumberExclusive+w)),c.addRange(new T(g.modified.endLineNumberExclusive,g.modified.endLineNumberExclusive+w))),(v>0||w>0)&&(o[d]=new Me(new T(g.original.startLineNumber-v,g.original.endLineNumberExclusive+w),new T(g.modified.startLineNumber-v,g.modified.endLineNumberExclusive+w)))}return o}function fi(e,t,n){if(e.trim()===t.trim())return!0;if(e.length>300&&t.length>300)return!1;let i=new it().compute(new Fe([e],new I(0,1),!1),new Fe([t],new I(0,1),!1),n),s=0,o=J.invert(i.diffs,e.length);for(let f of o)f.seq1Range.forEach(h=>{Nt(e.charCodeAt(h))||s++});function l(f){let h=0;for(let d=0;d<e.length;d++)Nt(f.charCodeAt(d))||h++;return h}let u=l(e.length>t.length?e:t);return s/u>.6&&u>10}function Hs(e){if(e.length===0)return e;e.sort(bt(n=>n.original.startLineNumber,xt));let t=[e[0]];for(let n=1;n<e.length;n++){let r=t[t.length-1],i=e[n],s=i.original.startLineNumber-r.original.endLineNumberExclusive,o=i.modified.startLineNumber-r.modified.endLineNumberExclusive;if(s>=0&&o>=0&&s+o<=2){t[t.length-1]=r.join(i);continue}t.push(i)}return t}function $s(e,t){let n=new et(e);return t=t.filter(r=>{let i=n.findLastMonotonous(l=>l.original.startLineNumber<r.original.endLineNumberExclusive)||new Me(new T(1,1),new T(1,1)),s=xe(e,l=>l.modified.startLineNumber<r.modified.endLineNumberExclusive);return i!==s}),t}function dr(e,t,n){let r=n;return r=mi(e,t,r),r=mi(e,t,r),r=Gs(e,t,r),r}function mi(e,t,n){if(n.length===0)return n;let r=[];r.push(n[0]);for(let s=1;s<n.length;s++){let o=r[r.length-1],l=n[s];if(l.seq1Range.isEmpty||l.seq2Range.isEmpty){let u=l.seq1Range.start-o.seq1Range.endExclusive,c;for(c=1;c<=u&&!(e.getElement(l.seq1Range.start-c)!==e.getElement(l.seq1Range.endExclusive-c)||t.getElement(l.seq2Range.start-c)!==t.getElement(l.seq2Range.endExclusive-c));c++);if(c--,c===u){r[r.length-1]=new J(new I(o.seq1Range.start,l.seq1Range.endExclusive-u),new I(o.seq2Range.start,l.seq2Range.endExclusive-u));continue}l=l.delta(-c)}r.push(l)}let i=[];for(let s=0;s<r.length-1;s++){let o=r[s+1],l=r[s];if(l.seq1Range.isEmpty||l.seq2Range.isEmpty){let u=o.seq1Range.start-l.seq1Range.endExclusive,c;for(c=0;c<u&&!(!e.isStronglyEqual(l.seq1Range.start+c,l.seq1Range.endExclusive+c)||!t.isStronglyEqual(l.seq2Range.start+c,l.seq2Range.endExclusive+c));c++);if(c===u){r[s+1]=new J(new I(l.seq1Range.start+u,o.seq1Range.endExclusive),new I(l.seq2Range.start+u,o.seq2Range.endExclusive));continue}c>0&&(l=l.delta(c))}i.push(l)}return r.length>0&&i.push(r[r.length-1]),i}function Gs(e,t,n){if(!e.getBoundaryScore||!t.getBoundaryScore)return n;for(let r=0;r<n.length;r++){let i=r>0?n[r-1]:void 0,s=n[r],o=r+1<n.length?n[r+1]:void 0,l=new I(i?i.seq1Range.start+1:0,o?o.seq1Range.endExclusive-1:e.length),u=new I(i?i.seq2Range.start+1:0,o?o.seq2Range.endExclusive-1:t.length);s.seq1Range.isEmpty?n[r]=gi(s,e,t,l,u):s.seq2Range.isEmpty&&(n[r]=gi(s.swap(),t,e,u,l).swap())}return n}function gi(e,t,n,r,i){let o=1;for(;e.seq1Range.start-o>=r.start&&e.seq2Range.start-o>=i.start&&n.isStronglyEqual(e.seq2Range.start-o,e.seq2Range.endExclusive-o)&&o<100;)o++;o--;let l=0;for(;e.seq1Range.start+l<r.endExclusive&&e.seq2Range.endExclusive+l<i.endExclusive&&n.isStronglyEqual(e.seq2Range.start+l,e.seq2Range.endExclusive+l)&&l<100;)l++;if(o===0&&l===0)return e;let u=0,c=-1;for(let f=-o;f<=l;f++){let h=e.seq2Range.start+f,d=e.seq2Range.endExclusive+f,g=e.seq1Range.start+f,p=t.getBoundaryScore(g)+n.getBoundaryScore(h)+n.getBoundaryScore(d);p>c&&(c=p,u=f)}return e.delta(u)}function pi(e,t,n){let r=[];for(let i of n){let s=r[r.length-1];if(!s){r.push(i);continue}i.seq1Range.start-s.seq1Range.endExclusive<=2||i.seq2Range.start-s.seq2Range.endExclusive<=2?r[r.length-1]=new J(s.seq1Range.join(i.seq1Range),s.seq2Range.join(i.seq2Range)):r.push(i)}return r}function bi(e,t,n){let r=[],i;function s(){if(!i)return;let l=i.s1Range.length-i.deleted,u=i.s2Range.length-i.added;Math.max(i.deleted,i.added)+(i.count-1)>l&&r.push(new J(i.s1Range,i.s2Range)),i=void 0}for(let l of n){let u=function(g,p){var m,_,N,b;if(!i||!i.s1Range.containsRange(g)||!i.s2Range.containsRange(p))if(i&&!(i.s1Range.endExclusive<g.start&&i.s2Range.endExclusive<p.start)){let w=I.tryCreate(i.s1Range.endExclusive,g.start),R=I.tryCreate(i.s2Range.endExclusive,p.start);i.deleted+=(m=w?.length)!==null&&m!==void 0?m:0,i.added+=(_=R?.length)!==null&&_!==void 0?_:0,i.s1Range=i.s1Range.join(g),i.s2Range=i.s2Range.join(p)}else s(),i={added:0,deleted:0,count:0,s1Range:g,s2Range:p};let x=g.intersect(l.seq1Range),v=p.intersect(l.seq2Range);i.count++,i.deleted+=(N=x?.length)!==null&&N!==void 0?N:0,i.added+=(b=v?.length)!==null&&b!==void 0?b:0},c=e.findWordContaining(l.seq1Range.start-1),f=t.findWordContaining(l.seq2Range.start-1),h=e.findWordContaining(l.seq1Range.endExclusive),d=t.findWordContaining(l.seq2Range.endExclusive);c&&h&&f&&d&&c.equals(h)&&f.equals(d)?u(c,f):(c&&f&&u(c,f),h&&d&&u(h,d))}return s(),js(n,r)}function js(e,t){let n=[];for(;e.length>0||t.length>0;){let r=e[0],i=t[0],s;r&&(!i||r.seq1Range.start<i.seq1Range.start)?s=e.shift():s=t.shift(),n.length>0&&n[n.length-1].seq1Range.endExclusive>=s.seq1Range.start?n[n.length-1]=n[n.length-1].join(s):n.push(s)}return n}function xi(e,t,n){let r=n;if(r.length===0)return r;let i=0,s;do{s=!1;let o=[r[0]];for(let l=1;l<r.length;l++){let f=function(d,g){let p=new I(c.seq1Range.endExclusive,u.seq1Range.start);return e.getText(p).replace(/\s/g,"").length<=4&&(d.seq1Range.length+d.seq2Range.length>5||g.seq1Range.length+g.seq2Range.length>5)},u=r[l],c=o[o.length-1];f(c,u)?(s=!0,o[o.length-1]=o[o.length-1].join(u)):o.push(u)}r=o}while(i++<10&&s);return r}function _i(e,t,n){let r=n;if(r.length===0)return r;let i=0,s;do{s=!1;let l=[r[0]];for(let u=1;u<r.length;u++){let h=function(g,p){let m=new I(f.seq1Range.endExclusive,c.seq1Range.start);if(e.countLinesIn(m)>5||m.length>500)return!1;let N=e.getText(m).trim();if(N.length>20||N.split(/\r\n|\r|\n/).length>1)return!1;let b=e.countLinesIn(g.seq1Range),x=g.seq1Range.length,v=t.countLinesIn(g.seq2Range),w=g.seq2Range.length,R=e.countLinesIn(p.seq1Range),y=p.seq1Range.length,M=t.countLinesIn(p.seq2Range),U=p.seq2Range.length,j=2*40+50;function B(k){return Math.min(k,j)}return Math.pow(Math.pow(B(b*40+x),1.5)+Math.pow(B(v*40+w),1.5),1.5)+Math.pow(Math.pow(B(R*40+y),1.5)+Math.pow(B(M*40+U),1.5),1.5)>(j**1.5)**1.5*1.3},c=r[u],f=l[l.length-1];h(f,c)?(s=!0,l[l.length-1]=l[l.length-1].join(c)):l.push(c)}r=l}while(i++<10&&s);let o=[];return Zr(r,(l,u,c)=>{let f=u;function h(N){return N.length>0&&N.trim().length<=3&&u.seq1Range.length+u.seq2Range.length>100}let d=e.extendToFullLines(u.seq1Range),g=e.getText(new I(d.start,u.seq1Range.start));h(g)&&(f=f.deltaStart(-g.length));let p=e.getText(new I(u.seq1Range.endExclusive,d.endExclusive));h(p)&&(f=f.deltaEnd(p.length));let m=J.fromOffsetPairs(l?l.getEndExclusives():ie.zero,c?c.getStarts():ie.max),_=f.intersect(m);o.push(_)}),o}var St=class{constructor(t,n){this.trimmedHash=t,this.lines=n}getElement(t){return this.trimmedHash[t]}get length(){return this.trimmedHash.length}getBoundaryScore(t){let n=t===0?0:vi(this.lines[t-1]),r=t===this.lines.length?0:vi(this.lines[t]);return 1e3-(n+r)}getText(t){return this.lines.slice(t.start,t.endExclusive).join(`
`)}isStronglyEqual(t,n){return this.lines[t]===this.lines[n]}};function vi(e){let t=0;for(;t<e.length&&(e.charCodeAt(t)===32||e.charCodeAt(t)===9);)t++;return t}var pn=class{constructor(){this.dynamicProgrammingDiffing=new dn,this.myersDiffingAlgorithm=new it}computeDiff(t,n,r){if(t.length<=1&&Jr(t,n,(w,R)=>w===R))return new Ee([],[],!1);if(t.length===1&&t[0].length===0||n.length===1&&n[0].length===0)return new Ee([new he(new T(1,t.length+1),new T(1,n.length+1),[new We(new q(1,1,t.length,t[0].length+1),new q(1,1,n.length,n[0].length+1))])],[],!1);let i=r.maxComputationTimeMs===0?ge.instance:new fn(r.maxComputationTimeMs),s=!r.ignoreTrimWhitespace,o=new Map;function l(w){let R=o.get(w);return R===void 0&&(R=o.size,o.set(w,R)),R}let u=t.map(w=>l(w.trim())),c=n.map(w=>l(w.trim())),f=new St(u,t),h=new St(c,n),d=f.length+h.length<1700?this.dynamicProgrammingDiffing.compute(f,h,i,(w,R)=>t[w]===n[R]?n[R].length===0?.1:1+Math.log(1+n[R].length):.99):this.myersDiffingAlgorithm.compute(f,h),g=d.diffs,p=d.hitTimeout;g=dr(f,h,g),g=xi(f,h,g);let m=[],_=w=>{if(s)for(let R=0;R<w;R++){let y=N+R,M=b+R;if(t[y]!==n[M]){let U=this.refineDiff(t,n,new J(new I(y,y+1),new I(M,M+1)),i,s);for(let j of U.mappings)m.push(j);U.hitTimeout&&(p=!0)}}},N=0,b=0;for(let w of g){Ke(()=>w.seq1Range.start-N===w.seq2Range.start-b);let R=w.seq1Range.start-N;_(R),N=w.seq1Range.endExclusive,b=w.seq2Range.endExclusive;let y=this.refineDiff(t,n,w,i,s);y.hitTimeout&&(p=!0);for(let M of y.mappings)m.push(M)}_(t.length-N);let x=Li(m,t,n),v=[];return r.computeMoves&&(v=this.computeMoves(x,t,n,u,c,i,s)),Ke(()=>{function w(y,M){if(y.lineNumber<1||y.lineNumber>M.length)return!1;let U=M[y.lineNumber-1];return!(y.column<1||y.column>U.length+1)}function R(y,M){return!(y.startLineNumber<1||y.startLineNumber>M.length+1||y.endLineNumberExclusive<1||y.endLineNumberExclusive>M.length+1)}for(let y of x){if(!y.innerChanges)return!1;for(let M of y.innerChanges)if(!(w(M.modifiedRange.getStartPosition(),n)&&w(M.modifiedRange.getEndPosition(),n)&&w(M.originalRange.getStartPosition(),t)&&w(M.originalRange.getEndPosition(),t)))return!1;if(!R(y.modified,n)||!R(y.original,t))return!1}return!0}),new Ee(x,v,p)}computeMoves(t,n,r,i,s,o,l){return di(t,n,r,i,s,o).map(f=>{let h=this.refineDiff(n,r,new J(f.original.toOffsetRange(),f.modified.toOffsetRange()),o,l),d=Li(h.mappings,n,r,!0);return new ln(f,d)})}refineDiff(t,n,r,i,s){let o=new Fe(t,r.seq1Range,s),l=new Fe(n,r.seq2Range,s),u=o.length+l.length<500?this.dynamicProgrammingDiffing.compute(o,l,i):this.myersDiffingAlgorithm.compute(o,l,i),c=u.diffs;return c=dr(o,l,c),c=bi(o,l,c),c=pi(o,l,c),c=_i(o,l,c),{mappings:c.map(h=>new We(o.translateRange(h.seq1Range),l.translateRange(h.seq2Range))),hitTimeout:u.hitTimeout}}};function Li(e,t,n,r=!1){let i=[];for(let s of Xr(e.map(o=>Qs(o,t,n)),(o,l)=>o.original.overlapOrTouch(l.original)||o.modified.overlapOrTouch(l.modified))){let o=s[0],l=s[s.length-1];i.push(new he(o.original.join(l.original),o.modified.join(l.modified),s.map(u=>u.innerChanges[0])))}return Ke(()=>!r&&i.length>0&&i[0].original.startLineNumber!==i[0].modified.startLineNumber?!1:sn(i,(s,o)=>o.original.startLineNumber-s.original.endLineNumberExclusive===o.modified.startLineNumber-s.modified.endLineNumberExclusive&&s.original.endLineNumberExclusive<o.original.startLineNumber&&s.modified.endLineNumberExclusive<o.modified.startLineNumber)),i}function Qs(e,t,n){let r=0,i=0;e.modifiedRange.endColumn===1&&e.originalRange.endColumn===1&&e.originalRange.startLineNumber+r<=e.originalRange.endLineNumber&&e.modifiedRange.startLineNumber+r<=e.modifiedRange.endLineNumber&&(i=-1),e.modifiedRange.startColumn-1>=n[e.modifiedRange.startLineNumber-1].length&&e.originalRange.startColumn-1>=t[e.originalRange.startLineNumber-1].length&&e.originalRange.startLineNumber<=e.originalRange.endLineNumber+i&&e.modifiedRange.startLineNumber<=e.modifiedRange.endLineNumber+i&&(r=1);let s=new T(e.originalRange.startLineNumber+r,e.originalRange.endLineNumber+1+i),o=new T(e.modifiedRange.startLineNumber+r,e.modifiedRange.endLineNumber+1+i);return new he(s,o,[e])}var mr={getLegacy:()=>new cn,getDefault:()=>new pn};function ke(e,t){let n=Math.pow(10,t);return Math.round(e*n)/n}var G=class{constructor(t,n,r,i=1){this._rgbaBrand=void 0,this.r=Math.min(255,Math.max(0,t))|0,this.g=Math.min(255,Math.max(0,n))|0,this.b=Math.min(255,Math.max(0,r))|0,this.a=ke(Math.max(Math.min(1,i),0),3)}static equals(t,n){return t.r===n.r&&t.g===n.g&&t.b===n.b&&t.a===n.a}},pe=class e{constructor(t,n,r,i){this._hslaBrand=void 0,this.h=Math.max(Math.min(360,t),0)|0,this.s=ke(Math.max(Math.min(1,n),0),3),this.l=ke(Math.max(Math.min(1,r),0),3),this.a=ke(Math.max(Math.min(1,i),0),3)}static equals(t,n){return t.h===n.h&&t.s===n.s&&t.l===n.l&&t.a===n.a}static fromRGBA(t){let n=t.r/255,r=t.g/255,i=t.b/255,s=t.a,o=Math.max(n,r,i),l=Math.min(n,r,i),u=0,c=0,f=(l+o)/2,h=o-l;if(h>0){switch(c=Math.min(f<=.5?h/(2*f):h/(2-2*f),1),o){case n:u=(r-i)/h+(r<i?6:0);break;case r:u=(i-n)/h+2;break;case i:u=(n-r)/h+4;break}u*=60,u=Math.round(u)}return new e(u,c,f,s)}static _hue2rgb(t,n,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?t+(n-t)*6*r:r<1/2?n:r<2/3?t+(n-t)*(2/3-r)*6:t}static toRGBA(t){let n=t.h/360,{s:r,l:i,a:s}=t,o,l,u;if(r===0)o=l=u=i;else{let c=i<.5?i*(1+r):i+r-i*r,f=2*i-c;o=e._hue2rgb(f,c,n+1/3),l=e._hue2rgb(f,c,n),u=e._hue2rgb(f,c,n-1/3)}return new G(Math.round(o*255),Math.round(l*255),Math.round(u*255),s)}},st=class e{constructor(t,n,r,i){this._hsvaBrand=void 0,this.h=Math.max(Math.min(360,t),0)|0,this.s=ke(Math.max(Math.min(1,n),0),3),this.v=ke(Math.max(Math.min(1,r),0),3),this.a=ke(Math.max(Math.min(1,i),0),3)}static equals(t,n){return t.h===n.h&&t.s===n.s&&t.v===n.v&&t.a===n.a}static fromRGBA(t){let n=t.r/255,r=t.g/255,i=t.b/255,s=Math.max(n,r,i),o=Math.min(n,r,i),l=s-o,u=s===0?0:l/s,c;return l===0?c=0:s===n?c=((r-i)/l%6+6)%6:s===r?c=(i-n)/l+2:c=(n-r)/l+4,new e(Math.round(c*60),u,s,t.a)}static toRGBA(t){let{h:n,s:r,v:i,a:s}=t,o=i*r,l=o*(1-Math.abs(n/60%2-1)),u=i-o,[c,f,h]=[0,0,0];return n<60?(c=o,f=l):n<120?(c=l,f=o):n<180?(f=o,h=l):n<240?(f=l,h=o):n<300?(c=l,h=o):n<=360&&(c=o,h=l),c=Math.round((c+u)*255),f=Math.round((f+u)*255),h=Math.round((h+u)*255),new G(c,f,h,s)}},H=class e{static fromHex(t){return e.Format.CSS.parseHex(t)||e.red}static equals(t,n){return!t&&!n?!0:!t||!n?!1:t.equals(n)}get hsla(){return this._hsla?this._hsla:pe.fromRGBA(this.rgba)}get hsva(){return this._hsva?this._hsva:st.fromRGBA(this.rgba)}constructor(t){if(t)if(t instanceof G)this.rgba=t;else if(t instanceof pe)this._hsla=t,this.rgba=pe.toRGBA(t);else if(t instanceof st)this._hsva=t,this.rgba=st.toRGBA(t);else throw new Error("Invalid color ctor argument");else throw new Error("Color needs a value")}equals(t){return!!t&&G.equals(this.rgba,t.rgba)&&pe.equals(this.hsla,t.hsla)&&st.equals(this.hsva,t.hsva)}getRelativeLuminance(){let t=e._relativeLuminanceForComponent(this.rgba.r),n=e._relativeLuminanceForComponent(this.rgba.g),r=e._relativeLuminanceForComponent(this.rgba.b),i=.2126*t+.7152*n+.0722*r;return ke(i,4)}static _relativeLuminanceForComponent(t){let n=t/255;return n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4)}isLighter(){return(this.rgba.r*299+this.rgba.g*587+this.rgba.b*114)/1e3>=128}isLighterThan(t){let n=this.getRelativeLuminance(),r=t.getRelativeLuminance();return n>r}isDarkerThan(t){let n=this.getRelativeLuminance(),r=t.getRelativeLuminance();return n<r}lighten(t){return new e(new pe(this.hsla.h,this.hsla.s,this.hsla.l+this.hsla.l*t,this.hsla.a))}darken(t){return new e(new pe(this.hsla.h,this.hsla.s,this.hsla.l-this.hsla.l*t,this.hsla.a))}transparent(t){let{r:n,g:r,b:i,a:s}=this.rgba;return new e(new G(n,r,i,s*t))}isTransparent(){return this.rgba.a===0}isOpaque(){return this.rgba.a===1}opposite(){return new e(new G(255-this.rgba.r,255-this.rgba.g,255-this.rgba.b,this.rgba.a))}makeOpaque(t){if(this.isOpaque()||t.rgba.a!==1)return this;let{r:n,g:r,b:i,a:s}=this.rgba;return new e(new G(t.rgba.r-s*(t.rgba.r-n),t.rgba.g-s*(t.rgba.g-r),t.rgba.b-s*(t.rgba.b-i),1))}toString(){return this._toString||(this._toString=e.Format.CSS.format(this)),this._toString}static getLighterColor(t,n,r){if(t.isLighterThan(n))return t;r=r||.5;let i=t.getRelativeLuminance(),s=n.getRelativeLuminance();return r=r*(s-i)/s,t.lighten(r)}static getDarkerColor(t,n,r){if(t.isDarkerThan(n))return t;r=r||.5;let i=t.getRelativeLuminance(),s=n.getRelativeLuminance();return r=r*(i-s)/i,t.darken(r)}};H.white=new H(new G(255,255,255,1));H.black=new H(new G(0,0,0,1));H.red=new H(new G(255,0,0,1));H.blue=new H(new G(0,0,255,1));H.green=new H(new G(0,255,0,1));H.cyan=new H(new G(0,255,255,1));H.lightgrey=new H(new G(211,211,211,1));H.transparent=new H(new G(0,0,0,0));(function(e){let t;(function(n){let r;(function(i){function s(m){return m.rgba.a===1?`rgb(${m.rgba.r}, ${m.rgba.g}, ${m.rgba.b})`:e.Format.CSS.formatRGBA(m)}i.formatRGB=s;function o(m){return`rgba(${m.rgba.r}, ${m.rgba.g}, ${m.rgba.b}, ${+m.rgba.a.toFixed(2)})`}i.formatRGBA=o;function l(m){return m.hsla.a===1?`hsl(${m.hsla.h}, ${(m.hsla.s*100).toFixed(2)}%, ${(m.hsla.l*100).toFixed(2)}%)`:e.Format.CSS.formatHSLA(m)}i.formatHSL=l;function u(m){return`hsla(${m.hsla.h}, ${(m.hsla.s*100).toFixed(2)}%, ${(m.hsla.l*100).toFixed(2)}%, ${m.hsla.a.toFixed(2)})`}i.formatHSLA=u;function c(m){let _=m.toString(16);return _.length!==2?"0"+_:_}function f(m){return`#${c(m.rgba.r)}${c(m.rgba.g)}${c(m.rgba.b)}`}i.formatHex=f;function h(m,_=!1){return _&&m.rgba.a===1?e.Format.CSS.formatHex(m):`#${c(m.rgba.r)}${c(m.rgba.g)}${c(m.rgba.b)}${c(Math.round(m.rgba.a*255))}`}i.formatHexA=h;function d(m){return m.isOpaque()?e.Format.CSS.formatHex(m):e.Format.CSS.formatRGBA(m)}i.format=d;function g(m){let _=m.length;if(_===0||m.charCodeAt(0)!==35)return null;if(_===7){let N=16*p(m.charCodeAt(1))+p(m.charCodeAt(2)),b=16*p(m.charCodeAt(3))+p(m.charCodeAt(4)),x=16*p(m.charCodeAt(5))+p(m.charCodeAt(6));return new e(new G(N,b,x,1))}if(_===9){let N=16*p(m.charCodeAt(1))+p(m.charCodeAt(2)),b=16*p(m.charCodeAt(3))+p(m.charCodeAt(4)),x=16*p(m.charCodeAt(5))+p(m.charCodeAt(6)),v=16*p(m.charCodeAt(7))+p(m.charCodeAt(8));return new e(new G(N,b,x,v/255))}if(_===4){let N=p(m.charCodeAt(1)),b=p(m.charCodeAt(2)),x=p(m.charCodeAt(3));return new e(new G(16*N+N,16*b+b,16*x+x))}if(_===5){let N=p(m.charCodeAt(1)),b=p(m.charCodeAt(2)),x=p(m.charCodeAt(3)),v=p(m.charCodeAt(4));return new e(new G(16*N+N,16*b+b,16*x+x,(16*v+v)/255))}return null}i.parseHex=g;function p(m){switch(m){case 48:return 0;case 49:return 1;case 50:return 2;case 51:return 3;case 52:return 4;case 53:return 5;case 54:return 6;case 55:return 7;case 56:return 8;case 57:return 9;case 97:return 10;case 65:return 10;case 98:return 11;case 66:return 11;case 99:return 12;case 67:return 12;case 100:return 13;case 68:return 13;case 101:return 14;case 69:return 14;case 102:return 15;case 70:return 15}return 0}})(r=n.CSS||(n.CSS={}))})(t=e.Format||(e.Format={}))})(H||(H={}));function Si(e){let t=[];for(let n of e){let r=Number(n);(r||r===0&&n.replace(/\s/g,"")!=="")&&t.push(r)}return t}function gr(e,t,n,r){return{red:e/255,blue:n/255,green:t/255,alpha:r}}function Ct(e,t){let n=t.index,r=t[0].length;if(!n)return;let i=e.positionAt(n);return{startLineNumber:i.lineNumber,startColumn:i.column,endLineNumber:i.lineNumber,endColumn:i.column+r}}function Js(e,t){if(!e)return;let n=H.Format.CSS.parseHex(t);if(n)return{range:e,color:gr(n.rgba.r,n.rgba.g,n.rgba.b,n.rgba.a)}}function wi(e,t,n){if(!e||t.length!==1)return;let i=t[0].values(),s=Si(i);return{range:e,color:gr(s[0],s[1],s[2],n?s[3]:1)}}function Ni(e,t,n){if(!e||t.length!==1)return;let i=t[0].values(),s=Si(i),o=new H(new pe(s[0],s[1]/100,s[2]/100,n?s[3]:1));return{range:e,color:gr(o.rgba.r,o.rgba.g,o.rgba.b,o.rgba.a)}}function At(e,t){return typeof e=="string"?[...e.matchAll(t)]:e.findMatches(t)}function Xs(e){let t=[],r=At(e,/\b(rgb|rgba|hsl|hsla)(\([0-9\s,.\%]*\))|(#)([A-Fa-f0-9]{3})\b|(#)([A-Fa-f0-9]{4})\b|(#)([A-Fa-f0-9]{6})\b|(#)([A-Fa-f0-9]{8})\b/gm);if(r.length>0)for(let i of r){let s=i.filter(c=>c!==void 0),o=s[1],l=s[2];if(!l)continue;let u;if(o==="rgb"){let c=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*\)$/gm;u=wi(Ct(e,i),At(l,c),!1)}else if(o==="rgba"){let c=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm;u=wi(Ct(e,i),At(l,c),!0)}else if(o==="hsl"){let c=/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*\)$/gm;u=Ni(Ct(e,i),At(l,c),!1)}else if(o==="hsla"){let c=/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm;u=Ni(Ct(e,i),At(l,c),!0)}else o==="#"&&(u=Js(Ct(e,i),o+l));u&&t.push(u)}return t}function Ci(e){return!e||typeof e.getValue!="function"||typeof e.positionAt!="function"?[]:Xs(e)}var pr=class extends Gt{get uri(){return this._uri}get eol(){return this._eol}getValue(){return this.getText()}findMatches(t){let n=[];for(let r=0;r<this._lines.length;r++){let i=this._lines[r],s=this.offsetAt(new Q(r+1,1)),o=i.matchAll(t);for(let l of o)(l.index||l.index===0)&&(l.index=l.index+s),n.push(l)}return n}getLinesContent(){return this._lines.slice(0)}getLineCount(){return this._lines.length}getLineContent(t){return this._lines[t-1]}getWordAtPosition(t,n){let r=_t(t.column,Qn(n),this._lines[t.lineNumber-1],0);return r?new q(t.lineNumber,r.startColumn,t.lineNumber,r.endColumn):null}words(t){let n=this._lines,r=this._wordenize.bind(this),i=0,s="",o=0,l=[];return{*[Symbol.iterator](){for(;;)if(o<l.length){let u=s.substring(l[o].start,l[o].end);o+=1,yield u}else if(i<n.length)s=n[i],l=r(s,t),o=0,i+=1;else break}}}getLineWords(t,n){let r=this._lines[t-1],i=this._wordenize(r,n),s=[];for(let o of i)s.push({word:r.substring(o.start,o.end),startColumn:o.start+1,endColumn:o.end+1});return s}_wordenize(t,n){let r=[],i;for(n.lastIndex=0;(i=n.exec(t))&&i[0].length!==0;)r.push({start:i.index,end:i.index+i[0].length});return r}getValueInRange(t){if(t=this._validateRange(t),t.startLineNumber===t.endLineNumber)return this._lines[t.startLineNumber-1].substring(t.startColumn-1,t.endColumn-1);let n=this._eol,r=t.startLineNumber-1,i=t.endLineNumber-1,s=[];s.push(this._lines[r].substring(t.startColumn-1));for(let o=r+1;o<i;o++)s.push(this._lines[o]);return s.push(this._lines[i].substring(0,t.endColumn-1)),s.join(n)}offsetAt(t){return t=this._validatePosition(t),this._ensureLineStarts(),this._lineStarts.getPrefixSum(t.lineNumber-2)+(t.column-1)}positionAt(t){t=Math.floor(t),t=Math.max(0,t),this._ensureLineStarts();let n=this._lineStarts.getIndexOf(t),r=this._lines[n.index].length;return{lineNumber:1+n.index,column:1+Math.min(n.remainder,r)}}_validateRange(t){let n=this._validatePosition({lineNumber:t.startLineNumber,column:t.startColumn}),r=this._validatePosition({lineNumber:t.endLineNumber,column:t.endColumn});return n.lineNumber!==t.startLineNumber||n.column!==t.startColumn||r.lineNumber!==t.endLineNumber||r.column!==t.endColumn?{startLineNumber:n.lineNumber,startColumn:n.column,endLineNumber:r.lineNumber,endColumn:r.column}:t}_validatePosition(t){if(!Q.isIPosition(t))throw new Error("bad position");let{lineNumber:n,column:r}=t,i=!1;if(n<1)n=1,r=1,i=!0;else if(n>this._lines.length)n=this._lines.length,r=this._lines[n-1].length+1,i=!0;else{let s=this._lines[n-1].length+1;r<1?(r=1,i=!0):r>s&&(r=s,i=!0)}return i?{lineNumber:n,column:r}:t}},ot=class e{constructor(t,n){this._host=t,this._models=Object.create(null),this._foreignModuleFactory=n,this._foreignModule=null}dispose(){this._models=Object.create(null)}_getModel(t){return this._models[t]}_getModels(){let t=[];return Object.keys(this._models).forEach(n=>t.push(this._models[n])),t}acceptNewModel(t){this._models[t.url]=new pr(Re.parse(t.url),t.lines,t.EOL,t.versionId)}acceptModelChanged(t,n){if(!this._models[t])return;this._models[t].onEvents(n)}acceptRemovedModel(t){this._models[t]&&delete this._models[t]}async computeUnicodeHighlights(t,n,r){let i=this._getModel(t);return i?on.computeUnicodeHighlights(i,n,r):{ranges:[],hasMore:!1,ambiguousCharacterCount:0,invisibleCharacterCount:0,nonBasicAsciiCharacterCount:0}}async computeDiff(t,n,r,i){let s=this._getModel(t),o=this._getModel(n);return!s||!o?null:e.computeDiff(s,o,r,i)}static computeDiff(t,n,r,i){let s=i==="advanced"?mr.getDefault():mr.getLegacy(),o=t.getLinesContent(),l=n.getLinesContent(),u=s.computeDiff(o,l,r),c=u.changes.length>0?!1:this._modelsAreIdentical(t,n);function f(h){return h.map(d=>{var g;return[d.original.startLineNumber,d.original.endLineNumberExclusive,d.modified.startLineNumber,d.modified.endLineNumberExclusive,(g=d.innerChanges)===null||g===void 0?void 0:g.map(p=>[p.originalRange.startLineNumber,p.originalRange.startColumn,p.originalRange.endLineNumber,p.originalRange.endColumn,p.modifiedRange.startLineNumber,p.modifiedRange.startColumn,p.modifiedRange.endLineNumber,p.modifiedRange.endColumn])]})}return{identical:c,quitEarly:u.hitTimeout,changes:f(u.changes),moves:u.moves.map(h=>[h.lineRangeMapping.original.startLineNumber,h.lineRangeMapping.original.endLineNumberExclusive,h.lineRangeMapping.modified.startLineNumber,h.lineRangeMapping.modified.endLineNumberExclusive,f(h.changes)])}}static _modelsAreIdentical(t,n){let r=t.getLineCount(),i=n.getLineCount();if(r!==i)return!1;for(let s=1;s<=r;s++){let o=t.getLineContent(s),l=n.getLineContent(s);if(o!==l)return!1}return!0}async computeMoreMinimalEdits(t,n,r){let i=this._getModel(t);if(!i)return n;let s=[],o;n=n.slice(0).sort((u,c)=>{if(u.range&&c.range)return q.compareRangesUsingStarts(u.range,c.range);let f=u.range?0:1,h=c.range?0:1;return f-h});let l=0;for(let u=1;u<n.length;u++)q.getEndPosition(n[l].range).equals(q.getStartPosition(n[u].range))?(n[l].range=q.fromPositions(q.getStartPosition(n[l].range),q.getEndPosition(n[u].range)),n[l].text+=n[u].text):(l++,n[l]=n[u]);n.length=l+1;for(let{range:u,text:c,eol:f}of n){if(typeof f=="number"&&(o=f),q.isEmpty(u)&&!c)continue;let h=i.getValueInRange(u);if(c=c.replace(/\r\n|\n|\r/g,i.eol),h===c)continue;if(Math.max(c.length,h.length)>e._diffLimit){s.push({range:u,text:c});continue}let d=Ur(h,c,r),g=i.offsetAt(q.lift(u).getStartPosition());for(let p of d){let m=i.positionAt(g+p.originalStart),_=i.positionAt(g+p.originalStart+p.originalLength),N={text:c.substr(p.modifiedStart,p.modifiedLength),range:{startLineNumber:m.lineNumber,startColumn:m.column,endLineNumber:_.lineNumber,endColumn:_.column}};i.getValueInRange(N.range)!==N.text&&s.push(N)}}return typeof o=="number"&&s.push({eol:o,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),s}async computeLinks(t){let n=this._getModel(t);return n?n1(n):null}async computeDefaultDocumentColors(t){let n=this._getModel(t);return n?Ci(n):null}async textualSuggest(t,n,r,i){let s=new He,o=new RegExp(r,i),l=new Set;e:for(let u of t){let c=this._getModel(u);if(c){for(let f of c.words(o))if(!(f===n||!isNaN(Number(f)))&&(l.add(f),l.size>e._suggestionsLimit))break e}}return{words:Array.from(l),duration:s.elapsed()}}async computeWordRanges(t,n,r,i){let s=this._getModel(t);if(!s)return Object.create(null);let o=new RegExp(r,i),l=Object.create(null);for(let u=n.startLineNumber;u<n.endLineNumber;u++){let c=s.getLineWords(u,o);for(let f of c){if(!isNaN(Number(f.word)))continue;let h=l[f.word];h||(h=[],l[f.word]=h),h.push({startLineNumber:u,startColumn:f.startColumn,endLineNumber:u,endColumn:f.endColumn})}}return l}async navigateValueSet(t,n,r,i,s){let o=this._getModel(t);if(!o)return null;let l=new RegExp(i,s);n.startColumn===n.endColumn&&(n={startLineNumber:n.startLineNumber,startColumn:n.startColumn,endLineNumber:n.endLineNumber,endColumn:n.endColumn+1});let u=o.getValueInRange(n),c=o.getWordAtPosition({lineNumber:n.startLineNumber,column:n.startColumn},l);if(!c)return null;let f=o.getValueInRange(c);return Ye.INSTANCE.navigateValueSet(n,u,c,f,r)}loadForeignModule(t,n,r){let o={host:Sr(r,(l,u)=>this._host.fhr(l,u)),getMirrorModels:()=>this._getModels()};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(o,n),Promise.resolve(ht(this._foreignModule))):Promise.reject(new Error("Unexpected usage"))}fmr(t,n){if(!this._foreignModule||typeof this._foreignModule[t]!="function")return Promise.reject(new Error("Missing requestHandler or method: "+t));try{return Promise.resolve(this._foreignModule[t].apply(this._foreignModule,n))}catch(r){return Promise.reject(r)}}};ot._diffLimit=1e5;ot._suggestionsLimit=1e4;typeof importScripts=="function"&&(globalThis.monaco=Y1());var br=!1;function Ys(e){if(br)return;br=!0;let t=new Vt(n=>{globalThis.postMessage(n)},n=>new ot(n,e));globalThis.onmessage=n=>{t.onmessage(n.data)}}globalThis.onmessage=e=>{br||Ys(null)};})();
