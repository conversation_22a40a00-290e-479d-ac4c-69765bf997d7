#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表
"""

import pymysql

def check_tables():
    """检查数据库表"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        print("✅ 数据库连接成功")
        
        with connection.cursor() as cursor:
            # 检查所有表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            print(f"📋 数据库中共有 {len(tables)} 个表:")
            for table in tables:
                print(f"   📝 {table[0]}")
            
            # 检查督办相关表
            supervision_tables = ['supervision_items', 'companies', 'company_progress']
            print(f"\n🔍 检查督办相关表:")
            
            for table in supervision_tables:
                cursor.execute(f"SHOW TABLES LIKE '{table}'")
                result = cursor.fetchone()
                if result:
                    print(f"   ✅ {table} 存在")
                    # 检查数据量
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"      📊 数据量: {count} 条")
                else:
                    print(f"   ❌ {table} 不存在")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_tables()
