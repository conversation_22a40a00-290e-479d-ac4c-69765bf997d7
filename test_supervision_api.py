#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试督办管理API
"""

import requests
import json

def test_supervision_api():
    """测试督办管理API"""
    print("🧪 测试督办管理API...")
    
    base_url = "http://localhost:8000/api/v1"
    
    # 先登录获取token
    try:
        print("🔐 登录获取token...")
        login_response = requests.post(f"{base_url}/auth/login", json={
            "username": "admin",
            "password": "admin123"
        })
        
        if login_response.status_code == 200:
            token = login_response.json().get('access_token')
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ 登录成功")
        else:
            print("❌ 登录失败，使用无token测试")
            headers = {}
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        headers = {}
    
    # 测试获取督办事项列表
    try:
        print("\n📋 测试获取督办事项列表...")
        response = requests.get(f"{base_url}/supervision/items", headers=headers)
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text[:500]}...")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 督办事项列表获取成功")
            if 'data' in data and len(data['data']) > 0:
                print(f"   📊 督办事项数量: {len(data['data'])}")
                for item in data['data'][:3]:
                    print(f"   📝 {item.get('sequence_number', 'N/A')}. {item.get('work_theme', 'N/A')}")
            else:
                print("   ⚠️  没有督办事项数据")
        else:
            print(f"❌ 督办事项列表获取失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试督办事项API失败: {str(e)}")
    
    # 测试获取公司列表
    try:
        print("\n🏢 测试获取公司列表...")
        response = requests.get(f"{base_url}/supervision/companies", headers=headers)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 公司列表获取成功")
            if 'data' in data and len(data['data']) > 0:
                print(f"   🏢 公司数量: {len(data['data'])}")
                for company in data['data'][:5]:
                    print(f"   🏢 {company.get('company_code', 'N/A')}: {company.get('company_name', 'N/A')}")
            else:
                print("   ⚠️  没有公司数据")
        else:
            print(f"❌ 公司列表获取失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试公司API失败: {str(e)}")

def test_database_connection():
    """测试数据库连接"""
    print("\n🗄️  测试数据库连接...")
    
    try:
        import pymysql
        
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        print("✅ 数据库连接成功")
        
        with connection.cursor() as cursor:
            # 检查督办相关表
            tables = ['supervision_items', 'companies', 'company_progress']
            
            for table in tables:
                cursor.execute(f"SHOW TABLES LIKE '{table}'")
                if cursor.fetchone():
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"✅ 表 {table} 存在，数据量: {count}")
                else:
                    print(f"❌ 表 {table} 不存在")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")

def main():
    print("🚀 督办管理功能测试")
    print("=" * 60)
    
    # 测试数据库连接
    test_database_connection()
    
    # 测试API
    test_supervision_api()
    
    print("\n📋 测试完成")
    print("如果还有问题，请检查:")
    print("   1. 数据库表是否正确创建")
    print("   2. 后端服务是否正常运行")
    print("   3. 前端是否能正常访问API")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
