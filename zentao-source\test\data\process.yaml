title: table zt_flow_process
desc: "标准过程"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: name
    note: "名称"
    range: 1-10000
    prefix: "过程名称"
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "分类"
    range: project,engineering,support
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: abbr
    note: "缩写"
    range: 1-10000
    prefix: "缩写"
    postfix: ""
    loop: 0
    format: ""
  - field: desc
    note: "描述"
    range: 1-10000
    prefix: "过程描述"
    postfix: ""
    loop: 0
    format: ""
  - field: assignedTo
    note: "指派给"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: "状态"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: order
    note: "排序"
    range: 10-100:10
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: "由谁创建"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedBy
    note: "由谁编辑"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedBy
    note: "由谁指派"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
