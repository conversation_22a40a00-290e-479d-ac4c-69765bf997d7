#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工单所有字段显示
验证项目工单列表是否返回了所有字段
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'pmo-backend'))

import asyncio
from app.api.endpoints.ticket_integration import get_ticket_projects, get_project_tickets

class AllFieldsTester:
    """所有字段测试器"""
    
    def __init__(self):
        self.mock_user = {"user_id": "test", "username": "test"}
    
    async def test_all_ticket_fields(self):
        """测试工单所有字段"""
        print("🎫 测试工单所有字段显示")
        print("=" * 80)
        
        try:
            # 1. 获取项目列表
            print("\n📋 获取项目列表...")
            projects_result = await get_ticket_projects(limit=5, current_user=self.mock_user)
            
            if not projects_result.get('success'):
                print(f"❌ 获取项目列表失败: {projects_result.get('message')}")
                return False
                
            projects = projects_result.get('data', [])
            if not projects:
                print("❌ 没有找到项目")
                return False
                
            print(f"✅ 找到 {len(projects)} 个项目")
            
            # 2. 选择第一个项目测试
            test_project = projects[0]
            project_id = test_project['feelec_project_id']
            project_name = test_project['feelec_name']
            
            print(f"\n🎯 测试项目: {project_name} (ID: {project_id})")
            
            # 3. 获取项目工单详情
            tickets_result = await get_project_tickets(project_id=project_id, current_user=self.mock_user)
            
            if not tickets_result.get('success'):
                print(f"❌ 获取项目工单失败: {tickets_result.get('message')}")
                return False
                
            data = tickets_result.get('data', {})
            tickets = data.get('tickets', [])
            
            if not tickets:
                print("⚠️  该项目没有工单")
                return True
                
            print(f"✅ 找到 {len(tickets)} 个工单")
            
            # 4. 检查第一个工单的所有字段
            test_ticket = tickets[0]
            print(f"\n🔍 检查工单所有字段: {test_ticket.get('feelec_title', '无标题')}")
            
            # 获取工单的所有字段
            all_fields = list(test_ticket.keys())
            all_fields.sort()
            
            print(f"\n📊 工单包含的所有字段 (共 {len(all_fields)} 个):")
            print("-" * 60)
            
            for i, field in enumerate(all_fields, 1):
                value = test_ticket[field]
                if value is not None and value != '':
                    value_str = str(value)[:50] + ('...' if len(str(value)) > 50 else '')
                    print(f"  {i:2d}. {field:<30} : {value_str}")
                else:
                    print(f"  {i:2d}. {field:<30} : (空值)")
            
            # 5. 按类别分组显示字段
            print(f"\n📋 字段分类统计:")
            
            # 基础字段
            basic_fields = [f for f in all_fields if f.startswith('feelec_') and not f.endswith('_name') and not f.endswith('_formatted') and not f.endswith('_text')]
            print(f"  - 基础字段 ({len(basic_fields)}个): {', '.join(basic_fields[:5])}{'...' if len(basic_fields) > 5 else ''}")
            
            # 名称字段
            name_fields = [f for f in all_fields if f.endswith('_name')]
            print(f"  - 名称字段 ({len(name_fields)}个): {', '.join(name_fields)}")
            
            # 格式化字段
            formatted_fields = [f for f in all_fields if f.endswith('_formatted')]
            print(f"  - 格式化字段 ({len(formatted_fields)}个): {', '.join(formatted_fields)}")
            
            # 文本字段
            text_fields = [f for f in all_fields if f.endswith('_text')]
            print(f"  - 文本字段 ({len(text_fields)}个): {', '.join(text_fields)}")
            
            # 状态字段
            status_fields = [f for f in all_fields if f.startswith('is_') or f.endswith('_color') or f.endswith('_days')]
            print(f"  - 状态字段 ({len(status_fields)}个): {', '.join(status_fields)}")
            
            # 其他字段
            other_fields = [f for f in all_fields if f not in basic_fields + name_fields + formatted_fields + text_fields + status_fields]
            print(f"  - 其他字段 ({len(other_fields)}个): {', '.join(other_fields)}")
            
            # 6. 重要字段值展示
            print(f"\n🎯 重要字段值展示:")
            important_fields = [
                'feelec_ticket_id', 'feelec_ticket_no', 'feelec_title',
                'feelec_project_id', 'project_name',
                'feelec_publisher_id', 'publisher_name',
                'feelec_processor_id', 'processor_name',
                'feelec_department_id', 'department_name',
                'feelec_company_id', 'company_name',
                'feelec_status_id', 'status_name',
                'feelec_priority', 'priority_text',
                'feelec_source', 'source_text',
                'feelec_template_id', 'template_name',
                'create_time_formatted', 'deadline_formatted',
                'is_completed', 'is_overdue'
            ]
            
            for field in important_fields:
                if field in test_ticket:
                    value = test_ticket[field]
                    print(f"  {field:<25} : {value}")
            
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

async def main():
    """主函数"""
    tester = AllFieldsTester()
    success = await tester.test_all_ticket_fields()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 测试通过！项目工单列表现在包含所有字段。")
        print("\n前端表格现在可以显示工单的完整信息！")
    else:
        print("⚠️  测试发现问题，请检查API实现。")

if __name__ == "__main__":
    asyncio.run(main())
