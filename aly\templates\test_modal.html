<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框测试</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h1>模态框测试页面</h1>
        
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info mb-4">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <p>点击下面的按钮测试模态框</p>
        
        <!-- 按钮触发模态框 -->
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal">
            <i class="fas fa-file-import me-2"></i>打开模态框
        </button>
        
        <!-- 按钮触发标准表单提交 -->
        <form action="/test_import_excel" method="post" enctype="multipart/form-data" class="mt-3">
            <div class="mb-3">
                <label for="normalFile" class="form-label">选择文件（直接表单）</label>
                <input type="file" class="form-control" id="normalFile" name="file">
            </div>
            <button type="submit" class="btn btn-success">提交表单</button>
        </form>
        
        <!-- 导入Excel模态框 -->
        <div class="modal fade" id="testModal" tabindex="-1" aria-labelledby="testModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="testModalLabel">
                            <i class="fas fa-file-excel me-2"></i>测试模态框
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form action="/test_import_excel" method="post" enctype="multipart/form-data" id="testForm">
                            <div class="mb-3">
                                <label for="excelFile" class="form-label">
                                    <i class="fas fa-file-upload me-1"></i>选择文件
                                </label>
                                <input type="file" class="form-control" id="excelFile" name="file" accept=".xls,.xlsx" required>
                                <div class="form-text">支持.xls和.xlsx格式</div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="testCheckbox" name="test_checkbox">
                                    <label class="form-check-label" for="testCheckbox">
                                        <i class="fas fa-check me-1 text-success"></i>测试复选框
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>取消
                        </button>
                        <button type="submit" form="testForm" class="btn btn-primary">
                            <i class="fas fa-upload me-1"></i>提交
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Font Awesome JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    
    <!-- 测试脚本 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('页面加载完成');
        
        // 测试模态框打开
        var testModal = document.getElementById('testModal');
        testModal.addEventListener('show.bs.modal', function() {
            console.log('模态框正在打开');
        });
        
        testModal.addEventListener('shown.bs.modal', function() {
            console.log('模态框已打开');
        });
    });
    </script>
</body>
</html> 