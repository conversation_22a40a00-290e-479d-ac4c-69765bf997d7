<?php
declare(strict_types=1);
/**
 * The cancel view file of task module of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.zentao.net)
 * @license     ZPL(https://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR>
 * @package     task
 * @link        https://www.zentao.net
 */

namespace zin;

modalHeader();
formPanel
(
    formGroup
    (
        set::label($lang->comment),
        editor
        (
            set::name('comment'),
            set::rows('5')
        ),
        input(
            set::className('hidden'),
            set::name('status'),
            set::value('cancel')
        )
    )
);
hr();
history();

render();
