#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试Excel导出导入功能
"""

import requests
import time
import os

def test_excel_export():
    """测试Excel导出"""
    print("📤 测试Excel导出功能...")
    
    try:
        response = requests.get("http://localhost:8000/api/v1/new-supervision/export-excel", timeout=30)
        
        print(f"导出API状态码: {response.status_code}")
        
        if response.status_code == 200:
            content_length = len(response.content)
            print(f"文件大小: {content_length} 字节")
            
            # 保存文件
            filename = 'test_export_complete.xlsx'
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ Excel导出成功，文件保存为: {filename}")
            
            # 验证文件
            if os.path.exists(filename) and os.path.getsize(filename) > 5000:
                print("✅ 文件验证通过")
                return True, filename
            else:
                print("❌ 文件验证失败")
                return False, None
        else:
            print(f"❌ 导出失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ 导出测试异常: {str(e)}")
        return False, None

def test_excel_import(filename):
    """测试Excel导入"""
    print(f"\n📥 测试Excel导入功能...")
    
    if not filename or not os.path.exists(filename):
        print("❌ 没有可用的Excel文件进行导入测试")
        return False
    
    try:
        # 读取文件
        with open(filename, 'rb') as f:
            files = {'file': (filename, f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            
            response = requests.post(
                "http://localhost:8000/api/v1/new-supervision/import-excel",
                files=files,
                timeout=60
            )
        
        print(f"导入API状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Excel导入成功")
            print(f"导入结果: {result}")
            return True
        else:
            print(f"❌ 导入失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 导入测试异常: {str(e)}")
        return False

def test_supervision_api():
    """测试督办管理API"""
    print("\n📋 测试督办管理API...")
    
    try:
        response = requests.get("http://localhost:8000/api/v1/new-supervision/items", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            items_count = len(data.get('data', []))
            companies_count = len(data.get('companies', []))
            
            print(f"✅ 督办管理API正常")
            print(f"   督办事项数量: {items_count}")
            print(f"   公司数量: {companies_count}")
            return True
        else:
            print(f"❌ 督办管理API异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试异常: {str(e)}")
        return False

def create_status_data():
    """创建测试状态数据"""
    print("\n📊 创建测试状态数据...")
    
    try:
        import pymysql
        
        # 数据库连接
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='',
            database='kanban2',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 检查是否已有状态数据
        cursor.execute("SELECT COUNT(*) FROM company_supervision_status")
        status_count = cursor.fetchone()[0]
        
        if status_count > 0:
            print(f"✅ 已有 {status_count} 条状态数据")
            connection.close()
            return True
        
        # 获取督办事项和公司
        cursor.execute("SELECT id, sequence_number FROM supervision_items ORDER BY sequence_number")
        items = cursor.fetchall()
        
        cursor.execute("SELECT id, company_code FROM companies WHERE is_active = TRUE ORDER BY display_order")
        companies = cursor.fetchall()
        
        print(f"找到 {len(items)} 个督办事项，{len(companies)} 家公司")
        
        # 创建状态数据
        insert_count = 0
        for item in items:
            item_id, seq_num = item
            for company in companies:
                company_id, company_code = company
                
                # 根据序号设置不同状态
                if seq_num <= 5:
                    status = '√'  # 已完成
                elif seq_num <= 10:
                    status = 'O'  # 进行中
                elif seq_num <= 15:
                    status = 'X'  # 未启动
                else:
                    status = '—'  # 不需要执行
                
                cursor.execute("""
                    INSERT INTO company_supervision_status 
                    (supervision_item_id, company_id, status, updated_by, created_at, updated_at)
                    VALUES (%s, %s, %s, 'system', NOW(), NOW())
                """, (item_id, company_id, status))
                
                insert_count += 1
        
        connection.commit()
        connection.close()
        
        print(f"✅ 创建了 {insert_count} 条状态数据")
        return True
        
    except Exception as e:
        print(f"❌ 创建状态数据失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 Excel导出导入完整测试")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待后端服务...")
    time.sleep(3)
    
    # 1. 测试督办管理API
    api_success = test_supervision_api()
    if not api_success:
        print("❌ 督办管理API异常，无法继续测试")
        return
    
    # 2. 创建测试状态数据
    status_success = create_status_data()
    
    # 3. 测试Excel导出
    export_success, export_file = test_excel_export()
    
    # 4. 测试Excel导入
    import_success = False
    if export_success and export_file:
        import_success = test_excel_import(export_file)
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"督办管理API: {'✅ 正常' if api_success else '❌ 异常'}")
    print(f"状态数据创建: {'✅ 成功' if status_success else '❌ 失败'}")
    print(f"Excel导出: {'✅ 成功' if export_success else '❌ 失败'}")
    print(f"Excel导入: {'✅ 成功' if import_success else '❌ 失败'}")
    
    if export_success and import_success:
        print("\n🎉 Excel导出导入功能完全正常！")
        print("\n✅ 功能特色:")
        print("   📤 Excel导出:")
        print("      • 包含29个督办事项完整信息")
        print("      • 包含14家公司状态数据")
        print("      • 自动生成带时间戳的文件名")
        print("      • 优化的列宽设置")
        
        print("\n   📥 Excel导入:")
        print("      • 支持.xlsx和.xls格式")
        print("      • 自动更新数据库记录")
        print("      • 批量处理公司状态")
        print("      • 完整的错误处理")
        
        print("\n🌐 使用方法:")
        print("   1. 访问: http://localhost:3000/new-supervision")
        print("   2. 点击'导出Excel'下载数据")
        print("   3. 编辑Excel文件")
        print("   4. 点击'导入Excel'上传更新")
        
    else:
        print("\n❌ 还有问题需要解决")
        if not export_success:
            print("   • Excel导出功能异常")
        if not import_success:
            print("   • Excel导入功能异常")
    
    print("\n🏁 测试完成")

if __name__ == "__main__":
    main()
