<?php
$lang->index->common      = '首页';
$lang->index->index       = '首页';
$lang->index->app         = '首页';
$lang->index->pleaseInput = '请输入';
$lang->index->search      = '搜索';

$lang->index->dock = new stdClass();
$lang->index->dock->open    = '打开';
$lang->index->dock->reload  = '刷新';
$lang->index->dock->close   = '关闭';
$lang->index->dock->sort    = '排序';
$lang->index->dock->save    = '退出排序';
$lang->index->dock->hide    = '隐藏';
$lang->index->dock->add     = '添加';
$lang->index->dock->divider = '分割线';
$lang->index->dock->restore = '恢复默认';

$lang->index->upgradeVersion = '可升级版本';
$lang->index->upgradeNow     = '现在升级';
$lang->index->upgrade        = '升级';
$lang->index->log            = '查看版本更新日志 >';
$lang->index->detailed       = '详情';
$lang->index->website        = '请访问官网';
$lang->index->tutorialTip    = '当前正处于教程模式，是否继续进入教程？';

$lang->index->chat = new stdclass();
$lang->index->chat->chat = '聊天';
$lang->index->chat->ai   = 'AI 对话';
$lang->index->chat->unconfiguredFormat  = '尚未配置 %s 功能，%s。';
$lang->index->chat->goConfigureFormat   = '点击前往 <a class="text-primary configure-chat-button" href="%s">%s 配置页面</a> 进行功能配置';
$lang->index->chat->contactAdminForHelp = '请联系管理员进行功能配置';
$lang->index->chat->unauthorized        = '没有权限访问 AI 聊天功能，请联系管理员进行权限分配。';
$lang->index->chat->reloadTip           = '若已完成相关配置，请尝试 <a class="text-primary" id="reload-ai-chat">重新加载页面</a>。';
