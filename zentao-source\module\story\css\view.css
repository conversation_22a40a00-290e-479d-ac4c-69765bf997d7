.side-col .cell {padding: 0px;}
.side-col #legendProjectAndTask .list-unstyled {padding: 10px; border-top: 0px; margin: 0px;}
#legendStories ul li {padding: 0px 0 0 12px; line-height: 20px; display: flex;}
#legendStories ul li:last-child {display: block;}
.main-actions .btn-toolbar .btn {padding-right: 6px; padding-left: 6px;}
.btn-edit-comment {z-index: 100;}
#legendRelated .table-data tbody>tr>th {width: 100px;}
#legendStories .list-unstyled li a {padding: 6px;}
#legendStories .list-unstyled li a:not(.removeButton) {overflow: hidden; white-space: nowrap; text-overflow: ellipsis;}
[lang^='zh'] #legendStories .list-unstyled li:last-child {padding: 6px 12px;}
.linkStoryTitle {white-space: nowrap; text-overflow: ellipsis; overflow: hidden;}
.linkStoryTr > th {width: 90px;}
.linkStoryTr > td {padding-left: 0px !important;}
.resolution {white-space: nowrap; overflow: hidden;}
.link-commit {overflow: hidden; white-space: nowrap; margin-right: 5px;}
