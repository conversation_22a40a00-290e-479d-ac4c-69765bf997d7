<?php
$lang->index->common      = 'Home';
$lang->index->index       = 'Home';
$lang->index->app         = 'Home';
$lang->index->pleaseInput = 'Please input';
$lang->index->search      = 'Search';

$lang->index->dock = new stdClass();
$lang->index->dock->open    = 'Open';
$lang->index->dock->reload  = 'Reload';
$lang->index->dock->close   = 'Close';
$lang->index->dock->sort    = 'Sort';
$lang->index->dock->save    = 'Exit sort';
$lang->index->dock->hide    = 'Hide';
$lang->index->dock->add     = 'Add';
$lang->index->dock->divider = 'Divider';
$lang->index->dock->restore = 'Restore defaults';

$lang->index->upgradeVersion = 'Upgradable version';
$lang->index->upgradeNow     = 'Upgrade now';
$lang->index->upgrade        = 'Upgrade';
$lang->index->log            = 'Log';
$lang->index->detailed       = 'Details';
$lang->index->website        = 'Please visit the official website';
$lang->index->tutorialTip    = 'Currently in tutorial mode, do you want to continue to the tutorial?';

$lang->index->chat = new stdclass();
$lang->index->chat->chat = 'Chat';
$lang->index->chat->ai   = 'AI';
$lang->index->chat->unconfiguredFormat  = '%s is currently not configured. %s.';
$lang->index->chat->goConfigureFormat   = 'To configure the function, click to navigate to <a class="text-primary configure-chat-button" href="%s">%s configuration</a>';
$lang->index->chat->contactAdminForHelp = 'Please contact an administrator for assistance';
$lang->index->chat->unauthorized        = 'You are not authorized to access AI chatting. Please contact an administrator for permission.';
$lang->index->chat->reloadTip           = 'You may <a class="text-primary" id="reload-ai-chat">reload this page</a> if you believe that the configuration has been completed.';
