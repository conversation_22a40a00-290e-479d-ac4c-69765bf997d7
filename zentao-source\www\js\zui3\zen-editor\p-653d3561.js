import{m as e}from"./p-aa688caf.js";import"./p-7900c24a.js";import"./p-986e5fe7.js";
/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/var t=Object.defineProperty,n=Object.getOwnPropertyDescriptor,_=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,i=(e,i,r,a)=>{if(i&&"object"==typeof i||"function"==typeof i)for(let s of _(i))o.call(e,s)||s===r||t(e,s,{get:()=>i[s],enumerable:!(a=n(i,s))||a.enumerable});return e},r={};i(r,e,"default");var a=["assign","flush","ftl","return","global","import","include","break","continue","local","nested","nt","setting","stop","t","lt","rt","fallback"],s=["attempt","autoesc","autoEsc","compress","comment","escape","noescape","function","if","list","items","sep","macro","noparse","noParse","noautoesc","noAutoEsc","outputformat","switch","visit","recurse"],u={close:">",id:"angle",open:"<"},c={close:"\\]",id:"bracket",open:"\\["},d={close:"[>\\]]",id:"auto",open:"[<\\[]"},l={close:"\\}",id:"dollar",open1:"\\$",open2:"\\{"},k={close:"\\]",id:"bracket",open1:"\\[",open2:"="};function p(e){return{brackets:[["<",">"],["[","]"],["(",")"],["{","}"]],comments:{blockComment:[`${e.open}--`,`--${e.close}`]},autoCloseBefore:"\n\r\t }]),.:;=",autoClosingPairs:[{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"},{open:'"',close:'"',notIn:["string"]},{open:"'",close:"'",notIn:["string"]}],surroundingPairs:[{open:'"',close:'"'},{open:"'",close:"'"},{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"},{open:"<",close:">"}],folding:{markers:{start:new RegExp(`${e.open}#(?:${s.join("|")})([^/${e.close}]*(?!/)${e.close})[^${e.open}]*$`),end:new RegExp(`${e.open}/#(?:${s.join("|")})[\\r\\n\\t ]*>`)}},onEnterRules:[{beforeText:new RegExp(`${e.open}#(?!(?:${a.join("|")}))([a-zA-Z_]+)([^/${e.close}]*(?!/)${e.close})[^${e.open}]*$`),afterText:new RegExp(`^${e.open}/#([a-zA-Z_]+)[\\r\\n\\t ]*${e.close}$`),action:{indentAction:r.languages.IndentAction.IndentOutdent}},{beforeText:new RegExp(`${e.open}#(?!(?:${a.join("|")}))([a-zA-Z_]+)([^/${e.close}]*(?!/)${e.close})[^${e.open}]*$`),action:{indentAction:r.languages.IndentAction.Indent}}]}}function g(){return{brackets:[["<",">"],["[","]"],["(",")"],["{","}"]],autoCloseBefore:"\n\r\t }]),.:;=",autoClosingPairs:[{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"},{open:'"',close:'"',notIn:["string"]},{open:"'",close:"'",notIn:["string"]}],surroundingPairs:[{open:'"',close:'"'},{open:"'",close:"'"},{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"},{open:"<",close:">"}],folding:{markers:{start:new RegExp(`[<\\[]#(?:${s.join("|")})([^/>\\]]*(?!/)[>\\]])[^<\\[]*$`),end:new RegExp(`[<\\[]/#(?:${s.join("|")})[\\r\\n\\t ]*>`)}},onEnterRules:[{beforeText:new RegExp(`[<\\[]#(?!(?:${a.join("|")}))([a-zA-Z_]+)([^/>\\]]*(?!/)[>\\]])[^[<\\[]]*$`),afterText:new RegExp("^[<\\[]/#([a-zA-Z_]+)[\\r\\n\\t ]*[>\\]]$"),action:{indentAction:r.languages.IndentAction.IndentOutdent}},{beforeText:new RegExp(`[<\\[]#(?!(?:${a.join("|")}))([a-zA-Z_]+)([^/>\\]]*(?!/)[>\\]])[^[<\\[]]*$`),action:{indentAction:r.languages.IndentAction.Indent}}]}}function A(e,t){const n=`_${e.id}_${t.id}`,_=e=>e.replace(/__id__/g,n),o=e=>{const t=e.source.replace(/__id__/g,n);return new RegExp(t,e.flags)};return{unicode:!0,includeLF:!1,start:_("default__id__"),ignoreCase:!1,defaultToken:"invalid",tokenPostfix:".freemarker2",brackets:[{open:"{",close:"}",token:"delimiter.curly"},{open:"[",close:"]",token:"delimiter.square"},{open:"(",close:")",token:"delimiter.parenthesis"},{open:"<",close:">",token:"delimiter.angle"}],[_("open__id__")]:new RegExp(e.open),[_("close__id__")]:new RegExp(e.close),[_("iOpen1__id__")]:new RegExp(t.open1),[_("iOpen2__id__")]:new RegExp(t.open2),[_("iClose__id__")]:new RegExp(t.close),[_("startTag__id__")]:o(/(@open__id__)(#)/),[_("endTag__id__")]:o(/(@open__id__)(\/#)/),[_("startOrEndTag__id__")]:o(/(@open__id__)(\/?#)/),[_("closeTag1__id__")]:o(/((?:@blank)*)(@close__id__)/),[_("closeTag2__id__")]:o(/((?:@blank)*\/?)(@close__id__)/),blank:/[ \t\n\r]/,keywords:["false","true","in","as","using"],directiveStartCloseTag1:/attempt|recover|sep|auto[eE]sc|no(?:autoe|AutoE)sc|compress|default|no[eE]scape|comment|no[pP]arse/,directiveStartCloseTag2:/else|break|continue|return|stop|flush|t|lt|rt|nt|nested|recurse|fallback|ftl/,directiveStartBlank:/if|else[iI]f|list|for[eE]ach|switch|case|assign|global|local|include|import|function|macro|transform|visit|stop|return|call|setting|output[fF]ormat|nested|recurse|escape|ftl|items/,directiveEndCloseTag1:/if|list|items|sep|recover|attempt|for[eE]ach|local|global|assign|function|macro|output[fF]ormat|auto[eE]sc|no(?:autoe|AutoE)sc|compress|transform|switch|escape|no[eE]scape/,escapedChar:/\\(?:[ntrfbgla\\'"\{=]|(?:x[0-9A-Fa-f]{1,4}))/,asciiDigit:/[0-9]/,integer:/[0-9]+/,nonEscapedIdStartChar:/[\$@-Z_a-z\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u1FFF\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183-\u2184\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3006\u3031-\u3035\u303B-\u303C\u3040-\u318F\u31A0-\u31BA\u31F0-\u31FF\u3300-\u337F\u3400-\u4DB5\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8D0-\uA8D9\uA8F2-\uA8F7\uA8FB\uA900-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF-\uA9D9\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA50-\uAA59\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5-\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uABC0-\uABE2\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40-\uFB41\uFB43-\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]/,escapedIdChar:/\\[\-\.:#]/,idStartChar:/(?:@nonEscapedIdStartChar)|(?:@escapedIdChar)/,id:/(?:@idStartChar)(?:(?:@idStartChar)|(?:@asciiDigit))*/,specialHashKeys:/\*\*|\*|false|true|in|as|using/,namedSymbols:/&lt;=|&gt;=|\\lte|\\lt|&lt;|\\gte|\\gt|&gt;|&amp;&amp;|\\and|-&gt;|->|==|!=|\+=|-=|\*=|\/=|%=|\+\+|--|<=|&&|\|\||:|\.\.\.|\.\.\*|\.\.<|\.\.!|\?\?|=|<|\+|-|\*|\/|%|\||\.\.|\?|!|&|\.|,|;/,arrows:["->","-&gt;"],delimiters:[";",":",",","."],stringOperators:["lte","lt","gte","gt"],noParseTags:["noparse","noParse","comment"],tokenizer:{[_("default__id__")]:[{include:_("@directive_token__id__")},{include:_("@interpolation_and_text_token__id__")}],[_("fmExpression__id__.directive")]:[{include:_("@blank_and_expression_comment_token__id__")},{include:_("@directive_end_token__id__")},{include:_("@expression_token__id__")}],[_("fmExpression__id__.interpolation")]:[{include:_("@blank_and_expression_comment_token__id__")},{include:_("@expression_token__id__")},{include:_("@greater_operators_token__id__")}],[_("inParen__id__.plain")]:[{include:_("@blank_and_expression_comment_token__id__")},{include:_("@directive_end_token__id__")},{include:_("@expression_token__id__")}],[_("inParen__id__.gt")]:[{include:_("@blank_and_expression_comment_token__id__")},{include:_("@expression_token__id__")},{include:_("@greater_operators_token__id__")}],[_("noSpaceExpression__id__")]:[{include:_("@no_space_expression_end_token__id__")},{include:_("@directive_end_token__id__")},{include:_("@expression_token__id__")}],[_("unifiedCall__id__")]:[{include:_("@unified_call_token__id__")}],[_("singleString__id__")]:[{include:_("@string_single_token__id__")}],[_("doubleString__id__")]:[{include:_("@string_double_token__id__")}],[_("rawSingleString__id__")]:[{include:_("@string_single_raw_token__id__")}],[_("rawDoubleString__id__")]:[{include:_("@string_double_raw_token__id__")}],[_("expressionComment__id__")]:[{include:_("@expression_comment_token__id__")}],[_("noParse__id__")]:[{include:_("@no_parse_token__id__")}],[_("terseComment__id__")]:[{include:_("@terse_comment_token__id__")}],[_("directive_token__id__")]:[[o(/(?:@startTag__id__)(@directiveStartCloseTag1)(?:@closeTag1__id__)/),"auto"===e.id?{cases:{"$1==<":{token:"@rematch",switchTo:`@default_angle_${t.id}`},"$1==[":{token:"@rematch",switchTo:`@default_bracket_${t.id}`}}}:[{token:"@brackets.directive"},{token:"delimiter.directive"},{cases:{"@noParseTags":{token:"tag",next:_("@noParse__id__.$3")},"@default":{token:"tag"}}},{token:"delimiter.directive"},{token:"@brackets.directive"}]],[o(/(?:@startTag__id__)(@directiveStartCloseTag2)(?:@closeTag2__id__)/),"auto"===e.id?{cases:{"$1==<":{token:"@rematch",switchTo:`@default_angle_${t.id}`},"$1==[":{token:"@rematch",switchTo:`@default_bracket_${t.id}`}}}:[{token:"@brackets.directive"},{token:"delimiter.directive"},{token:"tag"},{token:"delimiter.directive"},{token:"@brackets.directive"}]],[o(/(?:@startTag__id__)(@directiveStartBlank)(@blank)/),"auto"===e.id?{cases:{"$1==<":{token:"@rematch",switchTo:`@default_angle_${t.id}`},"$1==[":{token:"@rematch",switchTo:`@default_bracket_${t.id}`}}}:[{token:"@brackets.directive"},{token:"delimiter.directive"},{token:"tag"},{token:"",next:_("@fmExpression__id__.directive")}]],[o(/(?:@endTag__id__)(@directiveEndCloseTag1)(?:@closeTag1__id__)/),"auto"===e.id?{cases:{"$1==<":{token:"@rematch",switchTo:`@default_angle_${t.id}`},"$1==[":{token:"@rematch",switchTo:`@default_bracket_${t.id}`}}}:[{token:"@brackets.directive"},{token:"delimiter.directive"},{token:"tag"},{token:"delimiter.directive"},{token:"@brackets.directive"}]],[o(/(@open__id__)(@)/),"auto"===e.id?{cases:{"$1==<":{token:"@rematch",switchTo:`@default_angle_${t.id}`},"$1==[":{token:"@rematch",switchTo:`@default_bracket_${t.id}`}}}:[{token:"@brackets.directive"},{token:"delimiter.directive",next:_("@unifiedCall__id__")}]],[o(/(@open__id__)(\/@)((?:(?:@id)(?:\.(?:@id))*)?)(?:@closeTag1__id__)/),[{token:"@brackets.directive"},{token:"delimiter.directive"},{token:"tag"},{token:"delimiter.directive"},{token:"@brackets.directive"}]],[o(/(@open__id__)#--/),"auto"===e.id?{cases:{"$1==<":{token:"@rematch",switchTo:`@default_angle_${t.id}`},"$1==[":{token:"@rematch",switchTo:`@default_bracket_${t.id}`}}}:{token:"comment",next:_("@terseComment__id__")}],[o(/(?:@startOrEndTag__id__)([a-zA-Z_]+)/),"auto"===e.id?{cases:{"$1==<":{token:"@rematch",switchTo:`@default_angle_${t.id}`},"$1==[":{token:"@rematch",switchTo:`@default_bracket_${t.id}`}}}:[{token:"@brackets.directive"},{token:"delimiter.directive"},{token:"tag.invalid",next:_("@fmExpression__id__.directive")}]]],[_("interpolation_and_text_token__id__")]:[[o(/(@iOpen1__id__)(@iOpen2__id__)/),[{token:"bracket"===t.id?"@brackets.interpolation":"delimiter.interpolation"},{token:"bracket"===t.id?"delimiter.interpolation":"@brackets.interpolation",next:_("@fmExpression__id__.interpolation")}]],[/[\$#<\[\{]|(?:@blank)+|[^\$<#\[\{\n\r\t ]+/,{token:"source"}]],[_("string_single_token__id__")]:[[/[^'\\]/,{token:"string"}],[/@escapedChar/,{token:"string.escape"}],[/'/,{token:"string",next:"@pop"}]],[_("string_double_token__id__")]:[[/[^"\\]/,{token:"string"}],[/@escapedChar/,{token:"string.escape"}],[/"/,{token:"string",next:"@pop"}]],[_("string_single_raw_token__id__")]:[[/[^']+/,{token:"string.raw"}],[/'/,{token:"string.raw",next:"@pop"}]],[_("string_double_raw_token__id__")]:[[/[^"]+/,{token:"string.raw"}],[/"/,{token:"string.raw",next:"@pop"}]],[_("expression_token__id__")]:[[/(r?)(['"])/,{cases:{"r'":[{token:"keyword"},{token:"string.raw",next:_("@rawSingleString__id__")}],'r"':[{token:"keyword"},{token:"string.raw",next:_("@rawDoubleString__id__")}],"'":[{token:"source"},{token:"string",next:_("@singleString__id__")}],'"':[{token:"source"},{token:"string",next:_("@doubleString__id__")}]}}],[/(?:@integer)(?:\.(?:@integer))?/,{cases:{"(?:@integer)":{token:"number"},"@default":{token:"number.float"}}}],[/(\.)(@blank*)(@specialHashKeys)/,[{token:"delimiter"},{token:""},{token:"identifier"}]],[/(?:@namedSymbols)/,{cases:{"@arrows":{token:"meta.arrow"},"@delimiters":{token:"delimiter"},"@default":{token:"operators"}}}],[/@id/,{cases:{"@keywords":{token:"keyword.$0"},"@stringOperators":{token:"operators"},"@default":{token:"identifier"}}}],[/[\[\]\(\)\{\}]/,{cases:{"\\[":{cases:{"$S2==gt":{token:"@brackets",next:_("@inParen__id__.gt")},"@default":{token:"@brackets",next:_("@inParen__id__.plain")}}},"\\]":{cases:{..."bracket"===t.id?{"$S2==interpolation":{token:"@brackets.interpolation",next:"@popall"}}:{},..."bracket"===e.id?{"$S2==directive":{token:"@brackets.directive",next:"@popall"}}:{},[_("$S1==inParen__id__")]:{token:"@brackets",next:"@pop"},"@default":{token:"@brackets"}}},"\\(":{token:"@brackets",next:_("@inParen__id__.gt")},"\\)":{cases:{[_("$S1==inParen__id__")]:{token:"@brackets",next:"@pop"},"@default":{token:"@brackets"}}},"\\{":{cases:{"$S2==gt":{token:"@brackets",next:_("@inParen__id__.gt")},"@default":{token:"@brackets",next:_("@inParen__id__.plain")}}},"\\}":{cases:{..."bracket"===t.id?{}:{"$S2==interpolation":{token:"@brackets.interpolation",next:"@popall"}},[_("$S1==inParen__id__")]:{token:"@brackets",next:"@pop"},"@default":{token:"@brackets"}}}}}],[/\$\{/,{token:"delimiter.invalid"}]],[_("blank_and_expression_comment_token__id__")]:[[/(?:@blank)+/,{token:""}],[/[<\[][#!]--/,{token:"comment",next:_("@expressionComment__id__")}]],[_("directive_end_token__id__")]:[[/>/,"bracket"===e.id?{token:"operators"}:{token:"@brackets.directive",next:"@popall"}],[o(/(\/)(@close__id__)/),[{token:"delimiter.directive"},{token:"@brackets.directive",next:"@popall"}]]],[_("greater_operators_token__id__")]:[[/>/,{token:"operators"}],[/>=/,{token:"operators"}]],[_("no_space_expression_end_token__id__")]:[[/(?:@blank)+/,{token:"",switchTo:_("@fmExpression__id__.directive")}]],[_("unified_call_token__id__")]:[[/(@id)((?:@blank)+)/,[{token:"tag"},{token:"",next:_("@fmExpression__id__.directive")}]],[o(/(@id)(\/?)(@close__id__)/),[{token:"tag"},{token:"delimiter.directive"},{token:"@brackets.directive",next:"@popall"}]],[/./,{token:"@rematch",next:_("@noSpaceExpression__id__")}]],[_("no_parse_token__id__")]:[[o(/(@open__id__)(\/#?)([a-zA-Z]+)((?:@blank)*)(@close__id__)/),{cases:{"$S2==$3":[{token:"@brackets.directive"},{token:"delimiter.directive"},{token:"tag"},{token:""},{token:"@brackets.directive",next:"@popall"}],"$S2==comment":[{token:"comment"},{token:"comment"},{token:"comment"},{token:"comment"},{token:"comment"}],"@default":[{token:"source"},{token:"source"},{token:"source"},{token:"source"},{token:"source"}]}}],[/[^<\[\-]+|[<\[\-]/,{cases:{"$S2==comment":{token:"comment"},"@default":{token:"source"}}}]],[_("expression_comment_token__id__")]:[[/--[>\]]/,{token:"comment",next:"@pop"}],[/[^\->\]]+|[>\]\-]/,{token:"comment"}]],[_("terse_comment_token__id__")]:[[o(/--(?:@close__id__)/),{token:"comment",next:"@popall"}],[/[^<\[\-]+|[<\[\-]/,{token:"comment"}]]}}}function m(e){const t=A(u,e),n=A(c,e),_=A(d,e);return{...t,...n,..._,unicode:!0,includeLF:!1,start:`default_auto_${e.id}`,ignoreCase:!1,defaultToken:"invalid",tokenPostfix:".freemarker2",brackets:[{open:"{",close:"}",token:"delimiter.curly"},{open:"[",close:"]",token:"delimiter.square"},{open:"(",close:")",token:"delimiter.parenthesis"},{open:"<",close:">",token:"delimiter.angle"}],tokenizer:{...t.tokenizer,...n.tokenizer,..._.tokenizer}}}var f={conf:p(u),language:A(u,l)},F={conf:p(c),language:A(c,l)},b={conf:p(u),language:A(u,k)},x={conf:p(c),language:A(c,k)},$={conf:g(),language:m(l)},E={conf:g(),language:m(k)};export{b as TagAngleInterpolationBracket,f as TagAngleInterpolationDollar,E as TagAutoInterpolationBracket,$ as TagAutoInterpolationDollar,x as TagBracketInterpolationBracket,F as TagBracketInterpolationDollar}