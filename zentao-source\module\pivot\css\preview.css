#mainContent > .side-col.col-lg {width: 240px;}
.main-col .btn-toolbar {margin-bottom: 10px;}
.main-position {position: relative;}
.child-position {position: absolute; right: 0;}
.parent-position {padding-right: 160px;}
.datagrid {overflow: auto;}
.heading-padding, .datagrid-padding {padding-left: 6px; padding-right: 6px;}

.filter-items > .table-row{display: table!important;}
.filter-items .filter-item:first-child {padding-left: 0!important;}
.filterBox {display: flex; margin-bottom: 20px; margin-left: 6px; margin-right: 6px;}
.filter-items {display: flex; flex: 1; flex-wrap: wrap;}
.filter-item {padding: 0 16px 5px 0;}
.filter-item-grow {flex-grow: 1;}
.queryBtn {display: flex; align-items: center; padding-bottom: 5px; flex-basis: 60px;}
.filter-item .picker-selections {width: 128px; overflow: hidden; text-overflow: clip; white-space: nowrap;}
#filterItems .picker-selections {overflow: hidden; text-overflow: clip; white-space: nowrap;}
