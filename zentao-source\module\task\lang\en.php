<?php
/**
 * The task module English file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     task
 * @version     $Id: en.php 5040 2013-07-06 06:22:18Z <EMAIL> $
 * @link        https://www.zentao.net
 */
$lang->task->index               = "Home";
$lang->task->browse              = "Task List";
$lang->task->create              = "Create Task";
$lang->task->batchCreate         = "Batch Create";
$lang->task->batchCreateChildren = "Batch Create Child Tasks";
$lang->task->batchEdit           = "Batch Edit";
$lang->task->batchChangeModule   = "Batch Change Modules";
$lang->task->batchClose          = "Batch Close";
$lang->task->batchCancel         = "Batch Cancel";
$lang->task->edit                = "Edit Task";
$lang->task->delete              = "Delete";
$lang->task->deleteAction        = "Delete Task";
$lang->task->deleted             = "Deleted";
$lang->task->delayed             = 'Delayed';
$lang->task->view                = "Task Detail";
$lang->task->logEfforts          = "Effort";
$lang->task->record              = "Estimates";
$lang->task->recordedBy          = "Recorded By";
$lang->task->teamConsumed        = "Tean Consumed";
$lang->task->start               = "Start";
$lang->task->startAction         = "Start Task";
$lang->task->restart             = "Continue";
$lang->task->restartAction       = "Continue Task";
$lang->task->finishAction        = "Finish Task";
$lang->task->finish              = "Finish";
$lang->task->pause               = "Pause";
$lang->task->pauseAction         = "Pause Task";
$lang->task->close               = "Close";
$lang->task->closeAction         = "Close Task";
$lang->task->cancel              = "Cancel";
$lang->task->cancelAction        = "Cancel Task";
$lang->task->activateAction      = "Activate Task";
$lang->task->activate            = "Activate";
$lang->task->activatedDate       = "Activate Date";
$lang->task->export              = "Export Data";
$lang->task->exportAction        = "Export Task";
$lang->task->reportChart         = "Report Chart";
$lang->task->fromBug             = 'From Bug';
$lang->task->fromBugID           = 'From Bug ID';
$lang->task->case                = 'Linked Case';
$lang->task->process             = 'Process Task';
$lang->task->confirmStoryChange  = "Confirm Change";
$lang->task->confirmDeleteParent = 'Deleting a parent task will also delete all its child tasks. Are you sure you want to delete this task?';
$lang->task->storyChange         = "Story Changed";
$lang->task->progress            = 'Progress';
$lang->task->progressAB          = 'Progress';
$lang->task->progressTips        = 'Cost/(Cost+Left)';
$lang->task->copy                = 'Copy Task';
$lang->task->waitTask            = 'Waiting Task';
$lang->task->allModule           = 'All Module';
$lang->task->replace             = 'Replace';
$lang->task->committed           = 'Committed';
$lang->task->myEffort            = 'My Effort';
$lang->task->allEffort           = 'Team Effort';
$lang->task->teamOrder           = 'Order';
$lang->task->manageTeam          = 'Manage Team';
$lang->task->unfoldEffort        = 'Unfold Effort';
$lang->task->foldEffort          = 'Fold Effort';
$lang->task->addEffort           = 'Add Effort';
$lang->task->codeBranch          = 'Code Branch';
$lang->task->unlinkBranch        = 'Unlink code branch';
$lang->task->branchName          = 'Branch Name';
$lang->task->branchFrom          = 'Create from';
$lang->task->codeRepo            = 'Code Library';
$lang->task->relatedBranch       = 'Related Branch';
$lang->task->keywords            = 'Tags';
$lang->task->syncStory           = 'Sync to task';
$lang->task->addSibling          = 'Add Sibling';
$lang->task->addSub              = 'Add Child';
$lang->task->otherExecution      = 'Other Execution';

$lang->task->common            = 'Task';
$lang->task->id                = 'ID';
$lang->task->project           = $lang->projectCommon;
$lang->task->execution         = 'Execution';
$lang->task->stage             = $lang->executionCommon;
$lang->task->module            = 'Module';
$lang->task->moduleAB          = 'Module';
$lang->task->design            = 'Design';
$lang->task->story             = 'Story';
$lang->task->storyAB           = 'Story';
$lang->task->storySpec         = 'Story Description';
$lang->task->storyVerify       = 'Acceptance Criteria';
$lang->task->storyVersion      = 'Story Version';
$lang->task->storyFiles        = 'Story Dateien';
$lang->task->designVersion     = "Design Version";
$lang->task->color             = 'Color';
$lang->task->name              = 'Name';
$lang->task->type              = 'Type';
$lang->task->typeAB            = 'Type';
$lang->task->mode              = 'Mode';
$lang->task->sync2Gitlab       = 'Sync to GitLab';
$lang->task->pri               = 'Priority';
$lang->task->mailto            = 'Mailto';
$lang->task->estimate          = 'Estimates';
$lang->task->estimateAB        = 'Est.';
$lang->task->estimateLabel     = 'Est(Unit: h)';
$lang->task->left              = 'Hours Left';
$lang->task->leftAB            = 'Left';
$lang->task->consumed          = 'Total Cost';
$lang->task->currentConsumed   = 'Current Cost';
$lang->task->myConsumed        = 'My Cost';
$lang->task->consumedAB        = 'Cost';
$lang->task->consumedHours     = 'Cost';
$lang->task->hour              = 'Hours';
$lang->task->consumedThisTime  = 'Cost';
$lang->task->leftThisTime      = 'Left';
$lang->task->datePlan          = 'Timeframe';
$lang->task->estStarted        = 'StartDate';
$lang->task->realStarted       = 'ActualStart';
$lang->task->date              = 'Date';
$lang->task->deadline          = 'Deadline';
$lang->task->deadlineAB        = 'Deadline';
$lang->task->status            = 'Status';
$lang->task->statusAB          = 'Status';
$lang->task->subStatus         = 'Sub Status';
$lang->task->desc              = 'Description';
$lang->task->version           = 'Version';
$lang->task->estimateStartDate = 'Estimate Start Date';
$lang->task->actualStartDate   = 'Actual Start Date';
$lang->task->planDuration      = 'Plan Duration';
$lang->task->realDuration      = 'Real Duration';
$lang->task->version           = 'Version';
$lang->task->assign            = 'Assign';
$lang->task->assignAction      = 'Assign Task';
$lang->task->assignTo          = $lang->task->assign;
$lang->task->batchAssignTo     = 'Batch Assign';
$lang->task->assignedTo        = 'AssignTo';
$lang->task->assignedToAB      = 'AssignedTo';
$lang->task->assignedDate      = 'AssignedDate';
$lang->task->openedBy          = 'CreatedBy';
$lang->task->openedByAB        = 'Created';
$lang->task->openedDate        = 'CreatedDate';
$lang->task->openedDateAB      = 'CreatedOn';
$lang->task->finishedBy        = 'FinishedBy';
$lang->task->finishedByAB      = 'FinishedBy';
$lang->task->finishedDate      = 'ActualFinished';
$lang->task->finishedDateAB    = 'ActualFinish';
$lang->task->finishedList      = 'FinishedBy';
$lang->task->canceledBy        = 'CancelledBy';
$lang->task->canceledDate      = 'CancelledDate';
$lang->task->closedBy          = 'ClosedBy';
$lang->task->closedDate        = 'ClosedDate';
$lang->task->closedReason      = 'CloseReason';
$lang->task->lastEditedBy      = 'EditedBy';
$lang->task->lastEditedDate    = 'EditedDate';
$lang->task->lastEdited        = 'EditedBy';
$lang->task->recordWorkhour    = 'Effort';
$lang->task->editEffort        = 'Edit Effort';
$lang->task->deleteWorkhour    = 'Delete Workhours';
$lang->task->repo              = 'Repo';
$lang->task->mr                = 'Merge Requests';
$lang->task->entry             = 'Code Path';
$lang->task->lines             = 'Lines';
$lang->task->v1                = 'Version A';
$lang->task->v2                = 'Version B';
$lang->task->vision            = 'Vision';
$lang->task->colorTag          = 'Color';
$lang->task->files             = 'Files';
$lang->task->my                = 'My ';
$lang->task->hasConsumed       = 'Already Cost';
$lang->task->multiple          = 'Multiple Users';
$lang->task->multipleAB        = 'M';
$lang->task->teamSetting       = 'Team Setting';
$lang->task->team              = 'Team';
$lang->task->transfer          = 'Transfer';
$lang->task->transferTo        = 'Transfer To';
$lang->task->children          = 'Child Task';
$lang->task->childrenAB        = 'C';
$lang->task->parent            = 'Parent Task';
$lang->task->parentAB          = 'Parent';
$lang->task->showParent        = 'Show Parent Tasks';
$lang->task->lblPri            = 'P';
$lang->task->lblHour           = '(h)';
$lang->task->lblTestStory      = 'Story Tested';
$lang->task->teamMember        = 'Team Member';
$lang->task->addMember         = 'Add Member';
$lang->task->to                = 'To';
$lang->task->suffixHour        = 'h';
$lang->task->update            = 'Update';
$lang->task->isParent          = 'Is Parent';
$lang->task->path              = 'Path';

/* Fields of zt_taskestimate. */
$lang->task->task    = 'Task';
$lang->task->account = 'Account';
$lang->task->work    = 'Work';

$lang->task->recordWorkhourAction = 'Record Estimate';

$lang->task->ditto             = 'Ditto';
$lang->task->dittoNotice       = "This Task is not linked to %s like the last one!";
$lang->task->selectTestStory   = "Select {$lang->SRCommon}";
$lang->task->selectAllUser     = 'All Users';
$lang->task->noStory           = 'No Story Linked';
$lang->task->noAssigned        = 'Unassigned';
$lang->task->noFinished        = 'Unfinished';
$lang->task->noClosed          = 'Unclosed';
$lang->task->yesterdayFinished = 'Task Finished Yesterday';
$lang->task->allTasks          = 'Task';
$lang->task->linkMR            = 'Related MRs';
$lang->task->linkPR            = 'Related PRs';
$lang->task->linkCommit        = 'Related Commits';

$lang->task->statusList['']        = '';
$lang->task->statusList['wait']    = 'Waiting';
$lang->task->statusList['doing']   = 'Doing';
$lang->task->statusList['done']    = 'Done';
$lang->task->statusList['pause']   = 'Paused';
$lang->task->statusList['cancel']  = 'Cancelled';
$lang->task->statusList['closed']  = 'Closed';

$lang->task->typeList['']        = '';
$lang->task->typeList['design']  = 'Design';
$lang->task->typeList['devel']   = 'Develop';
$lang->task->typeList['request'] = 'Request';
$lang->task->typeList['test']    = 'Testing';
$lang->task->typeList['study']   = 'Study';
$lang->task->typeList['discuss'] = 'Discuss';
$lang->task->typeList['ui']      = 'UI';
$lang->task->typeList['affair']  = 'Work';
$lang->task->typeList['misc']    = 'Misc.';

$lang->task->priList[0]  = '';
$lang->task->priList[1]  = '1';
$lang->task->priList[2]  = '2';
$lang->task->priList[3]  = '3';
$lang->task->priList[4]  = '4';

$lang->task->reasonList['']       = '';
$lang->task->reasonList['done']   = 'Done';
$lang->task->reasonList['cancel'] = 'Cancelled';

$lang->task->modeList['linear'] = 'Multiple Person Serial';
$lang->task->modeList['multi']  = 'Multiple Person Parallel';

$lang->task->editModeList['single'] = 'Single';
$lang->task->editModeList['linear'] = 'Serial';
$lang->task->editModeList['multi']  = 'Parallel';

$lang->task->viewTypeList['tiled'] = 'Tiled';
$lang->task->viewTypeList['tree']  = 'Tree';

$lang->task->afterChoices['continueAdding'] = ' Continue Adding Tasks';
$lang->task->afterChoices['toTaskList']     = 'Go to Task List';
$lang->task->afterChoices['toStoryList']    = 'Go to Story List';

$lang->task->legendBasic  = 'Basic Info';
$lang->task->legendEffort = 'Effort';
$lang->task->legendLife   = 'Task Life';
$lang->task->legendDesc   = 'Task Description';
$lang->task->legendDetail = 'Task Detail';
$lang->task->legendMisc   = 'Misc.';

$lang->task->action = new stdclass();
$lang->task->action->linked2revision       = array('main' => '$date, linked by <strong>$actor</strong> to Revision <strong>$extra</strong>.');
$lang->task->action->unlinkedfromrevision  = array('main' => '$date, unlinked by <strong>$actor</strong> to Revision <strong>$extra</strong>.');
$lang->task->action->autobyparentrestarted = array('main' => '$date, continued parent task by <strong>$actor</strong>, this task automatically continue.');
$lang->task->action->autobychildrestarted  = array('main' => '$date, continued child task by <strong>$actor</strong>, this task automatically continue.');
$lang->task->action->autobyparentpaused    = array('main' => '$date, paused parent task by <strong>$actor</strong>, this task automatically pause.');
$lang->task->action->autobyparentcanceled  = array('main' => '$date, cancelled parent task by <strong>$actor</strong>, this task automatically cancel.');
$lang->task->action->autobyparentclosed    = array('main' => '$date, closed parent task by <strong>$actor</strong>, this task automatically close.');
$lang->task->action->autobychildstarted    = array('main' => '$date, started child task by <strong>$actor</strong>, this task automatically start.');
$lang->task->action->autobychildfinished   = array('main' => '$date, finished child task by <strong>$actor</strong>, this task automatically finish.');
$lang->task->action->autobychildactivated  = array('main' => '$date, activated child task by <strong>$actor</strong>, this task automatically activate.');

$lang->task->confirmDelete             = "Do you want to delete this task?";
$lang->task->confirmDeleteEffort       = "Do you want to delete it?";
$lang->task->confirmDeleteLastEffort   = "Do you want to delete the log? After deleting the last work log, the task status will be adjusted to Not Started.";
$lang->task->copyStoryTitle            = "Copy Story";
$lang->task->afterSubmit               = "Next ";
$lang->task->successSaved              = "Created!";
$lang->task->delayWarning              = "Delay %s days";
$lang->task->remindBug                 = "This task is converted from a bug. Do you want to update the Bug:%s?";
$lang->task->remindIssue               = "This task is converted from a issue. Do you want to update the Issue:%s?";
$lang->task->confirmChangeExecution    = "If you change {$lang->executionCommon}, Module, Story and AssignedTo will also be changed. Do you want to change it?";
$lang->task->confirmFinish             = '"Left Hour" is 0. Do you want to change the status to "Finished"?';
$lang->task->confirmRecord             = '"Left Hour" is 0. Do you want to set the task as "Finished"?';
$lang->task->confirmTransfer           = '"Left Hour" is 0，Do you want to assign to <strong>%s</strong> task?';
$lang->task->noticeTaskStart           = '"Cost Hour" and "Left Hour" cannot be 0 at the same time.';
$lang->task->noticeLinkStory           = "No story has been linked. You can for this %s";
$lang->task->noticeLinkStoryNoProduct  = "No story has been linked.";
$lang->task->noticeSaveRecord          = 'Your Hour is not saved. Please save it first.';
$lang->task->noticeManageTeam          = 'Task status is %s, can not manage team.';
$lang->task->teamNotEmpty              = 'Team can not be empty.';
$lang->task->commentActions            = '%s. %s, commented by <strong>%s</strong>.';
$lang->task->deniedNotice              = 'Only the %s can %s the task.';
$lang->task->deniedStatusNotice        = 'The task status is %s, the effort cannot be maintained.';
$lang->task->transferNotice            = 'Linear task cannot be transferred.';
$lang->task->noTask                    = 'No tasks yet. ';
$lang->task->noModule                  = '<div>You have no modules.</div><div>Manage now</div>';
$lang->task->createDenied              = "Create Task is denied in this %s";
$lang->task->cannotDeleteParent        = 'Cannot delete parent task';
$lang->task->addChildTask              = 'Because the task has cost hours, ZenTao will create a child task with the same name to record the cost housrs to ensure data consistency.';
$lang->task->selectTestStoryTip        = "The following {$lang->SRCommon} will be subtasks of this task";
$lang->task->effortOperateTips         = 'Only the project manager, the executive supervisor, and the department head have the authority to %s logs belonging to others.';
$lang->task->syncStoryToChildrenTip    = "Child tasks of %s do not have {$lang->SRCommon}, will {$lang->SRCommon} be synchronised with these child tasks?";

$lang->task->error = new stdclass();
$lang->task->error->totalNumber       = '"Total Cost" must be numbers.';
$lang->task->error->consumedNumber    = '"Current Cost" must be numbers.';
$lang->task->error->estimateNumber    = '"Estimates" must be a pasitive number.';
$lang->task->error->leftNumber        = '"Left" must be numbers.';
$lang->task->error->recordMinus       = '%s should not be negative number.';
$lang->task->error->consumedSmall     = '"Total Cost" must be > the last number.';
$lang->task->error->dateEmpty         = 'Please enter "Date"';
$lang->task->error->consumedThisTime  = 'Please enter "Hours Cost"';
$lang->task->error->left              = 'Please enter "Hours Left"';
$lang->task->error->work              = '"Comment" must be <  %d characters.';
$lang->task->error->teamMember        = 'Team members must be at least 2 people';
$lang->task->error->teamCantOperate   = 'Please activate the closed, suspended, and canceled tasks before setting the team.';
$lang->task->error->skipClose         = 'Task: %s is not "Finished" or "Cancelled". Do you want to close it?';
$lang->task->error->closeParent       = 'Task: %s is the Parent Task, which is automatically closed after all subtasks under the Parent Task are closed and cannot be closed manually.';
$lang->task->error->consumed          = 'Task: %s hour must be < 0. Ignore changes to this task.';
$lang->task->error->assignedTo        = 'Multi-user task in the current status cannot be assigned to a member who is not in the task team.';
$lang->task->error->consumedEmpty     = 'Cannot finish the task with 0 "Total Cost", please enter "Current Cost".';
$lang->task->error->consumedEmptyAB   = '"Current Cost" is 0, please confirm whether you submit.';
$lang->task->error->deadlineSmall     = '"Deadline" must be greater than "StartDate".';
$lang->task->error->alreadyStarted    = 'You cannot start this task, because it is started.';
$lang->task->error->realStartedEmpty  = '"Real Started" should not be empty.';
$lang->task->error->finishedDateEmpty = '"Finished Date" should not be empty.';
$lang->task->error->finishedDateSmall = '"Finished Date" should be > "Real Started"';
$lang->task->error->date              = 'The date should be <= today.';
$lang->task->error->leftEmptyAB       = 'When the task status is %s, "Hours Left" cannot be 0';
$lang->task->error->leftEmpty         = 'Task#%sWhen the task status is %s, "Left" cannot be 0';
$lang->task->error->notempty          = '%s must be > 0.';
$lang->task->error->teamLeftEmpty     = 'Please maintain team hours.';
$lang->task->error->beginLtExecution  = "The 'StartDate' of the task must be greater than or equal the 'Planned Begin' of %s to %s.";
$lang->task->error->beginGtExecution  = "The 'StartDate' of the task must be less than or equal the 'Planned End' of %s to %s.";
$lang->task->error->endGtExecution    = "The 'Deadline' of the task must be less than or equal the 'Planned End' of %s to %s.";
$lang->task->error->endLtExecution    = "The 'Deadline' of the task must be greater than or equal the 'Planned Begin' of %s to %s.";
$lang->task->error->dateExceed        = "Because the scheduled date of task %s exceeds the scheduled date of {$lang->execution->common}, it is automatically changed to the scheduled date of {$lang->execution->common}";
$lang->task->error->length            = "Length exceeds the limit of %d characters, cannot be saved. Please modify it again.";
$lang->task->error->emptyParentName   = "Contains subtasks, task names cannot be empty.";
$lang->task->error->noTestTask        = "Please select at least one {$lang->SRCommon}.";

/* Report. */
$lang->task->report = new stdclass();
$lang->task->report->common = 'Report';
$lang->task->report->select = 'Select Report Type';
$lang->task->report->create = 'Create Report';
$lang->task->report->value  = 'Tasks';

$lang->task->report->charts['tasksPerExecution']    = 'Group by ' . $lang->executionCommon . 'Task';
$lang->task->report->charts['tasksPerModule']       = 'Group by Module Task';
$lang->task->report->charts['tasksPerAssignedTo']   = 'Group by AssignedTo';
$lang->task->report->charts['tasksPerType']         = 'Group by Task Type';
$lang->task->report->charts['tasksPerPri']          = 'Group by Task Priority';
$lang->task->report->charts['tasksPerStatus']       = 'Group by Task Status';
$lang->task->report->charts['tasksPerDeadline']     = 'Group by Task Deadline';
$lang->task->report->charts['tasksPerEstimate']     = 'Group by Estimates';
$lang->task->report->charts['tasksPerLeft']         = 'Group by Hours Left';
$lang->task->report->charts['tasksPerConsumed']     = 'Group by Hours Cost';
$lang->task->report->charts['tasksPerFinishedBy']   = 'Group by FinishedBy';
$lang->task->report->charts['tasksPerClosedReason'] = 'Group by Close Reason';
$lang->task->report->charts['finishedTasksPerDay']  = 'Group by Tasks Finished/Day';

$lang->task->report->options = new stdclass();
$lang->task->report->options->graph = new stdclass();
$lang->task->report->options->type   = 'pie';
$lang->task->report->options->width  = 500;
$lang->task->report->options->height = 140;

$lang->task->report->tasksPerExecution    = new stdclass();
$lang->task->report->tasksPerModule       = new stdclass();
$lang->task->report->tasksPerAssignedTo   = new stdclass();
$lang->task->report->tasksPerType         = new stdclass();
$lang->task->report->tasksPerPri          = new stdclass();
$lang->task->report->tasksPerStatus       = new stdclass();
$lang->task->report->tasksPerDeadline     = new stdclass();
$lang->task->report->tasksPerEstimate     = new stdclass();
$lang->task->report->tasksPerLeft         = new stdclass();
$lang->task->report->tasksPerConsumed     = new stdclass();
$lang->task->report->tasksPerFinishedBy   = new stdclass();
$lang->task->report->tasksPerClosedReason = new stdclass();
$lang->task->report->finishedTasksPerDay  = new stdclass();

$lang->task->report->tasksPerExecution->item    = $lang->executionCommon;
$lang->task->report->tasksPerModule->item       = 'Module';
$lang->task->report->tasksPerAssignedTo->item   = 'AssignedTo';
$lang->task->report->tasksPerType->item         = 'Type';
$lang->task->report->tasksPerPri->item          = 'Priority';
$lang->task->report->tasksPerStatus->item       = 'Status';
$lang->task->report->tasksPerDeadline->item     = 'Date';
$lang->task->report->tasksPerEstimate->item     = 'Estimates';
$lang->task->report->tasksPerLeft->item         = 'Hours Left';
$lang->task->report->tasksPerConsumed->item     = 'Hours Cost';
$lang->task->report->tasksPerFinishedBy->item   = 'FinishedBy';
$lang->task->report->tasksPerClosedReason->item = 'Reason';
$lang->task->report->finishedTasksPerDay->item  = 'Date';

$lang->task->report->tasksPerExecution->graph    = new stdclass();
$lang->task->report->tasksPerModule->graph       = new stdclass();
$lang->task->report->tasksPerAssignedTo->graph   = new stdclass();
$lang->task->report->tasksPerType->graph         = new stdclass();
$lang->task->report->tasksPerPri->graph          = new stdclass();
$lang->task->report->tasksPerStatus->graph       = new stdclass();
$lang->task->report->tasksPerDeadline->graph     = new stdclass();
$lang->task->report->tasksPerEstimate->graph     = new stdclass();
$lang->task->report->tasksPerLeft->graph         = new stdclass();
$lang->task->report->tasksPerConsumed->graph     = new stdclass();
$lang->task->report->tasksPerFinishedBy->graph   = new stdclass();
$lang->task->report->tasksPerClosedReason->graph = new stdclass();
$lang->task->report->finishedTasksPerDay->graph  = new stdclass();

$lang->task->report->tasksPerExecution->graph->xAxisName    = $lang->executionCommon;
$lang->task->report->tasksPerModule->graph->xAxisName       = 'Module';
$lang->task->report->tasksPerAssignedTo->graph->xAxisName   = 'User';
$lang->task->report->tasksPerType->graph->xAxisName         = 'Type';
$lang->task->report->tasksPerPri->graph->xAxisName          = 'Priority';
$lang->task->report->tasksPerStatus->graph->xAxisName       = 'Status';
$lang->task->report->tasksPerDeadline->graph->xAxisName     = 'Date';
$lang->task->report->tasksPerEstimate->graph->xAxisName     = 'Estimates';
$lang->task->report->tasksPerLeft->graph->xAxisName         = 'Hours Left';
$lang->task->report->tasksPerConsumed->graph->xAxisName     = 'Hours Cost';
$lang->task->report->tasksPerFinishedBy->graph->xAxisName   = 'User';
$lang->task->report->tasksPerClosedReason->graph->xAxisName = 'Close Reason';

$lang->task->report->finishedTasksPerDay->type             = 'bar';
$lang->task->report->finishedTasksPerDay->graph->xAxisName = 'Date';

$lang->taskestimate = new stdclass();
$lang->taskestimate->consumed = 'Estimates';

$lang->task->overEsStartDate = 'The %s schedule start time has exceeded, please modify the %s schedule start time first';
$lang->task->overEsEndDate   = 'The %s schedule end time has exceeded, please modify the %s schedule end time first';

$lang->task->overParentEsStarted = 'StartDate is less than the parent task\'s startDate: %s';
$lang->task->overParentDeadline  = 'Deadline is greater than the parent task\'s deadline: %s';
$lang->task->overChildEstStarted = "Existed child task's startDate is less than the task's startDate: %s";
$lang->task->overChildDeadline   = "Existed child task's deadline is greater than the task's deadline: %s";

$lang->task->disabledHint = new stdclass();
$lang->task->disabledHint->assignedConfirmStoryChange = 'Changes can only be confirmed by the assignee.';
$lang->task->disabledHint->memberConfirmStoryChange   = 'Changes can only be confirmed by the task team member.';
