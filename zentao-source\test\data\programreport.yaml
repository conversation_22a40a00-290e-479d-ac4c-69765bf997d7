title: table zt_programreport
desc: ""
author: automated export
version: "1.0"
fields:
  - field: id
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: template
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: program
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: name
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: params
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: content
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: createdBy
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: createdDate
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: deleted
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
