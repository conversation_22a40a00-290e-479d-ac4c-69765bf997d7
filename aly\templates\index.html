<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>数据库管理系统</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <h1 class="mt-4 mb-4">MySQL 数据库管理工具</h1>
        
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">选择数据库连接</h5>
                <form action="/switch_connection" method="post" class="row g-3">
                    <div class="col-auto">
                        <select name="connection" class="form-select" id="connectionSelect">
                            <option value="aliyun" {% if current_connection == 'aliyun' %}selected{% endif %}>阿里云数据库</option>
                            <option value="ticket" {% if current_connection == 'ticket' %}selected{% endif %}>工单系统数据库</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-primary">切换连接</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="mb-4">
            <a href="/copy_table_form" class="btn btn-primary">复制表格</a>
            <a href="/export_databases" class="btn btn-secondary">导出数据库列表</a>
        </div>

        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">创建新数据库</h5>
                <form action="/create_database" method="post" class="row g-3">
                    <div class="col-auto">
                        <input type="text" class="form-control" name="db_name" placeholder="请输入数据库名称" required>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-primary">创建数据库</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <h5 class="card-title">数据库列表</h5>
                <div class="list-group">
                    {% for db in databases %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <span>{{ db }}</span>
                        <div>
                            <a href="/database/{{ db }}" class="btn btn-sm btn-success me-2">管理</a>
                            <a href="/delete_database/{{ db }}" class="btn btn-sm btn-danger" onclick="return confirm('确定要删除该数据库吗？')">删除</a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
</body>
</html>