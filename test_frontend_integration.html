<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工单集成测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .btn {
            background-color: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #337ecc;
        }
        .btn.success {
            background-color: #67C23A;
        }
        .btn.danger {
            background-color: #F56C6C;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border-left: 4px solid #409EFF;
        }
        .result.success {
            border-left-color: #67C23A;
            background-color: #f0f9ff;
        }
        .result.error {
            border-left-color: #F56C6C;
            background-color: #fef0f0;
        }
        .field-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .field-item {
            padding: 5px 10px;
            background: #e8f4fd;
            border-radius: 3px;
            font-size: 12px;
        }
        .loading {
            color: #999;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎫 工单集成功能测试</h1>
        <p>测试按状态获取工单列表的API是否返回完整字段</p>
        
        <div class="test-section">
            <h3>📋 API测试</h3>
            <button class="btn" onclick="testAPI('all')">测试全部工单</button>
            <button class="btn" onclick="testAPI('completed')">测试已完成工单</button>
            <button class="btn" onclick="testAPI('urgent')">测试紧急工单</button>
            <button class="btn success" onclick="testAllAPIs()">测试所有API</button>
            <div id="api-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🔍 字段对比测试</h3>
            <p>对比按状态获取的工单字段和项目工单字段是否一致</p>
            <button class="btn" onclick="compareFields()">开始字段对比</button>
            <div id="compare-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 测试结果汇总</h3>
            <div id="summary-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        let testResults = {};
        
        // 模拟登录token（实际应用中需要真实登录）
        const mockToken = 'mock-token-for-testing';
        
        async function makeAPICall(endpoint, params = {}) {
            try {
                const url = new URL(`${API_BASE}${endpoint}`);
                Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
                
                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${mockToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('API调用失败:', error);
                throw error;
            }
        }
        
        async function testAPI(status) {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = `<div class="loading">正在测试 ${status} 状态的工单API...</div>`;
            
            try {
                const data = await makeAPICall('/ticket-integration/tickets/by-status', {
                    status: status,
                    limit: 5
                });
                
                if (data.success && data.data && data.data.tickets) {
                    const tickets = data.data.tickets;
                    const firstTicket = tickets[0];
                    
                    testResults[status] = {
                        success: true,
                        count: tickets.length,
                        fields: firstTicket ? Object.keys(firstTicket) : [],
                        sampleData: firstTicket
                    };
                    
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>✅ ${status} 状态测试成功</h4>
                        <p><strong>工单数量:</strong> ${tickets.length}</p>
                        <p><strong>字段数量:</strong> ${firstTicket ? Object.keys(firstTicket).length : 0}</p>
                        ${firstTicket ? `
                        <p><strong>示例数据:</strong></p>
                        <ul>
                            <li>工单编号: ${firstTicket.feelec_ticket_no || '无'}</li>
                            <li>工单标题: ${firstTicket.feelec_title || '无'}</li>
                            <li>项目名称: ${firstTicket.project_name || '无'}</li>
                            <li>发布人: ${firstTicket.publisher_name || '无'}</li>
                            <li>状态: ${firstTicket.status_name || '无'}</li>
                        </ul>
                        ` : ''}
                    `;
                } else {
                    throw new Error(data.message || 'API返回数据格式错误');
                }
            } catch (error) {
                testResults[status] = {
                    success: false,
                    error: error.message
                };
                
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>❌ ${status} 状态测试失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                `;
            }
            
            updateSummary();
        }
        
        async function testAllAPIs() {
            const statuses = ['all', 'completed', 'urgent'];
            for (const status of statuses) {
                await testAPI(status);
                await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            }
        }
        
        async function compareFields() {
            const resultDiv = document.getElementById('compare-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = `<div class="loading">正在对比字段...</div>`;
            
            try {
                // 先测试按状态获取工单
                if (!testResults.all) {
                    await testAPI('all');
                }
                
                const statusFields = testResults.all.fields || [];
                
                // 期望的字段列表（基于项目工单列表）
                const expectedFields = [
                    'feelec_ticket_id', 'feelec_ticket_no', 'feelec_title',
                    'feelec_project_id', 'project_name', 'publisher_name',
                    'processor_name', 'department_name', 'company_name',
                    'status_name', 'priority_text', 'priority_color',
                    'template_name', 'source_text', 'is_overdue',
                    'create_time_formatted', 'first_assign_time_formatted',
                    'first_process_time_formatted', 'complete_time_formatted',
                    'deadline_formatted', 'process_duration_text',
                    'create_time', 'first_assign_time', 'first_process_time',
                    'complete_time', 'deadlines', 'feelec_content'
                ];
                
                const missingFields = expectedFields.filter(field => !statusFields.includes(field));
                const extraFields = statusFields.filter(field => !expectedFields.includes(field));
                
                let resultHTML = '<h4>🔍 字段对比结果</h4>';
                
                if (missingFields.length === 0) {
                    resultHTML += '<p class="success">✅ 所有必需字段都存在</p>';
                    resultDiv.className = 'result success';
                } else {
                    resultHTML += `<p class="error">❌ 缺少 ${missingFields.length} 个字段</p>`;
                    resultHTML += `<p><strong>缺少的字段:</strong> ${missingFields.join(', ')}</p>`;
                    resultDiv.className = 'result error';
                }
                
                resultHTML += `<p><strong>实际字段数量:</strong> ${statusFields.length}</p>`;
                resultHTML += `<p><strong>期望字段数量:</strong> ${expectedFields.length}</p>`;
                
                if (extraFields.length > 0) {
                    resultHTML += `<p><strong>额外字段:</strong> ${extraFields.join(', ')}</p>`;
                }
                
                resultDiv.innerHTML = resultHTML;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>❌ 字段对比失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                `;
            }
        }
        
        function updateSummary() {
            const summaryDiv = document.getElementById('summary-result');
            const results = Object.keys(testResults);
            
            if (results.length === 0) return;
            
            summaryDiv.style.display = 'block';
            
            const successCount = results.filter(status => testResults[status].success).length;
            const totalCount = results.length;
            
            let summaryHTML = '<h4>📊 测试汇总</h4>';
            summaryHTML += `<p><strong>测试进度:</strong> ${successCount}/${totalCount} 通过</p>`;
            
            results.forEach(status => {
                const result = testResults[status];
                if (result.success) {
                    summaryHTML += `<p>✅ ${status}: ${result.count} 条工单，${result.fields.length} 个字段</p>`;
                } else {
                    summaryHTML += `<p>❌ ${status}: ${result.error}</p>`;
                }
            });
            
            if (successCount === totalCount && totalCount >= 3) {
                summaryHTML += '<p class="success"><strong>🎉 所有测试通过！可以进行前端测试了！</strong></p>';
                summaryDiv.className = 'result success';
            } else {
                summaryDiv.className = 'result';
            }
            
            summaryDiv.innerHTML = summaryHTML;
        }
        
        // 页面加载完成后的提示
        window.onload = function() {
            console.log('🎫 工单集成测试页面已加载');
            console.log('请确保后端服务运行在 http://localhost:8000');
        };
    </script>
</body>
</html>
