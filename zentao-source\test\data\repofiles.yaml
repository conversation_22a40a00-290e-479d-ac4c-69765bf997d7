title: table zt_repofiles
desc: "代码文件"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: repo
    note: "代码库ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: revision
    note: "查看版本"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: path
    note: "文件地址"
    prefix: ""
    range: /trunk/zentaoext/zentaopro/cmmi/db/cmmi.sql
    postfix: ""
    loop: 0
    format: ""
  - field: parent
    note: "父级目录"
    range: /trunk/zentaoext/zentaopro/cmmi/db
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "类型"
    range: file
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: action
    note: "操作"
    range: A,C,D,M,G,U,R,I
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
