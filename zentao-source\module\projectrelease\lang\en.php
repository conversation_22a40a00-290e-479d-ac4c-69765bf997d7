<?php
/**
 * The release module zh-cn file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     release
 * @version     $Id: zh-cn.php 4129 2020-11-27 01:58:14Z wwccss $
 * @link        https://www.zentao.net
 */
$lang->projectrelease->common           = 'Release';
$lang->projectrelease->create           = "Create Release";
$lang->projectrelease->edit             = "Edit Release";
$lang->projectrelease->linkStory        = "Link Story";
$lang->projectrelease->linkBug          = "Link Bug";
$lang->projectrelease->delete           = "Delete Release";
$lang->projectrelease->deleted          = 'Deleted';
$lang->projectrelease->view             = "Release Detail";
$lang->projectrelease->browse           = "Release List";
$lang->projectrelease->changeStatus     = "Change Status";
$lang->projectrelease->batchUnlink      = "Batch Unlink";
$lang->projectrelease->batchUnlinkStory = "Batch Unlink Stories";
$lang->projectrelease->batchUnlinkBug   = "Batch Unlink Bugs";
$lang->projectrelease->unlinkStory      = "Unlink {$lang->SRCommon}";
$lang->projectrelease->unlinkBug        = 'Unlink Bug';
$lang->projectrelease->export           = 'Export as HTML';
$lang->projectrelease->browseAction     = "Release List";
$lang->projectrelease->notify           = 'notify';
$lang->projectrelease->publish          = "Publish";
$lang->projectrelease->product          = $lang->productCommon;
$lang->projectrelease->name             = $lang->product->system . ' Version';

$lang->projectrelease->featureBar['browse']['all']       = 'All';
$lang->projectrelease->featureBar['browse']['wait']      = 'Wait';
$lang->projectrelease->featureBar['browse']['normal']    = 'Released';
$lang->projectrelease->featureBar['browse']['terminate'] = 'Terminated';
