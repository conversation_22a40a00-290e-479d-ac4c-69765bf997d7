var c=(d,s,a)=>new Promise((u,o)=>{var l=t=>{try{e(a.next(t))}catch(n){o(n)}},i=t=>{try{e(a.throw(t))}catch(n){o(n)}},e=t=>t.done?u(t.value):Promise.resolve(t.value).then(l,i);e((a=a.apply(d,s)).next())});import{p as m,d as p,J as f,b9 as g,r as w,o as _,h as b,A as v,c as h,w as C,z as x,t as y,ad as k,f as S,F as I,c2 as A}from"./index.js";import{u as B}from"./useSync.hook-14394fcc.js";import"./plugin-37914809.js";import"./icon-bb3d09e7.js";import"./tables_list-f613fa36.js";const E=p({__name:"index",setup(d){const s=B();f();const a=()=>{var e;(e=window.fullscreen)==null||e.call(window)},u=()=>c(this,null,function*(){var n;const e=s.getStorageInfo,t=yield l();(n=window.saveAsDraft)==null||n.call(window,e,t)}),o=()=>c(this,null,function*(){var n;const e=s.getStorageInfo,t=yield l();(n=window.saveAsPublish)==null||n.call(window,e,t)}),l=()=>c(this,null,function*(){window.$loading.start();const e=document.querySelector(".go-edit-range"),t=yield A(e,{backgroundColor:null,allowTaint:!0,useCORS:!0});return window.$loading.finish(),t.toDataURL()}),i=g([{select:!0,title:"\u5168\u5C4F",event:a,className:"btn btn-full"},{select:!0,title:"\u5B58\u4E3A\u8349\u7A3F",event:u,className:"btn btn-full"},{select:!0,title:"\u53D1\u5E03",event:o,className:"btn btn-publish"}]);return(e,t)=>{const n=w("n-button");return _(!0),b(I,null,v(S(i),r=>(_(),h(n,{key:r.title,class:k(r.className),ghost:"",onClick:r.event},{default:C(()=>[x("span",null,y(r.title),1)]),_:2},1032,["class","onClick"]))),128)}}});var z=m(E,[["__scopeId","data-v-357dfaa3"]]);export{z as default};
