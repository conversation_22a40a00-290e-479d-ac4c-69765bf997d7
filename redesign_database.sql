-- ===================================================================
-- PMO督办管理系统 - 数据库重设计方案
-- 作者: AI Assistant
-- 日期: 2025-07-31
-- 目标: 统一数据结构，解决表冲突问题
-- ===================================================================

-- 1. 备份现有数据
-- ===================================================================

-- 备份 supervision_items 表
CREATE TABLE supervision_items_backup AS SELECT * FROM supervision_items;

-- 备份 company_progress 表
CREATE TABLE company_progress_backup AS SELECT * FROM company_progress;

-- 备份 company_supervision_status 表  
CREATE TABLE company_supervision_status_backup AS SELECT * FROM company_supervision_status;

-- 2. 创建新的统一状态表
-- ===================================================================

DROP TABLE IF EXISTS company_supervision_progress;

CREATE TABLE company_supervision_progress (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    supervision_item_id INT NOT NULL COMMENT '督办事项ID',
    company_id INT NOT NULL COMMENT '公司ID',
    status ENUM('√', 'O', '！', 'X', '—') NOT NULL DEFAULT 'X' COMMENT '进度状态: √已完成 O进行中 ！逾期 X未启动 —不需要执行',
    progress_description TEXT COMMENT '进度描述',
    existing_problems TEXT COMMENT '存在问题',
    next_plan TEXT COMMENT '下一步计划',
    completion_date DATE COMMENT '完成日期',
    updated_by VARCHAR(50) COMMENT '更新人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_supervision_company (supervision_item_id, company_id) COMMENT '督办事项-公司唯一索引',
    KEY idx_supervision_item (supervision_item_id) COMMENT '督办事项索引',
    KEY idx_company (company_id) COMMENT '公司索引',
    KEY idx_status (status) COMMENT '状态索引',
    KEY idx_updated_at (updated_at) COMMENT '更新时间索引',
    
    -- 外键约束
    FOREIGN KEY (supervision_item_id) REFERENCES supervision_items(id) ON DELETE CASCADE,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司督办进度表';

-- 3. 数据迁移：从 company_supervision_status 迁移数据
-- ===================================================================

INSERT INTO company_supervision_progress (
    supervision_item_id,
    company_id, 
    status,
    progress_description,
    existing_problems,
    next_plan,
    updated_by,
    created_at,
    updated_at
)
SELECT 
    supervision_item_id,
    company_id,
    status,
    progress_description,
    existing_problems,
    next_plan,
    updated_by,
    created_at,
    updated_at
FROM company_supervision_status
WHERE supervision_item_id IN (SELECT id FROM supervision_items WHERE deleted_at IS NULL)
  AND company_id IN (SELECT id FROM companies WHERE is_active = 1);

-- 4. 清理 supervision_items 表的冗余字段
-- ===================================================================

-- 移除公司状态字段
ALTER TABLE supervision_items 
DROP COLUMN caixian_status,
DROP COLUMN shouxian_status,
DROP COLUMN jinzu_status,
DROP COLUMN ziguan_status,
DROP COLUMN guangzu_status,
DROP COLUMN tongsheng_status,
DROP COLUMN danbao_status,
DROP COLUMN xiaodai_status,
DROP COLUMN baoli_status,
DROP COLUMN budongchan_status,
DROP COLUMN zhengxin_status,
DROP COLUMN jinfu_status,
DROP COLUMN benbu_status;

-- 移除冗余的进度字段（这些将通过动态计算获得）
ALTER TABLE supervision_items 
DROP COLUMN overall_progress,
DROP COLUMN completion_rate,
DROP COLUMN progress_status,
DROP COLUMN is_overdue;

-- 5. 删除旧的状态表
-- ===================================================================

DROP TABLE IF EXISTS company_progress;
DROP TABLE IF EXISTS company_supervision_status;

-- 6. 创建视图用于兼容性
-- ===================================================================

-- 创建督办事项详情视图（包含动态计算的进度信息）
CREATE VIEW v_supervision_items_detail AS
SELECT 
    si.id,
    si.sequence_number,
    si.work_dimension,
    si.work_theme,
    si.supervision_source,
    si.work_content,
    si.completion_deadline,
    si.completion_date,
    si.is_annual_assessment,
    si.created_at,
    si.updated_at,
    si.deleted_at,
    
    -- 动态计算的字段（通过子查询）
    (SELECT 
        CASE 
            WHEN COUNT(CASE WHEN csp.status = '！' THEN 1 END) > 0 THEN '！'
            WHEN COUNT(CASE WHEN csp.status = '√' THEN 1 END) = COUNT(CASE WHEN csp.status != '—' THEN 1 END) 
                 AND COUNT(CASE WHEN csp.status != '—' THEN 1 END) > 0 THEN '√'
            WHEN COUNT(CASE WHEN csp.status = 'O' THEN 1 END) > 0 THEN 'O'
            ELSE 'X'
        END
     FROM company_supervision_progress csp 
     JOIN companies c ON csp.company_id = c.id 
     WHERE csp.supervision_item_id = si.id AND c.is_active = 1
    ) AS overall_progress,
    
    (SELECT 
        CASE 
            WHEN COUNT(CASE WHEN csp.status != '—' THEN 1 END) = 0 THEN 100.0
            ELSE ROUND(
                COUNT(CASE WHEN csp.status = '√' THEN 1 END) * 100.0 / 
                COUNT(CASE WHEN csp.status != '—' THEN 1 END), 1
            )
        END
     FROM company_supervision_progress csp 
     JOIN companies c ON csp.company_id = c.id 
     WHERE csp.supervision_item_id = si.id AND c.is_active = 1
    ) AS completion_rate,
    
    (SELECT 
        CASE 
            WHEN si.completion_deadline IS NOT NULL AND CURDATE() > si.completion_deadline THEN 1
            ELSE 0
        END
    ) AS is_overdue

FROM supervision_items si
WHERE si.deleted_at IS NULL;

-- 7. 创建触发器确保数据完整性
-- ===================================================================

DELIMITER $$

-- 当添加新督办事项时，自动为所有活跃公司创建进度记录
CREATE TRIGGER tr_supervision_items_insert
AFTER INSERT ON supervision_items
FOR EACH ROW
BEGIN
    INSERT INTO company_supervision_progress (supervision_item_id, company_id, status, updated_by)
    SELECT NEW.id, c.id, 'X', 'system'
    FROM companies c 
    WHERE c.is_active = 1;
END$$

-- 当添加新公司时，自动为所有督办事项创建进度记录
CREATE TRIGGER tr_companies_insert
AFTER INSERT ON companies
FOR EACH ROW
BEGIN
    IF NEW.is_active = 1 THEN
        INSERT INTO company_supervision_progress (supervision_item_id, company_id, status, updated_by)
        SELECT si.id, NEW.id, 'X', 'system'
        FROM supervision_items si 
        WHERE si.deleted_at IS NULL;
    END IF;
END$$

-- 当公司状态变为活跃时，为所有督办事项创建进度记录
CREATE TRIGGER tr_companies_update
AFTER UPDATE ON companies
FOR EACH ROW
BEGIN
    IF OLD.is_active = 0 AND NEW.is_active = 1 THEN
        INSERT IGNORE INTO company_supervision_progress (supervision_item_id, company_id, status, updated_by)
        SELECT si.id, NEW.id, 'X', 'system'
        FROM supervision_items si 
        WHERE si.deleted_at IS NULL;
    END IF;
END$$

DELIMITER ;

-- 8. 创建索引优化查询性能
-- ===================================================================

-- 为视图查询优化创建复合索引
CREATE INDEX idx_csp_item_company_status ON company_supervision_progress (supervision_item_id, company_id, status);
CREATE INDEX idx_csp_status_active ON company_supervision_progress (status, supervision_item_id);

-- 9. 插入缺失的进度记录
-- ===================================================================

-- 确保每个督办事项都有对应的公司进度记录
INSERT IGNORE INTO company_supervision_progress (supervision_item_id, company_id, status, updated_by)
SELECT si.id, c.id, 'X', 'system'
FROM supervision_items si
CROSS JOIN companies c
WHERE si.deleted_at IS NULL 
  AND c.is_active = 1
  AND NOT EXISTS (
      SELECT 1 FROM company_supervision_progress csp 
      WHERE csp.supervision_item_id = si.id AND csp.company_id = c.id
  );

-- 10. 数据验证查询
-- ===================================================================

-- 验证数据完整性
SELECT 
    '数据验证' as check_type,
    (SELECT COUNT(*) FROM supervision_items WHERE deleted_at IS NULL) as supervision_items_count,
    (SELECT COUNT(*) FROM companies WHERE is_active = 1) as active_companies_count,
    (SELECT COUNT(*) FROM company_supervision_progress) as progress_records_count,
    (SELECT COUNT(*) FROM supervision_items WHERE deleted_at IS NULL) * 
    (SELECT COUNT(*) FROM companies WHERE is_active = 1) as expected_records_count;

-- 验证状态分布
SELECT status, COUNT(*) as count 
FROM company_supervision_progress 
GROUP BY status 
ORDER BY status;

-- ===================================================================
-- 重设计完成！
-- ===================================================================
