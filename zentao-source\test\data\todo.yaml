title: table zt_todo
desc: "待办"
author: ly
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: account
    note: "用户名"
    fields:
    - field: account1
      range: admin,user{99},test{100},dev{100},pm{100},po{100},td{100},pd{100},qd{100},top{100},outside{100}
    - field: account2
      range: "[],1-99,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: date
    note: "日期"
    range: "(-1M)-(+1w):-1D"
    prefix: ""
    postfix: ""
    loop: 0
    type: timestamp
    format: "YY/MM/DD"
  - field: begin
    note: "开始"
    range: "1000-1059:2"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: end
    note: "结束"
    range: "1400-1459:2"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "类型"
    range: custom,bug,task,story,testtask
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: objectID
    note: "关联编号"
    range: 0,1-20,1-20,1-20,1-20
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: pri
    note: "优先级"
    range: 1-4
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: name
    note: "待办名称"
    fields:
      - field: name1
        range: 自定义,BUG,任务,需求,测试单
      - field: name2
        range: 1-200,1-200,1-200,1-200.1-200
    prefix: ""
    postfix: "的待办"
    loop: 0
    format: ""
  - field: desc
    note: "描述"
    range: 1-10000
    prefix: "这是一个待办的描述"
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: "状态"
    range: wait,doing,done,closed
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: private
    note: "私人事务"
    range: 1{10},0{10}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: config
    note: "配置"
    range: ""
    prefix: ""
    postfix: ""
    format: ""
  - field: assignedTo
    note: "指派给"
    fields:
    - field: ass1
      range: admin,user{99},test{100},dev{100},pm{100},po{100},td{100},pd{100},qd{100},top{100},outside{100}
    - field: ass2
      range: "[],1-99,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100"
    prefix: ""
    postfix: ""
    format: ""
  - field: assignedBy
    note: "由谁指派"
    fields:
    - field: ass1
      range: admin,user{99},test{100},dev{100},pm{100},po{100},td{100},pd{100},qd{100},top{100},outside{100}
    - field: ass2
      range: "[],1-99,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100"
    prefix: ""
    postfix: ""
    format: ""
  - field: assignedDate
    note: "指派日期"
    range: "(M)-(w):60m"
    type: timestamp
    prefix: ""
    postfix: ""
    format: "YY-MM-DD hh:mm:ss"
  - field: finishedBy
    note: "由谁完成"
    range: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  #- field: finishedDate
  #  note: "完成时间"
  #  range: ""
  #  prefix: ""
  #  postfix: ""
  #  loop: 0
  #  format: ""
  - field: closedBy
    note: "由谁关闭"
    range: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  #- field: closedDate
  #  note: "关闭时间"
  #  range: ""
