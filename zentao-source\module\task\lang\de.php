<?php
/**
 * The task module English file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     task
 * @version     $Id: en.php 5040 2013-07-06 06:22:18Z <EMAIL> $
 * @link        https://www.zentao.net
 */
$lang->task->index               = "Index";
$lang->task->browse              = "Aufgabenliste";
$lang->task->create              = "Aufgabe erstellen";
$lang->task->batchCreate         = "Mehere erstellen";
$lang->task->batchCreateChildren = "Mehere Teilaufgaben";
$lang->task->batchEdit           = "Mehere bearneiten";
$lang->task->batchChangeModule   = "Mehere Module ändern";
$lang->task->batchClose          = "Mehere schließen";
$lang->task->batchCancel         = "Mehere abbrechen";
$lang->task->edit                = "Bearbeiten";
$lang->task->delete              = "Löschen";
$lang->task->deleteAction        = "Delete Task";
$lang->task->deleted             = "Gelöscht";
$lang->task->delayed             = 'Verzögert';
$lang->task->view                = "Übersicht";
$lang->task->logEfforts          = "Stunden";
$lang->task->record              = "Schätzung";
$lang->task->recordedBy          = "Recorded By";
$lang->task->teamConsumed        = "Tean Consumed";
$lang->task->start               = "Start";
$lang->task->startAction         = "Start Task";
$lang->task->restart             = "Fortführen";
$lang->task->restartAction       = "Continue Task";
$lang->task->finishAction        = "Finish Task";
$lang->task->finish              = "Abschließen";
$lang->task->pause               = "Pause";
$lang->task->pauseAction         = "Pause Task";
$lang->task->close               = "Schließen";
$lang->task->closeAction         = "Close Task";
$lang->task->cancel              = "Abbrechen";
$lang->task->cancelAction        = "Cancel Task";
$lang->task->activateAction      = "Activate Task";
$lang->task->activate            = "Aktivieren";
$lang->task->activatedDate       = "Activate Date";
$lang->task->export              = "Daten exportieren";
$lang->task->exportAction        = "Export Task";
$lang->task->reportChart         = "Bericht Chart";
$lang->task->fromBug             = 'Von Bug';
$lang->task->fromBugID           = 'From Bug ID';
$lang->task->case                = 'Fall';
$lang->task->process             = 'Process Task';
$lang->task->confirmStoryChange  = "Storyäanderung bestätigen";
$lang->task->confirmDeleteParent = 'Das Löschen einer übergeordneten Aufgabe löscht auch alle untergeordneten Aufgaben. Möchten Sie diese Aufgabe wirklich löschen?';
$lang->task->storyChange         = "Story Changed";
$lang->task->progress            = 'Fortschritt';
$lang->task->progressAB          = 'Fortschritt';
$lang->task->progressTips        = 'Genutzt/(Genutzt+Rest)';
$lang->task->copy                = 'Aufgabe kopieren';
$lang->task->waitTask            = 'Waiting Task';
$lang->task->allModule           = 'All Module';
$lang->task->replace             = 'Replace';
$lang->task->committed           = 'Committed';
$lang->task->myEffort            = 'My Effort';
$lang->task->allEffort           = 'Team Effort';
$lang->task->teamOrder           = 'Order';
$lang->task->manageTeam          = 'Manage Team';
$lang->task->unfoldEffort        = 'Unfold Effort';
$lang->task->foldEffort          = 'Fold Effort';
$lang->task->addEffort           = 'Add Effort';
$lang->task->codeBranch          = 'Code Branch';
$lang->task->unlinkBranch        = 'Unlink code branch';
$lang->task->branchName          = 'Branch Name';
$lang->task->branchFrom          = 'Create from';
$lang->task->codeRepo            = 'Code Library';
$lang->task->relatedBranch       = 'Related Branch';
$lang->task->keywords            = 'Tags';
$lang->task->syncStory           = 'Sync to task';
$lang->task->addSibling          = 'Add Sibling';
$lang->task->addSub              = 'Add Child';
$lang->task->otherExecution      = 'Other Execution';

$lang->task->common            = 'Aufgabe';
$lang->task->id                = 'ID';
$lang->task->project           = $lang->projectCommon;
$lang->task->execution         = 'Execution';
$lang->task->stage             = $lang->executionCommon;
$lang->task->module            = 'Modul';
$lang->task->moduleAB          = 'Modul';
$lang->task->design            = 'Design';
$lang->task->story             = 'Story';
$lang->task->storyAB           = 'Story';
$lang->task->storySpec         = 'Story Beschreibung';
$lang->task->storyVerify       = 'Abnahmebedingungen';
$lang->task->storyVersion      = 'Story Version';
$lang->task->storyFiles        = 'Story Dateien';
$lang->task->designVersion     = "Design Version";
$lang->task->color             = 'Color';
$lang->task->name              = 'Name';
$lang->task->type              = 'Typ';
$lang->task->typeAB            = 'Typ';
$lang->task->mode              = 'Mode';
$lang->task->sync2Gitlab       = 'Sync to GitLab';
$lang->task->pri               = 'Priorität';
$lang->task->mailto            = 'Mail an';
$lang->task->estimate          = 'Geplant(h)';
$lang->task->estimateAB        = 'Geplant(h)';
$lang->task->estimateLabel     = 'Est(Unit: h)';
$lang->task->left              = 'Rest';
$lang->task->leftAB            = 'Rest';
$lang->task->consumed          = 'Genutzt';
$lang->task->currentConsumed   = 'Current Cost';
$lang->task->myConsumed        = 'Von mir';
$lang->task->consumedAB        = 'Genutzt';
$lang->task->consumedHours     = 'Genutzt';
$lang->task->hour              = 'Stunden';
$lang->task->consumedThisTime  = 'Stunden';
$lang->task->leftThisTime      = 'Rest';
$lang->task->datePlan          = 'Plan';
$lang->task->estStarted        = 'Startschätzung';
$lang->task->realStarted       = 'Tatsächlicher Start';
$lang->task->date              = 'Datum';
$lang->task->deadline          = 'Fälligkeit';
$lang->task->deadlineAB        = 'Fälligkeit';
$lang->task->status            = 'Status';
$lang->task->statusAB          = 'Status';
$lang->task->subStatus         = 'Sub Status';
$lang->task->desc              = 'Beschreibung';
$lang->task->version           = 'Version';
$lang->task->estimateStartDate = 'Estimate Start Date';
$lang->task->actualStartDate   = 'Actual Start Date';
$lang->task->planDuration      = 'Plan Duration';
$lang->task->realDuration      = 'Real Duration';
$lang->task->version           = 'Version';
$lang->task->assign            = 'Zuordnen';
$lang->task->assignAction      = 'Assign Task';
$lang->task->assignTo          = $lang->task->assign;
$lang->task->batchAssignTo     = 'Mehere zuordnen';
$lang->task->assignedTo        = 'Zuständiger';
$lang->task->assignedToAB      = 'Zuständiger';
$lang->task->assignedDate      = 'Zugewisen am';
$lang->task->openedBy          = 'Ersteller';
$lang->task->openedByAB        = 'Created';
$lang->task->openedDate        = 'Erstellt am';
$lang->task->openedDateAB      = 'Hinzufügen';
$lang->task->finishedBy        = 'Abgeschlossen';
$lang->task->finishedByAB      = 'Abgeschlossen';
$lang->task->finishedDate      = 'Abgeschlossen am';
$lang->task->finishedDateAB    = 'Abgeschlossen am';
$lang->task->finishedList      = 'FinishedBy';
$lang->task->canceledBy        = 'Abgebrochen von';
$lang->task->canceledDate      = 'Abgebrochen am';
$lang->task->closedBy          = 'Geschlossen von';
$lang->task->closedDate        = 'Gecshlossen am';
$lang->task->closedReason      = 'Grund';
$lang->task->lastEditedBy      = 'Bearbeiter';
$lang->task->lastEditedDate    = 'Bearbeitet am';
$lang->task->lastEdited        = 'Luletzt bearbeitet';
$lang->task->recordWorkhour    = 'Fortschritt erfassen';
$lang->task->editEffort        = 'Schätzung bearbeiten';
$lang->task->deleteWorkhour    = 'Schätzung löschen';
$lang->task->repo              = 'Repo';
$lang->task->mr                = 'Merge Requests';
$lang->task->entry             = 'Code Path';
$lang->task->lines             = 'Lines';
$lang->task->v1                = 'Version A';
$lang->task->v2                = 'Version B';
$lang->task->vision            = 'Vision';
$lang->task->colorTag          = 'Farb Tag';
$lang->task->files             = 'Dateien';
$lang->task->my                = 'My ';
$lang->task->hasConsumed       = 'Verbraucht';
$lang->task->multiple          = 'Mehere Aufgaben';
$lang->task->multipleAB        = ' Mehrere';
$lang->task->teamSetting       = 'Team Setting';
$lang->task->team              = 'Team';
$lang->task->transfer          = 'Transfer';
$lang->task->transferTo        = 'Transfer nach';
$lang->task->children          = 'Teilaufgabe';
$lang->task->childrenAB        = 'Teilaufgabe';
$lang->task->parent            = 'Hauptaufgabe';
$lang->task->parentAB          = 'Hauptaufgabe';
$lang->task->showParent        = 'Show Parent Tasks';
$lang->task->lblPri            = 'P';
$lang->task->lblHour           = '(h)';
$lang->task->lblTestStory      = 'Story Tested';
$lang->task->teamMember        = 'Team Member';
$lang->task->addMember         = 'Add Member';
$lang->task->to                = 'To';
$lang->task->suffixHour        = 'h';
$lang->task->update            = 'Update';
$lang->task->isParent          = 'Is Parent';
$lang->task->path              = 'Path';

/* Fields of zt_taskestimate. */
$lang->task->task    = 'Task';
$lang->task->account = 'Account';
$lang->task->work    = 'Work';

$lang->task->recordWorkhourAction = 'Record Estimate';

$lang->task->ditto             = 'Dito';
$lang->task->dittoNotice       = "Diese Aufgabe gehört nicht zum %s wie die Vorherige!";
$lang->task->selectTestStory   = "Select {$lang->SRCommon}";
$lang->task->selectAllUser     = 'Alle';
$lang->task->noStory           = 'Keine Story';
$lang->task->noAssigned        = 'Nicht zugeordnet';
$lang->task->noFinished        = 'Nicht abgeschlossen';
$lang->task->noClosed          = 'Niht geschlossen';
$lang->task->yesterdayFinished = 'Gestern abgeschlossen';
$lang->task->allTasks          = 'Alle Aufgaben';
$lang->task->linkMR            = 'Related MRs';
$lang->task->linkPR            = 'Related PRs';
$lang->task->linkCommit        = 'Related Commits';

$lang->task->statusList['']        = '';
$lang->task->statusList['wait']    = 'Wartend';
$lang->task->statusList['doing']   = 'In Arbeit';
$lang->task->statusList['done']    = 'Erledigt';
$lang->task->statusList['pause']   = 'Pausiert';
$lang->task->statusList['cancel']  = 'Abgebrochen';
$lang->task->statusList['closed']  = 'Geschlossen';

$lang->task->typeList['']        = '';
$lang->task->typeList['design']  = 'Design';
$lang->task->typeList['devel']   = 'Entwicklung';
$lang->task->typeList['request'] = 'Request';
$lang->task->typeList['test']    = 'Test';
$lang->task->typeList['study']   = 'Studie';
$lang->task->typeList['discuss'] = 'Diskusion';
$lang->task->typeList['ui']      = 'UI';
$lang->task->typeList['affair']  = 'Arbeit';
$lang->task->typeList['misc']    = 'Sonstiges';

$lang->task->priList[0]  = '';
$lang->task->priList[1]  = '1';
$lang->task->priList[2]  = '2';
$lang->task->priList[3]  = '3';
$lang->task->priList[4]  = '4';

$lang->task->reasonList['']       = '';
$lang->task->reasonList['done']   = 'Erledigt';
$lang->task->reasonList['cancel'] = 'Abgebrochen';

$lang->task->modeList['linear'] = 'Multiple Person Serial';
$lang->task->modeList['multi']  = 'Multiple Person Parallel';

$lang->task->editModeList['single'] = 'Single';
$lang->task->editModeList['linear'] = 'Serial';
$lang->task->editModeList['multi']  = 'Parallel';

$lang->task->viewTypeList['tiled'] = 'Tiled';
$lang->task->viewTypeList['tree']  = 'Tree';

$lang->task->afterChoices['continueAdding'] = ' Weitere Aufgaben erfassen';
$lang->task->afterChoices['toTaskList']     = 'Zurück zur Aufgabenliste';
$lang->task->afterChoices['toStoryList']    = 'Zurück zur Storyliste';

$lang->task->legendBasic  = 'Basis Info';
$lang->task->legendEffort = 'Aufwand';
$lang->task->legendLife   = 'Aufgabenentwicklung';
$lang->task->legendDesc   = 'Aufgabenbeschreibung';
$lang->task->legendDetail = 'Task Detail';
$lang->task->legendMisc   = 'Misc.';

$lang->task->action = new stdclass();
$lang->task->action->linked2revision       = array('main' => '$date, linked by <strong>$actor</strong> to Revision <strong>$extra</strong>.');
$lang->task->action->unlinkedfromrevision  = array('main' => '$date, unlinked by <strong>$actor</strong> to Revision <strong>$extra</strong>.');
$lang->task->action->autobyparentrestarted = array('main' => '$date, continued parent task by <strong>$actor</strong>, this task automatically continue.');
$lang->task->action->autobychildrestarted  = array('main' => '$date, continued child task by <strong>$actor</strong>, this task automatically continue.');
$lang->task->action->autobyparentpaused    = array('main' => '$date, paused parent task by <strong>$actor</strong>, this task automatically pause.');
$lang->task->action->autobyparentcanceled  = array('main' => '$date, cancelled parent task by <strong>$actor</strong>, this task automatically cancel.');
$lang->task->action->autobyparentclosed    = array('main' => '$date, closed parent task by <strong>$actor</strong>, this task automatically close.');
$lang->task->action->autobychildstarted    = array('main' => '$date, started child task by <strong>$actor</strong>, this task automatically start.');
$lang->task->action->autobychildfinished   = array('main' => '$date, finished child task by <strong>$actor</strong>, this task automatically finish.');
$lang->task->action->autobychildactivated  = array('main' => '$date, activated child task by <strong>$actor</strong>, this task automatically activate.');

$lang->task->confirmDelete             = "Möchten Sie diese Aufgabe löschen?";
$lang->task->confirmDeleteEffort       = "Do you want to delete it?";
$lang->task->confirmDeleteLastEffort   = "Do you want to delete the log? After deleting the last work log, the task status will be adjusted to Not Started.";
$lang->task->copyStoryTitle            = "Story kopieren";
$lang->task->afterSubmit               = "Nach der Erstellung,";
$lang->task->successSaved              = "Erstellt!";
$lang->task->delayWarning              = "Verzögerung %s Tage";
$lang->task->remindBug                 = "Diese Aufgabe wurde aus einem Bus erzeugt. Möchten Sie den Bug aktualisieren:%s?";
$lang->task->remindIssue               = "This task is converted from a issue. Do you want to update the Issue:%s?";
$lang->task->confirmChangeExecution    = "Wenn Sie {$lang->executionCommon} ändern, das verwendete Modul, Story and Assignor will be changed. Do you want to do it?";
$lang->task->confirmFinish             = '"Reststunden " sind 0. Möchten Sue den Status auf Erledigt setzen?';
$lang->task->confirmRecord             = '"Reststunden " sind 0. Möchten Sie den Task auf Erledigt setzen?';
$lang->task->confirmTransfer           = '"Left Hour" is 0，Do you want to assign to <strong>%s</strong> task?';
$lang->task->noticeTaskStart           = '"Cost Hour" and "Left Hour" cannot be 0 at the same time.';
$lang->task->noticeLinkStory           = "No story has been linked. You can for this %s";
$lang->task->noticeLinkStoryNoProduct  = "No story has been linked.";
$lang->task->noticeSaveRecord          = 'Ihre Stunden wurden nicht gespeichrt. Bitte erst speichern.';
$lang->task->noticeManageTeam          = 'Task status is %s, can not manage team.';
$lang->task->teamNotEmpty              = 'Team can not be empty.';
$lang->task->commentActions            = '%s. %s, kommentiert von <strong>%s</strong>.';
$lang->task->deniedNotice              = 'Nur %s kann die Aufgabe %s.';
$lang->task->deniedStatusNotice        = 'The task status is %s, the effort cannot be maintained.';
$lang->task->transferNotice            = 'Linear task cannot be transferred.';
$lang->task->noTask                    = 'Keine Aufagben. ';
$lang->task->noModule                  = '<div>You have no modules.</div><div>Manage now</div>';
$lang->task->createDenied              = "Aufgben erstellen it in diesem %s gesperrt";
$lang->task->cannotDeleteParent        = 'Cannot delete parent task';
$lang->task->addChildTask              = 'Because the task has already consumed consumption, to ensure data consistency, we will help you create a subtask with the same name to record the consumption.';
$lang->task->selectTestStoryTip        = "The following {$lang->SRCommon} will be subtasks of this task";
$lang->task->effortOperateTips         = 'Only the project manager, the executive supervisor, and the department head have the authority to %s logs belonging to others.';
$lang->task->syncStoryToChildrenTip    = "Child tasks of %s do not have {$lang->SRCommon}, will {$lang->SRCommon} be synchronised with these child tasks?";

$lang->task->error = new stdclass();
$lang->task->error->totalNumber       = '"Total Cost" must be numbers.';
$lang->task->error->consumedNumber    = '"Verbraucht" muss eine Zahl sein.';
$lang->task->error->estimateNumber    = '"Schätzungen" müssen immer eine positive Zahl sein.';
$lang->task->error->leftNumber        = '"Left" must be numbers.';
$lang->task->error->recordMinus       = '%s should not be negative number.';
$lang->task->error->consumedSmall     = '"Genutzt" muss larger than before.';
$lang->task->error->dateEmpty         = 'Please enter "Date"';
$lang->task->error->consumedThisTime  = 'Bitte geben Sie die Stunden an';
$lang->task->error->left              = 'Bitte geben Sie die verbleibenden Stunden an"';
$lang->task->error->work              = '"Bemerkung" muss kleiner als %d Zeichen sein.';
$lang->task->error->teamMember        = 'Team members must be at least 2 people';
$lang->task->error->teamCantOperate   = 'Please activate the closed, suspended, and canceled tasks before setting the team.';
$lang->task->error->skipClose         = 'Aufgabe: %s ist nicht "Erledigt" oder "Abgebrochen". Möchten Sie die Aufgabe jetzt schließen?';
$lang->task->error->closeParent       = 'Task: %s is the Parent Task, which is automatically closed after all subtasks under the Parent Task are closed and cannot be closed manually.';
$lang->task->error->consumed          = 'Aufgabe: %s Hour must be more than 0. Ignore changes to this Task.';
$lang->task->error->assignedTo        = 'Mehere Tasks können nicht zugewisen werden, da die Benutzer nicht teil des Teams sind.';
$lang->task->error->consumedEmpty     = 'Cannot finish the task with 0 "Total Cost", please enter "Current Cost".';
$lang->task->error->consumedEmptyAB   = '"Current Cost" is 0, please confirm whether you submit.';
$lang->task->error->deadlineSmall     = '"Deadline" must be greater than "StartDate".';
$lang->task->error->alreadyStarted    = 'You cannot start this task, because it is started.';
$lang->task->error->realStartedEmpty  = '"Real Started" should not be empty.';
$lang->task->error->finishedDateEmpty = '"Finished Date" should not be empty.';
$lang->task->error->finishedDateSmall = '"Finished Date" should be > "Real Started"';
$lang->task->error->date              = 'The date should be <= today.';
$lang->task->error->leftEmptyAB       = 'When the task status is %s, "Hours Left" cannot be 0';
$lang->task->error->leftEmpty         = 'Task#%sWhen the task status is %s, "Left" cannot be 0';
$lang->task->error->notempty          = '%s must be > 0.';
$lang->task->error->teamLeftEmpty     = 'Please maintain team hours.';
$lang->task->error->beginLtExecution  = "The 'StartDate' of the task must be greater than or equal the 'Planned Begin' of %s to %s.";
$lang->task->error->beginGtExecution  = "The 'StartDate' of the task must be less than or equal the 'Planned End' of %s to %s.";
$lang->task->error->endGtExecution    = "The 'Deadline' of the task must be less than or equal the 'Planned End' of %s to %s.";
$lang->task->error->endLtExecution    = "The 'Deadline' of the task must be greater than or equal the 'Planned Begin' of %s to %s.";
$lang->task->error->dateExceed        = "Because the scheduled date of task %s exceeds the scheduled date of {$lang->execution->common}, it is automatically changed to the scheduled date of {$lang->execution->common}";
$lang->task->error->length            = "Length exceeds the limit of %d characters, cannot be saved. Please modify it again.";
$lang->task->error->emptyParentName   = "Contains subtasks, task names cannot be empty.";
$lang->task->error->noTestTask        = "Please select at least one {$lang->SRCommon}.";

/* Report. */
$lang->task->report = new stdclass();
$lang->task->report->common = 'Bericht';
$lang->task->report->select = 'Gruppieren nach';
$lang->task->report->create = 'Erstellen';
$lang->task->report->value  = 'Task Anzahl';

$lang->task->report->charts['tasksPerExecution']    = '' . $lang->executionCommon;
$lang->task->report->charts['tasksPerModule']       = 'Modul';
$lang->task->report->charts['tasksPerAssignedTo']   = 'Zustängikeit';
$lang->task->report->charts['tasksPerType']         = 'Kategorie';
$lang->task->report->charts['tasksPerPri']          = 'Priorität';
$lang->task->report->charts['tasksPerStatus']       = 'Status';
$lang->task->report->charts['tasksPerDeadline']     = 'Fälligkeit';
$lang->task->report->charts['tasksPerEstimate']     = 'geplanten Stunden';
$lang->task->report->charts['tasksPerLeft']         = 'restlichen Stunden';
$lang->task->report->charts['tasksPerConsumed']     = 'genutzten Stunden';
$lang->task->report->charts['tasksPerFinishedBy']   = 'abgeschlossen von';
$lang->task->report->charts['tasksPerClosedReason'] = 'Grund der Schließung';
$lang->task->report->charts['finishedTasksPerDay']  = 'Geschlossen/Tag';

$lang->task->report->options = new stdclass();
$lang->task->report->options->graph = new stdclass();
$lang->task->report->options->type   = 'pie';
$lang->task->report->options->width  = 500;
$lang->task->report->options->height = 140;

$lang->task->report->tasksPerExecution    = new stdclass();
$lang->task->report->tasksPerModule       = new stdclass();
$lang->task->report->tasksPerAssignedTo   = new stdclass();
$lang->task->report->tasksPerType         = new stdclass();
$lang->task->report->tasksPerPri          = new stdclass();
$lang->task->report->tasksPerStatus       = new stdclass();
$lang->task->report->tasksPerDeadline     = new stdclass();
$lang->task->report->tasksPerEstimate     = new stdclass();
$lang->task->report->tasksPerLeft         = new stdclass();
$lang->task->report->tasksPerConsumed     = new stdclass();
$lang->task->report->tasksPerFinishedBy   = new stdclass();
$lang->task->report->tasksPerClosedReason = new stdclass();
$lang->task->report->finishedTasksPerDay  = new stdclass();

$lang->task->report->tasksPerExecution->item    = $lang->executionCommon;
$lang->task->report->tasksPerModule->item       = 'Modul';
$lang->task->report->tasksPerAssignedTo->item   = 'Konto';
$lang->task->report->tasksPerType->item         = 'Typ';
$lang->task->report->tasksPerPri->item          = 'Priorität';
$lang->task->report->tasksPerStatus->item       = 'Status';
$lang->task->report->tasksPerDeadline->item     = 'Datum';
$lang->task->report->tasksPerEstimate->item     = 'Stunden';
$lang->task->report->tasksPerLeft->item         = 'Übrig';
$lang->task->report->tasksPerConsumed->item     = 'Verbraucht';
$lang->task->report->tasksPerFinishedBy->item   = 'Benutzer';
$lang->task->report->tasksPerClosedReason->item = 'Grund';
$lang->task->report->finishedTasksPerDay->item  = 'Datum';

$lang->task->report->tasksPerExecution->graph    = new stdclass();
$lang->task->report->tasksPerModule->graph       = new stdclass();
$lang->task->report->tasksPerAssignedTo->graph   = new stdclass();
$lang->task->report->tasksPerType->graph         = new stdclass();
$lang->task->report->tasksPerPri->graph          = new stdclass();
$lang->task->report->tasksPerStatus->graph       = new stdclass();
$lang->task->report->tasksPerDeadline->graph     = new stdclass();
$lang->task->report->tasksPerEstimate->graph     = new stdclass();
$lang->task->report->tasksPerLeft->graph         = new stdclass();
$lang->task->report->tasksPerConsumed->graph     = new stdclass();
$lang->task->report->tasksPerFinishedBy->graph   = new stdclass();
$lang->task->report->tasksPerClosedReason->graph = new stdclass();
$lang->task->report->finishedTasksPerDay->graph  = new stdclass();

$lang->task->report->tasksPerExecution->graph->xAxisName    = $lang->executionCommon;
$lang->task->report->tasksPerModule->graph->xAxisName       = 'Modul';
$lang->task->report->tasksPerAssignedTo->graph->xAxisName   = 'Benutzer';
$lang->task->report->tasksPerType->graph->xAxisName         = 'Typ';
$lang->task->report->tasksPerPri->graph->xAxisName          = 'Priorität';
$lang->task->report->tasksPerStatus->graph->xAxisName       = 'Status';
$lang->task->report->tasksPerDeadline->graph->xAxisName     = 'Datum';
$lang->task->report->tasksPerEstimate->graph->xAxisName     = 'Stunden';
$lang->task->report->tasksPerLeft->graph->xAxisName         = 'Übrig';
$lang->task->report->tasksPerConsumed->graph->xAxisName     = 'Verbraucht';
$lang->task->report->tasksPerFinishedBy->graph->xAxisName   = 'Benutzer';
$lang->task->report->tasksPerClosedReason->graph->xAxisName = 'Grund';

$lang->task->report->finishedTasksPerDay->type             = 'bar';
$lang->task->report->finishedTasksPerDay->graph->xAxisName = 'Datum';

$lang->taskestimate = new stdclass();
$lang->taskestimate->consumed = 'Schätzung';

$lang->task->overEsStartDate = 'The %s schedule start time has exceeded, please modify the %s schedule start time first';
$lang->task->overEsEndDate   = 'The %s schedule end time has exceeded, please modify the %s schedule end time first';

$lang->task->overParentEsStarted = 'StartDate is less than the parent task\'s startDate: %s';
$lang->task->overParentDeadline  = 'Deadline is greater than the parent task\'s deadline: %s';
$lang->task->overChildEstStarted = "Existed child task's startDate is less than the task's startDate: %s";
$lang->task->overChildDeadline   = "Existed child task's deadline is greater than the task's deadline: %s";

$lang->task->disabledHint = new stdclass();
$lang->task->disabledHint->assignedConfirmStoryChange = 'Changes can only be confirmed by the assignee.';
$lang->task->disabledHint->memberConfirmStoryChange   = 'Changes can only be confirmed by the task team member.';
