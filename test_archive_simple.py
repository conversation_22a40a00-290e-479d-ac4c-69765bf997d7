#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试档案数量功能
"""

import os
import sys

def test_archive_count_simple():
    """简单测试档案统计逻辑"""
    print("🧪 开始测试档案数量统计逻辑...")
    
    # 项目档案目录
    archive_base_dir = "project_archive_materials"
    project_code = "TEST_001"
    project_dir = os.path.join(archive_base_dir, project_code)
    
    print(f"📁 检查项目目录: {project_dir}")
    
    if not os.path.exists(project_dir):
        print(f"❌ 项目目录不存在: {project_dir}")
        return
    
    print(f"✅ 项目目录存在: {project_dir}")
    
    total_files = 0
    markdown_files = 0
    other_files = 0
    
    # 遍历项目目录统计文件
    for root, dirs, files in os.walk(project_dir):
        print(f"📂 扫描目录: {root}")
        for file in files:
            print(f"   📄 发现文件: {file}")
            if file.endswith(('.pdf', '.doc', '.docx', '.md', '.txt', '.xlsx', '.xls', '.jpg', '.png')):
                total_files += 1
                if file.endswith('.md'):
                    markdown_files += 1
                    print(f"   ✅ Markdown文件: {file}")
                else:
                    other_files += 1
                    print(f"   📋 其他文件: {file}")
    
    # 生成摘要
    if total_files == 0:
        archive_summary = "暂无档案"
    elif markdown_files == 0:
        archive_summary = f"共{total_files}个文件"
    else:
        archive_summary = f"共{total_files}个文件(md:{markdown_files})"
    
    print("\n📊 统计结果:")
    print(f"   总文件数: {total_files}")
    print(f"   Markdown文件数: {markdown_files}")
    print(f"   其他文件数: {other_files}")
    print(f"   档案摘要: {archive_summary}")
    
    if markdown_files > 0:
        print("✅ 成功检测到Markdown文件")
    else:
        print("⚠️  未检测到Markdown文件")
    
    return {
        "total_files": total_files,
        "markdown_files": markdown_files,
        "other_files": other_files,
        "archive_summary": archive_summary
    }

def create_test_files():
    """创建一些测试文件"""
    print("\n🔧 创建测试文件...")
    
    archive_base_dir = "project_archive_materials"
    project_code = "TEST_001"
    project_dir = os.path.join(archive_base_dir, project_code)
    
    # 确保目录存在
    os.makedirs(project_dir, exist_ok=True)
    
    # 创建一些测试文件
    test_files = [
        "项目立项.md",
        "需求分析.md", 
        "技术方案.pdf",
        "项目计划.xlsx"
    ]
    
    for file_name in test_files:
        file_path = os.path.join(project_dir, file_name)
        if not os.path.exists(file_path):
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"# {file_name}\n\n这是一个测试文件。")
            print(f"   ✅ 创建文件: {file_name}")
        else:
            print(f"   📄 文件已存在: {file_name}")

if __name__ == "__main__":
    print("🚀 档案数量功能简单测试开始")
    print("=" * 50)
    
    # 创建测试文件
    create_test_files()
    
    # 测试统计逻辑
    result = test_archive_count_simple()
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")
    
    if result and result['markdown_files'] > 0:
        print("🎉 档案数量统计功能正常工作！")
    else:
        print("⚠️  档案数量统计可能存在问题")
