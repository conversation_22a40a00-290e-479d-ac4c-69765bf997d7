<?php
/**
 * The testtask module English file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     testtask
 * @version     $Id: en.php 4490 2013-02-27 03:27:05Z <EMAIL> $
 * @link        https://www.zentao.net
 */
$lang->testtask->index            = "Home";
$lang->testtask->create           = "Test erstellen";
$lang->testtask->reportChart      = 'Bericht';
$lang->testtask->delete           = "Löschen";
$lang->testtask->importUnitResult = "Import Unit Result";
$lang->testtask->importUnit       = "Import Unit Result"; //Fix bug custom required testtask.
$lang->testtask->browseUnits      = "Unit Test List";
$lang->testtask->unitCases        = "Browse Unit Cases List";
$lang->testtask->view             = "Übersicht";
$lang->testtask->edit             = "Bearbeiten";
$lang->testtask->browse           = "Testaufgaben";
$lang->testtask->linkCase         = "Fälle";
$lang->testtask->selectVersion    = "Version wählen";
$lang->testtask->unlinkCase       = "Verknüpfung aufheben";
$lang->testtask->batchUnlinkCases = "Mehrere Verknüpfungen aufheben";
$lang->testtask->batchAssign      = "Mehrere zuordnen";
$lang->testtask->runCase          = "Ausführen";
$lang->testtask->running          = ", running.";
$lang->testtask->runningLog       = "Execution Log";
$lang->testtask->runNode          = "Executed by %s,Execute on node %s %s";
$lang->testtask->batchRun         = "Mehrere ausführen";
$lang->testtask->results          = "Ergebnisse";
$lang->testtask->createBug        = "Bug erstellen";
$lang->testtask->assign           = 'Zuordnen';
$lang->testtask->cases            = 'Fälle';
$lang->testtask->groupCase        = "Nach Gruppe";
$lang->testtask->pre              = 'Vorherige';
$lang->testtask->next             = 'Nächste';
$lang->testtask->start            = "Start";
$lang->testtask->close            = "Schließen";
$lang->testtask->wait             = "Wartend";
$lang->testtask->block            = "Block";
$lang->testtask->activate         = "Aktiviert";
$lang->testtask->testing          = "Testen";
$lang->testtask->blocked          = "Blockiert";
$lang->testtask->done             = "Getestet";
$lang->testtask->totalStatus      = "Alle";
$lang->testtask->all              = 'Alle';
$lang->testtask->allTasks         = 'Alle';
$lang->testtask->collapseAll      = 'Zuklappen';
$lang->testtask->expandAll        = 'Aufklappen';
$lang->testtask->auto             = 'Test Automation Tasks';
$lang->testtask->task             = 'Test Task';
$lang->testtask->run              = 'Test Run ID';
$lang->testtask->job              = 'Job';
$lang->testtask->compile          = 'Compile';
$lang->testtask->duration         = 'Duration';
$lang->testtask->myInvolved       = 'Involved';

$lang->testtask->viewAction     = "View Request";
$lang->testtask->casesAction    = 'Browse Cases List';
$lang->testtask->activateAction = "Activate Request";
$lang->testtask->blockAction    = "Block Request";
$lang->testtask->closeAction    = "Close Request";
$lang->testtask->startAction    = "Start Request";
$lang->testtask->resultsAction  = "Case Result";
$lang->testtask->reportAction   = 'Case Report';

$lang->testtask->id                = 'ID';
$lang->testtask->common            = 'Testaufgabe';
$lang->testtask->product           = $lang->productCommon;
$lang->testtask->project           = $lang->projectCommon;
$lang->testtask->execution         = $lang->execution->common;
$lang->testtask->type              = 'Typ';
$lang->testtask->build             = 'Build';
$lang->testtask->owner             = 'Besitzer';
$lang->testtask->members           = 'Participant';
$lang->testtask->executor          = 'Executor';
$lang->testtask->execTime          = 'Exec Time';
$lang->testtask->pri               = 'Priorität';
$lang->testtask->name              = 'Name';
$lang->testtask->unitName          = 'Name Of Unit Test';
$lang->testtask->begin             = 'Start';
$lang->testtask->end               = 'Ende';
$lang->testtask->realBegan         = 'Actual Started Date';
$lang->testtask->realFinishedDate  = 'Actual Finished Date';
$lang->testtask->desc              = 'Beschreibung';
$lang->testtask->mailto            = 'Mail an';
$lang->testtask->status            = 'Status';
$lang->testtask->statusAB          = 'Status';
$lang->testtask->subStatus         = 'Sub Status';
$lang->testtask->testreport        = 'Test Report';
$lang->testtask->assignCase        = 'Assign Testcase';
$lang->testtask->assignedTo        = 'Zugeordnet';
$lang->testtask->linkVersion       = 'Version';
$lang->testtask->lastRunAccount    = 'Ausgeführt von';
$lang->testtask->lastRunTime       = 'Ausgeführt am';
$lang->testtask->lastRunResult     = 'Ergebnis';
$lang->testtask->reportField       = 'Bericht';
$lang->testtask->files             = 'Upload';
$lang->testtask->case              = 'Fall';
$lang->testtask->version           = 'Version';
$lang->testtask->caseResult        = 'Test Ergebnis';
$lang->testtask->stepResults       = 'Schritt Ergebnis';
$lang->testtask->lastRunner        = 'Ausgeführt von';
$lang->testtask->lastRunDate       = 'Ausgeführt am';
$lang->testtask->createdBy         = 'Created By';
$lang->testtask->createdDate       = 'Created Date';
$lang->testtask->date              = 'Datum';
$lang->testtask->deleted           = "Deleted";
$lang->testtask->resultFile        = "Result File";
$lang->testtask->caseCount         = 'Case Count';
$lang->testtask->passCount         = 'Pass';
$lang->testtask->failCount         = 'Fail';
$lang->testtask->skipChangedCases  = 'The testcases that are not to be confirmed are ignored.';
$lang->testtask->summary           = '%s cases, %s failures, %s time.';
$lang->testtask->stepSummary       = 'Total %s steps, %s passes, %s failures.';
$lang->testtask->unitSummary       = 'Total %s unit test results.';
$lang->testtask->pageSummary       = 'Total testtasks: <strong>%s</strong>.';
$lang->testtask->mySummary         = 'Total testtasks: <strong>%s</strong>, Wait: <strong>%s</strong>, Testing: <strong>%s</strong>, Blocked: <strong>%s</strong>.';
$lang->testtask->allSummary        = 'Total testtasks: <strong>%s</strong>, Wait: <strong>%s</strong>, Testing: <strong>%s</strong>, Blocked: <strong>%s</strong>, Done: <strong>%s</strong>.';
$lang->testtask->checkedAllSummary = 'Seleted: <strong>%total%</strong>, Wait: <strong>%wait%</strong>, Testing: <strong>%testing%</strong>, Blocked: <strong>%blocked%</strong>, Done: <strong>%done%</strong>.';
$lang->testtask->emptyCases        = 'Cases %s have no steps, filter to show.';
$lang->testtask->caseEmpty         = 'Selected cases have no steps or do not meet the execution conditions, and have been ignored.';

$lang->testtask->beginAndEnd = 'Datum';
$lang->testtask->to          = 'An';

$lang->testtask->legendDesc      = 'Beschreibung';
$lang->testtask->legendReport    = 'Bericht';
$lang->testtask->legendBasicInfo = 'Basis Info';

$lang->testtask->statusList['wait']    = 'Wartend';
$lang->testtask->statusList['doing']   = 'In Arbeit';
$lang->testtask->statusList['done']    = 'Erledigt';
$lang->testtask->statusList['blocked'] = 'Blockiert';

$lang->testtask->priList[1] = '1';
$lang->testtask->priList[2] = '2';
$lang->testtask->priList[3] = '3';
$lang->testtask->priList[4] = '4';

$lang->testtask->unlinkedCases = 'Unverknüpfte Fälle';
$lang->testtask->linkByBuild   = 'Mit Build verknüpfen';
$lang->testtask->linkByStory   = 'Mit Story verknüpfen';
$lang->testtask->linkByBug     = 'Mit Bug verknüpfen';
$lang->testtask->linkBySuite   = 'Mit Suite verknüpfen';
$lang->testtask->browseBySuite = 'Browse by Suite';
$lang->testtask->passAll       = 'Alle erfolgreich';
$lang->testtask->pass          = 'Erfolgreich';
$lang->testtask->fail          = 'Fehlgeschlagen';
$lang->testtask->showResult    = 'Ausführungen <label class="label primary-pale rounded-full h-3 px-1.5 mx-1">%s</label>';
$lang->testtask->showFail      = 'Fehlgeschlagen <label class="label danger-pale rounded-full h-3 px-1.5 mx-1">%s</label>';
$lang->testtask->runInTask     = ' <strong>%s</strong> by <strong>%s</strong>, the build is <strong>%s</strong> ';
$lang->testtask->runCaseResult = ', executed %s, the results is <span class="text-%s font-bold">%s</span>.';

$lang->testtask->confirmDelete     = 'Möchten Sie dieses Build löschen?';
$lang->testtask->confirmUnlinkCase = 'Möchten Sie die Verknüpfung zu dem Fall aufheben?';
$lang->testtask->noticeNoOther     = "No test builds for this {$lang->productCommon}.";
$lang->testtask->noTesttask        = 'Keine Testaufgaben. ';
$lang->testtask->checkLinked       = "Please check whether the {$lang->productCommon} that the test request is linked to has been linked to a {$lang->executionCommon}.";
$lang->testtask->noImportData      = 'The imported XML does not parse the data.';
$lang->testtask->unitXMLFormat     = 'Please select a file in JUnit XML format.';
$lang->testtask->titleOfAuto       = "%s automated testing";
$lang->testtask->cannotBeParsed    = 'The content of the imported XML file is in the wrong format and cannot be parsed.';
$lang->testtask->finishedDateLess  = 'Actual Finished Date cannot be <= Begin Date %s';
$lang->testtask->finishedDateMore  = 'Actual Finished Date cannot be > Today';
$lang->testtask->emptyUnitTip      = 'No unit test results.';

$lang->testtask->assignedToMe  = 'Meine';
$lang->testtask->allCases      = 'Alle Fälle';

$lang->testtask->lblCases      = 'Fälle';
$lang->testtask->lblUnlinkCase = 'Fallverknüpfung aufheben';
$lang->testtask->lblRunCase    = 'Fälle ausführen';
$lang->testtask->lblResults    = 'Ergebnisse';

$lang->testtask->placeholder = new stdclass();
$lang->testtask->placeholder->begin = 'Start';
$lang->testtask->placeholder->end   = 'Ende';

$lang->testtask->mail = new stdclass();
$lang->testtask->mail->create = new stdclass();
$lang->testtask->mail->edit   = new stdclass();
$lang->testtask->mail->close  = new stdclass();
$lang->testtask->mail->create->title = "%s erstellte Testaufgabe #%s:%s";
$lang->testtask->mail->edit->title   = "%s abgeschlossene Testaufgaben #%s:%s";
$lang->testtask->mail->close->title  = "%s geschlossene Testaufgaben #%s:%s";

$lang->testtask->action = new stdclass();
$lang->testtask->action->testtaskopened  = '$date,  <strong>$actor</strong> öffnete die Testaufgabe <strong>$extra</strong>.' . "\n";
$lang->testtask->action->testtaskstarted = '$date,  <strong>$actor</strong> startete die Testaufgabe <strong>$extra</strong>.' . "\n";
$lang->testtask->action->testtaskclosed  = '$date,  <strong>$actor</strong> hat die Testaufgabe abgeschlossen <strong>$extra</strong>.' . "\n";

$lang->testtask->unexecuted = 'Nicht ausgeführt';

/* Statistical statement. */
$lang->testtask->report = new stdclass();
$lang->testtask->report->common = 'Bericht';
$lang->testtask->report->select = 'Berichttyp wählen';
$lang->testtask->report->create = 'Erzeugen';

$lang->testtask->report->testTaskPerRunResultTip = 'There are %s usecase, including %s passed,%s not executed, and %s failed';

$lang->testtask->report->charts['testTaskPerRunResult'] = 'Ergebnisbericht';
$lang->testtask->report->charts['testTaskPerType']      = 'Berichttyp';
$lang->testtask->report->charts['testTaskPerModule']    = 'Modulbericht';
$lang->testtask->report->charts['testTaskPerRunner']    = 'Ausgeführt von Bericht';

$lang->testtask->featureBar['browse']['totalStatus'] = $lang->testtask->totalStatus;
$lang->testtask->featureBar['browse']['myinvolved']  = $lang->testtask->myInvolved;
$lang->testtask->featureBar['browse']['wait']        = $lang->testtask->wait;
$lang->testtask->featureBar['browse']['doing']       = $lang->testtask->testing;
$lang->testtask->featureBar['browse']['blocked']     = $lang->testtask->blocked;
$lang->testtask->featureBar['browse']['done']        = $lang->testtask->done;

$lang->testtask->featureBar['cases']['all']          = $lang->testtask->allCases;
$lang->testtask->featureBar['cases']['assignedtome'] = $lang->testtask->assignedToMe;

$lang->testtask->featureBar['groupcase']['all']          = $lang->testtask->allCases;
$lang->testtask->featureBar['groupcase']['assignedtome'] = $lang->testtask->assignedToMe;

$lang->testtask->featureBar['linkcase']['all']     = $lang->all;
$lang->testtask->featureBar['linkcase']['bystory'] = $lang->testtask->linkByStory;
$lang->testtask->featureBar['linkcase']['bysuite'] = $lang->testtask->linkBySuite;
$lang->testtask->featureBar['linkcase']['bybuild'] = $lang->testtask->linkByBuild;
$lang->testtask->featureBar['linkcase']['bybug']   = $lang->testtask->linkByBug;

$lang->testtask->featureBar['browseunits']['all']       = 'All';
$lang->testtask->featureBar['browseunits']['newest']    = 'Recently';
$lang->testtask->featureBar['browseunits']['thisWeek']  = 'This week';
$lang->testtask->featureBar['browseunits']['lastWeek']  = 'Last week';
$lang->testtask->featureBar['browseunits']['thisMonth'] = 'This month';
$lang->testtask->featureBar['browseunits']['lastMonth'] = 'Last month';

$lang->testtask->typeList['integrate']   = 'Integrate';
$lang->testtask->typeList['system']      = 'System';
$lang->testtask->typeList['acceptance']  = 'Acceptance';
$lang->testtask->typeList['performance'] = 'Performance';
$lang->testtask->typeList['safety']      = 'Safety';
