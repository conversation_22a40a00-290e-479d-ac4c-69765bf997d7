body {padding-bottom: 0px;}
.w-code {width: 48%; word-break: break-all;}
td.code {color: #484848; padding: 0px 3px; white-space: pre-wrap;}
.none  {background: #EAF2F5;}
table.diff {margin-bottom: 0px;}
.diff caption {border: 1px solid #e4e4e4; background: #edf3fe; margin: 0; padding: 6px 2px 6px 10px; text-align: left; font-weight: bold; font-size: 13px;}
.diff th, .diff td {border: none;}
.diff th {padding-top: 2px; padding-bottom: 2px;}
.diff .line-new, .diff .line-new {background: #CFC;}
.diff .line-old, .diff .line-old {background: #FCC;}
.diff .line-all, .diff .line-all {background: #FFF;}
.diff .w-num {width: 25px; border-right: 1px solid #E4E4E4; background: #fafafa; border-left: 1px solid #E4E4E4; color: #999; font-weight: normal;}

.repoCode .diff tr .comment-btn .icon-wrapper {left: -30px;}
.repoCode .diff tr.over td.line-all, .repoCode .diff tr.over td.line-all {background: #f8eec7;}
.repoCode .diff tr.over td.line-new, .repoCode .diff tr.over td.line-new {background: #8eff8e;}
.repoCode .diff tr.over td.line-old, .repoCode .diff tr.over td.line-old {background: #f6b2b2;}

.repoCode form > .btn {margin-right: 10px;}
.label-exchange {background-color: #566F7C; cursor: pointer;}
.label-exchange i {padding: 0;}
.btn-download {border-right: none;}
.body-modal #back {display: none;}

.repoCode td.code {white-space: inherit;}
.repoCode .content, .repoCode .btn {background-color: #F4F5F7; border: none;}
.repoCode .nav-tabs > li.active > a:before {background: none; height: 0;}

#fileTabs .tab-pane {display: none;}
#fileTabs .tab-pane.active {display: block;}
#fileTabs .tab-nav-item {max-width: none !important;}
#fileTabs > .tabs-navbar {overflow: hidden; padding-bottom: 10px; position: relative; height: 35px;}
#fileTabs > .tabs-navbar > .nav-tabs {position: absolute; display: flex;}
#filesTree #modules {margin-top: 5px;}
#filesTree {overflow-y: auto;}

#fileTree li {padding: 0 0 0 8px;}
#fileTree li.has-list {padding-left: 20px;}
#fileTree li > a {display: block; padding: 6px 0; border-radius: 2px; padding-left: 6px; height: 30px; text-decoration: none;}
#fileTree li > a > span {display: inline-block;}
#fileTree li > a > span + span {margin-left: 8px;}
#fileTree li.selected > a {background-color: #E8F3FC;}
#fileTree li.selected > a > span.title {color: #006AF1;}
#fileTree .label-id {border-color: #cbd0db; color: #7d8599}
#fileTree .label.label-type {background: #fff; border: 1px solid #7d8599; color:#7d8599}

#fileTree li > a > span.title {color: #3C4353; white-space: nowrap; max-width: 60%; overflow: hidden; text-overflow: ellipsis; vertical-align: middle;}
#fileTree li > a > span.user {color: #838a9d;}
#fileTree li > a > span.user > .icon-person {font-size: 14px; position: relative; top: -1px; color: #a6aab8}
#fileTree li > a:first-child {padding-left: 18px;}
#fileTree li a.selected {color: #e9f2fb; background-color: #0c64eb;}

#fileTree li > .list-toggle {transform: rotate(0deg); width: 16px; height: 16px; border: 4px solid #a6aab8; border-radius: 2px; top: 7px;}
#fileTree li > .list-toggle:before {content: ' '; display: block; position: absolute; border: 1px solid #a6aab8; top: 2px; left: -3px; right: -3px; bottom: 2px; min-width: 0; transition: all .2s;}
#fileTree li > .list-toggle:hover:before, #fileTree li > .list-toggle:hover {border-color: #006AF1;}
#fileTree li.open > .list-toggle {width: 12px; height: 12px; top: 9px; background-color: #a6aab8; border-width: 3px; left: 3px;}
#fileTree li.open > .list-toggle:before {border: none; height: 2px; width: 6px; left: 0; top: 2px; background: #fff;}
#fileTree li.open > .list-toggle:hover {background: #006AF1;}

#fileTree ul > li:after {display: block; position: absolute; content: ' '; border-top: 1px dashed #cbd0db; top: 14px; left: -12px; z-index: 1; width: 10px;}
#fileTree ul > li:before, #fileTree ul > li.has-list:before {background: none; content: ' '; display: block; position: absolute; width: auto; height: auto; border: none; border-left: 1px dashed #cbd0db; top: -13px; bottom: 12px; left: -12px;}
#fileTree ul > li:last-child:before {bottom: auto; height: 29px;}
#fileTree ul > li:first-child:before {top: -9px;}
#fileTree ul > li.has-list:first-child:before {top: -13px;}
#fileTree ul > li.tree-single-item:before {height: 23px;}
#fileTree ul > li.has-list:after {width: 14px;}

#modules li {padding: 5px 0 0 15px;}
.file-tree #modules .icon {color: #9EA3B0;}
.file-tree #modules .icon-folder {padding-left: 5px;}
li.selected .doc-title .icon, li.selected .doc-title a {color: #438EFF !important;}

.btn-left, .btn-right {display: none;}
