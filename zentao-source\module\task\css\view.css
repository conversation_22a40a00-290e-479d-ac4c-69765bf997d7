.side-col .cell {padding: 0px;}
.side-col #legendProjectAndTask .list-unstyled {padding: 10px; border: 1px solid #ddd; border-top: 0px; margin: 0px;}
.tab-pane table {border: 1px solid #ddd; border-top: none;}
.MRThWidth {width: 100px !important;}
.btn-edit-comment {z-index: 100;}
.link-commit {overflow: hidden; white-space: nowrap; margin-right: 5px;}

#assignedTo a {border-color: transparent;}
#assignedTo a:hover {border-color: rgba(0,0,0,.2);}

.c-id {width: 50px;}
.c-pri {width: 40px;}
.c-deadline {width: 100px;}
.c-assignedTo {width: 120px;}
.c-status {width: 80px;}
.c-consumedAB {width: 60px;}
.c-leftAB {width: 60px;}
.c-actions {width: 175px;}

[lang^='de'] #mainContent .main-actions .btn-toolbar .btn {padding-left: 3px; padding-right: 3px;}

#childrenTable .c-id {width: 60px;}
#childrenTable .c-lblPri {width: 50px;}

#legendTeam .c-hours {width: 72px;}
