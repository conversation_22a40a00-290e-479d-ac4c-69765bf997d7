<?php
/**
 * The release module zh-cn file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     release
 * @version     $Id: zh-cn.php 4129 2020-11-27 01:58:14Z wwccss $
 * @link        https://www.zentao.net
 */
$lang->projectrelease->common           = $lang->projectCommon . '发布';
$lang->projectrelease->create           = "创建发布";
$lang->projectrelease->edit             = "编辑发布";
$lang->projectrelease->linkStory        = "关联{$lang->SRCommon}";
$lang->projectrelease->linkBug          = "关联Bug";
$lang->projectrelease->delete           = "删除发布";
$lang->projectrelease->deleted          = '已删除';
$lang->projectrelease->view             = "发布详情";
$lang->projectrelease->browse           = "浏览发布";
$lang->projectrelease->changeStatus     = "修改状态";
$lang->projectrelease->batchUnlink      = "批量移除";
$lang->projectrelease->batchUnlinkStory = "批量移除{$lang->SRCommon}";
$lang->projectrelease->batchUnlinkBug   = "批量移除Bug";
$lang->projectrelease->unlinkStory      = "移除{$lang->SRCommon}";
$lang->projectrelease->unlinkBug        = '移除Bug';
$lang->projectrelease->export           = '导出HTML';
$lang->projectrelease->browseAction     = "发布列表";
$lang->projectrelease->notify           = "通知";
$lang->projectrelease->publish          = "发布";
$lang->projectrelease->product          = "所属{$lang->productCommon}";
$lang->projectrelease->name             = $lang->product->system . '版本号';

$lang->projectrelease->featureBar['browse']['all']       = '全部';
$lang->projectrelease->featureBar['browse']['wait']      = '未开始';
$lang->projectrelease->featureBar['browse']['normal']    = '已发布';
$lang->projectrelease->featureBar['browse']['terminate'] = '停止维护';
