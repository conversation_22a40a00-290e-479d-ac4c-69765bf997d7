<!DOCTYPE html>
<html>
<head>
    <title>复制表格</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdn.staticfile.org/twitter-bootstrap/4.3.1/css/bootstrap.min.css">
    <script src="https://cdn.staticfile.org/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://cdn.staticfile.org/popper.js/1.15.0/umd/popper.min.js"></script>
    <script src="https://cdn.staticfile.org/twitter-bootstrap/4.3.1/js/bootstrap.min.js"></script>
    <style>
        body {
            padding: 20px;
        }
        .container {
            max-width: 800px;
        }
        .form-group {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="mb-4">复制表格</h2>
        
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form action="/copy_table" method="post">
            <div class="form-group">
                <label for="source_db">源数据库:</label>
                <select class="form-control" id="source_db" name="source_db" required>
                    <option value="">请选择源数据库</option>
                    {% for db in databases %}
                        <option value="{{ db }}">{{ db }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="form-group">
                <label for="source_table">源表:</label>
                <select class="form-control" id="source_table" name="source_table" required disabled>
                    <option value="">请先选择源数据库</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="target_db">目标数据库:</label>
                <select class="form-control" id="target_db" name="target_db" required>
                    <option value="">请选择目标数据库</option>
                    {% for db in databases %}
                        <option value="{{ db }}">{{ db }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="form-group">
                <label for="target_table">目标表名:</label>
                <input type="text" class="form-control" id="target_table" name="target_table" placeholder="留空则使用与源表相同的名称">
            </div>
            
            <div class="form-check mb-4">
                <input type="checkbox" class="form-check-input" id="copy_data" name="copy_data" checked>
                <label class="form-check-label" for="copy_data">同时复制数据</label>
            </div>
            
            <button type="submit" class="btn btn-primary">复制表格</button>
            <a href="/" class="btn btn-secondary ml-2">返回首页</a>
        </form>
    </div>
    
    <script>
        $(document).ready(function(){
            // 当源数据库选择变化时，加载相应的表
            $('#source_db').change(function(){
                var db = $(this).val();
                if(db) {
                    // 启用源表选择框
                    $('#source_table').prop('disabled', false);
                    
                    // 加载数据库中的表
                    $.getJSON('/get_tables/' + db, function(data) {
                        var options = '<option value="">请选择源表</option>';
                        if(data.tables) {
                            $.each(data.tables, function(index, table) {
                                options += '<option value="' + table + '">' + table + '</option>';
                            });
                        }
                        $('#source_table').html(options);
                    });
                } else {
                    $('#source_table').html('<option value="">请先选择源数据库</option>');
                    $('#source_table').prop('disabled', true);
                }
            });
            
            // 当源表选择变化时，自动填充目标表名
            $('#source_table').change(function(){
                var table = $(this).val();
                if(table && !$('#target_table').val()) {
                    $('#target_table').val(table);
                }
            });
        });
    </script>
</body>
</html> 