body {background: var(--color-primary-500) linear-gradient(-90deg, var(--color-primary-600) 0%, var(--color-primary-500) 100%); background-color: var(--color-primary-500);}
#main {min-height:0px;}
#main.loading::before {display: none!important;}
#login {max-width: 720px !important; margin: 0 auto; margin-top: 5%; padding: 0 40px;}
#loginPanel {background: #fff; overflow: hidden; box-shadow: 0 0 20px 0 rgba(0,0,0,.1); border-radius: 3px; position: relative;}
#main.loading #loginPanel::before {content: ' '; display: block; position: absolute; left: 0; top: 0; right: 0; bottom: 0; background-color: rgba(var(--color-canvas-rgb),.7); border-radius: inherit; z-index: 1;}
#loginPanel .header {position: relative;}
#loginPanel .header > h2 {font-size: 16px; margin: 0; line-height: 32px; max-width: calc(100% - 90px); display: inline-block;}

#logo-box {background-size: cover; background-position: center; position: relative;}
#login-logo, #login-ai {position: absolute; left: 50%; transform: translateX(-50%);}

#loginBox {padding: 24px 40px 0 40px;}

#loginPanel > .footer {background: #eee; padding: 20px; color: #838a9d;}
#loginPanel > .footer a {display: inline-block; margin-left: 5px; color: var(--color-gray-900);}
#loginPanel > .footer a:hover {color: var(--color-primary-500);}
#loginPanel > .loginBody {align-items: stretch;}
#loginPanel > .loginBody .loginExpired {margin-bottom: 8px;}
#login #info {margin-top: 10px; color: #fff; text-align:center;}
#login #info a {display: inline-block; margin-left: 5px;}
.actions.btn {min-width: 0; width: 85px;}
.langsDropMenu.dropdown-menu {min-width: 85px;}
#langs-toggle {float: right; position: absolute; right: 0;}

#loginForm {margin: 10px 0 30px 0;}
#loginOptions {position: relative;}
#keepLogin {display: inline-block; vertical-align: middle;}
.resetPassword {position: absolute; top: 50%; right: var(--form-grid-gap-x-half); transform: translateY(-50%); color: var(--color-fore);}

.form-actions {padding-bottom: 0 !important;}
.form-actions > button {width: 100%;}

.form-actions .btn {min-width: unset;}
.form-actions .resetPassword {box-shadow: unset; background-color: unset; color: unset;}
.form-actions .resetPassword:before {background-color: unset;}

.captchaBox .input-group-addon {width: 110px; padding: 0px;}
input[name="captcha"] {border-bottom-right-radius: var(--radius-none) !important; border-top-right-radius: var(--radius-none) !important;}

.showNotice {color: yellow;}
.showNotice:hover {color: yellow;}

.table-row-extension .alert {margin-bottom: 0px !important;}
.table-row-extension .content {color: #3c4353;}
.table-row-extension .expired-tips {cursor: pointer;}
