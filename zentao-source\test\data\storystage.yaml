title: table zt_storystage
desc: "需求阶段"
author: automated export
version: "1.0"
fields:
  - field: story
    note: "需求ID"
    range: 1-450
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: branch
    note: "分支"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: stage
    note: "所处阶段"
    range: wait,planned,projected,developing,developed,testing,tested,verified,released,closed
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: stagedBy
    note: "设置阶段者"
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
