{% extends "base.html" %}

{% block content %}
<h2>管理字段 - 表: {{ table_name }}</h2>
<a href="/database/{{ db_name }}" class="btn btn-secondary">返回</a>

<!-- 显示主键字段 -->
{% if primary_key %}
    <div class="alert alert-info mt-3">
        当前主键字段: <strong>{{ primary_key }}</strong>
    </div>
{% else %}
    <div class="alert alert-warning mt-3">
        警告: 此表没有主键字段。
    </div>
{% endif %}

<!-- 设置主键表单 -->
<form action="/set_primary_key/{{ db_name }}/{{ table_name }}" method="post" class="mb-3">
    <div class="form-group">
        <label for="primary_key_field">选择主键字段</label>
        <select class="form-control" id="primary_key_field" name="primary_key_field" required>
            {% for field in fields %}
                {% if 'INT' in field[1].upper() %}
                    <option value="{{ field[0] }}">{{ field[0] }} ({{ field[1] }})</option>
                {% endif %}
            {% endfor %}
        </select>
        <small class="form-text text-muted">请选择数值类型（如 INT）的字段作为主键。</small>
    </div>
    <button type="submit" class="btn btn-primary">设置为主键并添加自增属性</button>
</form>

<!-- 修改字段属性表单 -->
<form action="/modify_field/{{ db_name }}/{{ table_name }}" method="post" class="mb-3">
    <div class="form-group">
        <label for="field_name">选择字段</label>
        <select class="form-control" id="field_name" name="field_name" required>
            {% for field in fields %}
                <option value="{{ field[0] }}">{{ field[0] }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="form-group">
        <label for="field_type">字段类型</label>
        <select class="form-control" id="field_type" name="field_type" required>
            <option value="INT">INT</option>
            <option value="VARCHAR(255)">VARCHAR(255)</option>
            <option value="TEXT">TEXT</option>
            <option value="DATE">DATE</option>
            <option value="DATETIME">DATETIME</option>
        </select>
    </div>
    <div class="form-group">
        <label for="is_nullable">是否允许为空</label>
        <select class="form-control" id="is_nullable" name="is_nullable" required>
            <option value="YES">是</option>
            <option value="NO">否</option>
        </select>
    </div>
    <div class="form-group">
        <label for="default_value">默认值</label>
        <input type="text" class="form-control" id="default_value" name="default_value">
    </div>
    <button type="submit" class="btn btn-primary">修改字段属性</button>
</form>

<!-- 显示字段列表 -->
<h3>字段列表</h3>
<table class="table table-bordered table-hover">
    <thead>
        <tr>
            <th>字段名</th>
            <th>类型</th>
            <th>是否允许为空</th>
            <th>默认值</th>
        </tr>
    </thead>
    <tbody>
        {% for field in fields %}
            <tr>
                <td>{{ field[0] }}</td>
                <td>{{ field[1] }}</td>
                <td>{{ field[2] }}</td>
                <td>{{ field[4] }}</td>
            </tr>
        {% endfor %}
    </tbody>
</table>

<!-- 添加新字段 -->
<h3>添加新字段</h3>
<form action="/add_field/{{ db_name }}/{{ table_name }}" method="post">
    <div class="form-group">
        <label for="field_name">字段名</label>
        <input type="text" class="form-control" id="field_name" name="field_name" required>
    </div>
    <div class="form-group">
        <label for="field_type">字段类型</label>
        <input type="text" class="form-control" id="field_type" name="field_type" placeholder="例如: INT" required>
    </div>
    <button type="submit" class="btn btn-success">添加字段</button>
</form>
{% endblock %} 