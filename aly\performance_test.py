#!/usr/bin/env python3
"""
性能测试脚本
用于测试数据库连接和查询性能
"""

import time
import mysql.connector
from mysql.connector import pooling
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com'),
    'port': int(os.getenv('DB_PORT', 3306)),
    'user': os.getenv('DB_USER', 'cyh'),
    'password': os.getenv('DB_PASSWORD', 'Qq188788'),
    'connect_timeout': int(os.getenv('DB_TIMEOUT', 10)),
    'autocommit': True,
    'charset': 'utf8mb4',
    'use_unicode': True,
}

def test_direct_connection():
    """测试直接连接性能"""
    print("测试直接数据库连接...")
    times = []
    
    for i in range(5):
        start_time = time.time()
        try:
            conn = mysql.connector.connect(**DB_CONFIG)
            cursor = conn.cursor()
            cursor.execute("SHOW DATABASES")
            databases = cursor.fetchall()
            cursor.close()
            conn.close()
            end_time = time.time()
            times.append(end_time - start_time)
            print(f"  连接 {i+1}: {end_time - start_time:.3f}秒")
        except Exception as e:
            print(f"  连接 {i+1} 失败: {str(e)}")
    
    if times:
        avg_time = sum(times) / len(times)
        print(f"  平均连接时间: {avg_time:.3f}秒")
    return times

def test_connection_pool():
    """测试连接池性能"""
    print("\n测试连接池...")
    
    # 创建连接池
    try:
        pool = pooling.MySQLConnectionPool(
            pool_name="test_pool",
            pool_size=5,
            pool_reset_session=True,
            **DB_CONFIG
        )
        print("  连接池创建成功")
    except Exception as e:
        print(f"  连接池创建失败: {str(e)}")
        return []
    
    times = []
    for i in range(5):
        start_time = time.time()
        try:
            conn = pool.get_connection()
            cursor = conn.cursor()
            cursor.execute("SHOW DATABASES")
            databases = cursor.fetchall()
            cursor.close()
            conn.close()
            end_time = time.time()
            times.append(end_time - start_time)
            print(f"  连接池查询 {i+1}: {end_time - start_time:.3f}秒")
        except Exception as e:
            print(f"  连接池查询 {i+1} 失败: {str(e)}")
    
    if times:
        avg_time = sum(times) / len(times)
        print(f"  平均查询时间: {avg_time:.3f}秒")
    return times

def test_database_queries():
    """测试数据库查询性能"""
    print("\n测试数据库查询...")
    
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 测试SHOW DATABASES
        start_time = time.time()
        cursor.execute("SHOW DATABASES")
        databases = cursor.fetchall()
        end_time = time.time()
        print(f"  SHOW DATABASES: {end_time - start_time:.3f}秒 ({len(databases)} 个数据库)")
        
        # 如果有kanban数据库，测试表查询
        db_names = [db[0] for db in databases]
        if 'kanban' in db_names:
            conn.database = 'kanban'
            
            start_time = time.time()
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            end_time = time.time()
            print(f"  SHOW TABLES (kanban): {end_time - start_time:.3f}秒 ({len(tables)} 个表)")
            
            # 测试表数据查询（如果有表的话）
            if tables:
                table_name = tables[0][0]
                start_time = time.time()
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                count = cursor.fetchone()[0]
                end_time = time.time()
                print(f"  COUNT查询 ({table_name}): {end_time - start_time:.3f}秒 ({count} 条记录)")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"  查询测试失败: {str(e)}")

def main():
    print("=== 数据库性能测试 ===")
    print(f"连接目标: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print()
    
    # 测试直接连接
    direct_times = test_direct_connection()
    
    # 测试连接池
    pool_times = test_connection_pool()
    
    # 测试查询性能
    test_database_queries()
    
    # 性能对比
    if direct_times and pool_times:
        print("\n=== 性能对比 ===")
        direct_avg = sum(direct_times) / len(direct_times)
        pool_avg = sum(pool_times) / len(pool_times)
        improvement = ((direct_avg - pool_avg) / direct_avg) * 100
        print(f"直接连接平均时间: {direct_avg:.3f}秒")
        print(f"连接池平均时间: {pool_avg:.3f}秒")
        print(f"性能提升: {improvement:.1f}%")

if __name__ == "__main__":
    main()
