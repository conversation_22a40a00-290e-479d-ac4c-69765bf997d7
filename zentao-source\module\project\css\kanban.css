.main-table .table {cursor: default;}
.main-table .table td {background: #efefef;}

.board-item {border: 1px solid #EBEBEB; padding: 5px 10px; cursor: default; border-radius: 2px; background-color: #fff;}
.board-item:hover {border-color: #ccc;}
.board-project .board-item + .board-item {margin-top: 10px;}

.board-project {padding: 0 10px 10px 10px;}
.board-project .board-item:first-child {margin-top: 10px;}

.board-doing, .board-wait, .board-closed {padding: 0px !important; vertical-align: top !important;}
.board-doing .board-doing-project,.board-doing .board-doing-execution {padding: 10px;}
.board-doing .board-doing-project:not(:last-child),
.board-doing .board-doing-execution:not(:last-child) {border-bottom: 2px solid #fff;}

.board-doing-project .table-row .table-col:last-child,
.board-doing-execution .table-row .table-col:last-child, .c-progress {width: 30px; padding-top: 2px; padding-left: 3px;}
.table-row .table-col:first-child, .board-project .board-item {overflow: hidden; white-space: nowrap;}

table th, td {border: 2px solid #fff !important;}

.kanban-col[data-type='closedProject'] .count {display: none}
.kanban-item > a {cursor: move;}
.kanban-item.execution-item > a {cursor: pointer;}

.kanban.kanban-dragging {overflow: hidden!important;}
