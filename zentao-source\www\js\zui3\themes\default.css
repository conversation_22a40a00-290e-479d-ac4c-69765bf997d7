
:root
{
  --zt-menu-bg: var(--color-gray-900)!important;
  --zt-menu-hover-bg: var(--color-primary-600);
  --zt-menu-active-bg: var(--color-primary-500);
  --zt-header-bg: var(--color-primary-500);
  --zt-header-color: var(--color-canvas);
  --zt-navbar-active-color: var(--color-canvas);
}
#messageBar,
#quickAddMenu-toggle {
  --tw-ring-color: rgba(var(--color-canvas-rgb),var(--tw-ring-opacity));
  color: var(--color-canvas);
}
#navbar {
  --state-color: rgba(var(--color-inverse-rgb), .04);
  --state-active-color: rgba(var(--color-inverse-rgb), .08);
}
