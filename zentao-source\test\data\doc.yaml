title: table zt_doc
desc: "文档"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "文档编号"
    range: 1-10000
    prefix: ""
    postfix: ""
    format: ""
  - field: product
    note: "所属产品"
    range: 1-100,0{90},0{630},0{80}
    prefix: ""
    postfix: ""
    format: ""
  - field: project
    note: "所属项目"
    range: 0{100},11-100,0{630},0{80}
    prefix: ""
    postfix: ""
    format: ""
  - field: execution
    note: "所属迭代"
    range: 0{100},0{90},101-730,0{80}
    prefix: ""
    postfix: ""
    format: ""
  - field: lib
    note: "所属文档库"
    range: 1-900
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: template
    note: "模板"
    range: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: templateType
    note: "模板类型"
    range: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: chapterType
    note: "章节类型"
    range: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: module
    note: "模块"
    range: 3621-3630,0{890}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: title
    note: "文档标题"
    range: 1-10000
    prefix: "文档标题"
    postfix: ""
    loop: 0
    format: ""
  - field: keywords
    note: "关键词"
    range: 1-10000
    prefix: "关键词"
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "文档类型"
    range: text,markdown,url
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: parent
    note: "父级ID"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: path
    note: ""
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: grade
    note: ""
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: order
    note: "排序"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: views
    note: ""
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: addedBy
    note: "添加者"
    range: admin
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: addedDate
    note: "添加日期"
    range: Y-m-d
    prefix: ""
    type: timestamp
    postfix: ""
    format: "YYYY-MM-DD"
  - field: editedBy
    note: "编辑者"
    range: admin
    prefix: ""
    postfix: ""
    format: ""
  - field: editedDate
    note: "编辑日期"
    range: Y-m-d
    prefix: ""
    type: timestamp
    postfix: ""
    format: "YYYY-MM-DD"
  - field: acl
    note: "权限"
    range: open,custom,private
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: groups
    note: "分组"
    range: 0,1,1
    prefix: ""
    postfix: ""
    format: ""
  - field: users
    note: "用户"
    range: 0,admin,admin
    prefix: ""
    postfix: ""
    format: ""
  - field: draft
    note: "草稿内容"
    range: ''
  - field: version
    note: "版本号"
    range: 2{10},1{890}
    prefix: ""
    postfix: ""
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    format: ""
