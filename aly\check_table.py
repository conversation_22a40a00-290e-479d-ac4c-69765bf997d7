import mysql.connector
import json

# 工单系统数据库配置
ticket_config = {
    'host': '**********',
    'port': 3306,
    'user': 'qyuser',
    'password': 'C~w9d4kaWS',
    'charset': 'utf8mb4',
    'use_unicode': True
}

def analyze_ticket_database():
    """分析工单系统数据库结构"""
    try:
        # 建立连接
        conn = mysql.connector.connect(**ticket_config)
        cursor = conn.cursor()

        print("=== 工单系统数据库分析 ===\n")

        # 1. 获取所有数据库
        cursor.execute("SHOW DATABASES")
        databases = [db[0] for db in cursor.fetchall()]
        print("可用数据库:")
        for db in databases:
            if db not in ['information_schema', 'performance_schema', 'mysql', 'sys']:
                print(f"  - {db}")
        print()

        # 2. 分析每个业务数据库
        business_databases = [db for db in databases if db not in ['information_schema', 'performance_schema', 'mysql', 'sys']]

        database_analysis = {}

        for db_name in business_databases:
            print(f"=== 分析数据库: {db_name} ===")
            cursor.execute(f"USE `{db_name}`")

            # 获取所有表
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]

            database_analysis[db_name] = {
                'tables': {},
                'table_count': len(tables)
            }

            print(f"表数量: {len(tables)}")
            print("表列表:")
            for table in tables:
                print(f"  - {table}")
            print()

            # 分析每个表的结构
            for table_name in tables:
                print(f"--- 表: {table_name} ---")

                # 获取表结构
                cursor.execute(f"DESCRIBE `{table_name}`")
                columns = cursor.fetchall()

                # 获取表注释
                cursor.execute(f"""
                    SELECT TABLE_COMMENT
                    FROM information_schema.TABLES
                    WHERE TABLE_SCHEMA = '{db_name}' AND TABLE_NAME = '{table_name}'
                """)
                table_comment_result = cursor.fetchone()
                table_comment = table_comment_result[0] if table_comment_result else ""

                # 获取记录数
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                record_count = cursor.fetchone()[0]

                table_info = {
                    'comment': table_comment,
                    'record_count': record_count,
                    'columns': []
                }

                print(f"表注释: {table_comment}")
                print(f"记录数: {record_count}")
                print("字段结构:")

                for col in columns:
                    field_name = col[0]
                    field_type = col[1]
                    is_null = col[2]
                    key_type = col[3]
                    default_value = col[4]
                    extra = col[5]

                    # 获取字段注释
                    cursor.execute(f"""
                        SELECT COLUMN_COMMENT
                        FROM information_schema.COLUMNS
                        WHERE TABLE_SCHEMA = '{db_name}'
                        AND TABLE_NAME = '{table_name}'
                        AND COLUMN_NAME = '{field_name}'
                    """)
                    comment_result = cursor.fetchone()
                    field_comment = comment_result[0] if comment_result else ""

                    column_info = {
                        'name': field_name,
                        'type': field_type,
                        'null': is_null,
                        'key': key_type,
                        'default': default_value,
                        'extra': extra,
                        'comment': field_comment
                    }

                    table_info['columns'].append(column_info)

                    print(f"  {field_name}: {field_type} {'NULL' if is_null == 'YES' else 'NOT NULL'} {key_type} {extra} - {field_comment}")

                database_analysis[db_name]['tables'][table_name] = table_info
                print()

        # 保存分析结果到文件
        with open('ticket_database_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(database_analysis, f, ensure_ascii=False, indent=2)

        print("=== 分析完成 ===")
        print("详细分析结果已保存到: ticket_database_analysis.json")

        return database_analysis

    except Exception as e:
        print(f"错误: {str(e)}")
        return None
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    analyze_ticket_database()