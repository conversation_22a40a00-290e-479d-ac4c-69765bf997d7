import{r as t,h as i}from"./p-7900c24a.js";import{P as e,a as s,E as n,t as o,p as h}from"./p-fda4ec51.js";class l{constructor({editor:t,element:i,view:e,tippyOptions:s={},shouldShow:n}){this.preventHide=!1,this.shouldShow=({view:t,state:i})=>{const{selection:e}=i,{$anchor:s,empty:n}=e,o=1===s.depth,h=s.parent.isTextblock&&!s.parent.type.spec.code&&!s.parent.textContent;return!!(t.hasFocus()&&n&&o&&h&&this.editor.isEditable)},this.mousedownHandler=()=>{this.preventHide=!0},this.focusHandler=()=>{setTimeout((()=>this.update(this.editor.view)))},this.blurHandler=({event:t})=>{var i;this.preventHide?this.preventHide=!1:(null==t?void 0:t.relatedTarget)&&(null===(i=this.element.parentNode)||void 0===i?void 0:i.contains(t.relatedTarget))||this.hide()},this.tippyBlurHandler=t=>{this.blurHandler({event:t})},this.editor=t,this.element=i,this.view=e,n&&(this.shouldShow=n),this.element.addEventListener("mousedown",this.mousedownHandler,{capture:!0}),this.editor.on("focus",this.focusHandler),this.editor.on("blur",this.blurHandler),this.tippyOptions=s,this.element.remove(),this.element.style.visibility="visible"}createTooltip(){const{element:t}=this.editor.options;!this.tippy&&t.parentElement&&(this.tippy=o(t,{duration:0,getReferenceClientRect:null,content:this.element,interactive:!0,trigger:"manual",placement:"right",hideOnClick:"toggle",...this.tippyOptions}),this.tippy.popper.firstChild&&this.tippy.popper.firstChild.addEventListener("blur",this.tippyBlurHandler))}update(t,i){var e,s,n;const{state:o}=t,{doc:l,selection:r}=o,{from:d,to:c}=r;i&&i.doc.eq(l)&&i.selection.eq(r)||(this.createTooltip(),(null===(e=this.shouldShow)||void 0===e?void 0:e.call(this,{editor:this.editor,view:t,state:o,oldState:i}))?(null===(s=this.tippy)||void 0===s||s.setProps({getReferenceClientRect:(null===(n=this.tippyOptions)||void 0===n?void 0:n.getReferenceClientRect)||(()=>h(t,d,c))}),this.show()):this.hide())}show(){var t;null===(t=this.tippy)||void 0===t||t.show()}hide(){var t;null===(t=this.tippy)||void 0===t||t.hide()}destroy(){var t,i;(null===(t=this.tippy)||void 0===t?void 0:t.popper.firstChild)&&this.tippy.popper.firstChild.removeEventListener("blur",this.tippyBlurHandler),null===(i=this.tippy)||void 0===i||i.destroy(),this.element.removeEventListener("mousedown",this.mousedownHandler,{capture:!0}),this.editor.off("focus",this.focusHandler),this.editor.off("blur",this.blurHandler)}}const r=t=>new e({key:"string"==typeof t.pluginKey?new s(t.pluginKey):t.pluginKey,view:i=>new l({view:i,...t})});n.create({name:"floatingMenu",addOptions:()=>({element:null,tippyOptions:{},pluginKey:"floatingMenu",shouldShow:null}),addProseMirrorPlugins(){return this.options.element?[r({pluginKey:this.options.pluginKey,editor:this.editor,element:this.options.element,tippyOptions:this.options.tippyOptions,shouldShow:this.options.shouldShow})]:[]}});const d=class{constructor(i){t(this,i),this.menuProps=void 0,this.editor=void 0,this.element=void 0}componentDidLoad(){if(!Boolean(this.element)||this.editor.isDestroyed)return;const{pluginKey:t="floatingMenu",tippyOptions:i={},shouldShow:e=null}=this.menuProps,s=r({pluginKey:t,editor:this.editor,element:this.element,tippyOptions:Object.assign({placement:"left",getReferenceClientRect:()=>{const t=this.editor.view.state.selection.$anchor,i=t.pos-t.parentOffset;return h(this.editor.view,i,i)}},i),shouldShow:null!=e?e:({view:t,state:i})=>{const{selection:e}=i,{$anchor:s,empty:n}=e,o=1===s.depth;return!(!t.hasFocus()||!n||!o)}});this.editor.registerPlugin(s)}render(){return i("div",{key:"5f8300d192f4bca4595802d1e07096a62f415247",ref:t=>this.element=t,class:"floating-menu",style:{visibility:"hidden"}},[{icon:"ci-plus",title:"Add",action:()=>{}},{icon:"ci-more_vertical",title:"Click to tune",action:()=>{}}].map(((t,e)=>{const s=t,{isHidden:n,type:o}=s,h=function(t,i){var e={};for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&i.indexOf(s)<0&&(e[s]=t[s]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(s=Object.getOwnPropertySymbols(t);n<s.length;n++)i.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(t,s[n])&&(e[s[n]]=t[s[n]])}return e}(s,["isHidden","type"]);if(!n||!n())return"divider"===o?i("div",{class:"divider",key:e}):i("zen-editor-menu-item",{itemProps:Object.assign({},h),key:e})})))}};export{d as zen_editor_floating_menu}