-- 修复督办管理表结构

USE kanban2;

-- 检查并修复supervision_items表结构
-- 添加缺失的字段

-- 1. 添加is_annual_assessment字段
ALTER TABLE supervision_items 
ADD COLUMN IF NOT EXISTS is_annual_assessment ENUM('是', '否') DEFAULT '否' COMMENT '是否年度绩效考核指标'
AFTER work_content;

-- 2. 添加progress_description字段
ALTER TABLE supervision_items 
ADD COLUMN IF NOT EXISTS progress_description TEXT COMMENT '7月28日进度情况'
AFTER completion_deadline;

-- 3. 修改overall_progress字段为ENUM类型
ALTER TABLE supervision_items 
MODIFY COLUMN overall_progress ENUM('X 未启动', 'O进行中', '！逾期', '√ 已完成', '— 不需要执行') 
DEFAULT 'X 未启动' COMMENT '整体进度';

-- 4. 确保company_supervision_status表的status字段也是ENUM类型
ALTER TABLE company_supervision_status 
MODIFY COLUMN status ENUM('√', 'O', '！', 'X', '—') DEFAULT 'X' COMMENT '状态：√已完成 O进行中 ！逾期 X未启动 —不需要执行';

-- 5. 更新现有数据的is_annual_assessment字段
UPDATE supervision_items SET is_annual_assessment = '是' WHERE sequence_number IN (5, 6, 7, 8);
UPDATE supervision_items SET is_annual_assessment = '否' WHERE is_annual_assessment IS NULL OR is_annual_assessment = '';

-- 6. 更新现有数据的overall_progress字段
UPDATE supervision_items SET overall_progress = '√ 已完成' WHERE overall_progress = '已完成' OR overall_progress = '√已完成';
UPDATE supervision_items SET overall_progress = 'O进行中' WHERE overall_progress = '进行中' OR overall_progress = 'O进行中';
UPDATE supervision_items SET overall_progress = '！逾期' WHERE overall_progress = '逾期' OR overall_progress = '！逾期';
UPDATE supervision_items SET overall_progress = 'X 未启动' WHERE overall_progress = '未启动' OR overall_progress = 'X未启动' OR overall_progress IS NULL OR overall_progress = '';
UPDATE supervision_items SET overall_progress = '— 不需要执行' WHERE overall_progress = '不需要执行' OR overall_progress = '—不需要执行';

-- 显示修复后的表结构
DESCRIBE supervision_items;

-- 显示数据样本
SELECT id, sequence_number, work_theme, is_annual_assessment, overall_progress FROM supervision_items LIMIT 5;
