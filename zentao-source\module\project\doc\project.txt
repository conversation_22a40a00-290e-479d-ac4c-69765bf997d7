createGuide ## 1 zk
export
ajaxGetDropMenu
ajaxRetrieveCloneableProject
ajaxGetRemoveMemberTips
ajaxGetLinkedProducts
ajaxGetProjectFormInfo
index
browse
kanban
programTitle
create
edit
batchEdit
view
group
createGroup
dynamic
execution
bug
testcase
testreport
testtask
build
managePriv
team
unlinkMember
manageMembers
manageGroupMember
copyGroup
editGroup ## 2 sgm
start
suspend
close
activate
delete
updateOrder
whitelist
addWhitelist
unbindWhitelist
manageProducts
ajaxGetExecutions
ajaxGetPairsByExecution
checkPriv
accessDenied
buildSearchFrom
processProjectPrivs
createManageLink
buildMenuQuery
getProgramTree
getTreeMenu
buildProjectBuildSearchForm
changeExecutionStatus
updateShadowProduct
updateProductProgram
checkCanChangeModel
updateRepoRelations
linkedRepoPairs
getMultiLinkedProducts
getBudgetUnitList
getSwitcher ## 3 ly
getByID
getByShadowProduct
getInfoList
getParentProgram
getOverviewList
getTotalStoriesByProject
getTotalBugByProject
getTotalTaskByProject
getWaterfallProgress
getWaterfallPVEVAC
getWorkhour
getProjectsConsumed
getProjectLink
getStatData
getPairs
getPairsByProgram
getBrotherProjects
getByIdList
getPairsByIdList
getBranchesByProject
getBranchGroupByProject
getNoProductList
getProjectExecutionPairs
getPairsByModel
getStoriesByProject
getTeamMembers
getTeamMemberPairs
getTeamMemberGroup
getMembers2Import
getStats4Kanban ## 4 dk
getBudgetWithUnit
getDataByProject
create
update
batchUpdate
start
putoff
suspend
activate
close
isClickable
setMenu
saveState
unlinkMember
manageMembers
updateProducts
updateInvolvedUserView
computeProgress
setNoMultipleMenu
buildOperateMenuZin
buildOperateMenu
buildOperateViewMenu
buildOperateBrowseMenuZin
buildOperateBrowseMenu
printCell
printCellZin
