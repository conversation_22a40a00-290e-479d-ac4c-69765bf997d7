<?php
$lang->jenkins->common        = '<PERSON>';
$lang->jenkins->browse        = '<PERSON>ffiche<PERSON> Jenkins';
$lang->jenkins->create        = 'Cr閑r Jenkins';
$lang->jenkins->edit          = 'Editer Jenkins';
$lang->jenkins->delete        = 'Supprimer';
$lang->jenkins->confirmDelete = 'Voulez-vous supprimer ce serveur Jenkins ?';

$lang->jenkins->browseAction = 'Jenkins List';
$lang->jenkins->deleteAction = 'Delete Jenkins';

$lang->jenkins->id       = 'ID';
$lang->jenkins->name     = 'Nom';
$lang->jenkins->url      = 'Service URL';
$lang->jenkins->token    = 'Token';
$lang->jenkins->account  = 'UserName';
$lang->jenkins->password = 'Password';

$lang->jenkins->lblCreate  = 'Cr閑r Serveur Jenkins';
$lang->jenkins->desc       = 'Description';
$lang->jenkins->tokenFirst = 'Utiliser un Token si non vide.';
$lang->jenkins->tips       = 'Cancel "Prevent Cross Site Request Forgery exploits" when using password.';
$lang->jenkins->serverList = 'Server List';

$lang->jenkins->error = new stdclass();
$lang->jenkins->error->linkedJob    = 'Failed. This jenkins has associated with the compile.';
$lang->jenkins->error->unauthorized = 'Permission verification failed, please check account information';
