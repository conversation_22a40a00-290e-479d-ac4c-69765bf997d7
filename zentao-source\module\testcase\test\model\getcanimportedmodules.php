#!/usr/bin/env php
<?php

/**

title=测试 testsuiteModel->getCanImportedModules();
cid=0

*/
include dirname(__FILE__, 5) . '/test/lib/init.php';
include dirname(__FILE__, 2) . '/lib/testcase.unittest.class.php';

zenData('product')->gen(45);
zenData('branch')->gen(20);
zenData('case')->loadYaml('case')->gen(50);
zenData('module')->loadYaml('module_import')->gen(50);
zenData('userquery')->loadYaml('userquery')->gen(1);
zenData('user')->gen(1);

su('admin');

$productIdList  = array(1, 41);
$libIdList      = array(0, 1);
$branchList     = array('all', 1);
$returnTypeList = array('pairs', 'items');

$testcase = new testcaseTest();

r($testcase->getCanImportedModulesTest($productIdList[0], $libIdList[0], $branchList[0], $returnTypeList[0])) && p() && e('30:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;29:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;28:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;27:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;26:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;25:1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;24:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;23:1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;22:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;21:1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;');   // 测试获取产品 1 用例库 0 分支 all 返回类型 pairs 可以导入的用例
r($testcase->getCanImportedModulesTest($productIdList[0], $libIdList[0], $branchList[0], $returnTypeList[1])) && p() && e('30:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;29:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;28:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;27:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;26:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;25:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20;24:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;23:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20;22:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;21:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20;');                     // 测试获取产品 1 用例库 0 分支 all 返回类型 items 可以导入的用例
r($testcase->getCanImportedModulesTest($productIdList[0], $libIdList[1], $branchList[0], $returnTypeList[0])) && p() && e('30:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;28:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;26:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;24:1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;22:1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;'); // 测试获取产品 1 用例库 1 分支 all 返回类型 pairs 可以导入的用例
r($testcase->getCanImportedModulesTest($productIdList[0], $libIdList[1], $branchList[0], $returnTypeList[1])) && p() && e('30:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;28:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;26:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;24:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20;22:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20;');                  // 测试获取产品 1 用例库 1 分支 all 返回类型 items 可以导入的用例

r($testcase->getCanImportedModulesTest($productIdList[0], $libIdList[0], $branchList[1], $returnTypeList[0])) && p() && e('30:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;29:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;28:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;27:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;26:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;25:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;24:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;23:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;22:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;21:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;'); // 测试获取产品 1 用例库 0 分支 1 返回类型 pairs 可以导入的用例
r($testcase->getCanImportedModulesTest($productIdList[0], $libIdList[0], $branchList[1], $returnTypeList[1])) && p() && e('30:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;29:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;28:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;27:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;26:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;25:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;24:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;23:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;22:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;21:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;');                // 测试获取产品 1 用例库 0 分支 1 返回类型 items 可以导入的用例
r($testcase->getCanImportedModulesTest($productIdList[0], $libIdList[1], $branchList[1], $returnTypeList[0])) && p() && e('30:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;28:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;26:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;24:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;22:0,1,31,2,32,3,33,4,34,5,35,6,36,7,37,8,38,9,39,10,40,ditto;'); // 测试获取产品 1 用例库 1 分支 1 返回类型 pairs 可以导入的用例
r($testcase->getCanImportedModulesTest($productIdList[0], $libIdList[1], $branchList[1], $returnTypeList[1])) && p() && e('30:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;28:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;26:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;24:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;22:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;');                // 测试获取产品 1 用例库 1 分支 1 返回类型 items 可以导入的用例

r($testcase->getCanImportedModulesTest($productIdList[1], $libIdList[0], $branchList[0], $returnTypeList[0])) && p() && e('30:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;29:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;28:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;27:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;26:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;25:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;24:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;23:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;22:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;21:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;'); // 测试获取产品 41 用例库 0 分支 all 返回类型 pairs 可以导入的用例
r($testcase->getCanImportedModulesTest($productIdList[1], $libIdList[0], $branchList[0], $returnTypeList[1])) && p() && e('30:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;29:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;28:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;27:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;26:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;25:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;24:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;23:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;22:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;21:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;');                                                             // 测试获取产品 41 用例库 0 分支 all 返回类型 items 可以导入的用例
r($testcase->getCanImportedModulesTest($productIdList[1], $libIdList[1], $branchList[0], $returnTypeList[0])) && p() && e('30:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;28:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;26:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;24:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;22:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;'); // 测试获取产品 41 用例库 1 分支 all 返回类型 pairs 可以导入的用例
r($testcase->getCanImportedModulesTest($productIdList[1], $libIdList[1], $branchList[0], $returnTypeList[1])) && p() && e('30:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;28:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;26:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;24:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;22:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;');                                                             // 测试获取产品 41 用例库 1 分支 all 返回类型 items 可以导入的用例

r($testcase->getCanImportedModulesTest($productIdList[1], $libIdList[0], $branchList[1], $returnTypeList[0])) && p() && e('30:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;29:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;28:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;27:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;26:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;25:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;24:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;23:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;22:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;21:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;'); // 测试获取产品 41 用例库 0 分支 1 返回类型 pairs 可以导入的用例
r($testcase->getCanImportedModulesTest($productIdList[1], $libIdList[0], $branchList[1], $returnTypeList[1])) && p() && e('30:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;29:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;28:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;27:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;26:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;25:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;24:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;23:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;22:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;21:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;');                                                             // 测试获取产品 41 用例库 0 分支 1 返回类型 items 可以导入的用例
r($testcase->getCanImportedModulesTest($productIdList[1], $libIdList[1], $branchList[1], $returnTypeList[0])) && p() && e('30:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;28:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;26:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;24:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;22:0,11,41,12,42,13,43,14,44,15,45,16,46,17,47,18,48,19,49,20,50,ditto;'); // 测试获取产品 41 用例库 1 分支 1 返回类型 pairs 可以导入的用例
r($testcase->getCanImportedModulesTest($productIdList[1], $libIdList[1], $branchList[1], $returnTypeList[1])) && p() && e('30:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;28:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;26:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;24:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;22:0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21;');                                                             // 测试获取产品 41 用例库 1 分支 1 返回类型 items 可以导入的用例
