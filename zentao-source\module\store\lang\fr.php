<?php
$lang->store->appType          = 'Type';
$lang->store->appVersion       = 'Version';
$lang->store->releaseDate      = 'Publish Time';
$lang->store->author           = 'Author';
$lang->store->empty            = 'Empty';
$lang->store->install          = 'Install';
$lang->store->customInstall    = 'Custom Install';
$lang->store->searchApp        = 'Search';
$lang->store->testChannel      = 'Test Version';
$lang->store->stableChannel    = 'Stable Version';
$lang->store->appBasicInfo     = 'Basic Info';
$lang->store->screenshots      = 'Screenshots';
$lang->store->appDynamic       = 'Dynamic';
$lang->store->noDynamicArticle = 'Empty';
$lang->store->noScreenshot     = 'Empty';
$lang->store->cloudStore       = 'Component Store';
$lang->store->browse           = 'Component Store';
$lang->store->appView          = 'Component Detail';

$lang->store->support          = 'Support';
$lang->store->gitUrl           = 'Git';
$lang->store->dockerfileUrl    = 'Dockerfile';
$lang->store->forumUrl         = 'Forum';
$lang->store->alreadyInstalled = 'The component has been installed on the platform, Are you sure you want to continue with the installation?';

$lang->store->featureBar['browse']['id']          = 'Default';
$lang->store->featureBar['browse']['create_time'] = 'Newest';
$lang->store->featureBar['browse']['update_time'] = 'Recently updated';
