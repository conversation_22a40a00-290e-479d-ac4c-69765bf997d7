<?php
/**
 * 按系统统计代码评审问题数。
 * Count of issue.
 *
 * 范围：system
 * 对象：code repo
 * 目的：qc
 * 度量名称：按系统统计代码评审问题数
 * 单位：个
 * 描述：按系统统计代码评审问题数是指代码评审所有的问题数，该度量项可以帮助团队及时识别代码中的潜在缺陷，从而提高代码质量和团队的整体开发效率。
 * 定义：代码评审所有的问题数 过滤已删除的问题 过滤已删除的代码库
 *
 * @copyright Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.zentao.net)
 * <AUTHOR> <<EMAIL>>
 * @package
 * @uses      func
 * @license   ZPL(https://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * @Link      https://www.zentao.net
 */
class count_of_issue extends baseCalc
{
    public $dataset = 'getRepoIssues';

    public $fieldList = array('t1.id');

    public $result = 0;

    public function calculate($row)
    {
        if(!empty($row)) $this->result += 1;
    }

    public function getResult($options = array())
    {
        $records = $this->getRecords(array('value'));
        return $this->filterByOptions($records, $options);
    }
}
