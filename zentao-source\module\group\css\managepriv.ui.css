#privByModuleForm .form-row select {height: 250px !important; background-image: none;}
#privByModuleForm .form-row select option:hover {background: #2e7fff;}

#featureBar > .menu {background: unset;}
#versionSelect {flex: 0 1 12%;}

.container #mainContent {padding-top: 0;}

/* Manage priv by package. */
#managePrivFormContainer {gap: 16px; height: calc(100vh - 157px);}
#managePrivFormContainer > .main {flex: 1 1 75%; overflow-y: auto; padding: 0px; border: 1px solid #FFF; box-sizing: content-box;}
#managePrivFormContainer > .side {flex: 1 1 25%;}
#managePrivFormContainer .table {table-layout: fixed;}
.side .priv-panel {padding: 16px;}
.side .priv-panel {background: #fff; padding: 10px 16px; box-shadow: 0 1px 1px rgb(0 0 0 / 5%), 0 2px 6px 0 rgb(0 0 0 / 5%);}
.side .priv-panel {height: 40%;}
.side .priv-panel .table-empty-tip {padding: calc(15% - 10px) 10px;}
.side .priv-panel + .priv-panel .table-empty-tip {padding: calc(30% - 20px) 10px;}
.side .priv-panel + .priv-panel {margin-top: 16px; height: calc(60% - 16px);}
.side .priv-panel > .panel-content {height: calc(100% - 25px); overflow-y: auto; padding-left: 4px; margin-top: 10px;}
.side .priv-panel > .panel-bottom {padding-top: 20px;}
.side .priv-panel > .panel-title {position: relative;}
.side .icon-help + .popover {font-weight: 400;}
.side .menuTree .checkbox-group + .checkbox-group {margin-top: 20px;}
.side .menuTree .checkbox-group ul > li {padding: 5px 0; white-space: nowrap; overflow: hidden;}
.form-actions.priv-footer {padding: 14px; z-index: 11; box-shadow: inset 0 0px 0px rgb(0 0 0 / 10%), 0 0 5px rgb(0 0 0 / 10%); position: relative; min-height: 40px; background: #fff; border-radius: 0 0 4px 4px; margin: 0 !important; margin-bottom: 10px;}
.form-actions.priv-footer button[type=submit] {margin: 0px 20px;}
#main {padding-bottom: 0px;}

#privPackageList tr {border-bottom-width: 0;}
#privPackageList th.module {width: 142px; min-width: 142px; max-width: 142px; background-color: #EDEEF28C; font-weight: 600; padding-left: 20px; border-color: #FFF;}
#privPackageList .module label {overflow: visible;}
#privPackageList td.package-column {padding: 20px 16px; background-color: #FFF;}
#privPackageList div.package {display: inline-flex; width: calc(25% - 5px); padding: 10px 0; align-items: center;}
#privPackageList .package + .package {padding: 0;}
#privPackageList .package .check-all {height: 20px; max-width: calc(100% - 30px); overflow: hidden;}
#privPackageList .package input {margin-top: 0px; margin-left: 0px; left: 0px; height: 20px;}
#privPackageList .package label {display: inline-block; white-space: nowrap;}
#privPackageList .privs, #privPackageList .menus-privs {padding: 20px 20px 0 20px; margin: 10px 0px;border: 1px solid #D9DBE1; background-color: #F9FAFB; position: relative; display: block; max-width: unset; box-shadow: unset; z-index: 1; width: 100%;}
#privPackageList .privs .popover-content {padding: 0;}
#privPackageList .menus-privs {margin-top: 35px; border-style: dashed;}
#privPackageList .priv-toggle.icon {width: 13px; height: 13px; display: inline-flex; color: #9EA3B0; margin-left: 4px; cursor: pointer; border: 1px solid; border-radius: var(--radius-sm, 2px); align-items: center; justify-content: center;}
#privPackageList .priv-toggle.icon:before {content: "\e925"; font-size: 12px; min-width: unset; transform: scale(0.65); font-weight: 900;}
#privPackageList .priv-toggle.icon.open:before {content: "\e926";}
#privPackageList .popover .popover-content {display: inline-block; padding: 0;}
#privPackageList .popover .arrow {border-bottom-color: #D9DBE1; top: -10px;}
#privPackageList .popover .arrow:before {border-bottom-color: #F9FAFB; top: 2px; width: 16px; height: 16px; border: 1px solid #FFF; border-left-color: #D9DBE1; border-top-color: #D9DBE1;}
#privPackageList .group-item .checkbox-inline label {padding-left: 0}
#privPackageList .group-item {display: inline-block; min-width: 180px; margin-bottom: 15px;}
#privPackageList .group-item > .checkbox-primary {display: inline-block;}

.checkbox-primary > label.checkbox-indeterminate-block:before {--tw-border-opacity: 1; --tw-bg-opacity: 1; background-color: rgba(var(--color-primary-500-rgb),var(--tw-bg-opacity)); border-color: rgba(var(--color-primary-500-rgb),var(--tw-border-opacity));}
.checkbox-primary > label.checkbox-indeterminate-block:after {background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MTIiIGhlaWdodD0iNTEyIiB2aWV3Qm94PSIwIDAgMjA0OCAyMDQ4Ij48cGF0aCBmaWxsPSIjZmZmIiBkPSJNMTUzNiA1MTJ2MTAyNEg1MTJWNTEyaDEwMjR6Ii8+PC9zdmc+); background-size: cover; opacity: 1;}

/* Manage limit group. */
#managePrivForm th.text-right {text-align: right !important;}
#mainContent.manageLimitGroup {padding: 20px; background-color: #FFF;}
#mainContent.manageLimitGroup tr {border-bottom-width: 0;}

/* Manage priv by group. */

#privList td.menus {border-right: 0; padding-right: 0; width: 170px !important;}
#privList td.menus + td {border-left: 0; padding-left: 0px;}
#privList .menus .checkbox-primary {float: left; width: 160px;}
#privList .menus .checkbox-primary:first-child {float: left; width: auto;}
#privList .menus a {margin-left: 10px;}

#privList {margin-bottom: 0px;}
#privList tr {border-bottom-width: 0;}
#privList th.module {width: 142px; min-width: 142px; background-color: #EDEEF28C; font-weight: 600;}
#privList th.package {width: 142px; min-width: 142px; background-color: #F4F5F78C; font-weight: 600;}
#privList th .checkbox-inline input {margin-left: 0px;}
#privList .check-all {display: inline-block;}
#privList .check-all > label {padding-left: 0; font-weight: 600;}
#privList .text-middle.text-left {padding-left: 24px;}
#privList tbody > tr > td {padding: 4px 20px 20px 20px;}
#privList .group-item {margin-top: 16px; display: inline-block; width: 20%;}
#privList .group-item .checkbox-inline label {padding-left: 3px;}
