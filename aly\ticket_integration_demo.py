#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工单系统集成演示代码
演示如何将项目管理系统与工单系统进行集成
"""

import mysql.connector
import json
import time
from datetime import datetime
from typing import Dict, List, Optional
from decimal import Decimal

class TicketSystemConnector:
    """工单系统数据库连接器"""
    
    def __init__(self):
        self.config = {
            'host': '**********',
            'user': 'qyuser',
            'password': 'C~w9d4kaWS',
            'database': 'ticket',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
    
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = mysql.connector.connect(**self.config)
            print("✅ 工单系统数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 工单系统数据库连接失败: {e}")
            return False
    
    def execute_query(self, sql: str, params: tuple = None) -> List[Dict]:
        """执行查询SQL"""
        if not self.connection:
            self.connect()
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(sql, params or ())
            result = cursor.fetchall()
            cursor.close()
            return result
        except Exception as e:
            print(f"❌ SQL执行失败: {e}")
            return []
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("🔒 工单系统数据库连接已关闭")

class TicketDataAnalyzer:
    """工单数据分析器"""
    
    def __init__(self):
        self.connector = TicketSystemConnector()
    
    def get_project_summary(self) -> Dict:
        """获取项目概览数据"""
        if not self.connector.connect():
            return {}
        
        # 获取项目基本信息
        projects_sql = """
        SELECT 
            feelec_project_id,
            feelec_name,
            feelec_manager_id,
            feelec_department_id,
            create_time,
            complete_time
        FROM feelec_project 
        WHERE feelec_delete = 20 
        ORDER BY create_time DESC 
        LIMIT 10
        """
        
        projects = self.connector.execute_query(projects_sql)
        
        # 获取每个项目的工单统计
        for project in projects:
            project_id = project['feelec_project_id']
            
            # 工单统计
            tickets_sql = """
            SELECT 
                COUNT(*) as total_tickets,
                SUM(CASE WHEN feelec_status_id IN ('completed', 'closed') THEN 1 ELSE 0 END) as completed_tickets,
                AVG(CASE WHEN complete_time > 0 THEN complete_time - create_time ELSE NULL END) as avg_completion_time
            FROM feelec_ticket 
            WHERE feelec_project_id = %s AND feelec_delete = 20
            """
            
            ticket_stats = self.connector.execute_query(tickets_sql, (project_id,))
            if ticket_stats:
                project.update(ticket_stats[0])
            
            # 格式化时间
            project['create_time_formatted'] = datetime.fromtimestamp(project['create_time']).strftime('%Y-%m-%d %H:%M:%S')
            if project['complete_time'] > 0:
                project['complete_time_formatted'] = datetime.fromtimestamp(project['complete_time']).strftime('%Y-%m-%d %H:%M:%S')
            else:
                project['complete_time_formatted'] = '未完成'
        
        self.connector.close()
        return {
            'projects': projects,
            'total_projects': len(projects)
        }
    
    def get_ticket_details(self, project_id: str) -> Dict:
        """获取指定项目的工单详情"""
        if not self.connector.connect():
            return {}
        
        # 获取项目工单列表
        tickets_sql = """
        SELECT 
            t.feelec_ticket_id,
            t.feelec_title,
            t.feelec_ticket_no,
            t.feelec_publisher_id,
            t.feelec_processor_id,
            t.feelec_priority,
            t.feelec_status_id,
            t.first_assign_time,
            t.first_process_time,
            t.deadlines,
            t.complete_time,
            t.create_time,
            s.feelec_name as status_name
        FROM feelec_ticket t
        LEFT JOIN feelec_ticket_status s ON t.feelec_status_id = s.feelec_status_id
        WHERE t.feelec_project_id = %s AND t.feelec_delete = 20
        ORDER BY t.create_time DESC
        """
        
        tickets = self.connector.execute_query(tickets_sql, (project_id,))
        
        # 格式化数据
        for ticket in tickets:
            # 优先级映射
            priority_map = {1: '紧急', 2: '高', 3: '一般', 4: '低'}
            ticket['priority_text'] = priority_map.get(ticket['feelec_priority'], '未知')
            
            # 时间格式化
            ticket['create_time_formatted'] = datetime.fromtimestamp(ticket['create_time']).strftime('%Y-%m-%d %H:%M:%S')
            
            if ticket['complete_time'] > 0:
                ticket['complete_time_formatted'] = datetime.fromtimestamp(ticket['complete_time']).strftime('%Y-%m-%d %H:%M:%S')
                ticket['is_completed'] = True
            else:
                ticket['complete_time_formatted'] = '未完成'
                ticket['is_completed'] = False
            
            # 计算处理时长
            if ticket['first_process_time'] > 0 and ticket['complete_time'] > 0:
                process_duration = ticket['complete_time'] - ticket['first_process_time']
                ticket['process_duration_hours'] = round(process_duration / 3600, 2)
            else:
                ticket['process_duration_hours'] = None
        
        self.connector.close()
        return {
            'project_id': project_id,
            'tickets': tickets,
            'total_tickets': len(tickets)
        }
    
    def get_team_workload_analysis(self, department_id: str = None) -> Dict:
        """获取团队工作负荷分析"""
        if not self.connector.connect():
            return {}
        
        # 构建SQL查询
        base_sql = """
        SELECT 
            t.feelec_processor_id,
            t.feelec_department_id,
            COUNT(*) as total_tickets,
            SUM(CASE WHEN t.complete_time > 0 THEN 1 ELSE 0 END) as completed_tickets,
            AVG(CASE WHEN t.complete_time > 0 AND t.first_process_time > 0 
                THEN t.complete_time - t.first_process_time ELSE NULL END) as avg_process_time,
            COUNT(CASE WHEN t.feelec_priority = 1 THEN 1 END) as urgent_tickets,
            COUNT(CASE WHEN t.deadlines > 0 AND t.deadlines < UNIX_TIMESTAMP() AND t.complete_time = 0 THEN 1 END) as overdue_tickets
        FROM feelec_ticket t
        WHERE t.feelec_delete = 20 
        AND t.create_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY))
        """
        
        params = []
        if department_id:
            base_sql += " AND t.feelec_department_id = %s"
            params.append(department_id)
        
        base_sql += """
        GROUP BY t.feelec_processor_id, t.feelec_department_id
        HAVING total_tickets > 0
        ORDER BY total_tickets DESC
        """
        
        workload_data = self.connector.execute_query(base_sql, tuple(params))
        
        # 计算统计数据
        for item in workload_data:
            if item['total_tickets'] > 0:
                item['completion_rate'] = round((item['completed_tickets'] / item['total_tickets']) * 100, 2)
            else:
                item['completion_rate'] = 0
            
            if item['avg_process_time']:
                item['avg_process_hours'] = round(item['avg_process_time'] / 3600, 2)
            else:
                item['avg_process_hours'] = 0
        
        self.connector.close()
        return {
            'workload_data': workload_data,
            'analysis_period': '最近30天',
            'total_processors': len(workload_data)
        }

class ProjectTicketIntegrator:
    """项目工单集成器"""
    
    def __init__(self):
        self.analyzer = TicketDataAnalyzer()
    
    def generate_integration_report(self) -> Dict:
        """生成集成分析报告"""
        print("🔍 开始分析工单系统数据...")
        
        # 获取项目概览
        project_summary = self.analyzer.get_project_summary()
        print(f"📊 发现 {project_summary.get('total_projects', 0)} 个项目")
        
        # 获取团队负荷分析
        workload_analysis = self.analyzer.get_team_workload_analysis()
        print(f"👥 分析了 {workload_analysis.get('total_processors', 0)} 个处理人的工作负荷")
        
        # 生成集成建议
        integration_suggestions = self._generate_integration_suggestions(project_summary, workload_analysis)
        
        report = {
            'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'project_summary': project_summary,
            'workload_analysis': workload_analysis,
            'integration_suggestions': integration_suggestions
        }
        
        return report
    
    def _generate_integration_suggestions(self, project_summary: Dict, workload_analysis: Dict) -> List[Dict]:
        """生成集成建议"""
        suggestions = []
        
        # 基于项目数据的建议
        if project_summary.get('projects'):
            active_projects = len([p for p in project_summary['projects'] if p.get('complete_time', 0) == 0])
            suggestions.append({
                'category': '项目管理',
                'suggestion': f'发现 {active_projects} 个活跃项目，建议集成工单系统的项目进度跟踪功能',
                'priority': 'high'
            })
        
        # 基于工作负荷的建议
        if workload_analysis.get('workload_data'):
            high_workload_count = len([w for w in workload_analysis['workload_data'] if w.get('total_tickets', 0) > 20])
            if high_workload_count > 0:
                suggestions.append({
                    'category': '团队管理',
                    'suggestion': f'发现 {high_workload_count} 个高负荷处理人，建议集成工单分配优化功能',
                    'priority': 'medium'
                })
        
        # 通用集成建议
        suggestions.extend([
            {
                'category': '数据同步',
                'suggestion': '建议实现项目任务与工单的双向同步，确保数据一致性',
                'priority': 'high'
            },
            {
                'category': '状态管理',
                'suggestion': '集成工单状态管理，实现任务状态的自动更新',
                'priority': 'high'
            },
            {
                'category': '报表分析',
                'suggestion': '利用工单系统的丰富数据，增强项目报表和分析功能',
                'priority': 'medium'
            }
        ])
        
        return suggestions

def main():
    """主函数 - 演示集成分析"""
    print("🚀 工单系统集成分析演示")
    print("=" * 50)
    
    integrator = ProjectTicketIntegrator()
    
    try:
        # 生成集成报告
        report = integrator.generate_integration_report()
        
        # 保存报告 - 处理Decimal类型
        def decimal_converter(obj):
            if isinstance(obj, Decimal):
                return float(obj)
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

        report_file = f"integration_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=decimal_converter)
        
        print(f"📄 集成分析报告已保存到: {report_file}")
        
        # 显示关键信息
        print("\n📋 集成建议摘要:")
        for suggestion in report['integration_suggestions']:
            priority_icon = "🔴" if suggestion['priority'] == 'high' else "🟡"
            print(f"{priority_icon} [{suggestion['category']}] {suggestion['suggestion']}")
        
        print("\n✅ 分析完成！")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")

if __name__ == "__main__":
    main()
