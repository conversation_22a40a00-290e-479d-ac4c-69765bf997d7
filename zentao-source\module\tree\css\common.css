.row-module {max-width: 500px; min-width: 400px;}
.row-module + .row-module {margin-top: 5px;}
.col-module {min-width: 200px;}
.col-module .form-control {border-radius: 2px 0 0 2px;}
.col-module select.form-control {border-radius: 0 2px 2px 0; border-left-width: 0;}
.col-module select.form-control:focus {margin-left: -1px; border-left-width: 1px;}
.col-shorts .form-control {border-radius: 0 2px 2px 0; border-left-width: 0;}
.col-shorts .form-control:focus {margin-left: -1px; border-left-width: 1px;}
.col-shorts {width: 110px;}
.col-actions {width: 80px; padding-left: 5px;}

.col-module .chosen-container-single .chosen-single {border-radius: 2px 0 0 2px; border-right-width: 0; padding-right: 1px;}
.col-module .chosen-container-active .chosen-single {border-right-width: 1px; padding-right: 0;}
.col-shorts .chosen-container-single .chosen-single {border-radius: 0 2px 2px 0;}

#modulesTree li {padding-left: 20px;}
#modulesTree li > .list-toggle {top: 0;}

#childrenForm table tr > td.text-middle span {display: block; float: left;}

.body-modal #mainMenu {display: none;}
