#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的Excel导出修复和测试脚本
"""

import pymysql
import pandas as pd
import io
import os
import requests
import time
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': os.environ.get('DB_HOST', 'localhost'),
    'port': int(os.environ.get('DB_PORT', '3306')),
    'user': os.environ.get('DB_USER', 'root'),
    'password': os.environ.get('DB_PASSWORD', ''),
    'database': os.environ.get('DB_NAME', 'kanban2'),
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(**DB_CONFIG)

def check_and_fix_data():
    """检查并修复数据"""
    print("🔍 检查数据库数据...")
    
    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 1. 检查督办事项
        cursor.execute("SELECT COUNT(*) as count FROM supervision_items")
        items_count = cursor.fetchone()['count']
        print(f"督办事项数量: {items_count}")
        
        if items_count == 0:
            print("❌ 没有督办事项数据，需要先运行数据修复脚本")
            return False
        
        # 2. 检查公司数据
        cursor.execute("SELECT COUNT(*) as count FROM companies WHERE is_active = TRUE")
        companies_count = cursor.fetchone()['count']
        print(f"活跃公司数量: {companies_count}")
        
        # 3. 检查状态数据
        cursor.execute("SELECT COUNT(*) as count FROM company_supervision_status")
        status_count = cursor.fetchone()['count']
        print(f"状态记录数量: {status_count}")
        
        # 4. 如果没有状态数据，创建测试数据
        if status_count == 0:
            print("📊 创建测试状态数据...")
            
            # 获取督办事项和公司
            cursor.execute("SELECT id, sequence_number FROM supervision_items ORDER BY sequence_number")
            items = cursor.fetchall()
            
            cursor.execute("SELECT id, company_code FROM companies WHERE is_active = TRUE ORDER BY display_order")
            companies = cursor.fetchall()
            
            # 创建测试状态数据
            insert_count = 0
            for item in items:
                for company in companies:
                    # 根据序号创建不同状态
                    seq = item['sequence_number']
                    if seq <= 2:
                        status = '√'  # 已完成
                    elif seq <= 5:
                        status = 'O'  # 进行中
                    elif seq <= 8:
                        status = 'X'  # 未启动
                    else:
                        status = '—'  # 不需要执行
                    
                    cursor.execute("""
                        INSERT INTO company_supervision_status 
                        (supervision_item_id, company_id, status, updated_by, created_at, updated_at)
                        VALUES (%s, %s, %s, 'system', NOW(), NOW())
                    """, (item['id'], company['id'], status))
                    insert_count += 1
            
            connection.commit()
            print(f"✅ 创建了 {insert_count} 条测试状态数据")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据检查失败: {str(e)}")
        return False

def test_excel_generation():
    """测试Excel生成"""
    print("\n📊 测试Excel生成...")
    
    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 获取数据
        print("📋 获取督办事项数据...")
        cursor.execute("""
            SELECT id, sequence_number, work_dimension, work_theme, supervision_source,
                   work_content, is_annual_assessment, completion_deadline, overall_progress
            FROM supervision_items 
            ORDER BY sequence_number
        """)
        items = cursor.fetchall()
        print(f"获取到 {len(items)} 个督办事项")
        
        print("🏢 获取公司数据...")
        cursor.execute("""
            SELECT id, company_code, company_name 
            FROM companies 
            WHERE is_active = TRUE 
            ORDER BY display_order
        """)
        companies = cursor.fetchall()
        print(f"获取到 {len(companies)} 家公司")
        
        print("📈 获取状态数据...")
        cursor.execute("""
            SELECT css.supervision_item_id, c.company_code, css.status
            FROM company_supervision_status css
            JOIN companies c ON css.company_id = c.id
            WHERE c.is_active = TRUE
        """)
        statuses = cursor.fetchall()
        print(f"获取到 {len(statuses)} 条状态记录")
        
        # 构建状态映射
        status_map = {}
        for status in statuses:
            item_id = status['supervision_item_id']
            company_code = status['company_code']
            if item_id not in status_map:
                status_map[item_id] = {}
            status_map[item_id][company_code] = status['status']
        
        # 构建Excel数据
        print("📝 构建Excel数据...")
        excel_data = []
        
        # 表头
        header = ['序号', '工作维度', '工作主题', '督办来源', '工作内容和完成标志', 
                 '是否年度绩效考核指标', '完成时限', '整体进度']
        for company in companies:
            header.append(str(company['company_name']))
        
        # 数据行
        for item in items:
            row = [
                int(item['sequence_number']) if item['sequence_number'] else 0,
                str(item['work_dimension']) if item['work_dimension'] else '',
                str(item['work_theme']) if item['work_theme'] else '',
                str(item['supervision_source']) if item['supervision_source'] else '',
                str(item['work_content']) if item['work_content'] else '',
                str(item['is_annual_assessment']) if item['is_annual_assessment'] else '否',
                str(item['completion_deadline']) if item['completion_deadline'] else '',
                str(item['overall_progress']) if item['overall_progress'] else 'X 未启动'
            ]
            
            # 添加公司状态
            item_statuses = status_map.get(item['id'], {})
            for company in companies:
                company_status = item_statuses.get(company['company_code'], 'X')
                row.append(str(company_status))
            
            excel_data.append(row)
        
        # 创建DataFrame
        df = pd.DataFrame(excel_data, columns=header)
        print(f"DataFrame创建成功，形状: {df.shape}")
        
        # 生成Excel文件
        print("📄 生成Excel文件...")
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='督办管理', index=False)
            
            # 设置列宽
            worksheet = writer.sheets['督办管理']
            worksheet.column_dimensions['A'].width = 8   # 序号
            worksheet.column_dimensions['B'].width = 20  # 工作维度
            worksheet.column_dimensions['C'].width = 30  # 工作主题
            worksheet.column_dimensions['D'].width = 15  # 督办来源
            worksheet.column_dimensions['E'].width = 50  # 工作内容
            worksheet.column_dimensions['F'].width = 15  # 是否考核指标
            worksheet.column_dimensions['G'].width = 12  # 完成时限
            worksheet.column_dimensions['H'].width = 12  # 整体进度
            
            # 设置公司列宽
            for i, company in enumerate(companies):
                col_letter = chr(ord('I') + i)
                if ord(col_letter) <= ord('Z'):
                    worksheet.column_dimensions[col_letter].width = 8
        
        output.seek(0)
        
        # 保存测试文件
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_supervision_export_{current_time}.xlsx"
        
        with open(filename, 'wb') as f:
            f.write(output.read())
        
        print(f"✅ Excel文件生成成功: {filename}")
        print(f"文件大小: {os.path.getsize(filename)} 字节")
        
        connection.close()
        return True, filename
        
    except Exception as e:
        print(f"❌ Excel生成失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def test_api_export():
    """测试API导出"""
    print("\n🌐 测试API导出...")
    
    try:
        # 测试不需要认证的导出
        print("📤 调用导出API...")
        response = requests.get("http://localhost:8000/api/v1/new-supervision/export-excel", timeout=30)
        
        print(f"API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            content_length = len(response.content)
            print(f"响应内容长度: {content_length} 字节")
            
            if content_length > 1000:  # Excel文件应该有一定大小
                # 保存API导出的文件
                api_filename = f"api_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                with open(api_filename, 'wb') as f:
                    f.write(response.content)
                
                print(f"✅ API导出成功: {api_filename}")
                return True
            else:
                print("❌ API导出的文件太小")
                print(f"内容预览: {response.content[:200]}")
                return False
        else:
            print(f"❌ API导出失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 Excel导出完整修复和测试")
    print("=" * 60)

    # 1. 检查并修复数据
    print("步骤1: 检查并修复数据")
    if not check_and_fix_data():
        print("❌ 数据修复失败，无法继续")
        return

    # 2. 测试Excel生成
    print("\n步骤2: 测试Excel生成")
    excel_success, excel_file = test_excel_generation()

    # 3. 等待后端服务
    print("\n步骤3: 等待后端服务")
    time.sleep(2)

    # 4. 测试API导出
    print("\n步骤4: 测试API导出")
    api_success = test_api_export()

    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"数据修复: ✅ 成功")
    print(f"Excel生成: {'✅ 成功' if excel_success else '❌ 失败'}")
    print(f"API导出: {'✅ 成功' if api_success else '❌ 失败'}")

    if excel_success and api_success:
        print("\n🎉 Excel导出功能完全正常！")
        print("现在可以在浏览器中正常使用导出功能了")
    else:
        print("\n❌ 还有问题需要解决")
        if excel_file:
            print(f"可以查看生成的测试文件: {excel_file}")

    print("\n🏁 测试完成")

if __name__ == "__main__":
    main()
