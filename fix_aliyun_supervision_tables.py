#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复阿里云数据库中的督办管理表结构
"""

import pymysql
import time

def fix_aliyun_supervision_tables():
    """修复阿里云督办管理表"""
    print("🔧 修复阿里云数据库中的督办管理表结构...")
    
    try:
        # 连接阿里云数据库
        connection = pymysql.connect(
            host='rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
            port=3306,
            user='cyh',
            password='Qq188788',
            database='kanban2',
            charset='utf8mb4',
            connect_timeout=30
        )
        
        print("✅ 阿里云数据库连接成功")
        
        with connection.cursor() as cursor:
            # 1. 检查supervision_items表是否存在
            cursor.execute("SHOW TABLES LIKE 'supervision_items'")
            if cursor.fetchone():
                print("✅ supervision_items 表存在")
                
                # 检查表结构
                cursor.execute("DESCRIBE supervision_items")
                columns = cursor.fetchall()
                column_names = [col[0] for col in columns]
                
                print(f"   当前字段: {column_names}")
                
                # 检查并添加缺失的字段
                missing_fields = []
                
                if 'is_annual_assessment' not in column_names:
                    missing_fields.append('is_annual_assessment')
                    print("   ❌ 缺少 is_annual_assessment 字段")
                    
                if 'progress_description' not in column_names:
                    missing_fields.append('progress_description')
                    print("   ❌ 缺少 progress_description 字段")
                
                # 添加缺失的字段
                if missing_fields:
                    print(f"🔧 添加缺失的字段: {missing_fields}")
                    
                    if 'is_annual_assessment' in missing_fields:
                        cursor.execute("""
                            ALTER TABLE supervision_items 
                            ADD COLUMN is_annual_assessment ENUM('是', '否') DEFAULT '否' COMMENT '是否年度绩效考核指标'
                        """)
                        print("   ✅ 添加 is_annual_assessment 字段成功")
                    
                    if 'progress_description' in missing_fields:
                        cursor.execute("""
                            ALTER TABLE supervision_items 
                            ADD COLUMN progress_description TEXT COMMENT '7月28日进度情况'
                        """)
                        print("   ✅ 添加 progress_description 字段成功")
                    
                    # 检查overall_progress字段类型
                    for column in columns:
                        if column[0] == 'overall_progress':
                            if 'enum' not in column[1].lower():
                                print("   🔧 修改 overall_progress 字段类型...")
                                cursor.execute("""
                                    ALTER TABLE supervision_items 
                                    MODIFY COLUMN overall_progress ENUM('X 未启动', 'O进行中', '！逾期', '√ 已完成', '— 不需要执行') 
                                    DEFAULT 'X 未启动' COMMENT '整体进度'
                                """)
                                print("   ✅ overall_progress 字段类型修改成功")
                            break
                    
                    connection.commit()
                    print("✅ 表结构修复完成")
                else:
                    print("✅ 所有必需字段都存在")
                
            else:
                print("❌ supervision_items 表不存在，创建新表...")
                # 创建完整的supervision_items表
                cursor.execute("""
                    CREATE TABLE supervision_items (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        sequence_number INT NOT NULL COMMENT '序号',
                        work_dimension VARCHAR(100) NOT NULL COMMENT '工作维度',
                        work_theme VARCHAR(200) NOT NULL COMMENT '工作主题',
                        supervision_source VARCHAR(100) NOT NULL COMMENT '督办来源',
                        work_content TEXT NOT NULL COMMENT '工作内容和完成标志',
                        is_annual_assessment ENUM('是', '否') DEFAULT '否' COMMENT '是否年度绩效考核指标',
                        completion_deadline VARCHAR(50) NOT NULL COMMENT '完成时限',
                        progress_description TEXT COMMENT '7月28日进度情况',
                        overall_progress ENUM('X 未启动', 'O进行中', '！逾期', '√ 已完成', '— 不需要执行') DEFAULT 'X 未启动' COMMENT '整体进度',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='督办事项表'
                """)
                print("✅ supervision_items 表创建成功")
            
            # 2. 检查companies表
            cursor.execute("SHOW TABLES LIKE 'companies'")
            if not cursor.fetchone():
                print("❌ companies 表不存在，创建新表...")
                cursor.execute("""
                    CREATE TABLE companies (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        company_code VARCHAR(50) NOT NULL UNIQUE COMMENT '公司代码',
                        company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
                        display_order INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
                        is_active TINYINT DEFAULT 1 COMMENT '是否启用',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司表'
                """)
                print("✅ companies 表创建成功")
                
                # 插入公司数据
                companies_data = [
                    ('CXBX', '财险', 1), ('SXBX', '寿险', 2), ('JINZU', '金租', 3),
                    ('ZICHAN', '资管', 4), ('GUANGZU', '广租', 5), ('TONGSHENG', '通盛', 6),
                    ('DANBAO', '担保', 7), ('XIAODAI', '小贷', 8), ('BAOLI', '保理', 9),
                    ('BUDONGCHAN', '不动产', 10), ('ZHENGXIN', '征信', 11), ('JINFU', '金服', 12), ('BENBU', '本部', 13)
                ]
                
                for company_code, company_name, display_order in companies_data:
                    cursor.execute("""
                        INSERT IGNORE INTO companies (company_code, company_name, display_order)
                        VALUES (%s, %s, %s)
                    """, (company_code, company_name, display_order))
                print("✅ 公司数据插入成功")
            else:
                print("✅ companies 表存在")
            
            # 3. 检查company_supervision_status表
            cursor.execute("SHOW TABLES LIKE 'company_supervision_status'")
            if not cursor.fetchone():
                print("❌ company_supervision_status 表不存在，创建新表...")
                cursor.execute("""
                    CREATE TABLE company_supervision_status (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        supervision_item_id INT NOT NULL COMMENT '督办事项ID',
                        company_id INT NOT NULL COMMENT '公司ID',
                        status ENUM('√', 'O', '！', 'X', '—') DEFAULT 'X' COMMENT '状态：√已完成 O进行中 ！逾期 X未启动 —不需要执行',
                        progress_description TEXT COMMENT '当前进展情况',
                        existing_problems TEXT COMMENT '存在问题',
                        next_plan TEXT COMMENT '下一步计划',
                        updated_by VARCHAR(50) COMMENT '更新人',
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE KEY uk_item_company (supervision_item_id, company_id)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司督办状态表'
                """)
                print("✅ company_supervision_status 表创建成功")
            else:
                print("✅ company_supervision_status 表存在")
            
            # 4. 检查supervision_status_history表
            cursor.execute("SHOW TABLES LIKE 'supervision_status_history'")
            if not cursor.fetchone():
                print("❌ supervision_status_history 表不存在，创建新表...")
                cursor.execute("""
                    CREATE TABLE supervision_status_history (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        supervision_item_id INT NOT NULL COMMENT '督办事项ID',
                        company_id INT NOT NULL COMMENT '公司ID',
                        old_status ENUM('√', 'O', '！', 'X', '—') COMMENT '原状态',
                        new_status ENUM('√', 'O', '！', 'X', '—') COMMENT '新状态',
                        old_progress_description TEXT COMMENT '原进展情况',
                        new_progress_description TEXT COMMENT '新进展情况',
                        old_existing_problems TEXT COMMENT '原存在问题',
                        new_existing_problems TEXT COMMENT '新存在问题',
                        old_next_plan TEXT COMMENT '原下一步计划',
                        new_next_plan TEXT COMMENT '新下一步计划',
                        change_reason VARCHAR(500) COMMENT '变更原因',
                        updated_by VARCHAR(50) COMMENT '更新人',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='督办状态变更历史表'
                """)
                print("✅ supervision_status_history 表创建成功")
            else:
                print("✅ supervision_status_history 表存在")
            
            # 5. 插入测试数据（如果没有数据）
            cursor.execute("SELECT COUNT(*) FROM supervision_items")
            items_count = cursor.fetchone()[0]
            
            if items_count == 0:
                print("❌ 没有督办事项数据，插入测试数据...")
                items_data = [
                    (1, '一、监管和制度', '建立条线ITBP团队管理办法', '3月科技例会', 
                     '各单位及部门明确对接人，以条线形式建立ITBP服务团队。', 
                     '否', '5月末', '各部门及子公司已制定ITBP对接人。', '√ 已完成'),
                    (2, '一、监管和制度', '建立项目红绿灯管理办法', '3月科技例会',
                     '开展重点项目周跟踪机制，推行"亮黄牌"机制。',
                     '否', '4月末', '已建立公共台账实时反应项目红绿灯。', '√ 已完成'),
                    (3, '一、监管和制度', '印发8个信息化管理制度', '5月科技例会',
                     '各子公司参照集团印发的信息化管理制度。',
                     '否', '8月末', '本部制度印发已较为完整。', 'O进行中'),
                    (4, '二、数据治理和系统覆盖', '第一批次数据治理', '3月科技例会',
                     '持续开展数据治理工作，准确率、完整率、及时率达到要求。',
                     '是', '4月末', '已完成第一批次数据治理。', '√ 已完成'),
                    (5, '二、数据治理和系统覆盖', '第二批次数据治理', '5月科技例会',
                     '涉及经营、风控、人力的152项数据治理。',
                     '是', '11月末', '正在进行中。', 'O进行中')
                ]
                
                for item_data in items_data:
                    cursor.execute("""
                        INSERT INTO supervision_items 
                        (sequence_number, work_dimension, work_theme, supervision_source, work_content, 
                         is_annual_assessment, completion_deadline, progress_description, overall_progress)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, item_data)
                print("✅ 督办事项测试数据插入成功")
            else:
                print(f"✅ 已有 {items_count} 条督办事项数据")
            
            # 6. 为督办事项创建公司状态记录
            cursor.execute("SELECT COUNT(*) FROM company_supervision_status")
            status_count = cursor.fetchone()[0]
            
            if status_count == 0:
                print("❌ 没有公司状态记录，创建默认记录...")
                cursor.execute("SELECT id FROM supervision_items")
                items = cursor.fetchall()
                
                cursor.execute("SELECT id FROM companies WHERE is_active = 1")
                companies = cursor.fetchall()
                
                for item in items:
                    for company in companies:
                        cursor.execute("""
                            INSERT IGNORE INTO company_supervision_status (supervision_item_id, company_id, status)
                            VALUES (%s, %s, 'X')
                        """, (item[0], company[0]))
                print("✅ 公司状态记录创建成功")
            else:
                print(f"✅ 已有 {status_count} 条公司状态记录")
            
            connection.commit()
            
            # 验证修复结果
            print(f"\n📊 修复结果验证:")
            cursor.execute("SELECT COUNT(*) FROM supervision_items")
            items_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM companies")
            companies_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM company_supervision_status")
            status_count = cursor.fetchone()[0]
            
            print(f"   督办事项: {items_count} 条")
            print(f"   公司: {companies_count} 个")
            print(f"   状态记录: {status_count} 条")
            
            # 测试查询
            print(f"\n🧪 测试查询:")
            cursor.execute("""
                SELECT id, sequence_number, work_dimension, work_theme, supervision_source,
                       work_content, is_annual_assessment, completion_deadline, 
                       progress_description, overall_progress
                FROM supervision_items 
                ORDER BY sequence_number
                LIMIT 3
            """)
            items = cursor.fetchall()
            
            print(f"✅ 查询成功，返回 {len(items)} 条记录")
            for item in items:
                print(f"   {item[1]}. {item[3]} - 考核指标:{item[6]} - 进度:{item[9]}")
        
        connection.close()
        print("\n🎉 阿里云数据库修复完成！")
        return True
        
    except Exception as e:
        print(f"❌ 阿里云数据库修复失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 修复阿里云数据库中的督办管理表")
    print("=" * 60)
    
    success = fix_aliyun_supervision_tables()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 阿里云数据库修复完成")
        print("\n📋 下一步:")
        print("   1. 重启后端服务")
        print("   2. 测试API功能")
        print("   3. 在浏览器中测试前端功能")
        print("\n🌐 访问地址: http://localhost:3000/new-supervision")
    else:
        print("❌ 阿里云数据库修复失败")
    
    print("🏁 修复完成")

if __name__ == "__main__":
    main()
