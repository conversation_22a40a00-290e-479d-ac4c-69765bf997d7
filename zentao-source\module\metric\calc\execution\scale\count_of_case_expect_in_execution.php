<?php
/**
 * 按执行统计的测试用例预期条目数
 * Count of case expect in execution.
 *
 * 范围：execution
 * 对象：case
 * 目的：scale
 * 度量名称：按执行统计的测试用例预期条目数
 * 单位：个
 * 描述：按执行统计的测试用例预期条目数是指关联进执行的所有用例的预期条目数之和，可以用于评估测试用例的细致程度，可以帮助团队评估测试的深度和需求的复杂性。
 * 定义：执行中满足以下条件的用例预期条目的求和，条件是：执行下用例列表中的用例数，过滤已删除的用例，过滤已删除的执行，过滤已删除的项目。
 *
 * @copyright Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.zentao.net)
 * <AUTHOR> <<EMAIL>>
 * @package
 * @uses      func
 * @license   ZPL(https://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * @Link      https://www.zentao.net
 */
class count_of_case_expect_in_execution extends baseCalc
{
    public $dataset = 'getExecutionCasesSteps';

    public $fieldList = array('t2.project as execution', 't1.expect');

    public $result = array();

    public function calculate($row)
    {
        if(empty($row->expect)) return;
        if(!isset($this->result[$row->execution])) $this->result[$row->execution] = 0;
        $this->result[$row->execution] += 1;
    }

    public function getResult($options = array())
    {
        return $this->filterByOptions($this->getRecords(array('execution', 'value')), $options);
    }
}
