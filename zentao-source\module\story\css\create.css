.input-control.required:after {display: block; right: 29px; top: 5px;}
#pri + .chosen-container > .chosen-single {border-top-left-radius: 0 !important; border-bottom-left-radius: 0 !important;}

.pri-selector > .btn {padding: 5px 8px !important; width: 100%;}
.pri-selector > .dropdown-menu {padding: 10px;}

#source_chosen {min-width: 50px !important;}
#feedbackBox > .input-group + #notifyEmailLabel {position: relative; width: 50%; left: 48%}
#product + .picker{min-width: 400px}
#dataform .priBox, #dataform .estimateBox {width: 120px;}
#dataform .categoryBox {width: 150px;}
.needNotReviewBox {width: 130px; display: inline-block; padding: 7px;}
.input-group-addon.assignedTo {min-width: 77px;}
.input-group .source {display: flex; width: 50%;}
.input-group .sourceNote {display: flex; width: 50%;}
.source .input-group-addon, .sourceNote .input-group-addon {padding-top: 9px;}
#sourceNoteBox {width: 80px;}
#moduleIdBox .input-group-addon {min-width: 77px;}

.btn.btn-action, .c-actions .btn {color: grey}
#storyNoticeBranch {font-size: 13px; color: #5e626d;}
#storyNoticeBranch i {font-size: 14px; color: #0075ff; margin-right: 3px;}

.moduleBox .required:after {right: 25px;}