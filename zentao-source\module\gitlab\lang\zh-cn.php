<?php
$lang->gitlab->common            = 'GitLab';
$lang->gitlab->browse            = '浏览GitLab';
$lang->gitlab->search            = '搜索';
$lang->gitlab->create            = '添加GitLab';
$lang->gitlab->edit              = '编辑GitLab';
$lang->gitlab->view              = 'GitLab详情';
$lang->gitlab->bindUser          = '权限设置';
$lang->gitlab->webhook           = '接口：允许Webhook调用';
$lang->gitlab->importIssue       = '关联Issue';
$lang->gitlab->delete            = '删除GitLab';
$lang->gitlab->confirmDelete     = '确认删除该GitLab吗？';
$lang->gitlab->gitlabAvatar      = '头像';
$lang->gitlab->gitlabAccount     = 'GitLab用户';
$lang->gitlab->gitlabEmail       = 'GitLab用户邮箱';
$lang->gitlab->zentaoEmail       = '禅道用户邮箱';
$lang->gitlab->zentaoAccount     = '禅道用户';
$lang->gitlab->accountDesc       = '(系统会将相同邮箱地址的用户自动匹配)';
$lang->gitlab->bindingStatus     = '绑定状态';
$lang->gitlab->all               = '全部';
$lang->gitlab->notBind           = '未绑定';
$lang->gitlab->binded            = '已绑定';
$lang->gitlab->bindedError       = '绑定的用户已删除或者已修改，请重新绑定';
$lang->gitlab->bindDynamic       = '%s与禅道用户%s';
$lang->gitlab->serverFail        = '连接GitLab服务器异常，请检查GitLab服务器。';
$lang->gitlab->lastUpdate        = '最后更新';
$lang->gitlab->confirmAddWebhook = '您确定创建Webhook吗？';
$lang->gitlab->addWebhookSuccess = 'Webhook创建成功';
$lang->gitlab->failCreateWebhook = 'Webhook创建失败，请在tmp/log/saas.' . date('Ymd') . '.log.php 中查看日志';
$lang->gitlab->placeholderSearch = '请输入名称';

$lang->gitlab->bindStatus['binded']      = $lang->gitlab->binded;
$lang->gitlab->bindStatus['notBind']     = "<span class='text-danger'>{$lang->gitlab->notBind}</span>";
$lang->gitlab->bindStatus['bindedError'] = "<span class='text-danger'>{$lang->gitlab->bindedError}</span>";

$lang->gitlab->browseAction         = 'GitLab列表';
$lang->gitlab->deleteAction         = '删除GitLab';
$lang->gitlab->gitlabProject        = "{$lang->gitlab->common}项目";
$lang->gitlab->browseProject        = "GitLab项目列表";
$lang->gitlab->browseUser           = "用户";
$lang->gitlab->browseGroup          = "GitLab群组列表";
$lang->gitlab->browseBranch         = "GitLab分支列表";
$lang->gitlab->browseTag            = "GitLab标签列表";
$lang->gitlab->browseTagPriv        = "标签保护管理";
$lang->gitlab->gitlabIssue          = "{$lang->gitlab->common} issue";
$lang->gitlab->zentaoProduct        = '禅道产品';
$lang->gitlab->objectType           = '类型'; // task, bug, story
$lang->gitlab->******************** = '项目成员管理';
$lang->gitlab->createProject        = '添加GitLab项目';
$lang->gitlab->editProject          = '编辑GitLab项目';
$lang->gitlab->deleteProject        = '删除GitLab项目';
$lang->gitlab->createGroup          = '添加群组';
$lang->gitlab->editGroup            = '编辑群组';
$lang->gitlab->deleteGroup          = '删除群组';
$lang->gitlab->createUser           = '添加用户';
$lang->gitlab->editUser             = '编辑用户';
$lang->gitlab->deleteUser           = '删除用户';
$lang->gitlab->createBranch         = '创建分支';
$lang->gitlab->manageGroupMembers   = '群组成员管理';
$lang->gitlab->createWebhook        = '创建Webhook';
$lang->gitlab->browseBranchPriv     = '分支保护管理';
$lang->gitlab->createTag            = '创建标签';
$lang->gitlab->deleteTag            = '删除标签';
$lang->gitlab->saveFailed           = '『%s』保存失败';

$lang->gitlab->id             = 'ID';
$lang->gitlab->name           = "应用名称";
$lang->gitlab->url            = '服务器地址';
$lang->gitlab->token          = 'Token';
$lang->gitlab->defaultProject = '默认项目';
$lang->gitlab->private        = 'MD5验证';

$lang->gitlab->server        = "服务器列表";
$lang->gitlab->lblCreate     = '添加GitLab服务器';
$lang->gitlab->desc          = '描述';
$lang->gitlab->tokenFirst    = 'Token不为空时，优先使用Token。';
$lang->gitlab->tips          = '使用密码时，请在GitLab全局安全设置中禁用"防止跨站点请求伪造"选项。';
$lang->gitlab->emptyError    = "不能为空";
$lang->gitlab->createSuccess = "创建成功";
$lang->gitlab->mustBindUser  = '您还未绑定GitLab用户，请联系管理员进行绑定';
$lang->gitlab->noAccess      = '权限不足';
$lang->gitlab->notCompatible = '当前GitLab版本与禅道不兼容，请升级GitLab版本后重试';
$lang->gitlab->deleted       = '已删除';

$lang->gitlab->placeholder = new stdclass;
$lang->gitlab->placeholder->name        = '';
$lang->gitlab->placeholder->url         = "请填写GitLab Server首页的访问地址，如：https://gitlab.zentao.net。";
$lang->gitlab->placeholder->token       = "请填写具有root权限账户的access token";
$lang->gitlab->placeholder->projectPath = "项目标识串只能包含字母、数字、“_”、“-”和“.”。不能以“-”开头，以.git或者.atom结尾";

$lang->gitlab->noImportableIssues = "目前没有可供导入的issue。";
$lang->gitlab->tokenError         = "当前token非root权限。";
$lang->gitlab->tokenLimit         = "GitLab Token权限不足。请更换为有root权限的GitLab Token。";
$lang->gitlab->hostError          = "当前GitLab服务器地址无效或当前GitLab版本与禅道不兼容，请确认当前服务器可被访问或联系管理员升级GitLab至%s及以上版本后重试";
$lang->gitlab->bindUserError      = "不能重复绑定用户 %s";
$lang->gitlab->importIssueError   = "未选择该issue所属的执行。";
$lang->gitlab->importIssueWarn    = "存在导入失败的issue，可再次尝试导入。";

$lang->gitlab->accessLevels[10] = 'Guest';
$lang->gitlab->accessLevels[20] = 'Reporter';
$lang->gitlab->accessLevels[30] = 'Developer';
$lang->gitlab->accessLevels[40] = 'Maintainer';
$lang->gitlab->accessLevels[50] = 'Owner';

$lang->gitlab->apiError[] = 'internal is not allowed in a private group.';
$lang->gitlab->apiError[] = 'public is not allowed in a private group.';
$lang->gitlab->apiError[] = 'is too short (minimum is 8 characters)';
$lang->gitlab->apiError[] = "can contain only letters, digits, '_', '-' and '.'. Cannot start with '-', end in '.git' or end in '.atom'";
$lang->gitlab->apiError[] = 'Branch already exists';
$lang->gitlab->apiError[] = 'Failed to save group {:path=>["has already been taken"]}';
$lang->gitlab->apiError[] = 'Failed to save group {:path=>["已经被使用"]}';
$lang->gitlab->apiError[] = '403 Forbidden';
$lang->gitlab->apiError[] = 'is invalid';
$lang->gitlab->apiError[] = 'admin is a reserved name';
$lang->gitlab->apiError[] = 'has already been taken';
$lang->gitlab->apiError[] = 'Missing CI config file';
$lang->gitlab->apiError[] = 'is too big (should be at most 200.0 KB)';
$lang->gitlab->apiError[] = 'Reference not found';
$lang->gitlab->apiError[] = 'avatar is invalid';
$lang->gitlab->apiError[] = 'file format is not supported. Please try one of the following supported formats: image/png, image/jpeg, image/gif, image/bmp, image/tiff, image/vnd.microsoft.icon';
$lang->gitlab->apiError[] = 'must not contain commonly used combinations of words and letters';
$lang->gitlab->apiError[] = 'must be greater than or equal to 0';
$lang->gitlab->apiError[] = '500 Internal Server Error';

$lang->gitlab->errorLang[] = '私有分组的项目，可见性级别不能设为内部。';
$lang->gitlab->errorLang[] = '私有分组的项目，可见性级别不能设为公开。';
$lang->gitlab->errorLang[] = '密码太短（最少8个字符）';
$lang->gitlab->errorLang[] = "只能包含字母、数字、'.'-'和'.'。不能以'-'开头、以'.git'结尾或以'.atom'结尾。";
$lang->gitlab->errorLang[] = '分支名已存在。';
$lang->gitlab->errorLang[] = '保存失败，群组URL路径已经被使用。';
$lang->gitlab->errorLang[] = '保存失败，群组URL路径已经被使用。';
$lang->gitlab->errorLang[] = '当前用户绑定GitLab账号权限不足。';
$lang->gitlab->errorLang[] = '格式错误';
$lang->gitlab->errorLang[] = 'admin是保留名';
$lang->gitlab->errorLang[] = 'GitLab项目已存在';
$lang->gitlab->errorLang[] = '当前GitLab项目内没有可用的流水线，请先前往GitLab配置。';
$lang->gitlab->errorLang[] = '头像大小不能超过200KB';
$lang->gitlab->errorLang[] = '引用不存在';
$lang->gitlab->errorLang[] = '头像格式不正确';
$lang->gitlab->errorLang[] = '文件格式不支持，请尝试以下格式：image/png,image/jpeg,image/gif,image/bmp,image/tiff,image/vnd.microsoft.icon';
$lang->gitlab->errorLang[] = '不能使用常用的数字或字母组合';
$lang->gitlab->errorLang[] = '必须大于等于0';
$lang->gitlab->errorLang[] = '服务器返回错误：500';

$lang->gitlab->errorResonse['Email has already been taken']    = '邮箱已存在';
$lang->gitlab->errorResonse['Username has already been taken'] = '用户名已存在';

$lang->gitlab->project = new stdclass;
$lang->gitlab->project->id                         = "项目ID";
$lang->gitlab->project->name                       = "项目名称";
$lang->gitlab->project->create                     = "添加GitLab项目";
$lang->gitlab->project->edit                       = "编辑GitLab项目";
$lang->gitlab->project->url                        = "项目 URL";
$lang->gitlab->project->path                       = "项目标识串";
$lang->gitlab->project->description                = "项目描述";
$lang->gitlab->project->visibility                 = "可见性级别";
$lang->gitlab->project->visibilityList['private']  = "私有(项目访问必须明确授予每个用户。 如果此项目是在一个群组中，群组成员将会获得访问权限)";
$lang->gitlab->project->visibilityList['internal'] = "内部(除外部用户外，任何登录用户均可访问该项目)";
$lang->gitlab->project->visibilityList['public']   = "公开(该项目允许任何人访问)";
$lang->gitlab->project->star                       = "星标";
$lang->gitlab->project->fork                       = "派生";
$lang->gitlab->project->mergeRequests              = "合并请求";
$lang->gitlab->project->issues                     = "议题";
$lang->gitlab->project->tagList                    = "主题";
$lang->gitlab->project->tagListTips                = "用逗号分隔主题。";
$lang->gitlab->project->emptyNameError             = "项目名称不能为空";
$lang->gitlab->project->emptyPathError             = "项目标识串不能为空";
$lang->gitlab->project->confirmDelete              = '确认删除该GitLab项目吗？';
$lang->gitlab->project->notbindedError             = '还没绑定GitLab用户，无法修改权限！';
$lang->gitlab->project->publicTip                  = '当前项目的可见性级别将修改为公开，该项目可以在GitLab中没有任何身份验证的情况下被访问';

$lang->gitlab->user = new stdclass;
$lang->gitlab->user->id             = "用户ID";
$lang->gitlab->user->name           = "名称";
$lang->gitlab->user->username       = "用户名";
$lang->gitlab->user->email          = "邮箱";
$lang->gitlab->user->password       = "密码";
$lang->gitlab->user->passwordRepeat = "请重复密码";
$lang->gitlab->user->projectsLimit  = "项目限制";
$lang->gitlab->user->canCreateGroup = "可创建组";
$lang->gitlab->user->external       = "外部人员";
$lang->gitlab->user->externalTip    = "除非明确授予访问权限，否则外部用户无法查看内部或私有项目。另外，外部用户无法创建项目，群组或个人代码片段。";
$lang->gitlab->user->bind           = "禅道用户";
$lang->gitlab->user->avatar         = "头像";
$lang->gitlab->user->skype          = "Skype";
$lang->gitlab->user->linkedin       = "Linkedin";
$lang->gitlab->user->twitter        = "Twitter";
$lang->gitlab->user->websiteUrl     = "网站地址";
$lang->gitlab->user->note           = "备注";
$lang->gitlab->user->createOn       = "创建于";
$lang->gitlab->user->lastActivity   = "上次活动";
$lang->gitlab->user->create         = "添加GitLab用户";
$lang->gitlab->user->edit           = "编辑GitLab用户";
$lang->gitlab->user->emptyError     = "不能为空";
$lang->gitlab->user->passwordError  = "二次密码不一致！";
$lang->gitlab->user->bindError      = "该用户已经被绑定！";
$lang->gitlab->user->confirmDelete  = '确认删除该GitLab用户吗？';

$lang->gitlab->group = new stdclass;
$lang->gitlab->group->id                                      = "群组ID";
$lang->gitlab->group->name                                    = "群组名称";
$lang->gitlab->group->path                                    = "群组URL";
$lang->gitlab->group->pathTip                                 = "更改群组URL可能会有意想不到的副作用。";
$lang->gitlab->group->description                             = "群组描述";
$lang->gitlab->group->avatar                                  = "群组头像";
$lang->gitlab->group->avatarTip                               = '文件最大支持200k.';
$lang->gitlab->group->visibility                              = "可见性级别";
$lang->gitlab->group->visibilityList['private']               = "私有(群组及其项目只能由成员查看)";
$lang->gitlab->group->visibilityList['internal']              = "内部(除外部用户外，任何登录用户均可查看该组和任何内部项目)";
$lang->gitlab->group->visibilityList['public']                = "公开(群组和任何公共项目可以在没有任何身份验证的情况下查看)";
$lang->gitlab->group->permission                              = '许可';
$lang->gitlab->group->requestAccessEnabledTip                 = "允许用户请求访问(如果可见性是公开或内部的)";
$lang->gitlab->group->lfsEnabled                              = '大文件存储';
$lang->gitlab->group->lfsEnabledTip                           = "允许该组内的项目使用 Git LFS(可以在每个项目中覆盖此设置)";
$lang->gitlab->group->********************                    = "创建项目权限";
$lang->gitlab->group->********************List['noone']       = "禁止";
$lang->gitlab->group->********************List['maintainer']  = "维护者";
$lang->gitlab->group->********************List['developer']   = "开发者 + 维护者";
$lang->gitlab->group->subgroupCreationLevel                   = "创建子群组权限";
$lang->gitlab->group->subgroupCreationLevelList['owner']      = "所有者";
$lang->gitlab->group->subgroupCreationLevelList['maintainer'] = "维护者";
$lang->gitlab->group->create                                  = "添加群组";
$lang->gitlab->group->edit                                    = "编辑群组";
$lang->gitlab->group->createOn                                = "创建于";
$lang->gitlab->group->members                                 = "群组成员";
$lang->gitlab->group->confirmDelete                           = '确认删除该GitLab群组吗？';
$lang->gitlab->group->emptyError                              = "不能为空";
$lang->gitlab->group->manageMembers                           = '群组成员管理';
$lang->gitlab->group->memberName                              = '账号';
$lang->gitlab->group->memberAccessLevel                       = '角色权限';
$lang->gitlab->group->memberExpiresAt                         = '过期时间';
$lang->gitlab->group->repeatError                             = "群组成员不能重复添加";
$lang->gitlab->group->publicTip                               = '当前群组的可见性级别将修改为公开，该群组可以在GitLab中没有任何身份验证的情况下被访问';

$lang->gitlab->branch = new stdclass();
$lang->gitlab->branch->name                        = '分支名';
$lang->gitlab->branch->from                        = '创建自';
$lang->gitlab->branch->create                      = '创建';
$lang->gitlab->branch->lastCommitter               = '最后提交';
$lang->gitlab->branch->lastCommittedDate           = '最后修改时间';
$lang->gitlab->branch->accessLevel                 = "分支保护列表";
$lang->gitlab->branch->mergeAllowed                = "允许合并";
$lang->gitlab->branch->pushAllowed                 = "允许推送";
$lang->gitlab->branch->placeholderSearch           = "请输入分支名称";
$lang->gitlab->branch->placeholderSelect           = "请选择分支";
$lang->gitlab->branch->confirmDelete               = '确定删除分支保护？';
$lang->gitlab->branch->branchCreationLevelList[40] = "维护者";
$lang->gitlab->branch->branchCreationLevelList[30] = "开发者 + 维护者";
$lang->gitlab->branch->branchCreationLevelList[0]  = "禁止";
$lang->gitlab->branch->emptyPrivNameError          = "分支不能为空";
$lang->gitlab->branch->issetPrivNameError          = "已存在该保护分支";

$lang->gitlab->tag = new stdclass();
$lang->gitlab->tag->name               = '标签名';
$lang->gitlab->tag->ref                = '创建自';
$lang->gitlab->tag->lastCommitter      = '最后提交';
$lang->gitlab->tag->lastCommittedDate  = '最后修改时间';
$lang->gitlab->tag->placeholderSearch  = "请输入标签名称";
$lang->gitlab->tag->message            = '信息';
$lang->gitlab->tag->emptyNameError     = "标签名不能为空";
$lang->gitlab->tag->emptyRefError      = "创建自不能为空";
$lang->gitlab->tag->issetNameError     = "已存在该标签";
$lang->gitlab->tag->confirmDelete      = '确认删除该GitLab标签吗？';
$lang->gitlab->tag->protected          = '受保护';
$lang->gitlab->tag->accessLevel        = '允许创建';
$lang->gitlab->tag->protectConfirmDel  = '确认删除该GitLab标签保护吗？';
$lang->gitlab->tag->emptyPrivNameError = "标签不能为空";
$lang->gitlab->tag->issetPrivNameError = "已存在该保护标签";

$lang->gitlab->featureBar['binduser']['all']     = $lang->gitlab->all;
$lang->gitlab->featureBar['binduser']['notBind'] = $lang->gitlab->notBind;
$lang->gitlab->featureBar['binduser']['binded']  = $lang->gitlab->binded;
