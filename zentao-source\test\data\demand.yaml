title: table zt_demand
desc: "需求"
author: <PERSON><PERSON><PERSON> <PERSON>iu
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-1000
  - field: pool
    note: "需求池"
    range: 1-1000{4}
  - field: module
    note: "所属模块"
    range: 1821-10000
  - field: product
    note: "所属产品"
    range: 1-100{4}
  - field: parent
    note: "父需求ID"
    range: 0
  - field: pri
    note: "优先级"
    range: 1-4
  - field: category
    note: "预计工时"
    range: 1-20,20-1
  - field: source
    note: "需求来源"
    range: customer,user,po,market,service,operation,support,competitor,partner,dev,tester,bug,forum,other
  - field: sourceNote
    note: "来源备注"
    range: 1-10000
    prefix: "这里是需求来源备注"
  - field: title
    note: "需求名称"
    fields:
      - field: field1
        range: "需求池需求"
      - field: field2
        range: 1-10000
  - field: feedbackedBy
    note: "由谁反馈"
    range: ""
  - field: email
    note: "反馈邮箱"
    range: ""
  - field: assignedTo
    note: "指派给"
    fields:
      - field: assignedTo1
        range: admin,user,test,dev
      - field: assignedTo2
        range: "[],2-4"
  - field: assignedDate
    note: "指派日期"
    range: "(M)-(w)"
    type: timestamp
    format: "YY/MM/DD"
  - field: reviewedBy
    note: "评审者"
    fields:
      - field: reviewedBy1
        range: user,test,dev,admin
      - field: reviewedBy2
        range: 5-7,[]{10}
  - field: reviewedDate
    note: "创建日期"
    range: "(M)-(w)"
    type: timestamp
    postfix: ""
    format: "YY/MM/DD"
  - field: status
    note: "当前状态"
    range: draft,active,closed,changing,reviewing
  - field: duration
    range: ''
  - field: BSA
    range: ''
  - field: story
    range: 0
  - field: roadmap
    range: 0
  - field: createdBy
    note: "由谁创建"
    fields:
      - field: createdBy1
        range: admin,user,test,dev
      - field: createdBy2
        range: "[],2-4"
  - field: mailto
    range: ""
  - field: duplicateDemand
    range: 0
  - field: childDemands
    range: ""
  - field: version
    range: ""
  - field: vision
    range: ",rnd,or,"
  - field: color
    range: ""
  - field: changedBy
    note: "指派给"
    fields:
      - field: ""
        range: admin,user,test,dev
      - field: changedBy2
        range: "[],2-4"
  - field: changedDate
    note: "指派日期"
    range: "(M)-(w)"
    type: timestamp
    format: "YY/MM/DD"
  - field: closedBy
    note: "关闭者"
    fields:
      - field: closedBy1
        range: test,dev
      - field: closedBy2
        range: 1-10,1-10
  - field: changedDate
    note: "关闭日期"
    range: "(M)-(w)"
    type: timestamp
    postfix: ""
    format: "YY/MM/DD"
  - field: closedReason
    note: "关闭原因"
    range: done,subdivided,duplicate,postponed,willnotdo,cancel,bydesign
  - field: submitedBy
    note: "提交人"
    range: ''
  - field: lastEditedBy
    note: "最后修改者"
    range: ""
  - field: lastEditedDate
    note: "最后编辑日期"
    range: "(M)-(w)"
    type: timestamp
    postfix: ""
    format: "YY/MM/DD"
  - field: activatedDate
    note: "激活日期"
    range: "(M)-(w)"
    type: timestamp
    postfix: ""
    format: "YY/MM/DD"
  - field: deleted
    note: "是否删除"
    range: 0
  - field: distributedBy
    note: "由谁分发"
    range: ""
  - field: distributedDate
    note: "分发日期"
    range: "(M)-(w)"
    type: timestamp
    postfix: ""
    format: "YY/MM/DD"
