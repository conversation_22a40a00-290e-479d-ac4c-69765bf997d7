#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工单完整内容API
"""

import requests
import json

def test_ticket_content():
    """测试工单完整内容API"""
    try:
        # 先获取一个工单ID
        projects_url = "http://localhost:8000/api/ticket-integration/projects"
        headers = {"Authorization": "Bearer test_token"}
        
        print("🔍 获取项目列表...")
        projects_response = requests.get(projects_url, headers=headers, timeout=10)
        
        if projects_response.status_code != 200:
            print(f"❌ 获取项目列表失败: {projects_response.status_code}")
            return
            
        projects_data = projects_response.json()
        if not projects_data.get('success') or not projects_data.get('data'):
            print("❌ 项目列表为空")
            return
            
        # 获取第一个项目的工单
        project_id = projects_data['data'][0]['feelec_project_id']
        print(f"📋 测试项目ID: {project_id}")
        
        tickets_url = f"http://localhost:8000/api/ticket-integration/projects/{project_id}/tickets"
        tickets_response = requests.get(tickets_url, headers=headers, timeout=10)
        
        if tickets_response.status_code != 200:
            print(f"❌ 获取工单列表失败: {tickets_response.status_code}")
            return
            
        tickets_data = tickets_response.json()
        if not tickets_data.get('success') or not tickets_data.get('data') or not tickets_data['data'].get('tickets'):
            print("❌ 工单列表为空")
            return
            
        # 获取第一个工单的完整内容
        ticket_id = tickets_data['data']['tickets'][0]['feelec_ticket_id']
        print(f"🎫 测试工单ID: {ticket_id}")
        
        content_url = f"http://localhost:8000/api/ticket-integration/tickets/{ticket_id}/full-content"
        content_response = requests.get(content_url, headers=headers, timeout=10)
        
        if content_response.status_code == 200:
            data = content_response.json()
            if data.get('success'):
                ticket = data['data']
                print("✅ 工单完整内容获取成功")
                print(f"📋 工单信息:")
                print(f"   编号: {ticket.get('feelec_ticket_no')}")
                print(f"   标题: {ticket.get('feelec_title')}")
                print(f"   发布人: {ticket.get('publisher_name')} (ID: {ticket.get('feelec_publisher_id')})")
                print(f"   处理人: {ticket.get('processor_name')} (ID: {ticket.get('feelec_processor_id')})")
                print(f"   所属部门: {ticket.get('department_name')} (ID: {ticket.get('feelec_department_id')})")
                print(f"   主体公司: {ticket.get('company_name')} (ID: {ticket.get('feelec_company_id')})")
                print(f"   项目名称: {ticket.get('project_name')}")
                print(f"   工单模板: {ticket.get('template_name')}")
                print(f"   优先级: {ticket.get('priority_text')}")
                print(f"   状态: {ticket.get('status_name')}")
                print(f"   来源: {ticket.get('source_text')}")
                print(f"   是否完成: {ticket.get('is_completed')}")
                print(f"   是否逾期: {ticket.get('is_overdue')}")
                
                # 检查处理记录
                if ticket.get('process_records'):
                    print(f"\n📝 处理记录 ({len(ticket['process_records'])} 条):")
                    for i, record in enumerate(ticket['process_records'][:3], 1):  # 只显示前3条
                        print(f"   {i}. {record.get('processor_name')} - {record.get('action_name')} ({record.get('create_time_formatted')})")
                
                # 检查发布人信息
                if ticket.get('publisher_info'):
                    pub_info = ticket['publisher_info']
                    print(f"\n👤 发布人详细信息:")
                    print(f"   姓名: {pub_info.get('feelec_name')}")
                    print(f"   电话: {pub_info.get('feelec_mobile')}")
                    print(f"   邮箱: {pub_info.get('feelec_email')}")
                    if pub_info.get('company_info'):
                        print(f"   公司: {pub_info['company_info'].get('company_name')}")
                
            else:
                print(f"❌ API返回错误: {data.get('message')}")
        else:
            print(f"❌ 工单内容获取失败: {content_response.status_code}")
            print(f"错误信息: {content_response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_ticket_content()
