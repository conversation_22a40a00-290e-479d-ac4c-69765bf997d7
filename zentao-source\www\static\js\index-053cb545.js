import{d as n,c,w as o,r,o as l,z as t,e as d,j as s,p as i}from"./index.js";const u={class:"go-header-box"},p={class:"header-item left"},h={class:"header-item center"},v={class:"header-item right"},m=n({__name:"index",setup(f){return(e,g)=>{const a=r("n-space"),_=r("n-layout-header");return l(),c(_,{bordered:"",class:"go-header"},{default:o(()=>[t("header",u,[t("div",p,[d(a,null,{default:o(()=>[s(e.$slots,"left",{},void 0,!0)]),_:3})]),t("div",h,[s(e.$slots,"center",{},void 0,!0)]),t("div",v,[d(a,null,{default:o(()=>[s(e.$slots,"ri-left",{},void 0,!0),s(e.$slots,"ri-right",{},void 0,!0)]),_:3})])])]),_:3})}}});var x=i(m,[["__scopeId","data-v-351d7c0e"]]);export{x as L};
