#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的公司列管理功能测试
"""

import requests
import random
import time

BASE_URL = 'http://127.0.0.1:8001/api/v1/supervision'

def test_complete_flow():
    """测试完整的公司列管理流程"""
    print("🚀 开始完整的公司列管理功能测试...")
    
    # 1. 获取初始公司列表
    print("\n1. 获取初始公司列表...")
    initial_companies = get_companies()
    if initial_companies is None:
        print("❌ 无法获取初始公司列表，测试终止")
        return
    
    print(f"✅ 初始公司数量: {len(initial_companies)}")
    
    # 2. 添加新公司
    test_company = f"测试公司{random.randint(1000, 9999)}"
    print(f"\n2. 添加新公司: {test_company}")
    
    if add_company(test_company):
        print(f"✅ 成功添加公司: {test_company}")
    else:
        print(f"❌ 添加公司失败")
        return
    
    # 3. 验证公司已添加
    print("\n3. 验证公司已添加...")
    updated_companies = get_companies()
    if updated_companies and test_company in [c['company_name'] for c in updated_companies]:
        print(f"✅ 验证成功，公司已添加")
        print(f"当前公司数量: {len(updated_companies)}")
    else:
        print(f"❌ 验证失败，公司未找到")
        return
    
    # 4. 检查数据库结构
    print("\n4. 检查数据库结构...")
    if check_database_structure(test_company):
        print(f"✅ 数据库结构正确，包含 {test_company}_status 字段")
    else:
        print(f"❌ 数据库结构检查失败")
    
    # 5. 等待一下，然后删除测试公司
    print(f"\n5. 等待3秒后删除测试公司...")
    time.sleep(3)
    
    if delete_company(test_company):
        print(f"✅ 成功删除公司: {test_company}")
    else:
        print(f"❌ 删除公司失败")
        return
    
    # 6. 验证公司已删除
    print("\n6. 验证公司已删除...")
    final_companies = get_companies()
    if final_companies and test_company not in [c['company_name'] for c in final_companies]:
        print(f"✅ 验证成功，公司已删除")
        print(f"最终公司数量: {len(final_companies)}")
    else:
        print(f"❌ 验证失败，公司仍然存在")
    
    print("\n🎉 完整测试流程完成！")

def get_companies():
    """获取公司列表"""
    try:
        response = requests.get(f'{BASE_URL}/companies')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                return result.get('data', [])
    except Exception as e:
        print(f"获取公司列表异常: {e}")
    return None

def add_company(company_name):
    """添加公司"""
    try:
        response = requests.post(f'{BASE_URL}/companies', json={'company_name': company_name})
        if response.status_code == 200:
            result = response.json()
            return result.get('success', False)
    except Exception as e:
        print(f"添加公司异常: {e}")
    return False

def delete_company(company_name):
    """删除公司"""
    try:
        response = requests.delete(f'{BASE_URL}/companies/{company_name}')
        if response.status_code == 200:
            result = response.json()
            return result.get('success', False)
    except Exception as e:
        print(f"删除公司异常: {e}")
    return False

def check_database_structure(company_name):
    """检查数据库结构"""
    try:
        response = requests.get(f'{BASE_URL}/items')
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and result.get('data'):
                first_item = result['data'][0]
                field_name = f'{company_name}_status'
                return field_name in first_item
    except Exception as e:
        print(f"检查数据库结构异常: {e}")
    return False

if __name__ == "__main__":
    test_complete_flow()
