#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完成数据库重设计的最后步骤
替换旧表，创建视图和触发器
"""

import pymysql
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

# 数据库配置
DB_CONFIG = {
    'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'cyh',
    'password': 'Qq188788',
    'database': 'kanban2',
    'charset': 'utf8mb4'
}

def finalize_database_redesign():
    """完成数据库重设计"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        logging.info("🔧 完成数据库重设计最后步骤")
        logging.info("=" * 80)
        
        # 1. 替换旧表
        logging.info("步骤1: 替换旧表")
        logging.info("-" * 40)
        
        # 重命名旧表
        cursor.execute("RENAME TABLE supervision_items TO supervision_items_old_20250731")
        logging.info("✅ 重命名旧督办事项表")
        
        # 重命名新表
        cursor.execute("RENAME TABLE supervision_items_new TO supervision_items")
        logging.info("✅ 新督办事项表生效")
        
        # 2. 创建动态进度计算视图
        logging.info("\n步骤2: 创建动态进度计算视图")
        logging.info("-" * 40)
        
        create_view_sql = """
        CREATE VIEW v_supervision_items_with_progress AS
        SELECT 
            si.*,
            -- 动态计算整体进度状态
            (SELECT 
                CASE 
                    WHEN COUNT(CASE WHEN csp.status = '！' THEN 1 END) > 0 THEN '！'
                    WHEN COUNT(CASE WHEN csp.status = '√' THEN 1 END) = COUNT(CASE WHEN csp.status != '—' THEN 1 END) 
                         AND COUNT(CASE WHEN csp.status != '—' THEN 1 END) > 0 THEN '√'
                    WHEN COUNT(CASE WHEN csp.status = 'O' THEN 1 END) > 0 THEN 'O'
                    ELSE 'X'
                END
             FROM company_supervision_progress csp 
             JOIN companies c ON csp.company_id = c.id 
             WHERE csp.supervision_item_id = si.id AND c.is_active = 1
            ) AS overall_progress,
            
            -- 动态计算完成率
            (SELECT 
                CASE 
                    WHEN COUNT(CASE WHEN csp.status != '—' THEN 1 END) = 0 THEN 100.0
                    ELSE ROUND(
                        COUNT(CASE WHEN csp.status = '√' THEN 1 END) * 100.0 / 
                        COUNT(CASE WHEN csp.status != '—' THEN 1 END), 1
                    )
                END
             FROM company_supervision_progress csp 
             JOIN companies c ON csp.company_id = c.id 
             WHERE csp.supervision_item_id = si.id AND c.is_active = 1
            ) AS completion_rate,
            
            -- 动态判断是否逾期
            (SELECT 
                CASE 
                    WHEN si.completion_deadline IS NOT NULL AND CURDATE() > si.completion_deadline THEN 1
                    ELSE 0
                END
            ) AS is_overdue

        FROM supervision_items si
        WHERE si.deleted_at IS NULL
        """
        
        cursor.execute("DROP VIEW IF EXISTS v_supervision_items_with_progress")
        cursor.execute(create_view_sql)
        logging.info("✅ 创建动态进度计算视图")
        
        # 3. 创建触发器确保数据完整性
        logging.info("\n步骤3: 创建触发器")
        logging.info("-" * 40)
        
        # 删除旧触发器
        cursor.execute("DROP TRIGGER IF EXISTS tr_supervision_items_insert_new")
        cursor.execute("DROP TRIGGER IF EXISTS tr_companies_insert_new")
        
        # 创建新督办事项时的触发器
        trigger1_sql = """
        CREATE TRIGGER tr_supervision_items_insert_new
        AFTER INSERT ON supervision_items
        FOR EACH ROW
        BEGIN
            INSERT INTO company_supervision_progress (supervision_item_id, company_id, status, updated_by)
            SELECT NEW.id, c.id, 'X', 'system'
            FROM companies c 
            WHERE c.is_active = 1;
        END
        """
        
        cursor.execute(trigger1_sql)
        logging.info("✅ 创建督办事项插入触发器")
        
        # 创建新公司时的触发器
        trigger2_sql = """
        CREATE TRIGGER tr_companies_insert_new
        AFTER INSERT ON companies
        FOR EACH ROW
        BEGIN
            IF NEW.is_active = 1 THEN
                INSERT INTO company_supervision_progress (supervision_item_id, company_id, status, updated_by)
                SELECT si.id, NEW.id, 'X', 'system'
                FROM supervision_items si 
                WHERE si.deleted_at IS NULL;
            END IF;
        END
        """
        
        cursor.execute(trigger2_sql)
        logging.info("✅ 创建公司插入触发器")
        
        # 4. 创建优化索引
        logging.info("\n步骤4: 创建优化索引")
        logging.info("-" * 40)
        
        cursor.execute("CREATE INDEX idx_csp_item_company_status ON company_supervision_progress (supervision_item_id, company_id, status)")
        cursor.execute("CREATE INDEX idx_csp_status_active ON company_supervision_progress (status, supervision_item_id)")
        logging.info("✅ 创建优化索引")
        
        # 5. 数据验证
        logging.info("\n步骤5: 数据验证")
        logging.info("-" * 40)
        
        cursor.execute("SELECT COUNT(*) FROM supervision_items WHERE deleted_at IS NULL")
        items_count = cursor.fetchone()[0]
        logging.info(f"✅ 督办事项数量: {items_count}")
        
        cursor.execute("SELECT COUNT(*) FROM companies WHERE is_active = 1")
        companies_count = cursor.fetchone()[0]
        logging.info(f"✅ 活跃公司数量: {companies_count}")
        
        cursor.execute("SELECT COUNT(*) FROM company_supervision_progress")
        progress_count = cursor.fetchone()[0]
        logging.info(f"✅ 进度记录数量: {progress_count}")
        
        expected_count = items_count * companies_count
        logging.info(f"✅ 期望记录数量: {expected_count}")
        
        if progress_count == expected_count:
            logging.info("✅ 数据完整性验证通过")
        else:
            logging.warning(f"⚠️ 数据不完整: 实际{progress_count}, 期望{expected_count}")
        
        # 6. 测试动态进度计算
        logging.info("\n步骤6: 测试动态进度计算")
        logging.info("-" * 40)
        
        cursor.execute("""
        SELECT 
            sequence_number,
            work_theme,
            overall_progress,
            completion_rate,
            is_overdue
        FROM v_supervision_items_with_progress
        ORDER BY sequence_number
        LIMIT 5
        """)
        
        test_results = cursor.fetchall()
        for row in test_results:
            logging.info(f"✅ 督办{row[0]}: {row[1][:20]}... -> {row[2]} ({row[3]}%)")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        logging.info("=" * 80)
        logging.info("🎯 数据库重设计完全完成！")
        return True
        
    except Exception as e:
        logging.error(f"❌ 完成数据库重设计失败: {e}")
        return False

def main():
    """主函数"""
    logging.info("🚀 开始完成数据库重设计")
    
    if finalize_database_redesign():
        logging.info("🎯 数据库重设计全部完成！")
    else:
        logging.error("❌ 数据库重设计失败")

if __name__ == "__main__":
    main()
