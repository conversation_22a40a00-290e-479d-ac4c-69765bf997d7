title: project.
desc: project data.
author: <PERSON>
version: "1.0"
fields:
  - field: id
    range: 1-100
  - field: name
    note: "名称"
    fields:
    - field: name1
      range: 敏捷项目,瀑布项目,看板项目
    - field: name2
      range: 1-100
  - field: project
    range: 0
  - field: code
    prefix: "code"
    range: 1-1000
  - field: model
    range: scrum,waterfall,kanban
  - field: type
    range: project
  - field: budget
    range: 800000-1:100
  - field: status
    range: doing,wait,suspended,closed
  - field: auth
    range: "extend"
  - field: desc
    range: 1-10000
    prefix: "迭代描述"
  - field: begin
    range: "(-2M)-(+M):1D"
    type: timestamp
    format: "YYYY-MM-DD"
    postfix: "\t"
  - field: end
    range: "(+1w)-(+2M):1D"
    type: timestamp
    format: "YYYY-MM-DD"
    postfix: "\t"
  - field: grade
    range: 1
  - field: parent
    range: 0
  - field: path
    prefix: ","
    range: 1-100
    postfix: ","
  - field: hasProduct
    range: 0,1
  - field: multiple
    range: 1{10},0{10}
  - field: storyType
    range: "`story,requirement,epic`"
