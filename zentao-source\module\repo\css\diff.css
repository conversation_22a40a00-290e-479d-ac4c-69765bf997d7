body {padding-bottom: 0px;}
.w-code {width: 48%; word-break: break-all;}
td.code {color: #484848; padding: 0px 3px; white-space: pre-wrap;}
.none  {background: #EAF2F5;}
table.diff {margin-bottom: 0px;}
.diff caption {border: 1px solid #e4e4e4; background: #edf3fe; margin: 0; padding: 6px 2px 6px 10px; text-align: left; font-weight: bold; font-size: 13px;}
.diff th, .diff td {border: none;}
.diff th {padding-top: 2px; padding-bottom: 2px;}
.diff .line-new, .diff .line-new {background: #CFC;}
.diff .line-old, .diff .line-old {background: #FCC;}
.diff .line-all, .diff .line-all {background: #FFF;}
.diff .w-num {width: 25px; border-right: 1px solid #E4E4E4; background: #fafafa; border-left: 1px solid #E4E4E4; color: #999; font-weight: normal;}

.repoCode .diff tr .comment-btn .icon-wrapper {left: -30px;}
.repoCode .diff tr.over td.line-all, .repoCode .diff tr.over td.line-all {background: #f8eec7;}
.repoCode .diff tr.over td.line-new, .repoCode .diff tr.over td.line-new {background: #8eff8e;}
.repoCode .diff tr.over td.line-old, .repoCode .diff tr.over td.line-old {background: #f6b2b2;}

.repoCode {min-height: 500px;}
.repoCode form > .btn {margin-right: 10px;}
.label-exchange {background-color: #566F7C; cursor: pointer;}
.label-exchange i {padding: 0;}
.btn-download {border-right: none;}
.body-modal .repo-diff {display: none;}
.body-modal #mainMenu {padding-bottom: 40px;}

.repoCode td.code {white-space: inherit;}
.repoCode .nav-tabs > li.active > a:before {background: none; height: 0;}

#fileTabs .tab-pane {display: none;}
#fileTabs .tab-pane.active {display: block;}
#fileTabs .tab-nav-item {max-width: none !important; white-space: nowrap;}
#fileTabs .tab-nav-item > .tab-nav-link > .close {float: none; position: inherit; right: -1px; top: 2px;}
#filesTree #modules {margin-top: 5px;}
#filesTree {overflow-y: auto;}

.file-tree {padding: 0;}
#sidebar > .sidebar-toggle {left: auto; right: 3px;}
#sidebar > .sidebar-toggle > .icon {left : -1px;}
.hide-sidebar #sidebar {width: 0 !important;}
