<?php
/**
 * 按系统统计主机总数。
 * Count of host.
 *
 * 范围：system
 * 对象：host
 * 目的：scale
 * 度量名称：按系统统计主机总数
 * 单位：个
 * 描述：按系统统计的主机总数是指在禅道中的全部主机总数
 * 定义：所有主机的个数求和
 *
 * @copyright Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.zentao.net)
 * <AUTHOR> <<EMAIL>>
 * @package
 * @uses      func
 * @license   ZPL(https://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * @Link      https://www.zentao.net
 */
class count_of_host extends baseCalc
{
    public $dataset = 'getHosts';

    public $fieldList = array('id', 'type');

    public $result = 0;

    public function calculate($row)
    {
        if($row->type == 'normal') $this->result += 1;
    }

    public function getResult($options = array())
    {
        $records = $this->getRecords(array('value'));
        return $this->filterByOptions($records, $options);
    }
}
