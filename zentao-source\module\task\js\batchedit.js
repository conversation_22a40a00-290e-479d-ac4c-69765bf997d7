$(document).ready(removeDitto());//Remove 'ditto' in first row.

$(document).on('click', '.chosen-with-drop', function(){oldValue = $(this).prev('select').val();})//Save old value.

/* Set ditto value. */
$(document).on('change', 'select', function()
{
    if($(this).val() == 'ditto')
    {
        var index = $(this).closest('td').index();
        var row   = $(this).closest('tr').index();
        var tbody = $(this).closest('tr').parent();

        var value = '';
        for(var i = row - 1; i >= 0; i--)
        {
            value = tbody.children('tr').eq(i).find('td').eq(index).find('select').val();
            if(value != 'ditto') break;
        }

        isModules     = $(this).attr('name').indexOf('modules')     != -1;
        isAssignedTos = $(this).attr('name').indexOf('assignedTos') != -1;
        isFinishedBys = $(this).attr('name').indexOf('finishedBys') != -1;
        isClosedBys   = $(this).attr('name').indexOf('closedBys')   != -1;

        if(isModules || isAssignedTos || isFinishedBys || isClosedBys)
        {
            var valueStr = ',' + $(this).find('option').map(function(){return $(this).val();}).get().join(',') + ',';
            if(valueStr.indexOf(',' + value + ',') != -1)
            {
                $(this).val(value);
            }
            else
            {
                alert(dittoNotice);
                $(this).val(oldValue);
            }
        }
        else
        {
            $(this).val(value);
        }

        $(this).trigger("chosen:updated");
        $(this).trigger("change");
    }
});

$(function()
{
    $('#subNavbar li[data-id="task"]').addClass('active');

    $('#customField').click(function()
    {
        hiddenRequireFields();
    });
});
