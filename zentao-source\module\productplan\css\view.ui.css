#mainContent .detail-body .section-list .tabs ul {border-bottom-width: 1px; border-bottom-style: solid;}
#mainContent .detail-body .section-list .tab-content .table-data caption {padding-top: 16px;}
#planInfo table caption {font-size: 1rem; font-weight: bold;}
#planInfo .history {padding: 16px 0px;}
#mainContent .entity-label {flex: initial !important;}
#mainContent .tab-actions {z-index: 10;}
#planInfo .extendBox > .panel-heading {padding-left: 0; font-size: 1rem;}
