#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试工单集成功能 - 绕过认证
直接调用后端API函数进行测试
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'pmo-backend'))

import asyncio
from app.api.endpoints.ticket_integration import (
    get_dashboard_stats,
    get_ticket_projects,
    get_project_tickets,
    get_ticket_full_content
)

class DirectTicketTester:
    """直接工单测试器"""
    
    def __init__(self):
        self.mock_user = {"user_id": "test", "username": "test"}
    
    async def test_dashboard_stats(self):
        """测试仪表盘统计"""
        print("\n🎯 测试仪表盘统计...")
        
        try:
            result = await get_dashboard_stats(current_user=self.mock_user)
            
            if hasattr(result, 'status_code') and result.status_code == 200:
                print(f"   ✅ 仪表盘统计获取成功")
                return True
            elif isinstance(result, dict) and result.get('success'):
                print(f"   ✅ 仪表盘统计获取成功")
                data = result.get('data', {})
                print(f"      📊 项目统计: {data.get('project_stats', {})}")
                print(f"      📊 工单统计: {data.get('ticket_stats', {})}")
                return True
            else:
                print(f"   ❌ 获取失败: {result}")
                return False
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            return False
    
    async def test_projects_list(self):
        """测试项目列表"""
        print("\n🎯 测试项目列表...")
        
        try:
            result = await get_ticket_projects(limit=10, current_user=self.mock_user)
            
            if isinstance(result, dict) and result.get('success'):
                print(f"   ✅ 项目列表获取成功")
                data = result.get('data', {})
                projects = data.get('projects', []) if isinstance(data, dict) else data
                print(f"      📋 找到 {len(projects)} 个项目")

                if projects:
                    # 显示前3个项目
                    for i, project in enumerate(projects[:3], 1):
                        print(f"        {i}. {project.get('feelec_name')} (ID: {project.get('feelec_project_id')})")

                    return projects[0]['feelec_project_id'] if projects else None
                else:
                    print(f"      ⚠️  没有找到项目")
                    return None
            else:
                print(f"   ❌ 获取失败: {result}")
                return None
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            return None
    
    async def test_project_tickets(self, project_id):
        """测试项目工单列表"""
        print(f"\n🎯 测试项目 {project_id} 的工单列表...")
        
        try:
            result = await get_project_tickets(project_id=project_id, current_user=self.mock_user)
            
            if isinstance(result, dict) and result.get('success'):
                print(f"   ✅ 项目工单列表获取成功")
                data = result.get('data', {})
                tickets = data.get('tickets', []) if isinstance(data, dict) else data
                print(f"      📋 找到 {len(tickets)} 个工单")

                if tickets:
                    # 显示前3个工单
                    for i, ticket in enumerate(tickets[:3], 1):
                        print(f"        {i}. {ticket.get('feelec_title')} (ID: {ticket.get('feelec_ticket_id')})")

                    return tickets[0]['feelec_ticket_id'] if tickets else None
                else:
                    print(f"      ⚠️  该项目没有工单")
                    return None
            else:
                print(f"   ❌ 获取失败: {result}")
                return None
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            return None
    
    async def test_ticket_full_content(self, ticket_id):
        """测试工单完整内容"""
        print(f"\n🎯 测试工单 {ticket_id} 的完整内容...")
        
        try:
            result = await get_ticket_full_content(ticket_id=ticket_id, current_user=self.mock_user)
            
            if isinstance(result, dict) and result.get('success'):
                print(f"   ✅ 工单完整内容获取成功")
                data = result.get('data', {})
                
                # 显示基本信息
                print(f"      📋 工单基本信息:")
                print(f"        编号: {data.get('feelec_ticket_no')}")
                print(f"        标题: {data.get('feelec_title')}")
                print(f"        状态: {data.get('status_name')}")
                print(f"        优先级: {data.get('priority_text')}")
                
                # 显示发布人信息
                if data.get('publisher_info'):
                    publisher = data['publisher_info']
                    print(f"      👤 发布人信息:")
                    print(f"        姓名: {publisher.get('feelec_name')}")
                    print(f"        电话: {publisher.get('feelec_mobile', '无')}")
                    print(f"        邮箱: {publisher.get('feelec_email', '无')}")
                    
                    if publisher.get('company_info'):
                        company = publisher['company_info']
                        print(f"        公司: {company.get('company_name')}")
                
                # 显示工单内容
                if data.get('ticket_content'):
                    content = data['ticket_content']
                    print(f"      📄 工单详细内容:")
                    
                    if content.get('title'):
                        print(f"        标题: {content['title']}")
                    
                    if content.get('content'):
                        content_text = content['content']
                        # 移除HTML标签显示纯文本
                        import re
                        clean_content = re.sub(r'<[^>]+>', '', content_text)
                        if len(clean_content) > 100:
                            clean_content = clean_content[:100] + "..."
                        print(f"        内容: {clean_content}")
                    
                    # 显示其他字段
                    for key, value in content.items():
                        if key not in ['title', 'content'] and value:
                            print(f"        {key}: {value}")
                
                # 显示处理记录
                if data.get('process_records'):
                    records = data['process_records']
                    print(f"      📊 处理记录: {len(records)} 条")
                
                return True
            else:
                print(f"   ❌ 获取失败: {result}")
                return False
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            return False
    
    async def run_full_test(self):
        """运行完整测试"""
        print("🎫 工单集成功能直接测试")
        print("=" * 60)
        
        results = {
            'dashboard_stats': False,
            'projects_list': False,
            'project_tickets': False,
            'ticket_full_content': False
        }
        
        # 1. 测试仪表盘统计
        results['dashboard_stats'] = await self.test_dashboard_stats()
        
        # 2. 测试项目列表
        project_id = await self.test_projects_list()
        if project_id:
            results['projects_list'] = True
            
            # 3. 测试项目工单列表
            ticket_id = await self.test_project_tickets(project_id)
            if ticket_id:
                results['project_tickets'] = True
                
                # 4. 测试工单完整内容
                results['ticket_full_content'] = await self.test_ticket_full_content(ticket_id)
        
        # 显示测试结果
        print(f"\n🎉 测试完成！")
        print("=" * 60)
        print("📊 测试结果:")
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        success_count = sum(results.values())
        total_count = len(results)
        success_rate = (success_count / total_count) * 100
        
        print(f"\n总体成功率: {success_rate:.1f}% ({success_count}/{total_count})")
        
        if success_rate == 100:
            print("🎉 所有测试通过！工单集成功能完全可用！")
        elif success_rate >= 75:
            print("⚠️  大部分功能正常，有少量问题需要修复")
        else:
            print("❌ 存在较多问题，需要进一步调试")
        
        return results

async def main():
    """主函数"""
    print("🎫 工单集成功能直接测试")
    print("=" * 60)
    print("⚠️  直接调用后端函数，绕过HTTP认证")
    print("=" * 60)
    
    tester = DirectTicketTester()
    results = await tester.run_full_test()
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
