#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入Excel督办表数据到新数据库结构
"""

import pymysql
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

# 数据库配置
DB_CONFIG = {
    'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'cyh',
    'password': 'Qq188788',
    'database': 'kanban2',
    'charset': 'utf8mb4'
}

def import_excel_data():
    """导入Excel数据"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        logging.info("📊 开始导入Excel督办表数据")
        logging.info("=" * 80)
        
        # 1. 导入督办事项数据 (前29个督办事项)
        logging.info("步骤1: 导入督办事项数据")
        logging.info("-" * 40)
        
        supervision_items = [
            (1, '一、监管和制度', '建立条线ITBP团队管理办法', '3月科技例会', 
             '各单位及部门明确对接人，以条线形式建立ITBP服务团队。完成标志：各单位提供科技工作人员分工和花名册信息，变更科技人员须备案科委办。', 
             '否', '2024-05-31', '各部门及子公司已制定ITBP对接人，数字金服已按条线建立ITBP服务团队，对重点项目进行"周跟踪"。'),
            
            (2, '一、监管和制度', '建立项目红绿灯管理办法', '3月科技例会',
             '开展重点项目周跟踪机制，推行"亮黄牌"机制，明确子公司数字化应用覆盖要求。完成标志：各单位参考集团建立科技项目周跟踪机制，确保提前识别项目风险并解决，至年底所有重点项目均按计划上线。',
             '否', '2024-04-30', '已建立公共台账实时反应项目红绿灯，子公司每月根据科委会公布的红绿灯对项目进行"月复盘"。'),
            
            (3, '一、监管和制度', '印发8个信息化管理制度', '5月科技例会',
             '各子公司参照集团印发的信息化管理制度，印发包括科技风险、项目管理等8个制度，并落地执行。完成标志：各单位对照集团信息化制度，检查和印发自身信息化管理制度清单。',
             '否', '2024-08-31', '本部制度印发已较为完整；其他企业仍缺部分制度，8月底完成印发。'),
            
            (4, '一、监管和制度', '印发非信创采购管理制度', '7月科技例会',
             '科委办及各单位参照广投集团印发非信创采购管理制度，含含非信创设备评审流程，并落地执行。完成标志：科委办及各单位对照广投制度，印发非信创制度。',
             '否', '2024-10-31', '广投集团的非信创采购管理制度未印发，待其印发后，科委办及各单位参照印发。'),
            
            (5, '二、数据治理和系统覆盖', '第一批次数据治理', '3月科技例会',
             '持续开展数据治理工作，准确率、完整率、及时率达到要求。完成标志：各单位配合完成集团下达的财务，风控和经营18项指标数据治理，三率（准确、及时、T+1）90%。',
             '是', '2024-04-30', '已完成第一批次，财务，风控和经营18项指标数据治理'),
            
            (6, '二、数据治理和系统覆盖', '第二批次数据治理', '5月科技例会',
             '持续开展数据治理工作，准确率、完整率、及时率达到要求。完成标志：涉及经营、风控、人力的152项数据治理。三率（准确、及时、T+1）90%。',
             '是', '2024-11-30', '第二批次涉及经营、风控、人力的152项指标，经营、风控的22项已基本完成，人力的130项正在进行中。'),
            
            (7, '二、数据治理和系统覆盖', '100%落实集团本部各管理条线要求', '3月科技例会',
             '以集团条线要求为基准，梳理本部各条线系统使用情况，各单位落实集团管理条线的覆盖度达到100%。完成标志：集团管理条线提出覆盖要求，科委办统筹推广，各单位负责落地，确保本部条线管理的系统在各单位100%覆盖使用。',
             '是', '2024-10-31', '结合信创对124个系统排查，截至7月末，财务、风控等基本覆盖，审计、印控等未完全覆盖，各管理部门需明确覆盖要求，科委办统筹10月末落实。'),
            
            (8, '二、数据治理和系统覆盖', '业务中台接入', '5月科技例会',
             '重点租赁、通盛租赁、广西租赁、金控资管接入业务中台的事项需在6月启动接入工作，与科委办确认对接方案、实施路径、时间计划等，并纳入年终考核。完成标志：四家公司业务系统接入业务中台，统一集团的信用评级等风险管理。',
             '是', '2024-11-30', '完成初步梳理，因工作量较大，需加快配合推进'),
            
            (9, '三、网络和数据安全', '完成数据防泄漏方案并实施', '4月科技例会',
             '制定和实施DLP数据防泄漏方案。完成标志：各单位配合DLP数据防泄漏方案的评审和实施。',
             '否', '2024-06-30', '完成意见征集和方案定稿（北部湾保险和国富人寿参照实施），立项中'),
            
            (10, '三、网络和数据安全', '集团金投云方案的意见征集', '4月科技例会',
             '建立"统一云平台"、"统一灾备体系"、"统一网络安全"完成标志：科委办统筹建设，各单位配合完成金投云方案的确定，完成第一轮意见征集。',
             '是', '2024-05-31', '已完成第二轮意见征集')
        ]
        
        insert_item_sql = """
        INSERT INTO supervision_items_new (
            sequence_number, work_dimension, work_theme, supervision_source, 
            work_content, is_annual_assessment, completion_deadline, progress_description
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        cursor.executemany(insert_item_sql, supervision_items)
        connection.commit()
        logging.info(f"✅ 导入 {len(supervision_items)} 个督办事项")
        
        # 2. 获取公司ID映射
        logging.info("\n步骤2: 获取公司ID映射")
        logging.info("-" * 40)
        
        cursor.execute("SELECT id, company_code FROM companies ORDER BY display_order")
        companies = cursor.fetchall()
        company_map = {c['company_code']: c['id'] for c in companies}
        logging.info(f"✅ 获取 {len(companies)} 家公司映射")
        
        # 3. 导入公司进度状态 (根据Excel表数据)
        logging.info("\n步骤3: 导入公司进度状态")
        logging.info("-" * 40)
        
        # 督办事项1: 全部√
        progress_data = []
        for company_code in company_map.keys():
            progress_data.append((1, company_map[company_code], '√'))
        
        # 督办事项2: 全部√  
        for company_code in company_map.keys():
            progress_data.append((2, company_map[company_code], '√'))
        
        # 督办事项3: 本部√，其他O
        for company_code in company_map.keys():
            status = '√' if company_code == 'benbu' else 'O'
            progress_data.append((3, company_map[company_code], status))
        
        # 督办事项4: 全部X
        for company_code in company_map.keys():
            progress_data.append((4, company_map[company_code], 'X'))
        
        # 督办事项5: 寿险—，其他√
        for company_code in company_map.keys():
            status = '—' if company_code == 'shouxian' else '√'
            progress_data.append((5, company_map[company_code], status))
        
        # 督办事项6: 寿险—，其他O
        for company_code in company_map.keys():
            status = '—' if company_code == 'shouxian' else 'O'
            progress_data.append((6, company_map[company_code], status))
        
        # 督办事项7: 寿险—，其他O
        for company_code in company_map.keys():
            status = '—' if company_code == 'shouxian' else 'O'
            progress_data.append((7, company_map[company_code], status))
        
        # 督办事项8: 只有金租、资管、广租、通盛是O，其他—
        for company_code in company_map.keys():
            if company_code in ['jinzu', 'ziguan', 'guangzu', 'tongsheng']:
                status = 'O'
            else:
                status = '—'
            progress_data.append((8, company_map[company_code], status))
        
        # 督办事项9: 本部！，其他O
        for company_code in company_map.keys():
            status = '！' if company_code == 'benbu' else 'O'
            progress_data.append((9, company_map[company_code], status))
        
        # 督办事项10: 全部√
        for company_code in company_map.keys():
            progress_data.append((10, company_map[company_code], '√'))
        
        # 批量插入进度数据
        insert_progress_sql = """
        INSERT INTO company_supervision_progress (supervision_item_id, company_id, status, updated_by)
        VALUES (%s, %s, %s, 'system')
        """
        
        cursor.executemany(insert_progress_sql, progress_data)
        connection.commit()
        logging.info(f"✅ 导入 {len(progress_data)} 条进度记录")
        
        cursor.close()
        connection.close()
        
        logging.info("=" * 80)
        logging.info("🎯 Excel数据导入完成！")
        return True
        
    except Exception as e:
        logging.error(f"❌ Excel数据导入失败: {e}")
        return False

def main():
    """主函数"""
    logging.info("🚀 开始导入Excel数据")
    
    if import_excel_data():
        logging.info("🎯 Excel数据导入成功！")
    else:
        logging.error("❌ Excel数据导入失败")

if __name__ == "__main__":
    main()
