Options +FollowSymLinks +SymLinksIfOwnerMatch

#Prohibit directory browsing
Options -Indexes
# framework router settings.
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteRule (.*)$ index.php/$1 [L]
</IfModule>

<IfModule mod_php5.c>
# php settings.
php_value post_max_size 100M
php_flag  file_uploads On
php_value upload_max_filesize 100M
php_value display_errors 1
# for post vars limit.
php_value max_input_vars 100000
</IfModule>

<IfModule mod_php7.c>
# php settings.
php_value post_max_size 100M
php_flag  file_uploads On
php_value upload_max_filesize 100M
php_value display_errors 1
# for post vars limit.
php_value max_input_vars 100000
</IfModule>

<IfModule php_module>
# php settings.
Php_value post_max_size 100M
Php_flag  file_uploads On
Php_value upload_max_filesize 100M
Php_value display_errors 1
# for post vars limit.
Php_value max_input_vars 100000
</IfModule>

# yslow settings.
<IfModule mod_expires.c>
ExpiresActive On
ExpiresByType image/gif A2592000
ExpiresByType image/jpeg A2592000
ExpiresByType image/png A2592000
ExpiresByType image/x-icon A2592000
ExpiresByType application/javascript A2592000
ExpiresByType application/x-shockwave-flash A2592000
ExpiresByType text/css A604800
</IfModule>
FileEtag none
