#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前端功能
通过启动前端服务并验证功能
"""

import subprocess
import sys
import os
import time
import requests
import webbrowser
from datetime import datetime

def log(message, level="INFO"):
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def check_backend_service():
    """检查后端服务是否运行"""
    try:
        response = requests.get("http://localhost:8000/api/v1/new-supervision/items", timeout=5)
        if response.status_code == 200:
            data = response.json()
            items_count = len(data.get('data', []))
            log(f"✅ 后端服务正常运行，有 {items_count} 条督办事项")
            return True
        else:
            log(f"❌ 后端服务响应异常: {response.status_code}", "ERROR")
            return False
    except Exception as e:
        log(f"❌ 后端服务连接失败: {str(e)}", "ERROR")
        return False

def check_frontend_service():
    """检查前端服务是否运行"""
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            log("✅ 前端服务正常运行")
            return True
        else:
            log(f"❌ 前端服务响应异常: {response.status_code}", "ERROR")
            return False
    except Exception as e:
        log(f"❌ 前端服务连接失败: {str(e)}", "ERROR")
        return False

def start_frontend_service():
    """启动前端服务"""
    log("启动前端服务...")
    
    try:
        frontend_dir = "pmo-web"
        if not os.path.exists(frontend_dir):
            log("❌ 前端目录不存在", "ERROR")
            return None
            
        # 检查是否已安装依赖
        node_modules_path = os.path.join(frontend_dir, "node_modules")
        if not os.path.exists(node_modules_path):
            log("安装前端依赖...")
            install_process = subprocess.run(
                ["npm", "install"],
                cwd=frontend_dir,
                capture_output=True,
                text=True
            )
            if install_process.returncode != 0:
                log(f"❌ 依赖安装失败: {install_process.stderr}", "ERROR")
                return None
            log("✅ 依赖安装完成")
        
        # 启动开发服务器
        cmd = ["npm", "run", "dev"]
        
        process = subprocess.Popen(
            cmd,
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )
        
        # 等待服务启动
        log("等待前端服务启动...")
        for i in range(60):  # 等待60秒
            try:
                response = requests.get("http://localhost:3000", timeout=3)
                if response.status_code == 200:
                    log("✅ 前端服务启动成功")
                    return process
            except:
                pass
            
            if i % 10 == 0:
                log(f"等待中... ({i}/60)")
            time.sleep(1)
                
        log("❌ 前端服务启动超时", "ERROR")
        try:
            process.kill()
        except:
            pass
        return None
        
    except Exception as e:
        log(f"❌ 启动前端服务失败: {str(e)}", "ERROR")
        return None

def test_import_functionality():
    """测试导入功能"""
    try:
        log("测试导入功能...")
        
        # 创建测试数据
        import pandas as pd
        import io
        
        test_data = [{
            '操作类型': 'ADD',
            'ID': '',
            '序号': 9999,
            '工作维度': '前端测试维度',
            '工作主题': '前端测试主题',
            '督办来源': '前端测试来源',
            '工作内容和完成标志': '这是前端测试创建的督办事项',
            '是否年度绩效考核指标': '否',
            '完成时限': '2024-12-31',
            '整体进度': 'X 未启动'
        }]
        
        df = pd.DataFrame(test_data)
        excel_buffer = io.BytesIO()
        df.to_excel(excel_buffer, index=False)
        excel_buffer.seek(0)
        
        files = {'file': ('frontend_test.xlsx', excel_buffer.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
        response = requests.post("http://localhost:8000/api/v1/new-supervision/import-excel", files=files, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                log(f"✅ 导入测试成功: {result.get('message', '')}")
                return True
            else:
                log(f"❌ 导入失败: {result.get('message', '')}", "ERROR")
                return False
        else:
            log(f"❌ 导入请求失败: {response.status_code}", "ERROR")
            return False
            
    except Exception as e:
        log(f"❌ 测试导入失败: {str(e)}", "ERROR")
        return False

def cleanup_test_data():
    """清理测试数据"""
    try:
        log("清理测试数据...")
        
        import pandas as pd
        import io
        
        test_data = [{
            '操作类型': 'DELETE',
            'ID': '',
            '序号': 9999,
            '工作维度': '',
            '工作主题': '',
            '督办来源': '',
            '工作内容和完成标志': '',
            '是否年度绩效考核指标': '',
            '完成时限': '',
            '整体进度': ''
        }]
        
        df = pd.DataFrame(test_data)
        excel_buffer = io.BytesIO()
        df.to_excel(excel_buffer, index=False)
        excel_buffer.seek(0)
        
        files = {'file': ('frontend_cleanup.xlsx', excel_buffer.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
        response = requests.post("http://localhost:8000/api/v1/new-supervision/import-excel", files=files, timeout=30)
        
        if response.status_code == 200:
            log("✅ 测试数据清理完成")
        else:
            log("⚠️ 测试数据清理可能未完全成功", "WARNING")
            
    except Exception as e:
        log(f"⚠️ 清理测试数据时出错: {str(e)}", "WARNING")

def open_supervision_page():
    """打开督办管理页面"""
    try:
        supervision_url = "http://localhost:3000/#/new-supervision"
        log(f"打开督办管理页面: {supervision_url}")
        webbrowser.open(supervision_url)
        return True
    except Exception as e:
        log(f"❌ 打开页面失败: {str(e)}", "ERROR")
        return False

def main():
    log("🚀 开始前端功能测试")
    log("=" * 60)
    
    frontend_process = None
    
    try:
        # 1. 检查后端服务
        log("步骤1: 检查后端服务")
        if not check_backend_service():
            log("❌ 后端服务未运行，请先启动后端服务", "ERROR")
            return False
        
        # 2. 检查前端服务
        log("步骤2: 检查前端服务")
        if not check_frontend_service():
            log("前端服务未运行，尝试启动...")
            frontend_process = start_frontend_service()
            if not frontend_process:
                log("❌ 无法启动前端服务", "ERROR")
                return False
        else:
            log("✅ 前端服务已在运行")
        
        # 3. 测试导入功能
        log("步骤3: 测试导入功能")
        import_ok = test_import_functionality()
        
        # 4. 清理测试数据
        log("步骤4: 清理测试数据")
        cleanup_test_data()
        
        # 5. 打开督办管理页面
        log("步骤5: 打开督办管理页面")
        page_opened = open_supervision_page()
        
        # 6. 生成结果
        log("=" * 60)
        log("📊 测试结果")
        log("=" * 60)
        
        if import_ok and page_opened:
            print("""
🎉 前端功能测试完成！

✅ 测试结果：
- ✅ 后端服务正常运行
- ✅ 前端服务正常运行
- ✅ 导入功能测试通过
- ✅ 督办管理页面已打开

📋 手动测试步骤：
1. 在打开的页面中点击"导出Excel"按钮
2. 检查导出的Excel文件是否包含"操作类型"和"ID"列
3. 在Excel中修改数据，设置操作类型（ADD/UPDATE/DELETE）
4. 点击"导入Excel"按钮上传修改后的文件
5. 验证数据是否正确更新

🎯 如果以上步骤都正常，说明功能完全可用！

⚠️  注意：前端使用的是客户端Excel生成，所以导出功能应该包含正确的列。
   如果导出的Excel没有"操作类型"和"ID"列，请检查前端代码。
            """)
            return True
        else:
            print(f"""
❌ 测试结果：
- 后端服务: ✅ 正常
- 前端服务: {'✅ 正常' if page_opened else '❌ 异常'}
- 导入功能: {'✅ 通过' if import_ok else '❌ 失败'}

需要进一步检查。
            """)
            return False
            
    except KeyboardInterrupt:
        log("用户中断测试", "WARNING")
        return False
    except Exception as e:
        log(f"❌ 测试过程中出现异常: {str(e)}", "ERROR")
        return False
    finally:
        # 保持前端服务运行
        if frontend_process:
            log("前端服务保持运行状态，可以继续使用")

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 前端功能测试完成！请在浏览器中手动验证导出功能。")
        sys.exit(0)
    else:
        print("\n💥 前端功能测试失败！")
        sys.exit(1)
