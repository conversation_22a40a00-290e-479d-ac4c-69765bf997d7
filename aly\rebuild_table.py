import mysql.connector
from mysql.connector import Error
import time

# 数据库配置
config = {
    'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'cyh',
    'password': 'Qq188788',
    'database': 'kanban',
    'connect_timeout': 20,  # 增加连接超时时间
    'connection_timeout': 20
}

def get_connection(max_retries=3):
    for attempt in range(max_retries):
        try:
            return mysql.connector.connect(**config)
        except Error as e:
            if attempt == max_retries - 1:
                raise e
            print(f"连接失败，正在重试... ({attempt + 1}/{max_retries})")
            time.sleep(2)  # 等待2秒后重试

try:
    # 建立连接
    print("正在连接数据库...")
    conn = get_connection()
    cursor = conn.cursor()
    
    # 1. 创建临时表
    print("创建临时表...")
    cursor.execute("""
        CREATE TABLE weekly_reports_temp (
            id INT AUTO_INCREMENT PRIMARY KEY,
            project_code varchar(255) NOT NULL,
            project_name varchar(255) NOT NULL,
            project_start_date date DEFAULT NULL,
            project_amount decimal(10,2) DEFAULT NULL,
            construction_period int DEFAULT NULL,
            contract_date date DEFAULT NULL,
            department varchar(255) DEFAULT NULL,
            total_cost decimal(10,2) DEFAULT NULL,
            outsourcing_cost decimal(10,2) DEFAULT NULL,
            labor_cost decimal(10,2) DEFAULT NULL,
            requirement_progress decimal(5,3) DEFAULT NULL COMMENT '需求进度(0-1)',
            development_progress decimal(5,3) DEFAULT NULL COMMENT '开发进度(0-1)',
            project_progress decimal(5,3) DEFAULT NULL COMMENT '项目进度(0-1)',
            current_stage varchar(100) DEFAULT NULL,
            cost_progress decimal(5,3) DEFAULT NULL COMMENT '成本进度(0-1)',
            reporter varchar(100) DEFAULT NULL,
            overall_situation text,
            milestone_tasks text,
            quality_situation text,
            resource_input text,
            last_week_review text,
            this_week_completion text,
            next_week_plan text,
            communication text,
            key_issues_risks text,
            conclusions text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP,
            created_by varchar(100) DEFAULT NULL,
            period_start_date date DEFAULT NULL,
            period_end_date date DEFAULT NULL,
            KEY idx_project_code (project_code),
            KEY idx_project_name (project_name),
            KEY idx_department (department),
            KEY idx_period (period_start_date,period_end_date),
            KEY idx_created_at (created_at),
            KEY idx_updated_at (updated_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='项目周报表'
    """)
    
    # 2. 复制数据
    print("复制数据到临时表...")
    cursor.execute("""
        INSERT INTO weekly_reports_temp 
        (project_code, project_name, project_start_date, project_amount, 
         construction_period, contract_date, department, total_cost, 
         outsourcing_cost, labor_cost, requirement_progress, development_progress,
         project_progress, current_stage, cost_progress, reporter, 
         overall_situation, milestone_tasks, quality_situation, resource_input,
         last_week_review, this_week_completion, next_week_plan, communication,
         key_issues_risks, conclusions, created_at, updated_at, created_by,
         period_start_date, period_end_date)
        SELECT 
         project_code, project_name, project_start_date, project_amount,
         construction_period, contract_date, department, total_cost,
         outsourcing_cost, labor_cost, requirement_progress, development_progress,
         project_progress, current_stage, cost_progress, reporter,
         overall_situation, milestone_tasks, quality_situation, resource_input,
         last_week_review, this_week_completion, next_week_plan, communication,
         key_issues_risks, conclusions, created_at, updated_at, created_by,
         period_start_date, period_end_date
        FROM weekly_reports
    """)
    
    # 3. 删除原表
    print("删除原表...")
    cursor.execute("DROP TABLE weekly_reports")
    
    # 4. 重命名临时表
    print("重命名临时表...")
    cursor.execute("RENAME TABLE weekly_reports_temp TO weekly_reports")
    
    # 提交更改
    conn.commit()
    print("表重建完成！")
    
    # 5. 验证新表结构
    print("\n验证新表结构...")
    cursor.execute("SHOW CREATE TABLE weekly_reports")
    table_info = cursor.fetchone()
    print("\n当前表结构：")
    print(table_info[1])
    
except Exception as e:
    print(f"发生错误: {str(e)}")
    # 如果出错，尝试清理临时表
    try:
        cursor.execute("DROP TABLE IF EXISTS weekly_reports_temp")
        conn.commit()
    except:
        pass
finally:
    if 'cursor' in locals():
        cursor.close()
    if 'conn' in locals():
        conn.close() 