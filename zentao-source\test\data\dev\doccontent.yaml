title: table zt_doccontent
desc: ""
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: doc
    note: "文档ID"
    range: 1-900
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: title
    note: "文档标题"
    range: 1-10000
    prefix: "文档标题"
    postfix: ""
    loop: 0
    format: ""
  - field: digest
    note: "文档摘要"
    range: 1-10000
    prefix: "文档摘要"
    postfix: ""
    loop: 0
    format: ""
  - field: content
    note: "文档正文"
    fields:
      - field: content1
        range: 文档正文,文档正文,www.baidu.com
      - field: content2
        range: 1-2,[]
    postfix: ""
    loop: 0
    format: ""
  - field: files
    note: "附件ID"
    range: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "文档类型"
    range: text,markdown,html
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: version
    note: "版本号"
    range: 1{900},2{10}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
