body {margin-bottom: 25px;}
.fullscreen-close {top: 8px !important;}
#docList.table .c-name > .ajaxCollect {float: right; position: relative; right: 10px; top: 0px;}
#docList th.c-id {width: 72px;}
#docList th.c-user {width: 80px;}
#docList th.c-actions {width: 84px; padding-left: 15px;}
#docList .c-object {width: 180px; overflow: hidden; white-space: nowrap; text-overflow: clip;}
#docList.table .c-name > .doc-title {display: inline-block; max-width: calc(100% - 40px); overflow: hidden; background: transparent; padding-right:0px;}
#docList.table .c-name[data-status=draft] > .doc-title {max-width: calc(100% - 80px);}
#docList.table .c-name > span.doc-title {line-height: 0; vertical-align: inherit;}
#docList.table .c-name > .draft {background-color:rgba(129, 102, 238, 0.12); color:#8166EE;}
#docList .btn {padding: 0 2px;}
#docList .ajaxCollect {width: 30px; padding: 0 6px;}
.btn.ajaxCollect > img {width: 20px;}
#queryBox .btn-save-form, #queryBox #toggle-queries{display: none;}
