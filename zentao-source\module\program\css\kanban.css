.main-table .table {cursor: default;}
.main-table .table td {background: #f5f5f5;}
.main-table tbody>tr>td:first-child {padding: 0px 8px}

.lane-name {overflow: hidden; text-overflow: ellipsis; writing-mode: vertical-lr;}

.board-item {border: 1px solid #EBEBEB; padding: 5px 10px; cursor: default; border-radius: 2px; background-color: #fff;}
.board-item:hover {border-color: #ccc;}
.board-item {margin-top: 10px;}
.board-item:last-child {margin-bottom: 10px;}

#kanban {overflow-y: auto;}
#kanban tbody > tr > td {line-height: 24px;}
#kanban tbody > tr > td{border-right: 3px solid #fff; border-bottom: 2px solid #fff;}
#kanban thead > tr:first-child > th:not(:first-child) {border-right: 2px solid #fff; border-bottom: 2px solid #fff;}
#kanban thead > tr:last-child > th {border: 2px solid #fff;}

.doing-td, .wait-project, .normal-plan, .normal-release {vertical-align: top !important;}

.doing-td .board:first-child{padding-top: 0; border-top: unset;}

.project-line {width: 100%; height: 2px; background: #fff}

.doing-td .table-row .table-col:last-child, .c-progress {width: 30px;}
.doing-td .table-row .table-col:first-child {overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}

.scroll {overflow-x: hidden; overflow-y: auto;}
.fix-table-copy-wrapper thead > tr > th:first-child {background: none!important;}

#showSettingsBox {text-align:right; margin-top:-15px; margin-right:4px;}
#showSettingsBox .checkbox-primary {display:inline-block;}
