function t(t){this.content=t}function e(t,n,r){for(let i=0;;i++){if(i==t.childCount||i==n.childCount)return t.childCount==n.childCount?null:r;let s=t.child(i),o=n.child(i);if(s!=o){if(!s.sameMarkup(o))return r;if(s.isText&&s.text!=o.text){for(let t=0;s.text[t]==o.text[t];t++)r++;return r}if(s.content.size||o.content.size){let t=e(s.content,o.content,r+1);if(null!=t)return t}r+=s.nodeSize}else r+=s.nodeSize}}function n(t,e,r,i){for(let s=t.childCount,o=e.childCount;;){if(0==s||0==o)return s==o?null:{a:r,b:i};let l=t.child(--s),u=e.child(--o),h=l.nodeSize;if(l!=u){if(!l.sameMarkup(u))return{a:r,b:i};if(l.isText&&l.text!=u.text){let t=0,e=Math.min(l.text.length,u.text.length);for(;t<e&&l.text[l.text.length-t-1]==u.text[u.text.length-t-1];)t++,r--,i--;return{a:r,b:i}}if(l.content.size||u.content.size){let t=n(l.content,u.content,r-1,i-1);if(t)return t}r-=h,i-=h}else r-=h,i-=h}}t.prototype={constructor:t,find:function(t){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===t)return e;return-1},get:function(t){var e=this.find(t);return-1==e?void 0:this.content[e+1]},update:function(e,n,r){var i=r&&r!=e?this.remove(r):this,s=i.find(e),o=i.content.slice();return-1==s?o.push(r||e,n):(o[s+1]=n,r&&(o[s]=r)),new t(o)},remove:function(e){var n=this.find(e);if(-1==n)return this;var r=this.content.slice();return r.splice(n,2),new t(r)},addToStart:function(e,n){return new t([e,n].concat(this.remove(e).content))},addToEnd:function(e,n){var r=this.remove(e).content.slice();return r.push(e,n),new t(r)},addBefore:function(e,n,r){var i=this.remove(n),s=i.content.slice(),o=i.find(e);return s.splice(-1==o?s.length:o,0,n,r),new t(s)},forEach:function(t){for(var e=0;e<this.content.length;e+=2)t(this.content[e],this.content[e+1])},prepend:function(e){return(e=t.from(e)).size?new t(e.content.concat(this.subtract(e).content)):this},append:function(e){return(e=t.from(e)).size?new t(this.subtract(e).content.concat(e.content)):this},subtract:function(e){var n=this;e=t.from(e);for(var r=0;r<e.content.length;r+=2)n=n.remove(e.content[r]);return n},toObject:function(){var t={};return this.forEach((function(e,n){t[e]=n})),t},get size(){return this.content.length>>1}},t.from=function(e){if(e instanceof t)return e;var n=[];if(e)for(var r in e)n.push(r,e[r]);return new t(n)};class r{constructor(t,e){if(this.content=t,this.size=e||0,null==e)for(let e=0;e<t.length;e++)this.size+=t[e].nodeSize}nodesBetween(t,e,n,r=0,i){for(let s=0,o=0;o<e;s++){let l=this.content[s],u=o+l.nodeSize;if(u>t&&!1!==n(l,r+o,i||null,s)&&l.content.size){let i=o+1;l.nodesBetween(Math.max(0,t-i),Math.min(l.content.size,e-i),n,r+i)}o=u}}descendants(t){this.nodesBetween(0,this.size,t)}textBetween(t,e,n,r){let i="",s=!0;return this.nodesBetween(t,e,((o,l)=>{let u=o.isText?o.text.slice(Math.max(t,l)-l,e-l):o.isLeaf?r?"function"==typeof r?r(o):r:o.type.spec.leafText?o.type.spec.leafText(o):"":"";o.isBlock&&(o.isLeaf&&u||o.isTextblock)&&n&&(s?s=!1:i+=n),i+=u}),0),i}append(t){if(!t.size)return this;if(!this.size)return t;let e=this.lastChild,n=t.firstChild,i=this.content.slice(),s=0;for(e.isText&&e.sameMarkup(n)&&(i[i.length-1]=e.withText(e.text+n.text),s=1);s<t.content.length;s++)i.push(t.content[s]);return new r(i,this.size+t.size)}cut(t,e=this.size){if(0==t&&e==this.size)return this;let n=[],i=0;if(e>t)for(let r=0,s=0;s<e;r++){let o=this.content[r],l=s+o.nodeSize;l>t&&((s<t||l>e)&&(o=o.isText?o.cut(Math.max(0,t-s),Math.min(o.text.length,e-s)):o.cut(Math.max(0,t-s-1),Math.min(o.content.size,e-s-1))),n.push(o),i+=o.nodeSize),s=l}return new r(n,i)}cutByIndex(t,e){return t==e?r.empty:0==t&&e==this.content.length?this:new r(this.content.slice(t,e))}replaceChild(t,e){let n=this.content[t];if(n==e)return this;let i=this.content.slice(),s=this.size+e.nodeSize-n.nodeSize;return i[t]=e,new r(i,s)}addToStart(t){return new r([t].concat(this.content),this.size+t.nodeSize)}addToEnd(t){return new r(this.content.concat(t),this.size+t.nodeSize)}eq(t){if(this.content.length!=t.content.length)return!1;for(let e=0;e<this.content.length;e++)if(!this.content[e].eq(t.content[e]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(t){let e=this.content[t];if(!e)throw new RangeError("Index "+t+" out of range for "+this);return e}maybeChild(t){return this.content[t]||null}forEach(t){for(let e=0,n=0;e<this.content.length;e++){let r=this.content[e];t(r,n,e),n+=r.nodeSize}}findDiffStart(t,n=0){return e(this,t,n)}findDiffEnd(t,e=this.size,r=t.size){return n(this,t,e,r)}findIndex(t,e=-1){if(0==t)return s(0,t);if(t==this.size)return s(this.content.length,t);if(t>this.size||t<0)throw new RangeError(`Position ${t} outside of fragment (${this})`);for(let n=0,r=0;;n++){let i=r+this.child(n).nodeSize;if(i>=t)return i==t||e>0?s(n+1,i):s(n,r);r=i}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map((t=>t.toJSON())):null}static fromJSON(t,e){if(!e)return r.empty;if(!Array.isArray(e))throw new RangeError("Invalid input for Fragment.fromJSON");return new r(e.map(t.nodeFromJSON))}static fromArray(t){if(!t.length)return r.empty;let e,n=0;for(let r=0;r<t.length;r++){let i=t[r];n+=i.nodeSize,r&&i.isText&&t[r-1].sameMarkup(i)?(e||(e=t.slice(0,r)),e[e.length-1]=i.withText(e[e.length-1].text+i.text)):e&&e.push(i)}return new r(e||t,n)}static from(t){if(!t)return r.empty;if(t instanceof r)return t;if(Array.isArray(t))return this.fromArray(t);if(t.attrs)return new r([t],t.nodeSize);throw new RangeError("Can not convert "+t+" to a Fragment"+(t.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}r.empty=new r([],0);const i={index:0,offset:0};function s(t,e){return i.index=t,i.offset=e,i}function o(t,e){if(t===e)return!0;if(!t||"object"!=typeof t||!e||"object"!=typeof e)return!1;let n=Array.isArray(t);if(Array.isArray(e)!=n)return!1;if(n){if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!o(t[n],e[n]))return!1}else{for(let n in t)if(!(n in e)||!o(t[n],e[n]))return!1;for(let n in e)if(!(n in t))return!1}return!0}class l{constructor(t,e){this.type=t,this.attrs=e}addToSet(t){let e,n=!1;for(let r=0;r<t.length;r++){let i=t[r];if(this.eq(i))return t;if(this.type.excludes(i.type))e||(e=t.slice(0,r));else{if(i.type.excludes(this.type))return t;!n&&i.type.rank>this.type.rank&&(e||(e=t.slice(0,r)),e.push(this),n=!0),e&&e.push(i)}}return e||(e=t.slice()),n||e.push(this),e}removeFromSet(t){for(let e=0;e<t.length;e++)if(this.eq(t[e]))return t.slice(0,e).concat(t.slice(e+1));return t}isInSet(t){for(let e=0;e<t.length;e++)if(this.eq(t[e]))return!0;return!1}eq(t){return this==t||this.type==t.type&&o(this.attrs,t.attrs)}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return t}static fromJSON(t,e){if(!e)throw new RangeError("Invalid input for Mark.fromJSON");let n=t.marks[e.type];if(!n)throw new RangeError(`There is no mark type ${e.type} in this schema`);return n.create(e.attrs)}static sameSet(t,e){if(t==e)return!0;if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!t[n].eq(e[n]))return!1;return!0}static setFrom(t){if(!t||Array.isArray(t)&&0==t.length)return l.none;if(t instanceof l)return[t];let e=t.slice();return e.sort(((t,e)=>t.type.rank-e.type.rank)),e}}l.none=[];class u extends Error{}class h{constructor(t,e,n){this.content=t,this.openStart=e,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(t,e){let n=a(this.content,t+this.openStart,e);return n&&new h(n,this.openStart,this.openEnd)}removeBetween(t,e){return new h(f(this.content,t+this.openStart,e+this.openStart),this.openStart,this.openEnd)}eq(t){return this.content.eq(t.content)&&this.openStart==t.openStart&&this.openEnd==t.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let t={content:this.content.toJSON()};return this.openStart>0&&(t.openStart=this.openStart),this.openEnd>0&&(t.openEnd=this.openEnd),t}static fromJSON(t,e){if(!e)return h.empty;let n=e.openStart||0,i=e.openEnd||0;if("number"!=typeof n||"number"!=typeof i)throw new RangeError("Invalid input for Slice.fromJSON");return new h(r.fromJSON(t,e.content),n,i)}static maxOpen(t,e=!0){let n=0,r=0;for(let r=t.firstChild;r&&!r.isLeaf&&(e||!r.type.spec.isolating);r=r.firstChild)n++;for(let n=t.lastChild;n&&!n.isLeaf&&(e||!n.type.spec.isolating);n=n.lastChild)r++;return new h(t,n,r)}}function f(t,e,n){let{index:r,offset:i}=t.findIndex(e),s=t.maybeChild(r),{index:o,offset:l}=t.findIndex(n);if(i==e||s.isText){if(l!=n&&!t.child(o).isText)throw new RangeError("Removing non-flat range");return t.cut(0,e).append(t.cut(n))}if(r!=o)throw new RangeError("Removing non-flat range");return t.replaceChild(r,s.copy(f(s.content,e-i-1,n-i-1)))}function a(t,e,n,r){let{index:i,offset:s}=t.findIndex(e),o=t.maybeChild(i);if(s==e||o.isText)return r&&!r.canReplace(i,i,n)?null:t.cut(0,e).append(n).append(t.cut(e));let l=a(o.content,e-s-1,n);return l&&t.replaceChild(i,o.copy(l))}function c(t,e,n){if(n.openStart>t.depth)throw new u("Inserted content deeper than insertion position");if(t.depth-n.openStart!=e.depth-n.openEnd)throw new u("Inconsistent open depths");return d(t,e,n,0)}function d(t,e,n,i){let s=t.index(i),o=t.node(i);if(s==e.index(i)&&i<t.depth-n.openStart){let r=d(t,e,n,i+1);return o.copy(o.content.replaceChild(s,r))}if(n.content.size){if(n.openStart||n.openEnd||t.depth!=i||e.depth!=i){let{start:s,end:l}=function(t,e){let n=e.depth-t.openStart,i=e.node(n).copy(t.content);for(let t=n-1;t>=0;t--)i=e.node(t).copy(r.from(i));return{start:i.resolveNoCache(t.openStart+n),end:i.resolveNoCache(i.content.size-t.openEnd-n)}}(n,t);return g(o,b(t,s,l,e,i))}{let r=t.parent,i=r.content;return g(r,i.cut(0,t.parentOffset).append(n.content).append(i.cut(e.parentOffset)))}}return g(o,y(t,e,i))}function p(t,e){if(!e.type.compatibleContent(t.type))throw new u("Cannot join "+e.type.name+" onto "+t.type.name)}function m(t,e,n){let r=t.node(n);return p(r,e.node(n)),r}function w(t,e){let n=e.length-1;n>=0&&t.isText&&t.sameMarkup(e[n])?e[n]=t.withText(e[n].text+t.text):e.push(t)}function v(t,e,n,r){let i=(e||t).node(n),s=0,o=e?e.index(n):i.childCount;t&&(s=t.index(n),t.depth>n?s++:t.textOffset&&(w(t.nodeAfter,r),s++));for(let t=s;t<o;t++)w(i.child(t),r);e&&e.depth==n&&e.textOffset&&w(e.nodeBefore,r)}function g(t,e){return t.type.checkContent(e),t.copy(e)}function b(t,e,n,i,s){let o=t.depth>s&&m(t,e,s+1),l=i.depth>s&&m(n,i,s+1),u=[];return v(null,t,s,u),o&&l&&e.index(s)==n.index(s)?(p(o,l),w(g(o,b(t,e,n,i,s+1)),u)):(o&&w(g(o,y(t,e,s+1)),u),v(e,n,s,u),l&&w(g(l,y(n,i,s+1)),u)),v(i,null,s,u),new r(u)}function y(t,e,n){let i=[];return v(null,t,n,i),t.depth>n&&w(g(m(t,e,n+1),y(t,e,n+1)),i),v(e,null,n,i),new r(i)}h.empty=new h(r.empty,0,0);class k{constructor(t,e,n){this.pos=t,this.path=e,this.parentOffset=n,this.depth=e.length/3-1}resolveDepth(t){return null==t?this.depth:t<0?this.depth+t:t}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(t){return this.path[3*this.resolveDepth(t)]}index(t){return this.path[3*this.resolveDepth(t)+1]}indexAfter(t){return t=this.resolveDepth(t),this.index(t)+(t!=this.depth||this.textOffset?1:0)}start(t){return 0==(t=this.resolveDepth(t))?0:this.path[3*t-1]+1}end(t){return t=this.resolveDepth(t),this.start(t)+this.node(t).content.size}before(t){if(!(t=this.resolveDepth(t)))throw new RangeError("There is no position before the top-level node");return t==this.depth+1?this.pos:this.path[3*t-1]}after(t){if(!(t=this.resolveDepth(t)))throw new RangeError("There is no position after the top-level node");return t==this.depth+1?this.pos:this.path[3*t-1]+this.path[3*t].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let t=this.parent,e=this.index(this.depth);if(e==t.childCount)return null;let n=this.pos-this.path[this.path.length-1],r=t.child(e);return n?t.child(e).cut(n):r}get nodeBefore(){let t=this.index(this.depth),e=this.pos-this.path[this.path.length-1];return e?this.parent.child(t).cut(0,e):0==t?null:this.parent.child(t-1)}posAtIndex(t,e){e=this.resolveDepth(e);let n=this.path[3*e],r=0==e?0:this.path[3*e-1]+1;for(let e=0;e<t;e++)r+=n.child(e).nodeSize;return r}marks(){let t=this.parent,e=this.index();if(0==t.content.size)return l.none;if(this.textOffset)return t.child(e).marks;let n=t.maybeChild(e-1),r=t.maybeChild(e);if(!n){let t=n;n=r,r=t}let i=n.marks;for(var s=0;s<i.length;s++)!1!==i[s].type.spec.inclusive||r&&i[s].isInSet(r.marks)||(i=i[s--].removeFromSet(i));return i}marksAcross(t){let e=this.parent.maybeChild(this.index());if(!e||!e.isInline)return null;let n=e.marks,r=t.parent.maybeChild(t.index());for(var i=0;i<n.length;i++)!1!==n[i].type.spec.inclusive||r&&n[i].isInSet(r.marks)||(n=n[i--].removeFromSet(n));return n}sharedDepth(t){for(let e=this.depth;e>0;e--)if(this.start(e)<=t&&this.end(e)>=t)return e;return 0}blockRange(t=this,e){if(t.pos<this.pos)return t.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==t.pos?1:0);n>=0;n--)if(t.pos<=this.end(n)&&(!e||e(this.node(n))))return new S(this,t,n);return null}sameParent(t){return this.pos-this.parentOffset==t.pos-t.parentOffset}max(t){return t.pos>this.pos?t:this}min(t){return t.pos<this.pos?t:this}toString(){let t="";for(let e=1;e<=this.depth;e++)t+=(t?"/":"")+this.node(e).type.name+"_"+this.index(e-1);return t+":"+this.parentOffset}static resolve(t,e){if(!(e>=0&&e<=t.content.size))throw new RangeError("Position "+e+" out of range");let n=[],r=0,i=e;for(let e=t;;){let{index:t,offset:s}=e.content.findIndex(i),o=i-s;if(n.push(e,t,r+s),!o)break;if(e=e.child(t),e.isText)break;i=o-1,r+=s+1}return new k(e,n,i)}static resolveCached(t,e){for(let n=0;n<x.length;n++){let r=x[n];if(r.pos==e&&r.doc==t)return r}let n=x[M]=k.resolve(t,e);return M=(M+1)%O,n}}let x=[],M=0,O=12;class S{constructor(t,e,n){this.$from=t,this.$to=e,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const E=Object.create(null);class N{constructor(t,e,n,i=l.none){this.type=t,this.attrs=e,this.marks=i,this.content=n||r.empty}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(t){return this.content.child(t)}maybeChild(t){return this.content.maybeChild(t)}forEach(t){this.content.forEach(t)}nodesBetween(t,e,n,r=0){this.content.nodesBetween(t,e,n,r,this)}descendants(t){this.nodesBetween(0,this.content.size,t)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(t,e,n,r){return this.content.textBetween(t,e,n,r)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(t){return this==t||this.sameMarkup(t)&&this.content.eq(t.content)}sameMarkup(t){return this.hasMarkup(t.type,t.attrs,t.marks)}hasMarkup(t,e,n){return this.type==t&&o(this.attrs,e||t.defaultAttrs||E)&&l.sameSet(this.marks,n||l.none)}copy(t=null){return t==this.content?this:new N(this.type,this.attrs,t,this.marks)}mark(t){return t==this.marks?this:new N(this.type,this.attrs,this.content,t)}cut(t,e=this.content.size){return 0==t&&e==this.content.size?this:this.copy(this.content.cut(t,e))}slice(t,e=this.content.size,n=!1){if(t==e)return h.empty;let r=this.resolve(t),i=this.resolve(e),s=n?0:r.sharedDepth(e),o=r.start(s),l=r.node(s).content.cut(r.pos-o,i.pos-o);return new h(l,r.depth-s,i.depth-s)}replace(t,e,n){return c(this.resolve(t),this.resolve(e),n)}nodeAt(t){for(let e=this;;){let{index:n,offset:r}=e.content.findIndex(t);if(e=e.maybeChild(n),!e)return null;if(r==t||e.isText)return e;t-=r+1}}childAfter(t){let{index:e,offset:n}=this.content.findIndex(t);return{node:this.content.maybeChild(e),index:e,offset:n}}childBefore(t){if(0==t)return{node:null,index:0,offset:0};let{index:e,offset:n}=this.content.findIndex(t);if(n<t)return{node:this.content.child(e),index:e,offset:n};let r=this.content.child(e-1);return{node:r,index:e-1,offset:n-r.nodeSize}}resolve(t){return k.resolveCached(this,t)}resolveNoCache(t){return k.resolve(this,t)}rangeHasMark(t,e,n){let r=!1;return e>t&&this.nodesBetween(t,e,(t=>(n.isInSet(t.marks)&&(r=!0),!r))),r}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let t=this.type.name;return this.content.size&&(t+="("+this.content.toStringInner()+")"),R(this.marks,t)}contentMatchAt(t){let e=this.type.contentMatch.matchFragment(this.content,0,t);if(!e)throw new Error("Called contentMatchAt on a node with invalid content");return e}canReplace(t,e,n=r.empty,i=0,s=n.childCount){let o=this.contentMatchAt(t).matchFragment(n,i,s),l=o&&o.matchFragment(this.content,e);if(!l||!l.validEnd)return!1;for(let t=i;t<s;t++)if(!this.type.allowsMarks(n.child(t).marks))return!1;return!0}canReplaceWith(t,e,n,r){if(r&&!this.type.allowsMarks(r))return!1;let i=this.contentMatchAt(t).matchType(n),s=i&&i.matchFragment(this.content,e);return!!s&&s.validEnd}canAppend(t){return t.content.size?this.canReplace(this.childCount,this.childCount,t.content):this.type.compatibleContent(t.type)}check(){this.type.checkContent(this.content);let t=l.none;for(let e=0;e<this.marks.length;e++)t=this.marks[e].addToSet(t);if(!l.sameSet(t,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map((t=>t.type.name))}`);this.content.forEach((t=>t.check()))}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return this.content.size&&(t.content=this.content.toJSON()),this.marks.length&&(t.marks=this.marks.map((t=>t.toJSON()))),t}static fromJSON(t,e){if(!e)throw new RangeError("Invalid input for Node.fromJSON");let n=null;if(e.marks){if(!Array.isArray(e.marks))throw new RangeError("Invalid mark data for Node.fromJSON");n=e.marks.map(t.markFromJSON)}if("text"==e.type){if("string"!=typeof e.text)throw new RangeError("Invalid text node in JSON");return t.text(e.text,n)}let i=r.fromJSON(t,e.content);return t.nodeType(e.type).create(e.attrs,i,n)}}N.prototype.text=void 0;class C extends N{constructor(t,e,n,r){if(super(t,e,null,r),!n)throw new RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):R(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(t,e){return this.text.slice(t,e)}get nodeSize(){return this.text.length}mark(t){return t==this.marks?this:new C(this.type,this.attrs,this.text,t)}withText(t){return t==this.text?this:new C(this.type,this.attrs,t,this.marks)}cut(t=0,e=this.text.length){return 0==t&&e==this.text.length?this:this.withText(this.text.slice(t,e))}eq(t){return this.sameMarkup(t)&&this.text==t.text}toJSON(){let t=super.toJSON();return t.text=this.text,t}}function R(t,e){for(let n=t.length-1;n>=0;n--)e=t[n].type.name+"("+e+")";return e}class T{constructor(t){this.validEnd=t,this.next=[],this.wrapCache=[]}static parse(t,e){let n=new j(t,e);if(null==n.next)return T.empty;let r=A(n);n.next&&n.err("Unexpected trailing text");let i=function(t){let e=Object.create(null);return function n(r){let i=[];r.forEach((e=>{t[e].forEach((({term:e,to:n})=>{if(!e)return;let r;for(let t=0;t<i.length;t++)i[t][0]==e&&(r=i[t][1]);J(t,n).forEach((t=>{r||i.push([e,r=[]]),-1==r.indexOf(t)&&r.push(t)}))}))}));let s=e[r.join(",")]=new T(r.indexOf(t.length-1)>-1);for(let t=0;t<i.length;t++){let r=i[t][1].sort(B);s.next.push({type:i[t][0],next:e[r.join(",")]||n(r)})}return s}(J(t,0))}(function(t){let e=[[]];return i(function t(e,s){if("choice"==e.type)return e.exprs.reduce(((e,n)=>e.concat(t(n,s))),[]);if("seq"!=e.type){if("star"==e.type){let o=n();return r(s,o),i(t(e.expr,o),o),[r(o)]}if("plus"==e.type){let o=n();return i(t(e.expr,s),o),i(t(e.expr,o),o),[r(o)]}if("opt"==e.type)return[r(s)].concat(t(e.expr,s));if("range"==e.type){let o=s;for(let r=0;r<e.min;r++){let r=n();i(t(e.expr,o),r),o=r}if(-1==e.max)i(t(e.expr,o),o);else for(let s=e.min;s<e.max;s++){let s=n();r(o,s),i(t(e.expr,o),s),o=s}return[r(o)]}if("name"==e.type)return[r(s,void 0,e.value)];throw new Error("Unknown expr type")}for(let r=0;;r++){let o=t(e.exprs[r],s);if(r==e.exprs.length-1)return o;i(o,s=n())}}(t,0),n()),e;function n(){return e.push([])-1}function r(t,n,r){let i={term:r,to:n};return e[t].push(i),i}function i(t,e){t.forEach((t=>t.to=e))}}(r));return function(t,e){for(let n=0,r=[t];n<r.length;n++){let t=r[n],i=!t.validEnd,s=[];for(let e=0;e<t.next.length;e++){let{type:n,next:o}=t.next[e];s.push(n.name),!i||n.isText||n.hasRequiredAttrs()||(i=!1),-1==r.indexOf(o)&&r.push(o)}i&&e.err("Only non-generatable nodes ("+s.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(i,n),i}matchType(t){for(let e=0;e<this.next.length;e++)if(this.next[e].type==t)return this.next[e].next;return null}matchFragment(t,e=0,n=t.childCount){let r=this;for(let i=e;r&&i<n;i++)r=r.matchType(t.child(i).type);return r}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let t=0;t<this.next.length;t++){let{type:e}=this.next[t];if(!e.isText&&!e.hasRequiredAttrs())return e}return null}compatible(t){for(let e=0;e<this.next.length;e++)for(let n=0;n<t.next.length;n++)if(this.next[e].type==t.next[n].type)return!0;return!1}fillBefore(t,e=!1,n=0){let i=[this];return function s(o,l){let u=o.matchFragment(t,n);if(u&&(!e||u.validEnd))return r.from(l.map((t=>t.createAndFill())));for(let t=0;t<o.next.length;t++){let{type:e,next:n}=o.next[t];if(!e.isText&&!e.hasRequiredAttrs()&&-1==i.indexOf(n)){i.push(n);let t=s(n,l.concat(e));if(t)return t}}return null}(this,[])}findWrapping(t){for(let e=0;e<this.wrapCache.length;e+=2)if(this.wrapCache[e]==t)return this.wrapCache[e+1];let e=this.computeWrapping(t);return this.wrapCache.push(t,e),e}computeWrapping(t){let e=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let r=n.shift(),i=r.match;if(i.matchType(t)){let t=[];for(let e=r;e.type;e=e.via)t.push(e.type);return t.reverse()}for(let t=0;t<i.next.length;t++){let{type:s,next:o}=i.next[t];s.isLeaf||s.hasRequiredAttrs()||s.name in e||r.type&&!o.validEnd||(n.push({match:s.contentMatch,type:s,via:r}),e[s.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(t){if(t>=this.next.length)throw new RangeError(`There's no ${t}th edge in this content match`);return this.next[t]}toString(){let t=[];return function e(n){t.push(n);for(let r=0;r<n.next.length;r++)-1==t.indexOf(n.next[r].next)&&e(n.next[r].next)}(this),t.map(((e,n)=>{let r=n+(e.validEnd?"*":" ")+" ";for(let n=0;n<e.next.length;n++)r+=(n?", ":"")+e.next[n].type.name+"->"+t.indexOf(e.next[n].next);return r})).join("\n")}}T.empty=new T(!0);class j{constructor(t,e){this.string=t,this.nodeTypes=e,this.inline=null,this.pos=0,this.tokens=t.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(t){return this.next==t&&(this.pos++||!0)}err(t){throw new SyntaxError(t+" (in content expression '"+this.string+"')")}}function A(t){let e=[];do{e.push($(t))}while(t.eat("|"));return 1==e.length?e[0]:{type:"choice",exprs:e}}function $(t){let e=[];do{e.push(D(t))}while(t.next&&")"!=t.next&&"|"!=t.next);return 1==e.length?e[0]:{type:"seq",exprs:e}}function D(t){let e=function(t){if(t.eat("(")){let e=A(t);return t.eat(")")||t.err("Missing closing paren"),e}if(!/\W/.test(t.next)){let e=function(t,e){let n=t.nodeTypes,r=n[e];if(r)return[r];let i=[];for(let t in n){let r=n[t];r.groups.indexOf(e)>-1&&i.push(r)}return 0==i.length&&t.err("No node type or group '"+e+"' found"),i}(t,t.next).map((e=>(null==t.inline?t.inline=e.isInline:t.inline!=e.isInline&&t.err("Mixing inline and block content"),{type:"name",value:e})));return t.pos++,1==e.length?e[0]:{type:"choice",exprs:e}}t.err("Unexpected token '"+t.next+"'")}(t);for(;;)if(t.eat("+"))e={type:"plus",expr:e};else if(t.eat("*"))e={type:"star",expr:e};else if(t.eat("?"))e={type:"opt",expr:e};else{if(!t.eat("{"))break;e=I(t,e)}return e}function P(t){/\D/.test(t.next)&&t.err("Expected number, got '"+t.next+"'");let e=Number(t.next);return t.pos++,e}function I(t,e){let n=P(t),r=n;return t.eat(",")&&(r="}"!=t.next?P(t):-1),t.eat("}")||t.err("Unclosed braced range"),{type:"range",min:n,max:r,expr:e}}function B(t,e){return e-t}function J(t,e){let n=[];return function e(r){let i=t[r];if(1==i.length&&!i[0].term)return e(i[0].to);n.push(r);for(let t=0;t<i.length;t++){let{term:r,to:s}=i[t];r||-1!=n.indexOf(s)||e(s)}}(e),n.sort(B)}function F(t){let e=Object.create(null);for(let n in t){let r=t[n];if(!r.hasDefault)return null;e[n]=r.default}return e}function L(t,e){let n=Object.create(null);for(let r in t){let i=e&&e[r];if(void 0===i){let e=t[r];if(!e.hasDefault)throw new RangeError("No value supplied for attribute "+r);i=e.default}n[r]=i}return n}function q(t){let e=Object.create(null);if(t)for(let n in t)e[n]=new U(t[n]);return e}class H{constructor(t,e,n){this.name=t,this.schema=e,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=q(n.attrs),this.defaultAttrs=F(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==t),this.isText="text"==t}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==T.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let t in this.attrs)if(this.attrs[t].isRequired)return!0;return!1}compatibleContent(t){return this==t||this.contentMatch.compatible(t.contentMatch)}computeAttrs(t){return!t&&this.defaultAttrs?this.defaultAttrs:L(this.attrs,t)}create(t=null,e,n){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new N(this,this.computeAttrs(t),r.from(e),l.setFrom(n))}createChecked(t=null,e,n){return e=r.from(e),this.checkContent(e),new N(this,this.computeAttrs(t),e,l.setFrom(n))}createAndFill(t=null,e,n){if(t=this.computeAttrs(t),(e=r.from(e)).size){let t=this.contentMatch.fillBefore(e);if(!t)return null;e=t.append(e)}let i=this.contentMatch.matchFragment(e),s=i&&i.fillBefore(r.empty,!0);return s?new N(this,t,e.append(s),l.setFrom(n)):null}validContent(t){let e=this.contentMatch.matchFragment(t);if(!e||!e.validEnd)return!1;for(let e=0;e<t.childCount;e++)if(!this.allowsMarks(t.child(e).marks))return!1;return!0}checkContent(t){if(!this.validContent(t))throw new RangeError(`Invalid content for node ${this.name}: ${t.toString().slice(0,50)}`)}allowsMarkType(t){return null==this.markSet||this.markSet.indexOf(t)>-1}allowsMarks(t){if(null==this.markSet)return!0;for(let e=0;e<t.length;e++)if(!this.allowsMarkType(t[e].type))return!1;return!0}allowedMarks(t){if(null==this.markSet)return t;let e;for(let n=0;n<t.length;n++)this.allowsMarkType(t[n].type)?e&&e.push(t[n]):e||(e=t.slice(0,n));return e?e.length?e:l.none:t}static compile(t,e){let n=Object.create(null);t.forEach(((t,r)=>n[t]=new H(t,e,r)));let r=e.spec.topNode||"doc";if(!n[r])throw new RangeError("Schema is missing its top node type ('"+r+"')");if(!n.text)throw new RangeError("Every schema needs a 'text' type");for(let t in n.text.attrs)throw new RangeError("The text node type should not have attributes");return n}}class U{constructor(t){this.hasDefault=Object.prototype.hasOwnProperty.call(t,"default"),this.default=t.default}get isRequired(){return!this.hasDefault}}class W{constructor(t,e,n,r){this.name=t,this.rank=e,this.schema=n,this.spec=r,this.attrs=q(r.attrs),this.excluded=null;let i=F(this.attrs);this.instance=i?new l(this,i):null}create(t=null){return!t&&this.instance?this.instance:new l(this,L(this.attrs,t))}static compile(t,e){let n=Object.create(null),r=0;return t.forEach(((t,i)=>n[t]=new W(t,r++,e,i))),n}removeFromSet(t){for(var e=0;e<t.length;e++)t[e].type==this&&(t=t.slice(0,e).concat(t.slice(e+1)),e--);return t}isInSet(t){for(let e=0;e<t.length;e++)if(t[e].type==this)return t[e]}excludes(t){return this.excluded.indexOf(t)>-1}}class K{constructor(e){this.cached=Object.create(null);let n=this.spec={};for(let t in e)n[t]=e[t];n.nodes=t.from(e.nodes),n.marks=t.from(e.marks||{}),this.nodes=H.compile(this.spec.nodes,this),this.marks=W.compile(this.spec.marks,this);let r=Object.create(null);for(let t in this.nodes){if(t in this.marks)throw new RangeError(t+" can not be both a node and a mark");let e=this.nodes[t],n=e.spec.content||"",i=e.spec.marks;e.contentMatch=r[n]||(r[n]=T.parse(n,this.nodes)),e.inlineContent=e.contentMatch.inlineContent,e.markSet="_"==i?null:i?z(this,i.split(" ")):""!=i&&e.inlineContent?null:[]}for(let t in this.marks){let e=this.marks[t],n=e.spec.excludes;e.excluded=null==n?[e]:""==n?[]:z(this,n.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(t,e=null,n,r){if("string"==typeof t)t=this.nodeType(t);else{if(!(t instanceof H))throw new RangeError("Invalid node type: "+t);if(t.schema!=this)throw new RangeError("Node type from different schema used ("+t.name+")")}return t.createChecked(e,n,r)}text(t,e){let n=this.nodes.text;return new C(n,n.defaultAttrs,t,l.setFrom(e))}mark(t,e){return"string"==typeof t&&(t=this.marks[t]),t.create(e)}nodeFromJSON(t){return N.fromJSON(this,t)}markFromJSON(t){return l.fromJSON(this,t)}nodeType(t){let e=this.nodes[t];if(!e)throw new RangeError("Unknown node type: "+t);return e}}function z(t,e){let n=[];for(let r=0;r<e.length;r++){let i=e[r],s=t.marks[i],o=s;if(s)n.push(s);else for(let e in t.marks){let r=t.marks[e];("_"==i||r.spec.group&&r.spec.group.split(" ").indexOf(i)>-1)&&n.push(o=r)}if(!o)throw new SyntaxError("Unknown mark type: '"+e[r]+"'")}return n}class G{constructor(t,e){this.schema=t,this.rules=e,this.tags=[],this.styles=[],e.forEach((t=>{!function(t){return null!=t.tag}(t)?function(t){return null!=t.style}(t)&&this.styles.push(t):this.tags.push(t)})),this.normalizeLists=!this.tags.some((e=>{if(!/^(ul|ol)\b/.test(e.tag)||!e.node)return!1;let n=t.nodes[e.node];return n.contentMatch.matchType(n)}))}parse(t,e={}){let n=new Z(this,e,!1);return n.addAll(t,e.from,e.to),n.finish()}parseSlice(t,e={}){let n=new Z(this,e,!0);return n.addAll(t,e.from,e.to),h.maxOpen(n.finish())}matchTag(t,e,n){for(let r=n?this.tags.indexOf(n)+1:0;r<this.tags.length;r++){let n=this.tags[r];if(tt(t,n.tag)&&(void 0===n.namespace||t.namespaceURI==n.namespace)&&(!n.context||e.matchesContext(n.context))){if(n.getAttrs){let e=n.getAttrs(t);if(!1===e)continue;n.attrs=e||void 0}return n}}}matchStyle(t,e,n,r){for(let i=r?this.styles.indexOf(r)+1:0;i<this.styles.length;i++){let r=this.styles[i],s=r.style;if(!(0!=s.indexOf(t)||r.context&&!n.matchesContext(r.context)||s.length>t.length&&(61!=s.charCodeAt(t.length)||s.slice(t.length+1)!=e))){if(r.getAttrs){let t=r.getAttrs(e);if(!1===t)continue;r.attrs=t||void 0}return r}}}static schemaRules(t){let e=[];function n(t){let n=null==t.priority?50:t.priority,r=0;for(;r<e.length;r++){let t=e[r];if((null==t.priority?50:t.priority)<n)break}e.splice(r,0,t)}for(let e in t.marks){let r=t.marks[e].spec.parseDOM;r&&r.forEach((t=>{n(t=et(t)),t.mark||t.ignore||t.clearMark||(t.mark=e)}))}for(let e in t.nodes){let r=t.nodes[e].spec.parseDOM;r&&r.forEach((t=>{n(t=et(t)),t.node||t.ignore||t.mark||(t.node=e)}))}return e}static fromSchema(t){return t.cached.domParser||(t.cached.domParser=new G(t,G.schemaRules(t)))}}const V={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},_={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},Q={ol:!0,ul:!0};function Y(t,e,n){return null!=e?(e?1:0)|("full"===e?2:0):t&&"pre"==t.whitespace?3:-5&n}class X{constructor(t,e,n,r,i,s,o){this.type=t,this.attrs=e,this.marks=n,this.pendingMarks=r,this.solid=i,this.options=o,this.content=[],this.activeMarks=l.none,this.stashMarks=[],this.match=s||(4&o?null:t.contentMatch)}findWrapping(t){if(!this.match){if(!this.type)return[];let e=this.type.contentMatch.fillBefore(r.from(t));if(!e){let e,n=this.type.contentMatch;return(e=n.findWrapping(t.type))?(this.match=n,e):null}this.match=this.type.contentMatch.matchFragment(e)}return this.match.findWrapping(t.type)}finish(t){if(!(1&this.options)){let t,e=this.content[this.content.length-1];if(e&&e.isText&&(t=/[ \t\r\n\u000c]+$/.exec(e.text))){let n=e;e.text.length==t[0].length?this.content.pop():this.content[this.content.length-1]=n.withText(n.text.slice(0,n.text.length-t[0].length))}}let e=r.from(this.content);return!t&&this.match&&(e=e.append(this.match.fillBefore(r.empty,!0))),this.type?this.type.create(this.attrs,e,this.marks):e}popFromStashMark(t){for(let e=this.stashMarks.length-1;e>=0;e--)if(t.eq(this.stashMarks[e]))return this.stashMarks.splice(e,1)[0]}applyPending(t){for(let e=0,n=this.pendingMarks;e<n.length;e++){let r=n[e];(this.type?this.type.allowsMarkType(r.type):nt(r.type,t))&&!r.isInSet(this.activeMarks)&&(this.activeMarks=r.addToSet(this.activeMarks),this.pendingMarks=r.removeFromSet(this.pendingMarks))}}inlineContext(t){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:t.parentNode&&!V.hasOwnProperty(t.parentNode.nodeName.toLowerCase())}}class Z{constructor(t,e,n){this.parser=t,this.options=e,this.isOpen=n,this.open=0;let r,i=e.topNode,s=Y(null,e.preserveWhitespace,0)|(n?4:0);r=i?new X(i.type,i.attrs,l.none,l.none,!0,e.topMatch||i.type.contentMatch,s):new X(n?null:t.schema.topNodeType,null,l.none,l.none,!0,null,s),this.nodes=[r],this.find=e.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(t){3==t.nodeType?this.addTextNode(t):1==t.nodeType&&this.addElement(t)}withStyleRules(t,e){let n=t.getAttribute("style");if(!n)return e();let r=this.readStyles(function(t){let e,n=/\s*([\w-]+)\s*:\s*([^;]+)/g,r=[];for(;e=n.exec(t);)r.push(e[1],e[2].trim());return r}(n));if(!r)return;let[i,s]=r,o=this.top;for(let t=0;t<s.length;t++)this.removePendingMark(s[t],o);for(let t=0;t<i.length;t++)this.addPendingMark(i[t]);e();for(let t=0;t<i.length;t++)this.removePendingMark(i[t],o);for(let t=0;t<s.length;t++)this.addPendingMark(s[t])}addTextNode(t){let e=t.nodeValue,n=this.top;if(2&n.options||n.inlineContext(t)||/[^ \t\r\n\u000c]/.test(e)){if(1&n.options)e=2&n.options?e.replace(/\r\n?/g,"\n"):e.replace(/\r?\n|\r/g," ");else if(e=e.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(e)&&this.open==this.nodes.length-1){let r=n.content[n.content.length-1],i=t.previousSibling;(!r||i&&"BR"==i.nodeName||r.isText&&/[ \t\r\n\u000c]$/.test(r.text))&&(e=e.slice(1))}e&&this.insertNode(this.parser.schema.text(e)),this.findInText(t)}else this.findInside(t)}addElement(t,e){let n,r=t.nodeName.toLowerCase();Q.hasOwnProperty(r)&&this.parser.normalizeLists&&function(t){for(let e=t.firstChild,n=null;e;e=e.nextSibling){let t=1==e.nodeType?e.nodeName.toLowerCase():null;t&&Q.hasOwnProperty(t)&&n?(n.appendChild(e),e=n):"li"==t?n=e:t&&(n=null)}}(t);let i=this.options.ruleFromNode&&this.options.ruleFromNode(t)||(n=this.parser.matchTag(t,this,e));if(i?i.ignore:_.hasOwnProperty(r))this.findInside(t),this.ignoreFallback(t);else if(!i||i.skip||i.closeParent){i&&i.closeParent?this.open=Math.max(0,this.open-1):i&&i.skip.nodeType&&(t=i.skip);let e,n=this.top,s=this.needsBlock;if(V.hasOwnProperty(r))n.content.length&&n.content[0].isInline&&this.open&&(this.open--,n=this.top),e=!0,n.type||(this.needsBlock=!0);else if(!t.firstChild)return void this.leafFallback(t);i&&i.skip?this.addAll(t):this.withStyleRules(t,(()=>this.addAll(t))),e&&this.sync(n),this.needsBlock=s}else this.withStyleRules(t,(()=>{this.addElementByRule(t,i,!1===i.consuming?n:void 0)}))}leafFallback(t){"BR"==t.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(t.ownerDocument.createTextNode("\n"))}ignoreFallback(t){"BR"!=t.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"))}readStyles(t){let e=l.none,n=l.none;for(let r=0;r<t.length;r+=2)for(let i;;){let s=this.parser.matchStyle(t[r],t[r+1],this,i);if(!s)break;if(s.ignore)return null;if(s.clearMark?this.top.pendingMarks.concat(this.top.activeMarks).forEach((t=>{s.clearMark(t)&&(n=t.addToSet(n))})):e=this.parser.schema.marks[s.mark].create(s.attrs).addToSet(e),!1!==s.consuming)break;i=s}return[e,n]}addElementByRule(t,e,n){let r,i,s;e.node?(i=this.parser.schema.nodes[e.node],i.isLeaf?this.insertNode(i.create(e.attrs))||this.leafFallback(t):r=this.enter(i,e.attrs||null,e.preserveWhitespace)):(s=this.parser.schema.marks[e.mark].create(e.attrs),this.addPendingMark(s));let o=this.top;if(i&&i.isLeaf)this.findInside(t);else if(n)this.addElement(t,n);else if(e.getContent)this.findInside(t),e.getContent(t,this.parser.schema).forEach((t=>this.insertNode(t)));else{let n=t;"string"==typeof e.contentElement?n=t.querySelector(e.contentElement):"function"==typeof e.contentElement?n=e.contentElement(t):e.contentElement&&(n=e.contentElement),this.findAround(t,n,!0),this.addAll(n)}r&&this.sync(o)&&this.open--,s&&this.removePendingMark(s,o)}addAll(t,e,n){let r=e||0;for(let i=e?t.childNodes[e]:t.firstChild,s=null==n?null:t.childNodes[n];i!=s;i=i.nextSibling,++r)this.findAtPoint(t,r),this.addDOM(i);this.findAtPoint(t,r)}findPlace(t){let e,n;for(let r=this.open;r>=0;r--){let i=this.nodes[r],s=i.findWrapping(t);if(s&&(!e||e.length>s.length)&&(e=s,n=i,!s.length))break;if(i.solid)break}if(!e)return!1;this.sync(n);for(let t=0;t<e.length;t++)this.enterInner(e[t],null,!1);return!0}insertNode(t){if(t.isInline&&this.needsBlock&&!this.top.type){let t=this.textblockFromContext();t&&this.enterInner(t)}if(this.findPlace(t)){this.closeExtra();let e=this.top;e.applyPending(t.type),e.match&&(e.match=e.match.matchType(t.type));let n=e.activeMarks;for(let r=0;r<t.marks.length;r++)e.type&&!e.type.allowsMarkType(t.marks[r].type)||(n=t.marks[r].addToSet(n));return e.content.push(t.mark(n)),!0}return!1}enter(t,e,n){let r=this.findPlace(t.create(e));return r&&this.enterInner(t,e,!0,n),r}enterInner(t,e=null,n=!1,r){this.closeExtra();let i=this.top;i.applyPending(t),i.match=i.match&&i.match.matchType(t);let s=Y(t,r,i.options);4&i.options&&0==i.content.length&&(s|=4),this.nodes.push(new X(t,e,i.activeMarks,i.pendingMarks,n,null,s)),this.open++}closeExtra(t=!1){let e=this.nodes.length-1;if(e>this.open){for(;e>this.open;e--)this.nodes[e-1].content.push(this.nodes[e].finish(t));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(this.isOpen||this.options.topOpen)}sync(t){for(let e=this.open;e>=0;e--)if(this.nodes[e]==t)return this.open=e,!0;return!1}get currentPos(){this.closeExtra();let t=0;for(let e=this.open;e>=0;e--){let n=this.nodes[e].content;for(let e=n.length-1;e>=0;e--)t+=n[e].nodeSize;e&&t++}return t}findAtPoint(t,e){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==t&&this.find[n].offset==e&&(this.find[n].pos=this.currentPos)}findInside(t){if(this.find)for(let e=0;e<this.find.length;e++)null==this.find[e].pos&&1==t.nodeType&&t.contains(this.find[e].node)&&(this.find[e].pos=this.currentPos)}findAround(t,e,n){if(t!=e&&this.find)for(let r=0;r<this.find.length;r++)null==this.find[r].pos&&1==t.nodeType&&t.contains(this.find[r].node)&&e.compareDocumentPosition(this.find[r].node)&(n?2:4)&&(this.find[r].pos=this.currentPos)}findInText(t){if(this.find)for(let e=0;e<this.find.length;e++)this.find[e].node==t&&(this.find[e].pos=this.currentPos-(t.nodeValue.length-this.find[e].offset))}matchesContext(t){if(t.indexOf("|")>-1)return t.split(/\s*\|\s*/).some(this.matchesContext,this);let e=t.split("/"),n=this.options.context,r=!(this.isOpen||n&&n.parent.type!=this.nodes[0].type),i=(r?0:1)-(n?n.depth+1:0),s=(t,o)=>{for(;t>=0;t--){let l=e[t];if(""==l){if(t==e.length-1||0==t)continue;for(;o>=i;o--)if(s(t-1,o))return!0;return!1}{let t=o>0||0==o&&r?this.nodes[o].type:n&&o>=i?n.node(o-i).type:null;if(!t||t.name!=l&&-1==t.groups.indexOf(l))return!1;o--}}return!0};return s(e.length-1,this.open)}textblockFromContext(){let t=this.options.context;if(t)for(let e=t.depth;e>=0;e--){let n=t.node(e).contentMatchAt(t.indexAfter(e)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let t in this.parser.schema.nodes){let e=this.parser.schema.nodes[t];if(e.isTextblock&&e.defaultAttrs)return e}}addPendingMark(t){let e=function(t,e){for(let n=0;n<e.length;n++)if(t.eq(e[n]))return e[n]}(t,this.top.pendingMarks);e&&this.top.stashMarks.push(e),this.top.pendingMarks=t.addToSet(this.top.pendingMarks)}removePendingMark(t,e){for(let n=this.open;n>=0;n--){let r=this.nodes[n];if(r.pendingMarks.lastIndexOf(t)>-1)r.pendingMarks=t.removeFromSet(r.pendingMarks);else{r.activeMarks=t.removeFromSet(r.activeMarks);let e=r.popFromStashMark(t);e&&r.type&&r.type.allowsMarkType(e.type)&&(r.activeMarks=e.addToSet(r.activeMarks))}if(r==e)break}}}function tt(t,e){return(t.matches||t.msMatchesSelector||t.webkitMatchesSelector||t.mozMatchesSelector).call(t,e)}function et(t){let e={};for(let n in t)e[n]=t[n];return e}function nt(t,e){let n=e.schema.nodes;for(let r in n){let i=n[r];if(!i.allowsMarkType(t))continue;let s=[],o=t=>{s.push(t);for(let n=0;n<t.edgeCount;n++){let{type:r,next:i}=t.edge(n);if(r==e)return!0;if(s.indexOf(i)<0&&o(i))return!0}};if(o(i.contentMatch))return!0}}class rt{constructor(t,e){this.nodes=t,this.marks=e}serializeFragment(t,e={},n){n||(n=st(e).createDocumentFragment());let r=n,i=[];return t.forEach((t=>{if(i.length||t.marks.length){let n=0,s=0;for(;n<i.length&&s<t.marks.length;){let e=t.marks[s];if(this.marks[e.type.name]){if(!e.eq(i[n][0])||!1===e.type.spec.spanning)break;n++,s++}else s++}for(;n<i.length;)r=i.pop()[1];for(;s<t.marks.length;){let n=t.marks[s++],o=this.serializeMark(n,t.isInline,e);o&&(i.push([n,r]),r.appendChild(o.dom),r=o.contentDOM||o.dom)}}r.appendChild(this.serializeNodeInner(t,e))})),n}serializeNodeInner(t,e){let{dom:n,contentDOM:r}=rt.renderSpec(st(e),this.nodes[t.type.name](t));if(r){if(t.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(t.content,e,r)}return n}serializeNode(t,e={}){let n=this.serializeNodeInner(t,e);for(let r=t.marks.length-1;r>=0;r--){let i=this.serializeMark(t.marks[r],t.isInline,e);i&&((i.contentDOM||i.dom).appendChild(n),n=i.dom)}return n}serializeMark(t,e,n={}){let r=this.marks[t.type.name];return r&&rt.renderSpec(st(n),r(t,e))}static renderSpec(t,e,n=null){if("string"==typeof e)return{dom:t.createTextNode(e)};if(null!=e.nodeType)return{dom:e};if(e.dom&&null!=e.dom.nodeType)return e;let r,i=e[0],s=i.indexOf(" ");s>0&&(n=i.slice(0,s),i=i.slice(s+1));let o=n?t.createElementNS(n,i):t.createElement(i),l=e[1],u=1;if(l&&"object"==typeof l&&null==l.nodeType&&!Array.isArray(l)){u=2;for(let t in l)if(null!=l[t]){let e=t.indexOf(" ");e>0?o.setAttributeNS(t.slice(0,e),t.slice(e+1),l[t]):o.setAttribute(t,l[t])}}for(let i=u;i<e.length;i++){let s=e[i];if(0===s){if(i<e.length-1||i>u)throw new RangeError("Content hole must be the only child of its parent node");return{dom:o,contentDOM:o}}{let{dom:e,contentDOM:i}=rt.renderSpec(t,s,n);if(o.appendChild(e),i){if(r)throw new RangeError("Multiple content holes");r=i}}}return{dom:o,contentDOM:r}}static fromSchema(t){return t.cached.domSerializer||(t.cached.domSerializer=new rt(this.nodesFromSchema(t),this.marksFromSchema(t)))}static nodesFromSchema(t){let e=it(t.nodes);return e.text||(e.text=t=>t.text),e}static marksFromSchema(t){return it(t.marks)}}function it(t){let e={};for(let n in t){let r=t[n].spec.toDOM;r&&(e[n]=r)}return e}function st(t){return t.document||window.document}const ot=Math.pow(2,16);function lt(t,e){return t+e*ot}function ut(t){return 65535&t}class ht{constructor(t,e,n){this.pos=t,this.delInfo=e,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class ft{constructor(t,e=!1){if(this.ranges=t,this.inverted=e,!t.length&&ft.empty)return ft.empty}recover(t){let e=0,n=ut(t);if(!this.inverted)for(let t=0;t<n;t++)e+=this.ranges[3*t+2]-this.ranges[3*t+1];return this.ranges[3*n]+e+function(t){return(t-(65535&t))/ot}(t)}mapResult(t,e=1){return this._map(t,e,!1)}map(t,e=1){return this._map(t,e,!0)}_map(t,e,n){let r=0,i=this.inverted?2:1,s=this.inverted?1:2;for(let o=0;o<this.ranges.length;o+=3){let l=this.ranges[o]-(this.inverted?r:0);if(l>t)break;let u=this.ranges[o+i],h=this.ranges[o+s],f=l+u;if(t<=f){let i=l+r+((u?t==l?-1:t==f?1:e:e)<0?0:h);if(n)return i;let s=t==(e<0?l:f)?null:lt(o/3,t-l),a=t==l?2:t==f?1:4;return(e<0?t!=l:t!=f)&&(a|=8),new ht(i,a,s)}r+=h-u}return n?t+r:new ht(t+r,0,null)}touches(t,e){let n=0,r=ut(e),i=this.inverted?2:1,s=this.inverted?1:2;for(let e=0;e<this.ranges.length;e+=3){let o=this.ranges[e]-(this.inverted?n:0);if(o>t)break;let l=this.ranges[e+i];if(t<=o+l&&e==3*r)return!0;n+=this.ranges[e+s]-l}return!1}forEach(t){let e=this.inverted?2:1,n=this.inverted?1:2;for(let r=0,i=0;r<this.ranges.length;r+=3){let s=this.ranges[r],o=s-(this.inverted?i:0),l=s+(this.inverted?0:i),u=this.ranges[r+e],h=this.ranges[r+n];t(o,o+u,l,l+h),i+=h-u}}invert(){return new ft(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(t){return 0==t?ft.empty:new ft(t<0?[0,-t,0]:[0,0,t])}}ft.empty=new ft([]);class at{constructor(t=[],e,n=0,r=t.length){this.maps=t,this.mirror=e,this.from=n,this.to=r}slice(t=0,e=this.maps.length){return new at(this.maps,this.mirror,t,e)}copy(){return new at(this.maps.slice(),this.mirror&&this.mirror.slice(),this.from,this.to)}appendMap(t,e){this.to=this.maps.push(t),null!=e&&this.setMirror(this.maps.length-1,e)}appendMapping(t){for(let e=0,n=this.maps.length;e<t.maps.length;e++){let r=t.getMirror(e);this.appendMap(t.maps[e],null!=r&&r<e?n+r:void 0)}}getMirror(t){if(this.mirror)for(let e=0;e<this.mirror.length;e++)if(this.mirror[e]==t)return this.mirror[e+(e%2?-1:1)]}setMirror(t,e){this.mirror||(this.mirror=[]),this.mirror.push(t,e)}appendMappingInverted(t){for(let e=t.maps.length-1,n=this.maps.length+t.maps.length;e>=0;e--){let r=t.getMirror(e);this.appendMap(t.maps[e].invert(),null!=r&&r>e?n-r-1:void 0)}}invert(){let t=new at;return t.appendMappingInverted(this),t}map(t,e=1){if(this.mirror)return this._map(t,e,!0);for(let n=this.from;n<this.to;n++)t=this.maps[n].map(t,e);return t}mapResult(t,e=1){return this._map(t,e,!1)}_map(t,e,n){let r=0;for(let n=this.from;n<this.to;n++){let i=this.maps[n].mapResult(t,e);if(null!=i.recover){let e=this.getMirror(n);if(null!=e&&e>n&&e<this.to){n=e,t=this.maps[e].recover(i.recover);continue}}r|=i.delInfo,t=i.pos}return n?t:new ht(t,r,null)}}const ct=Object.create(null);class dt{getMap(){return ft.empty}merge(t){return null}static fromJSON(t,e){if(!e||!e.stepType)throw new RangeError("Invalid input for Step.fromJSON");let n=ct[e.stepType];if(!n)throw new RangeError(`No step type ${e.stepType} defined`);return n.fromJSON(t,e)}static jsonID(t,e){if(t in ct)throw new RangeError("Duplicate use of step JSON ID "+t);return ct[t]=e,e.prototype.jsonID=t,e}}class pt{constructor(t,e){this.doc=t,this.failed=e}static ok(t){return new pt(t,null)}static fail(t){return new pt(null,t)}static fromReplace(t,e,n,r){try{return pt.ok(t.replace(e,n,r))}catch(t){if(t instanceof u)return pt.fail(t.message);throw t}}}function mt(t,e,n){let i=[];for(let r=0;r<t.childCount;r++){let s=t.child(r);s.content.size&&(s=s.copy(mt(s.content,e,s))),s.isInline&&(s=e(s,n,r)),i.push(s)}return r.fromArray(i)}class wt extends dt{constructor(t,e,n){super(),this.from=t,this.to=e,this.mark=n}apply(t){let e=t.slice(this.from,this.to),n=t.resolve(this.from),r=n.node(n.sharedDepth(this.to)),i=new h(mt(e.content,((t,e)=>t.isAtom&&e.type.allowsMarkType(this.mark.type)?t.mark(this.mark.addToSet(t.marks)):t),r),e.openStart,e.openEnd);return pt.fromReplace(t,this.from,this.to,i)}invert(){return new vt(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deleted&&n.deleted||e.pos>=n.pos?null:new wt(e.pos,n.pos,this.mark)}merge(t){return t instanceof wt&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new wt(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new wt(e.from,e.to,t.markFromJSON(e.mark))}}dt.jsonID("addMark",wt);class vt extends dt{constructor(t,e,n){super(),this.from=t,this.to=e,this.mark=n}apply(t){let e=t.slice(this.from,this.to),n=new h(mt(e.content,(t=>t.mark(this.mark.removeFromSet(t.marks))),t),e.openStart,e.openEnd);return pt.fromReplace(t,this.from,this.to,n)}invert(){return new wt(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deleted&&n.deleted||e.pos>=n.pos?null:new vt(e.pos,n.pos,this.mark)}merge(t){return t instanceof vt&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new vt(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new vt(e.from,e.to,t.markFromJSON(e.mark))}}dt.jsonID("removeMark",vt);class gt extends dt{constructor(t,e){super(),this.pos=t,this.mark=e}apply(t){let e=t.nodeAt(this.pos);if(!e)return pt.fail("No node at mark step's position");let n=e.type.create(e.attrs,null,this.mark.addToSet(e.marks));return pt.fromReplace(t,this.pos,this.pos+1,new h(r.from(n),0,e.isLeaf?0:1))}invert(t){let e=t.nodeAt(this.pos);if(e){let t=this.mark.addToSet(e.marks);if(t.length==e.marks.length){for(let n=0;n<e.marks.length;n++)if(!e.marks[n].isInSet(t))return new gt(this.pos,e.marks[n]);return new gt(this.pos,this.mark)}}return new bt(this.pos,this.mark)}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new gt(e.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if("number"!=typeof e.pos)throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new gt(e.pos,t.markFromJSON(e.mark))}}dt.jsonID("addNodeMark",gt);class bt extends dt{constructor(t,e){super(),this.pos=t,this.mark=e}apply(t){let e=t.nodeAt(this.pos);if(!e)return pt.fail("No node at mark step's position");let n=e.type.create(e.attrs,null,this.mark.removeFromSet(e.marks));return pt.fromReplace(t,this.pos,this.pos+1,new h(r.from(n),0,e.isLeaf?0:1))}invert(t){let e=t.nodeAt(this.pos);return e&&this.mark.isInSet(e.marks)?new gt(this.pos,this.mark):this}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new bt(e.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if("number"!=typeof e.pos)throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new bt(e.pos,t.markFromJSON(e.mark))}}dt.jsonID("removeNodeMark",bt);class yt extends dt{constructor(t,e,n,r=!1){super(),this.from=t,this.to=e,this.slice=n,this.structure=r}apply(t){return this.structure&&xt(t,this.from,this.to)?pt.fail("Structure replace would overwrite content"):pt.fromReplace(t,this.from,this.to,this.slice)}getMap(){return new ft([this.from,this.to-this.from,this.slice.size])}invert(t){return new yt(this.from,this.from+this.slice.size,t.slice(this.from,this.to))}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deletedAcross&&n.deletedAcross?null:new yt(e.pos,Math.max(e.pos,n.pos),this.slice)}merge(t){if(!(t instanceof yt)||t.structure||this.structure)return null;if(this.from+this.slice.size!=t.from||this.slice.openEnd||t.slice.openStart){if(t.to!=this.from||this.slice.openStart||t.slice.openEnd)return null;{let e=this.slice.size+t.slice.size==0?h.empty:new h(t.slice.content.append(this.slice.content),t.slice.openStart,this.slice.openEnd);return new yt(t.from,this.to,e,this.structure)}}{let e=this.slice.size+t.slice.size==0?h.empty:new h(this.slice.content.append(t.slice.content),this.slice.openStart,t.slice.openEnd);return new yt(this.from,this.to+(t.to-t.from),e,this.structure)}}toJSON(){let t={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new yt(e.from,e.to,h.fromJSON(t,e.slice),!!e.structure)}}dt.jsonID("replace",yt);class kt extends dt{constructor(t,e,n,r,i,s,o=!1){super(),this.from=t,this.to=e,this.gapFrom=n,this.gapTo=r,this.slice=i,this.insert=s,this.structure=o}apply(t){if(this.structure&&(xt(t,this.from,this.gapFrom)||xt(t,this.gapTo,this.to)))return pt.fail("Structure gap-replace would overwrite content");let e=t.slice(this.gapFrom,this.gapTo);if(e.openStart||e.openEnd)return pt.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,e.content);return n?pt.fromReplace(t,this.from,this.to,n):pt.fail("Content does not fit in gap")}getMap(){return new ft([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(t){let e=this.gapTo-this.gapFrom;return new kt(this.from,this.from+this.slice.size+e,this.from+this.insert,this.from+this.insert+e,t.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1),r=t.map(this.gapFrom,-1),i=t.map(this.gapTo,1);return e.deletedAcross&&n.deletedAcross||r<e.pos||i>n.pos?null:new kt(e.pos,n.pos,r,i,this.slice,this.insert,this.structure)}toJSON(){let t={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to||"number"!=typeof e.gapFrom||"number"!=typeof e.gapTo||"number"!=typeof e.insert)throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new kt(e.from,e.to,e.gapFrom,e.gapTo,h.fromJSON(t,e.slice),e.insert,!!e.structure)}}function xt(t,e,n){let r=t.resolve(e),i=n-e,s=r.depth;for(;i>0&&s>0&&r.indexAfter(s)==r.node(s).childCount;)s--,i--;if(i>0){let t=r.node(s).maybeChild(r.indexAfter(s));for(;i>0;){if(!t||t.isLeaf)return!0;t=t.firstChild,i--}}return!1}function Mt(t,e,n){return(0==e||t.canReplace(e,t.childCount))&&(n==t.childCount||t.canReplace(0,n))}function Ot(t){let e=t.parent.content.cutByIndex(t.startIndex,t.endIndex);for(let n=t.depth;;--n){let r=t.$from.node(n),i=t.$from.index(n),s=t.$to.indexAfter(n);if(n<t.depth&&r.canReplace(i,s,e))return n;if(0==n||r.type.spec.isolating||!Mt(r,i,s))break}return null}function St(t,e,n=null,r=t){let i=function(t,e){let{parent:n,startIndex:r,endIndex:i}=t,s=n.contentMatchAt(r).findWrapping(e);return s&&n.canReplaceWith(r,i,s.length?s[0]:e)?s:null}(t,e),s=i&&function(t,e){let{parent:n,startIndex:r,endIndex:i}=t,s=n.child(r),o=e.contentMatch.findWrapping(s.type);if(!o)return null;let l=(o.length?o[o.length-1]:e).contentMatch;for(let t=r;l&&t<i;t++)l=l.matchType(n.child(t).type);return l&&l.validEnd?o:null}(r,e);return s?i.map(Et).concat({type:e,attrs:n}).concat(s.map(Et)):null}function Et(t){return{type:t,attrs:null}}function Nt(t,e,n=1,r){let i=t.resolve(e),s=i.depth-n,o=r&&r[r.length-1]||i.parent;if(s<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!o.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let t=i.depth-1,e=n-2;t>s;t--,e--){let n=i.node(t),s=i.index(t);if(n.type.spec.isolating)return!1;let o=n.content.cutByIndex(s,n.childCount),l=r&&r[e+1];l&&(o=o.replaceChild(0,l.type.create(l.attrs)));let u=r&&r[e]||n;if(!n.canReplace(s+1,n.childCount)||!u.type.validContent(o))return!1}let l=i.indexAfter(s),u=r&&r[0];return i.node(s).canReplaceWith(l,l,u?u.type:i.node(s+1).type)}function Ct(t,e){let n=t.resolve(e),r=n.index();return Rt(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(r,r+1)}function Rt(t,e){return!(!t||!e||t.isLeaf||!t.canAppend(e))}function Tt(t,e,n=-1){let r=t.resolve(e);for(let t=r.depth;;t--){let i,s,o=r.index(t);if(t==r.depth?(i=r.nodeBefore,s=r.nodeAfter):n>0?(i=r.node(t+1),o++,s=r.node(t).maybeChild(o)):(i=r.node(t).maybeChild(o-1),s=r.node(t+1)),i&&!i.isTextblock&&Rt(i,s)&&r.node(t).canReplace(o,o+1))return e;if(0==t)break;e=n<0?r.before(t):r.after(t)}}function jt(t,e,n){let r=t.resolve(e);if(!n.content.size)return e;let i=n.content;for(let t=0;t<n.openStart;t++)i=i.firstChild.content;for(let t=1;t<=(0==n.openStart&&n.size?2:1);t++)for(let e=r.depth;e>=0;e--){let n=e==r.depth?0:r.pos<=(r.start(e+1)+r.end(e+1))/2?-1:1,s=r.index(e)+(n>0?1:0),o=r.node(e),l=!1;if(1==t)l=o.canReplace(s,s,i);else{let t=o.contentMatchAt(s).findWrapping(i.firstChild.type);l=t&&o.canReplaceWith(s,s,t[0])}if(l)return 0==n?r.pos:n<0?r.before(e+1):r.after(e+1)}return null}function At(t,e,n=e,r=h.empty){if(e==n&&!r.size)return null;let i=t.resolve(e),s=t.resolve(n);return $t(i,s,r)?new yt(e,n,r):new Dt(i,s,r).fit()}function $t(t,e,n){return!n.openStart&&!n.openEnd&&t.start()==e.start()&&t.parent.canReplace(t.index(),e.index(),n.content)}dt.jsonID("replaceAround",kt);class Dt{constructor(t,e,n){this.$from=t,this.$to=e,this.unplaced=n,this.frontier=[],this.placed=r.empty;for(let e=0;e<=t.depth;e++){let n=t.node(e);this.frontier.push({type:n.type,match:n.contentMatchAt(t.indexAfter(e))})}for(let e=t.depth;e>0;e--)this.placed=r.from(t.node(e).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let t=this.findFittable();t?this.placeNodes(t):this.openMore()||this.dropNode()}let t=this.mustMoveInline(),e=this.placed.size-this.depth-this.$from.depth,n=this.$from,r=this.close(t<0?this.$to:n.doc.resolve(t));if(!r)return null;let i=this.placed,s=n.depth,o=r.depth;for(;s&&o&&1==i.childCount;)i=i.firstChild.content,s--,o--;let l=new h(i,s,o);return t>-1?new kt(n.pos,t,this.$to.pos,this.$to.end(),l,e):l.size||n.pos!=this.$to.pos?new yt(n.pos,r.pos,l):null}findFittable(){let t=this.unplaced.openStart;for(let e=this.unplaced.content,n=0,r=this.unplaced.openEnd;n<t;n++){let i=e.firstChild;if(e.childCount>1&&(r=0),i.type.spec.isolating&&r<=n){t=n;break}e=i.content}for(let e=1;e<=2;e++)for(let n=1==e?t:this.unplaced.openStart;n>=0;n--){let t,i=null;n?(i=Bt(this.unplaced.content,n-1).firstChild,t=i.content):t=this.unplaced.content;let s=t.firstChild;for(let t=this.depth;t>=0;t--){let o,{type:l,match:u}=this.frontier[t],h=null;if(1==e&&(s?u.matchType(s.type)||(h=u.fillBefore(r.from(s),!1)):i&&l.compatibleContent(i.type)))return{sliceDepth:n,frontierDepth:t,parent:i,inject:h};if(2==e&&s&&(o=u.findWrapping(s.type)))return{sliceDepth:n,frontierDepth:t,parent:i,wrap:o};if(i&&u.matchType(i.type))break}}}openMore(){let{content:t,openStart:e,openEnd:n}=this.unplaced,r=Bt(t,e);return!(!r.childCount||r.firstChild.isLeaf||(this.unplaced=new h(t,e+1,Math.max(n,r.size+e>=t.size-n?e+1:0)),0))}dropNode(){let{content:t,openStart:e,openEnd:n}=this.unplaced,r=Bt(t,e);if(r.childCount<=1&&e>0){let i=t.size-e<=e+r.size;this.unplaced=new h(Pt(t,e-1,1),e-1,i?e-1:n)}else this.unplaced=new h(Pt(t,e,1),e,n)}placeNodes({sliceDepth:t,frontierDepth:e,parent:n,inject:i,wrap:s}){for(;this.depth>e;)this.closeFrontierNode();if(s)for(let t=0;t<s.length;t++)this.openFrontierNode(s[t]);let o=this.unplaced,l=n?n.content:o.content,u=o.openStart-t,f=0,a=[],{match:c,type:d}=this.frontier[e];if(i){for(let t=0;t<i.childCount;t++)a.push(i.child(t));c=c.matchFragment(i)}let p=l.size+t-(o.content.size-o.openEnd);for(;f<l.childCount;){let t=l.child(f),e=c.matchType(t.type);if(!e)break;f++,(f>1||0==u||t.content.size)&&(c=e,a.push(Jt(t.mark(d.allowedMarks(t.marks)),1==f?u:0,f==l.childCount?p:-1)))}let m=f==l.childCount;m||(p=-1),this.placed=It(this.placed,e,r.from(a)),this.frontier[e].match=c,m&&p<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let t=0,e=l;t<p;t++){let t=e.lastChild;this.frontier.push({type:t.type,match:t.contentMatchAt(t.childCount)}),e=t.content}this.unplaced=m?0==t?h.empty:new h(Pt(o.content,t-1,1),t-1,p<0?o.openEnd:t-1):new h(Pt(o.content,t,f),o.openStart,o.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let t,e=this.frontier[this.depth];if(!e.type.isTextblock||!Ft(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return-1;let{depth:n}=this.$to,r=this.$to.after(n);for(;n>1&&r==this.$to.end(--n);)++r;return r}findCloseLevel(t){t:for(let e=Math.min(this.depth,t.depth);e>=0;e--){let{match:n,type:r}=this.frontier[e],i=e<t.depth&&t.end(e+1)==t.pos+(t.depth-(e+1)),s=Ft(t,e,r,n,i);if(s){for(let n=e-1;n>=0;n--){let{match:e,type:r}=this.frontier[n],i=Ft(t,n,r,e,!0);if(!i||i.childCount)continue t}return{depth:e,fit:s,move:i?t.doc.resolve(t.after(e+1)):t}}}}close(t){let e=this.findCloseLevel(t);if(!e)return null;for(;this.depth>e.depth;)this.closeFrontierNode();e.fit.childCount&&(this.placed=It(this.placed,e.depth,e.fit)),t=e.move;for(let n=e.depth+1;n<=t.depth;n++){let e=t.node(n),r=e.type.contentMatch.fillBefore(e.content,!0,t.index(n));this.openFrontierNode(e.type,e.attrs,r)}return t}openFrontierNode(t,e=null,n){let i=this.frontier[this.depth];i.match=i.match.matchType(t),this.placed=It(this.placed,this.depth,r.from(t.create(e,n))),this.frontier.push({type:t,match:t.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(r.empty,!0);t.childCount&&(this.placed=It(this.placed,this.frontier.length,t))}}function Pt(t,e,n){return 0==e?t.cutByIndex(n,t.childCount):t.replaceChild(0,t.firstChild.copy(Pt(t.firstChild.content,e-1,n)))}function It(t,e,n){return 0==e?t.append(n):t.replaceChild(t.childCount-1,t.lastChild.copy(It(t.lastChild.content,e-1,n)))}function Bt(t,e){for(let n=0;n<e;n++)t=t.firstChild.content;return t}function Jt(t,e,n){if(e<=0)return t;let i=t.content;return e>1&&(i=i.replaceChild(0,Jt(i.firstChild,e-1,1==i.childCount?n-1:0))),e>0&&(i=t.type.contentMatch.fillBefore(i).append(i),n<=0&&(i=i.append(t.type.contentMatch.matchFragment(i).fillBefore(r.empty,!0)))),t.copy(i)}function Ft(t,e,n,r,i){let s=t.node(e),o=i?t.indexAfter(e):t.index(e);if(o==s.childCount&&!n.compatibleContent(s.type))return null;let l=r.fillBefore(s.content,!0,o);return l&&!function(t,e,n){for(let r=n;r<e.childCount;r++)if(!t.allowsMarks(e.child(r).marks))return!0;return!1}(n,s.content,o)?l:null}function Lt(t,e,n,i,s){if(e<n){let r=t.firstChild;t=t.replaceChild(0,r.copy(Lt(r.content,e+1,n,i,r)))}if(e>i){let e=s.contentMatchAt(0),n=e.fillBefore(t).append(t);t=n.append(e.matchFragment(n).fillBefore(r.empty,!0))}return t}function qt(t,e){let n=[];for(let r=Math.min(t.depth,e.depth);r>=0;r--){let i=t.start(r);if(i<t.pos-(t.depth-r)||e.end(r)>e.pos+(e.depth-r)||t.node(r).type.spec.isolating||e.node(r).type.spec.isolating)break;(i==e.start(r)||r==t.depth&&r==e.depth&&t.parent.inlineContent&&e.parent.inlineContent&&r&&e.start(r-1)==i-1)&&n.push(r)}return n}class Ht extends dt{constructor(t,e,n){super(),this.pos=t,this.attr=e,this.value=n}apply(t){let e=t.nodeAt(this.pos);if(!e)return pt.fail("No node at attribute step's position");let n=Object.create(null);for(let t in e.attrs)n[t]=e.attrs[t];n[this.attr]=this.value;let i=e.type.create(n,null,e.marks);return pt.fromReplace(t,this.pos,this.pos+1,new h(r.from(i),0,e.isLeaf?0:1))}getMap(){return ft.empty}invert(t){return new Ht(this.pos,this.attr,t.nodeAt(this.pos).attrs[this.attr])}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new Ht(e.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(t,e){if("number"!=typeof e.pos||"string"!=typeof e.attr)throw new RangeError("Invalid input for AttrStep.fromJSON");return new Ht(e.pos,e.attr,e.value)}}dt.jsonID("attr",Ht);class Ut extends dt{constructor(t,e){super(),this.attr=t,this.value=e}apply(t){let e=Object.create(null);for(let n in t.attrs)e[n]=t.attrs[n];e[this.attr]=this.value;let n=t.type.create(e,t.content,t.marks);return pt.ok(n)}getMap(){return ft.empty}invert(t){return new Ut(this.attr,t.attrs[this.attr])}map(t){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(t,e){if("string"!=typeof e.attr)throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new Ut(e.attr,e.value)}}dt.jsonID("docAttr",Ut);let Wt=class extends Error{};Wt=function t(e){let n=Error.call(this,e);return n.__proto__=t.prototype,n},(Wt.prototype=Object.create(Error.prototype)).constructor=Wt,Wt.prototype.name="TransformError";class Kt{constructor(t){this.doc=t,this.steps=[],this.docs=[],this.mapping=new at}get before(){return this.docs.length?this.docs[0]:this.doc}step(t){let e=this.maybeStep(t);if(e.failed)throw new Wt(e.failed);return this}maybeStep(t){let e=t.apply(this.doc);return e.failed||this.addStep(t,e.doc),e}get docChanged(){return this.steps.length>0}addStep(t,e){this.docs.push(this.doc),this.steps.push(t),this.mapping.appendMap(t.getMap()),this.doc=e}replace(t,e=t,n=h.empty){let r=At(this.doc,t,e,n);return r&&this.step(r),this}replaceWith(t,e,n){return this.replace(t,e,new h(r.from(n),0,0))}delete(t,e){return this.replace(t,e,h.empty)}insert(t,e){return this.replaceWith(t,t,e)}replaceRange(t,e,n){return function(t,e,n,r){if(!r.size)return t.deleteRange(e,n);let i=t.doc.resolve(e),s=t.doc.resolve(n);if($t(i,s,r))return t.step(new yt(e,n,r));let o=qt(i,t.doc.resolve(n));0==o[o.length-1]&&o.pop();let l=-(i.depth+1);o.unshift(l);for(let t=i.depth,e=i.pos-1;t>0;t--,e--){let n=i.node(t).type.spec;if(n.defining||n.definingAsContext||n.isolating)break;o.indexOf(t)>-1?l=t:i.before(t)==e&&o.splice(1,0,-t)}let u=o.indexOf(l),f=[],a=r.openStart;for(let t=r.content,e=0;;e++){let n=t.firstChild;if(f.push(n),e==r.openStart)break;t=n.content}for(let t=a-1;t>=0;t--){let e=f[t],n=(c=e.type).spec.defining||c.spec.definingForContent;if(n&&!e.sameMarkup(i.node(Math.abs(l)-1)))a=t;else if(n||!e.type.isTextblock)break}var c;for(let e=r.openStart;e>=0;e--){let l=(e+a+1)%(r.openStart+1),c=f[l];if(c)for(let e=0;e<o.length;e++){let f=o[(e+u)%o.length],a=!0;f<0&&(a=!1,f=-f);let d=i.node(f-1),p=i.index(f-1);if(d.canReplaceWith(p,p,c.type,c.marks))return t.replace(i.before(f),a?s.after(f):n,new h(Lt(r.content,0,r.openStart,l),l,r.openEnd))}}let d=t.steps.length;for(let l=o.length-1;l>=0&&(t.replace(e,n,r),!(t.steps.length>d));l--){let t=o[l];t<0||(e=i.before(t),n=s.after(t))}}(this,t,e,n),this}replaceRangeWith(t,e,n){return function(t,e,n,i){if(!i.isInline&&e==n&&t.doc.resolve(e).parent.content.size){let r=function(t,e,n){let r=t.resolve(e);if(r.parent.canReplaceWith(r.index(),r.index(),n))return e;if(0==r.parentOffset)for(let t=r.depth-1;t>=0;t--){let e=r.index(t);if(r.node(t).canReplaceWith(e,e,n))return r.before(t+1);if(e>0)return null}if(r.parentOffset==r.parent.content.size)for(let t=r.depth-1;t>=0;t--){let e=r.indexAfter(t);if(r.node(t).canReplaceWith(e,e,n))return r.after(t+1);if(e<r.node(t).childCount)return null}return null}(t.doc,e,i.type);null!=r&&(e=n=r)}t.replaceRange(e,n,new h(r.from(i),0,0))}(this,t,e,n),this}deleteRange(t,e){return function(t,e,n){let r=t.doc.resolve(e),i=t.doc.resolve(n),s=qt(r,i);for(let e=0;e<s.length;e++){let n=s[e],o=e==s.length-1;if(o&&0==n||r.node(n).type.contentMatch.validEnd)return t.delete(r.start(n),i.end(n));if(n>0&&(o||r.node(n-1).canReplace(r.index(n-1),i.indexAfter(n-1))))return t.delete(r.before(n),i.after(n))}for(let s=1;s<=r.depth&&s<=i.depth;s++)if(e-r.start(s)==r.depth-s&&n>r.end(s)&&i.end(s)-n!=i.depth-s)return t.delete(r.before(s),n);t.delete(e,n)}(this,t,e),this}lift(t,e){return function(t,e,n){let{$from:i,$to:s,depth:o}=e,l=i.before(o+1),u=s.after(o+1),f=l,a=u,c=r.empty,d=0;for(let t=o,e=!1;t>n;t--)e||i.index(t)>0?(e=!0,c=r.from(i.node(t).copy(c)),d++):f--;let p=r.empty,m=0;for(let t=o,e=!1;t>n;t--)e||s.after(t+1)<s.end(t)?(e=!0,p=r.from(s.node(t).copy(p)),m++):a++;t.step(new kt(f,a,l,u,new h(c.append(p),d,m),c.size-d,!0))}(this,t,e),this}join(t,e=1){return function(t,e,n){let r=new yt(e-n,e+n,h.empty,!0);t.step(r)}(this,t,e),this}wrap(t,e){return function(t,e,n){let i=r.empty;for(let t=n.length-1;t>=0;t--){if(i.size){let e=n[t].type.contentMatch.matchFragment(i);if(!e||!e.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}i=r.from(n[t].type.create(n[t].attrs,i))}let s=e.start,o=e.end;t.step(new kt(s,o,s,o,new h(i,0,0),n.length,!0))}(this,t,e),this}setBlockType(t,e=t,n,i=null){return function(t,e,n,i,s){if(!i.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let o=t.steps.length;t.doc.nodesBetween(e,n,((e,n)=>{if(e.isTextblock&&!e.hasMarkup(i,s)&&function(t,e,n){let r=t.resolve(e),i=r.index();return r.parent.canReplaceWith(i,i+1,n)}(t.doc,t.mapping.slice(o).map(n),i)){t.clearIncompatible(t.mapping.slice(o).map(n,1),i);let l=t.mapping.slice(o),u=l.map(n,1),f=l.map(n+e.nodeSize,1);return t.step(new kt(u,f,u+1,f-1,new h(r.from(i.create(s,null,e.marks)),0,0),1,!0)),!1}}))}(this,t,e,n,i),this}setNodeMarkup(t,e,n=null,i){return function(t,e,n,i,s){let o=t.doc.nodeAt(e);if(!o)throw new RangeError("No node at given position");n||(n=o.type);let l=n.create(i,null,s||o.marks);if(o.isLeaf)return t.replaceWith(e,e+o.nodeSize,l);if(!n.validContent(o.content))throw new RangeError("Invalid content for node type "+n.name);t.step(new kt(e,e+o.nodeSize,e+1,e+o.nodeSize-1,new h(r.from(l),0,0),1,!0))}(this,t,e,n,i),this}setNodeAttribute(t,e,n){return this.step(new Ht(t,e,n)),this}setDocAttribute(t,e){return this.step(new Ut(t,e)),this}addNodeMark(t,e){return this.step(new gt(t,e)),this}removeNodeMark(t,e){if(!(e instanceof l)){let n=this.doc.nodeAt(t);if(!n)throw new RangeError("No node at position "+t);if(!(e=e.isInSet(n.marks)))return this}return this.step(new bt(t,e)),this}split(t,e=1,n){return function(t,e,n=1,i){let s=t.doc.resolve(e),o=r.empty,l=r.empty;for(let t=s.depth,e=s.depth-n,u=n-1;t>e;t--,u--){o=r.from(s.node(t).copy(o));let e=i&&i[u];l=r.from(e?e.type.create(e.attrs,l):s.node(t).copy(l))}t.step(new yt(e,e,new h(o.append(l),n,n),!0))}(this,t,e,n),this}addMark(t,e,n){return function(t,e,n,r){let i,s,o=[],l=[];t.doc.nodesBetween(e,n,((t,u,h)=>{if(!t.isInline)return;let f=t.marks;if(!r.isInSet(f)&&h.type.allowsMarkType(r.type)){let h=Math.max(u,e),a=Math.min(u+t.nodeSize,n),c=r.addToSet(f);for(let t=0;t<f.length;t++)f[t].isInSet(c)||(i&&i.to==h&&i.mark.eq(f[t])?i.to=a:o.push(i=new vt(h,a,f[t])));s&&s.to==h?s.to=a:l.push(s=new wt(h,a,r))}})),o.forEach((e=>t.step(e))),l.forEach((e=>t.step(e)))}(this,t,e,n),this}removeMark(t,e,n){return function(t,e,n,r){let i=[],s=0;t.doc.nodesBetween(e,n,((t,o)=>{if(!t.isInline)return;s++;let l=null;if(r instanceof W){let e,n=t.marks;for(;e=r.isInSet(n);)(l||(l=[])).push(e),n=e.removeFromSet(n)}else r?r.isInSet(t.marks)&&(l=[r]):l=t.marks;if(l&&l.length){let r=Math.min(o+t.nodeSize,n);for(let t=0;t<l.length;t++){let n,u=l[t];for(let t=0;t<i.length;t++){let e=i[t];e.step==s-1&&u.eq(i[t].style)&&(n=e)}n?(n.to=r,n.step=s):i.push({style:u,from:Math.max(o,e),to:r,step:s})}}})),i.forEach((e=>t.step(new vt(e.from,e.to,e.style))))}(this,t,e,n),this}clearIncompatible(t,e,n){return function(t,e,n,i=n.contentMatch){let s=t.doc.nodeAt(e),o=[],l=e+1;for(let e=0;e<s.childCount;e++){let u=s.child(e),f=l+u.nodeSize,a=i.matchType(u.type);if(a){i=a;for(let e=0;e<u.marks.length;e++)n.allowsMarkType(u.marks[e].type)||t.step(new vt(l,f,u.marks[e]));if(u.isText&&!n.spec.code){let t,e,i=/\r?\n|\r/g;for(;t=i.exec(u.text);)e||(e=new h(r.from(n.schema.text(" ",n.allowedMarks(u.marks))),0,0)),o.push(new yt(l+t.index,l+t.index+t[0].length,e))}}else o.push(new yt(l,f,h.empty));l=f}if(!i.validEnd){let e=i.fillBefore(r.empty,!0);t.replace(l,l,new h(e,0,0))}for(let e=o.length-1;e>=0;e--)t.step(o[e])}(this,t,e,n),this}}const zt=Object.create(null);class Gt{constructor(t,e,n){this.$anchor=t,this.$head=e,this.ranges=n||[new Vt(t.min(e),t.max(e))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let t=this.ranges;for(let e=0;e<t.length;e++)if(t[e].$from.pos!=t[e].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(t,e=h.empty){let n=e.content.lastChild,r=null;for(let t=0;t<e.openEnd;t++)r=n,n=n.lastChild;let i=t.steps.length,s=this.ranges;for(let o=0;o<s.length;o++){let{$from:l,$to:u}=s[o],f=t.mapping.slice(i);t.replaceRange(f.map(l.pos),f.map(u.pos),o?h.empty:e),0==o&&ie(t,i,(n?n.isInline:r&&r.isTextblock)?-1:1)}}replaceWith(t,e){let n=t.steps.length,r=this.ranges;for(let i=0;i<r.length;i++){let{$from:s,$to:o}=r[i],l=t.mapping.slice(n),u=l.map(s.pos),h=l.map(o.pos);i?t.deleteRange(u,h):(t.replaceRangeWith(u,h,e),ie(t,n,e.isInline?-1:1))}}static findFrom(t,e,n=!1){let r=t.parent.inlineContent?new Yt(t):re(t.node(0),t.parent,t.pos,t.index(),e,n);if(r)return r;for(let r=t.depth-1;r>=0;r--){let i=e<0?re(t.node(0),t.node(r),t.before(r+1),t.index(r),e,n):re(t.node(0),t.node(r),t.after(r+1),t.index(r)+1,e,n);if(i)return i}return null}static near(t,e=1){return this.findFrom(t,e)||this.findFrom(t,-e)||new ee(t.node(0))}static atStart(t){return re(t,t,0,0,1)||new ee(t)}static atEnd(t){return re(t,t,t.content.size,t.childCount,-1)||new ee(t)}static fromJSON(t,e){if(!e||!e.type)throw new RangeError("Invalid input for Selection.fromJSON");let n=zt[e.type];if(!n)throw new RangeError(`No selection type ${e.type} defined`);return n.fromJSON(t,e)}static jsonID(t,e){if(t in zt)throw new RangeError("Duplicate use of selection JSON ID "+t);return zt[t]=e,e.prototype.jsonID=t,e}getBookmark(){return Yt.between(this.$anchor,this.$head).getBookmark()}}Gt.prototype.visible=!0;class Vt{constructor(t,e){this.$from=t,this.$to=e}}let _t=!1;function Qt(t){_t||t.parent.inlineContent||(_t=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+t.parent.type.name+")"))}class Yt extends Gt{constructor(t,e=t){Qt(t),Qt(e),super(t,e)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(t,e){let n=t.resolve(e.map(this.head));if(!n.parent.inlineContent)return Gt.near(n);let r=t.resolve(e.map(this.anchor));return new Yt(r.parent.inlineContent?r:n,n)}replace(t,e=h.empty){if(super.replace(t,e),e==h.empty){let e=this.$from.marksAcross(this.$to);e&&t.ensureMarks(e)}}eq(t){return t instanceof Yt&&t.anchor==this.anchor&&t.head==this.head}getBookmark(){return new Xt(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(t,e){if("number"!=typeof e.anchor||"number"!=typeof e.head)throw new RangeError("Invalid input for TextSelection.fromJSON");return new Yt(t.resolve(e.anchor),t.resolve(e.head))}static create(t,e,n=e){let r=t.resolve(e);return new this(r,n==e?r:t.resolve(n))}static between(t,e,n){let r=t.pos-e.pos;if(n&&!r||(n=r>=0?1:-1),!e.parent.inlineContent){let t=Gt.findFrom(e,n,!0)||Gt.findFrom(e,-n,!0);if(!t)return Gt.near(e,n);e=t.$head}return t.parent.inlineContent||(0==r||(t=(Gt.findFrom(t,-n,!0)||Gt.findFrom(t,n,!0)).$anchor).pos<e.pos!=r<0)&&(t=e),new Yt(t,e)}}Gt.jsonID("text",Yt);class Xt{constructor(t,e){this.anchor=t,this.head=e}map(t){return new Xt(t.map(this.anchor),t.map(this.head))}resolve(t){return Yt.between(t.resolve(this.anchor),t.resolve(this.head))}}class Zt extends Gt{constructor(t){let e=t.nodeAfter,n=t.node(0).resolve(t.pos+e.nodeSize);super(t,n),this.node=e}map(t,e){let{deleted:n,pos:r}=e.mapResult(this.anchor),i=t.resolve(r);return n?Gt.near(i):new Zt(i)}content(){return new h(r.from(this.node),0,0)}eq(t){return t instanceof Zt&&t.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new te(this.anchor)}static fromJSON(t,e){if("number"!=typeof e.anchor)throw new RangeError("Invalid input for NodeSelection.fromJSON");return new Zt(t.resolve(e.anchor))}static create(t,e){return new Zt(t.resolve(e))}static isSelectable(t){return!t.isText&&!1!==t.type.spec.selectable}}Zt.prototype.visible=!1,Gt.jsonID("node",Zt);class te{constructor(t){this.anchor=t}map(t){let{deleted:e,pos:n}=t.mapResult(this.anchor);return e?new Xt(n,n):new te(n)}resolve(t){let e=t.resolve(this.anchor),n=e.nodeAfter;return n&&Zt.isSelectable(n)?new Zt(e):Gt.near(e)}}class ee extends Gt{constructor(t){super(t.resolve(0),t.resolve(t.content.size))}replace(t,e=h.empty){if(e==h.empty){t.delete(0,t.doc.content.size);let e=Gt.atStart(t.doc);e.eq(t.selection)||t.setSelection(e)}else super.replace(t,e)}toJSON(){return{type:"all"}}static fromJSON(t){return new ee(t)}map(t){return new ee(t)}eq(t){return t instanceof ee}getBookmark(){return ne}}Gt.jsonID("all",ee);const ne={map(){return this},resolve:t=>new ee(t)};function re(t,e,n,r,i,s=!1){if(e.inlineContent)return Yt.create(t,n);for(let o=r-(i>0?0:1);i>0?o<e.childCount:o>=0;o+=i){let r=e.child(o);if(r.isAtom){if(!s&&Zt.isSelectable(r))return Zt.create(t,n-(i<0?r.nodeSize:0))}else{let e=re(t,r,n+i,i<0?r.childCount:0,i,s);if(e)return e}n+=r.nodeSize*i}return null}function ie(t,e,n){let r=t.steps.length-1;if(r<e)return;let i,s=t.steps[r];(s instanceof yt||s instanceof kt)&&(t.mapping.maps[r].forEach(((t,e,n,r)=>{null==i&&(i=r)})),t.setSelection(Gt.near(t.doc.resolve(i),n)))}class se extends Kt{constructor(t){super(t.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=t.selection,this.storedMarks=t.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(t){if(t.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=t,this.curSelectionFor=this.steps.length,this.updated=-3&this.updated|1,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(t){return this.storedMarks=t,this.updated|=2,this}ensureMarks(t){return l.sameSet(this.storedMarks||this.selection.$from.marks(),t)||this.setStoredMarks(t),this}addStoredMark(t){return this.ensureMarks(t.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(t){return this.ensureMarks(t.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(t,e){super.addStep(t,e),this.updated=-3&this.updated,this.storedMarks=null}setTime(t){return this.time=t,this}replaceSelection(t){return this.selection.replace(this,t),this}replaceSelectionWith(t,e=!0){let n=this.selection;return e&&(t=t.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||l.none))),n.replaceWith(this,t),this}deleteSelection(){return this.selection.replace(this),this}insertText(t,e,n){let r=this.doc.type.schema;if(null==e)return t?this.replaceSelectionWith(r.text(t),!0):this.deleteSelection();{if(null==n&&(n=e),n=null==n?e:n,!t)return this.deleteRange(e,n);let i=this.storedMarks;if(!i){let t=this.doc.resolve(e);i=n==e?t.marks():t.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(e,n,r.text(t,i)),this.selection.empty||this.setSelection(Gt.near(this.selection.$to)),this}}setMeta(t,e){return this.meta["string"==typeof t?t:t.key]=e,this}getMeta(t){return this.meta["string"==typeof t?t:t.key]}get isGeneric(){for(let t in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function oe(t,e){return e&&t?t.bind(e):t}class le{constructor(t,e,n){this.name=t,this.init=oe(e.init,n),this.apply=oe(e.apply,n)}}const ue=[new le("doc",{init:t=>t.doc||t.schema.topNodeType.createAndFill(),apply:t=>t.doc}),new le("selection",{init:(t,e)=>t.selection||Gt.atStart(e.doc),apply:t=>t.selection}),new le("storedMarks",{init:t=>t.storedMarks||null,apply:(t,e,n,r)=>r.selection.$cursor?t.storedMarks:null}),new le("scrollToSelection",{init:()=>0,apply:(t,e)=>t.scrolledIntoView?e+1:e})];class he{constructor(t,e){this.schema=t,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=ue.slice(),e&&e.forEach((t=>{if(this.pluginsByKey[t.key])throw new RangeError("Adding different instances of a keyed plugin ("+t.key+")");this.plugins.push(t),this.pluginsByKey[t.key]=t,t.spec.state&&this.fields.push(new le(t.key,t.spec.state,t))}))}}class fe{constructor(t){this.config=t}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(t){return this.applyTransaction(t).state}filterTransaction(t,e=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=e){let e=this.config.plugins[n];if(e.spec.filterTransaction&&!e.spec.filterTransaction.call(e,t,this))return!1}return!0}applyTransaction(t){if(!this.filterTransaction(t))return{state:this,transactions:[]};let e=[t],n=this.applyInner(t),r=null;for(;;){let i=!1;for(let s=0;s<this.config.plugins.length;s++){let o=this.config.plugins[s];if(o.spec.appendTransaction){let l=r?r[s].n:0,u=r?r[s].state:this,h=l<e.length&&o.spec.appendTransaction.call(o,l?e.slice(l):e,u,n);if(h&&n.filterTransaction(h,s)){if(h.setMeta("appendedTransaction",t),!r){r=[];for(let t=0;t<this.config.plugins.length;t++)r.push(t<s?{state:n,n:e.length}:{state:this,n:0})}e.push(h),n=n.applyInner(h),i=!0}r&&(r[s]={state:n,n:e.length})}}if(!i)return{state:n,transactions:e}}}applyInner(t){if(!t.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let e=new fe(this.config),n=this.config.fields;for(let r=0;r<n.length;r++){let i=n[r];e[i.name]=i.apply(t,this[i.name],this,e)}return e}get tr(){return new se(this)}static create(t){let e=new he(t.doc?t.doc.type.schema:t.schema,t.plugins),n=new fe(e);for(let r=0;r<e.fields.length;r++)n[e.fields[r].name]=e.fields[r].init(t,n);return n}reconfigure(t){let e=new he(this.schema,t.plugins),n=e.fields,r=new fe(e);for(let e=0;e<n.length;e++){let i=n[e].name;r[i]=this.hasOwnProperty(i)?this[i]:n[e].init(t,r)}return r}toJSON(t){let e={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(e.storedMarks=this.storedMarks.map((t=>t.toJSON()))),t&&"object"==typeof t)for(let n in t){if("doc"==n||"selection"==n)throw new RangeError("The JSON fields `doc` and `selection` are reserved");let r=t[n],i=r.spec.state;i&&i.toJSON&&(e[n]=i.toJSON.call(r,this[r.key]))}return e}static fromJSON(t,e,n){if(!e)throw new RangeError("Invalid input for EditorState.fromJSON");if(!t.schema)throw new RangeError("Required config field 'schema' missing");let r=new he(t.schema,t.plugins),i=new fe(r);return r.fields.forEach((r=>{if("doc"==r.name)i.doc=N.fromJSON(t.schema,e.doc);else if("selection"==r.name)i.selection=Gt.fromJSON(i.doc,e.selection);else if("storedMarks"==r.name)e.storedMarks&&(i.storedMarks=e.storedMarks.map(t.schema.markFromJSON));else{if(n)for(let s in n){let o=n[s],l=o.spec.state;if(o.key==r.name&&l&&l.fromJSON&&Object.prototype.hasOwnProperty.call(e,s))return void(i[r.name]=l.fromJSON.call(o,t,e[s],i))}i[r.name]=r.init(t,i)}})),i}}function ae(t,e,n){for(let r in t){let i=t[r];i instanceof Function?i=i.bind(e):"handleDOMEvents"==r&&(i=ae(i,e,{})),n[r]=i}return n}class ce{constructor(t){this.spec=t,this.props={},t.props&&ae(t.props,this,this.props),this.key=t.key?t.key.key:pe("plugin")}getState(t){return t[this.key]}}const de=Object.create(null);function pe(t){return t in de?t+"$"+ ++de[t]:(de[t]=0,t+"$")}class me{constructor(t="key"){this.key=pe(t)}get(t){return t.config.pluginsByKey[this.key]}getState(t){return t[this.key]}}const we=function(t){for(var e=0;;e++)if(!(t=t.previousSibling))return e},ve=function(t){let e=t.assignedSlot||t.parentNode;return e&&11==e.nodeType?e.host:e};let ge=null;const be=function(t,e,n){let r=ge||(ge=document.createRange());return r.setEnd(t,null==n?t.nodeValue.length:n),r.setStart(t,e||0),r},ye=function(t,e,n,r){return n&&(xe(t,e,n,r,-1)||xe(t,e,n,r,1))},ke=/^(img|br|input|textarea|hr)$/i;function xe(t,e,n,r,i){for(;;){if(t==n&&e==r)return!0;if(e==(i<0?0:Me(t))){let n=t.parentNode;if(!n||1!=n.nodeType||Oe(t)||ke.test(t.nodeName)||"false"==t.contentEditable)return!1;e=we(t)+(i<0?0:1),t=n}else{if(1!=t.nodeType)return!1;if("false"==(t=t.childNodes[e+(i<0?-1:0)]).contentEditable)return!1;e=i<0?Me(t):0}}}function Me(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}function Oe(t){let e;for(let n=t;n&&!(e=n.pmViewDesc);n=n.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==t||e.contentDOM==t)}const Se=function(t){return t.focusNode&&ye(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)};function Ee(t,e){let n=document.createEvent("Event");return n.initEvent("keydown",!0,!0),n.keyCode=t,n.key=n.code=e,n}const Ne="undefined"!=typeof navigator?navigator:null,Ce="undefined"!=typeof document?document:null,Re=Ne&&Ne.userAgent||"",Te=/Edge\/(\d+)/.exec(Re),je=/MSIE \d/.exec(Re),Ae=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(Re),$e=!!(je||Ae||Te),De=je?document.documentMode:Ae?+Ae[1]:Te?+Te[1]:0,Pe=!$e&&/gecko\/(\d+)/i.test(Re);Pe&&/Firefox\/(\d+)/.exec(Re);const Ie=!$e&&/Chrome\/(\d+)/.exec(Re),Be=!!Ie,Je=Ie?+Ie[1]:0,Fe=!$e&&!!Ne&&/Apple Computer/.test(Ne.vendor),Le=Fe&&(/Mobile\/\w+/.test(Re)||!!Ne&&Ne.maxTouchPoints>2),qe=Le||!!Ne&&/Mac/.test(Ne.platform),He=!!Ne&&/Win/.test(Ne.platform),Ue=/Android \d/.test(Re),We=!!Ce&&"webkitFontSmoothing"in Ce.documentElement.style,Ke=We?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function ze(t){let e=t.defaultView&&t.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:t.documentElement.clientWidth,top:0,bottom:t.documentElement.clientHeight}}function Ge(t,e){return"number"==typeof t?t:t[e]}function Ve(t){let e=t.getBoundingClientRect();return{left:e.left,right:e.left+t.clientWidth*(e.width/t.offsetWidth||1),top:e.top,bottom:e.top+t.clientHeight*(e.height/t.offsetHeight||1)}}function _e(t,e,n){let r=t.someProp("scrollThreshold")||0,i=t.someProp("scrollMargin")||5,s=t.dom.ownerDocument;for(let o=n||t.dom;o;o=ve(o)){if(1!=o.nodeType)continue;let t=o,n=t==s.body,l=n?ze(s):Ve(t),u=0,h=0;if(e.top<l.top+Ge(r,"top")?h=-(l.top-e.top+Ge(i,"top")):e.bottom>l.bottom-Ge(r,"bottom")&&(h=e.bottom-e.top>l.bottom-l.top?e.top+Ge(i,"top")-l.top:e.bottom-l.bottom+Ge(i,"bottom")),e.left<l.left+Ge(r,"left")?u=-(l.left-e.left+Ge(i,"left")):e.right>l.right-Ge(r,"right")&&(u=e.right-l.right+Ge(i,"right")),u||h)if(n)s.defaultView.scrollBy(u,h);else{let n=t.scrollLeft,r=t.scrollTop;h&&(t.scrollTop+=h),u&&(t.scrollLeft+=u);let i=t.scrollLeft-n,s=t.scrollTop-r;e={left:e.left-i,top:e.top-s,right:e.right-i,bottom:e.bottom-s}}if(n||/^(fixed|sticky)$/.test(getComputedStyle(o).position))break}}function Qe(t){let e=[],n=t.ownerDocument;for(let r=t;r&&(e.push({dom:r,top:r.scrollTop,left:r.scrollLeft}),t!=n);r=ve(r));return e}function Ye(t,e){for(let n=0;n<t.length;n++){let{dom:r,top:i,left:s}=t[n];r.scrollTop!=i+e&&(r.scrollTop=i+e),r.scrollLeft!=s&&(r.scrollLeft=s)}}let Xe=null;function Ze(t,e){let n,r,i,s,o=2e8,l=0,u=e.top,h=e.top;for(let f=t.firstChild,a=0;f;f=f.nextSibling,a++){let t;if(1==f.nodeType)t=f.getClientRects();else{if(3!=f.nodeType)continue;t=be(f).getClientRects()}for(let c=0;c<t.length;c++){let d=t[c];if(d.top<=u&&d.bottom>=h){u=Math.max(d.bottom,u),h=Math.min(d.top,h);let t=d.left>e.left?d.left-e.left:d.right<e.left?e.left-d.right:0;if(t<o){n=f,o=t,r=t&&3==n.nodeType?{left:d.right<e.left?d.right:d.left,top:e.top}:e,1==f.nodeType&&t&&(l=a+(e.left>=(d.left+d.right)/2?1:0));continue}}else d.top>e.top&&!i&&d.left<=e.left&&d.right>=e.left&&(i=f,s={left:Math.max(d.left,Math.min(d.right,e.left)),top:d.top});!n&&(e.left>=d.right&&e.top>=d.top||e.left>=d.left&&e.top>=d.bottom)&&(l=a+1)}}return!n&&i&&(n=i,r=s,o=0),n&&3==n.nodeType?function(t,e){let n=t.nodeValue.length,r=document.createRange();for(let i=0;i<n;i++){r.setEnd(t,i+1),r.setStart(t,i);let n=sn(r,1);if(n.top!=n.bottom&&tn(e,n))return{node:t,offset:i+(e.left>=(n.left+n.right)/2?1:0)}}return{node:t,offset:0}}(n,r):!n||o&&1==n.nodeType?{node:t,offset:l}:Ze(n,r)}function tn(t,e){return t.left>=e.left-1&&t.left<=e.right+1&&t.top>=e.top-1&&t.top<=e.bottom+1}function en(t,e,n){let r=t.childNodes.length;if(r&&n.top<n.bottom)for(let i=Math.max(0,Math.min(r-1,Math.floor(r*(e.top-n.top)/(n.bottom-n.top))-2)),s=i;;){let n=t.childNodes[s];if(1==n.nodeType){let t=n.getClientRects();for(let r=0;r<t.length;r++){let i=t[r];if(tn(e,i))return en(n,e,i)}}if((s=(s+1)%r)==i)break}return t}function nn(t,e){let n,r=t.dom.ownerDocument,i=0,s=function(t,e,n){if(t.caretPositionFromPoint)try{let r=t.caretPositionFromPoint(e,n);if(r)return{node:r.offsetNode,offset:r.offset}}catch(t){}if(t.caretRangeFromPoint){let r=t.caretRangeFromPoint(e,n);if(r)return{node:r.startContainer,offset:r.startOffset}}}(r,e.left,e.top);s&&({node:n,offset:i}=s);let o,l=(t.root.elementFromPoint?t.root:r).elementFromPoint(e.left,e.top);if(!l||!t.dom.contains(1!=l.nodeType?l.parentNode:l)){let n=t.dom.getBoundingClientRect();if(!tn(e,n))return null;if(l=en(t.dom,e,n),!l)return null}if(Fe)for(let t=l;n&&t;t=ve(t))t.draggable&&(n=void 0);if(l=function(t,e){let n=t.parentNode;return n&&/^li$/i.test(n.nodeName)&&e.left<t.getBoundingClientRect().left?n:t}(l,e),n){if(Pe&&1==n.nodeType&&(i=Math.min(i,n.childNodes.length),i<n.childNodes.length)){let t,r=n.childNodes[i];"IMG"==r.nodeName&&(t=r.getBoundingClientRect()).right<=e.left&&t.bottom>e.top&&i++}let r;We&&i&&1==n.nodeType&&1==(r=n.childNodes[i-1]).nodeType&&"false"==r.contentEditable&&r.getBoundingClientRect().top>=e.top&&i--,n==t.dom&&i==n.childNodes.length-1&&1==n.lastChild.nodeType&&e.top>n.lastChild.getBoundingClientRect().bottom?o=t.state.doc.content.size:0!=i&&1==n.nodeType&&"BR"==n.childNodes[i-1].nodeName||(o=function(t,e,n,r){let i=-1;for(let n=e,s=!1;n!=t.dom;){let e=t.docView.nearestDesc(n,!0);if(!e)return null;if(1==e.dom.nodeType&&(e.node.isBlock&&e.parent&&!s||!e.contentDOM)){let t=e.dom.getBoundingClientRect();if(e.node.isBlock&&e.parent&&!s&&(s=!0,t.left>r.left||t.top>r.top?i=e.posBefore:(t.right<r.left||t.bottom<r.top)&&(i=e.posAfter)),!e.contentDOM&&i<0&&!e.node.isText)return(e.node.isBlock?r.top<(t.top+t.bottom)/2:r.left<(t.left+t.right)/2)?e.posBefore:e.posAfter}n=e.dom.parentNode}return i>-1?i:t.docView.posFromDOM(e,n,-1)}(t,n,i,e))}null==o&&(o=function(t,e,n){let{node:r,offset:i}=Ze(e,n),s=-1;if(1==r.nodeType&&!r.firstChild){let t=r.getBoundingClientRect();s=t.left!=t.right&&n.left>(t.left+t.right)/2?1:-1}return t.docView.posFromDOM(r,i,s)}(t,l,e));let u=t.docView.nearestDesc(l,!0);return{pos:o,inside:u?u.posAtStart-u.border:-1}}function rn(t){return t.top<t.bottom||t.left<t.right}function sn(t,e){let n=t.getClientRects();if(n.length){let t=n[e<0?0:n.length-1];if(rn(t))return t}return Array.prototype.find.call(n,rn)||t.getBoundingClientRect()}const on=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function ln(t,e,n){let{node:r,offset:i,atom:s}=t.docView.domFromPos(e,n<0?-1:1),o=We||Pe;if(3==r.nodeType){if(!o||!on.test(r.nodeValue)&&(n<0?i:i!=r.nodeValue.length)){let t=i,e=i,s=n<0?1:-1;return n<0&&!i?(e++,s=-1):n>=0&&i==r.nodeValue.length?(t--,s=1):n<0?t--:e++,un(sn(be(r,t,e),s),s<0)}{let t=sn(be(r,i,i),n);if(Pe&&i&&/\s/.test(r.nodeValue[i-1])&&i<r.nodeValue.length){let e=sn(be(r,i-1,i-1),-1);if(e.top==t.top){let n=sn(be(r,i,i+1),-1);if(n.top!=t.top)return un(n,n.left<e.left)}}return t}}if(!t.state.doc.resolve(e-(s||0)).parent.inlineContent){if(null==s&&i&&(n<0||i==Me(r))){let t=r.childNodes[i-1];if(1==t.nodeType)return hn(t.getBoundingClientRect(),!1)}if(null==s&&i<Me(r)){let t=r.childNodes[i];if(1==t.nodeType)return hn(t.getBoundingClientRect(),!0)}return hn(r.getBoundingClientRect(),n>=0)}if(null==s&&i&&(n<0||i==Me(r))){let t=r.childNodes[i-1],e=3==t.nodeType?be(t,Me(t)-(o?0:1)):1!=t.nodeType||"BR"==t.nodeName&&t.nextSibling?null:t;if(e)return un(sn(e,1),!1)}if(null==s&&i<Me(r)){let t=r.childNodes[i];for(;t.pmViewDesc&&t.pmViewDesc.ignoreForCoords;)t=t.nextSibling;let e=t?3==t.nodeType?be(t,0,o?0:1):1==t.nodeType?t:null:null;if(e)return un(sn(e,-1),!0)}return un(sn(3==r.nodeType?be(r):r,-n),n>=0)}function un(t,e){if(0==t.width)return t;let n=e?t.left:t.right;return{top:t.top,bottom:t.bottom,left:n,right:n}}function hn(t,e){if(0==t.height)return t;let n=e?t.top:t.bottom;return{top:n,bottom:n,left:t.left,right:t.right}}function fn(t,e,n){let r=t.state,i=t.root.activeElement;r!=e&&t.updateState(e),i!=t.dom&&t.focus();try{return n()}finally{r!=e&&t.updateState(r),i!=t.dom&&i&&i.focus()}}const an=/[\u0590-\u08ac]/;let cn=null,dn=null,pn=!1;class mn{constructor(t,e,n,r){this.parent=t,this.children=e,this.dom=n,this.contentDOM=r,this.dirty=0,n.pmViewDesc=this}matchesWidget(t){return!1}matchesMark(t){return!1}matchesNode(t,e,n){return!1}matchesHack(t){return!1}parseRule(){return null}stopEvent(t){return!1}get size(){let t=0;for(let e=0;e<this.children.length;e++)t+=this.children[e].size;return t}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let t=0;t<this.children.length;t++)this.children[t].destroy()}posBeforeChild(t){for(let e=0,n=this.posAtStart;;e++){let r=this.children[e];if(r==t)return n;n+=r.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(t,e,n){if(this.contentDOM&&this.contentDOM.contains(1==t.nodeType?t:t.parentNode)){if(n<0){let n,r;if(t==this.contentDOM)n=t.childNodes[e-1];else{for(;t.parentNode!=this.contentDOM;)t=t.parentNode;n=t.previousSibling}for(;n&&(!(r=n.pmViewDesc)||r.parent!=this);)n=n.previousSibling;return n?this.posBeforeChild(r)+r.size:this.posAtStart}{let n,r;if(t==this.contentDOM)n=t.childNodes[e];else{for(;t.parentNode!=this.contentDOM;)t=t.parentNode;n=t.nextSibling}for(;n&&(!(r=n.pmViewDesc)||r.parent!=this);)n=n.nextSibling;return n?this.posBeforeChild(r):this.posAtEnd}}let r;if(t==this.dom&&this.contentDOM)r=e>we(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))r=2&t.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(0==e)for(let e=t;;e=e.parentNode){if(e==this.dom){r=!1;break}if(e.previousSibling)break}if(null==r&&e==t.childNodes.length)for(let e=t;;e=e.parentNode){if(e==this.dom){r=!0;break}if(e.nextSibling)break}}return(null==r?n>0:r)?this.posAtEnd:this.posAtStart}nearestDesc(t,e=!1){for(let n=!0,r=t;r;r=r.parentNode){let i,s=this.getDesc(r);if(s&&(!e||s.node)){if(!n||!(i=s.nodeDOM)||(1==i.nodeType?i.contains(1==t.nodeType?t:t.parentNode):i==t))return s;n=!1}}}getDesc(t){let e=t.pmViewDesc;for(let t=e;t;t=t.parent)if(t==this)return e}posFromDOM(t,e,n){for(let r=t;r;r=r.parentNode){let i=this.getDesc(r);if(i)return i.localPosFromDOM(t,e,n)}return-1}descAt(t){for(let e=0,n=0;e<this.children.length;e++){let r=this.children[e],i=n+r.size;if(n==t&&i!=n){for(;!r.border&&r.children.length;)r=r.children[0];return r}if(t<i)return r.descAt(t-n-r.border);n=i}}domFromPos(t,e){if(!this.contentDOM)return{node:this.dom,offset:0,atom:t+1};let n=0,r=0;for(let e=0;n<this.children.length;n++){let i=this.children[n],s=e+i.size;if(s>t||i instanceof xn){r=t-e;break}e=s}if(r)return this.children[n].domFromPos(r-this.children[n].border,e);for(let t;n&&!(t=this.children[n-1]).size&&t instanceof wn&&t.side>=0;n--);if(e<=0){let t,r=!0;for(;t=n?this.children[n-1]:null,t&&t.dom.parentNode!=this.contentDOM;n--,r=!1);return t&&e&&r&&!t.border&&!t.domAtom?t.domFromPos(t.size,e):{node:this.contentDOM,offset:t?we(t.dom)+1:0}}{let t,r=!0;for(;t=n<this.children.length?this.children[n]:null,t&&t.dom.parentNode!=this.contentDOM;n++,r=!1);return t&&r&&!t.border&&!t.domAtom?t.domFromPos(0,e):{node:this.contentDOM,offset:t?we(t.dom):this.contentDOM.childNodes.length}}}parseRange(t,e,n=0){if(0==this.children.length)return{node:this.contentDOM,from:t,to:e,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let r=-1,i=-1;for(let s=n,o=0;;o++){let n=this.children[o],l=s+n.size;if(-1==r&&t<=l){let i=s+n.border;if(t>=i&&e<=l-n.border&&n.node&&n.contentDOM&&this.contentDOM.contains(n.contentDOM))return n.parseRange(t,e,i);t=s;for(let e=o;e>0;e--){let n=this.children[e-1];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(1)){r=we(n.dom)+1;break}t-=n.size}-1==r&&(r=0)}if(r>-1&&(l>e||o==this.children.length-1)){e=l;for(let t=o+1;t<this.children.length;t++){let n=this.children[t];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(-1)){i=we(n.dom);break}e+=n.size}-1==i&&(i=this.contentDOM.childNodes.length);break}s=l}return{node:this.contentDOM,from:t,to:e,fromOffset:r,toOffset:i}}emptyChildAt(t){if(this.border||!this.contentDOM||!this.children.length)return!1;let e=this.children[t<0?0:this.children.length-1];return 0==e.size||e.emptyChildAt(t)}domAfterPos(t){let{node:e,offset:n}=this.domFromPos(t,0);if(1!=e.nodeType||n==e.childNodes.length)throw new RangeError("No node after pos "+t);return e.childNodes[n]}setSelection(t,e,n,r=!1){let i=Math.min(t,e),s=Math.max(t,e);for(let o=0,l=0;o<this.children.length;o++){let u=this.children[o],h=l+u.size;if(i>l&&s<h)return u.setSelection(t-l-u.border,e-l-u.border,n,r);l=h}let o=this.domFromPos(t,t?-1:1),l=e==t?o:this.domFromPos(e,e?-1:1),u=n.getSelection(),h=!1;if((Pe||Fe)&&t==e){let{node:t,offset:e}=o;if(3==t.nodeType){if(h=!(!e||"\n"!=t.nodeValue[e-1]),h&&e==t.nodeValue.length)for(let e,n=t;n;n=n.parentNode){if(e=n.nextSibling){"BR"==e.nodeName&&(o=l={node:e.parentNode,offset:we(e)+1});break}let t=n.pmViewDesc;if(t&&t.node&&t.node.isBlock)break}}else{let n=t.childNodes[e-1];h=n&&("BR"==n.nodeName||"false"==n.contentEditable)}}if(Pe&&u.focusNode&&u.focusNode!=l.node&&1==u.focusNode.nodeType){let t=u.focusNode.childNodes[u.focusOffset];t&&"false"==t.contentEditable&&(r=!0)}if(!(r||h&&Fe)&&ye(o.node,o.offset,u.anchorNode,u.anchorOffset)&&ye(l.node,l.offset,u.focusNode,u.focusOffset))return;let f=!1;if((u.extend||t==e)&&!h){u.collapse(o.node,o.offset);try{t!=e&&u.extend(l.node,l.offset),f=!0}catch(t){}}if(!f){if(t>e){let t=o;o=l,l=t}let n=document.createRange();n.setEnd(l.node,l.offset),n.setStart(o.node,o.offset),u.removeAllRanges(),u.addRange(n)}}ignoreMutation(t){return!this.contentDOM&&"selection"!=t.type}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(t,e){for(let n=0,r=0;r<this.children.length;r++){let i=this.children[r],s=n+i.size;if(n==s?t<=s&&e>=n:t<s&&e>n){let r=n+i.border,o=s-i.border;if(t>=r&&e<=o)return this.dirty=t==n||e==s?2:1,void(t!=r||e!=o||!i.contentLost&&i.dom.parentNode==this.contentDOM?i.markDirty(t-r,e-r):i.dirty=3);i.dirty=i.dom!=i.contentDOM||i.dom.parentNode!=this.contentDOM||i.children.length?3:2}n=s}this.dirty=2}markParentsDirty(){let t=1;for(let e=this.parent;e;e=e.parent,t++){let n=1==t?2:1;e.dirty<n&&(e.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}isText(t){return!1}}class wn extends mn{constructor(t,e,n,r){let i,s=e.type.toDOM;if("function"==typeof s&&(s=s(n,(()=>i?i.parent?i.parent.posBeforeChild(i):void 0:r))),!e.type.spec.raw){if(1!=s.nodeType){let t=document.createElement("span");t.appendChild(s),s=t}s.contentEditable="false",s.classList.add("ProseMirror-widget")}super(t,[],s,null),this.widget=e,this.widget=e,i=this}matchesWidget(t){return 0==this.dirty&&t.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(t){let e=this.widget.spec.stopEvent;return!!e&&e(t)}ignoreMutation(t){return"selection"!=t.type||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get side(){return this.widget.type.side}}class vn extends mn{constructor(t,e,n,r){super(t,[],e,null),this.textDOM=n,this.text=r}get size(){return this.text.length}localPosFromDOM(t,e){return t!=this.textDOM?this.posAtStart+(e?this.size:0):this.posAtStart+e}domFromPos(t){return{node:this.textDOM,offset:t}}ignoreMutation(t){return"characterData"===t.type&&t.target.nodeValue==t.oldValue}}class gn extends mn{constructor(t,e,n,r){super(t,[],n,r),this.mark=e}static create(t,e,n,r){let i=r.nodeViews[e.type.name],s=i&&i(e,r,n);return s&&s.dom||(s=rt.renderSpec(document,e.type.spec.toDOM(e,n))),new gn(t,e,s.dom,s.contentDOM||s.dom)}parseRule(){return 3&this.dirty||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(t){return 3!=this.dirty&&this.mark.eq(t)}markDirty(t,e){if(super.markDirty(t,e),0!=this.dirty){let t=this.parent;for(;!t.node;)t=t.parent;t.dirty<this.dirty&&(t.dirty=this.dirty),this.dirty=0}}slice(t,e,n){let r=gn.create(this.parent,this.mark,!0,n),i=this.children,s=this.size;e<s&&(i=Pn(i,e,s,n)),t>0&&(i=Pn(i,0,t,n));for(let t=0;t<i.length;t++)i[t].parent=r;return r.children=i,r}}class bn extends mn{constructor(t,e,n,r,i,s,o,l,u){super(t,[],i,s),this.node=e,this.outerDeco=n,this.innerDeco=r,this.nodeDOM=o}static create(t,e,n,r,i,s){let o,l=i.nodeViews[e.type.name],u=l&&l(e,i,(()=>o?o.parent?o.parent.posBeforeChild(o):void 0:s),n,r),h=u&&u.dom,f=u&&u.contentDOM;if(e.isText)if(h){if(3!=h.nodeType)throw new RangeError("Text must be rendered as a DOM text node")}else h=document.createTextNode(e.text);else h||({dom:h,contentDOM:f}=rt.renderSpec(document,e.type.spec.toDOM(e)));f||e.isText||"BR"==h.nodeName||(h.hasAttribute("contenteditable")||(h.contentEditable="false"),e.type.spec.draggable&&(h.draggable=!0));let a=h;return h=Tn(h,n,e),u?o=new Mn(t,e,n,r,h,f||null,a,u,i,s+1):e.isText?new kn(t,e,n,r,h,a,i):new bn(t,e,n,r,h,f||null,a,i,s+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let t={node:this.node.type.name,attrs:this.node.attrs};if("pre"==this.node.type.whitespace&&(t.preserveWhitespace="full"),this.contentDOM)if(this.contentLost){for(let e=this.children.length-1;e>=0;e--){let n=this.children[e];if(this.dom.contains(n.dom.parentNode)){t.contentElement=n.dom.parentNode;break}}t.contentElement||(t.getContent=()=>r.empty)}else t.contentElement=this.contentDOM;else t.getContent=()=>this.node.content;return t}matchesNode(t,e,n){return 0==this.dirty&&t.eq(this.node)&&jn(e,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(t,e){let n=this.node.inlineContent,r=e,i=t.composing?this.localCompositionInfo(t,e):null,s=i&&i.pos>-1?i:null,o=i&&i.pos<0,u=new $n(this,s&&s.node,t);!function(t,e,n,r){let i=e.locals(t),s=0;if(0==i.length){for(let n=0;n<t.childCount;n++){let o=t.child(n);r(o,i,e.forChild(s,o),n),s+=o.nodeSize}return}let o=0,l=[],u=null;for(let h=0;;){let f,a,c,d;for(;o<i.length&&i[o].to==s;){let t=i[o++];t.widget&&(f?(a||(a=[f])).push(t):f=t)}if(f)if(a){a.sort(Dn);for(let t=0;t<a.length;t++)n(a[t],h,!!u)}else n(f,h,!!u);if(u)d=-1,c=u,u=null;else{if(!(h<t.childCount))break;d=h,c=t.child(h++)}for(let t=0;t<l.length;t++)l[t].to<=s&&l.splice(t--,1);for(;o<i.length&&i[o].from<=s&&i[o].to>s;)l.push(i[o++]);let p=s+c.nodeSize;if(c.isText){let t=p;o<i.length&&i[o].from<t&&(t=i[o].from);for(let e=0;e<l.length;e++)l[e].to<t&&(t=l[e].to);t<p&&(u=c.cut(t-s),c=c.cut(0,t-s),p=t,d=-1)}else for(;o<i.length&&i[o].to<p;)o++;r(c,c.isInline&&!c.isLeaf?l.filter((t=>!t.inline)):l.slice(),e.forChild(s,c),d),s=p}}(this.node,this.innerDeco,((e,i,s)=>{e.spec.marks?u.syncToMarks(e.spec.marks,n,t):e.type.side>=0&&!s&&u.syncToMarks(i==this.node.childCount?l.none:this.node.child(i).marks,n,t),u.placeWidget(e,t,r)}),((e,s,l,h)=>{let f;u.syncToMarks(e.marks,n,t),u.findNodeMatch(e,s,l,h)||o&&t.state.selection.from>r&&t.state.selection.to<r+e.nodeSize&&(f=u.findIndexWithChild(i.node))>-1&&u.updateNodeAt(e,s,l,f,t)||u.updateNextNode(e,s,l,t,h,r)||u.addNode(e,s,l,t,r),r+=e.nodeSize})),u.syncToMarks([],n,t),this.node.isTextblock&&u.addTextblockHacks(),u.destroyRest(),(u.changed||2==this.dirty)&&(s&&this.protectLocalComposition(t,s),On(this.contentDOM,this.children,t),Le&&function(t){if("UL"==t.nodeName||"OL"==t.nodeName){let e=t.style.cssText;t.style.cssText=e+"; list-style: square !important",window.getComputedStyle(t),t.style.cssText=e}}(this.dom))}localCompositionInfo(t,e){let{from:n,to:r}=t.state.selection;if(!(t.state.selection instanceof Yt)||n<e||r>e+this.node.content.size)return null;let i=t.input.compositionNode;if(!i||!this.dom.contains(i.parentNode))return null;if(this.node.inlineContent){let t=i.nodeValue,s=function(t,e,n,r){for(let i=0,s=0;i<t.childCount&&s<=r;){let o=t.child(i++),l=s;if(s+=o.nodeSize,!o.isText)continue;let u=o.text;for(;i<t.childCount;){let e=t.child(i++);if(s+=e.nodeSize,!e.isText)break;u+=e.text}if(s>=n){if(s>=r&&u.slice(r-e.length-l,r-l)==e)return r-e.length;let t=l<r?u.lastIndexOf(e,r-l-1):-1;if(t>=0&&t+e.length+l>=n)return l+t;if(n==r&&u.length>=r+e.length-l&&u.slice(r-l,r-l+e.length)==e)return r}}return-1}(this.node.content,t,n-e,r-e);return s<0?null:{node:i,pos:s,text:t}}return{node:i,pos:-1,text:""}}protectLocalComposition(t,{node:e,pos:n,text:r}){if(this.getDesc(e))return;let i=e;for(;i.parentNode!=this.contentDOM;i=i.parentNode){for(;i.previousSibling;)i.parentNode.removeChild(i.previousSibling);for(;i.nextSibling;)i.parentNode.removeChild(i.nextSibling);i.pmViewDesc&&(i.pmViewDesc=void 0)}let s=new vn(this,i,e,r);t.input.compositionNodes.push(s),this.children=Pn(this.children,n,n+r.length,t,s)}update(t,e,n,r){return!(3==this.dirty||!t.sameMarkup(this.node)||(this.updateInner(t,e,n,r),0))}updateInner(t,e,n,r){this.updateOuterDeco(e),this.node=t,this.innerDeco=n,this.contentDOM&&this.updateChildren(r,this.posAtStart),this.dirty=0}updateOuterDeco(t){if(jn(t,this.outerDeco))return;let e=1!=this.nodeDOM.nodeType,n=this.dom;this.dom=Cn(this.dom,this.nodeDOM,Nn(this.outerDeco,this.node,e),Nn(t,this.node,e)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=t}selectNode(){1==this.nodeDOM.nodeType&&this.nodeDOM.classList.add("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||(this.dom.draggable=!0)}deselectNode(){1==this.nodeDOM.nodeType&&this.nodeDOM.classList.remove("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||this.dom.removeAttribute("draggable")}get domAtom(){return this.node.isAtom}}function yn(t,e,n,r,i){Tn(r,e,t);let s=new bn(void 0,t,e,n,r,r,r,i,0);return s.contentDOM&&s.updateChildren(i,0),s}class kn extends bn{constructor(t,e,n,r,i,s,o){super(t,e,n,r,i,null,s,o,0)}parseRule(){let t=this.nodeDOM.parentNode;for(;t&&t!=this.dom&&!t.pmIsDeco;)t=t.parentNode;return{skip:t||!0}}update(t,e,n,r){return!(3==this.dirty||0!=this.dirty&&!this.inParent()||!t.sameMarkup(this.node)||(this.updateOuterDeco(e),0==this.dirty&&t.text==this.node.text||t.text==this.nodeDOM.nodeValue||(this.nodeDOM.nodeValue=t.text,r.trackWrites==this.nodeDOM&&(r.trackWrites=null)),this.node=t,this.dirty=0,0))}inParent(){let t=this.parent.contentDOM;for(let e=this.nodeDOM;e;e=e.parentNode)if(e==t)return!0;return!1}domFromPos(t){return{node:this.nodeDOM,offset:t}}localPosFromDOM(t,e,n){return t==this.nodeDOM?this.posAtStart+Math.min(e,this.node.text.length):super.localPosFromDOM(t,e,n)}ignoreMutation(t){return"characterData"!=t.type&&"selection"!=t.type}slice(t,e,n){let r=this.node.cut(t,e),i=document.createTextNode(r.text);return new kn(this.parent,r,this.outerDeco,this.innerDeco,i,i,n)}markDirty(t,e){super.markDirty(t,e),this.dom==this.nodeDOM||0!=t&&e!=this.nodeDOM.nodeValue.length||(this.dirty=3)}get domAtom(){return!1}isText(t){return this.node.text==t}}class xn extends mn{parseRule(){return{ignore:!0}}matchesHack(t){return 0==this.dirty&&this.dom.nodeName==t}get domAtom(){return!0}get ignoreForCoords(){return"IMG"==this.dom.nodeName}}class Mn extends bn{constructor(t,e,n,r,i,s,o,l,u,h){super(t,e,n,r,i,s,o,u,h),this.spec=l}update(t,e,n,r){if(3==this.dirty)return!1;if(this.spec.update){let i=this.spec.update(t,e,n);return i&&this.updateInner(t,e,n,r),i}return!(!this.contentDOM&&!t.isLeaf)&&super.update(t,e,n,r)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(t,e,n,r){this.spec.setSelection?this.spec.setSelection(t,e,n):super.setSelection(t,e,n,r)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(t){return!!this.spec.stopEvent&&this.spec.stopEvent(t)}ignoreMutation(t){return this.spec.ignoreMutation?this.spec.ignoreMutation(t):super.ignoreMutation(t)}}function On(t,e,n){let r=t.firstChild,i=!1;for(let s=0;s<e.length;s++){let o=e[s],l=o.dom;if(l.parentNode==t){for(;l!=r;)r=An(r),i=!0;r=r.nextSibling}else i=!0,t.insertBefore(l,r);if(o instanceof gn){let e=r?r.previousSibling:t.lastChild;On(o.contentDOM,o.children,n),r=e?e.nextSibling:t.firstChild}}for(;r;)r=An(r),i=!0;i&&n.trackWrites==t&&(n.trackWrites=null)}const Sn=function(t){t&&(this.nodeName=t)};Sn.prototype=Object.create(null);const En=[new Sn];function Nn(t,e,n){if(0==t.length)return En;let r=n?En[0]:new Sn,i=[r];for(let s=0;s<t.length;s++){let o=t[s].type.attrs;if(o){o.nodeName&&i.push(r=new Sn(o.nodeName));for(let t in o){let s=o[t];null!=s&&(n&&1==i.length&&i.push(r=new Sn(e.isInline?"span":"div")),"class"==t?r.class=(r.class?r.class+" ":"")+s:"style"==t?r.style=(r.style?r.style+";":"")+s:"nodeName"!=t&&(r[t]=s))}}}return i}function Cn(t,e,n,r){if(n==En&&r==En)return e;let i=e;for(let e=0;e<r.length;e++){let s=r[e],o=n[e];if(e){let e;o&&o.nodeName==s.nodeName&&i!=t&&(e=i.parentNode)&&e.nodeName.toLowerCase()==s.nodeName||(e=document.createElement(s.nodeName),e.pmIsDeco=!0,e.appendChild(i),o=En[0]),i=e}Rn(i,o||En[0],s)}return i}function Rn(t,e,n){for(let r in e)"class"==r||"style"==r||"nodeName"==r||r in n||t.removeAttribute(r);for(let r in n)"class"!=r&&"style"!=r&&"nodeName"!=r&&n[r]!=e[r]&&t.setAttribute(r,n[r]);if(e.class!=n.class){let r=e.class?e.class.split(" ").filter(Boolean):[],i=n.class?n.class.split(" ").filter(Boolean):[];for(let e=0;e<r.length;e++)-1==i.indexOf(r[e])&&t.classList.remove(r[e]);for(let e=0;e<i.length;e++)-1==r.indexOf(i[e])&&t.classList.add(i[e]);0==t.classList.length&&t.removeAttribute("class")}if(e.style!=n.style){if(e.style){let n,r=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g;for(;n=r.exec(e.style);)t.style.removeProperty(n[1])}n.style&&(t.style.cssText+=n.style)}}function Tn(t,e,n){return Cn(t,t,En,Nn(e,n,1!=t.nodeType))}function jn(t,e){if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!t[n].type.eq(e[n].type))return!1;return!0}function An(t){let e=t.nextSibling;return t.parentNode.removeChild(t),e}class $n{constructor(t,e,n){this.lock=e,this.view=n,this.index=0,this.stack=[],this.changed=!1,this.top=t,this.preMatch=function(t,e){let n=e,r=n.children.length,i=t.childCount,s=new Map,o=[];t:for(;i>0;){let l;for(;;)if(r){let t=n.children[r-1];if(!(t instanceof gn)){l=t,r--;break}n=t,r=t.children.length}else{if(n==e)break t;r=n.parent.children.indexOf(n),n=n.parent}let u=l.node;if(u){if(u!=t.child(i-1))break;--i,s.set(l,i),o.push(l)}}return{index:i,matched:s,matches:o.reverse()}}(t.node.content,t)}destroyBetween(t,e){if(t!=e){for(let n=t;n<e;n++)this.top.children[n].destroy();this.top.children.splice(t,e-t),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(t,e,n){let r=0,i=this.stack.length>>1,s=Math.min(i,t.length);for(;r<s&&(r==i-1?this.top:this.stack[r+1<<1]).matchesMark(t[r])&&!1!==t[r].type.spec.spanning;)r++;for(;r<i;)this.destroyRest(),this.top.dirty=0,this.index=this.stack.pop(),this.top=this.stack.pop(),i--;for(;i<t.length;){this.stack.push(this.top,this.index+1);let r=-1;for(let e=this.index;e<Math.min(this.index+3,this.top.children.length);e++){let n=this.top.children[e];if(n.matchesMark(t[i])&&!this.isLocked(n.dom)){r=e;break}}if(r>-1)r>this.index&&(this.changed=!0,this.destroyBetween(this.index,r)),this.top=this.top.children[this.index];else{let r=gn.create(this.top,t[i],e,n);this.top.children.splice(this.index,0,r),this.top=r,this.changed=!0}this.index=0,i++}}findNodeMatch(t,e,n,r){let i,s=-1;if(r>=this.preMatch.index&&(i=this.preMatch.matches[r-this.preMatch.index]).parent==this.top&&i.matchesNode(t,e,n))s=this.top.children.indexOf(i,this.index);else for(let r=this.index,i=Math.min(this.top.children.length,r+5);r<i;r++){let i=this.top.children[r];if(i.matchesNode(t,e,n)&&!this.preMatch.matched.has(i)){s=r;break}}return!(s<0||(this.destroyBetween(this.index,s),this.index++,0))}updateNodeAt(t,e,n,r,i){let s=this.top.children[r];return 3==s.dirty&&s.dom==s.contentDOM&&(s.dirty=2),!!s.update(t,e,n,i)&&(this.destroyBetween(this.index,r),this.index++,!0)}findIndexWithChild(t){for(;;){let e=t.parentNode;if(!e)return-1;if(e==this.top.contentDOM){let e=t.pmViewDesc;if(e)for(let t=this.index;t<this.top.children.length;t++)if(this.top.children[t]==e)return t;return-1}t=e}}updateNextNode(t,e,n,r,i,s){for(let o=this.index;o<this.top.children.length;o++){let l=this.top.children[o];if(l instanceof bn){let u=this.preMatch.matched.get(l);if(null!=u&&u!=i)return!1;let h,f=l.dom,a=this.isLocked(f)&&!(t.isText&&l.node&&l.node.isText&&l.nodeDOM.nodeValue==t.text&&3!=l.dirty&&jn(e,l.outerDeco));if(!a&&l.update(t,e,n,r))return this.destroyBetween(this.index,o),l.dom!=f&&(this.changed=!0),this.index++,!0;if(!a&&(h=this.recreateWrapper(l,t,e,n,r,s)))return this.top.children[this.index]=h,h.contentDOM&&(h.dirty=2,h.updateChildren(r,s+1),h.dirty=0),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(t,e,n,r,i,s){if(t.dirty||e.isAtom||!t.children.length||!t.node.content.eq(e.content))return null;let o=bn.create(this.top,e,n,r,i,s);if(o.contentDOM){o.children=t.children,t.children=[];for(let t of o.children)t.parent=o}return t.destroy(),o}addNode(t,e,n,r,i){let s=bn.create(this.top,t,e,n,r,i);s.contentDOM&&s.updateChildren(r,i+1),this.top.children.splice(this.index++,0,s),this.changed=!0}placeWidget(t,e,n){let r=this.index<this.top.children.length?this.top.children[this.index]:null;if(!r||!r.matchesWidget(t)||t!=r.widget&&r.widget.type.toDOM.parentNode){let r=new wn(this.top,t,e,n);this.top.children.splice(this.index++,0,r),this.changed=!0}else this.index++}addTextblockHacks(){let t=this.top.children[this.index-1],e=this.top;for(;t instanceof gn;)e=t,t=e.children[e.children.length-1];(!t||!(t instanceof kn)||/\n$/.test(t.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(t.node.text))&&((Fe||Be)&&t&&"false"==t.dom.contentEditable&&this.addHackNode("IMG",e),this.addHackNode("BR",this.top))}addHackNode(t,e){if(e==this.top&&this.index<e.children.length&&e.children[this.index].matchesHack(t))this.index++;else{let n=document.createElement(t);"IMG"==t&&(n.className="ProseMirror-separator",n.alt=""),"BR"==t&&(n.className="ProseMirror-trailingBreak");let r=new xn(this.top,[],n,null);e!=this.top?e.children.push(r):e.children.splice(this.index++,0,r),this.changed=!0}}isLocked(t){return this.lock&&(t==this.lock||1==t.nodeType&&t.contains(this.lock.parentNode))}}function Dn(t,e){return t.type.side-e.type.side}function Pn(t,e,n,r,i){let s=[];for(let o=0,l=0;o<t.length;o++){let u=t[o],h=l,f=l+=u.size;h>=n||f<=e?s.push(u):(h<e&&s.push(u.slice(0,e-h,r)),i&&(s.push(i),i=void 0),f>n&&s.push(u.slice(n-h,u.size,r)))}return s}function In(t,e=null){let n=t.domSelectionRange(),r=t.state.doc;if(!n.focusNode)return null;let i=t.docView.nearestDesc(n.focusNode),s=i&&0==i.size,o=t.docView.posFromDOM(n.focusNode,n.focusOffset,1);if(o<0)return null;let l,u,h=r.resolve(o);if(Se(n)){for(l=h;i&&!i.node;)i=i.parent;let t=i.node;if(i&&t.isAtom&&Zt.isSelectable(t)&&i.parent&&(!t.isInline||!function(t,e,n){for(let r=0==e,i=e==Me(t);r||i;){if(t==n)return!0;let e=we(t);if(!(t=t.parentNode))return!1;r=r&&0==e,i=i&&e==Me(t)}}(n.focusNode,n.focusOffset,i.dom))){let t=i.posBefore;u=new Zt(o==t?h:r.resolve(t))}}else{let e=t.docView.posFromDOM(n.anchorNode,n.anchorOffset,1);if(e<0)return null;l=r.resolve(e)}return u||(u=Kn(t,l,h,"pointer"==e||t.state.selection.head<h.pos&&!s?1:-1)),u}function Bn(t){return t.editable?t.hasFocus():Gn(t)&&document.activeElement&&document.activeElement.contains(t.dom)}function Jn(t,e=!1){let n=t.state.selection;if(Un(t,n),Bn(t)){if(!e&&t.input.mouseDown&&t.input.mouseDown.allowDefault&&Be){let e=t.domSelectionRange(),n=t.domObserver.currentSelection;if(e.anchorNode&&n.anchorNode&&ye(e.anchorNode,e.anchorOffset,n.anchorNode,n.anchorOffset))return t.input.mouseDown.delayedSelectionSync=!0,void t.domObserver.setCurSelection()}if(t.domObserver.disconnectSelection(),t.cursorWrapper)!function(t){let e=t.domSelection(),n=document.createRange(),r=t.cursorWrapper.dom,i="IMG"==r.nodeName;i?n.setEnd(r.parentNode,we(r)+1):n.setEnd(r,0),n.collapse(!1),e.removeAllRanges(),e.addRange(n),!i&&!t.state.selection.visible&&$e&&De<=11&&(r.disabled=!0,r.disabled=!1)}(t);else{let r,i,{anchor:s,head:o}=n;!Fn||n instanceof Yt||(n.$from.parent.inlineContent||(r=Ln(t,n.from)),n.empty||n.$from.parent.inlineContent||(i=Ln(t,n.to))),t.docView.setSelection(s,o,t.root,e),Fn&&(r&&Hn(r),i&&Hn(i)),n.visible?t.dom.classList.remove("ProseMirror-hideselection"):(t.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&function(t){let e=t.dom.ownerDocument;e.removeEventListener("selectionchange",t.input.hideSelectionGuard);let n=t.domSelectionRange(),r=n.anchorNode,i=n.anchorOffset;e.addEventListener("selectionchange",t.input.hideSelectionGuard=()=>{n.anchorNode==r&&n.anchorOffset==i||(e.removeEventListener("selectionchange",t.input.hideSelectionGuard),setTimeout((()=>{Bn(t)&&!t.state.selection.visible||t.dom.classList.remove("ProseMirror-hideselection")}),20))})}(t))}t.domObserver.setCurSelection(),t.domObserver.connectSelection()}}const Fn=Fe||Be&&Je<63;function Ln(t,e){let{node:n,offset:r}=t.docView.domFromPos(e,0),i=r<n.childNodes.length?n.childNodes[r]:null,s=r?n.childNodes[r-1]:null;if(Fe&&i&&"false"==i.contentEditable)return qn(i);if(!(i&&"false"!=i.contentEditable||s&&"false"!=s.contentEditable)){if(i)return qn(i);if(s)return qn(s)}}function qn(t){return t.contentEditable="true",Fe&&t.draggable&&(t.draggable=!1,t.wasDraggable=!0),t}function Hn(t){t.contentEditable="false",t.wasDraggable&&(t.draggable=!0,t.wasDraggable=null)}function Un(t,e){if(e instanceof Zt){let n=t.docView.descAt(e.from);n!=t.lastSelectedViewDesc&&(Wn(t),n&&n.selectNode(),t.lastSelectedViewDesc=n)}else Wn(t)}function Wn(t){t.lastSelectedViewDesc&&(t.lastSelectedViewDesc.parent&&t.lastSelectedViewDesc.deselectNode(),t.lastSelectedViewDesc=void 0)}function Kn(t,e,n,r){return t.someProp("createSelectionBetween",(r=>r(t,e,n)))||Yt.between(e,n,r)}function zn(t){return!(t.editable&&!t.hasFocus())&&Gn(t)}function Gn(t){let e=t.domSelectionRange();if(!e.anchorNode)return!1;try{return t.dom.contains(3==e.anchorNode.nodeType?e.anchorNode.parentNode:e.anchorNode)&&(t.editable||t.dom.contains(3==e.focusNode.nodeType?e.focusNode.parentNode:e.focusNode))}catch(t){return!1}}function Vn(t,e){let{$anchor:n,$head:r}=t.selection,i=e>0?n.max(r):n.min(r),s=i.parent.inlineContent?i.depth?t.doc.resolve(e>0?i.after():i.before()):null:i;return s&&Gt.findFrom(s,e)}function _n(t,e){return t.dispatch(t.state.tr.setSelection(e).scrollIntoView()),!0}function Qn(t,e,n){let r=t.state.selection;if(!(r instanceof Yt)){if(r instanceof Zt&&r.node.isInline)return _n(t,new Yt(e>0?r.$to:r.$from));{let n=Vn(t.state,e);return!!n&&_n(t,n)}}if(n.indexOf("s")>-1){let{$head:n}=r,i=n.textOffset?null:e<0?n.nodeBefore:n.nodeAfter;if(!i||i.isText||!i.isLeaf)return!1;let s=t.state.doc.resolve(n.pos+i.nodeSize*(e<0?-1:1));return _n(t,new Yt(r.$anchor,s))}if(!r.empty)return!1;if(t.endOfTextblock(e>0?"forward":"backward")){let n=Vn(t.state,e);return!!(n&&n instanceof Zt)&&_n(t,n)}if(!(qe&&n.indexOf("m")>-1)){let n,i=r.$head,s=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter;if(!s||s.isText)return!1;let o=e<0?i.pos-s.nodeSize:i.pos;return!!(s.isAtom||(n=t.docView.descAt(o))&&!n.contentDOM)&&(Zt.isSelectable(s)?_n(t,new Zt(e<0?t.state.doc.resolve(i.pos-s.nodeSize):i)):!!We&&_n(t,new Yt(t.state.doc.resolve(e<0?o:o+s.nodeSize))))}}function Yn(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}function Xn(t,e){let n=t.pmViewDesc;return n&&0==n.size&&(e<0||t.nextSibling||"BR"!=t.nodeName)}function Zn(t,e){return e<0?function(t){let e=t.domSelectionRange(),n=e.focusNode,r=e.focusOffset;if(!n)return;let i,s,o=!1;for(Pe&&1==n.nodeType&&r<Yn(n)&&Xn(n.childNodes[r],-1)&&(o=!0);;)if(r>0){if(1!=n.nodeType)break;{let t=n.childNodes[r-1];if(Xn(t,-1))i=n,s=--r;else{if(3!=t.nodeType)break;n=t,r=n.nodeValue.length}}}else{if(tr(n))break;{let e=n.previousSibling;for(;e&&Xn(e,-1);)i=n.parentNode,s=we(e),e=e.previousSibling;if(e)n=e,r=Yn(n);else{if(n=n.parentNode,n==t.dom)break;r=0}}}o?er(t,n,r):i&&er(t,i,s)}(t):function(t){let e=t.domSelectionRange(),n=e.focusNode,r=e.focusOffset;if(!n)return;let i,s,o=Yn(n);for(;;)if(r<o){if(1!=n.nodeType)break;if(!Xn(n.childNodes[r],1))break;i=n,s=++r}else{if(tr(n))break;{let e=n.nextSibling;for(;e&&Xn(e,1);)i=e.parentNode,s=we(e)+1,e=e.nextSibling;if(e)n=e,r=0,o=Yn(n);else{if(n=n.parentNode,n==t.dom)break;r=o=0}}}i&&er(t,i,s)}(t)}function tr(t){let e=t.pmViewDesc;return e&&e.node&&e.node.isBlock}function er(t,e,n){if(3!=e.nodeType){let t,r;(r=function(t,e){for(;t&&e==t.childNodes.length&&!Oe(t);)e=we(t)+1,t=t.parentNode;for(;t&&e<t.childNodes.length;){let n=t.childNodes[e];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;t=n,e=0}}(e,n))?(e=r,n=0):(t=function(t,e){for(;t&&!e&&!Oe(t);)e=we(t),t=t.parentNode;for(;t&&e;){let n=t.childNodes[e-1];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;e=(t=n).childNodes.length}}(e,n))&&(e=t,n=t.nodeValue.length)}let r=t.domSelection();if(Se(r)){let t=document.createRange();t.setEnd(e,n),t.setStart(e,n),r.removeAllRanges(),r.addRange(t)}else r.extend&&r.extend(e,n);t.domObserver.setCurSelection();let{state:i}=t;setTimeout((()=>{t.state==i&&Jn(t)}),50)}function nr(t,e){let n=t.state.doc.resolve(e);if(!Be&&!He&&n.parent.inlineContent){let r=t.coordsAtPos(e);if(e>n.start()){let n=t.coordsAtPos(e-1),i=(n.top+n.bottom)/2;if(i>r.top&&i<r.bottom&&Math.abs(n.left-r.left)>1)return n.left<r.left?"ltr":"rtl"}if(e<n.end()){let n=t.coordsAtPos(e+1),i=(n.top+n.bottom)/2;if(i>r.top&&i<r.bottom&&Math.abs(n.left-r.left)>1)return n.left>r.left?"ltr":"rtl"}}return"rtl"==getComputedStyle(t.dom).direction?"rtl":"ltr"}function rr(t,e,n){let r=t.state.selection;if(r instanceof Yt&&!r.empty||n.indexOf("s")>-1)return!1;if(qe&&n.indexOf("m")>-1)return!1;let{$from:i,$to:s}=r;if(!i.parent.inlineContent||t.endOfTextblock(e<0?"up":"down")){let n=Vn(t.state,e);if(n&&n instanceof Zt)return _n(t,n)}if(!i.parent.inlineContent){let n=e<0?i:s,o=r instanceof ee?Gt.near(n,e):Gt.findFrom(n,e);return!!o&&_n(t,o)}return!1}function ir(t,e){if(!(t.state.selection instanceof Yt))return!0;let{$head:n,$anchor:r,empty:i}=t.state.selection;if(!n.sameParent(r))return!0;if(!i)return!1;if(t.endOfTextblock(e>0?"forward":"backward"))return!0;let s=!n.textOffset&&(e<0?n.nodeBefore:n.nodeAfter);if(s&&!s.isText){let r=t.state.tr;return e<0?r.delete(n.pos-s.nodeSize,n.pos):r.delete(n.pos,n.pos+s.nodeSize),t.dispatch(r),!0}return!1}function sr(t,e,n){t.domObserver.stop(),e.contentEditable=n,t.domObserver.start()}function or(t,e){t.someProp("transformCopied",(n=>{e=n(e,t)}));let n=[],{content:r,openStart:i,openEnd:s}=e;for(;i>1&&s>1&&1==r.childCount&&1==r.firstChild.childCount;){i--,s--;let t=r.firstChild;n.push(t.type.name,t.attrs!=t.type.defaultAttrs?t.attrs:null),r=t.content}let o=t.someProp("clipboardSerializer")||rt.fromSchema(t.state.schema),l=wr(),u=l.createElement("div");u.appendChild(o.serializeFragment(r,{document:l}));let h,f=u.firstChild,a=0;for(;f&&1==f.nodeType&&(h=pr[f.nodeName.toLowerCase()]);){for(let t=h.length-1;t>=0;t--){let e=l.createElement(h[t]);for(;u.firstChild;)e.appendChild(u.firstChild);u.appendChild(e),a++}f=u.firstChild}return f&&1==f.nodeType&&f.setAttribute("data-pm-slice",`${i} ${s}${a?` -${a}`:""} ${JSON.stringify(n)}`),{dom:u,text:t.someProp("clipboardTextSerializer",(n=>n(e,t)))||e.content.textBetween(0,e.content.size,"\n\n")}}function lr(t,e,n,i,s){let o,l,u=s.parent.type.spec.code;if(!n&&!e)return null;let f=e&&(i||u||!n);if(f){if(t.someProp("transformPastedText",(n=>{e=n(e,u||i,t)})),u)return e?new h(r.from(t.state.schema.text(e.replace(/\r\n?/g,"\n"))),0,0):h.empty;let n=t.someProp("clipboardTextParser",(n=>n(e,s,i,t)));if(n)l=n;else{let n=s.marks(),{schema:r}=t.state,i=rt.fromSchema(r);o=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach((t=>{let e=o.appendChild(document.createElement("p"));t&&e.appendChild(i.serializeNode(r.text(t,n)))}))}}else t.someProp("transformPastedHTML",(e=>{n=e(n,t)})),o=function(t){let e=/^(\s*<meta [^>]*>)*/.exec(t);e&&(t=t.slice(e[0].length));let n,r=wr().createElement("div"),i=/<([a-z][^>\s]+)/i.exec(t);if((n=i&&pr[i[1].toLowerCase()])&&(t=n.map((t=>"<"+t+">")).join("")+t+n.map((t=>"</"+t+">")).reverse().join("")),r.innerHTML=t,n)for(let t=0;t<n.length;t++)r=r.querySelector(n[t])||r;return r}(n),We&&function(t){let e=t.querySelectorAll(Be?"span:not([class]):not([style])":"span.Apple-converted-space");for(let n=0;n<e.length;n++){let r=e[n];1==r.childNodes.length&&" "==r.textContent&&r.parentNode&&r.parentNode.replaceChild(t.ownerDocument.createTextNode(" "),r)}}(o);let a=o&&o.querySelector("[data-pm-slice]"),c=a&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(a.getAttribute("data-pm-slice")||"");if(c&&c[3])for(let t=+c[3];t>0;t--){let t=o.firstChild;for(;t&&1!=t.nodeType;)t=t.nextSibling;if(!t)break;o=t}if(!l){let e=t.someProp("clipboardParser")||t.someProp("domParser")||G.fromSchema(t.state.schema);l=e.parseSlice(o,{preserveWhitespace:!(!f&&!c),context:s,ruleFromNode:t=>"BR"!=t.nodeName||t.nextSibling||!t.parentNode||ur.test(t.parentNode.nodeName)?null:{ignore:!0}})}if(c)l=function(t,e){if(!t.size)return t;let n,i=t.content.firstChild.type.schema;try{n=JSON.parse(e)}catch(e){return t}let{content:s,openStart:o,openEnd:l}=t;for(let t=n.length-2;t>=0;t-=2){let e=i.nodes[n[t]];if(!e||e.hasRequiredAttrs())break;s=r.from(e.create(n[t+1],s)),o++,l++}return new h(s,o,l)}(dr(l,+c[1],+c[2]),c[4]);else if(l=h.maxOpen(function(t,e){if(t.childCount<2)return t;for(let n=e.depth;n>=0;n--){let i,s=e.node(n).contentMatchAt(e.index(n)),o=[];if(t.forEach((t=>{if(!o)return;let e,n=s.findWrapping(t.type);if(!n)return o=null;if(e=o.length&&i.length&&fr(n,i,t,o[o.length-1],0))o[o.length-1]=e;else{o.length&&(o[o.length-1]=ar(o[o.length-1],i.length));let e=hr(t,n);o.push(e),s=s.matchType(e.type),i=n}})),o)return r.from(o)}return t}(l.content,s),!0),l.openStart||l.openEnd){let t=0,e=0;for(let e=l.content.firstChild;t<l.openStart&&!e.type.spec.isolating;t++,e=e.firstChild);for(let t=l.content.lastChild;e<l.openEnd&&!t.type.spec.isolating;e++,t=t.lastChild);l=dr(l,t,e)}return t.someProp("transformPasted",(e=>{l=e(l,t)})),l}const ur=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function hr(t,e,n=0){for(let i=e.length-1;i>=n;i--)t=e[i].create(null,r.from(t));return t}function fr(t,e,n,i,s){if(s<t.length&&s<e.length&&t[s]==e[s]){let o=fr(t,e,n,i.lastChild,s+1);if(o)return i.copy(i.content.replaceChild(i.childCount-1,o));if(i.contentMatchAt(i.childCount).matchType(s==t.length-1?n.type:t[s+1]))return i.copy(i.content.append(r.from(hr(n,t,s+1))))}}function ar(t,e){if(0==e)return t;let n=t.content.replaceChild(t.childCount-1,ar(t.lastChild,e-1)),i=t.contentMatchAt(t.childCount).fillBefore(r.empty,!0);return t.copy(n.append(i))}function cr(t,e,n,i,s,o){let l=e<0?t.firstChild:t.lastChild,u=l.content;return t.childCount>1&&(o=0),s<i-1&&(u=cr(u,e,n,i,s+1,o)),s>=n&&(u=e<0?l.contentMatchAt(0).fillBefore(u,o<=s).append(u):u.append(l.contentMatchAt(l.childCount).fillBefore(r.empty,!0))),t.replaceChild(e<0?0:t.childCount-1,l.copy(u))}function dr(t,e,n){return e<t.openStart&&(t=new h(cr(t.content,-1,e,t.openStart,0,t.openEnd),e,t.openEnd)),n<t.openEnd&&(t=new h(cr(t.content,1,n,t.openEnd,0,0),t.openStart,n)),t}const pr={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let mr=null;function wr(){return mr||(mr=document.implementation.createHTMLDocument("title"))}const vr={},gr={},br={touchstart:!0,touchmove:!0};class yr{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:""},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastAndroidDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function kr(t,e){t.input.lastSelectionOrigin=e,t.input.lastSelectionTime=Date.now()}function xr(t){t.someProp("handleDOMEvents",(e=>{for(let n in e)t.input.eventHandlers[n]||t.dom.addEventListener(n,t.input.eventHandlers[n]=e=>Mr(t,e))}))}function Mr(t,e){return t.someProp("handleDOMEvents",(n=>{let r=n[e.type];return!!r&&(r(t,e)||e.defaultPrevented)}))}function Or(t,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let n=e.target;n!=t.dom;n=n.parentNode)if(!n||11==n.nodeType||n.pmViewDesc&&n.pmViewDesc.stopEvent(e))return!1;return!0}function Sr(t){return{left:t.clientX,top:t.clientY}}function Er(t,e,n,r,i){if(-1==r)return!1;let s=t.state.doc.resolve(r);for(let r=s.depth+1;r>0;r--)if(t.someProp(e,(e=>r>s.depth?e(t,n,s.nodeAfter,s.before(r),i,!0):e(t,n,s.node(r),s.before(r),i,!1))))return!0;return!1}function Nr(t,e,n){t.focused||t.focus();let r=t.state.tr.setSelection(e);"pointer"==n&&r.setMeta("pointer",!0),t.dispatch(r)}function Cr(t,e,n,r){return Er(t,"handleDoubleClickOn",e,n,r)||t.someProp("handleDoubleClick",(n=>n(t,e,r)))}function Rr(t,e,n,r){return Er(t,"handleTripleClickOn",e,n,r)||t.someProp("handleTripleClick",(n=>n(t,e,r)))||function(t,e,n){if(0!=n.button)return!1;let r=t.state.doc;if(-1==e)return!!r.inlineContent&&(Nr(t,Yt.create(r,0,r.content.size),"pointer"),!0);let i=r.resolve(e);for(let e=i.depth+1;e>0;e--){let n=e>i.depth?i.nodeAfter:i.node(e),s=i.before(e);if(n.inlineContent)Nr(t,Yt.create(r,s+1,s+1+n.content.size),"pointer");else{if(!Zt.isSelectable(n))continue;Nr(t,Zt.create(r,s),"pointer")}return!0}}(t,n,r)}function Tr(t){return Br(t)}gr.keydown=(t,e)=>{let n=e;if(t.input.shiftKey=16==n.keyCode||n.shiftKey,!$r(t,n)&&(t.input.lastKeyCode=n.keyCode,t.input.lastKeyCodeTime=Date.now(),!Ue||!Be||13!=n.keyCode))if(229!=n.keyCode&&t.domObserver.forceFlush(),!Le||13!=n.keyCode||n.ctrlKey||n.altKey||n.metaKey)t.someProp("handleKeyDown",(e=>e(t,n)))||function(t,e){let n=e.keyCode,r=function(t){let e="";return t.ctrlKey&&(e+="c"),t.metaKey&&(e+="m"),t.altKey&&(e+="a"),t.shiftKey&&(e+="s"),e}(e);if(8==n||qe&&72==n&&"c"==r)return ir(t,-1)||Zn(t,-1);if(46==n&&!e.shiftKey||qe&&68==n&&"c"==r)return ir(t,1)||Zn(t,1);if(13==n||27==n)return!0;if(37==n||qe&&66==n&&"c"==r){let e=37==n?"ltr"==nr(t,t.state.selection.from)?-1:1:-1;return Qn(t,e,r)||Zn(t,e)}if(39==n||qe&&70==n&&"c"==r){let e=39==n?"ltr"==nr(t,t.state.selection.from)?1:-1:1;return Qn(t,e,r)||Zn(t,e)}return 38==n||qe&&80==n&&"c"==r?rr(t,-1,r)||Zn(t,-1):40==n||qe&&78==n&&"c"==r?function(t){if(!Fe||t.state.selection.$head.parentOffset>0)return!1;let{focusNode:e,focusOffset:n}=t.domSelectionRange();if(e&&1==e.nodeType&&0==n&&e.firstChild&&"false"==e.firstChild.contentEditable){let n=e.firstChild;sr(t,n,"true"),setTimeout((()=>sr(t,n,"false")),20)}return!1}(t)||rr(t,1,r)||Zn(t,1):r==(qe?"m":"c")&&(66==n||73==n||89==n||90==n)}(t,n)?n.preventDefault():kr(t,"key");else{let e=Date.now();t.input.lastIOSEnter=e,t.input.lastIOSEnterFallbackTimeout=setTimeout((()=>{t.input.lastIOSEnter==e&&(t.someProp("handleKeyDown",(e=>e(t,Ee(13,"Enter")))),t.input.lastIOSEnter=0)}),200)}},gr.keyup=(t,e)=>{16==e.keyCode&&(t.input.shiftKey=!1)},gr.keypress=(t,e)=>{let n=e;if($r(t,n)||!n.charCode||n.ctrlKey&&!n.altKey||qe&&n.metaKey)return;if(t.someProp("handleKeyPress",(e=>e(t,n))))return void n.preventDefault();let r=t.state.selection;if(!(r instanceof Yt&&r.$from.sameParent(r.$to))){let e=String.fromCharCode(n.charCode);/[\r\n]/.test(e)||t.someProp("handleTextInput",(n=>n(t,r.$from.pos,r.$to.pos,e)))||t.dispatch(t.state.tr.insertText(e).scrollIntoView()),n.preventDefault()}};const jr=qe?"metaKey":"ctrlKey";vr.mousedown=(t,e)=>{let n=e;t.input.shiftKey=n.shiftKey;let r=Tr(t),i=Date.now(),s="singleClick";i-t.input.lastClick.time<500&&function(t,e){let n=e.x-t.clientX,r=e.y-t.clientY;return n*n+r*r<100}(n,t.input.lastClick)&&!n[jr]&&("singleClick"==t.input.lastClick.type?s="doubleClick":"doubleClick"==t.input.lastClick.type&&(s="tripleClick")),t.input.lastClick={time:i,x:n.clientX,y:n.clientY,type:s};let o=t.posAtCoords(Sr(n));o&&("singleClick"==s?(t.input.mouseDown&&t.input.mouseDown.done(),t.input.mouseDown=new Ar(t,o,n,!!r)):("doubleClick"==s?Cr:Rr)(t,o.pos,o.inside,n)?n.preventDefault():kr(t,"pointer"))};class Ar{constructor(t,e,n,r){let i,s;if(this.view=t,this.pos=e,this.event=n,this.flushed=r,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=t.state.doc,this.selectNode=!!n[jr],this.allowDefault=n.shiftKey,e.inside>-1)i=t.state.doc.nodeAt(e.inside),s=e.inside;else{let n=t.state.doc.resolve(e.pos);i=n.parent,s=n.depth?n.before():0}const o=r?null:n.target,l=o?t.docView.nearestDesc(o,!0):null;this.target=l?l.dom:null;let{selection:u}=t.state;(0==n.button&&i.type.spec.draggable&&!1!==i.type.spec.selectable||u instanceof Zt&&u.from<=s&&u.to>s)&&(this.mightDrag={node:i,pos:s,addAttr:!(!this.target||this.target.draggable),setUneditable:!(!this.target||!Pe||this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout((()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")}),20),this.view.domObserver.start()),t.root.addEventListener("mouseup",this.up=this.up.bind(this)),t.root.addEventListener("mousemove",this.move=this.move.bind(this)),kr(t,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout((()=>Jn(this.view))),this.view.input.mouseDown=null}up(t){if(this.done(),!this.view.dom.contains(t.target))return;let e=this.pos;this.view.state.doc!=this.startDoc&&(e=this.view.posAtCoords(Sr(t))),this.updateAllowDefault(t),this.allowDefault||!e?kr(this.view,"pointer"):function(t,e,n,r,i){return Er(t,"handleClickOn",e,n,r)||t.someProp("handleClick",(n=>n(t,e,r)))||(i?function(t,e){if(-1==e)return!1;let n,r,i=t.state.selection;i instanceof Zt&&(n=i.node);let s=t.state.doc.resolve(e);for(let t=s.depth+1;t>0;t--){let e=t>s.depth?s.nodeAfter:s.node(t);if(Zt.isSelectable(e)){r=n&&i.$from.depth>0&&t>=i.$from.depth&&s.before(i.$from.depth+1)==i.$from.pos?s.before(i.$from.depth):s.before(t);break}}return null!=r&&(Nr(t,Zt.create(t.state.doc,r),"pointer"),!0)}(t,n):function(t,e){if(-1==e)return!1;let n=t.state.doc.resolve(e),r=n.nodeAfter;return!!(r&&r.isAtom&&Zt.isSelectable(r))&&(Nr(t,new Zt(n),"pointer"),!0)}(t,n))}(this.view,e.pos,e.inside,t,this.selectNode)?t.preventDefault():0==t.button&&(this.flushed||Fe&&this.mightDrag&&!this.mightDrag.node.isAtom||Be&&!this.view.state.selection.visible&&Math.min(Math.abs(e.pos-this.view.state.selection.from),Math.abs(e.pos-this.view.state.selection.to))<=2)?(Nr(this.view,Gt.near(this.view.state.doc.resolve(e.pos)),"pointer"),t.preventDefault()):kr(this.view,"pointer")}move(t){this.updateAllowDefault(t),kr(this.view,"pointer"),0==t.buttons&&this.done()}updateAllowDefault(t){!this.allowDefault&&(Math.abs(this.event.x-t.clientX)>4||Math.abs(this.event.y-t.clientY)>4)&&(this.allowDefault=!0)}}function $r(t,e){return!!t.composing||!!(Fe&&Math.abs(e.timeStamp-t.input.compositionEndedAt)<500)&&(t.input.compositionEndedAt=-2e8,!0)}vr.touchstart=t=>{t.input.lastTouch=Date.now(),Tr(t),kr(t,"pointer")},vr.touchmove=t=>{t.input.lastTouch=Date.now(),kr(t,"pointer")},vr.contextmenu=t=>Tr(t);const Dr=Ue?5e3:-1;function Pr(t,e){clearTimeout(t.input.composingTimeout),e>-1&&(t.input.composingTimeout=setTimeout((()=>Br(t)),e))}function Ir(t){for(t.composing&&(t.input.composing=!1,t.input.compositionEndedAt=function(){let t=document.createEvent("Event");return t.initEvent("event",!0,!0),t.timeStamp}());t.input.compositionNodes.length>0;)t.input.compositionNodes.pop().markParentsDirty()}function Br(t,e=!1){if(!(Ue&&t.domObserver.flushingSoon>=0)){if(t.domObserver.forceFlush(),Ir(t),e||t.docView&&t.docView.dirty){let e=In(t);return e&&!e.eq(t.state.selection)?t.dispatch(t.state.tr.setSelection(e)):t.updateState(t.state),!0}return!1}}gr.compositionstart=gr.compositionupdate=t=>{if(!t.composing){t.domObserver.flush();let{state:e}=t,n=e.selection.$from;if(e.selection.empty&&(e.storedMarks||!n.textOffset&&n.parentOffset&&n.nodeBefore.marks.some((t=>!1===t.type.spec.inclusive))))t.markCursor=t.state.storedMarks||n.marks(),Br(t,!0),t.markCursor=null;else if(Br(t),Pe&&e.selection.empty&&n.parentOffset&&!n.textOffset&&n.nodeBefore.marks.length){let e=t.domSelectionRange();for(let n=e.focusNode,r=e.focusOffset;n&&1==n.nodeType&&0!=r;){let e=r<0?n.lastChild:n.childNodes[r-1];if(!e)break;if(3==e.nodeType){t.domSelection().collapse(e,e.nodeValue.length);break}n=e,r=-1}}t.input.composing=!0}Pr(t,Dr)},gr.compositionend=(t,e)=>{t.composing&&(t.input.composing=!1,t.input.compositionEndedAt=e.timeStamp,t.input.compositionPendingChanges=t.domObserver.pendingRecords().length?t.input.compositionID:0,t.input.compositionNode=null,t.input.compositionPendingChanges&&Promise.resolve().then((()=>t.domObserver.flush())),t.input.compositionID++,Pr(t,20))};const Jr=$e&&De<15||Le&&Ke<604;function Fr(t,e,n,r,i){let s=lr(t,e,n,r,t.state.selection.$from);if(t.someProp("handlePaste",(e=>e(t,i,s||h.empty))))return!0;if(!s)return!1;let o=function(t){return 0==t.openStart&&0==t.openEnd&&1==t.content.childCount?t.content.firstChild:null}(s),l=o?t.state.tr.replaceSelectionWith(o,r):t.state.tr.replaceSelection(s);return t.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function Lr(t){let e=t.getData("text/plain")||t.getData("Text");if(e)return e;let n=t.getData("text/uri-list");return n?n.replace(/\r?\n/g," "):""}vr.copy=gr.cut=(t,e)=>{let n=e,r=t.state.selection,i="cut"==n.type;if(r.empty)return;let s=Jr?null:n.clipboardData,o=r.content(),{dom:l,text:u}=or(t,o);s?(n.preventDefault(),s.clearData(),s.setData("text/html",l.innerHTML),s.setData("text/plain",u)):function(t,e){if(!t.dom.parentNode)return;let n=t.dom.parentNode.appendChild(document.createElement("div"));n.appendChild(e),n.style.cssText="position: fixed; left: -10000px; top: 10px";let r=getSelection(),i=document.createRange();i.selectNodeContents(e),t.dom.blur(),r.removeAllRanges(),r.addRange(i),setTimeout((()=>{n.parentNode&&n.parentNode.removeChild(n),t.focus()}),50)}(t,l),i&&t.dispatch(t.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))},gr.paste=(t,e)=>{let n=e;if(t.composing&&!Ue)return;let r=Jr?null:n.clipboardData,i=t.input.shiftKey&&45!=t.input.lastKeyCode;r&&Fr(t,Lr(r),r.getData("text/html"),i,n)?n.preventDefault():function(t,e){if(!t.dom.parentNode)return;let n=t.input.shiftKey||t.state.selection.$from.parent.type.spec.code,r=t.dom.parentNode.appendChild(document.createElement(n?"textarea":"div"));n||(r.contentEditable="true"),r.style.cssText="position: fixed; left: -10000px; top: 10px",r.focus();let i=t.input.shiftKey&&45!=t.input.lastKeyCode;setTimeout((()=>{t.focus(),r.parentNode&&r.parentNode.removeChild(r),n?Fr(t,r.value,null,i,e):Fr(t,r.textContent,r.innerHTML,i,e)}),50)}(t,n)};class qr{constructor(t,e,n){this.slice=t,this.move=e,this.node=n}}const Hr=qe?"altKey":"ctrlKey";vr.dragstart=(t,e)=>{let n=e,r=t.input.mouseDown;if(r&&r.done(),!n.dataTransfer)return;let i,s=t.state.selection,o=s.empty?null:t.posAtCoords(Sr(n));if(o&&o.pos>=s.from&&o.pos<=(s instanceof Zt?s.to-1:s.to));else if(r&&r.mightDrag)i=Zt.create(t.state.doc,r.mightDrag.pos);else if(n.target&&1==n.target.nodeType){let e=t.docView.nearestDesc(n.target,!0);e&&e.node.type.spec.draggable&&e!=t.docView&&(i=Zt.create(t.state.doc,e.posBefore))}let l=(i||t.state.selection).content(),{dom:u,text:h}=or(t,l);n.dataTransfer.clearData(),n.dataTransfer.setData(Jr?"Text":"text/html",u.innerHTML),n.dataTransfer.effectAllowed="copyMove",Jr||n.dataTransfer.setData("text/plain",h),t.dragging=new qr(l,!n[Hr],i)},vr.dragend=t=>{let e=t.dragging;window.setTimeout((()=>{t.dragging==e&&(t.dragging=null)}),50)},gr.dragover=gr.dragenter=(t,e)=>e.preventDefault(),gr.drop=(t,e)=>{let n=e,r=t.dragging;if(t.dragging=null,!n.dataTransfer)return;let i=t.posAtCoords(Sr(n));if(!i)return;let s=t.state.doc.resolve(i.pos),o=r&&r.slice;o?t.someProp("transformPasted",(e=>{o=e(o,t)})):o=lr(t,Lr(n.dataTransfer),Jr?null:n.dataTransfer.getData("text/html"),!1,s);let l=!(!r||n[Hr]);if(t.someProp("handleDrop",(e=>e(t,n,o||h.empty,l))))return void n.preventDefault();if(!o)return;n.preventDefault();let u=o?jt(t.state.doc,s.pos,o):s.pos;null==u&&(u=s.pos);let f=t.state.tr;if(l){let{node:t}=r;t?t.replace(f):f.deleteSelection()}let a=f.mapping.map(u),c=0==o.openStart&&0==o.openEnd&&1==o.content.childCount,d=f.doc;if(c?f.replaceRangeWith(a,a,o.content.firstChild):f.replaceRange(a,a,o),f.doc.eq(d))return;let p=f.doc.resolve(a);if(c&&Zt.isSelectable(o.content.firstChild)&&p.nodeAfter&&p.nodeAfter.sameMarkup(o.content.firstChild))f.setSelection(new Zt(p));else{let e=f.mapping.map(u);f.mapping.maps[f.mapping.maps.length-1].forEach(((t,n,r,i)=>e=i)),f.setSelection(Kn(t,p,f.doc.resolve(e)))}t.focus(),t.dispatch(f.setMeta("uiEvent","drop"))},vr.focus=t=>{t.input.lastFocus=Date.now(),t.focused||(t.domObserver.stop(),t.dom.classList.add("ProseMirror-focused"),t.domObserver.start(),t.focused=!0,setTimeout((()=>{t.docView&&t.hasFocus()&&!t.domObserver.currentSelection.eq(t.domSelectionRange())&&Jn(t)}),20))},vr.blur=(t,e)=>{let n=e;t.focused&&(t.domObserver.stop(),t.dom.classList.remove("ProseMirror-focused"),t.domObserver.start(),n.relatedTarget&&t.dom.contains(n.relatedTarget)&&t.domObserver.currentSelection.clear(),t.focused=!1)},vr.beforeinput=(t,e)=>{if(Be&&Ue&&"deleteContentBackward"==e.inputType){t.domObserver.flushSoon();let{domChangeCount:e}=t.input;setTimeout((()=>{if(t.input.domChangeCount!=e)return;if(t.dom.blur(),t.focus(),t.someProp("handleKeyDown",(e=>e(t,Ee(8,"Backspace")))))return;let{$cursor:n}=t.state.selection;n&&n.pos>0&&t.dispatch(t.state.tr.delete(n.pos-1,n.pos).scrollIntoView())}),50)}};for(let t in gr)vr[t]=gr[t];function Ur(t,e){if(t==e)return!0;for(let n in t)if(t[n]!==e[n])return!1;for(let n in e)if(!(n in t))return!1;return!0}class Wr{constructor(t,e){this.toDOM=t,this.spec=e||_r,this.side=this.spec.side||0}map(t,e,n,r){let{pos:i,deleted:s}=t.mapResult(e.from+r,this.side<0?-1:1);return s?null:new Gr(i-n,i-n,this)}valid(){return!0}eq(t){return this==t||t instanceof Wr&&(this.spec.key&&this.spec.key==t.spec.key||this.toDOM==t.toDOM&&Ur(this.spec,t.spec))}destroy(t){this.spec.destroy&&this.spec.destroy(t)}}class Kr{constructor(t,e){this.attrs=t,this.spec=e||_r}map(t,e,n,r){let i=t.map(e.from+r,this.spec.inclusiveStart?-1:1)-n,s=t.map(e.to+r,this.spec.inclusiveEnd?1:-1)-n;return i>=s?null:new Gr(i,s,this)}valid(t,e){return e.from<e.to}eq(t){return this==t||t instanceof Kr&&Ur(this.attrs,t.attrs)&&Ur(this.spec,t.spec)}static is(t){return t.type instanceof Kr}destroy(){}}class zr{constructor(t,e){this.attrs=t,this.spec=e||_r}map(t,e,n,r){let i=t.mapResult(e.from+r,1);if(i.deleted)return null;let s=t.mapResult(e.to+r,-1);return s.deleted||s.pos<=i.pos?null:new Gr(i.pos-n,s.pos-n,this)}valid(t,e){let n,{index:r,offset:i}=t.content.findIndex(e.from);return i==e.from&&!(n=t.child(r)).isText&&i+n.nodeSize==e.to}eq(t){return this==t||t instanceof zr&&Ur(this.attrs,t.attrs)&&Ur(this.spec,t.spec)}destroy(){}}class Gr{constructor(t,e,n){this.from=t,this.to=e,this.type=n}copy(t,e){return new Gr(t,e,this.type)}eq(t,e=0){return this.type.eq(t.type)&&this.from+e==t.from&&this.to+e==t.to}map(t,e,n){return this.type.map(t,this,e,n)}static widget(t,e,n){return new Gr(t,t,new Wr(e,n))}static inline(t,e,n,r){return new Gr(t,e,new Kr(n,r))}static node(t,e,n,r){return new Gr(t,e,new zr(n,r))}get spec(){return this.type.spec}get inline(){return this.type instanceof Kr}get widget(){return this.type instanceof Wr}}const Vr=[],_r={};class Qr{constructor(t,e){this.local=t.length?t:Vr,this.children=e.length?e:Vr}static create(t,e){return e.length?ni(e,t,0,_r):Yr}find(t,e,n){let r=[];return this.findInner(null==t?0:t,null==e?1e9:e,r,0,n),r}findInner(t,e,n,r,i){for(let s=0;s<this.local.length;s++){let o=this.local[s];o.from<=e&&o.to>=t&&(!i||i(o.spec))&&n.push(o.copy(o.from+r,o.to+r))}for(let s=0;s<this.children.length;s+=3)if(this.children[s]<e&&this.children[s+1]>t){let o=this.children[s]+1;this.children[s+2].findInner(t-o,e-o,n,r+o,i)}}map(t,e,n){return this==Yr||0==t.maps.length?this:this.mapInner(t,e,0,0,n||_r)}mapInner(t,e,n,r,i){let s;for(let o=0;o<this.local.length;o++){let l=this.local[o].map(t,n,r);l&&l.type.valid(e,l)?(s||(s=[])).push(l):i.onRemove&&i.onRemove(this.local[o].spec)}return this.children.length?function(t,e,n,r,i,s,o){let l=t.slice();for(let t=0,e=s;t<n.maps.length;t++){let r=0;n.maps[t].forEach(((t,n,i,s)=>{let o=s-i-(n-t);for(let i=0;i<l.length;i+=3){let s=l[i+1];if(s<0||t>s+e-r)continue;let u=l[i]+e-r;n>=u?l[i+1]=t<=u?-2:-1:t>=e&&o&&(l[i]+=o,l[i+1]+=o)}r+=o})),e=n.maps[t].map(e,-1)}let u=!1;for(let e=0;e<l.length;e+=3)if(l[e+1]<0){if(-2==l[e+1]){u=!0,l[e+1]=-1;continue}let h=n.map(t[e]+s),f=h-i;if(f<0||f>=r.content.size){u=!0;continue}let a=n.map(t[e+1]+s,-1)-i,{index:c,offset:d}=r.content.findIndex(f),p=r.maybeChild(c);if(p&&d==f&&d+p.nodeSize==a){let r=l[e+2].mapInner(n,p,h+1,t[e]+s+1,o);r!=Yr?(l[e]=f,l[e+1]=a,l[e+2]=r):(l[e+1]=-2,u=!0)}else u=!0}if(u){let u=function(t,e,n,r,i,s,o){function l(t,e){for(let s=0;s<t.local.length;s++){let l=t.local[s].map(r,i,e);l?n.push(l):o.onRemove&&o.onRemove(t.local[s].spec)}for(let n=0;n<t.children.length;n+=3)l(t.children[n+2],t.children[n]+e+1)}for(let n=0;n<t.length;n+=3)-1==t[n+1]&&l(t[n+2],e[n]+s+1);return n}(l,t,e,n,i,s,o),h=ni(u,r,0,o);e=h.local;for(let t=0;t<l.length;t+=3)l[t+1]<0&&(l.splice(t,3),t-=3);for(let t=0,e=0;t<h.children.length;t+=3){let n=h.children[t];for(;e<l.length&&l[e]<n;)e+=3;l.splice(e,0,h.children[t],h.children[t+1],h.children[t+2])}}return new Qr(e.sort(ri),l)}(this.children,s||[],t,e,n,r,i):s?new Qr(s.sort(ri),Vr):Yr}add(t,e){return e.length?this==Yr?Qr.create(t,e):this.addInner(t,e,0):this}addInner(t,e,n){let r,i=0;t.forEach(((t,s)=>{let o,l=s+n;if(o=ti(e,t,l)){for(r||(r=this.children.slice());i<r.length&&r[i]<s;)i+=3;r[i]==s?r[i+2]=r[i+2].addInner(t,o,l+1):r.splice(i,0,s,s+t.nodeSize,ni(o,t,l+1,_r)),i+=3}}));let s=Zr(i?ei(e):e,-n);for(let e=0;e<s.length;e++)s[e].type.valid(t,s[e])||s.splice(e--,1);return new Qr(s.length?this.local.concat(s).sort(ri):this.local,r||this.children)}remove(t){return 0==t.length||this==Yr?this:this.removeInner(t,0)}removeInner(t,e){let n=this.children,r=this.local;for(let r=0;r<n.length;r+=3){let i,s=n[r]+e,o=n[r+1]+e;for(let e,n=0;n<t.length;n++)(e=t[n])&&e.from>s&&e.to<o&&(t[n]=null,(i||(i=[])).push(e));if(!i)continue;n==this.children&&(n=this.children.slice());let l=n[r+2].removeInner(i,s+1);l!=Yr?n[r+2]=l:(n.splice(r,3),r-=3)}if(r.length)for(let n,i=0;i<t.length;i++)if(n=t[i])for(let t=0;t<r.length;t++)r[t].eq(n,e)&&(r==this.local&&(r=this.local.slice()),r.splice(t--,1));return n==this.children&&r==this.local?this:r.length||n.length?new Qr(r,n):Yr}forChild(t,e){if(this==Yr)return this;if(e.isLeaf)return Qr.empty;let n,r;for(let e=0;e<this.children.length;e+=3)if(this.children[e]>=t){this.children[e]==t&&(n=this.children[e+2]);break}let i=t+1,s=i+e.content.size;for(let t=0;t<this.local.length;t++){let e=this.local[t];if(e.from<s&&e.to>i&&e.type instanceof Kr){let t=Math.max(i,e.from)-i,n=Math.min(s,e.to)-i;t<n&&(r||(r=[])).push(e.copy(t,n))}}if(r){let t=new Qr(r.sort(ri),Vr);return n?new Xr([t,n]):t}return n||Yr}eq(t){if(this==t)return!0;if(!(t instanceof Qr)||this.local.length!=t.local.length||this.children.length!=t.children.length)return!1;for(let e=0;e<this.local.length;e++)if(!this.local[e].eq(t.local[e]))return!1;for(let e=0;e<this.children.length;e+=3)if(this.children[e]!=t.children[e]||this.children[e+1]!=t.children[e+1]||!this.children[e+2].eq(t.children[e+2]))return!1;return!0}locals(t){return ii(this.localsInner(t))}localsInner(t){if(this==Yr)return Vr;if(t.inlineContent||!this.local.some(Kr.is))return this.local;let e=[];for(let t=0;t<this.local.length;t++)this.local[t].type instanceof Kr||e.push(this.local[t]);return e}}Qr.empty=new Qr([],[]),Qr.removeOverlap=ii;const Yr=Qr.empty;class Xr{constructor(t){this.members=t}map(t,e){const n=this.members.map((n=>n.map(t,e,_r)));return Xr.from(n)}forChild(t,e){if(e.isLeaf)return Qr.empty;let n=[];for(let r=0;r<this.members.length;r++){let i=this.members[r].forChild(t,e);i!=Yr&&(i instanceof Xr?n=n.concat(i.members):n.push(i))}return Xr.from(n)}eq(t){if(!(t instanceof Xr)||t.members.length!=this.members.length)return!1;for(let e=0;e<this.members.length;e++)if(!this.members[e].eq(t.members[e]))return!1;return!0}locals(t){let e,n=!0;for(let r=0;r<this.members.length;r++){let i=this.members[r].localsInner(t);if(i.length)if(e){n&&(e=e.slice(),n=!1);for(let t=0;t<i.length;t++)e.push(i[t])}else e=i}return e?ii(n?e:e.sort(ri)):Vr}static from(t){switch(t.length){case 0:return Yr;case 1:return t[0];default:return new Xr(t.every((t=>t instanceof Qr))?t:t.reduce(((t,e)=>t.concat(e instanceof Qr?e:e.members)),[]))}}}function Zr(t,e){if(!e||!t.length)return t;let n=[];for(let r=0;r<t.length;r++){let i=t[r];n.push(new Gr(i.from+e,i.to+e,i.type))}return n}function ti(t,e,n){if(e.isLeaf)return null;let r=n+e.nodeSize,i=null;for(let e,s=0;s<t.length;s++)(e=t[s])&&e.from>n&&e.to<r&&((i||(i=[])).push(e),t[s]=null);return i}function ei(t){let e=[];for(let n=0;n<t.length;n++)null!=t[n]&&e.push(t[n]);return e}function ni(t,e,n,r){let i=[],s=!1;e.forEach(((e,o)=>{let l=ti(t,e,o+n);if(l){s=!0;let t=ni(l,e,n+o+1,r);t!=Yr&&i.push(o,o+e.nodeSize,t)}}));let o=Zr(s?ei(t):t,-n).sort(ri);for(let t=0;t<o.length;t++)o[t].type.valid(e,o[t])||(r.onRemove&&r.onRemove(o[t].spec),o.splice(t--,1));return o.length||i.length?new Qr(o,i):Yr}function ri(t,e){return t.from-e.from||t.to-e.to}function ii(t){let e=t;for(let n=0;n<e.length-1;n++){let r=e[n];if(r.from!=r.to)for(let i=n+1;i<e.length;i++){let s=e[i];if(s.from!=r.from){s.from<r.to&&(e==t&&(e=t.slice()),e[n]=r.copy(r.from,s.from),si(e,i,r.copy(s.from,r.to)));break}s.to!=r.to&&(e==t&&(e=t.slice()),e[i]=s.copy(s.from,r.to),si(e,i+1,s.copy(r.to,s.to)))}}return e}function si(t,e,n){for(;e<t.length&&ri(n,t[e])>0;)e++;t.splice(e,0,n)}function oi(t){let e=[];return t.someProp("decorations",(n=>{let r=n(t.state);r&&r!=Yr&&e.push(r)})),t.cursorWrapper&&e.push(Qr.create(t.state.doc,[t.cursorWrapper.deco])),Xr.from(e)}const li={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},ui=$e&&De<=11;class hi{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(t){this.anchorNode=t.anchorNode,this.anchorOffset=t.anchorOffset,this.focusNode=t.focusNode,this.focusOffset=t.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(t){return t.anchorNode==this.anchorNode&&t.anchorOffset==this.anchorOffset&&t.focusNode==this.focusNode&&t.focusOffset==this.focusOffset}}class fi{constructor(t,e){this.view=t,this.handleDOMChange=e,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new hi,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.observer=window.MutationObserver&&new window.MutationObserver((t=>{for(let e=0;e<t.length;e++)this.queue.push(t[e]);$e&&De<=11&&t.some((t=>"childList"==t.type&&t.removedNodes.length||"characterData"==t.type&&t.oldValue.length>t.target.nodeValue.length))?this.flushSoon():this.flush()})),ui&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout((()=>{this.flushingSoon=-1,this.flush()}),20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,li)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let t=this.observer.takeRecords();if(t.length){for(let e=0;e<t.length;e++)this.queue.push(t[e]);window.setTimeout((()=>this.flush()),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout((()=>this.suppressingSelectionUpdates=!1),50)}onSelectionChange(){if(zn(this.view)){if(this.suppressingSelectionUpdates)return Jn(this.view);if($e&&De<=11&&!this.view.state.selection.empty){let t=this.view.domSelectionRange();if(t.focusNode&&ye(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(t){if(!t.focusNode)return!0;let e,n=new Set;for(let e=t.focusNode;e;e=ve(e))n.add(e);for(let r=t.anchorNode;r;r=ve(r))if(n.has(r)){e=r;break}let r=e&&this.view.docView.nearestDesc(e);return r&&r.ignoreMutation({type:"selection",target:3==e.nodeType?e.parentNode:e})?(this.setCurSelection(),!0):void 0}pendingRecords(){if(this.observer)for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}flush(){let{view:t}=this;if(!t.docView||this.flushingSoon>-1)return;let e=this.pendingRecords();e.length&&(this.queue=[]);let n=t.domSelectionRange(),r=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(n)&&zn(t)&&!this.ignoreSelectionChange(n),i=-1,s=-1,o=!1,l=[];if(t.editable)for(let t=0;t<e.length;t++){let n=this.registerMutation(e[t],l);n&&(i=i<0?n.from:Math.min(n.from,i),s=s<0?n.to:Math.max(n.to,s),n.typeOver&&(o=!0))}if(Pe&&l.length>1){let t=l.filter((t=>"BR"==t.nodeName));if(2==t.length){let e=t[0],n=t[1];e.parentNode&&e.parentNode.parentNode==n.parentNode?n.remove():e.remove()}}let u=null;i<0&&r&&t.input.lastFocus>Date.now()-200&&Math.max(t.input.lastTouch,t.input.lastClick.time)<Date.now()-300&&Se(n)&&(u=In(t))&&u.eq(Gt.near(t.state.doc.resolve(0),1))?(t.input.lastFocus=0,Jn(t),this.currentSelection.set(n),t.scrollToSelection()):(i>-1||r)&&(i>-1&&(t.docView.markDirty(i,s),function(t){if(!ai.has(t)&&(ai.set(t,null),-1!==["normal","nowrap","pre-line"].indexOf(getComputedStyle(t.dom).whiteSpace))){if(t.requiresGeckoHackNode=Pe,ci)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),ci=!0}}(t)),this.handleDOMChange(i,s,o,l),t.docView&&t.docView.dirty?t.updateState(t.state):this.currentSelection.eq(n)||Jn(t),this.currentSelection.set(n))}registerMutation(t,e){if(e.indexOf(t.target)>-1)return null;let n=this.view.docView.nearestDesc(t.target);if("attributes"==t.type&&(n==this.view.docView||"contenteditable"==t.attributeName||"style"==t.attributeName&&!t.oldValue&&!t.target.getAttribute("style")))return null;if(!n||n.ignoreMutation(t))return null;if("childList"==t.type){for(let n=0;n<t.addedNodes.length;n++)e.push(t.addedNodes[n]);if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(t.target))return{from:n.posBefore,to:n.posAfter};let r=t.previousSibling,i=t.nextSibling;if($e&&De<=11&&t.addedNodes.length)for(let e=0;e<t.addedNodes.length;e++){let{previousSibling:n,nextSibling:s}=t.addedNodes[e];(!n||Array.prototype.indexOf.call(t.addedNodes,n)<0)&&(r=n),(!s||Array.prototype.indexOf.call(t.addedNodes,s)<0)&&(i=s)}let s=r&&r.parentNode==t.target?we(r)+1:0,o=n.localPosFromDOM(t.target,s,-1),l=i&&i.parentNode==t.target?we(i):t.target.childNodes.length;return{from:o,to:n.localPosFromDOM(t.target,l,1)}}return"attributes"==t.type?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:{from:n.posAtStart,to:n.posAtEnd,typeOver:t.target.nodeValue==t.oldValue}}}let ai=new WeakMap,ci=!1;function di(t,e){let n=e.startContainer,r=e.startOffset,i=e.endContainer,s=e.endOffset,o=t.domAtPos(t.state.selection.anchor);return ye(o.node,o.offset,i,s)&&([n,r,i,s]=[i,s,n,r]),{anchorNode:n,anchorOffset:r,focusNode:i,focusOffset:s}}function pi(t){let e=t.pmViewDesc;if(e)return e.parseRule();if("BR"==t.nodeName&&t.parentNode){if(Fe&&/^(ul|ol)$/i.test(t.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}if(t.parentNode.lastChild==t||Fe&&/^(tr|table)$/i.test(t.parentNode.nodeName))return{ignore:!0}}else if("IMG"==t.nodeName&&t.getAttribute("mark-placeholder"))return{ignore:!0};return null}const mi=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function wi(t,e,n){return Math.max(n.anchor,n.head)>e.content.size?null:Kn(t,e.resolve(n.anchor),e.resolve(n.head))}function vi(t,e,n){let r=t.depth,i=e?t.end():t.pos;for(;r>0&&(e||t.indexAfter(r)==t.node(r).childCount);)r--,i++,e=!1;if(n){let e=t.node(r).maybeChild(t.indexAfter(r));for(;e&&!e.isLeaf;)e=e.firstChild,i++}return i}function gi(t){if(2!=t.length)return!1;let e=t.charCodeAt(0),n=t.charCodeAt(1);return e>=56320&&e<=57343&&n>=55296&&n<=56319}class bi{constructor(t,e){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new yr,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=e,this.state=e.state,this.directPlugins=e.plugins||[],this.directPlugins.forEach(Oi),this.dispatch=this.dispatch.bind(this),this.dom=t&&t.mount||document.createElement("div"),t&&(t.appendChild?t.appendChild(this.dom):"function"==typeof t?t(this.dom):t.mount&&(this.mounted=!0)),this.editable=xi(this),ki(this),this.nodeViews=Mi(this),this.docView=yn(this.state.doc,yi(this),oi(this),this.dom,this),this.domObserver=new fi(this,((t,e,n,i)=>function(t,e,n,i,s){let o=t.input.compositionPendingChanges||(t.composing?t.input.compositionID:0);if(t.input.compositionPendingChanges=0,e<0){let e=t.input.lastSelectionTime>Date.now()-50?t.input.lastSelectionOrigin:null,n=In(t,e);if(n&&!t.state.selection.eq(n)){if(Be&&Ue&&13===t.input.lastKeyCode&&Date.now()-100<t.input.lastKeyCodeTime&&t.someProp("handleKeyDown",(e=>e(t,Ee(13,"Enter")))))return;let r=t.state.tr.setSelection(n);"pointer"==e?r.setMeta("pointer",!0):"key"==e&&r.scrollIntoView(),o&&r.setMeta("composition",o),t.dispatch(r)}return}let l=t.state.doc.resolve(e),u=l.sharedDepth(n);e=l.before(u+1),n=t.state.doc.resolve(n).after(u+1);let h,f,a=t.state.selection,c=function(t,e,n){let r,{node:i,fromOffset:s,toOffset:o,from:l,to:u}=t.docView.parseRange(e,n),h=t.domSelectionRange(),f=h.anchorNode;if(f&&t.dom.contains(1==f.nodeType?f:f.parentNode)&&(r=[{node:f,offset:h.anchorOffset}],Se(h)||r.push({node:h.focusNode,offset:h.focusOffset})),Be&&8===t.input.lastKeyCode)for(let t=o;t>s;t--){let e=i.childNodes[t-1],n=e.pmViewDesc;if("BR"==e.nodeName&&!n){o=t;break}if(!n||n.size)break}let a=t.state.doc,c=t.someProp("domParser")||G.fromSchema(t.state.schema),d=a.resolve(l),p=null,m=c.parse(i,{topNode:d.parent,topMatch:d.parent.contentMatchAt(d.index()),topOpen:!0,from:s,to:o,preserveWhitespace:"pre"!=d.parent.type.whitespace||"full",findPositions:r,ruleFromNode:pi,context:d});if(r&&null!=r[0].pos){let t=r[0].pos,e=r[1]&&r[1].pos;null==e&&(e=t),p={anchor:t+l,head:e+l}}return{doc:m,sel:p,from:l,to:u}}(t,e,n),d=t.state.doc,p=d.slice(c.from,c.to);8===t.input.lastKeyCode&&Date.now()-100<t.input.lastKeyCodeTime?(h=t.state.selection.to,f="end"):(h=t.state.selection.from,f="start"),t.input.lastKeyCode=null;let m=function(t,e,n,r,i){let s=t.findDiffStart(e,n);if(null==s)return null;let{a:o,b:l}=t.findDiffEnd(e,n+t.size,n+e.size);if("end"==i&&(r-=o+Math.max(0,s-Math.min(o,l))-s),o<s&&t.size<e.size){let t=r<=s&&r>=o?s-r:0;s-=t,s&&s<e.size&&gi(e.textBetween(s-1,s+1))&&(s+=t?1:-1),l=s+(l-o),o=s}else if(l<s){let e=r<=s&&r>=l?s-r:0;s-=e,s&&s<t.size&&gi(t.textBetween(s-1,s+1))&&(s+=e?1:-1),o=s+(o-l),l=s}return{start:s,endA:o,endB:l}}(p.content,c.doc.content,c.from,h,f);if((Le&&t.input.lastIOSEnter>Date.now()-225||Ue)&&s.some((t=>1==t.nodeType&&!mi.test(t.nodeName)))&&(!m||m.endA>=m.endB)&&t.someProp("handleKeyDown",(e=>e(t,Ee(13,"Enter")))))return void(t.input.lastIOSEnter=0);if(!m){if(!(i&&a instanceof Yt&&!a.empty&&a.$head.sameParent(a.$anchor))||t.composing||c.sel&&c.sel.anchor!=c.sel.head){if(c.sel){let e=wi(t,t.state.doc,c.sel);if(e&&!e.eq(t.state.selection)){let n=t.state.tr.setSelection(e);o&&n.setMeta("composition",o),t.dispatch(n)}}return}m={start:a.from,endA:a.to,endB:a.to}}t.input.domChangeCount++,t.state.selection.from<t.state.selection.to&&m.start==m.endB&&t.state.selection instanceof Yt&&(m.start>t.state.selection.from&&m.start<=t.state.selection.from+2&&t.state.selection.from>=c.from?m.start=t.state.selection.from:m.endA<t.state.selection.to&&m.endA>=t.state.selection.to-2&&t.state.selection.to<=c.to&&(m.endB+=t.state.selection.to-m.endA,m.endA=t.state.selection.to)),$e&&De<=11&&m.endB==m.start+1&&m.endA==m.start&&m.start>c.from&&"  "==c.doc.textBetween(m.start-c.from-1,m.start-c.from+1)&&(m.start--,m.endA--,m.endB--);let w,v=c.doc.resolveNoCache(m.start-c.from),g=c.doc.resolveNoCache(m.endB-c.from),b=d.resolve(m.start),y=v.sameParent(g)&&v.parent.inlineContent&&b.end()>=m.endA;if((Le&&t.input.lastIOSEnter>Date.now()-225&&(!y||s.some((t=>"DIV"==t.nodeName||"P"==t.nodeName)))||!y&&v.pos<c.doc.content.size&&!v.sameParent(g)&&(w=Gt.findFrom(c.doc.resolve(v.pos+1),1,!0))&&w.head==g.pos)&&t.someProp("handleKeyDown",(e=>e(t,Ee(13,"Enter")))))return void(t.input.lastIOSEnter=0);if(t.state.selection.anchor>m.start&&function(t,e,n,r,i){if(n-e<=i.pos-r.pos||vi(r,!0,!1)<i.pos)return!1;let s=t.resolve(e);if(!r.parent.isTextblock){let t=s.nodeAfter;return null!=t&&n==e+t.nodeSize}if(s.parentOffset<s.parent.content.size||!s.parent.isTextblock)return!1;let o=t.resolve(vi(s,!0,!0));return!(!o.parent.isTextblock||o.pos>n||vi(o,!0,!1)<n)&&r.parent.content.cut(r.parentOffset).eq(o.parent.content)}(d,m.start,m.endA,v,g)&&t.someProp("handleKeyDown",(e=>e(t,Ee(8,"Backspace")))))return void(Ue&&Be&&t.domObserver.suppressSelectionUpdates());Be&&Ue&&m.endB==m.start&&(t.input.lastAndroidDelete=Date.now()),Ue&&!y&&v.start()!=g.start()&&0==g.parentOffset&&v.depth==g.depth&&c.sel&&c.sel.anchor==c.sel.head&&c.sel.head==m.endA&&(m.endB-=2,g=c.doc.resolveNoCache(m.endB-c.from),setTimeout((()=>{t.someProp("handleKeyDown",(function(e){return e(t,Ee(13,"Enter"))}))}),20));let k,x,M,O=m.start,S=m.endA;if(y)if(v.pos==g.pos)$e&&De<=11&&0==v.parentOffset&&(t.domObserver.suppressSelectionUpdates(),setTimeout((()=>Jn(t)),20)),k=t.state.tr.delete(O,S),x=d.resolve(m.start).marksAcross(d.resolve(m.endA));else if(m.endA==m.endB&&(M=function(t,e){let n,i,s,o=t.firstChild.marks,l=e.firstChild.marks,u=o,h=l;for(let t=0;t<l.length;t++)u=l[t].removeFromSet(u);for(let t=0;t<o.length;t++)h=o[t].removeFromSet(h);if(1==u.length&&0==h.length)i=u[0],n="add",s=t=>t.mark(i.addToSet(t.marks));else{if(0!=u.length||1!=h.length)return null;i=h[0],n="remove",s=t=>t.mark(i.removeFromSet(t.marks))}let f=[];for(let t=0;t<e.childCount;t++)f.push(s(e.child(t)));if(r.from(f).eq(t))return{mark:i,type:n}}(v.parent.content.cut(v.parentOffset,g.parentOffset),b.parent.content.cut(b.parentOffset,m.endA-b.start()))))k=t.state.tr,"add"==M.type?k.addMark(O,S,M.mark):k.removeMark(O,S,M.mark);else if(v.parent.child(v.index()).isText&&v.index()==g.index()-(g.textOffset?0:1)){let e=v.parent.textBetween(v.parentOffset,g.parentOffset);if(t.someProp("handleTextInput",(n=>n(t,O,S,e))))return;k=t.state.tr.insertText(e,O,S)}if(k||(k=t.state.tr.replace(O,S,c.doc.slice(m.start-c.from,m.endB-c.from))),c.sel){let e=wi(t,k.doc,c.sel);e&&!(Be&&Ue&&t.composing&&e.empty&&(m.start!=m.endB||t.input.lastAndroidDelete<Date.now()-100)&&(e.head==O||e.head==k.mapping.map(S)-1)||$e&&e.empty&&e.head==O)&&k.setSelection(e)}x&&k.ensureMarks(x),o&&k.setMeta("composition",o),t.dispatch(k.scrollIntoView())}(this,t,e,n,i))),this.domObserver.start(),function(t){for(let e in vr){let n=vr[e];t.dom.addEventListener(e,t.input.eventHandlers[e]=e=>{!Or(t,e)||Mr(t,e)||!t.editable&&e.type in gr||n(t,e)},br[e]?{passive:!0}:void 0)}Fe&&t.dom.addEventListener("input",(()=>null)),xr(t)}(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let t=this._props;this._props={};for(let e in t)this._props[e]=t[e];this._props.state=this.state}return this._props}update(t){t.handleDOMEvents!=this._props.handleDOMEvents&&xr(this);let e=this._props;this._props=t,t.plugins&&(t.plugins.forEach(Oi),this.directPlugins=t.plugins),this.updateStateInner(t.state,e)}setProps(t){let e={};for(let t in this._props)e[t]=this._props[t];e.state=this.state;for(let n in t)e[n]=t[n];this.update(e)}updateState(t){this.updateStateInner(t,this._props)}updateStateInner(t,e){var n;let r=this.state,i=!1,s=!1;t.storedMarks&&this.composing&&(Ir(this),s=!0),this.state=t;let o=r.plugins!=t.plugins||this._props.plugins!=e.plugins;if(o||this._props.plugins!=e.plugins||this._props.nodeViews!=e.nodeViews){let t=Mi(this);(function(t,e){let n=0,r=0;for(let r in t){if(t[r]!=e[r])return!0;n++}for(let t in e)r++;return n!=r})(t,this.nodeViews)&&(this.nodeViews=t,i=!0)}(o||e.handleDOMEvents!=this._props.handleDOMEvents)&&xr(this),this.editable=xi(this),ki(this);let l=oi(this),u=yi(this),h=r.plugins==t.plugins||r.doc.eq(t.doc)?t.scrollToSelection>r.scrollToSelection?"to selection":"preserve":"reset",f=i||!this.docView.matchesNode(t.doc,u,l);!f&&t.selection.eq(r.selection)||(s=!0);let a="preserve"==h&&s&&null==this.dom.style.overflowAnchor&&function(t){let e,n,r=t.dom.getBoundingClientRect(),i=Math.max(0,r.top);for(let s=(r.left+r.right)/2,o=i+1;o<Math.min(innerHeight,r.bottom);o+=5){let r=t.root.elementFromPoint(s,o);if(!r||r==t.dom||!t.dom.contains(r))continue;let l=r.getBoundingClientRect();if(l.top>=i-20){e=r,n=l.top;break}}return{refDOM:e,refTop:n,stack:Qe(t.dom)}}(this);if(s){this.domObserver.stop();let e=f&&($e||Be)&&!this.composing&&!r.selection.empty&&!t.selection.empty&&function(t,e){let n=Math.min(t.$anchor.sharedDepth(t.head),e.$anchor.sharedDepth(e.head));return t.$anchor.start(n)!=e.$anchor.start(n)}(r.selection,t.selection);if(f){let n=Be?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=function(t){let e=t.domSelectionRange();if(!e.focusNode)return null;let n=function(t,e){for(;;){if(3==t.nodeType&&e)return t;if(1==t.nodeType&&e>0){if("false"==t.contentEditable)return null;e=Me(t=t.childNodes[e-1])}else{if(!t.parentNode||Oe(t))return null;e=we(t),t=t.parentNode}}}(e.focusNode,e.focusOffset),r=function(t,e){for(;;){if(3==t.nodeType&&e<t.nodeValue.length)return t;if(1==t.nodeType&&e<t.childNodes.length){if("false"==t.contentEditable)return null;t=t.childNodes[e],e=0}else{if(!t.parentNode||Oe(t))return null;e=we(t)+1,t=t.parentNode}}}(e.focusNode,e.focusOffset);if(n&&r&&n!=r){let e=r.pmViewDesc;if(!e||!e.isText(r.nodeValue))return r;if(t.input.compositionNode==r){let t=n.pmViewDesc;if(t&&t.isText(n.nodeValue))return r}}return n||r}(this)),!i&&this.docView.update(t.doc,u,l,this)||(this.docView.updateOuterDeco(u),this.docView.destroy(),this.docView=yn(t.doc,u,l,this.dom,this)),n&&!this.trackWrites&&(e=!0)}e||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&function(t){let e=t.docView.domFromPos(t.state.selection.anchor,0),n=t.domSelectionRange();return ye(e.node,e.offset,n.anchorNode,n.anchorOffset)}(this))?Jn(this,e):(Un(this,t.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(r),(null===(n=this.dragging)||void 0===n?void 0:n.node)&&!r.doc.eq(t.doc)&&this.updateDraggedNode(this.dragging,r),"reset"==h?this.dom.scrollTop=0:"to selection"==h?this.scrollToSelection():a&&function({refDOM:t,refTop:e,stack:n}){let r=t?t.getBoundingClientRect().top:0;Ye(n,0==r?0:r-e)}(a)}scrollToSelection(){let t=this.domSelectionRange().focusNode;if(this.someProp("handleScrollToSelection",(t=>t(this))));else if(this.state.selection instanceof Zt){let e=this.docView.domAfterPos(this.state.selection.from);1==e.nodeType&&_e(this,e.getBoundingClientRect(),t)}else _e(this,this.coordsAtPos(this.state.selection.head,1),t)}destroyPluginViews(){let t;for(;t=this.pluginViews.pop();)t.destroy&&t.destroy()}updatePluginViews(t){if(t&&t.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(let e=0;e<this.pluginViews.length;e++){let n=this.pluginViews[e];n.update&&n.update(this,t)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let t=0;t<this.directPlugins.length;t++){let e=this.directPlugins[t];e.spec.view&&this.pluginViews.push(e.spec.view(this))}for(let t=0;t<this.state.plugins.length;t++){let e=this.state.plugins[t];e.spec.view&&this.pluginViews.push(e.spec.view(this))}}}updateDraggedNode(t,e){let n=t.node,r=-1;if(this.state.doc.nodeAt(n.from)==n.node)r=n.from;else{let t=n.from+(this.state.doc.content.size-e.doc.content.size);(t>0&&this.state.doc.nodeAt(t))==n.node&&(r=t)}this.dragging=new qr(t.slice,t.move,r<0?void 0:Zt.create(this.state.doc,r))}someProp(t,e){let n,r=this._props&&this._props[t];if(null!=r&&(n=e?e(r):r))return n;for(let r=0;r<this.directPlugins.length;r++){let i=this.directPlugins[r].props[t];if(null!=i&&(n=e?e(i):i))return n}let i=this.state.plugins;if(i)for(let r=0;r<i.length;r++){let s=i[r].props[t];if(null!=s&&(n=e?e(s):s))return n}}hasFocus(){if($e){let t=this.root.activeElement;if(t==this.dom)return!0;if(!t||!this.dom.contains(t))return!1;for(;t&&this.dom!=t&&this.dom.contains(t);){if("false"==t.contentEditable)return!1;t=t.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&function(t){if(t.setActive)return t.setActive();if(Xe)return t.focus(Xe);let e=Qe(t);t.focus(null==Xe?{get preventScroll(){return Xe={preventScroll:!0},!0}}:void 0),Xe||(Xe=!1,Ye(e,0))}(this.dom),Jn(this),this.domObserver.start()}get root(){let t=this._root;if(null==t)for(let t=this.dom.parentNode;t;t=t.parentNode)if(9==t.nodeType||11==t.nodeType&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t;return t||document}updateRoot(){this._root=null}posAtCoords(t){return nn(this,t)}coordsAtPos(t,e=1){return ln(this,t,e)}domAtPos(t,e=0){return this.docView.domFromPos(t,e)}nodeDOM(t){let e=this.docView.descAt(t);return e?e.nodeDOM:null}posAtDOM(t,e,n=-1){let r=this.docView.posFromDOM(t,e,n);if(null==r)throw new RangeError("DOM position not inside the editor");return r}endOfTextblock(t,e){return function(t,e,n){return cn==e&&dn==n?pn:(cn=e,dn=n,pn="up"==n||"down"==n?function(t,e,n){let r=e.selection,i="up"==n?r.$from:r.$to;return fn(t,e,(()=>{let{node:e}=t.docView.domFromPos(i.pos,"up"==n?-1:1);for(;;){let n=t.docView.nearestDesc(e,!0);if(!n)break;if(n.node.isBlock){e=n.contentDOM||n.dom;break}e=n.dom.parentNode}let r=ln(t,i.pos,1);for(let t=e.firstChild;t;t=t.nextSibling){let e;if(1==t.nodeType)e=t.getClientRects();else{if(3!=t.nodeType)continue;e=be(t,0,t.nodeValue.length).getClientRects()}for(let t=0;t<e.length;t++){let i=e[t];if(i.bottom>i.top+1&&("up"==n?r.top-i.top>2*(i.bottom-r.top):i.bottom-r.bottom>2*(r.bottom-i.top)))return!1}}return!0}))}(t,e,n):function(t,e,n){let{$head:r}=e.selection;if(!r.parent.isTextblock)return!1;let i=r.parentOffset,s=!i,o=i==r.parent.content.size,l=t.domSelection();return an.test(r.parent.textContent)&&l.modify?fn(t,e,(()=>{let{focusNode:e,focusOffset:i,anchorNode:s,anchorOffset:o}=t.domSelectionRange(),u=l.caretBidiLevel;l.modify("move",n,"character");let h=r.depth?t.docView.domAfterPos(r.before()):t.dom,{focusNode:f,focusOffset:a}=t.domSelectionRange(),c=f&&!h.contains(1==f.nodeType?f:f.parentNode)||e==f&&i==a;try{l.collapse(s,o),e&&(e!=s||i!=o)&&l.extend&&l.extend(e,i)}catch(t){}return null!=u&&(l.caretBidiLevel=u),c})):"left"==n||"backward"==n?s:o}(t,e,n))}(this,e||this.state,t)}pasteHTML(t,e){return Fr(this,"",t,!1,e||new ClipboardEvent("paste"))}pasteText(t,e){return Fr(this,t,null,!0,e||new ClipboardEvent("paste"))}destroy(){this.docView&&(function(t){t.domObserver.stop();for(let e in t.input.eventHandlers)t.dom.removeEventListener(e,t.input.eventHandlers[e]);clearTimeout(t.input.composingTimeout),clearTimeout(t.input.lastIOSEnterFallbackTimeout)}(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],oi(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,ge=null)}get isDestroyed(){return null==this.docView}dispatchEvent(t){return function(t,e){Mr(t,e)||!vr[e.type]||!t.editable&&e.type in gr||vr[e.type](t,e)}(this,t)}dispatch(t){let e=this._props.dispatchTransaction;e?e.call(this,t):this.updateState(this.state.apply(t))}domSelectionRange(){let t=this.domSelection();return Fe&&11===this.root.nodeType&&function(t){let e=t.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}(this.dom.ownerDocument)==this.dom&&function(t,e){if(e.getComposedRanges){let n=e.getComposedRanges(t.root)[0];if(n)return di(t,n)}let n;function r(t){t.preventDefault(),t.stopImmediatePropagation(),n=t.getTargetRanges()[0]}return t.dom.addEventListener("beforeinput",r,!0),document.execCommand("indent"),t.dom.removeEventListener("beforeinput",r,!0),n?di(t,n):null}(this,t)||t}domSelection(){return this.root.getSelection()}}function yi(t){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(t.editable),t.someProp("attributes",(n=>{if("function"==typeof n&&(n=n(t.state)),n)for(let t in n)"class"==t?e.class+=" "+n[t]:"style"==t?e.style=(e.style?e.style+";":"")+n[t]:e[t]||"contenteditable"==t||"nodeName"==t||(e[t]=String(n[t]))})),e.translate||(e.translate="no"),[Gr.node(0,t.state.doc.content.size,e)]}function ki(t){if(t.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),t.cursorWrapper={dom:e,deco:Gr.widget(t.state.selection.head,e,{raw:!0,marks:t.markCursor})}}else t.cursorWrapper=null}function xi(t){return!t.someProp("editable",(e=>!1===e(t.state)))}function Mi(t){let e=Object.create(null);function n(t){for(let n in t)Object.prototype.hasOwnProperty.call(e,n)||(e[n]=t[n])}return t.someProp("nodeViews",n),t.someProp("markViews",n),e}function Oi(t){if(t.spec.state||t.spec.filterTransaction||t.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}for(var Si={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},Ei={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},Ni="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),Ci="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),Ri=0;Ri<10;Ri++)Si[48+Ri]=Si[96+Ri]=String(Ri);for(Ri=1;Ri<=24;Ri++)Si[Ri+111]="F"+Ri;for(Ri=65;Ri<=90;Ri++)Si[Ri]=String.fromCharCode(Ri+32),Ei[Ri]=String.fromCharCode(Ri);for(var Ti in Si)Ei.hasOwnProperty(Ti)||(Ei[Ti]=Si[Ti]);const ji="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform);function Ai(t){let e,n,r,i,s=t.split(/-(?!$)/),o=s[s.length-1];"Space"==o&&(o=" ");for(let t=0;t<s.length-1;t++){let o=s[t];if(/^(cmd|meta|m)$/i.test(o))i=!0;else if(/^a(lt)?$/i.test(o))e=!0;else if(/^(c|ctrl|control)$/i.test(o))n=!0;else if(/^s(hift)?$/i.test(o))r=!0;else{if(!/^mod$/i.test(o))throw new Error("Unrecognized modifier name: "+o);ji?i=!0:n=!0}}return e&&(o="Alt-"+o),n&&(o="Ctrl-"+o),i&&(o="Meta-"+o),r&&(o="Shift-"+o),o}function $i(t,e,n=!0){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),n&&e.shiftKey&&(t="Shift-"+t),t}function Di(t){let e=function(t){let e=Object.create(null);for(let n in t)e[Ai(n)]=t[n];return e}(t);return function(t,n){let r,i=function(t){var e=!(Ni&&t.metaKey&&t.shiftKey&&!t.ctrlKey&&!t.altKey||Ci&&t.shiftKey&&t.key&&1==t.key.length||"Unidentified"==t.key)&&t.key||(t.shiftKey?Ei:Si)[t.keyCode]||t.key||"Unidentified";return"Esc"==e&&(e="Escape"),"Del"==e&&(e="Delete"),"Left"==e&&(e="ArrowLeft"),"Up"==e&&(e="ArrowUp"),"Right"==e&&(e="ArrowRight"),"Down"==e&&(e="ArrowDown"),e}(n),s=e[$i(i,n)];if(s&&s(t.state,t.dispatch,t))return!0;if(1==i.length&&" "!=i){if(n.shiftKey){let r=e[$i(i,n,!1)];if(r&&r(t.state,t.dispatch,t))return!0}if((n.shiftKey||n.altKey||n.metaKey||i.charCodeAt(0)>127)&&(r=Si[n.keyCode])&&r!=i){let i=e[$i(r,n)];if(i&&i(t.state,t.dispatch,t))return!0}}return!1}}const Pi=(t,e)=>!t.selection.empty&&(e&&e(t.tr.deleteSelection().scrollIntoView()),!0);function Ii(t,e){let{$cursor:n}=t.selection;return!n||(e?!e.endOfTextblock("backward",t):n.parentOffset>0)?null:n}const Bi=(t,e,n)=>{let r=Ii(t,n);if(!r)return!1;let i=qi(r);if(!i){let n=r.blockRange(),i=n&&Ot(n);return null!=i&&(e&&e(t.tr.lift(n,i).scrollIntoView()),!0)}let s=i.nodeBefore;if(!s.type.spec.isolating&&Xi(t,i,e))return!0;if(0==r.parent.content.size&&(Fi(s,"end")||Zt.isSelectable(s))){let n=At(t.doc,r.before(),r.after(),h.empty);if(n&&n.slice.size<n.to-n.from){if(e){let r=t.tr.step(n);r.setSelection(Fi(s,"end")?Gt.findFrom(r.doc.resolve(r.mapping.map(i.pos,-1)),-1):Zt.create(r.doc,i.pos-s.nodeSize)),e(r.scrollIntoView())}return!0}}return!(!s.isAtom||i.depth!=r.depth-1||(e&&e(t.tr.delete(i.pos-s.nodeSize,i.pos).scrollIntoView()),0))};function Ji(t,e,n){let r=e.nodeBefore,i=e.pos-1;for(;!r.isTextblock;i--){if(r.type.spec.isolating)return!1;let t=r.lastChild;if(!t)return!1;r=t}let s=e.nodeAfter,o=e.pos+1;for(;!s.isTextblock;o++){if(s.type.spec.isolating)return!1;let t=s.firstChild;if(!t)return!1;s=t}let l=At(t.doc,i,o,h.empty);if(!l||l.from!=i||l instanceof yt&&l.slice.size>=o-i)return!1;if(n){let e=t.tr.step(l);e.setSelection(Yt.create(e.doc,i)),n(e.scrollIntoView())}return!0}function Fi(t,e,n=!1){for(let r=t;r;r="start"==e?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(n&&1!=r.childCount)return!1}return!1}const Li=(t,e,n)=>{let{$head:r,empty:i}=t.selection,s=r;if(!i)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("backward",t):r.parentOffset>0)return!1;s=qi(r)}let o=s&&s.nodeBefore;return!(!o||!Zt.isSelectable(o)||(e&&e(t.tr.setSelection(Zt.create(t.doc,s.pos-o.nodeSize)).scrollIntoView()),0))};function qi(t){if(!t.parent.type.spec.isolating)for(let e=t.depth-1;e>=0;e--){if(t.index(e)>0)return t.doc.resolve(t.before(e+1));if(t.node(e).type.spec.isolating)break}return null}function Hi(t,e){let{$cursor:n}=t.selection;return!n||(e?!e.endOfTextblock("forward",t):n.parentOffset<n.parent.content.size)?null:n}const Ui=(t,e,n)=>{let r=Hi(t,n);if(!r)return!1;let i=Ki(r);if(!i)return!1;let s=i.nodeAfter;if(Xi(t,i,e))return!0;if(0==r.parent.content.size&&(Fi(s,"start")||Zt.isSelectable(s))){let n=At(t.doc,r.before(),r.after(),h.empty);if(n&&n.slice.size<n.to-n.from){if(e){let r=t.tr.step(n);r.setSelection(Fi(s,"start")?Gt.findFrom(r.doc.resolve(r.mapping.map(i.pos)),1):Zt.create(r.doc,r.mapping.map(i.pos))),e(r.scrollIntoView())}return!0}}return!(!s.isAtom||i.depth!=r.depth-1||(e&&e(t.tr.delete(i.pos,i.pos+s.nodeSize).scrollIntoView()),0))},Wi=(t,e,n)=>{let{$head:r,empty:i}=t.selection,s=r;if(!i)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("forward",t):r.parentOffset<r.parent.content.size)return!1;s=Ki(r)}let o=s&&s.nodeAfter;return!(!o||!Zt.isSelectable(o)||(e&&e(t.tr.setSelection(Zt.create(t.doc,s.pos)).scrollIntoView()),0))};function Ki(t){if(!t.parent.type.spec.isolating)for(let e=t.depth-1;e>=0;e--){let n=t.node(e);if(t.index(e)+1<n.childCount)return t.doc.resolve(t.after(e+1));if(n.type.spec.isolating)break}return null}const zi=(t,e)=>{let{$head:n,$anchor:r}=t.selection;return!(!n.parent.type.spec.code||!n.sameParent(r)||(e&&e(t.tr.insertText("\n").scrollIntoView()),0))};function Gi(t){for(let e=0;e<t.edgeCount;e++){let{type:n}=t.edge(e);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}const Vi=(t,e)=>{let{$head:n,$anchor:r}=t.selection;if(!n.parent.type.spec.code||!n.sameParent(r))return!1;let i=n.node(-1),s=n.indexAfter(-1),o=Gi(i.contentMatchAt(s));if(!o||!i.canReplaceWith(s,s,o))return!1;if(e){let r=n.after(),i=t.tr.replaceWith(r,r,o.createAndFill());i.setSelection(Gt.near(i.doc.resolve(r),1)),e(i.scrollIntoView())}return!0},_i=(t,e)=>{let n=t.selection,{$from:r,$to:i}=n;if(n instanceof ee||r.parent.inlineContent||i.parent.inlineContent)return!1;let s=Gi(i.parent.contentMatchAt(i.indexAfter()));if(!s||!s.isTextblock)return!1;if(e){let n=(!r.parentOffset&&i.index()<i.parent.childCount?r:i).pos,o=t.tr.insert(n,s.createAndFill());o.setSelection(Yt.create(o.doc,n+1)),e(o.scrollIntoView())}return!0},Qi=(t,e)=>{let{$cursor:n}=t.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let r=n.before();if(Nt(t.doc,r))return e&&e(t.tr.split(r).scrollIntoView()),!0}let r=n.blockRange(),i=r&&Ot(r);return null!=i&&(e&&e(t.tr.lift(r,i).scrollIntoView()),!0)};var Yi;function Xi(t,e,n){let i,s,o=e.nodeBefore,l=e.nodeAfter;if(o.type.spec.isolating||l.type.spec.isolating)return!1;if(function(t,e,n){let r=e.nodeBefore,i=e.nodeAfter,s=e.index();return!(!(r&&i&&r.type.compatibleContent(i.type))||(!r.content.size&&e.parent.canReplace(s-1,s)?(n&&n(t.tr.delete(e.pos-r.nodeSize,e.pos).scrollIntoView()),0):!e.parent.canReplace(s,s+1)||!i.isTextblock&&!Ct(t.doc,e.pos)||(n&&n(t.tr.clearIncompatible(e.pos,r.type,r.contentMatchAt(r.childCount)).join(e.pos).scrollIntoView()),0)))}(t,e,n))return!0;let u=e.parent.canReplace(e.index(),e.index()+1);if(u&&(i=(s=o.contentMatchAt(o.childCount)).findWrapping(l.type))&&s.matchType(i[0]||l.type).validEnd){if(n){let s=e.pos+l.nodeSize,u=r.empty;for(let t=i.length-1;t>=0;t--)u=r.from(i[t].create(null,u));u=r.from(o.copy(u));let f=t.tr.step(new kt(e.pos-1,s,e.pos,s,new h(u,1,0),i.length,!0)),a=s+2*i.length;Ct(f.doc,a)&&f.join(a),n(f.scrollIntoView())}return!0}let f=Gt.findFrom(e,1),a=f&&f.$from.blockRange(f.$to),c=a&&Ot(a);if(null!=c&&c>=e.depth)return n&&n(t.tr.lift(a,c).scrollIntoView()),!0;if(u&&Fi(l,"start",!0)&&Fi(o,"end")){let i=o,s=[];for(;s.push(i),!i.isTextblock;)i=i.lastChild;let u=l,f=1;for(;!u.isTextblock;u=u.firstChild)f++;if(i.canReplace(i.childCount,i.childCount,u.content)){if(n){let i=r.empty;for(let t=s.length-1;t>=0;t--)i=r.from(s[t].copy(i));n(t.tr.step(new kt(e.pos-s.length,e.pos+l.nodeSize,e.pos+f,e.pos+l.nodeSize-f,new h(i,s.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function Zi(t){return function(e,n){let r=e.selection,i=t<0?r.$from:r.$to,s=i.depth;for(;i.node(s).isInline;){if(!s)return!1;s--}return!!i.node(s).isTextblock&&(n&&n(e.tr.setSelection(Yt.create(e.doc,t<0?i.start(s):i.end(s)))),!0)}}const ts=Zi(-1),es=Zi(1);function ns(t,e=null){return function(n,r){let i=!1;for(let r=0;r<n.selection.ranges.length&&!i;r++){let{$from:{pos:s},$to:{pos:o}}=n.selection.ranges[r];n.doc.nodesBetween(s,o,((r,s)=>{if(i)return!1;if(r.isTextblock&&!r.hasMarkup(t,e))if(r.type==t)i=!0;else{let e=n.doc.resolve(s),r=e.index();i=e.parent.canReplaceWith(r,r+1,t)}}))}if(!i)return!1;if(r){let i=n.tr;for(let r=0;r<n.selection.ranges.length;r++){let{$from:{pos:s},$to:{pos:o}}=n.selection.ranges[r];i.setBlockType(s,o,t,e)}r(i.scrollIntoView())}return!0}}function rs(...t){return function(e,n,r){for(let i=0;i<t.length;i++)if(t[i](e,n,r))return!0;return!1}}let is=rs(Pi,Bi,Li),ss=rs(Pi,Ui,Wi);const ls={Enter:rs(zi,_i,Qi,((t,e)=>{let{$from:n,$to:r}=t.selection;if(t.selection instanceof Zt&&t.selection.node.isBlock)return!(!n.parentOffset||!Nt(t.doc,n.pos)||(e&&e(t.tr.split(n.pos).scrollIntoView()),0));if(!n.parent.isBlock)return!1;if(e){let i=r.parentOffset==r.parent.content.size,s=t.tr;(t.selection instanceof Yt||t.selection instanceof ee)&&s.deleteSelection();let o=0==n.depth?null:Gi(n.node(-1).contentMatchAt(n.indexAfter(-1))),l=Yi?[Yi]:i&&o?[{type:o}]:void 0,u=Nt(s.doc,s.mapping.map(n.pos),1,l);if(l||u||!Nt(s.doc,s.mapping.map(n.pos),1,o?[{type:o}]:void 0)||(o&&(l=[{type:o}]),u=!0),u&&(s.split(s.mapping.map(n.pos),1,l),!i&&!n.parentOffset&&n.parent.type!=o)){let t=s.mapping.map(n.before()),e=s.doc.resolve(t);o&&n.node(-1).canReplaceWith(e.index(),e.index()+1,o)&&s.setNodeMarkup(s.mapping.map(n.before()),o)}e(s.scrollIntoView())}return!0})),"Mod-Enter":Vi,Backspace:is,"Mod-Backspace":is,"Shift-Backspace":is,Delete:ss,"Mod-Delete":ss,"Mod-a":(t,e)=>(e&&e(t.tr.setSelection(new ee(t.doc))),!0)},us={"Ctrl-h":ls.Backspace,"Alt-Backspace":ls["Mod-Backspace"],"Ctrl-d":ls.Delete,"Ctrl-Alt-Backspace":ls["Mod-Delete"],"Alt-Delete":ls["Mod-Delete"],"Alt-d":ls["Mod-Delete"],"Ctrl-a":ts,"Ctrl-e":es};for(let t in ls)us[t]=ls[t];function hs(t){const{state:e,transaction:n}=t;let{selection:r}=n,{doc:i}=n,{storedMarks:s}=n;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return s},get selection(){return r},get doc(){return i},get tr(){return r=n.selection,i=n.doc,s=n.storedMarks,n}}}"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform();class fs{constructor(t){this.editor=t.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=t.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:t,editor:e,state:n}=this,{view:r}=e,{tr:i}=n,s=this.buildProps(i);return Object.fromEntries(Object.entries(t).map((([t,e])=>[t,(...t)=>{const n=e(...t)(s);return i.getMeta("preventDispatch")||this.hasCustomState||r.dispatch(i),n}])))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(t,e=!0){const{rawCommands:n,editor:r,state:i}=this,{view:s}=r,o=[],l=!!t,u=t||i.tr,h={...Object.fromEntries(Object.entries(n).map((([t,n])=>[t,(...t)=>{const r=this.buildProps(u,e),i=n(...t)(r);return o.push(i),h}]))),run:()=>(l||!e||u.getMeta("preventDispatch")||this.hasCustomState||s.dispatch(u),o.every((t=>!0===t)))};return h}createCan(t){const{rawCommands:e,state:n}=this,r=!1,i=t||n.tr,s=this.buildProps(i,r);return{...Object.fromEntries(Object.entries(e).map((([t,e])=>[t,(...t)=>e(...t)({...s,dispatch:void 0})]))),chain:()=>this.createChain(i,r)}}buildProps(t,e=!0){const{rawCommands:n,editor:r,state:i}=this,{view:s}=r,o={tr:t,editor:r,view:s,state:hs({state:i,transaction:t}),dispatch:e?()=>{}:void 0,chain:()=>this.createChain(t,e),can:()=>this.createCan(t),get commands(){return Object.fromEntries(Object.entries(n).map((([t,e])=>[t,(...t)=>e(...t)(o)])))}};return o}}class as{constructor(){this.callbacks={}}on(t,e){return this.callbacks[t]||(this.callbacks[t]=[]),this.callbacks[t].push(e),this}emit(t,...e){const n=this.callbacks[t];return n&&n.forEach((t=>t.apply(this,e))),this}off(t,e){const n=this.callbacks[t];return n&&(e?this.callbacks[t]=n.filter((t=>t!==e)):delete this.callbacks[t]),this}removeAllListeners(){this.callbacks={}}}function cs(t,e,n){return void 0===t.config[e]&&t.parent?cs(t.parent,e,n):"function"==typeof t.config[e]?t.config[e].bind({...n,parent:t.parent?cs(t.parent,e,n):null}):t.config[e]}function ds(t){return{baseExtensions:t.filter((t=>"extension"===t.type)),nodeExtensions:t.filter((t=>"node"===t.type)),markExtensions:t.filter((t=>"mark"===t.type))}}function ps(t){const e=[],{nodeExtensions:n,markExtensions:r}=ds(t),i=[...n,...r],s={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return t.forEach((t=>{const n=cs(t,"addGlobalAttributes",{name:t.name,options:t.options,storage:t.storage});n&&n().forEach((t=>{t.types.forEach((n=>{Object.entries(t.attributes).forEach((([t,r])=>{e.push({type:n,name:t,attribute:{...s,...r}})}))}))}))})),i.forEach((t=>{const n=cs(t,"addAttributes",{name:t.name,options:t.options,storage:t.storage});if(!n)return;const r=n();Object.entries(r).forEach((([n,r])=>{const i={...s,...r};"function"==typeof(null==i?void 0:i.default)&&(i.default=i.default()),(null==i?void 0:i.isRequired)&&void 0===(null==i?void 0:i.default)&&delete i.default,e.push({type:t.name,name:n,attribute:i})}))})),e}function ms(t,e){if("string"==typeof t){if(!e.nodes[t])throw Error(`There is no node type named '${t}'. Maybe you forgot to add the extension?`);return e.nodes[t]}return t}function ws(...t){return t.filter((t=>!!t)).reduce(((t,e)=>{const n={...t};return Object.entries(e).forEach((([t,e])=>{if(n[t])if("class"===t){const r=e?e.split(" "):[],i=n[t]?n[t].split(" "):[],s=r.filter((t=>!i.includes(t)));n[t]=[...i,...s].join(" ")}else n[t]="style"===t?[n[t],e].join("; "):e;else n[t]=e})),n}),{})}function vs(t,e){return e.filter((t=>t.attribute.rendered)).map((e=>e.attribute.renderHTML?e.attribute.renderHTML(t.attrs)||{}:{[e.name]:t.attrs[e.name]})).reduce(((t,e)=>ws(t,e)),{})}function gs(t){return"function"==typeof t}function bs(t,e,...n){return gs(t)?e?t.bind(e)(...n):t(...n):t}function ys(t,e){return t.style?t:{...t,getAttrs:n=>{const r=t.getAttrs?t.getAttrs(n):t.attrs;if(!1===r)return!1;const i=e.reduce(((t,e)=>{const r=e.attribute.parseHTML?e.attribute.parseHTML(n):function(t){return"string"!=typeof t?t:t.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(t):"true"===t||"false"!==t&&t}(n.getAttribute(e.name));return null==r?t:{...t,[e.name]:r}}),{});return{...r,...i}}}}function ks(t){return Object.fromEntries(Object.entries(t).filter((([t,e])=>("attrs"!==t||!function(t={}){return 0===Object.keys(t).length&&t.constructor===Object}(e))&&null!=e)))}function xs(t,e){return e.nodes[t]||e.marks[t]||null}function Ms(t,e){return Array.isArray(e)?e.some((e=>("string"==typeof e?e:e.name)===t.name)):e}const Os=(t,e=500)=>{let n="";const r=t.parentOffset;return t.parent.nodesBetween(Math.max(0,r-e),r,((t,e,i,s)=>{var o,l;const u=(null===(l=(o=t.type.spec).toText)||void 0===l?void 0:l.call(o,{node:t,pos:e,parent:i,index:s}))||t.textContent||"%leaf%";n+=u.slice(0,Math.max(0,r-e))})),n};function Ss(t){return"[object RegExp]"===Object.prototype.toString.call(t)}class Es{constructor(t){this.find=t.find,this.handler=t.handler}}const Ns=(t,e)=>{if(Ss(e))return e.exec(t);const n=e(t);if(!n)return null;const r=[n.text];return r.index=n.index,r.input=t,r.data=n.data,n.replaceWith&&(n.text.includes(n.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),r.push(n.replaceWith)),r};function Cs(t){var e;const{editor:n,from:r,to:i,text:s,rules:o,plugin:l}=t,{view:u}=n;if(u.composing)return!1;const h=u.state.doc.resolve(r);if(h.parent.type.spec.code||(null===(e=h.nodeBefore||h.nodeAfter)||void 0===e?void 0:e.marks.find((t=>t.type.spec.code))))return!1;let f=!1;const a=Os(h)+s;return o.forEach((t=>{if(f)return;const e=Ns(a,t.find);if(!e)return;const o=u.state.tr,h=hs({state:u.state,transaction:o}),c={from:r-(e[0].length-s.length),to:i},{commands:d,chain:p,can:m}=new fs({editor:n,state:h});null!==t.handler({state:h,range:c,match:e,commands:d,chain:p,can:m})&&o.steps.length&&(o.setMeta(l,{transform:o,from:r,to:i,text:s}),u.dispatch(o),f=!0)})),f}function Rs(t){const{editor:e,rules:n}=t,r=new ce({state:{init:()=>null,apply(t,i){const s=t.getMeta(r);if(s)return s;const o=t.getMeta("applyInputRules");return!!o&&setTimeout((()=>{const{from:t,text:i}=o;Cs({editor:e,from:t,to:t+i.length,text:i,rules:n,plugin:r})})),t.selectionSet||t.docChanged?null:i}},props:{handleTextInput:(t,i,s,o)=>Cs({editor:e,from:i,to:s,text:o,rules:n,plugin:r}),handleDOMEvents:{compositionend:t=>(setTimeout((()=>{const{$cursor:i}=t.state.selection;i&&Cs({editor:e,from:i.pos,to:i.pos,text:"",rules:n,plugin:r})})),!1)},handleKeyDown(t,i){if("Enter"!==i.key)return!1;const{$cursor:s}=t.state.selection;return!!s&&Cs({editor:e,from:s.pos,to:s.pos,text:"\n",rules:n,plugin:r})}},isInputRules:!0});return r}class Ts{constructor(t){this.find=t.find,this.handler=t.handler}}function js(t){const{editor:e,rules:n}=t;let r=null,i=!1,s=!1,o="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null,l="undefined"!=typeof DragEvent?new DragEvent("drop"):null;const u=({state:t,from:n,to:r,rule:i,pasteEvt:s})=>{const u=t.tr,h=hs({state:t,transaction:u}),f=function(t){const{editor:e,state:n,from:r,to:i,rule:s,pasteEvent:o,dropEvent:l}=t,{commands:u,chain:h,can:f}=new fs({editor:e,state:n}),a=[];return n.doc.nodesBetween(r,i,((t,e)=>{if(!t.isTextblock||t.type.spec.code)return;const c=Math.max(r,e),d=Math.min(i,e+t.content.size);((t,e,n)=>{if(Ss(e))return[...t.matchAll(e)];const r=e(t,n);return r?r.map((e=>{const n=[e.text];return n.index=e.index,n.input=t,n.data=e.data,e.replaceWith&&(e.text.includes(e.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),n.push(e.replaceWith)),n})):[]})(t.textBetween(c-e,d-e,void 0,"￼"),s.find,o).forEach((t=>{if(void 0===t.index)return;const e=c+t.index+1,r=e+t[0].length,i={from:n.tr.mapping.map(e),to:n.tr.mapping.map(r)},d=s.handler({state:n,range:i,match:t,commands:u,chain:h,can:f,pasteEvent:o,dropEvent:l});a.push(d)}))})),a.every((t=>null!==t))}({editor:e,state:h,from:Math.max(n-1,0),to:r.b-1,rule:i,pasteEvent:s,dropEvent:l});if(f&&u.steps.length)return l="undefined"!=typeof DragEvent?new DragEvent("drop"):null,o="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null,u};return n.map((t=>new ce({view(t){const e=e=>{var n;r=(null===(n=t.dom.parentElement)||void 0===n?void 0:n.contains(e.target))?t.dom.parentElement:null};return window.addEventListener("dragstart",e),{destroy(){window.removeEventListener("dragstart",e)}}},props:{handleDOMEvents:{drop:(t,e)=>(s=r===t.dom.parentElement,l=e,!1),paste:(t,e)=>{var n;const r=null===(n=e.clipboardData)||void 0===n?void 0:n.getData("text/html");return o=e,i=!!(null==r?void 0:r.includes("data-pm-slice")),!1}}},appendTransaction:(e,n,r)=>{const l=e[0],h="paste"===l.getMeta("uiEvent")&&!i,f="drop"===l.getMeta("uiEvent")&&!s,a=l.getMeta("applyPasteRules"),c=!!a;if(!h&&!f&&!c)return;if(c){const{from:e,text:n}=a,i=e+n.length,s=(t=>{var e;const n=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return null===(e=n.clipboardData)||void 0===e||e.setData("text/html",t),n})(n);return u({rule:t,state:r,from:e,to:{b:i},pasteEvt:s})}const d=n.doc.content.findDiffStart(r.doc.content),p=n.doc.content.findDiffEnd(r.doc.content);return"number"==typeof d&&p&&d!==p.b?u({rule:t,state:r,from:d,to:p,pasteEvt:o}):void 0}})))}class As{constructor(t,e){this.splittableMarks=[],this.editor=e,this.extensions=As.resolve(t),this.schema=function(t,e){var n;const r=ps(t),{nodeExtensions:i,markExtensions:s}=ds(t),o=null===(n=i.find((t=>cs(t,"topNode"))))||void 0===n?void 0:n.name,l=Object.fromEntries(i.map((n=>{const i=r.filter((t=>t.type===n.name)),s={name:n.name,options:n.options,storage:n.storage,editor:e},o=ks({...t.reduce(((t,e)=>{const r=cs(e,"extendNodeSchema",s);return{...t,...r?r(n):{}}}),{}),content:bs(cs(n,"content",s)),marks:bs(cs(n,"marks",s)),group:bs(cs(n,"group",s)),inline:bs(cs(n,"inline",s)),atom:bs(cs(n,"atom",s)),selectable:bs(cs(n,"selectable",s)),draggable:bs(cs(n,"draggable",s)),code:bs(cs(n,"code",s)),defining:bs(cs(n,"defining",s)),isolating:bs(cs(n,"isolating",s)),attrs:Object.fromEntries(i.map((t=>{var e;return[t.name,{default:null===(e=null==t?void 0:t.attribute)||void 0===e?void 0:e.default}]})))}),l=bs(cs(n,"parseHTML",s));l&&(o.parseDOM=l.map((t=>ys(t,i))));const u=cs(n,"renderHTML",s);u&&(o.toDOM=t=>u({node:t,HTMLAttributes:vs(t,i)}));const h=cs(n,"renderText",s);return h&&(o.toText=h),[n.name,o]}))),u=Object.fromEntries(s.map((n=>{const i=r.filter((t=>t.type===n.name)),s={name:n.name,options:n.options,storage:n.storage,editor:e},o=ks({...t.reduce(((t,e)=>{const r=cs(e,"extendMarkSchema",s);return{...t,...r?r(n):{}}}),{}),inclusive:bs(cs(n,"inclusive",s)),excludes:bs(cs(n,"excludes",s)),group:bs(cs(n,"group",s)),spanning:bs(cs(n,"spanning",s)),code:bs(cs(n,"code",s)),attrs:Object.fromEntries(i.map((t=>{var e;return[t.name,{default:null===(e=null==t?void 0:t.attribute)||void 0===e?void 0:e.default}]})))}),l=bs(cs(n,"parseHTML",s));l&&(o.parseDOM=l.map((t=>ys(t,i))));const u=cs(n,"renderHTML",s);return u&&(o.toDOM=t=>u({mark:t,HTMLAttributes:vs(t,i)})),[n.name,o]})));return new K({topNode:o,nodes:l,marks:u})}(this.extensions,e),this.extensions.forEach((t=>{var e;this.editor.extensionStorage[t.name]=t.storage;const n={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:xs(t.name,this.schema)};"mark"===t.type&&(null===(e=bs(cs(t,"keepOnSplit",n)))||void 0===e||e)&&this.splittableMarks.push(t.name);const r=cs(t,"onBeforeCreate",n);r&&this.editor.on("beforeCreate",r);const i=cs(t,"onCreate",n);i&&this.editor.on("create",i);const s=cs(t,"onUpdate",n);s&&this.editor.on("update",s);const o=cs(t,"onSelectionUpdate",n);o&&this.editor.on("selectionUpdate",o);const l=cs(t,"onTransaction",n);l&&this.editor.on("transaction",l);const u=cs(t,"onFocus",n);u&&this.editor.on("focus",u);const h=cs(t,"onBlur",n);h&&this.editor.on("blur",h);const f=cs(t,"onDestroy",n);f&&this.editor.on("destroy",f)}))}static resolve(t){const e=As.sort(As.flatten(t)),n=function(t){const e=t.filter(((e,n)=>t.indexOf(e)!==n));return[...new Set(e)]}(e.map((t=>t.name)));return n.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${n.map((t=>`'${t}'`)).join(", ")}]. This can lead to issues.`),e}static flatten(t){return t.map((t=>{const e=cs(t,"addExtensions",{name:t.name,options:t.options,storage:t.storage});return e?[t,...this.flatten(e())]:t})).flat(10)}static sort(t){return t.sort(((t,e)=>{const n=cs(t,"priority")||100,r=cs(e,"priority")||100;return n>r?-1:n<r?1:0}))}get commands(){return this.extensions.reduce(((t,e)=>{const n=cs(e,"addCommands",{name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:xs(e.name,this.schema)});return n?{...t,...n()}:t}),{})}get plugins(){const{editor:t}=this,e=As.sort([...this.extensions].reverse()),n=[],r=[],i=e.map((e=>{const i={name:e.name,options:e.options,storage:e.storage,editor:t,type:xs(e.name,this.schema)},s=[],o=cs(e,"addKeyboardShortcuts",i);let l={};if("mark"===e.type&&e.config.exitable&&(l.ArrowRight=()=>Po.handleExit({editor:t,mark:e})),o){const e=Object.fromEntries(Object.entries(o()).map((([e,n])=>[e,()=>n({editor:t})])));l={...l,...e}}const u=new ce({props:{handleKeyDown:Di(l)}});s.push(u);const h=cs(e,"addInputRules",i);Ms(e,t.options.enableInputRules)&&h&&n.push(...h());const f=cs(e,"addPasteRules",i);Ms(e,t.options.enablePasteRules)&&f&&r.push(...f());const a=cs(e,"addProseMirrorPlugins",i);if(a){const t=a();s.push(...t)}return s})).flat();return[Rs({editor:t,rules:n}),...js({editor:t,rules:r}),...i]}get attributes(){return ps(this.extensions)}get nodeViews(){const{editor:t}=this,{nodeExtensions:e}=ds(this.extensions);return Object.fromEntries(e.filter((t=>!!cs(t,"addNodeView"))).map((e=>{const n=this.attributes.filter((t=>t.type===e.name)),r={name:e.name,options:e.options,storage:e.storage,editor:t,type:ms(e.name,this.schema)},i=cs(e,"addNodeView",r);return i?[e.name,(r,s,o,l)=>{const u=vs(r,n);return i()({editor:t,node:r,getPos:o,decorations:l,HTMLAttributes:u,extension:e})}]:[]})))}}function $s(t){return"Object"===function(t){return Object.prototype.toString.call(t).slice(8,-1)}(t)&&t.constructor===Object&&Object.getPrototypeOf(t)===Object.prototype}function Ds(t,e){const n={...t};return $s(t)&&$s(e)&&Object.keys(e).forEach((r=>{$s(e[r])?r in t?n[r]=Ds(t[r],e[r]):Object.assign(n,{[r]:e[r]}):Object.assign(n,{[r]:e[r]})})),n}class Ps{constructor(t={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=bs(cs(this,"addOptions",{name:this.name}))),this.storage=bs(cs(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new Ps(t)}configure(t={}){const e=this.extend();return e.options=Ds(this.options,t),e.storage=bs(cs(e,"addStorage",{name:e.name,options:e.options})),e}extend(t={}){const e=new Ps({...this.config,...t});return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${e.name}".`),e.options=bs(cs(e,"addOptions",{name:e.name})),e.storage=bs(cs(e,"addStorage",{name:e.name,options:e.options})),e}}function Is(t,e,n){const{from:r,to:i}=e,{blockSeparator:s="\n\n",textSerializers:o={}}=n||{};let l="",u=!0;return t.nodesBetween(r,i,((t,n,h,f)=>{var a;const c=null==o?void 0:o[t.type.name];if(c)return t.isBlock&&!u&&(l+=s,u=!0),h&&(l+=c({node:t,pos:n,parent:h,index:f,range:e})),!1;t.isText?(l+=null===(a=null==t?void 0:t.text)||void 0===a?void 0:a.slice(Math.max(r,n)-n,i-n),u=!1):t.isBlock&&!u&&(l+=s,u=!0)})),l}function Bs(t){return Object.fromEntries(Object.entries(t.nodes).filter((([,t])=>t.spec.toText)).map((([t,e])=>[t,e.spec.toText])))}const Js=Ps.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new ce({key:new me("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:t}=this,{state:e,schema:n}=t,{doc:r,selection:i}=e,{ranges:s}=i,o=Math.min(...s.map((t=>t.$from.pos))),l=Math.max(...s.map((t=>t.$to.pos))),u=Bs(n);return Is(r,{from:o,to:l},{...void 0!==this.options.blockSeparator?{blockSeparator:this.options.blockSeparator}:{},textSerializers:u})}}})]}});function Fs(t,e,n={strict:!0}){const r=Object.keys(e);return!r.length||r.every((r=>n.strict?e[r]===t[r]:Ss(e[r])?e[r].test(t[r]):e[r]===t[r]))}function Ls(t,e,n={}){return t.find((t=>t.type===e&&Fs(t.attrs,n)))}function qs(t,e,n={}){return!!Ls(t,e,n)}function Hs(t,e,n={}){if(!t||!e)return;let r=t.parent.childAfter(t.parentOffset);if(t.parentOffset===r.offset&&0!==r.offset&&(r=t.parent.childBefore(t.parentOffset)),!r.node)return;const i=Ls([...r.node.marks],e,n);if(!i)return;let s=r.index,o=t.start()+r.offset,l=s+1,u=o+r.node.nodeSize;for(Ls([...r.node.marks],e,n);s>0&&i.isInSet(t.parent.child(s-1).marks);)s-=1,o-=t.parent.child(s).nodeSize;for(;l<t.parent.childCount&&qs([...t.parent.child(l).marks],e,n);)u+=t.parent.child(l).nodeSize,l+=1;return{from:o,to:u}}function Us(t,e){if("string"==typeof t){if(!e.marks[t])throw Error(`There is no mark type named '${t}'. Maybe you forgot to add the extension?`);return e.marks[t]}return t}function Ws(t){return t instanceof Yt}function Ks(t=0,e=0,n=0){return Math.min(Math.max(t,e),n)}function zs(t,e=null){if(!e)return null;const n=Gt.atStart(t),r=Gt.atEnd(t);if("start"===e||!0===e)return n;if("end"===e)return r;const i=n.from,s=r.to;return"all"===e?Yt.create(t,Ks(0,i,s),Ks(t.content.size,i,s)):Yt.create(t,Ks(e,i,s),Ks(e,i,s))}function Gs(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}const Vs=t=>{const e=t.childNodes;for(let n=e.length-1;n>=0;n-=1){const r=e[n];3===r.nodeType&&r.nodeValue&&/^(\n\s\s|\n)$/.test(r.nodeValue)?t.removeChild(r):1===r.nodeType&&Vs(r)}return t};function _s(t){const e=`<body>${t}</body>`,n=(new window.DOMParser).parseFromString(e,"text/html").body;return Vs(n)}function Qs(t,e,n){if(n={slice:!0,parseOptions:{},...n},"object"==typeof t&&null!==t)try{return Array.isArray(t)&&t.length>0?r.fromArray(t.map((t=>e.nodeFromJSON(t)))):e.nodeFromJSON(t)}catch(r){return console.warn("[tiptap warn]: Invalid content.","Passed value:",t,"Error:",r),Qs("",e,n)}if("string"==typeof t){const r=G.fromSchema(e);return n.slice?r.parseSlice(_s(t),n.parseOptions).content:r.parse(_s(t),n.parseOptions)}return Qs("",e,n)}function Ys(){return"undefined"!=typeof navigator&&/Mac/.test(navigator.platform)}function Xs(t,e,n={}){const{from:r,to:i,empty:s}=t.selection,o=e?ms(e,t.schema):null,l=[];t.doc.nodesBetween(r,i,((t,e)=>{if(t.isText)return;const n=Math.max(r,e),s=Math.min(i,e+t.nodeSize);l.push({node:t,from:n,to:s})}));const u=i-r,h=l.filter((t=>!o||o.name===t.node.type.name)).filter((t=>Fs(t.node.attrs,n,{strict:!1})));return s?!!h.length:h.reduce(((t,e)=>t+e.to-e.from),0)>=u}function Zs(t,e){return e.nodes[t]?"node":e.marks[t]?"mark":null}function to(t,e){const n="string"==typeof e?[e]:e;return Object.keys(t).reduce(((e,r)=>(n.includes(r)||(e[r]=t[r]),e)),{})}function eo(t,e,n={}){return Qs(t,e,{slice:!1,parseOptions:n})}function no(t,e){const n=Us(e,t.schema),{from:r,to:i,empty:s}=t.selection,o=[];s?(t.storedMarks&&o.push(...t.storedMarks),o.push(...t.selection.$head.marks())):t.doc.nodesBetween(r,i,(t=>{o.push(...t.marks)}));const l=o.find((t=>t.type.name===n.name));return l?{...l.attrs}:{}}function ro(t,e){const n=new Kt(t);return e.forEach((t=>{t.steps.forEach((t=>{n.step(t)}))})),n}function io(t,e){const n=[];return t.descendants(((t,r)=>{e(t)&&n.push({node:t,pos:r})})),n}function so(t,e,n){const r=[];return t.nodesBetween(e.from,e.to,((t,e)=>{n(t)&&r.push({node:t,pos:e})})),r}function oo(t,e){for(let n=t.depth;n>0;n-=1){const r=t.node(n);if(e(r))return{pos:n>0?t.before(n):0,start:t.start(n),depth:n,node:r}}}function lo(t){return e=>oo(e.$from,t)}function uo(t,e){const n=rt.fromSchema(e).serializeFragment(t),r=document.implementation.createHTMLDocument().createElement("div");return r.appendChild(n),r.innerHTML}function ho(t,e){const n=Zs("string"==typeof e?e:e.name,t.schema);return"node"===n?function(t,e){const n=ms(e,t.schema),{from:r,to:i}=t.selection,s=[];t.doc.nodesBetween(r,i,(t=>{s.push(t)}));const o=s.reverse().find((t=>t.type.name===n.name));return o?{...o.attrs}:{}}(t,e):"mark"===n?no(t,e):{}}function fo(t){const{mapping:e,steps:n}=t,r=[];return e.maps.forEach(((t,i)=>{const s=[];if(t.ranges.length)t.forEach(((t,e)=>{s.push({from:t,to:e})}));else{const{from:t,to:e}=n[i];if(void 0===t||void 0===e)return;s.push({from:t,to:e})}s.forEach((({from:t,to:n})=>{const s=e.slice(i).map(t,-1),o=e.slice(i).map(n),l=e.invert().map(s,-1),u=e.invert().map(o);r.push({oldRange:{from:l,to:u},newRange:{from:s,to:o}})}))})),function(t){const e=function(t,e=JSON.stringify){const n={};return t.filter((t=>{const r=e(t);return!Object.prototype.hasOwnProperty.call(n,r)&&(n[r]=!0)}))}(t);return 1===e.length?e:e.filter(((t,n)=>{const r=e.filter(((t,e)=>e!==n));return!r.some((e=>t.oldRange.from>=e.oldRange.from&&t.oldRange.to<=e.oldRange.to&&t.newRange.from>=e.newRange.from&&t.newRange.to<=e.newRange.to))}))}(r)}function ao(t,e,n){const r=[];return t===e?n.resolve(t).marks().forEach((e=>{const i=Hs(n.resolve(t-1),e.type);i&&r.push({mark:e,...i})})):n.nodesBetween(t,e,((t,e)=>{t&&void 0!==(null==t?void 0:t.nodeSize)&&r.push(...t.marks.map((n=>({from:e,to:e+t.nodeSize,mark:n}))))})),r}function co(t,e,n){return Object.fromEntries(Object.entries(n).filter((([n])=>{const r=t.find((t=>t.type===e&&t.name===n));return!!r&&r.attribute.keepOnSplit})))}function po(t,e,n={}){const{empty:r,ranges:i}=t.selection,s=e?Us(e,t.schema):null;if(r)return!!(t.storedMarks||t.selection.$from.marks()).filter((t=>!s||s.name===t.type.name)).find((t=>Fs(t.attrs,n,{strict:!1})));let o=0;const l=[];if(i.forEach((({$from:e,$to:n})=>{const r=e.pos,i=n.pos;t.doc.nodesBetween(r,i,((t,e)=>{if(!t.isText&&!t.marks.length)return;const n=Math.max(r,e),s=Math.min(i,e+t.nodeSize);o+=s-n,l.push(...t.marks.map((t=>({mark:t,from:n,to:s}))))}))})),0===o)return!1;const u=l.filter((t=>!s||s.name===t.mark.type.name)).filter((t=>Fs(t.mark.attrs,n,{strict:!1}))).reduce(((t,e)=>t+e.to-e.from),0),h=l.filter((t=>!s||t.mark.type!==s&&t.mark.type.excludes(s))).reduce(((t,e)=>t+e.to-e.from),0);return(u>0?u+h:u)>=o}function mo(t,e){const{nodeExtensions:n}=ds(e),r=n.find((e=>e.name===t));if(!r)return!1;const i=bs(cs(r,"group",{name:r.name,options:r.options,storage:r.storage}));return"string"==typeof i&&i.split(" ").includes("list")}function wo(t){return t instanceof Zt}function vo(t,e,n){const r=t.state.doc.content.size,i=Ks(e,0,r),s=Ks(n,0,r),o=t.coordsAtPos(i),l=t.coordsAtPos(s,-1),u=Math.min(o.top,l.top),h=Math.max(o.bottom,l.bottom),f=Math.min(o.left,l.left),a=Math.max(o.right,l.right),c={top:u,bottom:h,left:f,right:a,width:a-f,height:h-u,x:f,y:u};return{...c,toJSON:()=>c}}function go(t,e){const n=t.storedMarks||t.selection.$to.parentOffset&&t.selection.$from.marks();if(n){const r=n.filter((t=>null==e?void 0:e.includes(t.type.name)));t.tr.ensureMarks(r)}}const bo=(t,e)=>{const n=lo((t=>t.type===e))(t.selection);if(!n)return!0;const r=t.doc.resolve(Math.max(0,n.pos-1)).before(n.depth);if(void 0===r)return!0;const i=t.doc.nodeAt(r);return n.node.type!==(null==i?void 0:i.type)||!Ct(t.doc,n.pos)||(t.join(n.pos),!0)},yo=(t,e)=>{const n=lo((t=>t.type===e))(t.selection);if(!n)return!0;const r=t.doc.resolve(n.start).after(n.depth);if(void 0===r)return!0;const i=t.doc.nodeAt(r);return n.node.type!==(null==i?void 0:i.type)||!Ct(t.doc,r)||(t.join(r),!0)};var ko=Object.freeze({__proto__:null,blur:()=>({editor:t,view:e})=>(requestAnimationFrame((()=>{var n;t.isDestroyed||(e.dom.blur(),null===(n=null===window||void 0===window?void 0:window.getSelection())||void 0===n||n.removeAllRanges())})),!0),clearContent:(t=!1)=>({commands:e})=>e.setContent("",t),clearNodes:()=>({state:t,tr:e,dispatch:n})=>{const{selection:r}=e,{ranges:i}=r;return!n||(i.forEach((({$from:n,$to:r})=>{t.doc.nodesBetween(n.pos,r.pos,((t,n)=>{if(t.type.isText)return;const{doc:r,mapping:i}=e,s=r.resolve(i.map(n)),o=r.resolve(i.map(n+t.nodeSize)),l=s.blockRange(o);if(!l)return;const u=Ot(l);if(t.type.isTextblock){const{defaultType:t}=s.parent.contentMatchAt(s.index());e.setNodeMarkup(l.start,t)}(u||0===u)&&e.lift(l,u)}))})),!0)},command:t=>e=>t(e),createParagraphNear:()=>({state:t,dispatch:e})=>_i(t,e),cut:(t,e)=>({editor:n,tr:r})=>{const{state:i}=n,s=i.doc.slice(t.from,t.to);r.deleteRange(t.from,t.to);const o=r.mapping.map(e);return r.insert(o,s.content),r.setSelection(new Yt(r.doc.resolve(o-1))),!0},deleteCurrentNode:()=>({tr:t,dispatch:e})=>{const{selection:n}=t,r=n.$anchor.node();if(r.content.size>0)return!1;const i=t.selection.$anchor;for(let n=i.depth;n>0;n-=1)if(i.node(n).type===r.type){if(e){const e=i.before(n),r=i.after(n);t.delete(e,r).scrollIntoView()}return!0}return!1},deleteNode:t=>({tr:e,state:n,dispatch:r})=>{const i=ms(t,n.schema),s=e.selection.$anchor;for(let t=s.depth;t>0;t-=1)if(s.node(t).type===i){if(r){const n=s.before(t),r=s.after(t);e.delete(n,r).scrollIntoView()}return!0}return!1},deleteRange:t=>({tr:e,dispatch:n})=>{const{from:r,to:i}=t;return n&&e.delete(r,i),!0},deleteSelection:()=>({state:t,dispatch:e})=>Pi(t,e),enter:()=>({commands:t})=>t.keyboardShortcut("Enter"),exitCode:()=>({state:t,dispatch:e})=>Vi(t,e),extendMarkRange:(t,e={})=>({tr:n,state:r,dispatch:i})=>{const s=Us(t,r.schema),{doc:o,selection:l}=n,{$from:u,from:h,to:f}=l;if(i){const t=Hs(u,s,e);if(t&&t.from<=h&&t.to>=f){const e=Yt.create(o,t.from,t.to);n.setSelection(e)}}return!0},first:t=>e=>{const n="function"==typeof t?t(e):t;for(let t=0;t<n.length;t+=1)if(n[t](e))return!0;return!1},focus:(t=null,e={})=>({editor:n,view:r,tr:i,dispatch:s})=>{e={scrollIntoView:!0,...e};const o=()=>{Gs()&&r.dom.focus(),requestAnimationFrame((()=>{n.isDestroyed||(r.focus(),(null==e?void 0:e.scrollIntoView)&&n.commands.scrollIntoView())}))};if(r.hasFocus()&&null===t||!1===t)return!0;if(s&&null===t&&!Ws(n.state.selection))return o(),!0;const l=zs(i.doc,t)||n.state.selection,u=n.state.selection.eq(l);return s&&(u||i.setSelection(l),u&&i.storedMarks&&i.setStoredMarks(i.storedMarks),o()),!0},forEach:(t,e)=>n=>t.every(((t,r)=>e(t,{...n,index:r}))),insertContent:(t,e)=>({tr:n,commands:r})=>r.insertContentAt({from:n.selection.from,to:n.selection.to},t,e),insertContentAt:(t,e,n)=>({tr:r,dispatch:i,editor:s})=>{if(i){n={parseOptions:{},updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...n};const i=Qs(e,s.schema,{parseOptions:{preserveWhitespace:"full",...n.parseOptions}});if("<>"===i.toString())return!0;let o,{from:l,to:u}="number"==typeof t?{from:t,to:t}:{from:t.from,to:t.to},h=!0,f=!0;if((i.toString().startsWith("<")?i:[i]).forEach((t=>{t.check(),h=!!h&&t.isText&&0===t.marks.length,f=!!f&&t.isBlock})),l===u&&f){const{parent:t}=r.doc.resolve(l);t.isTextblock&&!t.type.spec.code&&!t.childCount&&(l-=1,u+=1)}h?(o=Array.isArray(e)?e.map((t=>t.text||"")).join(""):"object"==typeof e&&e&&e.text?e.text:e,r.insertText(o,l,u)):(o=i,r.replaceWith(l,u,o)),n.updateSelection&&function(t,e){const n=t.steps.length-1;if(n<e)return;const r=t.steps[n];if(!(r instanceof yt||r instanceof kt))return;let i=0;t.mapping.maps[n].forEach(((t,e,n,r)=>{0===i&&(i=r)})),t.setSelection(Gt.near(t.doc.resolve(i),-1))}(r,r.steps.length-1),n.applyInputRules&&r.setMeta("applyInputRules",{from:l,text:o}),n.applyPasteRules&&r.setMeta("applyPasteRules",{from:l,text:o})}return!0},joinUp:()=>({state:t,dispatch:e})=>((t,e)=>{let n,r=t.selection,i=r instanceof Zt;if(i){if(r.node.isTextblock||!Ct(t.doc,r.from))return!1;n=r.from}else if(n=Tt(t.doc,r.from,-1),null==n)return!1;if(e){let r=t.tr.join(n);i&&r.setSelection(Zt.create(r.doc,n-t.doc.resolve(n).nodeBefore.nodeSize)),e(r.scrollIntoView())}return!0})(t,e),joinDown:()=>({state:t,dispatch:e})=>((t,e)=>{let n,r=t.selection;if(r instanceof Zt){if(r.node.isTextblock||!Ct(t.doc,r.to))return!1;n=r.to}else if(n=Tt(t.doc,r.to,1),null==n)return!1;return e&&e(t.tr.join(n).scrollIntoView()),!0})(t,e),joinBackward:()=>({state:t,dispatch:e})=>Bi(t,e),joinForward:()=>({state:t,dispatch:e})=>Ui(t,e),joinItemBackward:()=>({tr:t,state:e,dispatch:n})=>{try{const r=Tt(e.doc,e.selection.$from.pos,-1);return null!=r&&(t.join(r,2),n&&n(t),!0)}catch{return!1}},joinItemForward:()=>({state:t,dispatch:e,tr:n})=>{try{const r=Tt(t.doc,t.selection.$from.pos,1);return null!=r&&(n.join(r,2),e&&e(n),!0)}catch(t){return!1}},joinTextblockBackward:()=>({state:t,dispatch:e})=>((t,e)=>{let n=Ii(t,void 0);if(!n)return!1;let r=qi(n);return!!r&&Ji(t,r,e)})(t,e),joinTextblockForward:()=>({state:t,dispatch:e})=>((t,e)=>{let n=Hi(t,void 0);if(!n)return!1;let r=Ki(n);return!!r&&Ji(t,r,e)})(t,e),keyboardShortcut:t=>({editor:e,view:n,tr:r,dispatch:i})=>{const s=function(t){const e=t.split(/-(?!$)/);let n,r,i,s,o=e[e.length-1];"Space"===o&&(o=" ");for(let t=0;t<e.length-1;t+=1){const o=e[t];if(/^(cmd|meta|m)$/i.test(o))s=!0;else if(/^a(lt)?$/i.test(o))n=!0;else if(/^(c|ctrl|control)$/i.test(o))r=!0;else if(/^s(hift)?$/i.test(o))i=!0;else{if(!/^mod$/i.test(o))throw new Error(`Unrecognized modifier name: ${o}`);Gs()||Ys()?s=!0:r=!0}}return n&&(o=`Alt-${o}`),r&&(o=`Ctrl-${o}`),s&&(o=`Meta-${o}`),i&&(o=`Shift-${o}`),o}(t).split(/-(?!$)/),o=s.find((t=>!["Alt","Ctrl","Meta","Shift"].includes(t))),l=new KeyboardEvent("keydown",{key:"Space"===o?" ":o,altKey:s.includes("Alt"),ctrlKey:s.includes("Ctrl"),metaKey:s.includes("Meta"),shiftKey:s.includes("Shift"),bubbles:!0,cancelable:!0}),u=e.captureTransaction((()=>{n.someProp("handleKeyDown",(t=>t(n,l)))}));return null==u||u.steps.forEach((t=>{const e=t.map(r.mapping);e&&i&&r.maybeStep(e)})),!0},lift:(t,e={})=>({state:n,dispatch:r})=>!!Xs(n,ms(t,n.schema),e)&&((t,e)=>{let{$from:n,$to:r}=t.selection,i=n.blockRange(r),s=i&&Ot(i);return null!=s&&(e&&e(t.tr.lift(i,s).scrollIntoView()),!0)})(n,r),liftEmptyBlock:()=>({state:t,dispatch:e})=>Qi(t,e),liftListItem:t=>({state:e,dispatch:n})=>{return(i=ms(t,e.schema),function(t,e){let{$from:n,$to:s}=t.selection,o=n.blockRange(s,(t=>t.childCount>0&&t.firstChild.type==i));return!!o&&(!e||(n.node(o.depth-1).type==i?function(t,e,n,i){let s=t.tr,o=i.end,l=i.$to.end(i.depth);o<l&&(s.step(new kt(o-1,l,o,l,new h(r.from(n.create(null,i.parent.copy())),1,0),1,!0)),i=new S(s.doc.resolve(i.$from.pos),s.doc.resolve(l),i.depth));const u=Ot(i);if(null==u)return!1;s.lift(i,u);let f=s.mapping.map(o,-1)-1;return Ct(s.doc,f)&&s.join(f),e(s.scrollIntoView()),!0}(t,e,i,o):function(t,e,n){let i=t.tr,s=n.parent;for(let t=n.end,e=n.endIndex-1,r=n.startIndex;e>r;e--)t-=s.child(e).nodeSize,i.delete(t-1,t+1);let o=i.doc.resolve(n.start),l=o.nodeAfter;if(i.mapping.map(n.end)!=n.start+o.nodeAfter.nodeSize)return!1;let u=0==n.startIndex,f=n.endIndex==s.childCount,a=o.node(-1),c=o.index(-1);if(!a.canReplace(c+(u?0:1),c+1,l.content.append(f?r.empty:r.from(s))))return!1;let d=o.pos,p=d+l.nodeSize;return i.step(new kt(d-(u?1:0),p+(f?1:0),d+1,p-1,new h((u?r.empty:r.from(s.copy(r.empty))).append(f?r.empty:r.from(s.copy(r.empty))),u?0:1,f?0:1),u?0:1)),e(i.scrollIntoView()),!0}(t,e,o)))})(e,n);var i},newlineInCode:()=>({state:t,dispatch:e})=>zi(t,e),resetAttributes:(t,e)=>({tr:n,state:r,dispatch:i})=>{let s=null,o=null;const l=Zs("string"==typeof t?t:t.name,r.schema);return!!l&&("node"===l&&(s=ms(t,r.schema)),"mark"===l&&(o=Us(t,r.schema)),i&&n.selection.ranges.forEach((t=>{r.doc.nodesBetween(t.$from.pos,t.$to.pos,((t,r)=>{s&&s===t.type&&n.setNodeMarkup(r,void 0,to(t.attrs,e)),o&&t.marks.length&&t.marks.forEach((i=>{o===i.type&&n.addMark(r,r+t.nodeSize,o.create(to(i.attrs,e)))}))}))})),!0)},scrollIntoView:()=>({tr:t,dispatch:e})=>(e&&t.scrollIntoView(),!0),selectAll:()=>({tr:t,commands:e})=>e.setTextSelection({from:0,to:t.doc.content.size}),selectNodeBackward:()=>({state:t,dispatch:e})=>Li(t,e),selectNodeForward:()=>({state:t,dispatch:e})=>Wi(t,e),selectParentNode:()=>({state:t,dispatch:e})=>((t,e)=>{let n,{$from:r,to:i}=t.selection,s=r.sharedDepth(i);return 0!=s&&(n=r.before(s),e&&e(t.tr.setSelection(Zt.create(t.doc,n))),!0)})(t,e),selectTextblockEnd:()=>({state:t,dispatch:e})=>es(t,e),selectTextblockStart:()=>({state:t,dispatch:e})=>ts(t,e),setContent:(t,e=!1,n={})=>({tr:r,editor:i,dispatch:s})=>{const{doc:o}=r,l=eo(t,i.schema,n);return s&&r.replaceWith(0,o.content.size,l).setMeta("preventUpdate",!e),!0},setMark:(t,e={})=>({tr:n,state:r,dispatch:i})=>{const{selection:s}=n,{empty:o,ranges:l}=s,u=Us(t,r.schema);if(i)if(o){const t=no(r,u);n.addStoredMark(u.create({...t,...e}))}else l.forEach((t=>{const i=t.$from.pos,s=t.$to.pos;r.doc.nodesBetween(i,s,((t,r)=>{const o=Math.max(r,i),l=Math.min(r+t.nodeSize,s);t.marks.find((t=>t.type===u))?t.marks.forEach((t=>{u===t.type&&n.addMark(o,l,u.create({...t.attrs,...e}))})):n.addMark(o,l,u.create(e))}))}));return function(t,e,n){var r;const{selection:i}=e;let s=null;if(Ws(i)&&(s=i.$cursor),s){const e=null!==(r=t.storedMarks)&&void 0!==r?r:s.marks();return!!n.isInSet(e)||!e.some((t=>t.type.excludes(n)))}const{ranges:o}=i;return o.some((({$from:e,$to:r})=>{let i=0===e.depth&&t.doc.inlineContent&&t.doc.type.allowsMarkType(n);return t.doc.nodesBetween(e.pos,r.pos,((t,e,r)=>{if(i)return!1;if(t.isInline){const e=!r||r.type.allowsMarkType(n),s=!!n.isInSet(t.marks)||!t.marks.some((t=>t.type.excludes(n)));i=e&&s}return!i})),i}))}(r,n,u)},setMeta:(t,e)=>({tr:n})=>(n.setMeta(t,e),!0),setNode:(t,e={})=>({state:n,dispatch:r,chain:i})=>{const s=ms(t,n.schema);return s.isTextblock?i().command((({commands:t})=>!!ns(s,e)(n)||t.clearNodes())).command((({state:t})=>ns(s,e)(t,r))).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},setNodeSelection:t=>({tr:e,dispatch:n})=>{if(n){const{doc:n}=e,r=Ks(t,0,n.content.size),i=Zt.create(n,r);e.setSelection(i)}return!0},setTextSelection:t=>({tr:e,dispatch:n})=>{if(n){const{doc:n}=e,{from:r,to:i}="number"==typeof t?{from:t,to:t}:t,s=Yt.atStart(n).from,o=Yt.atEnd(n).to,l=Ks(r,s,o),u=Ks(i,s,o),h=Yt.create(n,l,u);e.setSelection(h)}return!0},sinkListItem:t=>({state:e,dispatch:n})=>{const i=ms(t,e.schema);return(s=i,function(t,e){let{$from:n,$to:i}=t.selection,o=n.blockRange(i,(t=>t.childCount>0&&t.firstChild.type==s));if(!o)return!1;let l=o.startIndex;if(0==l)return!1;let u=o.parent,f=u.child(l-1);if(f.type!=s)return!1;if(e){let n=f.lastChild&&f.lastChild.type==u.type,i=r.from(n?s.create():null),l=new h(r.from(s.create(null,r.from(u.type.create(null,i)))),n?3:1,0),a=o.start,c=o.end;e(t.tr.step(new kt(a-(n?3:1),c,a,c,l,1,!0)).scrollIntoView())}return!0})(e,n);var s},splitBlock:({keepMarks:t=!0}={})=>({tr:e,state:n,dispatch:r,editor:i})=>{const{selection:s,doc:o}=e,{$from:l,$to:u}=s,h=co(i.extensionManager.attributes,l.node().type.name,l.node().attrs);if(s instanceof Zt&&s.node.isBlock)return!(!l.parentOffset||!Nt(o,l.pos)||(r&&(t&&go(n,i.extensionManager.splittableMarks),e.split(l.pos).scrollIntoView()),0));if(!l.parent.isBlock)return!1;if(r){const r=u.parentOffset===u.parent.content.size;s instanceof Yt&&e.deleteSelection();const o=0===l.depth?void 0:function(t){for(let e=0;e<t.edgeCount;e+=1){const{type:n}=t.edge(e);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}(l.node(-1).contentMatchAt(l.indexAfter(-1)));let f=r&&o?[{type:o,attrs:h}]:void 0,a=Nt(e.doc,e.mapping.map(l.pos),1,f);if(f||a||!Nt(e.doc,e.mapping.map(l.pos),1,o?[{type:o}]:void 0)||(a=!0,f=o?[{type:o,attrs:h}]:void 0),a&&(e.split(e.mapping.map(l.pos),1,f),o&&!r&&!l.parentOffset&&l.parent.type!==o)){const t=e.mapping.map(l.before()),n=e.doc.resolve(t);l.node(-1).canReplaceWith(n.index(),n.index()+1,o)&&e.setNodeMarkup(e.mapping.map(l.before()),o)}t&&go(n,i.extensionManager.splittableMarks),e.scrollIntoView()}return!0},splitListItem:t=>({tr:e,state:n,dispatch:i,editor:s})=>{var o;const l=ms(t,n.schema),{$from:u,$to:f}=n.selection,a=n.selection.node;if(a&&a.isBlock||u.depth<2||!u.sameParent(f))return!1;const c=u.node(-1);if(c.type!==l)return!1;const d=s.extensionManager.attributes;if(0===u.parent.content.size&&u.node(-1).childCount===u.indexAfter(-1)){if(2===u.depth||u.node(-3).type!==l||u.index(-2)!==u.node(-2).childCount-1)return!1;if(i){let t=r.empty;const n=u.index(-1)?1:u.index(-2)?2:3;for(let e=u.depth-n;e>=u.depth-3;e-=1)t=r.from(u.node(e).copy(t));const i=u.indexAfter(-1)<u.node(-2).childCount?1:u.indexAfter(-2)<u.node(-3).childCount?2:3,s=co(d,u.node().type.name,u.node().attrs),f=(null===(o=l.contentMatch.defaultType)||void 0===o?void 0:o.createAndFill(s))||void 0;t=t.append(r.from(l.createAndFill(null,f)||void 0));const a=u.before(u.depth-(n-1));e.replace(a,u.after(-i),new h(t,4-n,0));let c=-1;e.doc.nodesBetween(a,e.doc.content.size,((t,e)=>{if(c>-1)return!1;t.isTextblock&&0===t.content.size&&(c=e+1)})),c>-1&&e.setSelection(Yt.near(e.doc.resolve(c))),e.scrollIntoView()}return!0}const p=f.pos===u.end()?c.contentMatchAt(0).defaultType:null,m=co(d,c.type.name,c.attrs),w=co(d,u.node().type.name,u.node().attrs);e.delete(u.pos,f.pos);const v=p?[{type:l,attrs:m},{type:p,attrs:w}]:[{type:l,attrs:m}];if(!Nt(e.doc,u.pos,2))return!1;if(i){const{selection:t,storedMarks:r}=n,{splittableMarks:o}=s.extensionManager,l=r||t.$to.parentOffset&&t.$from.marks();if(e.split(u.pos,2,v).scrollIntoView(),!l||!i)return!0;const h=l.filter((t=>o.includes(t.type.name)));e.ensureMarks(h)}return!0},toggleList:(t,e,n,r={})=>({editor:i,tr:s,state:o,dispatch:l,chain:u,commands:h,can:f})=>{const{extensions:a,splittableMarks:c}=i.extensionManager,d=ms(t,o.schema),p=ms(e,o.schema),{selection:m,storedMarks:w}=o,{$from:v,$to:g}=m,b=v.blockRange(g),y=w||m.$to.parentOffset&&m.$from.marks();if(!b)return!1;const k=lo((t=>mo(t.type.name,a)))(m);if(b.depth>=1&&k&&b.depth-k.depth<=1){if(k.node.type===d)return h.liftListItem(p);if(mo(k.node.type.name,a)&&d.validContent(k.node.content)&&l)return u().command((()=>(s.setNodeMarkup(k.pos,d),!0))).command((()=>bo(s,d))).command((()=>yo(s,d))).run()}return n&&y&&l?u().command((()=>{const t=f().wrapInList(d,r),e=y.filter((t=>c.includes(t.type.name)));return s.ensureMarks(e),!!t||h.clearNodes()})).wrapInList(d,r).command((()=>bo(s,d))).command((()=>yo(s,d))).run():u().command((()=>!!f().wrapInList(d,r)||h.clearNodes())).wrapInList(d,r).command((()=>bo(s,d))).command((()=>yo(s,d))).run()},toggleMark:(t,e={},n={})=>({state:r,commands:i})=>{const{extendEmptyMarkRange:s=!1}=n,o=Us(t,r.schema);return po(r,o,e)?i.unsetMark(o,{extendEmptyMarkRange:s}):i.setMark(o,e)},toggleNode:(t,e,n={})=>({state:r,commands:i})=>{const s=ms(t,r.schema),o=ms(e,r.schema);return Xs(r,s,n)?i.setNode(o):i.setNode(s,n)},toggleWrap:(t,e={})=>({state:n,commands:r})=>{const i=ms(t,n.schema);return Xs(n,i,e)?r.lift(i):r.wrapIn(i,e)},undoInputRule:()=>({state:t,dispatch:e})=>{const n=t.plugins;for(let r=0;r<n.length;r+=1){const i=n[r];let s;if(i.spec.isInputRules&&(s=i.getState(t))){if(e){const e=t.tr,n=s.transform;for(let t=n.steps.length-1;t>=0;t-=1)e.step(n.steps[t].invert(n.docs[t]));if(s.text){const n=e.doc.resolve(s.from).marks();e.replaceWith(s.from,s.to,t.schema.text(s.text,n))}else e.delete(s.from,s.to)}return!0}}return!1},unsetAllMarks:()=>({tr:t,dispatch:e})=>{const{selection:n}=t,{empty:r,ranges:i}=n;return r||e&&i.forEach((e=>{t.removeMark(e.$from.pos,e.$to.pos)})),!0},unsetMark:(t,e={})=>({tr:n,state:r,dispatch:i})=>{var s;const{extendEmptyMarkRange:o=!1}=e,{selection:l}=n,u=Us(t,r.schema),{$from:h,empty:f,ranges:a}=l;if(!i)return!0;if(f&&o){let{from:t,to:e}=l;const r=null===(s=h.marks().find((t=>t.type===u)))||void 0===s?void 0:s.attrs,i=Hs(h,u,r);i&&(t=i.from,e=i.to),n.removeMark(t,e,u)}else a.forEach((t=>{n.removeMark(t.$from.pos,t.$to.pos,u)}));return n.removeStoredMark(u),!0},updateAttributes:(t,e={})=>({tr:n,state:r,dispatch:i})=>{let s=null,o=null;const l=Zs("string"==typeof t?t:t.name,r.schema);return!!l&&("node"===l&&(s=ms(t,r.schema)),"mark"===l&&(o=Us(t,r.schema)),i&&n.selection.ranges.forEach((t=>{const i=t.$from.pos,l=t.$to.pos;r.doc.nodesBetween(i,l,((t,r)=>{s&&s===t.type&&n.setNodeMarkup(r,void 0,{...t.attrs,...e}),o&&t.marks.length&&t.marks.forEach((s=>{if(o===s.type){const u=Math.max(r,i),h=Math.min(r+t.nodeSize,l);n.addMark(u,h,o.create({...s.attrs,...e}))}}))}))})),!0)},wrapIn:(t,e={})=>({state:n,dispatch:r})=>function(t,e=null){return function(n,r){let{$from:i,$to:s}=n.selection,o=i.blockRange(s),l=o&&St(o,t,e);return!!l&&(r&&r(n.tr.wrap(o,l).scrollIntoView()),!0)}}(ms(t,n.schema),e)(n,r),wrapInList:(t,e={})=>({state:n,dispatch:i})=>function(t,e=null){return function(n,i){let{$from:s,$to:o}=n.selection,l=s.blockRange(o),u=!1,f=l;if(!l)return!1;if(l.depth>=2&&s.node(l.depth-1).type.compatibleContent(t)&&0==l.startIndex){if(0==s.index(l.depth-1))return!1;let t=n.doc.resolve(l.start-2);f=new S(t,t,l.depth),l.endIndex<l.parent.childCount&&(l=new S(s,n.doc.resolve(o.end(l.depth)),l.depth)),u=!0}let a=St(f,t,e,l);return!!a&&(i&&i(function(t,e,n,i,s){let o=r.empty;for(let t=n.length-1;t>=0;t--)o=r.from(n[t].type.create(n[t].attrs,o));t.step(new kt(e.start-(i?2:0),e.end,e.start,e.end,new h(o,0,0),n.length,!0));let l=0;for(let t=0;t<n.length;t++)n[t].type==s&&(l=t+1);let u=n.length-l,f=e.start+n.length-(i?2:0),a=e.parent;for(let n=e.startIndex,r=e.endIndex,i=!0;n<r;n++,i=!1)!i&&Nt(t.doc,f,u)&&(t.split(f,u),f+=2*u),f+=a.child(n).nodeSize;return t}(n.tr,l,a,u,t).scrollIntoView()),!0)}}(ms(t,n.schema),e)(n,i)});const xo=Ps.create({name:"commands",addCommands:()=>({...ko})}),Mo=Ps.create({name:"editable",addProseMirrorPlugins(){return[new ce({key:new me("editable"),props:{editable:()=>this.editor.options.editable}})]}}),Oo=Ps.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:t}=this;return[new ce({key:new me("focusEvents"),props:{handleDOMEvents:{focus:(e,n)=>{t.isFocused=!0;const r=t.state.tr.setMeta("focus",{event:n}).setMeta("addToHistory",!1);return e.dispatch(r),!1},blur:(e,n)=>{t.isFocused=!1;const r=t.state.tr.setMeta("blur",{event:n}).setMeta("addToHistory",!1);return e.dispatch(r),!1}}}})]}}),So=Ps.create({name:"keymap",addKeyboardShortcuts(){const t=()=>this.editor.commands.first((({commands:t})=>[()=>t.undoInputRule(),()=>t.command((({tr:e})=>{const{selection:n,doc:r}=e,{empty:i,$anchor:s}=n,{pos:o,parent:l}=s,u=s.parent.isTextblock&&o>0?e.doc.resolve(o-1):s,h=u.parent.type.spec.isolating&&1===u.parent.childCount?s.pos-s.parentOffset===s.pos:Gt.atStart(r).from===o;return!(!(i&&h&&l.type.isTextblock)||l.textContent.length)&&t.clearNodes()})),()=>t.deleteSelection(),()=>t.joinBackward(),()=>t.selectNodeBackward()])),e=()=>this.editor.commands.first((({commands:t})=>[()=>t.deleteSelection(),()=>t.deleteCurrentNode(),()=>t.joinForward(),()=>t.selectNodeForward()])),n={Enter:()=>this.editor.commands.first((({commands:t})=>[()=>t.newlineInCode(),()=>t.createParagraphNear(),()=>t.liftEmptyBlock(),()=>t.splitBlock()])),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:t,"Mod-Backspace":t,"Shift-Backspace":t,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},r={...n},i={...n,"Ctrl-h":t,"Alt-Backspace":t,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return Gs()||Ys()?i:r},addProseMirrorPlugins(){return[new ce({key:new me("clearDocument"),appendTransaction:(t,e,n)=>{if(!t.some((t=>t.docChanged))||e.doc.eq(n.doc))return;const{empty:r,from:i,to:s}=e.selection,o=Gt.atStart(e.doc).from,l=Gt.atEnd(e.doc).to;if(r||i!==o||s!==l)return;if(0!==n.doc.textBetween(0,n.doc.content.size," "," ").length)return;const u=n.tr,h=hs({state:n,transaction:u}),{commands:f}=new fs({editor:this.editor,state:h});return f.clearNodes(),u.steps.length?u:void 0}})]}}),Eo=Ps.create({name:"tabindex",addProseMirrorPlugins(){return[new ce({key:new me("tabindex"),props:{attributes:this.editor.isEditable?{tabindex:"0"}:{}}})]}});var No=Object.freeze({__proto__:null,ClipboardTextSerializer:Js,Commands:xo,Editable:Mo,FocusEvents:Oo,Keymap:So,Tabindex:Eo});class Co{constructor(t,e,n=!1,r=null){this.currentNode=null,this.actualDepth=null,this.isBlock=n,this.resolvedPos=t,this.editor=e,this.currentNode=r}get name(){return this.node.type.name}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var t;return null!==(t=this.actualDepth)&&void 0!==t?t:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(t){let e=this.from,n=this.to;if(this.isBlock){if(0===this.content.size)return void console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);e=this.from+1,n=this.to-1}this.editor.commands.insertContentAt({from:e,to:n},t)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(0===this.depth)return null;const t=this.resolvedPos.start(this.resolvedPos.depth-1),e=this.resolvedPos.doc.resolve(t);return new Co(e,this.editor)}get before(){let t=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return t.depth!==this.depth&&(t=this.resolvedPos.doc.resolve(this.from-3)),new Co(t,this.editor)}get after(){let t=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return t.depth!==this.depth&&(t=this.resolvedPos.doc.resolve(this.to+3)),new Co(t,this.editor)}get children(){const t=[];return this.node.content.forEach(((e,n)=>{const r=e.isBlock&&!e.isTextblock,i=this.resolvedPos.doc.resolve(this.pos+n+1);if(!r&&i.depth<=this.depth)return;const s=new Co(i,this.editor,r,r?e:null);r&&(s.actualDepth=this.depth+1),t.push(new Co(i,this.editor,r,r?e:null))})),t}get firstChild(){return this.children[0]||null}get lastChild(){const t=this.children;return t[t.length-1]||null}closest(t,e={}){let n=null,r=this.parent;for(;r&&!n;)r.node.type.name===t&&(Object.keys(e).length>0||(n=r)),r=r.parent;return n}querySelector(t,e={}){return this.querySelectorAll(t,e,!0)[0]||null}querySelectorAll(t,e={},n=!1){let r=[];if(!this.children||0===this.children.length)return r;const i=Object.keys(e);return this.children.forEach((s=>{n&&r.length>0||(s.node.type.name===t&&i.every((t=>e[t]===s.node.attrs[t]))&&r.push(s),n&&r.length>0||(r=r.concat(s.querySelectorAll(t,e,n))))})),r}setAttribute(t){const e=this.editor.state.selection;this.editor.chain().setTextSelection(this.from).updateAttributes(this.node.type.name,t).setTextSelection(e.from).run()}}class Ro extends as{constructor(t={}){super(),this.isFocused=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(t),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),window.setTimeout((()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}))}),0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=function(t,e){const n=document.querySelector("style[data-tiptap-style]");if(null!==n)return n;const r=document.createElement("style");return e&&r.setAttribute("nonce",e),r.setAttribute("data-tiptap-style",""),r.innerHTML='.ProseMirror {\n  position: relative;\n}\n\n.ProseMirror {\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  white-space: break-spaces;\n  -webkit-font-variant-ligatures: none;\n  font-variant-ligatures: none;\n  font-feature-settings: "liga" 0; /* the above doesn\'t seem to work in Edge */\n}\n\n.ProseMirror [contenteditable="false"] {\n  white-space: normal;\n}\n\n.ProseMirror [contenteditable="false"] [contenteditable="true"] {\n  white-space: pre-wrap;\n}\n\n.ProseMirror pre {\n  white-space: pre-wrap;\n}\n\nimg.ProseMirror-separator {\n  display: inline !important;\n  border: none !important;\n  margin: 0 !important;\n  width: 1px !important;\n  height: 1px !important;\n}\n\n.ProseMirror-gapcursor {\n  display: none;\n  pointer-events: none;\n  position: absolute;\n  margin: 0;\n}\n\n.ProseMirror-gapcursor:after {\n  content: "";\n  display: block;\n  position: absolute;\n  top: -2px;\n  width: 20px;\n  border-top: 1px solid black;\n  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;\n}\n\n@keyframes ProseMirror-cursor-blink {\n  to {\n    visibility: hidden;\n  }\n}\n\n.ProseMirror-hideselection *::selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection *::-moz-selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection * {\n  caret-color: transparent;\n}\n\n.ProseMirror-focused .ProseMirror-gapcursor {\n  display: block;\n}\n\n.tippy-box[data-animation=fade][data-state=hidden] {\n  opacity: 0\n}',document.getElementsByTagName("head")[0].appendChild(r),r}(0,this.options.injectNonce))}setOptions(t={}){this.options={...this.options,...t},this.view&&this.state&&!this.isDestroyed&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(t,e=!0){this.setOptions({editable:t}),e&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(t,e){const n=gs(e)?e(t,[...this.state.plugins]):[...this.state.plugins,t],r=this.state.reconfigure({plugins:n});this.view.updateState(r)}unregisterPlugin(t){if(this.isDestroyed)return;const e="string"==typeof t?`${t}$`:t.key,n=this.state.reconfigure({plugins:this.state.plugins.filter((t=>!t.key.startsWith(e)))});this.view.updateState(n)}createExtensionManager(){var t,e;const n=[...this.options.enableCoreExtensions?[Mo,Js.configure({blockSeparator:null===(e=null===(t=this.options.coreExtensionOptions)||void 0===t?void 0:t.clipboardTextSerializer)||void 0===e?void 0:e.blockSeparator}),xo,Oo,So,Eo]:[],...this.options.extensions].filter((t=>["extension","node","mark"].includes(null==t?void 0:t.type)));this.extensionManager=new As(n,this)}createCommandManager(){this.commandManager=new fs({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){const t=eo(this.options.content,this.schema,this.options.parseOptions),e=zs(t,this.options.autofocus);this.view=new bi(this.options.element,{...this.options.editorProps,dispatchTransaction:this.dispatchTransaction.bind(this),state:fe.create({doc:t,selection:e||void 0})});const n=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(n),this.createNodeViews(),this.prependClass(),this.view.dom.editor=this}createNodeViews(){this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(t){this.isCapturingTransaction=!0,t(),this.isCapturingTransaction=!1;const e=this.capturedTransaction;return this.capturedTransaction=null,e}dispatchTransaction(t){if(this.view.isDestroyed)return;if(this.isCapturingTransaction)return this.capturedTransaction?void t.steps.forEach((t=>{var e;return null===(e=this.capturedTransaction)||void 0===e?void 0:e.step(t)})):void(this.capturedTransaction=t);const e=this.state.apply(t),n=!this.state.selection.eq(e.selection);this.view.updateState(e),this.emit("transaction",{editor:this,transaction:t}),n&&this.emit("selectionUpdate",{editor:this,transaction:t});const r=t.getMeta("focus"),i=t.getMeta("blur");r&&this.emit("focus",{editor:this,event:r.event,transaction:t}),i&&this.emit("blur",{editor:this,event:i.event,transaction:t}),t.docChanged&&!t.getMeta("preventUpdate")&&this.emit("update",{editor:this,transaction:t})}getAttributes(t){return ho(this.state,t)}isActive(t,e){return function(t,e,n={}){if(!e)return Xs(t,null,n)||po(t,null,n);const r=Zs(e,t.schema);return"node"===r?Xs(t,e,n):"mark"===r&&po(t,e,n)}(this.state,"string"==typeof t?t:null,"string"==typeof t?e:t)}getJSON(){return this.state.doc.toJSON()}getHTML(){return uo(this.state.doc.content,this.schema)}getText(t){const{blockSeparator:e="\n\n",textSerializers:n={}}=t||{};return function(t,e){return Is(t,{from:0,to:t.content.size},e)}(this.state.doc,{blockSeparator:e,textSerializers:{...Bs(this.schema),...n}})}get isEmpty(){return function(t){var e;const n=null===(e=t.type.createAndFill())||void 0===e?void 0:e.toJSON(),r=t.toJSON();return JSON.stringify(n)===JSON.stringify(r)}(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){this.emit("destroy"),this.view&&this.view.destroy(),this.removeAllListeners()}get isDestroyed(){var t;return!(null===(t=this.view)||void 0===t?void 0:t.docView)}$node(t,e){var n;return(null===(n=this.$doc)||void 0===n?void 0:n.querySelector(t,e))||null}$nodes(t,e){var n;return(null===(n=this.$doc)||void 0===n?void 0:n.querySelectorAll(t,e))||null}$pos(t){const e=this.state.doc.resolve(t);return new Co(e,this)}get $doc(){return this.$pos(0)}}function To(t){return new Es({find:t.find,handler:({state:e,range:n,match:r})=>{const i=bs(t.getAttributes,void 0,r);if(!1===i||null===i)return null;const{tr:s}=e,o=r[r.length-1],l=r[0];if(o){const r=l.search(/\S/),u=n.from+l.indexOf(o),h=u+o.length;if(ao(n.from,n.to,e.doc).filter((e=>e.mark.type.excluded.find((n=>n===t.type&&n!==e.mark.type)))).filter((t=>t.to>u)).length)return null;h<n.to&&s.delete(h,n.to),u>n.from&&s.delete(n.from+r,u),s.addMark(n.from+r,n.from+r+o.length,t.type.create(i||{})),s.removeStoredMark(t.type)}}})}function jo(t){return new Es({find:t.find,handler:({state:e,range:n,match:r})=>{const i=bs(t.getAttributes,void 0,r)||{},{tr:s}=e,o=n.from;let l=n.to;const u=t.type.create(i);if(r[1]){let t=o+r[0].lastIndexOf(r[1]);t>l?t=l:l=t+r[1].length,s.insertText(r[0][r[0].length-1],o+r[0].length-1),s.replaceWith(t,l,u)}else r[0]&&s.insert(o-1,t.type.create(i)).delete(s.mapping.map(o),s.mapping.map(l));s.scrollIntoView()}})}function Ao(t){return new Es({find:t.find,handler:({state:e,range:n,match:r})=>{const i=e.doc.resolve(n.from),s=bs(t.getAttributes,void 0,r)||{};if(!i.node(-1).canReplaceWith(i.index(-1),i.indexAfter(-1),t.type))return null;e.tr.delete(n.from,n.to).setBlockType(n.from,n.from,t.type,s)}})}function $o(t){return new Es({find:t.find,handler:({state:e,range:n,match:r})=>{let i=t.replace,s=n.from;const o=n.to;if(r[1]){const t=r[0].lastIndexOf(r[1]);i+=r[0].slice(t+r[1].length),s+=t;const e=s-o;e>0&&(i=r[0].slice(t-e,t)+i,s=o)}e.tr.insertText(i,s,o)}})}function Do(t){return new Es({find:t.find,handler:({state:e,range:n,match:r,chain:i})=>{const s=bs(t.getAttributes,void 0,r)||{},o=e.tr.delete(n.from,n.to),l=o.doc.resolve(n.from).blockRange(),u=l&&St(l,t.type,s);if(!u)return null;if(o.wrap(l,u),t.keepMarks&&t.editor){const{selection:n,storedMarks:r}=e,{splittableMarks:i}=t.editor.extensionManager,s=r||n.$to.parentOffset&&n.$from.marks();if(s){const t=s.filter((t=>i.includes(t.type.name)));o.ensureMarks(t)}}if(t.keepAttributes){const e="bulletList"===t.type.name||"orderedList"===t.type.name?"listItem":"taskList";i().updateAttributes(e,s).run()}const h=o.doc.resolve(n.from-1).nodeBefore;h&&h.type===t.type&&Ct(o.doc,n.from-1)&&(!t.joinPredicate||t.joinPredicate(r,h))&&o.join(n.from-1)}})}class Po{constructor(t={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=bs(cs(this,"addOptions",{name:this.name}))),this.storage=bs(cs(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new Po(t)}configure(t={}){const e=this.extend();return e.options=Ds(this.options,t),e.storage=bs(cs(e,"addStorage",{name:e.name,options:e.options})),e}extend(t={}){const e=new Po({...this.config,...t});return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${e.name}".`),e.options=bs(cs(e,"addOptions",{name:e.name})),e.storage=bs(cs(e,"addStorage",{name:e.name,options:e.options})),e}static handleExit({editor:t,mark:e}){const{tr:n}=t.state,r=t.state.selection.$from;if(r.pos===r.end()){const i=r.marks();if(!i.find((t=>(null==t?void 0:t.type.name)===e.name)))return!1;const s=i.find((t=>(null==t?void 0:t.type.name)===e.name));return s&&n.removeStoredMark(s),n.insertText(" ",r.pos),t.view.dispatch(n),!0}return!1}}class Io{constructor(t={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=bs(cs(this,"addOptions",{name:this.name}))),this.storage=bs(cs(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new Io(t)}configure(t={}){const e=this.extend();return e.options=Ds(this.options,t),e.storage=bs(cs(e,"addStorage",{name:e.name,options:e.options})),e}extend(t={}){const e=new Io({...this.config,...t});return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${e.name}".`),e.options=bs(cs(e,"addOptions",{name:e.name})),e.storage=bs(cs(e,"addStorage",{name:e.name,options:e.options})),e}}function Bo(t){return new Ts({find:t.find,handler:({state:e,range:n,match:r,pasteEvent:i})=>{const s=bs(t.getAttributes,void 0,r,i);if(!1===s||null===s)return null;const{tr:o}=e,l=r[r.length-1],u=r[0];let h=n.to;if(l){const r=u.search(/\S/),i=n.from+u.indexOf(l),f=i+l.length;if(ao(n.from,n.to,e.doc).filter((e=>e.mark.type.excluded.find((n=>n===t.type&&n!==e.mark.type)))).filter((t=>t.to>i)).length)return null;f<n.to&&o.delete(f,n.to),i>n.from&&o.delete(n.from+r,i),h=n.from+r+l.length,o.addMark(n.from+r,h,t.type.create(s||{})),o.removeStoredMark(t.type)}}})}function Jo(t){return t.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")}var Fo="top",Lo="bottom",qo="right",Ho="left",Uo="auto",Wo=[Fo,Lo,qo,Ho],Ko="start",zo="end",Go="clippingParents",Vo="viewport",_o="popper",Qo="reference",Yo=Wo.reduce((function(t,e){return t.concat([e+"-"+Ko,e+"-"+zo])}),[]),Xo=[].concat(Wo,[Uo]).reduce((function(t,e){return t.concat([e,e+"-"+Ko,e+"-"+zo])}),[]),Zo=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function tl(t){return t?(t.nodeName||"").toLowerCase():null}function el(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function nl(t){return t instanceof el(t).Element||t instanceof Element}function rl(t){return t instanceof el(t).HTMLElement||t instanceof HTMLElement}function il(t){return"undefined"!=typeof ShadowRoot&&(t instanceof el(t).ShadowRoot||t instanceof ShadowRoot)}const sl={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var e=t.state;Object.keys(e.elements).forEach((function(t){var n=e.styles[t]||{},r=e.attributes[t]||{},i=e.elements[t];rl(i)&&tl(i)&&(Object.assign(i.style,n),Object.keys(r).forEach((function(t){var e=r[t];!1===e?i.removeAttribute(t):i.setAttribute(t,!0===e?"":e)})))}))},effect:function(t){var e=t.state,n={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,n.popper),e.styles=n,e.elements.arrow&&Object.assign(e.elements.arrow.style,n.arrow),function(){Object.keys(e.elements).forEach((function(t){var r=e.elements[t],i=e.attributes[t]||{},s=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:n[t]).reduce((function(t,e){return t[e]="",t}),{});rl(r)&&tl(r)&&(Object.assign(r.style,s),Object.keys(i).forEach((function(t){r.removeAttribute(t)})))}))}},requires:["computeStyles"]};function ol(t){return t.split("-")[0]}var ll=Math.max,ul=Math.min,hl=Math.round;function fl(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map((function(t){return t.brand+"/"+t.version})).join(" "):navigator.userAgent}function al(){return!/^((?!chrome|android).)*safari/i.test(fl())}function cl(t,e,n){void 0===e&&(e=!1),void 0===n&&(n=!1);var r=t.getBoundingClientRect(),i=1,s=1;e&&rl(t)&&(i=t.offsetWidth>0&&hl(r.width)/t.offsetWidth||1,s=t.offsetHeight>0&&hl(r.height)/t.offsetHeight||1);var o=(nl(t)?el(t):window).visualViewport,l=!al()&&n,u=(r.left+(l&&o?o.offsetLeft:0))/i,h=(r.top+(l&&o?o.offsetTop:0))/s,f=r.width/i,a=r.height/s;return{width:f,height:a,top:h,right:u+f,bottom:h+a,left:u,x:u,y:h}}function dl(t){var e=cl(t),n=t.offsetWidth,r=t.offsetHeight;return Math.abs(e.width-n)<=1&&(n=e.width),Math.abs(e.height-r)<=1&&(r=e.height),{x:t.offsetLeft,y:t.offsetTop,width:n,height:r}}function pl(t,e){var n=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(n&&il(n)){var r=e;do{if(r&&t.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function ml(t){return el(t).getComputedStyle(t)}function wl(t){return["table","td","th"].indexOf(tl(t))>=0}function vl(t){return((nl(t)?t.ownerDocument:t.document)||window.document).documentElement}function gl(t){return"html"===tl(t)?t:t.assignedSlot||t.parentNode||(il(t)?t.host:null)||vl(t)}function bl(t){return rl(t)&&"fixed"!==ml(t).position?t.offsetParent:null}function yl(t){for(var e=el(t),n=bl(t);n&&wl(n)&&"static"===ml(n).position;)n=bl(n);return n&&("html"===tl(n)||"body"===tl(n)&&"static"===ml(n).position)?e:n||function(t){var e=/firefox/i.test(fl());if(/Trident/i.test(fl())&&rl(t)&&"fixed"===ml(t).position)return null;var n=gl(t);for(il(n)&&(n=n.host);rl(n)&&["html","body"].indexOf(tl(n))<0;){var r=ml(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||e&&"filter"===r.willChange||e&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(t)||e}function kl(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function xl(t,e,n){return ll(t,ul(e,n))}function Ml(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function Ol(t,e){return e.reduce((function(e,n){return e[n]=t,e}),{})}function Sl(t){return t.split("-")[1]}var El={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Nl(t){var e,n=t.popper,r=t.popperRect,i=t.placement,s=t.variation,o=t.offsets,l=t.position,u=t.gpuAcceleration,h=t.adaptive,f=t.roundOffsets,a=t.isFixed,c=o.x,d=void 0===c?0:c,p=o.y,m=void 0===p?0:p,w="function"==typeof f?f({x:d,y:m}):{x:d,y:m};d=w.x,m=w.y;var v=o.hasOwnProperty("x"),g=o.hasOwnProperty("y"),b=Ho,y=Fo,k=window;if(h){var x=yl(n),M="clientHeight",O="clientWidth";x===el(n)&&"static"!==ml(x=vl(n)).position&&"absolute"===l&&(M="scrollHeight",O="scrollWidth"),(i===Fo||(i===Ho||i===qo)&&s===zo)&&(y=Lo,m-=(a&&x===k&&k.visualViewport?k.visualViewport.height:x[M])-r.height,m*=u?1:-1),i!==Ho&&(i!==Fo&&i!==Lo||s!==zo)||(b=qo,d-=(a&&x===k&&k.visualViewport?k.visualViewport.width:x[O])-r.width,d*=u?1:-1)}var S,E=Object.assign({position:l},h&&El),N=!0===f?function(t,e){var n=t.y,r=e.devicePixelRatio||1;return{x:hl(t.x*r)/r||0,y:hl(n*r)/r||0}}({x:d,y:m},el(n)):{x:d,y:m};return d=N.x,m=N.y,Object.assign({},E,u?((S={})[y]=g?"0":"",S[b]=v?"0":"",S.transform=(k.devicePixelRatio||1)<=1?"translate("+d+"px, "+m+"px)":"translate3d("+d+"px, "+m+"px, 0)",S):((e={})[y]=g?m+"px":"",e[b]=v?d+"px":"",e.transform="",e))}var Cl={passive:!0},Rl={left:"right",right:"left",bottom:"top",top:"bottom"};function Tl(t){return t.replace(/left|right|bottom|top/g,(function(t){return Rl[t]}))}var jl={start:"end",end:"start"};function Al(t){return t.replace(/start|end/g,(function(t){return jl[t]}))}function $l(t){var e=el(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Dl(t){return cl(vl(t)).left+$l(t).scrollLeft}function Pl(t){var e=ml(t);return/auto|scroll|overlay|hidden/.test(e.overflow+e.overflowY+e.overflowX)}function Il(t){return["html","body","#document"].indexOf(tl(t))>=0?t.ownerDocument.body:rl(t)&&Pl(t)?t:Il(gl(t))}function Bl(t,e){var n;void 0===e&&(e=[]);var r=Il(t),i=r===(null==(n=t.ownerDocument)?void 0:n.body),s=el(r),o=i?[s].concat(s.visualViewport||[],Pl(r)?r:[]):r,l=e.concat(o);return i?l:l.concat(Bl(gl(o)))}function Jl(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function Fl(t,e,n){return e===Vo?Jl(function(t,e){var n=el(t),r=vl(t),i=n.visualViewport,s=r.clientWidth,o=r.clientHeight,l=0,u=0;if(i){s=i.width,o=i.height;var h=al();(h||!h&&"fixed"===e)&&(l=i.offsetLeft,u=i.offsetTop)}return{width:s,height:o,x:l+Dl(t),y:u}}(t,n)):nl(e)?function(t,e){var n=cl(t,!1,"fixed"===e);return n.top=n.top+t.clientTop,n.left=n.left+t.clientLeft,n.bottom=n.top+t.clientHeight,n.right=n.left+t.clientWidth,n.width=t.clientWidth,n.height=t.clientHeight,n.x=n.left,n.y=n.top,n}(e,n):Jl(function(t){var e,n=vl(t),r=$l(t),i=null==(e=t.ownerDocument)?void 0:e.body,s=ll(n.scrollWidth,n.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),o=ll(n.scrollHeight,n.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),l=-r.scrollLeft+Dl(t),u=-r.scrollTop;return"rtl"===ml(i||n).direction&&(l+=ll(n.clientWidth,i?i.clientWidth:0)-s),{width:s,height:o,x:l,y:u}}(vl(t)))}function Ll(t){var e,n=t.reference,r=t.element,i=t.placement,s=i?ol(i):null,o=i?Sl(i):null,l=n.x+n.width/2-r.width/2,u=n.y+n.height/2-r.height/2;switch(s){case Fo:e={x:l,y:n.y-r.height};break;case Lo:e={x:l,y:n.y+n.height};break;case qo:e={x:n.x+n.width,y:u};break;case Ho:e={x:n.x-r.width,y:u};break;default:e={x:n.x,y:n.y}}var h=s?kl(s):null;if(null!=h){var f="y"===h?"height":"width";switch(o){case Ko:e[h]=e[h]-(n[f]/2-r[f]/2);break;case zo:e[h]=e[h]+(n[f]/2-r[f]/2)}}return e}function ql(t,e){void 0===e&&(e={});var n=e.placement,r=void 0===n?t.placement:n,i=e.strategy,s=void 0===i?t.strategy:i,o=e.boundary,l=void 0===o?Go:o,u=e.rootBoundary,h=void 0===u?Vo:u,f=e.elementContext,a=void 0===f?_o:f,c=e.altBoundary,d=void 0!==c&&c,p=e.padding,m=void 0===p?0:p,w=Ml("number"!=typeof m?m:Ol(m,Wo)),v=t.rects.popper,g=t.elements[d?a===_o?Qo:_o:a],b=function(t,e,n,r){var i="clippingParents"===e?function(t){var e=Bl(gl(t)),n=["absolute","fixed"].indexOf(ml(t).position)>=0&&rl(t)?yl(t):t;return nl(n)?e.filter((function(t){return nl(t)&&pl(t,n)&&"body"!==tl(t)})):[]}(t):[].concat(e),s=[].concat(i,[n]),o=s.reduce((function(e,n){var i=Fl(t,n,r);return e.top=ll(i.top,e.top),e.right=ul(i.right,e.right),e.bottom=ul(i.bottom,e.bottom),e.left=ll(i.left,e.left),e}),Fl(t,s[0],r));return o.width=o.right-o.left,o.height=o.bottom-o.top,o.x=o.left,o.y=o.top,o}(nl(g)?g:g.contextElement||vl(t.elements.popper),l,h,s),y=cl(t.elements.reference),k=Ll({reference:y,element:v,strategy:"absolute",placement:r}),x=Jl(Object.assign({},v,k)),M=a===_o?x:y,O={top:b.top-M.top+w.top,bottom:M.bottom-b.bottom+w.bottom,left:b.left-M.left+w.left,right:M.right-b.right+w.right},S=t.modifiersData.offset;if(a===_o&&S){var E=S[r];Object.keys(O).forEach((function(t){var e=[qo,Lo].indexOf(t)>=0?1:-1,n=[Fo,Lo].indexOf(t)>=0?"y":"x";O[t]+=E[n]*e}))}return O}function Hl(t,e){void 0===e&&(e={});var n=e.boundary,r=e.rootBoundary,i=e.padding,s=e.flipVariations,o=e.allowedAutoPlacements,l=void 0===o?Xo:o,u=Sl(e.placement),h=u?s?Yo:Yo.filter((function(t){return Sl(t)===u})):Wo,f=h.filter((function(t){return l.indexOf(t)>=0}));0===f.length&&(f=h);var a=f.reduce((function(e,s){return e[s]=ql(t,{placement:s,boundary:n,rootBoundary:r,padding:i})[ol(s)],e}),{});return Object.keys(a).sort((function(t,e){return a[t]-a[e]}))}const Ul={name:"flip",enabled:!0,phase:"main",fn:function(t){var e=t.state,n=t.options,r=t.name;if(!e.modifiersData[r]._skip){for(var i=n.mainAxis,s=void 0===i||i,o=n.altAxis,l=void 0===o||o,u=n.fallbackPlacements,h=n.padding,f=n.boundary,a=n.rootBoundary,c=n.altBoundary,d=n.flipVariations,p=void 0===d||d,m=n.allowedAutoPlacements,w=e.options.placement,v=ol(w),g=u||(v!==w&&p?function(t){if(ol(t)===Uo)return[];var e=Tl(t);return[Al(t),e,Al(e)]}(w):[Tl(w)]),b=[w].concat(g).reduce((function(t,n){return t.concat(ol(n)===Uo?Hl(e,{placement:n,boundary:f,rootBoundary:a,padding:h,flipVariations:p,allowedAutoPlacements:m}):n)}),[]),y=e.rects.reference,k=e.rects.popper,x=new Map,M=!0,O=b[0],S=0;S<b.length;S++){var E=b[S],N=ol(E),C=Sl(E)===Ko,R=[Fo,Lo].indexOf(N)>=0,T=R?"width":"height",j=ql(e,{placement:E,boundary:f,rootBoundary:a,altBoundary:c,padding:h}),A=R?C?qo:Ho:C?Lo:Fo;y[T]>k[T]&&(A=Tl(A));var $=Tl(A),D=[];if(s&&D.push(j[N]<=0),l&&D.push(j[A]<=0,j[$]<=0),D.every((function(t){return t}))){O=E,M=!1;break}x.set(E,D)}if(M)for(var P=function(t){var e=b.find((function(e){var n=x.get(e);if(n)return n.slice(0,t).every((function(t){return t}))}));if(e)return O=e,"break"},I=p?3:1;I>0&&"break"!==P(I);I--);e.placement!==O&&(e.modifiersData[r]._skip=!0,e.placement=O,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Wl(t,e,n){return void 0===n&&(n={x:0,y:0}),{top:t.top-e.height-n.y,right:t.right-e.width+n.x,bottom:t.bottom-e.height+n.y,left:t.left-e.width-n.x}}function Kl(t){return[Fo,qo,Lo,Ho].some((function(e){return t[e]>=0}))}function zl(t,e,n){void 0===n&&(n=!1);var r,i,s=rl(e),o=rl(e)&&function(t){var e=t.getBoundingClientRect(),n=hl(e.width)/t.offsetWidth||1,r=hl(e.height)/t.offsetHeight||1;return 1!==n||1!==r}(e),l=vl(e),u=cl(t,o,n),h={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(s||!s&&!n)&&(("body"!==tl(e)||Pl(l))&&(h=(r=e)!==el(r)&&rl(r)?{scrollLeft:(i=r).scrollLeft,scrollTop:i.scrollTop}:$l(r)),rl(e)?((f=cl(e,!0)).x+=e.clientLeft,f.y+=e.clientTop):l&&(f.x=Dl(l))),{x:u.left+h.scrollLeft-f.x,y:u.top+h.scrollTop-f.y,width:u.width,height:u.height}}function Gl(t){var e=new Map,n=new Set,r=[];function i(t){n.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach((function(t){if(!n.has(t)){var r=e.get(t);r&&i(r)}})),r.push(t)}return t.forEach((function(t){e.set(t.name,t)})),t.forEach((function(t){n.has(t.name)||i(t)})),r}var Vl={placement:"bottom",modifiers:[],strategy:"absolute"};function _l(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return!e.some((function(t){return!(t&&"function"==typeof t.getBoundingClientRect)}))}function Ql(t){void 0===t&&(t={});var e=t.defaultModifiers,n=void 0===e?[]:e,r=t.defaultOptions,i=void 0===r?Vl:r;return function(t,e,r){void 0===r&&(r=i);var s,o,l={placement:"bottom",orderedModifiers:[],options:Object.assign({},Vl,i),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},u=[],h=!1,f={state:l,setOptions:function(r){var s="function"==typeof r?r(l.options):r;a(),l.options=Object.assign({},i,l.options,s),l.scrollParents={reference:nl(t)?Bl(t):t.contextElement?Bl(t.contextElement):[],popper:Bl(e)};var o=function(t){var e=Gl(t);return Zo.reduce((function(t,n){return t.concat(e.filter((function(t){return t.phase===n})))}),[])}(function(t){var e=t.reduce((function(t,e){var n=t[e.name];return t[e.name]=n?Object.assign({},n,e,{options:Object.assign({},n.options,e.options),data:Object.assign({},n.data,e.data)}):e,t}),{});return Object.keys(e).map((function(t){return e[t]}))}([].concat(n,l.options.modifiers)));return l.orderedModifiers=o.filter((function(t){return t.enabled})),l.orderedModifiers.forEach((function(t){var e=t.options,n=t.effect;if("function"==typeof n){var r=n({state:l,name:t.name,instance:f,options:void 0===e?{}:e});u.push(r||function(){})}})),f.update()},forceUpdate:function(){if(!h){var t=l.elements,e=t.reference,n=t.popper;if(_l(e,n)){l.rects={reference:zl(e,yl(n),"fixed"===l.options.strategy),popper:dl(n)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach((function(t){return l.modifiersData[t.name]=Object.assign({},t.data)}));for(var r=0;r<l.orderedModifiers.length;r++)if(!0!==l.reset){var i=l.orderedModifiers[r],s=i.fn,o=i.options;"function"==typeof s&&(l=s({state:l,options:void 0===o?{}:o,name:i.name,instance:f})||l)}else l.reset=!1,r=-1}}},update:(s=function(){return new Promise((function(t){f.forceUpdate(),t(l)}))},function(){return o||(o=new Promise((function(t){Promise.resolve().then((function(){o=void 0,t(s())}))}))),o}),destroy:function(){a(),h=!0}};if(!_l(t,e))return f;function a(){u.forEach((function(t){return t()})),u=[]}return f.setOptions(r).then((function(t){!h&&r.onFirstUpdate&&r.onFirstUpdate(t)})),f}}var Yl=Ql({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,n=t.instance,r=t.options,i=r.scroll,s=void 0===i||i,o=r.resize,l=void 0===o||o,u=el(e.elements.popper),h=[].concat(e.scrollParents.reference,e.scrollParents.popper);return s&&h.forEach((function(t){t.addEventListener("scroll",n.update,Cl)})),l&&u.addEventListener("resize",n.update,Cl),function(){s&&h.forEach((function(t){t.removeEventListener("scroll",n.update,Cl)})),l&&u.removeEventListener("resize",n.update,Cl)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state;e.modifiersData[t.name]=Ll({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,n=t.options,r=n.gpuAcceleration,i=void 0===r||r,s=n.adaptive,o=void 0===s||s,l=n.roundOffsets,u=void 0===l||l,h={placement:ol(e.placement),variation:Sl(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:i,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,Nl(Object.assign({},h,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:o,roundOffsets:u})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,Nl(Object.assign({},h,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:u})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}},sl,{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var e=t.state,n=t.name,r=t.options.offset,i=void 0===r?[0,0]:r,s=Xo.reduce((function(t,n){return t[n]=function(t,e,n){var r=ol(t),i=[Ho,Fo].indexOf(r)>=0?-1:1,s="function"==typeof n?n(Object.assign({},e,{placement:t})):n,o=s[0],l=s[1];return o=o||0,l=(l||0)*i,[Ho,qo].indexOf(r)>=0?{x:l,y:o}:{x:o,y:l}}(n,e.rects,i),t}),{}),o=s[e.placement],l=o.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=o.x,e.modifiersData.popperOffsets.y+=l),e.modifiersData[n]=s}},Ul,{name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e=t.state,n=t.options,r=t.name,i=n.mainAxis,s=void 0===i||i,o=n.altAxis,l=void 0!==o&&o,u=n.tether,h=void 0===u||u,f=n.tetherOffset,a=void 0===f?0:f,c=ql(e,{boundary:n.boundary,rootBoundary:n.rootBoundary,padding:n.padding,altBoundary:n.altBoundary}),d=ol(e.placement),p=Sl(e.placement),m=!p,w=kl(d),v="x"===w?"y":"x",g=e.modifiersData.popperOffsets,b=e.rects.reference,y=e.rects.popper,k="function"==typeof a?a(Object.assign({},e.rects,{placement:e.placement})):a,x="number"==typeof k?{mainAxis:k,altAxis:k}:Object.assign({mainAxis:0,altAxis:0},k),M=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,O={x:0,y:0};if(g){if(s){var S,E="y"===w?Fo:Ho,N="y"===w?Lo:qo,C="y"===w?"height":"width",R=g[w],T=R+c[E],j=R-c[N],A=h?-y[C]/2:0,$=p===Ko?b[C]:y[C],D=p===Ko?-y[C]:-b[C],P=e.elements.arrow,I=h&&P?dl(P):{width:0,height:0},B=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},J=B[E],F=B[N],L=xl(0,b[C],I[C]),q=m?b[C]/2-A-L-J-x.mainAxis:$-L-J-x.mainAxis,H=m?-b[C]/2+A+L+F+x.mainAxis:D+L+F+x.mainAxis,U=e.elements.arrow&&yl(e.elements.arrow),W=null!=(S=null==M?void 0:M[w])?S:0,K=R+H-W,z=xl(h?ul(T,R+q-W-(U?"y"===w?U.clientTop||0:U.clientLeft||0:0)):T,R,h?ll(j,K):j);g[w]=z,O[w]=z-R}if(l){var G,V=g[v],_="y"===v?"height":"width",Q=V+c["x"===w?Fo:Ho],Y=V-c["x"===w?Lo:qo],X=-1!==[Fo,Ho].indexOf(d),Z=null!=(G=null==M?void 0:M[v])?G:0,tt=X?Q:V-b[_]-y[_]-Z+x.altAxis,et=X?V+b[_]+y[_]-Z-x.altAxis:Y,nt=h&&X?function(t,e,n){var r=xl(t,e,n);return r>n?n:r}(tt,V,et):xl(h?tt:Q,V,h?et:Y);g[v]=nt,O[v]=nt-V}e.modifiersData[r]=O}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,n=t.state,r=t.name,i=t.options,s=n.elements.arrow,o=n.modifiersData.popperOffsets,l=ol(n.placement),u=kl(l),h=[Ho,qo].indexOf(l)>=0?"height":"width";if(s&&o){var f=function(t,e){return Ml("number"!=typeof(t="function"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:Ol(t,Wo))}(i.padding,n),a=dl(s),c="y"===u?Fo:Ho,d="y"===u?Lo:qo,p=n.rects.reference[h]+n.rects.reference[u]-o[u]-n.rects.popper[h],m=o[u]-n.rects.reference[u],w=yl(s),v=w?"y"===u?w.clientHeight||0:w.clientWidth||0:0,g=v/2-a[h]/2+(p/2-m/2),b=xl(f[c],g,v-a[h]-f[d]);n.modifiersData[r]=((e={})[u]=b,e.centerOffset=b-g,e)}},effect:function(t){var e=t.state,n=t.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=e.elements.popper.querySelector(r)))&&pl(e.elements.popper,r)&&(e.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,n=t.name,r=e.rects.reference,i=e.rects.popper,s=e.modifiersData.preventOverflow,o=ql(e,{elementContext:"reference"}),l=ql(e,{altBoundary:!0}),u=Wl(o,r),h=Wl(l,i,s),f=Kl(u),a=Kl(h);e.modifiersData[n]={referenceClippingOffsets:u,popperEscapeOffsets:h,isReferenceHidden:f,hasPopperEscaped:a},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":f,"data-popper-escaped":a})}}]}),Xl="tippy-content",Zl="tippy-backdrop",tu="tippy-arrow",eu="tippy-svg-arrow",nu={passive:!0,capture:!0},ru=function(){return document.body};function iu(t,e,n){if(Array.isArray(t)){var r=t[e];return null==r?Array.isArray(n)?n[e]:n:r}return t}function su(t,e){var n={}.toString.call(t);return 0===n.indexOf("[object")&&n.indexOf(e+"]")>-1}function ou(t,e){return"function"==typeof t?t.apply(void 0,e):t}function lu(t,e){return 0===e?t:function(r){clearTimeout(n),n=setTimeout((function(){t(r)}),e)};var n}function uu(t){return[].concat(t)}function hu(t,e){-1===t.indexOf(e)&&t.push(e)}function fu(t){return[].slice.call(t)}function au(t){return Object.keys(t).reduce((function(e,n){return void 0!==t[n]&&(e[n]=t[n]),e}),{})}function cu(){return document.createElement("div")}function du(t){return["Element","Fragment"].some((function(e){return su(t,e)}))}function pu(t,e){t.forEach((function(t){t&&(t.style.transitionDuration=e+"ms")}))}function mu(t,e){t.forEach((function(t){t&&t.setAttribute("data-state",e)}))}function wu(t,e,n){var r=e+"EventListener";["transitionend","webkitTransitionEnd"].forEach((function(e){t[r](e,n)}))}function vu(t,e){for(var n=e;n;){var r;if(t.contains(n))return!0;n=null==n.getRootNode||null==(r=n.getRootNode())?void 0:r.host}return!1}var gu={isTouch:!1},bu=0;function yu(){gu.isTouch||(gu.isTouch=!0,window.performance&&document.addEventListener("mousemove",ku))}function ku(){var t=performance.now();t-bu<20&&(gu.isTouch=!1,document.removeEventListener("mousemove",ku)),bu=t}function xu(){var t,e=document.activeElement;(t=e)&&t._tippy&&t._tippy.reference===t&&e.blur&&!e._tippy.state.isVisible&&e.blur()}var Mu=!("undefined"==typeof window||"undefined"==typeof document||!window.msCrypto),Ou=Object.assign({appendTo:ru,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),Su=Object.keys(Ou);function Eu(t){var e=(t.plugins||[]).reduce((function(e,n){var r,i=n.name;return i&&(e[i]=void 0!==t[i]?t[i]:null!=(r=Ou[i])?r:n.defaultValue),e}),{});return Object.assign({},t,e)}function Nu(t,e){var n=Object.assign({},e,{content:ou(e.content,[t])},e.ignoreAttributes?{}:function(t,e){return(e?Object.keys(Eu(Object.assign({},Ou,{plugins:e}))):Su).reduce((function(e,n){var r=(t.getAttribute("data-tippy-"+n)||"").trim();if(!r)return e;if("content"===n)e[n]=r;else try{e[n]=JSON.parse(r)}catch(t){e[n]=r}return e}),{})}(t,e.plugins));return n.aria=Object.assign({},Ou.aria,n.aria),n.aria={expanded:"auto"===n.aria.expanded?e.interactive:n.aria.expanded,content:"auto"===n.aria.content?e.interactive?null:"describedby":n.aria.content},n}var Cu=function(){return"innerHTML"};function Ru(t,e){t[Cu()]=e}function Tu(t){var e=cu();return!0===t?e.className=tu:(e.className=eu,du(t)?e.appendChild(t):Ru(e,t)),e}function ju(t,e){du(e.content)?(Ru(t,""),t.appendChild(e.content)):"function"!=typeof e.content&&(e.allowHTML?Ru(t,e.content):t.textContent=e.content)}function Au(t){var e=t.firstElementChild,n=fu(e.children);return{box:e,content:n.find((function(t){return t.classList.contains(Xl)})),arrow:n.find((function(t){return t.classList.contains(tu)||t.classList.contains(eu)})),backdrop:n.find((function(t){return t.classList.contains(Zl)}))}}function $u(t){var e=cu(),n=cu();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var r=cu();function i(n,r){var i=Au(e),s=i.box,o=i.content,l=i.arrow;r.theme?s.setAttribute("data-theme",r.theme):s.removeAttribute("data-theme"),"string"==typeof r.animation?s.setAttribute("data-animation",r.animation):s.removeAttribute("data-animation"),r.inertia?s.setAttribute("data-inertia",""):s.removeAttribute("data-inertia"),s.style.maxWidth="number"==typeof r.maxWidth?r.maxWidth+"px":r.maxWidth,r.role?s.setAttribute("role",r.role):s.removeAttribute("role"),n.content===r.content&&n.allowHTML===r.allowHTML||ju(o,t.props),r.arrow?l?n.arrow!==r.arrow&&(s.removeChild(l),s.appendChild(Tu(r.arrow))):s.appendChild(Tu(r.arrow)):l&&s.removeChild(l)}return r.className=Xl,r.setAttribute("data-state","hidden"),ju(r,t.props),e.appendChild(n),n.appendChild(r),i(t.props,t.props),{popper:e,onUpdate:i}}$u.$$tippy=!0;var Du=1,Pu=[],Iu=[];function Bu(t,e){var n,r,i,s,o,l,u,h,f=Nu(t,Object.assign({},Ou,Eu(au(e)))),a=!1,c=!1,d=!1,p=!1,m=[],w=lu(G,f.interactiveDebounce),v=Du++,g=(h=f.plugins).filter((function(t,e){return h.indexOf(t)===e})),b={id:v,reference:t,popper:cu(),popperInstance:null,props:f,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:g,clearDelayTimeouts:function(){clearTimeout(n),clearTimeout(r),cancelAnimationFrame(i)},setProps:function(e){if(!b.state.isDestroyed){$("onBeforeUpdate",[b,e]),K();var n=b.props,r=Nu(t,Object.assign({},n,au(e),{ignoreAttributes:!0}));b.props=r,W(),n.interactiveDebounce!==r.interactiveDebounce&&(I(),w=lu(G,r.interactiveDebounce)),n.triggerTarget&&!r.triggerTarget?uu(n.triggerTarget).forEach((function(t){t.removeAttribute("aria-expanded")})):r.triggerTarget&&t.removeAttribute("aria-expanded"),P(),A(),x&&x(n,r),b.popperInstance&&(Y(),Z().forEach((function(t){requestAnimationFrame(t._tippy.popperInstance.forceUpdate)}))),$("onAfterUpdate",[b,e])}},setContent:function(t){b.setProps({content:t})},show:function(){var t=b.state.isVisible,e=b.state.isDestroyed,n=!b.state.isEnabled,r=gu.isTouch&&!b.props.touch,i=iu(b.props.duration,0,Ou.duration);if(!(t||e||n||r||C().hasAttribute("disabled")||($("onShow",[b],!1),!1===b.props.onShow(b)))){if(b.state.isVisible=!0,N()&&(k.style.visibility="visible"),A(),L(),b.state.isMounted||(k.style.transition="none"),N()){var s=T();pu([s.box,s.content],0)}l=function(){var t;if(b.state.isVisible&&!p){if(p=!0,k.style.transition=b.props.moveTransition,N()&&b.props.animation){var e=T(),n=e.box,r=e.content;pu([n,r],i),mu([n,r],"visible")}D(),P(),hu(Iu,b),null==(t=b.popperInstance)||t.forceUpdate(),$("onMount",[b]),b.props.animation&&N()&&function(t){H(t,(function(){b.state.isShown=!0,$("onShown",[b])}))}(i)}},function(){var t,e=b.props.appendTo,n=C();(t=b.props.interactive&&e===ru||"parent"===e?n.parentNode:ou(e,[n])).contains(k)||t.appendChild(k),b.state.isMounted=!0,Y()}()}},hide:function(){var t=!b.state.isVisible,e=b.state.isDestroyed,n=!b.state.isEnabled,r=iu(b.props.duration,1,Ou.duration);if(!(t||e||n)&&($("onHide",[b],!1),!1!==b.props.onHide(b))){if(b.state.isVisible=!1,b.state.isShown=!1,p=!1,a=!1,N()&&(k.style.visibility="hidden"),I(),q(),A(!0),N()){var i=T(),s=i.box,o=i.content;b.props.animation&&(pu([s,o],r),mu([s,o],"hidden"))}D(),P(),b.props.animation?N()&&function(t,e){H(t,(function(){!b.state.isVisible&&k.parentNode&&k.parentNode.contains(k)&&e()}))}(r,b.unmount):b.unmount()}},hideWithInteractivity:function(t){R().addEventListener("mousemove",w),hu(Pu,w),w(t)},enable:function(){b.state.isEnabled=!0},disable:function(){b.hide(),b.state.isEnabled=!1},unmount:function(){b.state.isVisible&&b.hide(),b.state.isMounted&&(X(),Z().forEach((function(t){t._tippy.unmount()})),k.parentNode&&k.parentNode.removeChild(k),Iu=Iu.filter((function(t){return t!==b})),b.state.isMounted=!1,$("onHidden",[b]))},destroy:function(){b.state.isDestroyed||(b.clearDelayTimeouts(),b.unmount(),K(),delete t._tippy,b.state.isDestroyed=!0,$("onDestroy",[b]))}};if(!f.render)return b;var y=f.render(b),k=y.popper,x=y.onUpdate;k.setAttribute("data-tippy-root",""),k.id="tippy-"+b.id,b.popper=k,t._tippy=b,k._tippy=b;var M=g.map((function(t){return t.fn(b)})),O=t.hasAttribute("aria-expanded");return W(),P(),A(),$("onCreate",[b]),f.showOnCreate&&tt(),k.addEventListener("mouseenter",(function(){b.props.interactive&&b.state.isVisible&&b.clearDelayTimeouts()})),k.addEventListener("mouseleave",(function(){b.props.interactive&&b.props.trigger.indexOf("mouseenter")>=0&&R().addEventListener("mousemove",w)})),b;function S(){var t=b.props.touch;return Array.isArray(t)?t:[t,0]}function E(){return"hold"===S()[0]}function N(){var t;return!(null==(t=b.props.render)||!t.$$tippy)}function C(){return u||t}function R(){var t,e,n=C().parentNode;return n&&null!=(e=uu(n)[0])&&null!=(t=e.ownerDocument)&&t.body?e.ownerDocument:document}function T(){return Au(k)}function j(t){return b.state.isMounted&&!b.state.isVisible||gu.isTouch||s&&"focus"===s.type?0:iu(b.props.delay,t?0:1,Ou.delay)}function A(t){void 0===t&&(t=!1),k.style.pointerEvents=b.props.interactive&&!t?"":"none",k.style.zIndex=""+b.props.zIndex}function $(t,e,n){var r;void 0===n&&(n=!0),M.forEach((function(n){n[t]&&n[t].apply(n,e)})),n&&(r=b.props)[t].apply(r,e)}function D(){var e=b.props.aria;if(e.content){var n="aria-"+e.content,r=k.id;uu(b.props.triggerTarget||t).forEach((function(t){var e=t.getAttribute(n);if(b.state.isVisible)t.setAttribute(n,e?e+" "+r:r);else{var i=e&&e.replace(r,"").trim();i?t.setAttribute(n,i):t.removeAttribute(n)}}))}}function P(){!O&&b.props.aria.expanded&&uu(b.props.triggerTarget||t).forEach((function(t){b.props.interactive?t.setAttribute("aria-expanded",b.state.isVisible&&t===C()?"true":"false"):t.removeAttribute("aria-expanded")}))}function I(){R().removeEventListener("mousemove",w),Pu=Pu.filter((function(t){return t!==w}))}function B(e){if(!gu.isTouch||!d&&"mousedown"!==e.type){var n=e.composedPath&&e.composedPath()[0]||e.target;if(!b.props.interactive||!vu(k,n)){if(uu(b.props.triggerTarget||t).some((function(t){return vu(t,n)}))){if(gu.isTouch)return;if(b.state.isVisible&&b.props.trigger.indexOf("click")>=0)return}else $("onClickOutside",[b,e]);!0===b.props.hideOnClick&&(b.clearDelayTimeouts(),b.hide(),c=!0,setTimeout((function(){c=!1})),b.state.isMounted||q())}}}function J(){d=!0}function F(){d=!1}function L(){var t=R();t.addEventListener("mousedown",B,!0),t.addEventListener("touchend",B,nu),t.addEventListener("touchstart",F,nu),t.addEventListener("touchmove",J,nu)}function q(){var t=R();t.removeEventListener("mousedown",B,!0),t.removeEventListener("touchend",B,nu),t.removeEventListener("touchstart",F,nu),t.removeEventListener("touchmove",J,nu)}function H(t,e){var n=T().box;function r(t){t.target===n&&(wu(n,"remove",r),e())}if(0===t)return e();wu(n,"remove",o),wu(n,"add",r),o=r}function U(e,n,r){void 0===r&&(r=!1),uu(b.props.triggerTarget||t).forEach((function(t){t.addEventListener(e,n,r),m.push({node:t,eventType:e,handler:n,options:r})}))}function W(){var t;E()&&(U("touchstart",z,{passive:!0}),U("touchend",V,{passive:!0})),(t=b.props.trigger,t.split(/\s+/).filter(Boolean)).forEach((function(t){if("manual"!==t)switch(U(t,z),t){case"mouseenter":U("mouseleave",V);break;case"focus":U(Mu?"focusout":"blur",_);break;case"focusin":U("focusout",_)}}))}function K(){m.forEach((function(t){t.node.removeEventListener(t.eventType,t.handler,t.options)})),m=[]}function z(t){var e,n=!1;if(b.state.isEnabled&&!Q(t)&&!c){var r="focus"===(null==(e=s)?void 0:e.type);s=t,u=t.currentTarget,P(),!b.state.isVisible&&su(t,"MouseEvent")&&Pu.forEach((function(e){return e(t)})),"click"===t.type&&(b.props.trigger.indexOf("mouseenter")<0||a)&&!1!==b.props.hideOnClick&&b.state.isVisible?n=!0:tt(t),"click"===t.type&&(a=!n),n&&!r&&et(t)}}function G(t){var e=t.target,n=C().contains(e)||k.contains(e);if("mousemove"!==t.type||!n){var r=Z().concat(k).map((function(t){var e,n=null==(e=t._tippy.popperInstance)?void 0:e.state;return n?{popperRect:t.getBoundingClientRect(),popperState:n,props:f}:null})).filter(Boolean);(function(t,e){var n=e.clientX,r=e.clientY;return t.every((function(t){var e=t.popperRect,i=t.popperState,s=t.props.interactiveBorder,o=i.placement.split("-")[0],l=i.modifiersData.offset;return!l||e.top-r+("bottom"===o?l.top.y:0)>s||r-e.bottom-("top"===o?l.bottom.y:0)>s||e.left-n+("right"===o?l.left.x:0)>s||n-e.right-("left"===o?l.right.x:0)>s}))})(r,t)&&(I(),et(t))}}function V(t){Q(t)||b.props.trigger.indexOf("click")>=0&&a||(b.props.interactive?b.hideWithInteractivity(t):et(t))}function _(t){b.props.trigger.indexOf("focusin")<0&&t.target!==C()||b.props.interactive&&t.relatedTarget&&k.contains(t.relatedTarget)||et(t)}function Q(t){return!!gu.isTouch&&E()!==t.type.indexOf("touch")>=0}function Y(){X();var e=b.props,n=e.popperOptions,r=e.placement,i=e.offset,s=e.getReferenceClientRect,o=e.moveTransition,u=N()?Au(k).arrow:null,h=s?{getBoundingClientRect:s,contextElement:s.contextElement||C()}:t,f=[{name:"offset",options:{offset:i}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!o}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(t){var e=t.state;if(N()){var n=T().box;["placement","reference-hidden","escaped"].forEach((function(t){"placement"===t?n.setAttribute("data-placement",e.placement):e.attributes.popper["data-popper-"+t]?n.setAttribute("data-"+t,""):n.removeAttribute("data-"+t)})),e.attributes.popper={}}}}];N()&&u&&f.push({name:"arrow",options:{element:u,padding:3}}),f.push.apply(f,(null==n?void 0:n.modifiers)||[]),b.popperInstance=Yl(h,k,Object.assign({},n,{placement:r,onFirstUpdate:l,modifiers:f}))}function X(){b.popperInstance&&(b.popperInstance.destroy(),b.popperInstance=null)}function Z(){return fu(k.querySelectorAll("[data-tippy-root]"))}function tt(t){b.clearDelayTimeouts(),t&&$("onTrigger",[b,t]),L();var e=j(!0),r=S(),i=r[1];gu.isTouch&&"hold"===r[0]&&i&&(e=i),e?n=setTimeout((function(){b.show()}),e):b.show()}function et(t){if(b.clearDelayTimeouts(),$("onUntrigger",[b,t]),b.state.isVisible){if(!(b.props.trigger.indexOf("mouseenter")>=0&&b.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(t.type)>=0&&a)){var e=j(!1);e?r=setTimeout((function(){b.state.isVisible&&b.hide()}),e):i=requestAnimationFrame((function(){b.hide()}))}}else q()}}function Ju(t,e){void 0===e&&(e={});var n=Ou.plugins.concat(e.plugins||[]);document.addEventListener("touchstart",yu,nu),window.addEventListener("blur",xu);var r,i=Object.assign({},e,{plugins:n}),s=(r=t,du(r)?[r]:function(t){return su(t,"NodeList")}(r)?fu(r):Array.isArray(r)?r:fu(document.querySelectorAll(r))).reduce((function(t,e){var n=e&&Bu(e,i);return n&&t.push(n),t}),[]);return du(t)?s[0]:s}Ju.defaultProps=Ou,Ju.setDefaultProps=function(t){Object.keys(t).forEach((function(e){Ou[e]=t[e]}))},Ju.currentInput=gu,Object.assign({},sl,{effect:function(t){var e=t.state,n={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(e.elements.popper.style,n.popper),e.styles=n,e.elements.arrow&&Object.assign(e.elements.arrow.style,n.arrow)}}),Ju.setDefaultProps({render:$u});export{at as A,jo as B,Ys as C,Qr as D,Ps as E,r as F,K as G,l as H,uo as I,G as J,No as K,Vt as L,Po as M,Io as N,Kt as O,ce as P,oo as Q,Jo as R,Gt as S,Yt as T,me as a,wo as b,Ro as c,To as d,Bo as e,$o as f,no as g,Ao as h,Ws as i,io as j,Gr as k,ro as l,ws as m,fo as n,so as o,vo as p,ao as q,ho as r,jt as s,Ju as t,h as u,Zt as v,Do as w,Di as x,bs as y,cs as z}