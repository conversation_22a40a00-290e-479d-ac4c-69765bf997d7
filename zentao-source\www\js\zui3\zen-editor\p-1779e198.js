import{conf as e,language as t}from"./p-468cbb6a.js";import"./p-aa688caf.js";import"./p-7900c24a.js";import"./p-986e5fe7.js";
/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.45.0(5e5af013f8d295555a7210df0d5f2cea0bf5dd56)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/var s=e,i={defaultToken:"invalid",tokenPostfix:".js",keywords:["break","case","catch","class","continue","const","constructor","debugger","default","delete","do","else","export","extends","false","finally","for","from","function","get","if","import","in","instanceof","let","new","null","return","set","static","super","switch","symbol","this","throw","true","try","typeof","undefined","var","void","while","with","yield","async","await","of"],typeKeywords:[],operators:t.operators,symbols:t.symbols,escapes:t.escapes,digits:t.digits,octaldigits:t.octaldigits,binarydigits:t.binarydigits,hexdigits:t.hexdigits,regexpctl:t.regexpctl,regexpesc:t.regexpesc,tokenizer:t.tokenizer};export{s as conf,i as language}