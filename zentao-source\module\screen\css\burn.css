.header {display:flex; justify-content:center; }
.img-header {width:1342px; height: 129px; background-image:url("static/images/screen_header.png"); background-repeat:no-repeat;}
.title {padding-top:39px; text-align: center; white-space:nowrap; color:#B2D5E5; font-family: "Helvetica Neue",Helvetica,Tahoma,Arial,'PingFang SC','Source Han Sans CN','Source Han <PERSON>','Source <PERSON>','Hiragino Sans GB','WenQuanYi Micro Hei','Microsoft YaHei',sans-serif; letter-spacing:5px;}
.time {position:absolute; right:10px; top:30px; color:#eee}

body {background:rgb(0,16,40);}
.content {display: flex; justify-content: center;}
.burn {width: 1300px; display: flex; flex-wrap: wrap; justify-content: space-around; padding: 20px 0;}
.burn > .container {flex: 0 0 33%; margin-top: 20px; height: 370px; width: 370px; background-image:url("static/images/burn_bg.png"); background-repeat:no-repeat; background-size: calc(100% - 14px) calc(100%); left: 7px;}
.burn > .container canvas{max-width: calc(100% - 15px); max-height: calc(100% - 15px);}

.content .table-empty-tip {background: transparent;}
