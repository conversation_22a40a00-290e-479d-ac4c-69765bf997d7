title: zt_project
author: <PERSON><PERSON><PERSON>
version: "1.0"
fields:
- field: id
  range: 1-7
- field: type
  range: "project{2},sprint{2},stage{3}"
- field: name
  range: "项目1,项目2,迭代1,迭代2,阶段1,阶段2,阶段3"
- field: project
  range: "0{2},1{2},2{3}"
- field: model
  range: "scrum,waterfall,[]{5}"
- field: status
  range: "suspended{3},doing{2},suspended{2}"
- field: path
  range: "1,2,`1,3`,`1,4`,`2,5`, `2,6`,`2,7`"
  prefix: ','
  postfix: ','
- field: deleted
  range: "0{6},1"
