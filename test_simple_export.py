#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试Excel导出功能（不需要登录）
"""

import requests
import time

def test_simple_export():
    """简单测试Excel导出"""
    print("📤 简单测试Excel导出功能...")
    
    try:
        # 直接测试Excel导出API
        print("📋 直接测试Excel导出...")
        export_response = requests.get(
            "http://localhost:8000/api/v1/new-supervision/export-excel",
            timeout=30
        )
        
        print(f"导出API状态码: {export_response.status_code}")
        
        if export_response.status_code == 200:
            content_length = len(export_response.content)
            print(f"Content-Length: {content_length} bytes")
            
            if content_length > 100:
                print("✅ Excel导出功能正常")
                
                # 保存文件
                with open('simple_export_test.xlsx', 'wb') as f:
                    f.write(export_response.content)
                print("✅ Excel文件已保存为 simple_export_test.xlsx")
                
                return True
            else:
                print("❌ Excel文件内容太小")
                print(f"文件内容: {export_response.content}")
                return False
        elif export_response.status_code == 401:
            print("❌ 需要登录认证")
            return False
        else:
            print(f"❌ Excel导出失败: {export_response.status_code}")
            print(f"错误响应: {export_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

def main():
    print("🚀 简单Excel导出测试")
    print("=" * 40)
    
    time.sleep(1)
    success = test_simple_export()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 测试通过！")
    else:
        print("❌ 测试失败！")

if __name__ == "__main__":
    main()
