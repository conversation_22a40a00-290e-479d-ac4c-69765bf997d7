<?php
/**
 * The project build module en file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     projectrelease
 * @version     $Id: zh-cn.php 4129 2020-11-27 01:58:14Z wwccss $
 * @link        https://www.zentao.net
 */
$lang->projectbuild->common           = $lang->projectCommon . ' Build';
$lang->projectbuild->browse           = 'Build List';
$lang->projectbuild->create           = "Create Build";
$lang->projectbuild->edit             = "Edit Build";
$lang->projectbuild->delete           = "Delete Build";
$lang->projectbuild->view             = "Build Detail";
$lang->projectbuild->linkStory        = "Link {$lang->SRCommon}";
$lang->projectbuild->linkBug          = "Link Bug";
$lang->projectbuild->unlinkStory      = "Unlink {$lang->SRCommon}";
$lang->projectbuild->unlinkBug        = "Unlink Bug";
$lang->projectbuild->batchUnlink      = 'Batch Unlink';
$lang->projectbuild->batchUnlinkStory = "Batch Unlink {$lang->SRCommon}";
$lang->projectbuild->batchUnlinkBug   = 'Batch Unlink Bug';
$lang->projectbuild->name             = 'Name';
$lang->projectbuild->execution        = $lang->executionCommon;
$lang->projectbuild->product          = $lang->productCommon;
$lang->projectbuild->scmPath          = 'SCM Path';
$lang->projectbuild->filePath         = 'File Path';
$lang->projectbuild->desc             = 'Description';
$lang->projectbuild->systemName       = 'Belong' . $lang->product->system;
$lang->projectbuild->system           = 'Belong' . $lang->product->system;
