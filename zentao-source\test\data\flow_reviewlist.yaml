title: table zt_flow_reviewlist
desc: ""
author: automated export
version: "1.0"
fields:
  - field: id
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: title
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: object
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: category
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: parent
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedTo
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: subStatus
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdDate
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedBy
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: editedDate
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedBy
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedDate
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: ""
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
