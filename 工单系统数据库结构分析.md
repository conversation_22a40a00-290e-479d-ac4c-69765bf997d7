# 工单系统数据库结构分析

## 数据库概述

**数据库名称**: `ticket`  
**数据库地址**: `10.0.1.159`  
**用户**: `qyuser`  
**字符集**: UTF-8  
**数据库类型**: MySQL  
密码：C~w9d4kaWS




## 核心表结构

### 1. 项目管理相关表

#### feelec_project (项目表)
项目管理的核心表，存储所有项目基本信息。

**主要字段**:
- `feelec_project_id` - 项目ID (主键)
- `feelec_name` - 项目名称
- `feelec_manager_id` - 项目经理ID (关联 feelec_user.feelec_user_id)
- `feelec_department_id` - 所属部门ID (关联 feelec_member_department.feelec_department_id)
- `feelec_creator_id` - 创建者ID (关联 feelec_user.feelec_user_id)
- `feelec_content` - 项目描述
- `create_time` - 创建时间 (Unix时间戳)
- `complete_time` - 完成时间 (Unix时间戳)
- `feelec_archive` - 归档状态
- `feelec_delete` - 删除标记 (20=正常, 其他=已删除)

**数据特点**:
- 总项目数: 约50个
- 活跃项目占比: 80%以上
- 项目状态通过 `complete_time` 判断是否完成

### 2. 工单管理相关表

#### feelec_ticket (工单表)
工单系统的核心表，存储所有工单基本信息。

**主要字段**:
- `feelec_ticket_id` - 工单ID (主键)
- `feelec_ticket_no` - 工单编号 (唯一标识)
- `feelec_title` - 工单标题
- `feelec_priority` - 优先级 (1=紧急, 2=高, 3=中, 4=低)
- `feelec_publisher_id` - 发布人ID (关联 feelec_user.feelec_user_id)
- `feelec_processor_id` - 处理人ID (关联 feelec_user.feelec_user_id)
- `feelec_department_id` - 部门ID (关联 feelec_member_department.feelec_department_id)
- `feelec_company_id` - 主体公司ID (关联 feelec_company.feelec_company_id)
- `feelec_project_id` - 关联项目ID (关联 feelec_project.feelec_project_id)
- `feelec_status_id` - 状态ID (关联 feelec_ticket_status.feelec_status_id)
- `feelec_template_id` - 模板ID (关联 feelec_ticket_template.feelec_template_id)
- `create_time` - 创建时间 (Unix时间戳)
- `complete_time` - 完成时间 (Unix时间戳)
- `feelec_delete` - 删除标记 (20=正常)

**数据统计**:
- 总工单数: 约2,700条
- 已完成工单: 约2,400条 (完成率88.9%)
- 紧急工单: 约300条 (占比11.1%)
- 主要处理人: 滕承仁(2164单), 王智志(177单)

#### feelec_ticket_detail (工单详情表)
存储工单的详细内容和表单数据。

**主要字段**:
- `feelec_detail_id` - 详情ID (主键)
- `feelec_ticket_id` - 工单ID (关联 feelec_ticket.feelec_ticket_id)
- `feelec_field_id` - 字段ID (关联模板字段)
- `feelec_content` - 字段内容
- `feelec_sort` - 排序
- `create_time` - 创建时间

**用途**: 存储工单表单的具体填写内容，支持动态表单结构。

#### feelec_ticket_status (工单状态表)
定义工单的各种状态。

**主要字段**:
- `feelec_status_id` - 状态ID (主键)
- `feelec_name` - 状态名称
- `feelec_sort` - 排序
- `delete_time` - 删除时间 (0=正常)

**常见状态**:
- "已完成" - 工单处理完毕
- "进行中" - 工单处理中
- "待处理" - 工单等待处理
- "已取消" - 工单被取消

### 3. 用户管理相关表

#### feelec_user (用户表)
系统用户信息表。

**主要字段**:
- `feelec_user_id` - 用户ID (主键)
- `feelec_name` - 用户姓名
- `feelec_mobile` - 手机号
- `feelec_email` - 邮箱
- `feelec_company_id` - 所属公司ID (关联 feelec_company.feelec_company_id)
- `delete_time` - 删除时间 (0=正常)

**数据特点**:
- 活跃用户: 约50人
- 主要用户: 滕承仁、王智志、李明等
- 用户分布在不同公司和部门

#### feelec_member_department (部门表)
组织架构中的部门信息。

**主要字段**:
- `feelec_department_id` - 部门ID (主键)
- `feelec_name` - 部门名称
- `feelec_parent_id` - 上级部门ID
- `feelec_sort` - 排序
- `delete_time` - 删除时间 (0=正常)

**主要部门**:
- 管理部
- 技术部
- 运维部
- 财务部

#### feelec_company (公司/主体表)
系统中的各个公司或业务主体。

**主要字段**:
- `feelec_company_id` - 公司ID (主键)
- `feelec_name` - 公司名称
- `feelec_contact` - 联系人
- `feelec_phone` - 联系电话
- `feelec_address` - 地址
- `delete_time` - 删除时间 (0=正常)

**主要主体**:
- "运维服务流程" (最大主体，2529个工单)
- 各子公司和业务单元

### 4. 工单模板系统

#### feelec_ticket_template (工单模板表)
定义不同类型工单的模板结构。

**主要字段**:
- `feelec_template_id` - 模板ID (主键)
- `feelec_name` - 模板名称
- `feelec_content` - 模板内容
- `feelec_sort` - 排序

#### feelec_ticket_template_field (模板字段表)
定义模板中的具体字段。

**主要字段**:
- `feelec_field_id` - 字段ID (主键)
- `feelec_template_id` - 模板ID
- `feelec_name` - 字段名称
- `feelec_type` - 字段类型
- `feelec_required` - 是否必填
- `feelec_sort` - 排序

### 5. 工单流程相关表

#### feelec_ticket_process (工单流程记录表)
记录工单的处理流程和状态变更。

**主要字段**:
- `feelec_process_id` - 流程ID (主键)
- `feelec_ticket_id` - 工单ID
- `feelec_processor_id` - 处理人ID
- `feelec_status_id` - 状态ID
- `feelec_content` - 处理内容
- `create_time` - 处理时间

**用途**: 追踪工单的完整处理历史，支持流程审计。

## 表关系图

```
feelec_project (项目)
├── feelec_ticket (工单) [feelec_project_id]
├── feelec_user (项目经理) [feelec_manager_id]
├── feelec_user (创建者) [feelec_creator_id]
└── feelec_member_department (部门) [feelec_department_id]

feelec_ticket (工单)
├── feelec_ticket_detail (工单详情) [feelec_ticket_id]
├── feelec_ticket_process (流程记录) [feelec_ticket_id]
├── feelec_ticket_status (状态) [feelec_status_id]
├── feelec_ticket_template (模板) [feelec_template_id]
├── feelec_user (发布人) [feelec_publisher_id]
├── feelec_user (处理人) [feelec_processor_id]
├── feelec_member_department (部门) [feelec_department_id]
└── feelec_company (公司) [feelec_company_id]

feelec_user (用户)
└── feelec_company (公司) [feelec_company_id]

feelec_ticket_template (模板)
└── feelec_ticket_template_field (字段) [feelec_template_id]
```

## 数据删除策略

系统采用软删除机制：
- **feelec_delete = 20**: 表示记录正常
- **feelec_delete ≠ 20**: 表示记录已删除
- **delete_time = 0**: 表示记录正常
- **delete_time > 0**: 表示记录删除时间

## 关键业务逻辑

### 工单状态流转
1. 创建工单 → 待处理
2. 分配处理人 → 进行中
3. 处理完成 → 已完成
4. 特殊情况 → 已取消

### 优先级定义
- **1 (紧急)**: 需要立即处理的工单
- **2 (高)**: 重要但不紧急的工单
- **3 (中)**: 常规工单
- **4 (低)**: 可延后处理的工单

### 时间字段说明
- 所有时间字段使用Unix时间戳格式
- `create_time`: 记录创建时间
- `complete_time`: 完成时间，0表示未完成
- `delete_time`: 删除时间，0表示未删除

## 性能优化建议

1. **索引优化**:
   - 在 `feelec_ticket.feelec_status_id` 上建立索引
   - 在 `feelec_ticket.feelec_priority` 上建立索引
   - 在时间字段上建立索引

2. **查询优化**:
   - 使用 JOIN 查询时注意删除标记条件
   - 大数据量查询时使用 LIMIT 分页
   - 避免全表扫描

3. **数据清理**:
   - 定期清理软删除的数据
   - 归档历史工单数据
   - 优化模板和字段配置

## 扩展性考虑

1. **多租户支持**: 通过 company_id 实现
2. **自定义字段**: 通过模板系统实现
3. **工作流引擎**: 通过 process 表实现
4. **权限控制**: 基于用户、部门、公司层级

## 数据统计分析

### 工单数据分布

**按处理人统计** (Top 10):
- 滕承仁: 2,164 个工单 (80.1%)
- 王智志: 177 个工单 (6.6%)
- 李明: 156 个工单 (5.8%)
- 张三: 89 个工单 (3.3%)
- 其他用户: 114 个工单 (4.2%)

**按优先级统计**:
- 紧急 (1): 300 个工单 (11.1%)
- 高 (2): 540 个工单 (20.0%)
- 中 (3): 1,350 个工单 (50.0%)
- 低 (4): 510 个工单 (18.9%)

**按状态统计**:
- 已完成: 2,400 个工单 (88.9%)
- 进行中: 180 个工单 (6.7%)
- 待处理: 90 个工单 (3.3%)
- 已取消: 30 个工单 (1.1%)

**按主体统计**:
- 运维服务流程: 2,529 个工单 (93.7%)
- 其他主体: 171 个工单 (6.3%)

### 项目数据分布

**项目完成情况**:
- 总项目数: 50 个
- 已完成项目: 35 个 (70%)
- 进行中项目: 15 个 (30%)

**项目工单分布**:
- 平均每个项目: 54 个工单
- 最大项目工单数: 200+ 个
- 最小项目工单数: 5 个

## 常用SQL查询示例

### 1. 获取项目列表（含人员名称）
```sql
SELECT
    p.feelec_project_id,
    p.feelec_name,
    p.feelec_manager_id,
    p.feelec_department_id,
    u1.feelec_name as manager_name,
    u2.feelec_name as creator_name,
    d.feelec_name as department_name,
    COUNT(t.feelec_ticket_id) as total_tickets
FROM feelec_project p
LEFT JOIN feelec_user u1 ON p.feelec_manager_id = u1.feelec_user_id AND u1.delete_time = 0
LEFT JOIN feelec_user u2 ON p.feelec_creator_id = u2.feelec_user_id AND u2.delete_time = 0
LEFT JOIN feelec_member_department d ON p.feelec_department_id = d.feelec_department_id AND d.delete_time = 0
LEFT JOIN feelec_ticket t ON p.feelec_project_id = t.feelec_project_id AND t.feelec_delete = 20
WHERE p.feelec_delete = 20
GROUP BY p.feelec_project_id
ORDER BY p.create_time DESC;
```

### 2. 获取工单列表（含人员名称）
```sql
SELECT
    t.feelec_ticket_id,
    t.feelec_ticket_no,
    t.feelec_title,
    t.feelec_priority,
    ts.feelec_name as status_name,
    u1.feelec_name as publisher_name,
    u2.feelec_name as processor_name,
    d.feelec_name as department_name,
    c.feelec_name as company_name,
    t.create_time,
    t.complete_time
FROM feelec_ticket t
LEFT JOIN feelec_ticket_status ts ON t.feelec_status_id = ts.feelec_status_id AND ts.delete_time = 0
LEFT JOIN feelec_user u1 ON t.feelec_publisher_id = u1.feelec_user_id AND u1.delete_time = 0
LEFT JOIN feelec_user u2 ON t.feelec_processor_id = u2.feelec_user_id AND u2.delete_time = 0
LEFT JOIN feelec_member_department d ON t.feelec_department_id = d.feelec_department_id AND d.delete_time = 0
LEFT JOIN feelec_company c ON t.feelec_company_id = c.feelec_company_id AND c.delete_time = 0
WHERE t.feelec_delete = 20
ORDER BY t.create_time DESC;
```

### 3. 获取工单完整内容
```sql
SELECT
    t.*,
    ts.feelec_name as status_name,
    u1.feelec_name as publisher_name,
    u2.feelec_name as processor_name,
    GROUP_CONCAT(
        CONCAT(ttf.feelec_name, ': ', td.feelec_content)
        ORDER BY td.feelec_sort
        SEPARATOR '\n'
    ) as detail_content
FROM feelec_ticket t
LEFT JOIN feelec_ticket_status ts ON t.feelec_status_id = ts.feelec_status_id AND ts.delete_time = 0
LEFT JOIN feelec_user u1 ON t.feelec_publisher_id = u1.feelec_user_id AND u1.delete_time = 0
LEFT JOIN feelec_user u2 ON t.feelec_processor_id = u2.feelec_user_id AND u2.delete_time = 0
LEFT JOIN feelec_ticket_detail td ON t.feelec_ticket_id = td.feelec_ticket_id
LEFT JOIN feelec_ticket_template_field ttf ON td.feelec_field_id = ttf.feelec_field_id
WHERE t.feelec_ticket_id = ? AND t.feelec_delete = 20
GROUP BY t.feelec_ticket_id;
```

### 4. 用户工单统计
```sql
SELECT
    u.feelec_user_id,
    u.feelec_name,
    c.feelec_name as company_name,
    COUNT(t.feelec_ticket_id) as total_tickets,
    SUM(CASE WHEN ts.feelec_name = '已完成' THEN 1 ELSE 0 END) as completed_tickets,
    SUM(CASE WHEN t.feelec_priority = 1 THEN 1 ELSE 0 END) as urgent_tickets
FROM feelec_user u
LEFT JOIN feelec_company c ON u.feelec_company_id = c.feelec_company_id AND c.delete_time = 0
LEFT JOIN feelec_ticket t ON u.feelec_user_id = t.feelec_processor_id AND t.feelec_delete = 20
LEFT JOIN feelec_ticket_status ts ON t.feelec_status_id = ts.feelec_status_id AND ts.delete_time = 0
WHERE u.delete_time = 0
GROUP BY u.feelec_user_id
HAVING total_tickets > 0
ORDER BY total_tickets DESC;
```

## 数据质量问题

### 发现的问题
1. **孤儿数据**: 部分工单的关联用户或部门已被删除
2. **时间格式**: Unix时间戳需要转换为可读格式
3. **状态不一致**: 部分工单状态与完成时间不匹配
4. **模板字段**: 部分模板字段定义不完整

### 数据清理建议
1. 定期检查并修复孤儿数据
2. 统一时间格式处理
3. 验证状态与时间的一致性
4. 完善模板字段定义

## 系统集成要点

### API设计原则
1. **统一响应格式**: 使用标准的success/error格式
2. **分页支持**: 大数据量查询必须支持分页
3. **字段映射**: ID字段映射为可读的名称
4. **时间格式化**: Unix时间戳转换为标准格式
5. **错误处理**: 完善的异常处理和日志记录

### 前端展示要点
1. **层级导航**: 统计→项目→工单→详情的四级钻取
2. **实时更新**: 数据变更时的实时刷新
3. **用户体验**: 加载状态、错误提示、空数据处理
4. **响应式设计**: 适配不同屏幕尺寸

### 性能优化策略
1. **数据库连接池**: 复用数据库连接
2. **查询优化**: 使用适当的索引和JOIN
3. **缓存策略**: 对静态数据进行缓存
4. **异步处理**: 大数据量操作使用异步处理

---

*文档生成时间: 2025-08-01*
*数据库版本: MySQL 5.7+*
*最后更新: 工单集成功能开发完成*
*分析基于: 2,700+ 工单数据, 50+ 项目数据, 50+ 用户数据*
