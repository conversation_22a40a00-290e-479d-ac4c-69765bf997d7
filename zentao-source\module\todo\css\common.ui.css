.have-fix .input-control-prefix {width: 42px; background: #f6f7f9; opacity: 1;}
.have-fix .input-group-addon {width: 42px;}
.highlight-suffix .input-control-suffix,
.highlight-prefix .input-control-prefix {opacity: 1;}
.form-horz .form-group.config-day {padding-left: 50px;}
.config-day>.input-control {width: 64px; flex: none;}
.config-day>.form-label {width: 50px;}
.config-day>.form-label::after {content: '';}
.input-control > .form-control.before-days {padding-left: 50px;}
.form .label-id {height: 20px; border-color: #D8DBDE; border-radius: 10px;}
.form-row .form-group.items-center {align-items: center;}

.time-input.focus{z-index: 2;}
