<?php
$views     = array();
$resources = array();
$views['max']['rnd'] = 'a:19:{i:0;s:2:"my";i:1;s:7:"program";i:2;s:7:"product";i:3;s:7:"project";i:4;s:9:"execution";i:5;s:2:"qa";i:6;s:6:"devops";i:7;s:6:"kanban";i:8;s:3:"doc";i:9;s:2:"bi";i:10;s:6:"system";i:11;s:5:"admin";i:12;s:9:"menuOrder";i:13;s:8:"feedback";i:14;s:8:"workflow";i:15;s:2:"oa";i:16;s:3:"ops";i:17;s:11:"traincourse";i:18;s:8:"assetlib";}';
$resources['max']['rnd'] = 'a:199:{s:5:"index";a:1:{s:5:"index";s:5:"index";}s:2:"my";a:22:{s:5:"index";s:11:"indexAction";s:4:"todo";s:10:"todoAction";s:4:"work";s:10:"workAction";s:10:"contribute";s:16:"contributeAction";s:7:"project";s:7:"project";s:7:"profile";s:13:"profileAction";s:12:"uploadAvatar";s:12:"uploadAvatar";s:10:"preference";s:10:"preference";s:7:"dynamic";s:13:"dynamicAction";s:11:"editProfile";s:11:"editProfile";s:14:"changePassword";s:14:"changePassword";s:14:"manageContacts";s:14:"manageContacts";s:14:"deleteContacts";s:14:"deleteContacts";s:5:"score";s:5:"score";s:4:"team";s:4:"team";s:9:"execution";s:9:"execution";s:3:"doc";s:3:"doc";s:5:"audit";s:5:"audit";s:7:"meeting";s:7:"meeting";s:8:"calendar";s:14:"calendarAction";s:6:"effort";s:12:"effortAction";s:6:"review";s:6:"review";}s:4:"todo";a:17:{s:6:"create";s:6:"create";s:11:"createcycle";s:11:"createCycle";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:6:"export";s:6:"export";s:5:"start";s:5:"start";s:6:"finish";s:6:"finish";s:11:"batchFinish";s:11:"batchFinish";s:12:"import2Today";s:12:"import2Today";s:8:"assignTo";s:12:"assignAction";s:8:"activate";s:8:"activate";s:5:"close";s:5:"close";s:10:"batchClose";s:10:"batchClose";s:8:"calendar";s:8:"calendar";}s:7:"program";a:18:{s:6:"browse";s:6:"browse";s:6:"kanban";s:12:"kanbanAction";s:4:"view";s:4:"view";s:7:"product";s:7:"product";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:5:"start";s:5:"start";s:7:"suspend";s:7:"suspend";s:8:"activate";s:8:"activate";s:5:"close";s:5:"close";s:6:"delete";s:6:"delete";s:7:"project";s:7:"project";s:11:"stakeholder";s:11:"stakeholder";s:17:"createStakeholder";s:17:"createStakeholder";s:17:"unlinkStakeholder";s:17:"unlinkStakeholder";s:23:"batchUnlinkStakeholders";s:23:"batchUnlinkStakeholders";s:15:"unbindWhitelist";s:15:"unbindWhitelist";s:11:"updateOrder";s:11:"updateOrder";}s:9:"personnel";a:4:{s:10:"accessible";s:10:"accessible";s:6:"invest";s:6:"invest";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";}s:7:"product";a:22:{s:5:"index";s:11:"indexAction";s:6:"browse";s:6:"browse";s:11:"requirement";s:11:"requirement";s:6:"create";s:6:"create";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:6:"delete";s:6:"delete";s:7:"roadmap";s:7:"roadmap";s:5:"track";s:5:"track";s:7:"dynamic";s:7:"dynamic";s:7:"project";s:7:"project";s:9:"dashboard";s:9:"dashboard";s:5:"close";s:11:"closeAction";s:11:"updateOrder";s:11:"orderAction";s:3:"all";s:4:"list";s:6:"kanban";s:6:"kanban";s:10:"manageLine";s:10:"manageLine";s:6:"export";s:12:"exportAction";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";s:15:"unbindWhitelist";s:15:"unbindWhitelist";}s:5:"story";a:35:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:10:"editAction";s:6:"export";s:12:"exportAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:6:"change";s:12:"changeAction";s:6:"review";s:12:"reviewAction";s:12:"submitReview";s:12:"submitReview";s:11:"batchReview";s:11:"batchReview";s:6:"recall";s:12:"recallAction";s:5:"close";s:11:"closeAction";s:10:"batchClose";s:10:"batchClose";s:15:"batchChangePlan";s:15:"batchChangePlan";s:16:"batchChangeStage";s:16:"batchChangeStage";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:8:"activate";s:14:"activateAction";s:5:"tasks";s:5:"tasks";s:4:"bugs";s:4:"bugs";s:5:"cases";s:5:"cases";s:6:"report";s:12:"reportAction";s:9:"linkStory";s:9:"linkStory";s:17:"batchChangeBranch";s:17:"batchChangeBranch";s:17:"batchChangeModule";s:17:"batchChangeModule";s:11:"batchToTask";s:11:"batchToTask";s:18:"processStoryChange";s:18:"processStoryChange";s:11:"linkStories";s:13:"linkStoriesAB";s:8:"relieved";s:13:"relievedTwins";s:9:"batchEdit";s:9:"batchEdit";s:6:"import";s:10:"importCase";s:14:"exportTemplate";s:14:"exportTemplate";s:11:"importToLib";s:11:"importToLib";s:16:"batchImportToLib";s:16:"batchImportToLib";s:8:"relation";s:8:"relation";}s:11:"requirement";a:24:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:10:"editAction";s:6:"export";s:12:"exportAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:6:"change";s:12:"changeAction";s:6:"review";s:12:"reviewAction";s:12:"submitReview";s:12:"submitReview";s:11:"batchReview";s:11:"batchReview";s:6:"recall";s:6:"recall";s:5:"close";s:11:"closeAction";s:10:"batchClose";s:10:"batchClose";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:8:"activate";s:14:"activateAction";s:6:"report";s:12:"reportAction";s:9:"linkStory";s:9:"linkStory";s:17:"batchChangeBranch";s:17:"batchChangeBranch";s:17:"batchChangeModule";s:17:"batchChangeModule";s:16:"linkRequirements";s:18:"linkRequirementsAB";s:9:"batchEdit";s:9:"batchEdit";s:6:"import";s:10:"importCase";s:14:"exportTemplate";s:14:"exportTemplate";}s:11:"productplan";a:17:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:7:"linkBug";s:7:"linkBug";s:9:"unlinkBug";s:9:"unlinkBug";s:14:"batchUnlinkBug";s:14:"batchUnlinkBug";s:9:"batchEdit";s:9:"batchEdit";s:5:"start";s:5:"start";s:6:"finish";s:6:"finish";s:5:"close";s:5:"close";s:8:"activate";s:8:"activate";s:17:"batchChangeStatus";s:17:"batchChangeStatus";}s:7:"release";a:14:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:6:"export";s:6:"export";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:7:"linkBug";s:7:"linkBug";s:9:"unlinkBug";s:9:"unlinkBug";s:14:"batchUnlinkBug";s:14:"batchUnlinkBug";s:12:"changeStatus";s:12:"changeStatus";s:6:"notify";s:6:"notify";}s:7:"project";a:35:{s:5:"index";s:5:"index";s:6:"browse";s:6:"browse";s:6:"kanban";s:6:"kanban";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:5:"group";s:5:"group";s:11:"createGroup";s:11:"createGroup";s:10:"managePriv";s:10:"managePriv";s:13:"manageMembers";s:13:"manageMembers";s:17:"manageGroupMember";s:17:"manageGroupMember";s:9:"copyGroup";s:9:"copyGroup";s:9:"editGroup";s:9:"editGroup";s:5:"start";s:5:"start";s:7:"suspend";s:7:"suspend";s:5:"close";s:5:"close";s:8:"activate";s:8:"activate";s:11:"updateOrder";s:11:"updateOrder";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";s:15:"unbindWhitelist";s:15:"unbindWhitelist";s:14:"manageProducts";s:14:"manageProducts";s:7:"dynamic";s:7:"dynamic";s:3:"bug";s:3:"bug";s:8:"testcase";s:8:"testcase";s:8:"testtask";s:8:"testtask";s:10:"testreport";s:10:"testreport";s:9:"execution";s:9:"execution";s:6:"export";s:6:"export";s:4:"team";s:10:"teamAction";s:12:"unlinkMember";s:18:"unlinkMemberAction";s:12:"programTitle";s:16:"moduleOpenAction";s:8:"approval";s:8:"approval";}s:11:"projectplan";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";}s:12:"projectstory";a:8:{s:5:"story";s:5:"story";s:5:"track";s:11:"trackAction";s:4:"view";s:4:"view";s:9:"linkStory";s:9:"linkStory";s:17:"importplanstories";s:17:"importplanstories";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:13:"importFromLib";s:13:"importFromLib";}s:9:"execution";a:65:{s:4:"view";s:4:"view";s:6:"create";s:10:"createExec";s:4:"edit";s:10:"editAction";s:9:"batchedit";s:15:"batchEditAction";s:17:"batchchangestatus";s:17:"batchChangeStatus";s:5:"start";s:11:"startAction";s:8:"activate";s:14:"activateAction";s:6:"putoff";s:11:"delayAction";s:7:"suspend";s:13:"suspendAction";s:5:"close";s:11:"closeAction";s:6:"delete";s:8:"deleteAB";s:4:"task";s:4:"task";s:9:"grouptask";s:9:"groupTask";s:10:"importtask";s:10:"importTask";s:17:"importplanstories";s:17:"importPlanStories";s:9:"importBug";s:9:"importBug";s:5:"story";s:5:"story";s:5:"build";s:5:"build";s:8:"testcase";s:8:"testcase";s:3:"bug";s:3:"bug";s:8:"testtask";s:8:"testtask";s:10:"testreport";s:10:"testreport";s:4:"burn";s:4:"burn";s:11:"computeBurn";s:17:"computeBurnAction";s:3:"cfd";s:3:"CFD";s:10:"computeCFD";s:10:"computeCFD";s:8:"fixFirst";s:8:"fixFirst";s:4:"team";s:10:"teamAction";s:7:"dynamic";s:7:"dynamic";s:14:"manageProducts";s:14:"manageProducts";s:13:"manageMembers";s:13:"manageMembers";s:12:"unlinkMember";s:12:"unlinkMember";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:11:"updateOrder";s:11:"updateOrder";s:10:"taskKanban";s:10:"taskKanban";s:11:"printKanban";s:17:"printKanbanAction";s:4:"tree";s:10:"treeAction";s:8:"treeTask";s:12:"treeViewTask";s:9:"treeStory";s:13:"treeViewStory";s:3:"all";s:14:"allExecutionAB";s:6:"export";s:12:"exportAction";s:11:"storyKanban";s:11:"storyKanban";s:9:"storySort";s:9:"storySort";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";s:15:"unbindWhitelist";s:15:"unbindWhitelist";s:13:"storyEstimate";s:13:"storyEstimate";s:15:"executionkanban";s:12:"kanbanAction";s:6:"kanban";s:8:"RDKanban";s:9:"setKanban";s:9:"setKanban";s:3:"doc";s:3:"doc";s:9:"storyView";s:9:"storyView";s:8:"calendar";s:8:"calendar";s:14:"effortCalendar";s:14:"effortCalendar";s:6:"effort";s:12:"effortAction";s:10:"taskEffort";s:10:"taskEffort";s:17:"computeTaskEffort";s:17:"computeTaskEffort";s:14:"deleterelation";s:14:"deleterelation";s:16:"maintainrelation";s:12:"editrelation";s:8:"relation";s:12:"viewrelation";s:5:"gantt";s:10:"ganttchart";s:12:"ganttsetting";s:12:"ganttSetting";s:9:"ganttEdit";s:9:"ganttEdit";}s:6:"kanban";a:44:{s:5:"space";s:11:"spaceCommon";s:11:"createSpace";s:11:"createSpace";s:9:"editSpace";s:9:"editSpace";s:10:"closeSpace";s:10:"closeSpace";s:11:"deleteSpace";s:11:"deleteSpace";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:5:"close";s:5:"close";s:6:"delete";s:6:"delete";s:12:"createRegion";s:12:"createRegion";s:10:"editRegion";s:10:"editRegion";s:10:"sortRegion";s:10:"sortRegion";s:9:"sortGroup";s:9:"sortGroup";s:12:"deleteRegion";s:12:"deleteRegion";s:10:"createLane";s:10:"createLane";s:8:"sortLane";s:8:"sortLane";s:10:"deleteLane";s:10:"deleteLane";s:12:"createColumn";s:12:"createColumn";s:9:"setColumn";s:10:"editColumn";s:6:"setWIP";s:6:"setWIP";s:10:"sortColumn";s:10:"sortColumn";s:12:"deleteColumn";s:12:"deleteColumn";s:10:"createCard";s:10:"createCard";s:8:"editCard";s:8:"editCard";s:8:"viewCard";s:8:"viewCard";s:8:"sortCard";s:8:"sortCard";s:10:"deleteCard";s:10:"deleteCard";s:12:"assigntoCard";s:12:"assigntoCard";s:8:"moveCard";s:8:"moveCard";s:12:"setCardColor";s:12:"setCardColor";s:18:"viewArchivedColumn";s:18:"viewArchivedColumn";s:15:"batchCreateCard";s:15:"batchCreateCard";s:13:"activateSpace";s:13:"activateSpace";s:7:"setting";s:7:"setting";s:8:"activate";s:8:"activate";s:13:"editLaneColor";s:13:"editLaneColor";s:12:"editLaneName";s:12:"editLaneName";s:11:"splitColumn";s:11:"splitColumn";s:13:"archiveColumn";s:13:"archiveColumn";s:13:"restoreColumn";s:13:"restoreColumn";s:11:"archiveCard";s:11:"archiveCard";s:16:"viewArchivedCard";s:16:"viewArchivedCard";s:11:"restoreCard";s:11:"restoreCard";}s:11:"programplan";a:4:{s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"browse";s:5:"gantt";s:9:"ganttEdit";s:9:"ganttEdit";}s:4:"task";a:27:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";s:4:"edit";s:4:"edit";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:5:"start";s:11:"startAction";s:5:"pause";s:11:"pauseAction";s:7:"restart";s:13:"restartAction";s:6:"finish";s:12:"finishAction";s:6:"cancel";s:12:"cancelAction";s:5:"close";s:11:"closeAction";s:10:"batchClose";s:10:"batchClose";s:8:"activate";s:14:"activateAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:6:"export";s:12:"exportAction";s:18:"confirmStoryChange";s:18:"confirmStoryChange";s:14:"recordEstimate";s:20:"recordEstimateAction";s:12:"editEstimate";s:12:"editEstimate";s:14:"deleteEstimate";s:14:"deleteEstimate";s:6:"report";s:11:"reportChart";s:17:"batchChangeModule";s:17:"batchChangeModule";s:11:"batchCancel";s:11:"batchCancel";s:6:"import";s:6:"import";s:14:"exportTemplate";s:14:"exportTemplate";s:19:"confirmdesignchange";s:19:"confirmDesignChange";}s:5:"build";a:10:{s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:7:"linkBug";s:7:"linkBug";s:9:"unlinkBug";s:9:"unlinkBug";s:14:"batchUnlinkBug";s:14:"batchUnlinkBug";}s:6:"design";a:14:{s:6:"browse";s:6:"browse";s:4:"view";s:4:"view";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:4:"edit";s:8:"assignTo";s:8:"assignTo";s:6:"delete";s:6:"delete";s:10:"linkCommit";s:10:"linkCommit";s:10:"viewCommit";s:10:"viewCommit";s:12:"unlinkCommit";s:12:"unlinkCommit";s:8:"revision";s:8:"revision";s:6:"submit";s:6:"submit";s:7:"setType";s:7:"setType";s:11:"setPlusType";s:11:"setPlusType";}s:2:"qa";a:1:{s:5:"index";s:11:"indexAction";}s:3:"bug";a:26:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";s:10:"confirmBug";s:13:"confirmAction";s:12:"batchConfirm";s:12:"batchConfirm";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:7:"resolve";s:13:"resolveAction";s:12:"batchResolve";s:12:"batchResolve";s:10:"batchClose";s:10:"batchClose";s:13:"batchActivate";s:13:"batchActivate";s:8:"activate";s:14:"activateAction";s:5:"close";s:11:"closeAction";s:6:"report";s:12:"reportAction";s:6:"export";s:12:"exportAction";s:18:"confirmStoryChange";s:18:"confirmStoryChange";s:6:"delete";s:12:"deleteAction";s:8:"linkBugs";s:8:"linkBugs";s:17:"batchChangeModule";s:17:"batchChangeModule";s:17:"batchChangeBranch";s:17:"batchChangeBranch";s:15:"batchChangePlan";s:15:"batchChangePlan";s:6:"import";s:10:"importCase";s:14:"exportTemplate";s:14:"exportTemplate";}s:8:"testcase";a:40:{s:6:"browse";s:6:"browse";s:9:"groupCase";s:9:"groupCase";s:8:"zeroCase";s:8:"zeroCase";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:9:"createBug";s:9:"createBug";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:6:"delete";s:12:"deleteAction";s:6:"export";s:12:"exportAction";s:13:"confirmChange";s:13:"confirmChange";s:18:"confirmStoryChange";s:18:"confirmStoryChange";s:9:"batchEdit";s:9:"batchEdit";s:11:"batchDelete";s:11:"batchDelete";s:17:"batchChangeModule";s:17:"batchChangeModule";s:17:"batchChangeBranch";s:17:"batchChangeBranch";s:9:"linkCases";s:9:"linkCases";s:8:"linkBugs";s:8:"linkBugs";s:4:"bugs";s:4:"bugs";s:6:"review";s:6:"review";s:11:"batchReview";s:11:"batchReview";s:23:"batchConfirmStoryChange";s:23:"batchConfirmStoryChange";s:13:"importFromLib";s:13:"importFromLib";s:19:"batchCaseTypeChange";s:19:"batchCaseTypeChange";s:20:"confirmLibcaseChange";s:20:"confirmLibcaseChange";s:19:"ignoreLibcaseChange";s:19:"ignoreLibcaseChange";s:11:"importToLib";s:11:"importToLib";s:10:"automation";s:10:"automation";s:10:"showScript";s:10:"showScript";s:11:"createScene";s:11:"createScene";s:9:"editScene";s:9:"editScene";s:11:"deleteScene";s:11:"deleteScene";s:11:"changeScene";s:11:"changeScene";s:16:"batchChangeScene";s:16:"batchChangeScene";s:11:"updateOrder";s:11:"updateOrder";s:11:"importXmind";s:11:"importXmind";s:11:"exportXmind";s:11:"exportXmind";s:14:"exportTemplate";s:14:"exportTemplate";s:6:"import";s:16:"importCaseAction";s:6:"submit";s:6:"submit";}s:8:"testtask";a:24:{s:6:"create";s:6:"create";s:6:"browse";s:6:"browse";s:4:"view";s:10:"viewAction";s:5:"cases";s:11:"casesAction";s:9:"groupCase";s:9:"groupCase";s:4:"edit";s:4:"edit";s:5:"start";s:11:"startAction";s:8:"activate";s:14:"activateAction";s:5:"block";s:11:"blockAction";s:5:"close";s:11:"closeAction";s:6:"delete";s:6:"delete";s:11:"batchAssign";s:11:"batchAssign";s:8:"linkcase";s:8:"linkCase";s:10:"unlinkcase";s:13:"lblUnlinkCase";s:7:"runcase";s:10:"lblRunCase";s:7:"results";s:13:"resultsAction";s:16:"batchUnlinkCases";s:16:"batchUnlinkCases";s:6:"report";s:12:"reportAction";s:11:"browseUnits";s:11:"browseUnits";s:9:"unitCases";s:9:"unitCases";s:16:"importUnitResult";s:16:"importUnitResult";s:8:"batchRun";s:8:"batchRun";s:13:"runDeployCase";s:13:"runDeployCase";s:17:"deployCaseResults";s:17:"deployCaseResults";}s:9:"testsuite";a:8:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:8:"linkCase";s:8:"linkCase";s:10:"unlinkCase";s:16:"unlinkCaseAction";s:16:"batchUnlinkCases";s:16:"batchUnlinkCases";}s:10:"testreport";a:6:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:4:"edit";s:4:"edit";s:6:"export";s:12:"exportAction";}s:9:"milestone";a:2:{s:5:"index";s:11:"indexAction";s:16:"saveOtherProblem";s:16:"saveOtherProblem";}s:7:"caselib";a:10:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:10:"createCase";s:10:"createCase";s:15:"batchCreateCase";s:15:"batchCreateCase";s:14:"exportTemplate";s:14:"exportTemplate";s:6:"import";s:12:"importAction";s:10:"showImport";s:10:"showImport";}s:6:"zahost";a:8:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:11:"browseImage";s:11:"browseImage";s:13:"downloadImage";s:13:"downloadImage";s:14:"cancelDownload";s:6:"cancel";}s:6:"zanode";a:17:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:7:"destroy";s:7:"destroy";s:6:"reboot";s:6:"reboot";s:6:"resume";s:6:"resume";s:6:"getVNC";s:6:"getVNC";s:5:"start";s:4:"boot";s:5:"close";s:8:"shutdown";s:4:"view";s:4:"view";s:11:"createImage";s:11:"createImage";s:14:"browseSnapshot";s:14:"browseSnapshot";s:14:"createSnapshot";s:14:"createSnapshot";s:12:"editSnapshot";s:12:"editSnapshot";s:15:"restoreSnapshot";s:15:"restoreSnapshot";s:14:"deleteSnapshot";s:14:"deleteSnapshot";s:7:"suspend";s:7:"suspend";}s:3:"doc";a:32:{s:5:"index";s:5:"index";s:7:"mySpace";s:7:"mySpace";s:6:"myView";s:6:"myView";s:12:"myCollection";s:12:"myCollection";s:10:"myCreation";s:10:"myCreation";s:8:"myEdited";s:8:"myEdited";s:9:"createLib";s:9:"createLib";s:7:"editLib";s:7:"editLib";s:9:"deleteLib";s:9:"deleteLib";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:10:"deleteFile";s:10:"deleteFile";s:7:"collect";s:13:"collectAction";s:12:"productSpace";s:12:"productSpace";s:12:"projectSpace";s:12:"projectSpace";s:9:"teamSpace";s:9:"teamSpace";s:9:"showFiles";s:9:"showFiles";s:10:"addCatalog";s:10:"addCatalog";s:11:"editCatalog";s:11:"editCatalog";s:11:"sortCatalog";s:11:"sortCatalog";s:13:"deleteCatalog";s:13:"deleteCatalog";s:14:"displaySetting";s:14:"displaySetting";s:4:"diff";s:10:"diffAction";s:11:"mine2export";s:11:"mine2export";s:14:"product2export";s:14:"product2export";s:14:"project2export";s:14:"project2export";s:13:"custom2export";s:13:"custom2export";s:16:"execution2export";s:16:"execution2export";s:19:"importToPracticeLib";s:19:"importToPracticeLib";s:20:"importToComponentLib";s:20:"importToComponentLib";}s:6:"screen";a:9:{s:6:"browse";s:6:"browse";s:4:"view";s:4:"view";s:6:"create";s:6:"create";s:4:"edit";s:10:"editScreen";s:6:"design";s:12:"designScreen";s:7:"publish";s:13:"publishScreen";s:6:"delete";s:12:"deleteScreen";s:10:"annualData";s:10:"annualData";s:13:"allAnnualData";s:13:"allAnnualData";}s:5:"pivot";a:24:{s:6:"browse";s:12:"browseAction";s:7:"preview";s:7:"preview";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"design";s:6:"design";s:6:"delete";s:6:"delete";s:6:"export";s:6:"export";s:11:"showProduct";s:11:"showProduct";s:11:"showProject";s:11:"showProject";s:16:"projectDeviation";s:16:"projectDeviation";s:14:"productSummary";s:14:"productSummary";s:9:"bugCreate";s:9:"bugCreate";s:9:"bugAssign";s:9:"bugAssign";s:8:"workload";s:8:"workload";s:8:"casesrun";s:8:"casesrun";s:14:"storyLinkedBug";s:14:"storyLinkedBug";s:8:"testcase";s:8:"testcase";s:5:"build";s:5:"build";s:11:"workSummary";s:11:"workSummary";s:7:"roadmap";s:7:"roadmap";s:13:"productInvest";s:13:"productInvest";s:10:"bugSummary";s:10:"bugSummary";s:16:"bugAssignSummary";s:16:"bugAssignSummary";s:17:"workAssignSummary";s:17:"workAssignSummary";}s:5:"chart";a:7:{s:6:"browse";s:12:"browseAction";s:7:"preview";s:7:"preview";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"design";s:6:"design";s:6:"delete";s:6:"delete";s:6:"export";s:6:"export";}s:8:"dataview";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:5:"query";s:6:"design";s:6:"delete";s:6:"delete";}s:9:"dimension";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:6:"report";a:12:{s:4:"show";s:4:"show";s:6:"custom";s:6:"custom";s:10:"editReport";s:16:"editReportAction";s:9:"useReport";s:15:"useReportAction";s:12:"deleteReport";s:12:"deleteReport";s:10:"saveReport";s:10:"saveReport";s:13:"crystalExport";s:13:"crystalExport";s:16:"customeredReport";s:16:"customeredReport";s:10:"viewReport";s:10:"viewReport";s:14:"projectSummary";s:14:"projectSummary";s:15:"projectWorkload";s:15:"projectWorkload";s:6:"export";s:6:"export";}s:7:"company";a:9:{s:6:"browse";s:6:"browse";s:4:"edit";s:4:"edit";s:7:"dynamic";s:7:"dynamic";s:4:"view";s:4:"view";s:4:"todo";s:11:"companyTodo";s:8:"calendar";s:14:"effortCalendar";s:7:"allTodo";s:7:"allTodo";s:6:"effort";s:13:"companyEffort";s:9:"alleffort";s:9:"allEffort";}s:4:"dept";a:5:{s:6:"browse";s:6:"browse";s:11:"updateOrder";s:11:"updateOrder";s:11:"manageChild";s:17:"manageChildAction";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:5:"group";a:9:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"copy";s:4:"copy";s:6:"delete";s:6:"delete";s:10:"managePriv";s:10:"managePriv";s:12:"manageMember";s:12:"manageMember";s:18:"manageProjectAdmin";s:18:"manageProjectAdmin";s:10:"manageView";s:10:"manageView";}s:4:"user";a:27:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:6:"unlock";s:6:"unlock";s:6:"delete";s:6:"delete";s:4:"todo";s:4:"todo";s:4:"task";s:4:"task";s:3:"bug";s:3:"bug";s:7:"dynamic";s:7:"dynamic";s:7:"profile";s:7:"profile";s:9:"batchEdit";s:9:"batchEdit";s:6:"unbind";s:6:"unbind";s:17:"setPublicTemplate";s:17:"setPublicTemplate";s:6:"export";s:6:"export";s:14:"exportTemplate";s:14:"exportTemplate";s:5:"story";s:5:"story";s:8:"testTask";s:8:"testTask";s:8:"testCase";s:8:"testCase";s:9:"execution";s:9:"execution";s:14:"effortcalendar";s:14:"effortcalendar";s:12:"todocalendar";s:12:"todocalendar";s:6:"effort";s:6:"effort";s:6:"import";s:6:"import";s:10:"importldap";s:10:"importLDAP";s:5:"issue";s:5:"issue";s:4:"risk";s:4:"risk";}s:5:"admin";a:8:{s:5:"index";s:5:"index";s:9:"checkWeak";s:9:"checkWeak";s:3:"sso";s:9:"ssoAction";s:8:"register";s:8:"register";s:8:"xuanxuan";s:8:"xuanxuan";s:15:"resetPWDSetting";s:15:"resetPWDSetting";s:11:"tableEngine";s:11:"tableEngine";s:4:"safe";s:9:"safeIndex";}s:5:"stage";a:7:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:4:"edit";s:7:"setType";s:7:"setType";s:6:"delete";s:6:"delete";s:10:"plusBrowse";s:10:"plusBrowse";}s:9:"extension";a:10:{s:6:"browse";s:12:"browseAction";s:6:"obtain";s:6:"obtain";s:9:"structure";s:15:"structureAction";s:7:"install";s:7:"install";s:9:"uninstall";s:15:"uninstallAction";s:8:"activate";s:14:"activateAction";s:10:"deactivate";s:16:"deactivateAction";s:6:"upload";s:6:"upload";s:5:"erase";s:11:"eraseAction";s:7:"upgrade";s:7:"upgrade";}s:6:"custom";a:19:{s:3:"set";s:3:"set";s:7:"product";s:11:"productName";s:9:"execution";s:15:"executionCommon";s:8:"required";s:8:"required";s:7:"restore";s:7:"restore";s:4:"flow";s:4:"flow";s:8:"timezone";s:8:"timezone";s:8:"estimate";s:8:"estimate";s:16:"editStoryConcept";s:16:"editStoryConcept";s:18:"browseStoryConcept";s:18:"browseStoryConcept";s:17:"setDefaultConcept";s:17:"setDefaultConcept";s:18:"deleteStoryConcept";s:18:"deleteStoryConcept";s:6:"kanban";s:6:"kanban";s:4:"code";s:4:"code";s:5:"hours";s:5:"hours";s:7:"percent";s:7:"percent";s:13:"limitTaskDate";s:19:"limitTaskDateAction";s:15:"setStoryConcept";s:15:"setStoryConcept";s:11:"libreoffice";s:11:"libreOffice";}s:6:"action";a:6:{s:5:"trash";s:11:"trashAction";s:8:"undelete";s:14:"undeleteAction";s:7:"hideOne";s:13:"hideOneAction";s:7:"hideAll";s:7:"hideAll";s:7:"comment";s:7:"comment";s:11:"editComment";s:11:"editComment";}s:4:"mail";a:10:{s:5:"index";s:5:"index";s:6:"detect";s:12:"detectAction";s:4:"edit";s:4:"edit";s:4:"save";s:10:"saveAction";s:4:"test";s:4:"test";s:5:"reset";s:11:"resetAction";s:6:"browse";s:6:"browse";s:6:"delete";s:6:"delete";s:11:"batchDelete";s:11:"batchDelete";s:6:"resend";s:12:"resendAction";}s:3:"sms";a:3:{s:5:"index";s:5:"index";s:4:"test";s:4:"test";s:5:"reset";s:5:"reset";}s:3:"svn";a:3:{s:4:"diff";s:4:"diff";s:3:"cat";s:3:"cat";s:7:"apiSync";s:7:"apiSync";}s:3:"git";a:3:{s:4:"diff";s:4:"diff";s:3:"cat";s:3:"cat";s:7:"apiSync";s:7:"apiSync";}s:6:"search";a:7:{s:9:"buildForm";s:9:"buildForm";s:10:"buildQuery";s:10:"buildQuery";s:9:"saveQuery";s:9:"saveQuery";s:11:"deleteQuery";s:11:"deleteQuery";s:6:"select";s:6:"select";s:5:"index";s:5:"index";s:10:"buildIndex";s:10:"buildIndex";}s:4:"tree";a:10:{s:6:"browse";s:6:"browse";s:10:"browseTask";s:10:"browseTask";s:11:"updateOrder";s:11:"updateOrder";s:11:"manageChild";s:11:"manageChild";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:8:"editHost";s:8:"editHost";s:3:"fix";s:3:"fix";s:10:"browsehost";s:16:"groupMaintenance";s:11:"browsegroup";s:11:"browsegroup";}s:3:"api";a:22:{s:5:"index";s:5:"index";s:9:"createLib";s:9:"createLib";s:7:"editLib";s:7:"editLib";s:9:"deleteLib";s:9:"deleteLib";s:13:"createRelease";s:13:"createRelease";s:8:"releases";s:8:"releases";s:13:"deleteRelease";s:13:"deleteRelease";s:6:"struct";s:6:"struct";s:12:"createStruct";s:12:"createStruct";s:10:"editStruct";s:10:"editStruct";s:12:"deleteStruct";s:12:"deleteStruct";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:10:"addCatalog";s:10:"addCatalog";s:11:"editCatalog";s:11:"editCatalog";s:11:"sortCatalog";s:11:"sortCatalog";s:13:"deleteCatalog";s:13:"deleteCatalog";s:8:"getModel";s:8:"getModel";s:5:"debug";s:5:"debug";s:3:"sql";s:3:"sql";s:6:"export";s:6:"export";}s:4:"file";a:5:{s:8:"download";s:8:"download";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:12:"uploadImages";s:12:"uploadImages";s:9:"setPublic";s:9:"setPublic";}s:4:"misc";a:1:{s:4:"ping";s:4:"ping";}s:6:"backup";a:7:{s:5:"index";s:5:"index";s:6:"backup";s:6:"backup";s:7:"restore";s:13:"restoreAction";s:6:"delete";s:6:"delete";s:7:"setting";s:13:"settingAction";s:11:"rmPHPHeader";s:11:"rmPHPHeader";s:6:"change";s:6:"change";}s:4:"cron";a:7:{s:5:"index";s:5:"index";s:6:"turnon";s:6:"turnon";s:6:"create";s:12:"createAction";s:4:"edit";s:4:"edit";s:6:"toggle";s:6:"toggle";s:6:"delete";s:6:"delete";s:11:"openProcess";s:7:"restart";}s:3:"dev";a:2:{s:3:"api";s:3:"api";s:2:"db";s:2:"db";}s:6:"editor";a:6:{s:5:"index";s:5:"index";s:6:"extend";s:6:"extend";s:4:"edit";s:4:"edit";s:7:"newPage";s:7:"newPage";s:4:"save";s:4:"save";s:6:"delete";s:6:"delete";}s:5:"issue";a:16:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:6:"delete";s:12:"deleteAction";s:4:"edit";s:10:"editAction";s:7:"confirm";s:13:"confirmAction";s:8:"assignTo";s:14:"assignToAction";s:5:"close";s:11:"closeAction";s:6:"cancel";s:12:"cancelAction";s:8:"activate";s:14:"activateAction";s:7:"resolve";s:13:"resolveAction";s:4:"view";s:4:"view";s:6:"export";s:12:"exportAction";s:11:"importToLib";s:11:"importToLib";s:16:"batchImportToLib";s:16:"batchImportToLib";s:13:"importFromLib";s:13:"importFromLib";}s:4:"risk";a:16:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:12:"deleteAction";s:8:"activate";s:14:"activateAction";s:5:"close";s:11:"closeAction";s:6:"hangup";s:12:"hangupAction";s:11:"batchCreate";s:11:"batchCreate";s:6:"cancel";s:12:"cancelAction";s:5:"track";s:11:"trackAction";s:4:"view";s:4:"view";s:8:"assignTo";s:14:"assignToAction";s:11:"importToLib";s:11:"importToLib";s:16:"batchImportToLib";s:16:"batchImportToLib";s:13:"importFromLib";s:13:"importFromLib";s:6:"export";s:12:"exportAction";}s:2:"mr";a:16:{s:6:"create";s:6:"create";s:6:"browse";s:12:"browseAction";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:10:"viewAction";s:6:"accept";s:6:"accept";s:4:"diff";s:8:"viewDiff";s:4:"link";s:8:"linkList";s:9:"linkStory";s:9:"linkStory";s:7:"linkBug";s:7:"linkBug";s:8:"linkTask";s:8:"linkTask";s:6:"unlink";s:6:"unlink";s:8:"approval";s:8:"approval";s:5:"close";s:5:"close";s:6:"reopen";s:6:"reopen";s:9:"addReview";s:9:"addReview";}s:6:"budget";a:7:{s:6:"browse";s:12:"browseAction";s:7:"summary";s:13:"summaryAction";s:6:"create";s:12:"createAction";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:10:"editAction";s:4:"view";s:10:"viewAction";s:6:"delete";s:12:"deleteAction";}s:14:"workestimation";a:1:{s:5:"index";s:5:"index";}s:18:"durationestimation";a:2:{s:5:"index";s:11:"indexAction";s:6:"create";s:6:"create";}s:7:"holiday";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:6:"import";s:12:"importAction";}s:6:"weekly";a:2:{s:5:"index";s:5:"index";s:18:"exportweeklyreport";s:18:"exportWeeklyReport";}s:11:"opportunity";a:22:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:12:"deleteAction";s:8:"activate";s:14:"activateAction";s:5:"close";s:11:"closeAction";s:6:"hangup";s:12:"hangupAction";s:6:"cancel";s:12:"cancelAction";s:5:"track";s:11:"trackAction";s:4:"view";s:4:"view";s:8:"assignTo";s:14:"assignToAction";s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";s:13:"batchAssignTo";s:13:"batchAssignTo";s:10:"batchClose";s:10:"batchClose";s:11:"batchCancel";s:11:"batchCancel";s:11:"batchHangup";s:11:"batchHangup";s:13:"batchActivate";s:13:"batchActivate";s:11:"importToLib";s:11:"importToLib";s:16:"batchImportToLib";s:16:"batchImportToLib";s:13:"importFromLib";s:13:"importFromLib";s:6:"export";s:12:"exportAction";}s:9:"trainplan";a:10:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";s:11:"batchFinish";s:11:"batchFinish";s:6:"delete";s:6:"delete";s:6:"finish";s:12:"finishAction";s:7:"summary";s:13:"summaryAction";s:4:"view";s:4:"view";}s:2:"nc";a:10:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:7:"resolve";s:7:"resolve";s:4:"view";s:4:"view";s:5:"close";s:5:"close";s:6:"delete";s:6:"delete";s:8:"assignTo";s:8:"assignTo";s:8:"activate";s:8:"activate";s:6:"export";s:12:"exportAction";}s:11:"gapanalysis";a:7:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";}s:12:"researchplan";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";}s:14:"researchreport";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";}s:7:"meeting";a:6:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:7:"minutes";s:7:"minutes";}s:11:"meetingroom";a:7:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";}s:8:"approval";a:1:{s:8:"progress";s:8:"progress";}s:8:"workflow";a:17:{s:10:"browseFlow";s:10:"browseFlow";s:8:"browseDB";s:8:"browseDB";s:6:"create";s:6:"create";s:4:"copy";s:4:"copy";s:4:"edit";s:4:"edit";s:6:"backup";s:6:"backup";s:7:"upgrade";s:13:"upgradeAction";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:9:"flowchart";s:9:"flowchart";s:2:"ui";s:2:"ui";s:7:"release";s:7:"release";s:10:"deactivate";s:10:"deactivate";s:8:"activate";s:8:"activate";s:11:"setApproval";s:11:"setApproval";s:5:"setJS";s:5:"setJS";s:6:"setCSS";s:6:"setCSS";}s:13:"workflowfield";a:11:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";s:6:"import";s:6:"import";s:10:"showImport";s:10:"showImport";s:14:"exportTemplate";s:14:"exportTemplate";s:8:"setValue";s:8:"setValue";s:9:"setExport";s:9:"setExport";s:9:"setSearch";s:9:"setSearch";}s:14:"workflowaction";a:10:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";s:15:"setVerification";s:15:"setVerification";s:9:"setNotice";s:9:"setNotice";s:5:"setJS";s:5:"setJS";s:6:"setCSS";s:6:"setCSS";}s:14:"workflowlayout";a:2:{s:5:"admin";s:5:"admin";s:5:"block";s:5:"block";}s:17:"workflowcondition";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:15:"workflowlinkage";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:12:"workflowhook";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:13:"workflowlabel";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";}s:16:"workflowrelation";a:1:{s:5:"admin";s:5:"admin";}s:14:"workflowreport";a:5:{s:6:"browse";s:4:"brow";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";}s:18:"workflowdatasource";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:12:"workflowrule";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";}s:12:"projectbuild";a:11:{s:6:"browse";s:6:"browse";s:4:"view";s:4:"view";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:7:"linkBug";s:7:"linkBug";s:9:"unlinkBug";s:9:"unlinkBug";s:14:"batchUnlinkBug";s:14:"batchUnlinkBug";}s:14:"projectrelease";a:14:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:6:"export";s:6:"export";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:7:"linkBug";s:7:"linkBug";s:9:"unlinkBug";s:9:"unlinkBug";s:14:"batchUnlinkBug";s:14:"batchUnlinkBug";s:12:"changeStatus";s:12:"changeStatus";s:6:"notify";s:6:"notify";}s:11:"stakeholder";a:9:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:10:"viewAction";s:11:"communicate";s:11:"communicate";s:6:"expect";s:6:"expect";s:9:"userIssue";s:9:"userIssue";}s:6:"branch";a:8:{s:6:"manage";s:6:"manage";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:5:"close";s:11:"closeAction";s:8:"activate";s:14:"activateAction";s:4:"sort";s:4:"sort";s:9:"batchEdit";s:9:"batchEdit";s:11:"mergeBranch";s:17:"mergeBranchAction";}s:4:"repo";a:27:{s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:6:"delete";s:6:"delete";s:14:"showSyncCommit";s:14:"showSyncCommit";s:8:"maintain";s:8:"maintain";s:6:"browse";s:12:"browseAction";s:4:"view";s:4:"view";s:4:"diff";s:10:"diffAction";s:3:"log";s:3:"log";s:8:"revision";s:14:"revisionAction";s:5:"blame";s:11:"blameAction";s:8:"download";s:14:"downloadAction";s:8:"setRules";s:8:"setRules";s:15:"apiGetRepoByUrl";s:15:"apiGetRepoByUrl";s:12:"downloadCode";s:12:"downloadCode";s:9:"linkStory";s:9:"linkStory";s:7:"linkBug";s:7:"linkBug";s:8:"linkTask";s:8:"linkTask";s:6:"unlink";s:6:"unlink";s:6:"import";s:12:"importAction";s:6:"review";s:12:"reviewAction";s:6:"addBug";s:6:"addBug";s:7:"editBug";s:7:"editBug";s:9:"deleteBug";s:9:"deleteBug";s:10:"addComment";s:10:"addComment";s:11:"editComment";s:11:"editComment";s:13:"deleteComment";s:13:"deleteComment";}s:2:"ci";a:2:{s:12:"commitResult";s:12:"commitResult";s:18:"checkCompileStatus";s:18:"checkCompileStatus";}s:7:"compile";a:3:{s:6:"browse";s:6:"browse";s:4:"logs";s:4:"logs";s:11:"syncCompile";s:11:"syncCompile";}s:7:"jenkins";a:4:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:12:"deleteAction";}s:3:"job";a:6:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"exec";s:4:"exec";s:4:"view";s:4:"view";}s:9:"datatable";a:1:{s:9:"setGlobal";s:9:"setGlobal";}s:6:"gitlab";a:30:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:11:"importIssue";s:11:"importIssue";s:6:"delete";s:6:"delete";s:8:"bindUser";s:8:"bindUser";s:13:"browseProject";s:13:"browseProject";s:13:"createProject";s:13:"createProject";s:11:"editProject";s:11:"editProject";s:13:"deleteProject";s:13:"deleteProject";s:11:"browseGroup";s:11:"browseGroup";s:11:"createGroup";s:11:"createGroup";s:9:"editGroup";s:9:"editGroup";s:11:"deleteGroup";s:11:"deleteGroup";s:18:"manageGroupMembers";s:18:"manageGroupMembers";s:10:"browseUser";s:10:"browseUser";s:10:"createUser";s:10:"createUser";s:8:"editUser";s:8:"editUser";s:10:"deleteUser";s:10:"deleteUser";s:12:"createBranch";s:12:"createBranch";s:12:"browseBranch";s:12:"browseBranch";s:7:"webhook";s:7:"webhook";s:13:"createWebhook";s:13:"createWebhook";s:20:"manageProjectMembers";s:20:"manageProjectMembers";s:16:"manageBranchPriv";s:16:"browseBranchPriv";s:13:"manageTagPriv";s:13:"browseTagPriv";s:9:"browseTag";s:9:"browseTag";s:9:"createTag";s:9:"createTag";s:9:"deleteTag";s:9:"deleteTag";}s:4:"gogs";a:6:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:8:"bindUser";s:8:"bindUser";}s:5:"gitea";a:6:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:8:"bindUser";s:8:"bindUser";}s:9:"sonarqube";a:10:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:13:"browseProject";s:13:"browseProject";s:13:"createProject";s:13:"createProject";s:13:"deleteProject";s:13:"deleteProject";s:7:"execJob";s:7:"execJob";s:10:"reportView";s:10:"reportView";s:11:"browseIssue";s:11:"browseIssue";}s:3:"app";a:1:{s:10:"serverlink";s:10:"serverLink";}s:7:"webhook";a:7:{s:6:"browse";s:4:"list";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:3:"log";s:9:"logAction";s:4:"bind";s:4:"bind";s:10:"chooseDept";s:10:"chooseDept";}s:7:"message";a:3:{s:5:"index";s:5:"index";s:7:"browser";s:7:"browser";s:7:"setting";s:7:"setting";}s:7:"contact";a:0:{}s:18:"programstakeholder";a:0:{}s:13:"executionview";a:0:{}s:11:"managespace";a:0:{}s:10:"systemteam";a:0:{}s:14:"systemschedule";a:0:{}s:12:"systemeffort";a:0:{}s:13:"systemdynamic";a:0:{}s:13:"systemcompany";a:0:{}s:8:"pipeline";a:0:{}s:13:"devopssetting";a:0:{}s:13:"featureswitch";a:0:{}s:10:"importdata";a:0:{}s:13:"systemsetting";a:0:{}s:11:"staffmanage";a:0:{}s:11:"modelconfig";a:0:{}s:13:"featureconfig";a:0:{}s:11:"doctemplate";a:0:{}s:13:"notifysetting";a:0:{}s:8:"bidesign";a:0:{}s:16:"personalsettings";a:0:{}s:15:"projectsettings";a:0:{}s:10:"dataaccess";a:0:{}s:14:"executiongantt";a:0:{}s:15:"executionkanban";a:0:{}s:13:"executionburn";a:0:{}s:12:"executioncfd";a:0:{}s:14:"executionstory";a:0:{}s:11:"executionqa";a:0:{}s:17:"executionsettings";a:0:{}s:14:"generalcomment";a:0:{}s:11:"generalping";a:0:{}s:15:"generaltemplate";a:0:{}s:13:"generaleffort";a:0:{}s:15:"productsettings";a:0:{}s:13:"projectreview";a:0:{}s:12:"projecttrack";a:0:{}s:9:"projectqa";a:0:{}s:6:"effort";a:8:{s:8:"calendar";s:14:"calendarAction";s:11:"batchCreate";s:11:"batchCreate";s:15:"createForObject";s:15:"createForObject";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:6:"export";s:12:"exportAction";}s:4:"ldap";a:1:{s:3:"set";s:6:"common";}s:10:"sqlbuilder";a:4:{s:13:"browseSQLView";s:13:"browseSQLView";s:13:"createSQLView";s:13:"createSQLView";s:11:"editSQLView";s:11:"editSQLView";s:13:"deleteSQLView";s:13:"deleteSQLView";}s:8:"feedback";a:30:{s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:10:"editOthers";s:10:"editOthers";s:9:"adminView";s:9:"adminView";s:5:"admin";s:5:"admin";s:8:"assignTo";s:12:"assignAction";s:6:"toTask";s:6:"toTask";s:6:"toTodo";s:6:"toTodo";s:5:"toBug";s:5:"toBug";s:7:"toStory";s:7:"toStory";s:8:"toTicket";s:8:"toTicket";s:11:"toUserStory";s:11:"toUserStory";s:6:"review";s:12:"reviewAction";s:7:"comment";s:7:"comment";s:5:"reply";s:5:"reply";s:3:"ask";s:3:"ask";s:5:"close";s:11:"closeAction";s:6:"delete";s:6:"delete";s:8:"activate";s:8:"activate";s:6:"export";s:12:"exportAction";s:9:"batchEdit";s:9:"batchEdit";s:10:"batchClose";s:10:"batchClose";s:11:"batchReview";s:11:"batchReview";s:13:"batchAssignTo";s:13:"batchAssignTo";s:8:"products";s:8:"products";s:13:"manageProduct";s:13:"manageProduct";s:6:"import";s:6:"import";s:14:"exportTemplate";s:14:"exportTemplate";s:11:"syncProduct";s:11:"syncProduct";s:14:"productSetting";s:14:"productSetting";}s:3:"faq";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:6:"ticket";a:21:{s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"browse";s:6:"browse";s:8:"assignTo";s:6:"assign";s:9:"createBug";s:9:"createBug";s:11:"createStory";s:11:"createStory";s:5:"start";s:5:"start";s:6:"finish";s:6:"finish";s:5:"close";s:5:"close";s:8:"activate";s:8:"activate";s:6:"delete";s:6:"delete";s:14:"exportTemplate";s:14:"exportTemplate";s:6:"import";s:6:"import";s:6:"export";s:12:"exportAction";s:9:"batchEdit";s:9:"batchEdit";s:10:"batchClose";s:10:"batchClose";s:13:"batchActivate";s:13:"batchActivate";s:11:"batchFinish";s:11:"batchFinish";s:13:"batchAssignTo";s:13:"batchAssignTo";s:11:"syncProduct";s:11:"syncProduct";}s:12:"feedbackpriv";a:0:{}s:6:"attend";a:15:{s:10:"department";s:10:"department";s:7:"company";s:7:"company";s:12:"browseReview";s:12:"browseReview";s:6:"review";s:6:"review";s:6:"export";s:12:"exportAction";s:4:"stat";s:12:"reportAction";s:8:"saveStat";s:14:"saveStatAction";s:10:"exportStat";s:10:"exportStat";s:6:"detail";s:12:"detailAction";s:12:"exportDetail";s:12:"exportDetail";s:8:"settings";s:8:"settings";s:16:"personalSettings";s:16:"personalSettings";s:10:"setManager";s:10:"setManager";s:8:"personal";s:8:"personal";s:4:"edit";s:10:"editAction";}s:5:"leave";a:13:{s:12:"browseReview";s:12:"browseReview";s:7:"company";s:13:"companyAction";s:6:"review";s:12:"reviewAction";s:6:"export";s:12:"exportAction";s:11:"setReviewer";s:17:"setReviewerAction";s:14:"personalAnnual";s:14:"personalAnnual";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:10:"viewAction";s:12:"switchstatus";s:12:"switchstatus";s:4:"back";s:10:"backAction";}s:6:"makeup";a:11:{s:12:"browseReview";s:12:"browseReview";s:7:"company";s:13:"companyAction";s:6:"review";s:12:"reviewAction";s:6:"export";s:12:"exportAction";s:11:"setReviewer";s:17:"setReviewerAction";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:4:"view";s:10:"viewAction";s:6:"delete";s:12:"deleteAction";s:12:"switchstatus";s:12:"switchstatus";}s:8:"overtime";a:11:{s:12:"browseReview";s:12:"browseReview";s:7:"company";s:13:"companyAction";s:6:"review";s:12:"reviewAction";s:6:"export";s:12:"exportAction";s:11:"setReviewer";s:17:"setReviewerAction";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:4:"view";s:10:"viewAction";s:6:"delete";s:12:"deleteAction";s:12:"switchstatus";s:12:"switchstatus";}s:4:"lieu";a:10:{s:7:"company";s:13:"companyAction";s:12:"browseReview";s:18:"browseReviewAction";s:6:"review";s:12:"reviewAction";s:11:"setReviewer";s:17:"setReviewerAction";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:10:"viewAction";s:12:"switchstatus";s:12:"switchstatus";}s:14:"officeapproval";a:0:{}s:13:"officesetting";a:0:{}s:14:"datapermission";a:0:{}s:12:"officeexport";a:0:{}s:3:"ops";a:2:{s:5:"index";s:5:"index";s:7:"setting";s:7:"setting";}s:4:"host";a:7:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:10:"editAction";s:4:"view";s:4:"view";s:6:"delete";s:12:"deleteAction";s:12:"changeStatus";s:12:"changeStatus";s:7:"treemap";s:7:"treemap";}s:10:"serverroom";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:10:"editAction";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";}s:7:"account";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:10:"editAction";s:4:"view";s:4:"view";s:6:"delete";s:12:"deleteAction";}s:6:"domain";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:10:"editAction";s:4:"view";s:4:"view";s:6:"delete";s:12:"deleteAction";}s:7:"service";a:6:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:6:"manage";s:6:"manage";}s:6:"deploy";a:20:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:8:"activate";s:14:"activateAction";s:6:"finish";s:12:"finishAction";s:5:"scope";s:5:"scope";s:11:"manageScope";s:11:"manageScope";s:4:"view";s:4:"view";s:5:"cases";s:11:"casesAction";s:9:"linkCases";s:9:"linkCases";s:10:"unlinkCase";s:10:"unlinkCase";s:16:"batchUnlinkCases";s:16:"batchUnlinkCases";s:5:"steps";s:5:"steps";s:10:"manageStep";s:10:"manageStep";s:10:"finishStep";s:10:"finishStep";s:8:"assignTo";s:12:"assignAction";s:8:"viewStep";s:8:"viewStep";s:8:"editStep";s:8:"editStep";s:10:"deleteStep";s:10:"deleteStep";}s:10:"conference";a:1:{s:5:"admin";s:5:"admin";}s:11:"traincourse";a:8:{s:6:"browse";s:12:"browseAction";s:5:"admin";s:11:"adminAction";s:12:"deleteCourse";s:12:"deleteCourse";s:12:"changeStatus";s:12:"changeStatus";s:12:"uploadCourse";s:12:"uploadCourse";s:11:"batchImport";s:11:"batchImport";s:10:"viewCourse";s:10:"viewCourse";s:11:"viewChapter";s:11:"viewChapter";}s:4:"pssp";a:2:{s:6:"browse";s:6:"browse";s:6:"update";s:6:"update";}s:8:"baseline";a:8:{s:12:"templateType";s:12:"templateType";s:8:"template";s:8:"template";s:14:"createTemplate";s:14:"createTemplate";s:4:"view";s:4:"view";s:12:"editTemplate";s:12:"editTemplate";s:8:"editBook";s:8:"editBook";s:10:"manageBook";s:10:"manageBook";s:6:"delete";s:6:"delete";}s:8:"classify";a:1:{s:6:"browse";s:6:"browse";}s:2:"cm";a:6:{s:6:"create";s:6:"create";s:6:"delete";s:6:"delete";s:4:"edit";s:4:"edit";s:6:"browse";s:6:"browse";s:4:"view";s:4:"view";s:6:"report";s:6:"report";}s:4:"cmcl";a:6:{s:11:"batchCreate";s:11:"batchCreate";s:6:"delete";s:6:"delete";s:4:"edit";s:4:"edit";s:6:"browse";s:6:"browse";s:4:"view";s:4:"view";s:19:"waterfallplusBrowse";s:19:"waterfallplusBrowse";}s:7:"auditcl";a:8:{s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";s:6:"delete";s:6:"delete";s:4:"edit";s:4:"edit";s:11:"scrumBrowse";s:11:"scrumBrowse";s:6:"browse";s:6:"browse";s:15:"agilePlusBrowse";s:15:"agilePlusBrowse";s:19:"waterfallplusBrowse";s:19:"waterfallplusBrowse";}s:8:"reviewcl";a:7:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:6:"delete";s:6:"delete";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:19:"waterfallplusBrowse";s:19:"waterfallplusBrowse";}s:7:"process";a:11:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:6:"delete";s:6:"delete";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:11:"updateOrder";s:11:"updateOrder";s:12:"activityList";s:12:"activityList";s:11:"scrumBrowse";s:11:"scrumBrowse";s:6:"browse";s:6:"browse";s:15:"agilePlusBrowse";s:15:"agilePlusBrowse";s:19:"waterfallPlusBrowse";s:19:"waterfallPlusBrowse";}s:8:"activity";a:9:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:6:"delete";s:6:"delete";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:8:"assignTo";s:8:"assignTo";s:10:"outputList";s:10:"outputList";s:11:"updateOrder";s:11:"updateOrder";}s:7:"zoutput";a:8:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:11:"updateOrder";s:11:"updateOrder";}s:9:"auditplan";a:11:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:10:"editAction";s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";s:10:"batchCheck";s:10:"batchCheck";s:5:"check";s:5:"check";s:2:"nc";s:2:"nc";s:6:"result";s:6:"result";s:6:"delete";s:6:"delete";s:8:"assignTo";s:8:"assignTo";}s:7:"subject";a:1:{s:6:"browse";s:6:"common";}s:12:"approvalflow";a:10:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"design";s:6:"design";s:6:"delete";s:6:"delete";s:4:"role";s:8:"roleList";s:10:"createRole";s:10:"createRole";s:8:"editRole";s:8:"editRole";s:10:"deleteRole";s:10:"deleteRole";}s:11:"reviewissue";a:7:{s:5:"issue";s:5:"issue";s:12:"updateStatus";s:12:"updateStatus";s:8:"resolved";s:8:"resolved";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";}s:13:"reviewsetting";a:2:{s:7:"version";s:7:"version";s:20:"waterfallplusVersion";s:20:"waterfallplusVersion";}s:6:"review";a:11:{s:6:"browse";s:12:"browseAction";s:6:"assess";s:6:"assess";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"submit";s:6:"submit";s:6:"recall";s:6:"recall";s:6:"report";s:12:"reviewReport";s:7:"toAudit";s:7:"toAudit";s:5:"audit";s:5:"audit";s:6:"delete";s:6:"delete";}s:11:"measurement";a:13:{s:7:"settips";s:7:"setTips";s:6:"setSQL";s:6:"setSQL";s:6:"browse";s:12:"browseAction";s:11:"createBasic";s:11:"createBasic";s:6:"delete";s:12:"deleteAction";s:9:"editBasic";s:9:"editBasic";s:10:"searchMeas";s:10:"searchMeas";s:8:"template";s:8:"template";s:14:"createTemplate";s:14:"createTemplate";s:12:"editTemplate";s:12:"editTemplate";s:12:"viewTemplate";s:12:"viewTemplate";s:9:"batchEdit";s:15:"batchEditAction";s:10:"saveReport";s:12:"saveReportAB";}s:10:"measrecord";a:1:{s:6:"browse";s:6:"browse";}s:8:"assetlib";a:98:{s:7:"caselib";s:7:"caseLib";s:8:"storylib";s:8:"storyLib";s:14:"createStorylib";s:14:"createStoryLib";s:12:"editStorylib";s:12:"editStoryLib";s:14:"deleteStorylib";s:14:"deleteStoryLib";s:12:"storyLibView";s:12:"storyLibView";s:5:"story";s:5:"story";s:11:"importStory";s:11:"importStory";s:13:"assignToStory";s:13:"assignToStory";s:18:"batchAssignToStory";s:18:"batchAssignToStory";s:12:"approveStory";s:12:"approveStory";s:17:"batchApproveStory";s:17:"batchApproveStory";s:9:"editStory";s:9:"editStory";s:11:"removeStory";s:11:"removeStory";s:16:"batchRemoveStory";s:16:"batchRemoveStory";s:9:"storyView";s:9:"storyView";s:8:"issuelib";s:8:"issueLib";s:14:"createIssuelib";s:14:"createIssueLib";s:12:"editIssuelib";s:12:"editIssueLib";s:14:"deleteIssuelib";s:14:"deleteIssueLib";s:12:"issueLibView";s:12:"issueLibView";s:5:"issue";s:5:"issue";s:11:"importIssue";s:11:"importIssue";s:13:"assignToIssue";s:13:"assignToIssue";s:18:"batchAssignToIssue";s:18:"batchAssignToIssue";s:12:"approveIssue";s:12:"approveIssue";s:17:"batchApproveIssue";s:17:"batchApproveIssue";s:9:"editIssue";s:9:"editIssue";s:11:"removeIssue";s:11:"removeIssue";s:16:"batchRemoveIssue";s:16:"batchRemoveIssue";s:9:"issueView";s:9:"issueView";s:7:"risklib";s:7:"riskLib";s:13:"createRisklib";s:13:"createRiskLib";s:11:"editRisklib";s:11:"editRiskLib";s:13:"deleteRisklib";s:13:"deleteRiskLib";s:11:"riskLibView";s:11:"riskLibView";s:4:"risk";s:4:"risk";s:10:"importRisk";s:10:"importRisk";s:12:"assignToRisk";s:12:"assignToRisk";s:17:"batchAssignToRisk";s:17:"batchAssignToRisk";s:11:"approveRisk";s:11:"approveRisk";s:16:"batchApproveRisk";s:16:"batchApproveRisk";s:8:"editRisk";s:8:"editRisk";s:10:"removeRisk";s:10:"removeRisk";s:15:"batchRemoveRisk";s:15:"batchRemoveRisk";s:8:"riskView";s:8:"riskView";s:14:"opportunitylib";s:14:"opportunityLib";s:20:"createOpportunitylib";s:20:"createOpportunityLib";s:18:"editOpportunitylib";s:18:"editOpportunityLib";s:20:"deleteOpportunitylib";s:20:"deleteOpportunityLib";s:18:"opportunityLibView";s:18:"opportunityLibView";s:11:"opportunity";s:11:"opportunity";s:17:"importOpportunity";s:17:"importOpportunity";s:19:"assignToOpportunity";s:19:"assignToOpportunity";s:24:"batchAssignToOpportunity";s:24:"batchAssignToOpportunity";s:18:"approveOpportunity";s:18:"approveOpportunity";s:23:"batchApproveOpportunity";s:23:"batchApproveOpportunity";s:15:"editOpportunity";s:15:"editOpportunity";s:17:"removeOpportunity";s:17:"removeOpportunity";s:22:"batchRemoveOpportunity";s:22:"batchRemoveOpportunity";s:15:"opportunityView";s:15:"opportunityView";s:11:"practicelib";s:11:"practiceLib";s:17:"createPracticelib";s:17:"createPracticeLib";s:15:"editPracticelib";s:15:"editPracticeLib";s:17:"deletePracticelib";s:17:"deletePracticeLib";s:15:"practiceLibView";s:15:"practiceLibView";s:8:"practice";s:8:"practice";s:14:"importPractice";s:14:"importPractice";s:16:"assignToPractice";s:16:"assignToPractice";s:21:"batchAssignToPractice";s:21:"batchAssignToPractice";s:15:"approvePractice";s:15:"approvePractice";s:20:"batchApprovePractice";s:20:"batchApprovePractice";s:12:"editPractice";s:12:"editPractice";s:14:"removePractice";s:14:"removePractice";s:19:"batchRemovePractice";s:19:"batchRemovePractice";s:12:"practiceView";s:12:"practiceView";s:12:"componentlib";s:12:"componentLib";s:18:"createComponentlib";s:18:"createComponentLib";s:16:"editComponentlib";s:16:"editComponentLib";s:18:"deleteComponentlib";s:18:"deleteComponentLib";s:16:"componentLibView";s:16:"componentLibView";s:9:"component";s:9:"component";s:15:"importComponent";s:15:"importComponent";s:17:"assignToComponent";s:17:"assignToComponent";s:22:"batchAssignToComponent";s:22:"batchAssignToComponent";s:16:"approveComponent";s:16:"approveComponent";s:21:"batchApproveComponent";s:21:"batchApproveComponent";s:13:"editComponent";s:13:"editComponent";s:15:"removeComponent";s:15:"removeComponent";s:20:"batchRemoveComponent";s:20:"batchRemoveComponent";s:13:"componentView";s:13:"componentView";s:12:"storylibSort";s:12:"storylibSort";s:11:"caselibSort";s:11:"caselibSort";s:12:"issuelibSort";s:12:"issuelibSort";s:11:"risklibSort";s:11:"risklibSort";s:18:"opportunitylibSort";s:18:"opportunitylibSort";s:15:"practicelibSort";s:15:"practicelibSort";s:16:"componentlibSort";s:16:"componentlibSort";}s:8:"storylib";a:0:{}s:8:"issuelib";a:0:{}s:7:"risklib";a:0:{}s:14:"opportunitylib";a:0:{}s:11:"practicelib";a:0:{}s:12:"componentlib";a:0:{}s:13:"projectreport";a:0:{}s:15:"projectresearch";a:0:{}s:16:"projectauditplan";a:0:{}s:18:"projectgapanalysis";a:0:{}s:7:"setting";a:1:{s:8:"xuanxuan";s:8:"xuanxuan";}s:2:"im";a:2:{s:18:"downloadXxdPackage";s:11:"downloadXXD";s:5:"debug";s:5:"debug";}s:6:"client";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}}';
$views['max']['lite'] = 'a:11:{i:0;s:2:"my";i:1;s:7:"project";i:2;s:9:"execution";i:3;s:6:"kanban";i:4;s:3:"doc";i:5;s:6:"system";i:6;s:5:"admin";i:7;s:8:"feedback";i:8;s:2:"oa";i:9;s:8:"workflow";i:10;s:9:"menuOrder";}';
$resources['max']['lite'] = 'a:54:{s:5:"index";a:1:{s:5:"index";s:5:"index";}s:2:"my";a:21:{s:5:"index";s:11:"indexAction";s:4:"todo";s:4:"todo";s:4:"work";s:10:"workAction";s:10:"contribute";s:16:"contributeAction";s:7:"project";s:7:"project";s:7:"profile";s:13:"profileAction";s:12:"uploadAvatar";s:12:"uploadAvatar";s:7:"dynamic";s:13:"dynamicAction";s:11:"editProfile";s:11:"editProfile";s:14:"changePassword";s:14:"changePassword";s:14:"manageContacts";s:14:"manageContacts";s:14:"deleteContacts";s:14:"deleteContacts";s:5:"score";s:5:"score";s:4:"team";s:4:"team";s:9:"execution";s:9:"execution";s:5:"story";s:5:"story";s:4:"task";s:4:"task";s:3:"doc";s:3:"doc";s:8:"calendar";s:14:"calendarAction";s:6:"effort";s:6:"effort";s:6:"review";s:6:"review";}s:4:"todo";a:17:{s:6:"create";s:6:"create";s:11:"createcycle";s:11:"createCycle";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:6:"export";s:6:"export";s:5:"start";s:5:"start";s:6:"finish";s:6:"finish";s:11:"batchFinish";s:11:"batchFinish";s:12:"import2Today";s:12:"import2Today";s:8:"assignTo";s:12:"assignAction";s:8:"activate";s:8:"activate";s:5:"close";s:5:"close";s:10:"batchClose";s:10:"batchClose";s:8:"calendar";s:8:"calendar";}s:9:"personnel";a:4:{s:10:"accessible";s:10:"accessible";s:6:"invest";s:6:"invest";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";}s:5:"story";a:26:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:10:"editAction";s:6:"export";s:12:"exportAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:6:"change";s:12:"changeAction";s:6:"review";s:12:"reviewAction";s:12:"submitReview";s:12:"submitReview";s:11:"batchReview";s:11:"batchReview";s:6:"recall";s:6:"recall";s:5:"close";s:11:"closeAction";s:10:"batchClose";s:10:"batchClose";s:15:"batchChangePlan";s:15:"batchChangePlan";s:16:"batchChangeStage";s:16:"batchChangeStage";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:8:"activate";s:14:"activateAction";s:5:"tasks";s:5:"tasks";s:6:"report";s:12:"reportAction";s:9:"linkStory";s:9:"linkStory";s:17:"batchChangeBranch";s:17:"batchChangeBranch";s:17:"batchChangeModule";s:17:"batchChangeModule";s:11:"batchToTask";s:11:"batchToTask";s:18:"processStoryChange";s:18:"processStoryChange";s:9:"batchEdit";s:9:"batchEdit";}s:7:"project";a:28:{s:5:"index";s:5:"index";s:6:"browse";s:6:"browse";s:6:"kanban";s:6:"kanban";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:5:"group";s:5:"group";s:11:"createGroup";s:11:"createGroup";s:10:"managePriv";s:10:"managePriv";s:13:"manageMembers";s:13:"manageMembers";s:17:"manageGroupMember";s:17:"manageGroupMember";s:9:"copyGroup";s:9:"copyGroup";s:9:"editGroup";s:9:"editGroup";s:5:"start";s:5:"start";s:7:"suspend";s:7:"suspend";s:5:"close";s:5:"close";s:8:"activate";s:8:"activate";s:11:"updateOrder";s:11:"updateOrder";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";s:15:"unbindWhitelist";s:15:"unbindWhitelist";s:7:"dynamic";s:7:"dynamic";s:9:"execution";s:9:"execution";s:6:"export";s:6:"export";s:12:"unlinkMember";s:12:"unlinkMember";s:4:"team";s:10:"teamAction";}s:12:"projectstory";a:5:{s:5:"story";s:5:"story";s:4:"view";s:4:"view";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";}s:9:"execution";a:37:{s:4:"view";s:4:"view";s:6:"create";s:10:"createExec";s:4:"edit";s:10:"editAction";s:9:"batchedit";s:15:"batchEditAction";s:5:"start";s:11:"startAction";s:8:"activate";s:14:"activateAction";s:6:"putoff";s:11:"delayAction";s:7:"suspend";s:13:"suspendAction";s:5:"close";s:11:"closeAction";s:6:"delete";s:8:"deleteAB";s:4:"task";s:4:"task";s:9:"grouptask";s:9:"groupTask";s:10:"importtask";s:10:"importTask";s:5:"story";s:5:"story";s:4:"team";s:10:"teamAction";s:7:"dynamic";s:7:"dynamic";s:13:"manageMembers";s:13:"manageMembers";s:12:"unlinkMember";s:12:"unlinkMember";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:11:"updateOrder";s:11:"updateOrder";s:10:"taskKanban";s:10:"taskKanban";s:4:"tree";s:10:"treeAction";s:8:"treeTask";s:12:"treeViewTask";s:9:"treeStory";s:13:"treeViewStory";s:3:"all";s:14:"allExecutionAB";s:6:"export";s:12:"exportAction";s:11:"storyKanban";s:11:"storyKanban";s:9:"storySort";s:9:"storySort";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";s:15:"unbindWhitelist";s:15:"unbindWhitelist";s:6:"kanban";s:6:"kanban";s:5:"gantt";s:10:"ganttchart";s:8:"calendar";s:8:"calendar";s:3:"doc";s:3:"doc";}s:4:"task";a:26:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";s:4:"edit";s:4:"edit";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:5:"start";s:11:"startAction";s:5:"pause";s:11:"pauseAction";s:7:"restart";s:13:"restartAction";s:6:"finish";s:12:"finishAction";s:6:"cancel";s:12:"cancelAction";s:5:"close";s:11:"closeAction";s:10:"batchClose";s:10:"batchClose";s:8:"activate";s:14:"activateAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:6:"export";s:12:"exportAction";s:18:"confirmStoryChange";s:18:"confirmStoryChange";s:14:"recordEstimate";s:20:"recordEstimateAction";s:12:"editEstimate";s:12:"editEstimate";s:14:"deleteEstimate";s:14:"deleteEstimate";s:6:"report";s:11:"reportChart";s:17:"batchChangeModule";s:17:"batchChangeModule";s:14:"exportTemplate";s:14:"exportTemplate";s:6:"import";s:6:"import";s:11:"batchCancel";s:11:"batchCancel";}s:3:"doc";a:26:{s:5:"index";s:5:"index";s:7:"mySpace";s:7:"mySpace";s:6:"myView";s:6:"myView";s:12:"myCollection";s:12:"myCollection";s:10:"myCreation";s:10:"myCreation";s:9:"createLib";s:9:"createLib";s:7:"editLib";s:7:"editLib";s:9:"deleteLib";s:9:"deleteLib";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:10:"deleteFile";s:10:"deleteFile";s:7:"collect";s:13:"collectAction";s:12:"projectSpace";s:12:"projectSpace";s:9:"showFiles";s:9:"showFiles";s:10:"addCatalog";s:10:"addCatalog";s:11:"editCatalog";s:11:"editCatalog";s:13:"deleteCatalog";s:13:"deleteCatalog";s:14:"displaySetting";s:14:"displaySetting";s:4:"diff";s:10:"diffAction";s:11:"mine2export";s:11:"mine2export";s:14:"product2export";s:14:"product2export";s:14:"project2export";s:14:"project2export";s:13:"custom2export";s:13:"custom2export";s:16:"execution2export";s:16:"execution2export";}s:7:"company";a:9:{s:6:"browse";s:6:"browse";s:4:"edit";s:4:"edit";s:7:"dynamic";s:7:"dynamic";s:4:"view";s:4:"view";s:4:"todo";s:11:"companyTodo";s:8:"calendar";s:14:"effortCalendar";s:7:"allTodo";s:7:"allTodo";s:6:"effort";s:13:"companyEffort";s:9:"alleffort";s:9:"allEffort";}s:4:"dept";a:5:{s:6:"browse";s:6:"browse";s:11:"updateOrder";s:11:"updateOrder";s:11:"manageChild";s:11:"manageChild";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:5:"group";a:9:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"copy";s:4:"copy";s:6:"delete";s:6:"delete";s:10:"managePriv";s:10:"managePriv";s:12:"manageMember";s:12:"manageMember";s:18:"manageProjectAdmin";s:18:"manageProjectAdmin";s:10:"manageView";s:10:"manageView";}s:4:"user";a:15:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:6:"unlock";s:6:"unlock";s:6:"delete";s:6:"delete";s:4:"todo";s:4:"todo";s:4:"task";s:4:"task";s:7:"dynamic";s:7:"dynamic";s:7:"profile";s:7:"profile";s:9:"batchEdit";s:9:"batchEdit";s:6:"unbind";s:6:"unbind";s:17:"setPublicTemplate";s:17:"setPublicTemplate";s:5:"story";s:5:"story";s:9:"execution";s:9:"execution";}s:5:"admin";a:6:{s:5:"index";s:5:"index";s:9:"checkWeak";s:9:"checkWeak";s:3:"sso";s:9:"ssoAction";s:8:"register";s:8:"register";s:8:"xuanxuan";s:8:"xuanxuan";s:4:"safe";s:9:"safeIndex";}s:9:"extension";a:10:{s:6:"browse";s:12:"browseAction";s:6:"obtain";s:6:"obtain";s:9:"structure";s:15:"structureAction";s:7:"install";s:7:"install";s:9:"uninstall";s:15:"uninstallAction";s:8:"activate";s:14:"activateAction";s:10:"deactivate";s:16:"deactivateAction";s:6:"upload";s:6:"upload";s:5:"erase";s:11:"eraseAction";s:7:"upgrade";s:7:"upgrade";}s:6:"custom";a:13:{s:3:"set";s:3:"set";s:7:"product";s:11:"productName";s:9:"execution";s:15:"executionCommon";s:8:"required";s:8:"required";s:7:"restore";s:7:"restore";s:4:"flow";s:4:"flow";s:8:"timezone";s:8:"timezone";s:15:"setStoryConcept";s:15:"setStoryConcept";s:16:"editStoryConcept";s:16:"editStoryConcept";s:18:"browseStoryConcept";s:18:"browseStoryConcept";s:17:"setDefaultConcept";s:17:"setDefaultConcept";s:18:"deleteStoryConcept";s:18:"deleteStoryConcept";s:11:"libreoffice";s:11:"libreOffice";}s:6:"action";a:6:{s:5:"trash";s:5:"trash";s:8:"undelete";s:14:"undeleteAction";s:7:"hideOne";s:13:"hideOneAction";s:7:"hideAll";s:7:"hideAll";s:7:"comment";s:7:"comment";s:11:"editComment";s:11:"editComment";}s:6:"search";a:7:{s:9:"buildForm";s:9:"buildForm";s:10:"buildQuery";s:10:"buildQuery";s:9:"saveQuery";s:9:"saveQuery";s:11:"deleteQuery";s:11:"deleteQuery";s:6:"select";s:6:"select";s:5:"index";s:5:"index";s:10:"buildIndex";s:10:"buildIndex";}s:4:"tree";a:9:{s:6:"browse";s:6:"browse";s:10:"browseTask";s:10:"browseTask";s:11:"updateOrder";s:11:"updateOrder";s:11:"manageChild";s:11:"manageChild";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:8:"editHost";s:8:"editHost";s:3:"fix";s:3:"fix";s:10:"browsehost";s:16:"groupMaintenance";}s:4:"file";a:5:{s:8:"download";s:8:"download";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:12:"uploadImages";s:12:"uploadImages";s:9:"setPublic";s:9:"setPublic";}s:4:"misc";a:1:{s:4:"ping";s:4:"ping";}s:6:"backup";a:7:{s:5:"index";s:5:"index";s:6:"backup";s:6:"backup";s:7:"restore";s:7:"restore";s:6:"delete";s:6:"delete";s:7:"setting";s:13:"settingAction";s:11:"rmPHPHeader";s:11:"rmPHPHeader";s:6:"change";s:6:"change";}s:4:"cron";a:7:{s:5:"index";s:5:"index";s:6:"turnon";s:6:"turnon";s:6:"create";s:12:"createAction";s:4:"edit";s:4:"edit";s:6:"toggle";s:6:"toggle";s:6:"delete";s:6:"delete";s:11:"openProcess";s:7:"restart";}s:7:"message";a:3:{s:5:"index";s:5:"index";s:7:"browser";s:7:"browser";s:7:"setting";s:7:"setting";}s:2:"im";a:2:{s:18:"downloadXxdPackage";s:11:"downloadXXD";s:5:"debug";s:5:"debug";}s:6:"client";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:7:"setting";a:1:{s:8:"xuanxuan";s:8:"xuanxuan";}s:6:"kanban";a:41:{s:5:"space";s:11:"spaceCommon";s:11:"createSpace";s:11:"createSpace";s:9:"editSpace";s:9:"editSpace";s:10:"closeSpace";s:10:"closeSpace";s:11:"deleteSpace";s:11:"deleteSpace";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:5:"close";s:5:"close";s:6:"delete";s:6:"delete";s:12:"createRegion";s:12:"createRegion";s:10:"editRegion";s:10:"editRegion";s:10:"sortRegion";s:10:"sortRegion";s:9:"sortGroup";s:9:"sortGroup";s:12:"deleteRegion";s:12:"deleteRegion";s:10:"createLane";s:10:"createLane";s:8:"sortLane";s:8:"sortLane";s:10:"deleteLane";s:10:"deleteLane";s:12:"createColumn";s:12:"createColumn";s:9:"setColumn";s:9:"setColumn";s:6:"setWIP";s:6:"setWIP";s:10:"sortColumn";s:10:"sortColumn";s:12:"deleteColumn";s:12:"deleteColumn";s:10:"createCard";s:10:"createCard";s:8:"editCard";s:8:"editCard";s:8:"viewCard";s:8:"viewCard";s:8:"sortCard";s:8:"sortCard";s:10:"deleteCard";s:10:"deleteCard";s:12:"assigntoCard";s:12:"assigntoCard";s:8:"moveCard";s:8:"moveCard";s:12:"setCardColor";s:12:"setCardColor";s:18:"viewArchivedColumn";s:18:"viewArchivedColumn";s:15:"batchCreateCard";s:15:"batchCreateCard";s:12:"editLaneName";s:12:"editLaneName";s:13:"editLaneColor";s:13:"editLaneColor";s:11:"splitColumn";s:11:"splitColumn";s:13:"archiveColumn";s:13:"archiveColumn";s:13:"restoreColumn";s:13:"restoreColumn";s:11:"archiveCard";s:11:"archiveCard";s:16:"viewArchivedCard";s:16:"viewArchivedCard";s:11:"restoreCard";s:11:"restoreCard";}s:4:"mail";a:10:{s:5:"index";s:5:"index";s:6:"detect";s:12:"detectAction";s:4:"edit";s:4:"edit";s:4:"save";s:10:"saveAction";s:4:"test";s:4:"test";s:5:"reset";s:11:"resetAction";s:6:"browse";s:6:"browse";s:6:"delete";s:6:"delete";s:11:"batchDelete";s:11:"batchDelete";s:6:"resend";s:12:"resendAction";}s:9:"datatable";a:1:{s:9:"setGlobal";s:9:"setGlobal";}s:7:"webhook";a:7:{s:6:"browse";s:4:"list";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:3:"log";s:9:"logAction";s:4:"bind";s:4:"bind";s:10:"chooseDept";s:10:"chooseDept";}s:6:"effort";a:8:{s:11:"batchCreate";s:11:"batchCreate";s:15:"createForObject";s:15:"createForObject";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:6:"export";s:6:"export";s:8:"calendar";s:8:"calendar";}s:8:"feedback";a:9:{s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"browse";s:6:"browse";s:4:"view";s:4:"view";s:7:"comment";s:7:"comment";s:6:"delete";s:6:"delete";s:5:"close";s:11:"closeAction";s:6:"export";s:12:"exportAction";s:8:"assignTo";s:12:"assignAction";}s:3:"faq";a:1:{s:6:"browse";s:6:"browse";}s:6:"ticket";a:11:{s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"browse";s:6:"browse";s:8:"assignTo";s:6:"assign";s:5:"close";s:5:"close";s:8:"activate";s:8:"activate";s:6:"delete";s:6:"delete";s:14:"exportTemplate";s:14:"exportTemplate";s:6:"import";s:6:"import";s:6:"export";s:12:"exportAction";}s:6:"attend";a:15:{s:10:"department";s:10:"department";s:7:"company";s:7:"company";s:12:"browseReview";s:12:"browseReview";s:6:"review";s:6:"review";s:6:"export";s:12:"exportAction";s:4:"stat";s:12:"reportAction";s:8:"saveStat";s:14:"saveStatAction";s:10:"exportStat";s:10:"exportStat";s:6:"detail";s:12:"detailAction";s:12:"exportDetail";s:12:"exportDetail";s:8:"settings";s:8:"settings";s:16:"personalSettings";s:16:"personalSettings";s:10:"setManager";s:10:"setManager";s:8:"personal";s:8:"personal";s:4:"edit";s:10:"editAction";}s:5:"leave";a:13:{s:12:"browseReview";s:12:"browseReview";s:7:"company";s:13:"companyAction";s:6:"review";s:12:"reviewAction";s:6:"export";s:12:"exportAction";s:11:"setReviewer";s:17:"setReviewerAction";s:14:"personalAnnual";s:14:"personalAnnual";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:10:"viewAction";s:12:"switchstatus";s:12:"switchstatus";s:4:"back";s:10:"backAction";}s:6:"makeup";a:11:{s:12:"browseReview";s:12:"browseReview";s:7:"company";s:13:"companyAction";s:6:"review";s:12:"reviewAction";s:6:"export";s:12:"exportAction";s:11:"setReviewer";s:17:"setReviewerAction";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:4:"view";s:10:"viewAction";s:6:"delete";s:12:"deleteAction";s:12:"switchstatus";s:12:"switchstatus";}s:8:"overtime";a:11:{s:12:"browseReview";s:12:"browseReview";s:7:"company";s:13:"companyAction";s:6:"review";s:12:"reviewAction";s:6:"export";s:12:"exportAction";s:11:"setReviewer";s:17:"setReviewerAction";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:4:"view";s:10:"viewAction";s:6:"delete";s:12:"deleteAction";s:12:"switchstatus";s:12:"switchstatus";}s:4:"lieu";a:10:{s:7:"company";s:13:"companyAction";s:12:"browseReview";s:18:"browseReviewAction";s:6:"review";s:12:"reviewAction";s:11:"setReviewer";s:17:"setReviewerAction";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:10:"viewAction";s:12:"switchstatus";s:12:"switchstatus";}s:8:"workflow";a:16:{s:10:"browseFlow";s:10:"browseFlow";s:8:"browseDB";s:8:"browseDB";s:6:"create";s:6:"create";s:4:"copy";s:4:"copy";s:4:"edit";s:4:"edit";s:6:"backup";s:6:"backup";s:7:"upgrade";s:13:"upgradeAction";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:9:"flowchart";s:9:"flowchart";s:2:"ui";s:2:"ui";s:7:"release";s:13:"releaseAction";s:10:"deactivate";s:16:"deactivateAction";s:8:"activate";s:14:"activateAction";s:5:"setJS";s:11:"setJSAction";s:6:"setCSS";s:12:"setCSSAction";}s:13:"workflowfield";a:11:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";s:6:"import";s:6:"import";s:10:"showImport";s:10:"showImport";s:14:"exportTemplate";s:14:"exportTemplate";s:8:"setValue";s:8:"setValue";s:9:"setExport";s:9:"setExport";s:9:"setSearch";s:9:"setSearch";}s:14:"workflowaction";a:10:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";s:15:"setVerification";s:15:"setVerification";s:9:"setNotice";s:9:"setNotice";s:5:"setJS";s:11:"setJSAction";s:6:"setCSS";s:12:"setCSSAction";}s:17:"workflowcondition";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:14:"workflowlayout";a:2:{s:5:"admin";s:5:"admin";s:5:"block";s:5:"block";}s:15:"workflowlinkage";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:12:"workflowhook";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:13:"workflowlabel";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";}s:16:"workflowrelation";a:1:{s:5:"admin";s:5:"admin";}s:14:"workflowreport";a:5:{s:6:"browse";s:4:"brow";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";}s:18:"workflowdatasource";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:12:"workflowrule";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";}s:3:"sms";a:3:{s:5:"index";s:5:"index";s:4:"test";s:4:"test";s:5:"reset";s:5:"reset";}}';
$views['biz']['rnd'] = 'a:17:{i:0;s:2:"my";i:1;s:7:"product";i:2;s:7:"project";i:3;s:9:"execution";i:4;s:2:"qa";i:5;s:6:"devops";i:6;s:6:"kanban";i:7;s:3:"doc";i:8;s:2:"bi";i:9;s:6:"system";i:10;s:5:"admin";i:11;s:9:"menuOrder";i:12;s:8:"feedback";i:13;s:8:"workflow";i:14;s:2:"oa";i:15;s:3:"ops";i:16;s:11:"traincourse";}';
$resources['biz']['rnd'] = 'a:154:{s:5:"index";a:1:{s:5:"index";s:5:"index";}s:2:"my";a:21:{s:5:"index";s:11:"indexAction";s:4:"todo";s:10:"todoAction";s:4:"work";s:10:"workAction";s:10:"contribute";s:16:"contributeAction";s:7:"project";s:7:"project";s:7:"profile";s:13:"profileAction";s:12:"uploadAvatar";s:12:"uploadAvatar";s:10:"preference";s:10:"preference";s:7:"dynamic";s:13:"dynamicAction";s:11:"editProfile";s:11:"editProfile";s:14:"changePassword";s:14:"changePassword";s:14:"manageContacts";s:14:"manageContacts";s:14:"deleteContacts";s:14:"deleteContacts";s:5:"score";s:5:"score";s:4:"team";s:4:"team";s:9:"execution";s:9:"execution";s:3:"doc";s:3:"doc";s:5:"audit";s:5:"audit";s:8:"calendar";s:14:"calendarAction";s:6:"effort";s:12:"effortAction";s:6:"review";s:6:"review";}s:4:"todo";a:17:{s:6:"create";s:6:"create";s:11:"createcycle";s:11:"createCycle";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:6:"export";s:6:"export";s:5:"start";s:5:"start";s:6:"finish";s:6:"finish";s:11:"batchFinish";s:11:"batchFinish";s:12:"import2Today";s:12:"import2Today";s:8:"assignTo";s:12:"assignAction";s:8:"activate";s:8:"activate";s:5:"close";s:5:"close";s:10:"batchClose";s:10:"batchClose";s:8:"calendar";s:8:"calendar";}s:9:"personnel";a:4:{s:10:"accessible";s:10:"accessible";s:6:"invest";s:6:"invest";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";}s:7:"product";a:19:{s:5:"index";s:11:"indexAction";s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:6:"delete";s:6:"delete";s:7:"dynamic";s:7:"dynamic";s:7:"project";s:7:"project";s:9:"dashboard";s:9:"dashboard";s:5:"close";s:11:"closeAction";s:11:"updateOrder";s:11:"orderAction";s:3:"all";s:4:"list";s:6:"kanban";s:6:"kanban";s:10:"manageLine";s:10:"manageLine";s:6:"export";s:12:"exportAction";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";s:15:"unbindWhitelist";s:15:"unbindWhitelist";}s:5:"story";a:31:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:10:"editAction";s:6:"export";s:12:"exportAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:6:"change";s:12:"changeAction";s:6:"review";s:12:"reviewAction";s:12:"submitReview";s:12:"submitReview";s:11:"batchReview";s:11:"batchReview";s:6:"recall";s:12:"recallAction";s:5:"close";s:11:"closeAction";s:10:"batchClose";s:10:"batchClose";s:15:"batchChangePlan";s:15:"batchChangePlan";s:16:"batchChangeStage";s:16:"batchChangeStage";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:8:"activate";s:14:"activateAction";s:5:"tasks";s:5:"tasks";s:4:"bugs";s:4:"bugs";s:5:"cases";s:5:"cases";s:6:"report";s:12:"reportAction";s:17:"batchChangeBranch";s:17:"batchChangeBranch";s:17:"batchChangeModule";s:17:"batchChangeModule";s:11:"batchToTask";s:11:"batchToTask";s:18:"processStoryChange";s:18:"processStoryChange";s:11:"linkStories";s:13:"linkStoriesAB";s:8:"relieved";s:13:"relievedTwins";s:9:"batchEdit";s:9:"batchEdit";s:6:"import";s:10:"importCase";s:14:"exportTemplate";s:14:"exportTemplate";}s:11:"productplan";a:17:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:7:"linkBug";s:7:"linkBug";s:9:"unlinkBug";s:9:"unlinkBug";s:14:"batchUnlinkBug";s:14:"batchUnlinkBug";s:9:"batchEdit";s:9:"batchEdit";s:5:"start";s:5:"start";s:6:"finish";s:6:"finish";s:5:"close";s:5:"close";s:8:"activate";s:8:"activate";s:17:"batchChangeStatus";s:17:"batchChangeStatus";}s:7:"release";a:14:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:6:"export";s:6:"export";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:7:"linkBug";s:7:"linkBug";s:9:"unlinkBug";s:9:"unlinkBug";s:14:"batchUnlinkBug";s:14:"batchUnlinkBug";s:12:"changeStatus";s:12:"changeStatus";s:6:"notify";s:6:"notify";}s:7:"project";a:33:{s:5:"index";s:5:"index";s:6:"browse";s:6:"browse";s:6:"kanban";s:6:"kanban";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:5:"group";s:5:"group";s:11:"createGroup";s:11:"createGroup";s:10:"managePriv";s:10:"managePriv";s:13:"manageMembers";s:13:"manageMembers";s:17:"manageGroupMember";s:17:"manageGroupMember";s:9:"copyGroup";s:9:"copyGroup";s:9:"editGroup";s:9:"editGroup";s:5:"start";s:5:"start";s:7:"suspend";s:7:"suspend";s:5:"close";s:5:"close";s:8:"activate";s:8:"activate";s:11:"updateOrder";s:11:"updateOrder";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";s:15:"unbindWhitelist";s:15:"unbindWhitelist";s:14:"manageProducts";s:14:"manageProducts";s:7:"dynamic";s:7:"dynamic";s:3:"bug";s:3:"bug";s:8:"testcase";s:8:"testcase";s:8:"testtask";s:8:"testtask";s:10:"testreport";s:10:"testreport";s:9:"execution";s:9:"execution";s:6:"export";s:6:"export";s:4:"team";s:10:"teamAction";s:12:"unlinkMember";s:18:"unlinkMemberAction";}s:11:"projectplan";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";}s:12:"projectstory";a:6:{s:5:"story";s:5:"story";s:4:"view";s:4:"view";s:9:"linkStory";s:9:"linkStory";s:17:"importplanstories";s:17:"importplanstories";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";}s:9:"execution";a:65:{s:4:"view";s:4:"view";s:6:"create";s:10:"createExec";s:4:"edit";s:10:"editAction";s:9:"batchedit";s:15:"batchEditAction";s:17:"batchchangestatus";s:17:"batchChangeStatus";s:5:"start";s:11:"startAction";s:8:"activate";s:14:"activateAction";s:6:"putoff";s:11:"delayAction";s:7:"suspend";s:13:"suspendAction";s:5:"close";s:11:"closeAction";s:6:"delete";s:8:"deleteAB";s:4:"task";s:4:"task";s:9:"grouptask";s:9:"groupTask";s:10:"importtask";s:10:"importTask";s:17:"importplanstories";s:17:"importPlanStories";s:9:"importBug";s:9:"importBug";s:5:"story";s:5:"story";s:5:"build";s:5:"build";s:8:"testcase";s:8:"testcase";s:3:"bug";s:3:"bug";s:8:"testtask";s:8:"testtask";s:10:"testreport";s:10:"testreport";s:4:"burn";s:4:"burn";s:11:"computeBurn";s:17:"computeBurnAction";s:3:"cfd";s:3:"CFD";s:10:"computeCFD";s:10:"computeCFD";s:8:"fixFirst";s:8:"fixFirst";s:4:"team";s:10:"teamAction";s:7:"dynamic";s:7:"dynamic";s:14:"manageProducts";s:14:"manageProducts";s:13:"manageMembers";s:13:"manageMembers";s:12:"unlinkMember";s:12:"unlinkMember";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:11:"updateOrder";s:11:"updateOrder";s:10:"taskKanban";s:10:"taskKanban";s:11:"printKanban";s:17:"printKanbanAction";s:4:"tree";s:10:"treeAction";s:8:"treeTask";s:12:"treeViewTask";s:9:"treeStory";s:13:"treeViewStory";s:3:"all";s:14:"allExecutionAB";s:6:"export";s:12:"exportAction";s:11:"storyKanban";s:11:"storyKanban";s:9:"storySort";s:9:"storySort";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";s:15:"unbindWhitelist";s:15:"unbindWhitelist";s:13:"storyEstimate";s:13:"storyEstimate";s:15:"executionkanban";s:12:"kanbanAction";s:6:"kanban";s:8:"RDKanban";s:9:"setKanban";s:9:"setKanban";s:3:"doc";s:3:"doc";s:9:"storyView";s:9:"storyView";s:8:"calendar";s:8:"calendar";s:14:"effortCalendar";s:14:"effortCalendar";s:6:"effort";s:12:"effortAction";s:10:"taskEffort";s:10:"taskEffort";s:17:"computeTaskEffort";s:17:"computeTaskEffort";s:14:"deleterelation";s:14:"deleterelation";s:16:"maintainrelation";s:12:"editrelation";s:8:"relation";s:12:"viewrelation";s:5:"gantt";s:10:"ganttchart";s:12:"ganttsetting";s:12:"ganttSetting";s:9:"ganttEdit";s:9:"ganttEdit";}s:6:"kanban";a:44:{s:5:"space";s:11:"spaceCommon";s:11:"createSpace";s:11:"createSpace";s:9:"editSpace";s:9:"editSpace";s:10:"closeSpace";s:10:"closeSpace";s:11:"deleteSpace";s:11:"deleteSpace";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:5:"close";s:5:"close";s:6:"delete";s:6:"delete";s:12:"createRegion";s:12:"createRegion";s:10:"editRegion";s:10:"editRegion";s:10:"sortRegion";s:10:"sortRegion";s:9:"sortGroup";s:9:"sortGroup";s:12:"deleteRegion";s:12:"deleteRegion";s:10:"createLane";s:10:"createLane";s:8:"sortLane";s:8:"sortLane";s:10:"deleteLane";s:10:"deleteLane";s:12:"createColumn";s:12:"createColumn";s:9:"setColumn";s:10:"editColumn";s:6:"setWIP";s:6:"setWIP";s:10:"sortColumn";s:10:"sortColumn";s:12:"deleteColumn";s:12:"deleteColumn";s:10:"createCard";s:10:"createCard";s:8:"editCard";s:8:"editCard";s:8:"viewCard";s:8:"viewCard";s:8:"sortCard";s:8:"sortCard";s:10:"deleteCard";s:10:"deleteCard";s:12:"assigntoCard";s:12:"assigntoCard";s:8:"moveCard";s:8:"moveCard";s:12:"setCardColor";s:12:"setCardColor";s:18:"viewArchivedColumn";s:18:"viewArchivedColumn";s:15:"batchCreateCard";s:15:"batchCreateCard";s:13:"activateSpace";s:13:"activateSpace";s:7:"setting";s:7:"setting";s:8:"activate";s:8:"activate";s:13:"editLaneColor";s:13:"editLaneColor";s:12:"editLaneName";s:12:"editLaneName";s:11:"splitColumn";s:11:"splitColumn";s:13:"archiveColumn";s:13:"archiveColumn";s:13:"restoreColumn";s:13:"restoreColumn";s:11:"archiveCard";s:11:"archiveCard";s:16:"viewArchivedCard";s:16:"viewArchivedCard";s:11:"restoreCard";s:11:"restoreCard";}s:11:"programplan";a:2:{s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";}s:4:"task";a:26:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";s:4:"edit";s:4:"edit";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:5:"start";s:11:"startAction";s:5:"pause";s:11:"pauseAction";s:7:"restart";s:13:"restartAction";s:6:"finish";s:12:"finishAction";s:6:"cancel";s:12:"cancelAction";s:5:"close";s:11:"closeAction";s:10:"batchClose";s:10:"batchClose";s:8:"activate";s:14:"activateAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:6:"export";s:12:"exportAction";s:18:"confirmStoryChange";s:18:"confirmStoryChange";s:14:"recordEstimate";s:20:"recordEstimateAction";s:12:"editEstimate";s:12:"editEstimate";s:14:"deleteEstimate";s:14:"deleteEstimate";s:6:"report";s:11:"reportChart";s:17:"batchChangeModule";s:17:"batchChangeModule";s:11:"batchCancel";s:11:"batchCancel";s:6:"import";s:6:"import";s:14:"exportTemplate";s:14:"exportTemplate";}s:5:"build";a:10:{s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:7:"linkBug";s:7:"linkBug";s:9:"unlinkBug";s:9:"unlinkBug";s:14:"batchUnlinkBug";s:14:"batchUnlinkBug";}s:6:"design";a:11:{s:6:"browse";s:6:"browse";s:4:"view";s:4:"view";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:4:"edit";s:8:"assignTo";s:8:"assignTo";s:6:"delete";s:6:"delete";s:10:"linkCommit";s:10:"linkCommit";s:10:"viewCommit";s:10:"viewCommit";s:12:"unlinkCommit";s:12:"unlinkCommit";s:8:"revision";s:8:"revision";}s:2:"qa";a:1:{s:5:"index";s:11:"indexAction";}s:3:"bug";a:26:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";s:10:"confirmBug";s:13:"confirmAction";s:12:"batchConfirm";s:12:"batchConfirm";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:7:"resolve";s:13:"resolveAction";s:12:"batchResolve";s:12:"batchResolve";s:10:"batchClose";s:10:"batchClose";s:13:"batchActivate";s:13:"batchActivate";s:8:"activate";s:14:"activateAction";s:5:"close";s:11:"closeAction";s:6:"report";s:12:"reportAction";s:6:"export";s:12:"exportAction";s:18:"confirmStoryChange";s:18:"confirmStoryChange";s:6:"delete";s:12:"deleteAction";s:8:"linkBugs";s:8:"linkBugs";s:17:"batchChangeModule";s:17:"batchChangeModule";s:17:"batchChangeBranch";s:17:"batchChangeBranch";s:15:"batchChangePlan";s:15:"batchChangePlan";s:6:"import";s:10:"importCase";s:14:"exportTemplate";s:14:"exportTemplate";}s:8:"testcase";a:39:{s:6:"browse";s:6:"browse";s:9:"groupCase";s:9:"groupCase";s:8:"zeroCase";s:8:"zeroCase";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:9:"createBug";s:9:"createBug";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:6:"delete";s:12:"deleteAction";s:6:"export";s:12:"exportAction";s:13:"confirmChange";s:13:"confirmChange";s:18:"confirmStoryChange";s:18:"confirmStoryChange";s:9:"batchEdit";s:9:"batchEdit";s:11:"batchDelete";s:11:"batchDelete";s:17:"batchChangeModule";s:17:"batchChangeModule";s:17:"batchChangeBranch";s:17:"batchChangeBranch";s:9:"linkCases";s:9:"linkCases";s:8:"linkBugs";s:8:"linkBugs";s:4:"bugs";s:4:"bugs";s:6:"review";s:6:"review";s:11:"batchReview";s:11:"batchReview";s:23:"batchConfirmStoryChange";s:23:"batchConfirmStoryChange";s:13:"importFromLib";s:13:"importFromLib";s:19:"batchCaseTypeChange";s:19:"batchCaseTypeChange";s:20:"confirmLibcaseChange";s:20:"confirmLibcaseChange";s:19:"ignoreLibcaseChange";s:19:"ignoreLibcaseChange";s:11:"importToLib";s:11:"importToLib";s:10:"automation";s:10:"automation";s:10:"showScript";s:10:"showScript";s:11:"createScene";s:11:"createScene";s:9:"editScene";s:9:"editScene";s:11:"deleteScene";s:11:"deleteScene";s:11:"changeScene";s:11:"changeScene";s:16:"batchChangeScene";s:16:"batchChangeScene";s:11:"updateOrder";s:11:"updateOrder";s:11:"importXmind";s:11:"importXmind";s:11:"exportXmind";s:11:"exportXmind";s:14:"exportTemplate";s:14:"exportTemplate";s:6:"import";s:16:"importCaseAction";}s:8:"testtask";a:24:{s:6:"create";s:6:"create";s:6:"browse";s:6:"browse";s:4:"view";s:10:"viewAction";s:5:"cases";s:11:"casesAction";s:9:"groupCase";s:9:"groupCase";s:4:"edit";s:4:"edit";s:5:"start";s:11:"startAction";s:8:"activate";s:14:"activateAction";s:5:"block";s:11:"blockAction";s:5:"close";s:11:"closeAction";s:6:"delete";s:6:"delete";s:11:"batchAssign";s:11:"batchAssign";s:8:"linkcase";s:8:"linkCase";s:10:"unlinkcase";s:13:"lblUnlinkCase";s:7:"runcase";s:10:"lblRunCase";s:7:"results";s:13:"resultsAction";s:16:"batchUnlinkCases";s:16:"batchUnlinkCases";s:6:"report";s:12:"reportAction";s:11:"browseUnits";s:11:"browseUnits";s:9:"unitCases";s:9:"unitCases";s:16:"importUnitResult";s:16:"importUnitResult";s:8:"batchRun";s:8:"batchRun";s:13:"runDeployCase";s:13:"runDeployCase";s:17:"deployCaseResults";s:17:"deployCaseResults";}s:9:"testsuite";a:8:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:8:"linkCase";s:8:"linkCase";s:10:"unlinkCase";s:16:"unlinkCaseAction";s:16:"batchUnlinkCases";s:16:"batchUnlinkCases";}s:10:"testreport";a:6:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:4:"edit";s:4:"edit";s:6:"export";s:12:"exportAction";}s:7:"caselib";a:10:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:10:"createCase";s:10:"createCase";s:15:"batchCreateCase";s:15:"batchCreateCase";s:14:"exportTemplate";s:14:"exportTemplate";s:6:"import";s:12:"importAction";s:10:"showImport";s:10:"showImport";}s:6:"zahost";a:8:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:11:"browseImage";s:11:"browseImage";s:13:"downloadImage";s:13:"downloadImage";s:14:"cancelDownload";s:6:"cancel";}s:6:"zanode";a:17:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:7:"destroy";s:7:"destroy";s:6:"reboot";s:6:"reboot";s:6:"resume";s:6:"resume";s:6:"getVNC";s:6:"getVNC";s:5:"start";s:4:"boot";s:5:"close";s:8:"shutdown";s:4:"view";s:4:"view";s:11:"createImage";s:11:"createImage";s:14:"browseSnapshot";s:14:"browseSnapshot";s:14:"createSnapshot";s:14:"createSnapshot";s:12:"editSnapshot";s:12:"editSnapshot";s:15:"restoreSnapshot";s:15:"restoreSnapshot";s:14:"deleteSnapshot";s:14:"deleteSnapshot";s:7:"suspend";s:7:"suspend";}s:3:"doc";a:30:{s:5:"index";s:5:"index";s:7:"mySpace";s:7:"mySpace";s:6:"myView";s:6:"myView";s:12:"myCollection";s:12:"myCollection";s:10:"myCreation";s:10:"myCreation";s:8:"myEdited";s:8:"myEdited";s:9:"createLib";s:9:"createLib";s:7:"editLib";s:7:"editLib";s:9:"deleteLib";s:9:"deleteLib";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:10:"deleteFile";s:10:"deleteFile";s:7:"collect";s:13:"collectAction";s:12:"productSpace";s:12:"productSpace";s:12:"projectSpace";s:12:"projectSpace";s:9:"teamSpace";s:9:"teamSpace";s:9:"showFiles";s:9:"showFiles";s:10:"addCatalog";s:10:"addCatalog";s:11:"editCatalog";s:11:"editCatalog";s:11:"sortCatalog";s:11:"sortCatalog";s:13:"deleteCatalog";s:13:"deleteCatalog";s:14:"displaySetting";s:14:"displaySetting";s:4:"diff";s:10:"diffAction";s:11:"mine2export";s:11:"mine2export";s:14:"product2export";s:14:"product2export";s:14:"project2export";s:14:"project2export";s:13:"custom2export";s:13:"custom2export";s:16:"execution2export";s:16:"execution2export";}s:6:"screen";a:9:{s:6:"browse";s:6:"browse";s:4:"view";s:4:"view";s:6:"create";s:6:"create";s:4:"edit";s:10:"editScreen";s:6:"design";s:12:"designScreen";s:7:"publish";s:13:"publishScreen";s:6:"delete";s:12:"deleteScreen";s:10:"annualData";s:10:"annualData";s:13:"allAnnualData";s:13:"allAnnualData";}s:5:"pivot";a:23:{s:6:"browse";s:12:"browseAction";s:7:"preview";s:7:"preview";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"design";s:6:"design";s:6:"delete";s:6:"delete";s:6:"export";s:6:"export";s:11:"showProduct";s:11:"showProduct";s:11:"showProject";s:11:"showProject";s:16:"projectDeviation";s:16:"projectDeviation";s:14:"productSummary";s:14:"productSummary";s:9:"bugCreate";s:9:"bugCreate";s:9:"bugAssign";s:9:"bugAssign";s:8:"workload";s:8:"workload";s:8:"casesrun";s:8:"casesrun";s:14:"storyLinkedBug";s:14:"storyLinkedBug";s:8:"testcase";s:8:"testcase";s:5:"build";s:5:"build";s:11:"workSummary";s:11:"workSummary";s:13:"productInvest";s:13:"productInvest";s:10:"bugSummary";s:10:"bugSummary";s:16:"bugAssignSummary";s:16:"bugAssignSummary";s:17:"workAssignSummary";s:17:"workAssignSummary";}s:5:"chart";a:7:{s:6:"browse";s:12:"browseAction";s:7:"preview";s:7:"preview";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"design";s:6:"design";s:6:"delete";s:6:"delete";s:6:"export";s:6:"export";}s:8:"dataview";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:5:"query";s:6:"design";s:6:"delete";s:6:"delete";}s:9:"dimension";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:6:"report";a:1:{s:6:"export";s:6:"export";}s:7:"company";a:9:{s:6:"browse";s:6:"browse";s:4:"edit";s:4:"edit";s:7:"dynamic";s:7:"dynamic";s:4:"view";s:4:"view";s:4:"todo";s:11:"companyTodo";s:8:"calendar";s:14:"effortCalendar";s:7:"allTodo";s:7:"allTodo";s:6:"effort";s:13:"companyEffort";s:9:"alleffort";s:9:"allEffort";}s:4:"dept";a:5:{s:6:"browse";s:6:"browse";s:11:"updateOrder";s:11:"updateOrder";s:11:"manageChild";s:17:"manageChildAction";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:5:"group";a:9:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"copy";s:4:"copy";s:6:"delete";s:6:"delete";s:10:"managePriv";s:10:"managePriv";s:12:"manageMember";s:12:"manageMember";s:18:"manageProjectAdmin";s:18:"manageProjectAdmin";s:10:"manageView";s:10:"manageView";}s:4:"user";a:25:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:6:"unlock";s:6:"unlock";s:6:"delete";s:6:"delete";s:4:"todo";s:4:"todo";s:4:"task";s:4:"task";s:3:"bug";s:3:"bug";s:7:"dynamic";s:7:"dynamic";s:7:"profile";s:7:"profile";s:9:"batchEdit";s:9:"batchEdit";s:6:"unbind";s:6:"unbind";s:17:"setPublicTemplate";s:17:"setPublicTemplate";s:6:"export";s:6:"export";s:14:"exportTemplate";s:14:"exportTemplate";s:5:"story";s:5:"story";s:8:"testTask";s:8:"testTask";s:8:"testCase";s:8:"testCase";s:9:"execution";s:9:"execution";s:14:"effortcalendar";s:14:"effortcalendar";s:12:"todocalendar";s:12:"todocalendar";s:6:"effort";s:6:"effort";s:6:"import";s:6:"import";s:10:"importldap";s:10:"importLDAP";}s:5:"admin";a:8:{s:5:"index";s:5:"index";s:9:"checkWeak";s:9:"checkWeak";s:3:"sso";s:9:"ssoAction";s:8:"register";s:8:"register";s:8:"xuanxuan";s:8:"xuanxuan";s:15:"resetPWDSetting";s:15:"resetPWDSetting";s:11:"tableEngine";s:11:"tableEngine";s:4:"safe";s:9:"safeIndex";}s:5:"stage";a:7:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:4:"edit";s:7:"setType";s:7:"setType";s:6:"delete";s:6:"delete";s:10:"plusBrowse";s:10:"plusBrowse";}s:9:"extension";a:10:{s:6:"browse";s:12:"browseAction";s:6:"obtain";s:6:"obtain";s:9:"structure";s:15:"structureAction";s:7:"install";s:7:"install";s:9:"uninstall";s:15:"uninstallAction";s:8:"activate";s:14:"activateAction";s:10:"deactivate";s:16:"deactivateAction";s:6:"upload";s:6:"upload";s:5:"erase";s:11:"eraseAction";s:7:"upgrade";s:7:"upgrade";}s:6:"custom";a:18:{s:3:"set";s:3:"set";s:7:"product";s:11:"productName";s:9:"execution";s:15:"executionCommon";s:8:"required";s:8:"required";s:7:"restore";s:7:"restore";s:4:"flow";s:4:"flow";s:8:"timezone";s:8:"timezone";s:15:"setStoryConcept";s:15:"setStoryConcept";s:16:"editStoryConcept";s:16:"editStoryConcept";s:18:"browseStoryConcept";s:18:"browseStoryConcept";s:17:"setDefaultConcept";s:17:"setDefaultConcept";s:18:"deleteStoryConcept";s:18:"deleteStoryConcept";s:6:"kanban";s:6:"kanban";s:4:"code";s:4:"code";s:5:"hours";s:5:"hours";s:7:"percent";s:7:"percent";s:13:"limitTaskDate";s:19:"limitTaskDateAction";s:11:"libreoffice";s:11:"libreOffice";}s:6:"action";a:6:{s:5:"trash";s:11:"trashAction";s:8:"undelete";s:14:"undeleteAction";s:7:"hideOne";s:13:"hideOneAction";s:7:"hideAll";s:7:"hideAll";s:7:"comment";s:7:"comment";s:11:"editComment";s:11:"editComment";}s:4:"mail";a:10:{s:5:"index";s:5:"index";s:6:"detect";s:12:"detectAction";s:4:"edit";s:4:"edit";s:4:"save";s:10:"saveAction";s:4:"test";s:4:"test";s:5:"reset";s:11:"resetAction";s:6:"browse";s:6:"browse";s:6:"delete";s:6:"delete";s:11:"batchDelete";s:11:"batchDelete";s:6:"resend";s:12:"resendAction";}s:3:"sms";a:3:{s:5:"index";s:5:"index";s:4:"test";s:4:"test";s:5:"reset";s:5:"reset";}s:3:"svn";a:3:{s:4:"diff";s:4:"diff";s:3:"cat";s:3:"cat";s:7:"apiSync";s:7:"apiSync";}s:3:"git";a:3:{s:4:"diff";s:4:"diff";s:3:"cat";s:3:"cat";s:7:"apiSync";s:7:"apiSync";}s:6:"search";a:7:{s:9:"buildForm";s:9:"buildForm";s:10:"buildQuery";s:10:"buildQuery";s:9:"saveQuery";s:9:"saveQuery";s:11:"deleteQuery";s:11:"deleteQuery";s:6:"select";s:6:"select";s:5:"index";s:5:"index";s:10:"buildIndex";s:10:"buildIndex";}s:4:"tree";a:10:{s:6:"browse";s:6:"browse";s:10:"browseTask";s:10:"browseTask";s:11:"updateOrder";s:11:"updateOrder";s:11:"manageChild";s:11:"manageChild";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:8:"editHost";s:8:"editHost";s:3:"fix";s:3:"fix";s:10:"browsehost";s:16:"groupMaintenance";s:11:"browsegroup";s:11:"browsegroup";}s:3:"api";a:22:{s:5:"index";s:5:"index";s:9:"createLib";s:9:"createLib";s:7:"editLib";s:7:"editLib";s:9:"deleteLib";s:9:"deleteLib";s:13:"createRelease";s:13:"createRelease";s:8:"releases";s:8:"releases";s:13:"deleteRelease";s:13:"deleteRelease";s:6:"struct";s:6:"struct";s:12:"createStruct";s:12:"createStruct";s:10:"editStruct";s:10:"editStruct";s:12:"deleteStruct";s:12:"deleteStruct";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:10:"addCatalog";s:10:"addCatalog";s:11:"editCatalog";s:11:"editCatalog";s:11:"sortCatalog";s:11:"sortCatalog";s:13:"deleteCatalog";s:13:"deleteCatalog";s:8:"getModel";s:8:"getModel";s:5:"debug";s:5:"debug";s:3:"sql";s:3:"sql";s:6:"export";s:6:"export";}s:4:"file";a:5:{s:8:"download";s:8:"download";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:12:"uploadImages";s:12:"uploadImages";s:9:"setPublic";s:9:"setPublic";}s:4:"misc";a:1:{s:4:"ping";s:4:"ping";}s:6:"backup";a:7:{s:5:"index";s:5:"index";s:6:"backup";s:6:"backup";s:7:"restore";s:13:"restoreAction";s:6:"delete";s:6:"delete";s:7:"setting";s:13:"settingAction";s:11:"rmPHPHeader";s:11:"rmPHPHeader";s:6:"change";s:6:"change";}s:4:"cron";a:7:{s:5:"index";s:5:"index";s:6:"turnon";s:6:"turnon";s:6:"create";s:12:"createAction";s:4:"edit";s:4:"edit";s:6:"toggle";s:6:"toggle";s:6:"delete";s:6:"delete";s:11:"openProcess";s:7:"restart";}s:3:"dev";a:2:{s:3:"api";s:3:"api";s:2:"db";s:2:"db";}s:6:"editor";a:6:{s:5:"index";s:5:"index";s:6:"extend";s:6:"extend";s:4:"edit";s:4:"edit";s:7:"newPage";s:7:"newPage";s:4:"save";s:4:"save";s:6:"delete";s:6:"delete";}s:7:"message";a:3:{s:5:"index";s:5:"index";s:7:"browser";s:7:"browser";s:7:"setting";s:7:"setting";}s:6:"gitlab";a:30:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:11:"importIssue";s:11:"importIssue";s:6:"delete";s:6:"delete";s:8:"bindUser";s:8:"bindUser";s:13:"browseProject";s:13:"browseProject";s:13:"createProject";s:13:"createProject";s:11:"editProject";s:11:"editProject";s:13:"deleteProject";s:13:"deleteProject";s:11:"browseGroup";s:11:"browseGroup";s:11:"createGroup";s:11:"createGroup";s:9:"editGroup";s:9:"editGroup";s:11:"deleteGroup";s:11:"deleteGroup";s:18:"manageGroupMembers";s:18:"manageGroupMembers";s:10:"browseUser";s:10:"browseUser";s:10:"createUser";s:10:"createUser";s:8:"editUser";s:8:"editUser";s:10:"deleteUser";s:10:"deleteUser";s:12:"createBranch";s:12:"createBranch";s:12:"browseBranch";s:12:"browseBranch";s:7:"webhook";s:7:"webhook";s:13:"createWebhook";s:13:"createWebhook";s:20:"manageProjectMembers";s:20:"manageProjectMembers";s:16:"manageBranchPriv";s:16:"browseBranchPriv";s:13:"manageTagPriv";s:13:"browseTagPriv";s:9:"browseTag";s:9:"browseTag";s:9:"createTag";s:9:"createTag";s:9:"deleteTag";s:9:"deleteTag";}s:2:"mr";a:16:{s:6:"create";s:6:"create";s:6:"browse";s:12:"browseAction";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:10:"viewAction";s:6:"accept";s:6:"accept";s:4:"diff";s:8:"viewDiff";s:4:"link";s:8:"linkList";s:9:"linkStory";s:9:"linkStory";s:7:"linkBug";s:7:"linkBug";s:8:"linkTask";s:8:"linkTask";s:6:"unlink";s:6:"unlink";s:8:"approval";s:8:"approval";s:5:"close";s:5:"close";s:6:"reopen";s:6:"reopen";s:9:"addReview";s:9:"addReview";}s:3:"app";a:1:{s:10:"serverlink";s:10:"serverLink";}s:4:"gogs";a:6:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:8:"bindUser";s:8:"bindUser";}s:5:"gitea";a:6:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:8:"bindUser";s:8:"bindUser";}s:7:"holiday";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:6:"import";s:12:"importAction";}s:8:"workflow";a:16:{s:10:"browseFlow";s:10:"browseFlow";s:8:"browseDB";s:8:"browseDB";s:6:"create";s:6:"create";s:4:"copy";s:4:"copy";s:4:"edit";s:4:"edit";s:6:"backup";s:6:"backup";s:7:"upgrade";s:13:"upgradeAction";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:9:"flowchart";s:9:"flowchart";s:2:"ui";s:2:"ui";s:7:"release";s:7:"release";s:10:"deactivate";s:10:"deactivate";s:8:"activate";s:8:"activate";s:5:"setJS";s:5:"setJS";s:6:"setCSS";s:6:"setCSS";}s:13:"workflowfield";a:11:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";s:6:"import";s:6:"import";s:10:"showImport";s:10:"showImport";s:14:"exportTemplate";s:14:"exportTemplate";s:8:"setValue";s:8:"setValue";s:9:"setExport";s:9:"setExport";s:9:"setSearch";s:9:"setSearch";}s:14:"workflowaction";a:10:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";s:15:"setVerification";s:15:"setVerification";s:9:"setNotice";s:9:"setNotice";s:5:"setJS";s:5:"setJS";s:6:"setCSS";s:6:"setCSS";}s:14:"workflowlayout";a:2:{s:5:"admin";s:5:"admin";s:5:"block";s:5:"block";}s:17:"workflowcondition";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:15:"workflowlinkage";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:12:"workflowhook";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:13:"workflowlabel";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";}s:16:"workflowrelation";a:1:{s:5:"admin";s:5:"admin";}s:14:"workflowreport";a:5:{s:6:"browse";s:4:"brow";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";}s:18:"workflowdatasource";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:12:"workflowrule";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";}s:12:"projectbuild";a:11:{s:6:"browse";s:6:"browse";s:4:"view";s:4:"view";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:7:"linkBug";s:7:"linkBug";s:9:"unlinkBug";s:9:"unlinkBug";s:14:"batchUnlinkBug";s:14:"batchUnlinkBug";}s:14:"projectrelease";a:14:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:6:"export";s:6:"export";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:7:"linkBug";s:7:"linkBug";s:9:"unlinkBug";s:9:"unlinkBug";s:14:"batchUnlinkBug";s:14:"batchUnlinkBug";s:12:"changeStatus";s:12:"changeStatus";s:6:"notify";s:6:"notify";}s:11:"stakeholder";a:9:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:10:"viewAction";s:11:"communicate";s:11:"communicate";s:6:"expect";s:6:"expect";s:9:"userIssue";s:9:"userIssue";}s:6:"branch";a:8:{s:6:"manage";s:6:"manage";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:5:"close";s:11:"closeAction";s:8:"activate";s:14:"activateAction";s:4:"sort";s:4:"sort";s:9:"batchEdit";s:9:"batchEdit";s:11:"mergeBranch";s:17:"mergeBranchAction";}s:4:"repo";a:27:{s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:6:"delete";s:6:"delete";s:14:"showSyncCommit";s:14:"showSyncCommit";s:8:"maintain";s:8:"maintain";s:6:"browse";s:12:"browseAction";s:4:"view";s:4:"view";s:4:"diff";s:10:"diffAction";s:3:"log";s:3:"log";s:8:"revision";s:14:"revisionAction";s:5:"blame";s:11:"blameAction";s:8:"download";s:14:"downloadAction";s:8:"setRules";s:8:"setRules";s:15:"apiGetRepoByUrl";s:15:"apiGetRepoByUrl";s:12:"downloadCode";s:12:"downloadCode";s:9:"linkStory";s:9:"linkStory";s:7:"linkBug";s:7:"linkBug";s:8:"linkTask";s:8:"linkTask";s:6:"unlink";s:6:"unlink";s:6:"import";s:12:"importAction";s:6:"review";s:12:"reviewAction";s:6:"addBug";s:6:"addBug";s:7:"editBug";s:7:"editBug";s:9:"deleteBug";s:9:"deleteBug";s:10:"addComment";s:10:"addComment";s:11:"editComment";s:11:"editComment";s:13:"deleteComment";s:13:"deleteComment";}s:2:"ci";a:2:{s:12:"commitResult";s:12:"commitResult";s:18:"checkCompileStatus";s:18:"checkCompileStatus";}s:7:"compile";a:3:{s:6:"browse";s:6:"browse";s:4:"logs";s:4:"logs";s:11:"syncCompile";s:11:"syncCompile";}s:7:"jenkins";a:4:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:12:"deleteAction";}s:3:"job";a:6:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"exec";s:4:"exec";s:4:"view";s:4:"view";}s:9:"datatable";a:1:{s:9:"setGlobal";s:9:"setGlobal";}s:9:"sonarqube";a:10:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:13:"browseProject";s:13:"browseProject";s:13:"createProject";s:13:"createProject";s:13:"deleteProject";s:13:"deleteProject";s:7:"execJob";s:7:"execJob";s:10:"reportView";s:10:"reportView";s:11:"browseIssue";s:11:"browseIssue";}s:7:"webhook";a:7:{s:6:"browse";s:4:"list";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:3:"log";s:9:"logAction";s:4:"bind";s:4:"bind";s:10:"chooseDept";s:10:"chooseDept";}s:7:"contact";a:0:{}s:18:"programstakeholder";a:0:{}s:12:"researchplan";a:0:{}s:14:"workestimation";a:0:{}s:11:"gapanalysis";a:0:{}s:13:"executionview";a:0:{}s:11:"managespace";a:0:{}s:10:"systemteam";a:0:{}s:14:"systemschedule";a:0:{}s:12:"systemeffort";a:0:{}s:13:"systemdynamic";a:0:{}s:13:"systemcompany";a:0:{}s:8:"pipeline";a:0:{}s:13:"devopssetting";a:0:{}s:13:"featureswitch";a:0:{}s:10:"importdata";a:0:{}s:13:"systemsetting";a:0:{}s:11:"staffmanage";a:0:{}s:11:"modelconfig";a:0:{}s:13:"featureconfig";a:0:{}s:11:"doctemplate";a:0:{}s:13:"notifysetting";a:0:{}s:8:"bidesign";a:0:{}s:16:"personalsettings";a:0:{}s:15:"projectsettings";a:0:{}s:10:"dataaccess";a:0:{}s:14:"executiongantt";a:0:{}s:15:"executionkanban";a:0:{}s:13:"executionburn";a:0:{}s:12:"executioncfd";a:0:{}s:14:"executionstory";a:0:{}s:11:"executionqa";a:0:{}s:17:"executionsettings";a:0:{}s:14:"generalcomment";a:0:{}s:11:"generalping";a:0:{}s:15:"generaltemplate";a:0:{}s:13:"generaleffort";a:0:{}s:15:"productsettings";a:0:{}s:13:"projectreview";a:0:{}s:12:"projecttrack";a:0:{}s:9:"projectqa";a:0:{}s:6:"effort";a:8:{s:8:"calendar";s:14:"calendarAction";s:11:"batchCreate";s:11:"batchCreate";s:15:"createForObject";s:15:"createForObject";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:6:"export";s:12:"exportAction";}s:4:"ldap";a:1:{s:3:"set";s:6:"common";}s:8:"feedback";a:29:{s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:10:"editOthers";s:10:"editOthers";s:9:"adminView";s:9:"adminView";s:5:"admin";s:5:"admin";s:8:"assignTo";s:12:"assignAction";s:6:"toTask";s:6:"toTask";s:6:"toTodo";s:6:"toTodo";s:5:"toBug";s:5:"toBug";s:7:"toStory";s:7:"toStory";s:8:"toTicket";s:8:"toTicket";s:6:"review";s:12:"reviewAction";s:7:"comment";s:7:"comment";s:5:"reply";s:5:"reply";s:3:"ask";s:3:"ask";s:5:"close";s:11:"closeAction";s:6:"delete";s:6:"delete";s:8:"activate";s:8:"activate";s:6:"export";s:12:"exportAction";s:9:"batchEdit";s:9:"batchEdit";s:10:"batchClose";s:10:"batchClose";s:11:"batchReview";s:11:"batchReview";s:13:"batchAssignTo";s:13:"batchAssignTo";s:8:"products";s:8:"products";s:13:"manageProduct";s:13:"manageProduct";s:6:"import";s:6:"import";s:14:"exportTemplate";s:14:"exportTemplate";s:11:"syncProduct";s:11:"syncProduct";s:14:"productSetting";s:14:"productSetting";}s:3:"faq";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:6:"ticket";a:21:{s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"browse";s:6:"browse";s:8:"assignTo";s:6:"assign";s:9:"createBug";s:9:"createBug";s:11:"createStory";s:11:"createStory";s:5:"start";s:5:"start";s:6:"finish";s:6:"finish";s:5:"close";s:5:"close";s:8:"activate";s:8:"activate";s:6:"delete";s:6:"delete";s:14:"exportTemplate";s:14:"exportTemplate";s:6:"import";s:6:"import";s:6:"export";s:12:"exportAction";s:9:"batchEdit";s:9:"batchEdit";s:10:"batchClose";s:10:"batchClose";s:13:"batchActivate";s:13:"batchActivate";s:11:"batchFinish";s:11:"batchFinish";s:13:"batchAssignTo";s:13:"batchAssignTo";s:11:"syncProduct";s:11:"syncProduct";}s:12:"feedbackpriv";a:0:{}s:6:"attend";a:15:{s:10:"department";s:10:"department";s:7:"company";s:7:"company";s:12:"browseReview";s:12:"browseReview";s:6:"review";s:6:"review";s:6:"export";s:12:"exportAction";s:4:"stat";s:12:"reportAction";s:8:"saveStat";s:14:"saveStatAction";s:10:"exportStat";s:10:"exportStat";s:6:"detail";s:12:"detailAction";s:12:"exportDetail";s:12:"exportDetail";s:8:"settings";s:8:"settings";s:16:"personalSettings";s:16:"personalSettings";s:10:"setManager";s:10:"setManager";s:8:"personal";s:8:"personal";s:4:"edit";s:10:"editAction";}s:5:"leave";a:13:{s:12:"browseReview";s:12:"browseReview";s:7:"company";s:13:"companyAction";s:6:"review";s:12:"reviewAction";s:6:"export";s:12:"exportAction";s:11:"setReviewer";s:17:"setReviewerAction";s:14:"personalAnnual";s:14:"personalAnnual";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:10:"viewAction";s:12:"switchstatus";s:12:"switchstatus";s:4:"back";s:10:"backAction";}s:6:"makeup";a:11:{s:12:"browseReview";s:12:"browseReview";s:7:"company";s:13:"companyAction";s:6:"review";s:12:"reviewAction";s:6:"export";s:12:"exportAction";s:11:"setReviewer";s:17:"setReviewerAction";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:4:"view";s:10:"viewAction";s:6:"delete";s:12:"deleteAction";s:12:"switchstatus";s:12:"switchstatus";}s:8:"overtime";a:11:{s:12:"browseReview";s:12:"browseReview";s:7:"company";s:13:"companyAction";s:6:"review";s:12:"reviewAction";s:6:"export";s:12:"exportAction";s:11:"setReviewer";s:17:"setReviewerAction";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:4:"view";s:10:"viewAction";s:6:"delete";s:12:"deleteAction";s:12:"switchstatus";s:12:"switchstatus";}s:4:"lieu";a:10:{s:7:"company";s:13:"companyAction";s:12:"browseReview";s:18:"browseReviewAction";s:6:"review";s:12:"reviewAction";s:11:"setReviewer";s:17:"setReviewerAction";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:10:"viewAction";s:12:"switchstatus";s:12:"switchstatus";}s:14:"officeapproval";a:0:{}s:13:"officesetting";a:0:{}s:14:"datapermission";a:0:{}s:12:"officeexport";a:0:{}s:3:"ops";a:2:{s:5:"index";s:5:"index";s:7:"setting";s:7:"setting";}s:4:"host";a:7:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:10:"editAction";s:4:"view";s:4:"view";s:6:"delete";s:12:"deleteAction";s:12:"changeStatus";s:12:"changeStatus";s:7:"treemap";s:7:"treemap";}s:10:"serverroom";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:10:"editAction";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";}s:7:"account";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:10:"editAction";s:4:"view";s:4:"view";s:6:"delete";s:12:"deleteAction";}s:6:"domain";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:10:"editAction";s:4:"view";s:4:"view";s:6:"delete";s:12:"deleteAction";}s:7:"service";a:6:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:6:"manage";s:6:"manage";}s:6:"deploy";a:20:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:8:"activate";s:14:"activateAction";s:6:"finish";s:12:"finishAction";s:5:"scope";s:5:"scope";s:11:"manageScope";s:11:"manageScope";s:4:"view";s:4:"view";s:5:"cases";s:11:"casesAction";s:9:"linkCases";s:9:"linkCases";s:10:"unlinkCase";s:10:"unlinkCase";s:16:"batchUnlinkCases";s:16:"batchUnlinkCases";s:5:"steps";s:5:"steps";s:10:"manageStep";s:10:"manageStep";s:10:"finishStep";s:10:"finishStep";s:8:"assignTo";s:12:"assignAction";s:8:"viewStep";s:8:"viewStep";s:8:"editStep";s:8:"editStep";s:10:"deleteStep";s:10:"deleteStep";}s:10:"conference";a:1:{s:5:"admin";s:5:"admin";}s:11:"traincourse";a:8:{s:6:"browse";s:12:"browseAction";s:5:"admin";s:11:"adminAction";s:12:"deleteCourse";s:12:"deleteCourse";s:12:"changeStatus";s:12:"changeStatus";s:12:"uploadCourse";s:12:"uploadCourse";s:11:"batchImport";s:11:"batchImport";s:10:"viewCourse";s:10:"viewCourse";s:11:"viewChapter";s:11:"viewChapter";}s:7:"setting";a:1:{s:8:"xuanxuan";s:8:"xuanxuan";}s:2:"im";a:2:{s:18:"downloadXxdPackage";s:11:"downloadXXD";s:5:"debug";s:5:"debug";}s:6:"client";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}}';
$views['biz']['lite'] = 'a:11:{i:0;s:2:"my";i:1;s:7:"project";i:2;s:9:"execution";i:3;s:6:"kanban";i:4;s:3:"doc";i:5;s:6:"system";i:6;s:5:"admin";i:7;s:8:"feedback";i:8;s:2:"oa";i:9;s:8:"workflow";i:10;s:9:"menuOrder";}';
$resources['biz']['lite'] = 'a:54:{s:5:"index";a:1:{s:5:"index";s:5:"index";}s:2:"my";a:21:{s:5:"index";s:11:"indexAction";s:4:"todo";s:4:"todo";s:4:"work";s:10:"workAction";s:10:"contribute";s:16:"contributeAction";s:7:"project";s:7:"project";s:7:"profile";s:13:"profileAction";s:12:"uploadAvatar";s:12:"uploadAvatar";s:7:"dynamic";s:13:"dynamicAction";s:11:"editProfile";s:11:"editProfile";s:14:"changePassword";s:14:"changePassword";s:14:"manageContacts";s:14:"manageContacts";s:14:"deleteContacts";s:14:"deleteContacts";s:5:"score";s:5:"score";s:4:"team";s:4:"team";s:9:"execution";s:9:"execution";s:5:"story";s:5:"story";s:4:"task";s:4:"task";s:3:"doc";s:3:"doc";s:8:"calendar";s:14:"calendarAction";s:6:"effort";s:6:"effort";s:6:"review";s:6:"review";}s:4:"todo";a:17:{s:6:"create";s:6:"create";s:11:"createcycle";s:11:"createCycle";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:6:"export";s:6:"export";s:5:"start";s:5:"start";s:6:"finish";s:6:"finish";s:11:"batchFinish";s:11:"batchFinish";s:12:"import2Today";s:12:"import2Today";s:8:"assignTo";s:12:"assignAction";s:8:"activate";s:8:"activate";s:5:"close";s:5:"close";s:10:"batchClose";s:10:"batchClose";s:8:"calendar";s:8:"calendar";}s:9:"personnel";a:4:{s:10:"accessible";s:10:"accessible";s:6:"invest";s:6:"invest";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";}s:5:"story";a:26:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:10:"editAction";s:6:"export";s:12:"exportAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:6:"change";s:12:"changeAction";s:6:"review";s:12:"reviewAction";s:12:"submitReview";s:12:"submitReview";s:11:"batchReview";s:11:"batchReview";s:6:"recall";s:6:"recall";s:5:"close";s:11:"closeAction";s:10:"batchClose";s:10:"batchClose";s:15:"batchChangePlan";s:15:"batchChangePlan";s:16:"batchChangeStage";s:16:"batchChangeStage";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:8:"activate";s:14:"activateAction";s:5:"tasks";s:5:"tasks";s:6:"report";s:12:"reportAction";s:9:"linkStory";s:9:"linkStory";s:17:"batchChangeBranch";s:17:"batchChangeBranch";s:17:"batchChangeModule";s:17:"batchChangeModule";s:11:"batchToTask";s:11:"batchToTask";s:18:"processStoryChange";s:18:"processStoryChange";s:9:"batchEdit";s:9:"batchEdit";}s:7:"project";a:28:{s:5:"index";s:5:"index";s:6:"browse";s:6:"browse";s:6:"kanban";s:6:"kanban";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:5:"group";s:5:"group";s:11:"createGroup";s:11:"createGroup";s:10:"managePriv";s:10:"managePriv";s:13:"manageMembers";s:13:"manageMembers";s:17:"manageGroupMember";s:17:"manageGroupMember";s:9:"copyGroup";s:9:"copyGroup";s:9:"editGroup";s:9:"editGroup";s:5:"start";s:5:"start";s:7:"suspend";s:7:"suspend";s:5:"close";s:5:"close";s:8:"activate";s:8:"activate";s:11:"updateOrder";s:11:"updateOrder";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";s:15:"unbindWhitelist";s:15:"unbindWhitelist";s:7:"dynamic";s:7:"dynamic";s:9:"execution";s:9:"execution";s:6:"export";s:6:"export";s:12:"unlinkMember";s:12:"unlinkMember";s:4:"team";s:10:"teamAction";}s:12:"projectstory";a:5:{s:5:"story";s:5:"story";s:4:"view";s:4:"view";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";}s:9:"execution";a:37:{s:4:"view";s:4:"view";s:6:"create";s:10:"createExec";s:4:"edit";s:10:"editAction";s:9:"batchedit";s:15:"batchEditAction";s:5:"start";s:11:"startAction";s:8:"activate";s:14:"activateAction";s:6:"putoff";s:11:"delayAction";s:7:"suspend";s:13:"suspendAction";s:5:"close";s:11:"closeAction";s:6:"delete";s:8:"deleteAB";s:4:"task";s:4:"task";s:9:"grouptask";s:9:"groupTask";s:10:"importtask";s:10:"importTask";s:5:"story";s:5:"story";s:4:"team";s:10:"teamAction";s:7:"dynamic";s:7:"dynamic";s:13:"manageMembers";s:13:"manageMembers";s:12:"unlinkMember";s:12:"unlinkMember";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:11:"updateOrder";s:11:"updateOrder";s:10:"taskKanban";s:10:"taskKanban";s:4:"tree";s:10:"treeAction";s:8:"treeTask";s:12:"treeViewTask";s:9:"treeStory";s:13:"treeViewStory";s:3:"all";s:14:"allExecutionAB";s:6:"export";s:12:"exportAction";s:11:"storyKanban";s:11:"storyKanban";s:9:"storySort";s:9:"storySort";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";s:15:"unbindWhitelist";s:15:"unbindWhitelist";s:6:"kanban";s:6:"kanban";s:5:"gantt";s:10:"ganttchart";s:8:"calendar";s:8:"calendar";s:3:"doc";s:3:"doc";}s:4:"task";a:26:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";s:4:"edit";s:4:"edit";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:5:"start";s:11:"startAction";s:5:"pause";s:11:"pauseAction";s:7:"restart";s:13:"restartAction";s:6:"finish";s:12:"finishAction";s:6:"cancel";s:12:"cancelAction";s:5:"close";s:11:"closeAction";s:10:"batchClose";s:10:"batchClose";s:8:"activate";s:14:"activateAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:6:"export";s:12:"exportAction";s:18:"confirmStoryChange";s:18:"confirmStoryChange";s:14:"recordEstimate";s:20:"recordEstimateAction";s:12:"editEstimate";s:12:"editEstimate";s:14:"deleteEstimate";s:14:"deleteEstimate";s:6:"report";s:11:"reportChart";s:17:"batchChangeModule";s:17:"batchChangeModule";s:14:"exportTemplate";s:14:"exportTemplate";s:6:"import";s:6:"import";s:11:"batchCancel";s:11:"batchCancel";}s:3:"doc";a:26:{s:5:"index";s:5:"index";s:7:"mySpace";s:7:"mySpace";s:6:"myView";s:6:"myView";s:12:"myCollection";s:12:"myCollection";s:10:"myCreation";s:10:"myCreation";s:9:"createLib";s:9:"createLib";s:7:"editLib";s:7:"editLib";s:9:"deleteLib";s:9:"deleteLib";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:10:"deleteFile";s:10:"deleteFile";s:7:"collect";s:13:"collectAction";s:12:"projectSpace";s:12:"projectSpace";s:9:"showFiles";s:9:"showFiles";s:10:"addCatalog";s:10:"addCatalog";s:11:"editCatalog";s:11:"editCatalog";s:13:"deleteCatalog";s:13:"deleteCatalog";s:14:"displaySetting";s:14:"displaySetting";s:4:"diff";s:10:"diffAction";s:11:"mine2export";s:11:"mine2export";s:14:"product2export";s:14:"product2export";s:14:"project2export";s:14:"project2export";s:13:"custom2export";s:13:"custom2export";s:16:"execution2export";s:16:"execution2export";}s:7:"company";a:9:{s:6:"browse";s:6:"browse";s:4:"edit";s:4:"edit";s:7:"dynamic";s:7:"dynamic";s:4:"view";s:4:"view";s:4:"todo";s:11:"companyTodo";s:8:"calendar";s:14:"effortCalendar";s:7:"allTodo";s:7:"allTodo";s:6:"effort";s:13:"companyEffort";s:9:"alleffort";s:9:"allEffort";}s:4:"dept";a:5:{s:6:"browse";s:6:"browse";s:11:"updateOrder";s:11:"updateOrder";s:11:"manageChild";s:11:"manageChild";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:5:"group";a:9:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"copy";s:4:"copy";s:6:"delete";s:6:"delete";s:10:"managePriv";s:10:"managePriv";s:12:"manageMember";s:12:"manageMember";s:18:"manageProjectAdmin";s:18:"manageProjectAdmin";s:10:"manageView";s:10:"manageView";}s:4:"user";a:15:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:6:"unlock";s:6:"unlock";s:6:"delete";s:6:"delete";s:4:"todo";s:4:"todo";s:4:"task";s:4:"task";s:7:"dynamic";s:7:"dynamic";s:7:"profile";s:7:"profile";s:9:"batchEdit";s:9:"batchEdit";s:6:"unbind";s:6:"unbind";s:17:"setPublicTemplate";s:17:"setPublicTemplate";s:5:"story";s:5:"story";s:9:"execution";s:9:"execution";}s:5:"admin";a:6:{s:5:"index";s:5:"index";s:9:"checkWeak";s:9:"checkWeak";s:3:"sso";s:9:"ssoAction";s:8:"register";s:8:"register";s:8:"xuanxuan";s:8:"xuanxuan";s:4:"safe";s:9:"safeIndex";}s:9:"extension";a:10:{s:6:"browse";s:12:"browseAction";s:6:"obtain";s:6:"obtain";s:9:"structure";s:15:"structureAction";s:7:"install";s:7:"install";s:9:"uninstall";s:15:"uninstallAction";s:8:"activate";s:14:"activateAction";s:10:"deactivate";s:16:"deactivateAction";s:6:"upload";s:6:"upload";s:5:"erase";s:11:"eraseAction";s:7:"upgrade";s:7:"upgrade";}s:6:"custom";a:13:{s:3:"set";s:3:"set";s:7:"product";s:11:"productName";s:9:"execution";s:15:"executionCommon";s:8:"required";s:8:"required";s:7:"restore";s:7:"restore";s:4:"flow";s:4:"flow";s:8:"timezone";s:8:"timezone";s:15:"setStoryConcept";s:15:"setStoryConcept";s:16:"editStoryConcept";s:16:"editStoryConcept";s:18:"browseStoryConcept";s:18:"browseStoryConcept";s:17:"setDefaultConcept";s:17:"setDefaultConcept";s:18:"deleteStoryConcept";s:18:"deleteStoryConcept";s:11:"libreoffice";s:11:"libreOffice";}s:6:"action";a:6:{s:5:"trash";s:5:"trash";s:8:"undelete";s:14:"undeleteAction";s:7:"hideOne";s:13:"hideOneAction";s:7:"hideAll";s:7:"hideAll";s:7:"comment";s:7:"comment";s:11:"editComment";s:11:"editComment";}s:6:"search";a:7:{s:9:"buildForm";s:9:"buildForm";s:10:"buildQuery";s:10:"buildQuery";s:9:"saveQuery";s:9:"saveQuery";s:11:"deleteQuery";s:11:"deleteQuery";s:6:"select";s:6:"select";s:5:"index";s:5:"index";s:10:"buildIndex";s:10:"buildIndex";}s:4:"tree";a:9:{s:6:"browse";s:6:"browse";s:10:"browseTask";s:10:"browseTask";s:11:"updateOrder";s:11:"updateOrder";s:11:"manageChild";s:11:"manageChild";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:8:"editHost";s:8:"editHost";s:3:"fix";s:3:"fix";s:10:"browsehost";s:16:"groupMaintenance";}s:4:"file";a:5:{s:8:"download";s:8:"download";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:12:"uploadImages";s:12:"uploadImages";s:9:"setPublic";s:9:"setPublic";}s:4:"misc";a:1:{s:4:"ping";s:4:"ping";}s:6:"backup";a:7:{s:5:"index";s:5:"index";s:6:"backup";s:6:"backup";s:7:"restore";s:7:"restore";s:6:"delete";s:6:"delete";s:7:"setting";s:13:"settingAction";s:11:"rmPHPHeader";s:11:"rmPHPHeader";s:6:"change";s:6:"change";}s:4:"cron";a:7:{s:5:"index";s:5:"index";s:6:"turnon";s:6:"turnon";s:6:"create";s:12:"createAction";s:4:"edit";s:4:"edit";s:6:"toggle";s:6:"toggle";s:6:"delete";s:6:"delete";s:11:"openProcess";s:7:"restart";}s:7:"message";a:3:{s:5:"index";s:5:"index";s:7:"browser";s:7:"browser";s:7:"setting";s:7:"setting";}s:2:"im";a:2:{s:18:"downloadXxdPackage";s:11:"downloadXXD";s:5:"debug";s:5:"debug";}s:6:"client";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:7:"setting";a:1:{s:8:"xuanxuan";s:8:"xuanxuan";}s:6:"kanban";a:41:{s:5:"space";s:11:"spaceCommon";s:11:"createSpace";s:11:"createSpace";s:9:"editSpace";s:9:"editSpace";s:10:"closeSpace";s:10:"closeSpace";s:11:"deleteSpace";s:11:"deleteSpace";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:5:"close";s:5:"close";s:6:"delete";s:6:"delete";s:12:"createRegion";s:12:"createRegion";s:10:"editRegion";s:10:"editRegion";s:10:"sortRegion";s:10:"sortRegion";s:9:"sortGroup";s:9:"sortGroup";s:12:"deleteRegion";s:12:"deleteRegion";s:10:"createLane";s:10:"createLane";s:8:"sortLane";s:8:"sortLane";s:10:"deleteLane";s:10:"deleteLane";s:12:"createColumn";s:12:"createColumn";s:9:"setColumn";s:9:"setColumn";s:6:"setWIP";s:6:"setWIP";s:10:"sortColumn";s:10:"sortColumn";s:12:"deleteColumn";s:12:"deleteColumn";s:10:"createCard";s:10:"createCard";s:8:"editCard";s:8:"editCard";s:8:"viewCard";s:8:"viewCard";s:8:"sortCard";s:8:"sortCard";s:10:"deleteCard";s:10:"deleteCard";s:12:"assigntoCard";s:12:"assigntoCard";s:8:"moveCard";s:8:"moveCard";s:12:"setCardColor";s:12:"setCardColor";s:18:"viewArchivedColumn";s:18:"viewArchivedColumn";s:15:"batchCreateCard";s:15:"batchCreateCard";s:12:"editLaneName";s:12:"editLaneName";s:13:"editLaneColor";s:13:"editLaneColor";s:11:"splitColumn";s:11:"splitColumn";s:13:"archiveColumn";s:13:"archiveColumn";s:13:"restoreColumn";s:13:"restoreColumn";s:11:"archiveCard";s:11:"archiveCard";s:16:"viewArchivedCard";s:16:"viewArchivedCard";s:11:"restoreCard";s:11:"restoreCard";}s:4:"mail";a:10:{s:5:"index";s:5:"index";s:6:"detect";s:12:"detectAction";s:4:"edit";s:4:"edit";s:4:"save";s:10:"saveAction";s:4:"test";s:4:"test";s:5:"reset";s:11:"resetAction";s:6:"browse";s:6:"browse";s:6:"delete";s:6:"delete";s:11:"batchDelete";s:11:"batchDelete";s:6:"resend";s:12:"resendAction";}s:9:"datatable";a:1:{s:9:"setGlobal";s:9:"setGlobal";}s:7:"webhook";a:7:{s:6:"browse";s:4:"list";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:3:"log";s:9:"logAction";s:4:"bind";s:4:"bind";s:10:"chooseDept";s:10:"chooseDept";}s:6:"effort";a:8:{s:11:"batchCreate";s:11:"batchCreate";s:15:"createForObject";s:15:"createForObject";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:6:"export";s:6:"export";s:8:"calendar";s:8:"calendar";}s:8:"feedback";a:9:{s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"browse";s:6:"browse";s:4:"view";s:4:"view";s:7:"comment";s:7:"comment";s:6:"delete";s:6:"delete";s:5:"close";s:11:"closeAction";s:6:"export";s:12:"exportAction";s:8:"assignTo";s:12:"assignAction";}s:3:"faq";a:1:{s:6:"browse";s:6:"browse";}s:6:"ticket";a:11:{s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"browse";s:6:"browse";s:8:"assignTo";s:6:"assign";s:5:"close";s:5:"close";s:8:"activate";s:8:"activate";s:6:"delete";s:6:"delete";s:14:"exportTemplate";s:14:"exportTemplate";s:6:"import";s:6:"import";s:6:"export";s:12:"exportAction";}s:6:"attend";a:15:{s:10:"department";s:10:"department";s:7:"company";s:7:"company";s:12:"browseReview";s:12:"browseReview";s:6:"review";s:6:"review";s:6:"export";s:12:"exportAction";s:4:"stat";s:12:"reportAction";s:8:"saveStat";s:14:"saveStatAction";s:10:"exportStat";s:10:"exportStat";s:6:"detail";s:12:"detailAction";s:12:"exportDetail";s:12:"exportDetail";s:8:"settings";s:8:"settings";s:16:"personalSettings";s:16:"personalSettings";s:10:"setManager";s:10:"setManager";s:8:"personal";s:8:"personal";s:4:"edit";s:10:"editAction";}s:5:"leave";a:13:{s:12:"browseReview";s:12:"browseReview";s:7:"company";s:13:"companyAction";s:6:"review";s:12:"reviewAction";s:6:"export";s:12:"exportAction";s:11:"setReviewer";s:17:"setReviewerAction";s:14:"personalAnnual";s:14:"personalAnnual";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:10:"viewAction";s:12:"switchstatus";s:12:"switchstatus";s:4:"back";s:10:"backAction";}s:6:"makeup";a:11:{s:12:"browseReview";s:12:"browseReview";s:7:"company";s:13:"companyAction";s:6:"review";s:12:"reviewAction";s:6:"export";s:12:"exportAction";s:11:"setReviewer";s:17:"setReviewerAction";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:4:"view";s:10:"viewAction";s:6:"delete";s:12:"deleteAction";s:12:"switchstatus";s:12:"switchstatus";}s:8:"overtime";a:11:{s:12:"browseReview";s:12:"browseReview";s:7:"company";s:13:"companyAction";s:6:"review";s:12:"reviewAction";s:6:"export";s:12:"exportAction";s:11:"setReviewer";s:17:"setReviewerAction";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:4:"view";s:10:"viewAction";s:6:"delete";s:12:"deleteAction";s:12:"switchstatus";s:12:"switchstatus";}s:4:"lieu";a:10:{s:7:"company";s:13:"companyAction";s:12:"browseReview";s:18:"browseReviewAction";s:6:"review";s:12:"reviewAction";s:11:"setReviewer";s:17:"setReviewerAction";s:8:"personal";s:8:"personal";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:10:"viewAction";s:12:"switchstatus";s:12:"switchstatus";}s:8:"workflow";a:16:{s:10:"browseFlow";s:10:"browseFlow";s:8:"browseDB";s:8:"browseDB";s:6:"create";s:6:"create";s:4:"copy";s:4:"copy";s:4:"edit";s:4:"edit";s:6:"backup";s:6:"backup";s:7:"upgrade";s:13:"upgradeAction";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:9:"flowchart";s:9:"flowchart";s:2:"ui";s:2:"ui";s:7:"release";s:13:"releaseAction";s:10:"deactivate";s:16:"deactivateAction";s:8:"activate";s:14:"activateAction";s:5:"setJS";s:11:"setJSAction";s:6:"setCSS";s:12:"setCSSAction";}s:13:"workflowfield";a:11:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";s:6:"import";s:6:"import";s:10:"showImport";s:10:"showImport";s:14:"exportTemplate";s:14:"exportTemplate";s:8:"setValue";s:8:"setValue";s:9:"setExport";s:9:"setExport";s:9:"setSearch";s:9:"setSearch";}s:14:"workflowaction";a:10:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";s:15:"setVerification";s:15:"setVerification";s:9:"setNotice";s:9:"setNotice";s:5:"setJS";s:11:"setJSAction";s:6:"setCSS";s:12:"setCSSAction";}s:17:"workflowcondition";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:14:"workflowlayout";a:2:{s:5:"admin";s:5:"admin";s:5:"block";s:5:"block";}s:15:"workflowlinkage";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:12:"workflowhook";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:13:"workflowlabel";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";}s:16:"workflowrelation";a:1:{s:5:"admin";s:5:"admin";}s:14:"workflowreport";a:5:{s:6:"browse";s:4:"brow";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"sort";s:4:"sort";}s:18:"workflowdatasource";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:12:"workflowrule";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";}s:3:"sms";a:3:{s:5:"index";s:5:"index";s:4:"test";s:4:"test";s:5:"reset";s:5:"reset";}}';
$views['open']['rnd'] = 'a:13:{i:0;s:2:"my";i:1;s:7:"program";i:2;s:7:"product";i:3;s:7:"project";i:4;s:9:"execution";i:5;s:2:"qa";i:6;s:6:"devops";i:7;s:6:"kanban";i:8;s:3:"doc";i:9;s:2:"bi";i:10;s:6:"system";i:11;s:5:"admin";i:12;s:9:"menuOrder";}';
$resources['open']['rnd'] = 'a:117:{s:5:"index";a:1:{s:5:"index";s:5:"index";}s:2:"my";a:18:{s:5:"index";s:11:"indexAction";s:4:"todo";s:10:"todoAction";s:4:"work";s:10:"workAction";s:10:"contribute";s:16:"contributeAction";s:7:"project";s:7:"project";s:7:"profile";s:13:"profileAction";s:12:"uploadAvatar";s:12:"uploadAvatar";s:10:"preference";s:10:"preference";s:7:"dynamic";s:13:"dynamicAction";s:11:"editProfile";s:11:"editProfile";s:14:"changePassword";s:14:"changePassword";s:14:"manageContacts";s:14:"manageContacts";s:14:"deleteContacts";s:14:"deleteContacts";s:4:"team";s:4:"team";s:9:"execution";s:9:"execution";s:3:"doc";s:3:"doc";s:5:"audit";s:5:"audit";s:8:"calendar";s:14:"calendarAction";}s:4:"todo";a:16:{s:6:"create";s:6:"create";s:11:"createcycle";s:11:"createCycle";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:6:"export";s:6:"export";s:5:"start";s:5:"start";s:6:"finish";s:6:"finish";s:11:"batchFinish";s:11:"batchFinish";s:12:"import2Today";s:12:"import2Today";s:8:"assignTo";s:12:"assignAction";s:8:"activate";s:8:"activate";s:5:"close";s:5:"close";s:10:"batchClose";s:10:"batchClose";}s:7:"program";a:18:{s:6:"browse";s:6:"browse";s:6:"kanban";s:12:"kanbanAction";s:4:"view";s:4:"view";s:7:"product";s:7:"product";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:5:"start";s:5:"start";s:7:"suspend";s:7:"suspend";s:8:"activate";s:8:"activate";s:5:"close";s:5:"close";s:6:"delete";s:6:"delete";s:7:"project";s:7:"project";s:11:"stakeholder";s:11:"stakeholder";s:17:"createStakeholder";s:17:"createStakeholder";s:17:"unlinkStakeholder";s:17:"unlinkStakeholder";s:23:"batchUnlinkStakeholders";s:23:"batchUnlinkStakeholders";s:15:"unbindWhitelist";s:15:"unbindWhitelist";s:11:"updateOrder";s:11:"updateOrder";}s:9:"personnel";a:4:{s:10:"accessible";s:10:"accessible";s:6:"invest";s:6:"invest";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";}s:7:"product";a:22:{s:5:"index";s:11:"indexAction";s:6:"browse";s:6:"browse";s:11:"requirement";s:11:"requirement";s:6:"create";s:6:"create";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:6:"delete";s:6:"delete";s:7:"roadmap";s:7:"roadmap";s:5:"track";s:5:"track";s:7:"dynamic";s:7:"dynamic";s:7:"project";s:7:"project";s:9:"dashboard";s:9:"dashboard";s:5:"close";s:11:"closeAction";s:11:"updateOrder";s:11:"orderAction";s:3:"all";s:4:"list";s:6:"kanban";s:6:"kanban";s:10:"manageLine";s:10:"manageLine";s:6:"export";s:12:"exportAction";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";s:15:"unbindWhitelist";s:15:"unbindWhitelist";}s:5:"story";a:30:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:10:"editAction";s:6:"export";s:12:"exportAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:6:"change";s:12:"changeAction";s:6:"review";s:12:"reviewAction";s:12:"submitReview";s:12:"submitReview";s:11:"batchReview";s:11:"batchReview";s:6:"recall";s:12:"recallAction";s:5:"close";s:11:"closeAction";s:10:"batchClose";s:10:"batchClose";s:15:"batchChangePlan";s:15:"batchChangePlan";s:16:"batchChangeStage";s:16:"batchChangeStage";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:8:"activate";s:14:"activateAction";s:5:"tasks";s:5:"tasks";s:4:"bugs";s:4:"bugs";s:5:"cases";s:5:"cases";s:6:"report";s:12:"reportAction";s:9:"linkStory";s:9:"linkStory";s:17:"batchChangeBranch";s:17:"batchChangeBranch";s:17:"batchChangeModule";s:17:"batchChangeModule";s:11:"batchToTask";s:11:"batchToTask";s:18:"processStoryChange";s:18:"processStoryChange";s:11:"linkStories";s:13:"linkStoriesAB";s:8:"relieved";s:13:"relievedTwins";s:9:"batchEdit";s:9:"batchEdit";}s:11:"requirement";a:22:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:10:"editAction";s:6:"export";s:12:"exportAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:6:"change";s:12:"changeAction";s:6:"review";s:12:"reviewAction";s:12:"submitReview";s:12:"submitReview";s:11:"batchReview";s:11:"batchReview";s:6:"recall";s:6:"recall";s:5:"close";s:11:"closeAction";s:10:"batchClose";s:10:"batchClose";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:8:"activate";s:14:"activateAction";s:6:"report";s:12:"reportAction";s:9:"linkStory";s:9:"linkStory";s:17:"batchChangeBranch";s:17:"batchChangeBranch";s:17:"batchChangeModule";s:17:"batchChangeModule";s:16:"linkRequirements";s:18:"linkRequirementsAB";s:9:"batchEdit";s:9:"batchEdit";}s:11:"productplan";a:17:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:7:"linkBug";s:7:"linkBug";s:9:"unlinkBug";s:9:"unlinkBug";s:14:"batchUnlinkBug";s:14:"batchUnlinkBug";s:9:"batchEdit";s:9:"batchEdit";s:5:"start";s:5:"start";s:6:"finish";s:6:"finish";s:5:"close";s:5:"close";s:8:"activate";s:8:"activate";s:17:"batchChangeStatus";s:17:"batchChangeStatus";}s:7:"release";a:14:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:6:"export";s:6:"export";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:7:"linkBug";s:7:"linkBug";s:9:"unlinkBug";s:9:"unlinkBug";s:14:"batchUnlinkBug";s:14:"batchUnlinkBug";s:12:"changeStatus";s:12:"changeStatus";s:6:"notify";s:6:"notify";}s:7:"project";a:34:{s:5:"index";s:5:"index";s:6:"browse";s:6:"browse";s:6:"kanban";s:6:"kanban";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:5:"group";s:5:"group";s:11:"createGroup";s:11:"createGroup";s:10:"managePriv";s:10:"managePriv";s:13:"manageMembers";s:13:"manageMembers";s:17:"manageGroupMember";s:17:"manageGroupMember";s:9:"copyGroup";s:9:"copyGroup";s:9:"editGroup";s:9:"editGroup";s:5:"start";s:5:"start";s:7:"suspend";s:7:"suspend";s:5:"close";s:5:"close";s:8:"activate";s:8:"activate";s:11:"updateOrder";s:11:"updateOrder";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";s:15:"unbindWhitelist";s:15:"unbindWhitelist";s:14:"manageProducts";s:14:"manageProducts";s:7:"dynamic";s:7:"dynamic";s:3:"bug";s:3:"bug";s:8:"testcase";s:8:"testcase";s:8:"testtask";s:8:"testtask";s:10:"testreport";s:10:"testreport";s:9:"execution";s:9:"execution";s:6:"export";s:6:"export";s:4:"team";s:10:"teamAction";s:12:"unlinkMember";s:18:"unlinkMemberAction";s:12:"programTitle";s:16:"moduleOpenAction";}s:11:"projectplan";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";}s:12:"projectstory";a:6:{s:5:"story";s:5:"story";s:4:"view";s:4:"view";s:9:"linkStory";s:9:"linkStory";s:17:"importplanstories";s:17:"importplanstories";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";}s:9:"execution";a:54:{s:4:"view";s:4:"view";s:6:"create";s:10:"createExec";s:4:"edit";s:10:"editAction";s:9:"batchedit";s:15:"batchEditAction";s:17:"batchchangestatus";s:17:"batchChangeStatus";s:5:"start";s:11:"startAction";s:8:"activate";s:14:"activateAction";s:6:"putoff";s:11:"delayAction";s:7:"suspend";s:13:"suspendAction";s:5:"close";s:11:"closeAction";s:6:"delete";s:8:"deleteAB";s:4:"task";s:4:"task";s:9:"grouptask";s:9:"groupTask";s:10:"importtask";s:10:"importTask";s:17:"importplanstories";s:17:"importPlanStories";s:9:"importBug";s:9:"importBug";s:5:"story";s:5:"story";s:5:"build";s:5:"build";s:8:"testcase";s:8:"testcase";s:3:"bug";s:3:"bug";s:8:"testtask";s:8:"testtask";s:10:"testreport";s:10:"testreport";s:4:"burn";s:4:"burn";s:11:"computeBurn";s:17:"computeBurnAction";s:3:"cfd";s:3:"CFD";s:10:"computeCFD";s:10:"computeCFD";s:8:"fixFirst";s:8:"fixFirst";s:4:"team";s:10:"teamAction";s:7:"dynamic";s:7:"dynamic";s:14:"manageProducts";s:14:"manageProducts";s:13:"manageMembers";s:13:"manageMembers";s:12:"unlinkMember";s:12:"unlinkMember";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:11:"updateOrder";s:11:"updateOrder";s:10:"taskKanban";s:10:"taskKanban";s:11:"printKanban";s:17:"printKanbanAction";s:4:"tree";s:10:"treeAction";s:8:"treeTask";s:12:"treeViewTask";s:9:"treeStory";s:13:"treeViewStory";s:3:"all";s:14:"allExecutionAB";s:6:"export";s:12:"exportAction";s:11:"storyKanban";s:11:"storyKanban";s:9:"storySort";s:9:"storySort";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";s:15:"unbindWhitelist";s:15:"unbindWhitelist";s:13:"storyEstimate";s:13:"storyEstimate";s:15:"executionkanban";s:12:"kanbanAction";s:6:"kanban";s:8:"RDKanban";s:9:"setKanban";s:9:"setKanban";s:3:"doc";s:3:"doc";s:9:"storyView";s:9:"storyView";}s:6:"kanban";a:44:{s:5:"space";s:11:"spaceCommon";s:11:"createSpace";s:11:"createSpace";s:9:"editSpace";s:9:"editSpace";s:10:"closeSpace";s:10:"closeSpace";s:11:"deleteSpace";s:11:"deleteSpace";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:5:"close";s:5:"close";s:6:"delete";s:6:"delete";s:12:"createRegion";s:12:"createRegion";s:10:"editRegion";s:10:"editRegion";s:10:"sortRegion";s:10:"sortRegion";s:9:"sortGroup";s:9:"sortGroup";s:12:"deleteRegion";s:12:"deleteRegion";s:10:"createLane";s:10:"createLane";s:8:"sortLane";s:8:"sortLane";s:10:"deleteLane";s:10:"deleteLane";s:12:"createColumn";s:12:"createColumn";s:9:"setColumn";s:10:"editColumn";s:6:"setWIP";s:6:"setWIP";s:10:"sortColumn";s:10:"sortColumn";s:12:"deleteColumn";s:12:"deleteColumn";s:10:"createCard";s:10:"createCard";s:8:"editCard";s:8:"editCard";s:8:"viewCard";s:8:"viewCard";s:8:"sortCard";s:8:"sortCard";s:10:"deleteCard";s:10:"deleteCard";s:12:"assigntoCard";s:12:"assigntoCard";s:8:"moveCard";s:8:"moveCard";s:12:"setCardColor";s:12:"setCardColor";s:18:"viewArchivedColumn";s:18:"viewArchivedColumn";s:15:"batchCreateCard";s:15:"batchCreateCard";s:13:"activateSpace";s:13:"activateSpace";s:7:"setting";s:7:"setting";s:8:"activate";s:8:"activate";s:13:"editLaneColor";s:13:"editLaneColor";s:12:"editLaneName";s:12:"editLaneName";s:11:"splitColumn";s:11:"splitColumn";s:13:"archiveColumn";s:13:"archiveColumn";s:13:"restoreColumn";s:13:"restoreColumn";s:11:"archiveCard";s:11:"archiveCard";s:16:"viewArchivedCard";s:16:"viewArchivedCard";s:11:"restoreCard";s:11:"restoreCard";}s:11:"programplan";a:2:{s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";}s:4:"task";a:24:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";s:4:"edit";s:4:"edit";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:5:"start";s:11:"startAction";s:5:"pause";s:11:"pauseAction";s:7:"restart";s:13:"restartAction";s:6:"finish";s:12:"finishAction";s:6:"cancel";s:12:"cancelAction";s:5:"close";s:11:"closeAction";s:10:"batchClose";s:10:"batchClose";s:8:"activate";s:14:"activateAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:6:"export";s:12:"exportAction";s:18:"confirmStoryChange";s:18:"confirmStoryChange";s:14:"recordEstimate";s:20:"recordEstimateAction";s:12:"editEstimate";s:12:"editEstimate";s:14:"deleteEstimate";s:14:"deleteEstimate";s:6:"report";s:11:"reportChart";s:17:"batchChangeModule";s:17:"batchChangeModule";s:11:"batchCancel";s:11:"batchCancel";}s:5:"build";a:10:{s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:7:"linkBug";s:7:"linkBug";s:9:"unlinkBug";s:9:"unlinkBug";s:14:"batchUnlinkBug";s:14:"batchUnlinkBug";}s:6:"design";a:11:{s:6:"browse";s:6:"browse";s:4:"view";s:4:"view";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:4:"edit";s:8:"assignTo";s:8:"assignTo";s:6:"delete";s:6:"delete";s:10:"linkCommit";s:10:"linkCommit";s:10:"viewCommit";s:10:"viewCommit";s:12:"unlinkCommit";s:12:"unlinkCommit";s:8:"revision";s:8:"revision";}s:2:"qa";a:1:{s:5:"index";s:11:"indexAction";}s:3:"bug";a:24:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";s:10:"confirmBug";s:13:"confirmAction";s:12:"batchConfirm";s:12:"batchConfirm";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:7:"resolve";s:13:"resolveAction";s:12:"batchResolve";s:12:"batchResolve";s:10:"batchClose";s:10:"batchClose";s:13:"batchActivate";s:13:"batchActivate";s:8:"activate";s:14:"activateAction";s:5:"close";s:11:"closeAction";s:6:"report";s:12:"reportAction";s:6:"export";s:12:"exportAction";s:18:"confirmStoryChange";s:18:"confirmStoryChange";s:6:"delete";s:12:"deleteAction";s:8:"linkBugs";s:8:"linkBugs";s:17:"batchChangeModule";s:17:"batchChangeModule";s:17:"batchChangeBranch";s:17:"batchChangeBranch";s:15:"batchChangePlan";s:15:"batchChangePlan";}s:8:"testcase";a:39:{s:6:"browse";s:6:"browse";s:9:"groupCase";s:9:"groupCase";s:8:"zeroCase";s:8:"zeroCase";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:9:"createBug";s:9:"createBug";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:6:"delete";s:12:"deleteAction";s:6:"export";s:12:"exportAction";s:13:"confirmChange";s:13:"confirmChange";s:18:"confirmStoryChange";s:18:"confirmStoryChange";s:9:"batchEdit";s:9:"batchEdit";s:11:"batchDelete";s:11:"batchDelete";s:17:"batchChangeModule";s:17:"batchChangeModule";s:17:"batchChangeBranch";s:17:"batchChangeBranch";s:9:"linkCases";s:9:"linkCases";s:8:"linkBugs";s:8:"linkBugs";s:4:"bugs";s:4:"bugs";s:6:"review";s:6:"review";s:11:"batchReview";s:11:"batchReview";s:23:"batchConfirmStoryChange";s:23:"batchConfirmStoryChange";s:13:"importFromLib";s:13:"importFromLib";s:19:"batchCaseTypeChange";s:19:"batchCaseTypeChange";s:20:"confirmLibcaseChange";s:20:"confirmLibcaseChange";s:19:"ignoreLibcaseChange";s:19:"ignoreLibcaseChange";s:11:"importToLib";s:11:"importToLib";s:10:"automation";s:10:"automation";s:10:"showScript";s:10:"showScript";s:11:"createScene";s:11:"createScene";s:9:"editScene";s:9:"editScene";s:11:"deleteScene";s:11:"deleteScene";s:11:"changeScene";s:11:"changeScene";s:16:"batchChangeScene";s:16:"batchChangeScene";s:11:"updateOrder";s:11:"updateOrder";s:11:"importXmind";s:11:"importXmind";s:11:"exportXmind";s:11:"exportXmind";s:14:"exportTemplate";s:14:"exportTemplate";s:6:"import";s:12:"importAction";}s:8:"testtask";a:22:{s:6:"create";s:6:"create";s:6:"browse";s:6:"browse";s:4:"view";s:10:"viewAction";s:5:"cases";s:11:"casesAction";s:9:"groupCase";s:9:"groupCase";s:4:"edit";s:4:"edit";s:5:"start";s:11:"startAction";s:8:"activate";s:14:"activateAction";s:5:"block";s:11:"blockAction";s:5:"close";s:11:"closeAction";s:6:"delete";s:6:"delete";s:11:"batchAssign";s:11:"batchAssign";s:8:"linkcase";s:8:"linkCase";s:10:"unlinkcase";s:13:"lblUnlinkCase";s:7:"runcase";s:10:"lblRunCase";s:7:"results";s:13:"resultsAction";s:16:"batchUnlinkCases";s:16:"batchUnlinkCases";s:6:"report";s:12:"reportAction";s:11:"browseUnits";s:11:"browseUnits";s:9:"unitCases";s:9:"unitCases";s:16:"importUnitResult";s:16:"importUnitResult";s:8:"batchRun";s:8:"batchRun";}s:9:"testsuite";a:8:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:8:"linkCase";s:8:"linkCase";s:10:"unlinkCase";s:16:"unlinkCaseAction";s:16:"batchUnlinkCases";s:16:"batchUnlinkCases";}s:10:"testreport";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:4:"edit";s:4:"edit";}s:7:"caselib";a:10:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:10:"createCase";s:10:"createCase";s:15:"batchCreateCase";s:15:"batchCreateCase";s:14:"exportTemplate";s:14:"exportTemplate";s:6:"import";s:12:"importAction";s:10:"showImport";s:10:"showImport";}s:6:"zahost";a:8:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:11:"browseImage";s:11:"browseImage";s:13:"downloadImage";s:13:"downloadImage";s:14:"cancelDownload";s:6:"cancel";}s:6:"zanode";a:17:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:7:"destroy";s:7:"destroy";s:6:"reboot";s:6:"reboot";s:6:"resume";s:6:"resume";s:6:"getVNC";s:6:"getVNC";s:5:"start";s:4:"boot";s:5:"close";s:8:"shutdown";s:4:"view";s:4:"view";s:11:"createImage";s:11:"createImage";s:14:"browseSnapshot";s:14:"browseSnapshot";s:14:"createSnapshot";s:14:"createSnapshot";s:12:"editSnapshot";s:12:"editSnapshot";s:15:"restoreSnapshot";s:15:"restoreSnapshot";s:14:"deleteSnapshot";s:14:"deleteSnapshot";s:7:"suspend";s:7:"suspend";}s:3:"doc";a:24:{s:5:"index";s:5:"index";s:7:"mySpace";s:7:"mySpace";s:6:"myView";s:6:"myView";s:12:"myCollection";s:12:"myCollection";s:10:"myCreation";s:10:"myCreation";s:8:"myEdited";s:8:"myEdited";s:9:"createLib";s:9:"createLib";s:7:"editLib";s:7:"editLib";s:9:"deleteLib";s:9:"deleteLib";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:10:"deleteFile";s:10:"deleteFile";s:7:"collect";s:13:"collectAction";s:12:"productSpace";s:12:"productSpace";s:12:"projectSpace";s:12:"projectSpace";s:9:"teamSpace";s:9:"teamSpace";s:9:"showFiles";s:9:"showFiles";s:10:"addCatalog";s:10:"addCatalog";s:11:"editCatalog";s:11:"editCatalog";s:11:"sortCatalog";s:11:"sortCatalog";s:13:"deleteCatalog";s:13:"deleteCatalog";s:14:"displaySetting";s:14:"displaySetting";}s:6:"screen";a:4:{s:6:"browse";s:6:"browse";s:4:"view";s:4:"view";s:10:"annualData";s:10:"annualData";s:13:"allAnnualData";s:13:"allAnnualData";}s:5:"pivot";a:6:{s:7:"preview";s:7:"preview";s:14:"productSummary";s:14:"productSummary";s:16:"projectDeviation";s:16:"projectDeviation";s:9:"bugCreate";s:9:"bugCreate";s:9:"bugAssign";s:9:"bugAssign";s:8:"workload";s:8:"workload";}s:5:"chart";a:1:{s:7:"preview";s:7:"preview";}s:6:"report";a:0:{}s:7:"company";a:4:{s:6:"browse";s:6:"browse";s:4:"edit";s:4:"edit";s:7:"dynamic";s:7:"dynamic";s:4:"view";s:4:"view";}s:4:"dept";a:5:{s:6:"browse";s:6:"browse";s:11:"updateOrder";s:11:"updateOrder";s:11:"manageChild";s:17:"manageChildAction";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:5:"group";a:9:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"copy";s:4:"copy";s:6:"delete";s:6:"delete";s:10:"managePriv";s:10:"managePriv";s:12:"manageMember";s:12:"manageMember";s:18:"manageProjectAdmin";s:18:"manageProjectAdmin";s:10:"manageView";s:10:"manageView";}s:4:"user";a:18:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:6:"unlock";s:6:"unlock";s:6:"delete";s:6:"delete";s:4:"todo";s:4:"todo";s:4:"task";s:4:"task";s:3:"bug";s:3:"bug";s:7:"dynamic";s:7:"dynamic";s:7:"profile";s:7:"profile";s:9:"batchEdit";s:9:"batchEdit";s:6:"unbind";s:6:"unbind";s:17:"setPublicTemplate";s:17:"setPublicTemplate";s:5:"story";s:5:"story";s:8:"testTask";s:8:"testTask";s:8:"testCase";s:8:"testCase";s:9:"execution";s:9:"execution";}s:5:"admin";a:8:{s:5:"index";s:5:"index";s:9:"checkWeak";s:9:"checkWeak";s:3:"sso";s:9:"ssoAction";s:8:"register";s:8:"register";s:8:"xuanxuan";s:8:"xuanxuan";s:15:"resetPWDSetting";s:15:"resetPWDSetting";s:11:"tableEngine";s:11:"tableEngine";s:4:"safe";s:9:"safeIndex";}s:5:"stage";a:7:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:4:"edit";s:7:"setType";s:7:"setType";s:6:"delete";s:6:"delete";s:10:"plusBrowse";s:10:"plusBrowse";}s:9:"extension";a:10:{s:6:"browse";s:12:"browseAction";s:6:"obtain";s:6:"obtain";s:9:"structure";s:15:"structureAction";s:7:"install";s:7:"install";s:9:"uninstall";s:15:"uninstallAction";s:8:"activate";s:14:"activateAction";s:10:"deactivate";s:16:"deactivateAction";s:6:"upload";s:6:"upload";s:5:"erase";s:11:"eraseAction";s:7:"upgrade";s:7:"upgrade";}s:6:"custom";a:17:{s:3:"set";s:3:"set";s:7:"product";s:11:"productName";s:9:"execution";s:15:"executionCommon";s:8:"required";s:8:"required";s:7:"restore";s:7:"restore";s:4:"flow";s:4:"flow";s:8:"timezone";s:8:"timezone";s:15:"setStoryConcept";s:15:"setStoryConcept";s:16:"editStoryConcept";s:16:"editStoryConcept";s:18:"browseStoryConcept";s:18:"browseStoryConcept";s:17:"setDefaultConcept";s:17:"setDefaultConcept";s:18:"deleteStoryConcept";s:18:"deleteStoryConcept";s:6:"kanban";s:6:"kanban";s:4:"code";s:4:"code";s:5:"hours";s:5:"hours";s:7:"percent";s:7:"percent";s:13:"limitTaskDate";s:19:"limitTaskDateAction";}s:6:"action";a:6:{s:5:"trash";s:11:"trashAction";s:8:"undelete";s:14:"undeleteAction";s:7:"hideOne";s:13:"hideOneAction";s:7:"hideAll";s:7:"hideAll";s:7:"comment";s:7:"comment";s:11:"editComment";s:11:"editComment";}s:4:"mail";a:10:{s:5:"index";s:5:"index";s:6:"detect";s:12:"detectAction";s:4:"edit";s:4:"edit";s:4:"save";s:10:"saveAction";s:4:"test";s:4:"test";s:5:"reset";s:11:"resetAction";s:6:"browse";s:6:"browse";s:6:"delete";s:6:"delete";s:11:"batchDelete";s:11:"batchDelete";s:6:"resend";s:12:"resendAction";}s:3:"svn";a:3:{s:4:"diff";s:4:"diff";s:3:"cat";s:3:"cat";s:7:"apiSync";s:7:"apiSync";}s:3:"git";a:3:{s:4:"diff";s:4:"diff";s:3:"cat";s:3:"cat";s:7:"apiSync";s:7:"apiSync";}s:6:"search";a:7:{s:9:"buildForm";s:9:"buildForm";s:10:"buildQuery";s:10:"buildQuery";s:9:"saveQuery";s:9:"saveQuery";s:11:"deleteQuery";s:11:"deleteQuery";s:6:"select";s:6:"select";s:5:"index";s:5:"index";s:10:"buildIndex";s:10:"buildIndex";}s:4:"tree";a:7:{s:6:"browse";s:6:"browse";s:10:"browseTask";s:10:"browseTask";s:11:"updateOrder";s:11:"updateOrder";s:11:"manageChild";s:11:"manageChild";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:3:"fix";s:3:"fix";}s:3:"api";a:21:{s:5:"index";s:5:"index";s:9:"createLib";s:9:"createLib";s:7:"editLib";s:7:"editLib";s:9:"deleteLib";s:9:"deleteLib";s:13:"createRelease";s:13:"createRelease";s:8:"releases";s:8:"releases";s:13:"deleteRelease";s:13:"deleteRelease";s:6:"struct";s:6:"struct";s:12:"createStruct";s:12:"createStruct";s:10:"editStruct";s:10:"editStruct";s:12:"deleteStruct";s:12:"deleteStruct";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:10:"addCatalog";s:10:"addCatalog";s:11:"editCatalog";s:11:"editCatalog";s:11:"sortCatalog";s:11:"sortCatalog";s:13:"deleteCatalog";s:13:"deleteCatalog";s:8:"getModel";s:8:"getModel";s:5:"debug";s:5:"debug";s:3:"sql";s:3:"sql";}s:4:"file";a:5:{s:8:"download";s:8:"download";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:12:"uploadImages";s:12:"uploadImages";s:9:"setPublic";s:9:"setPublic";}s:4:"misc";a:1:{s:4:"ping";s:4:"ping";}s:6:"backup";a:7:{s:5:"index";s:5:"index";s:6:"backup";s:6:"backup";s:7:"restore";s:13:"restoreAction";s:6:"delete";s:6:"delete";s:7:"setting";s:13:"settingAction";s:11:"rmPHPHeader";s:11:"rmPHPHeader";s:6:"change";s:6:"change";}s:4:"cron";a:7:{s:5:"index";s:5:"index";s:6:"turnon";s:6:"turnon";s:6:"create";s:12:"createAction";s:4:"edit";s:4:"edit";s:6:"toggle";s:6:"toggle";s:6:"delete";s:6:"delete";s:11:"openProcess";s:7:"restart";}s:3:"dev";a:2:{s:3:"api";s:3:"api";s:2:"db";s:2:"db";}s:6:"editor";a:6:{s:5:"index";s:5:"index";s:6:"extend";s:6:"extend";s:4:"edit";s:4:"edit";s:7:"newPage";s:7:"newPage";s:4:"save";s:4:"save";s:6:"delete";s:6:"delete";}s:7:"message";a:3:{s:5:"index";s:5:"index";s:7:"browser";s:7:"browser";s:7:"setting";s:7:"setting";}s:6:"gitlab";a:30:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:11:"importIssue";s:11:"importIssue";s:6:"delete";s:6:"delete";s:8:"bindUser";s:8:"bindUser";s:13:"browseProject";s:13:"browseProject";s:13:"createProject";s:13:"createProject";s:11:"editProject";s:11:"editProject";s:13:"deleteProject";s:13:"deleteProject";s:11:"browseGroup";s:11:"browseGroup";s:11:"createGroup";s:11:"createGroup";s:9:"editGroup";s:9:"editGroup";s:11:"deleteGroup";s:11:"deleteGroup";s:18:"manageGroupMembers";s:18:"manageGroupMembers";s:10:"browseUser";s:10:"browseUser";s:10:"createUser";s:10:"createUser";s:8:"editUser";s:8:"editUser";s:10:"deleteUser";s:10:"deleteUser";s:12:"createBranch";s:12:"createBranch";s:12:"browseBranch";s:12:"browseBranch";s:7:"webhook";s:7:"webhook";s:13:"createWebhook";s:13:"createWebhook";s:20:"manageProjectMembers";s:20:"manageProjectMembers";s:16:"manageBranchPriv";s:16:"browseBranchPriv";s:13:"manageTagPriv";s:13:"browseTagPriv";s:9:"browseTag";s:9:"browseTag";s:9:"createTag";s:9:"createTag";s:9:"deleteTag";s:9:"deleteTag";}s:2:"mr";a:16:{s:6:"create";s:6:"create";s:6:"browse";s:12:"browseAction";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:10:"viewAction";s:6:"accept";s:6:"accept";s:4:"diff";s:8:"viewDiff";s:4:"link";s:8:"linkList";s:9:"linkStory";s:9:"linkStory";s:7:"linkBug";s:7:"linkBug";s:8:"linkTask";s:8:"linkTask";s:6:"unlink";s:6:"unlink";s:8:"approval";s:8:"approval";s:5:"close";s:5:"close";s:6:"reopen";s:6:"reopen";s:9:"addReview";s:9:"addReview";}s:3:"app";a:1:{s:10:"serverlink";s:10:"serverLink";}s:4:"gogs";a:6:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:8:"bindUser";s:8:"bindUser";}s:5:"gitea";a:6:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:8:"bindUser";s:8:"bindUser";}s:7:"holiday";a:5:{s:6:"browse";s:6:"browse";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:6:"delete";s:12:"deleteAction";s:6:"import";s:12:"importAction";}s:12:"projectbuild";a:11:{s:6:"browse";s:6:"browse";s:4:"view";s:4:"view";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:7:"linkBug";s:7:"linkBug";s:9:"unlinkBug";s:9:"unlinkBug";s:14:"batchUnlinkBug";s:14:"batchUnlinkBug";}s:14:"projectrelease";a:14:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:6:"export";s:6:"export";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:7:"linkBug";s:7:"linkBug";s:9:"unlinkBug";s:9:"unlinkBug";s:14:"batchUnlinkBug";s:14:"batchUnlinkBug";s:12:"changeStatus";s:12:"changeStatus";s:6:"notify";s:6:"notify";}s:11:"stakeholder";a:9:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"view";s:10:"viewAction";s:11:"communicate";s:11:"communicate";s:6:"expect";s:6:"expect";s:9:"userIssue";s:9:"userIssue";}s:6:"branch";a:8:{s:6:"manage";s:6:"manage";s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:5:"close";s:11:"closeAction";s:8:"activate";s:14:"activateAction";s:4:"sort";s:4:"sort";s:9:"batchEdit";s:9:"batchEdit";s:11:"mergeBranch";s:17:"mergeBranchAction";}s:4:"repo";a:20:{s:6:"create";s:12:"createAction";s:4:"edit";s:10:"editAction";s:6:"delete";s:6:"delete";s:14:"showSyncCommit";s:14:"showSyncCommit";s:8:"maintain";s:8:"maintain";s:6:"browse";s:12:"browseAction";s:4:"view";s:4:"view";s:4:"diff";s:10:"diffAction";s:3:"log";s:3:"log";s:8:"revision";s:14:"revisionAction";s:5:"blame";s:11:"blameAction";s:8:"download";s:14:"downloadAction";s:8:"setRules";s:8:"setRules";s:15:"apiGetRepoByUrl";s:15:"apiGetRepoByUrl";s:12:"downloadCode";s:12:"downloadCode";s:9:"linkStory";s:9:"linkStory";s:7:"linkBug";s:7:"linkBug";s:8:"linkTask";s:8:"linkTask";s:6:"unlink";s:6:"unlink";s:6:"import";s:12:"importAction";}s:2:"ci";a:2:{s:12:"commitResult";s:12:"commitResult";s:18:"checkCompileStatus";s:18:"checkCompileStatus";}s:7:"compile";a:3:{s:6:"browse";s:6:"browse";s:4:"logs";s:4:"logs";s:11:"syncCompile";s:11:"syncCompile";}s:7:"jenkins";a:4:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:12:"deleteAction";}s:3:"job";a:6:{s:6:"browse";s:12:"browseAction";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:4:"exec";s:4:"exec";s:4:"view";s:4:"view";}s:9:"datatable";a:1:{s:9:"setGlobal";s:9:"setGlobal";}s:9:"sonarqube";a:10:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:13:"browseProject";s:13:"browseProject";s:13:"createProject";s:13:"createProject";s:13:"deleteProject";s:13:"deleteProject";s:7:"execJob";s:7:"execJob";s:10:"reportView";s:10:"reportView";s:11:"browseIssue";s:11:"browseIssue";}s:7:"webhook";a:7:{s:6:"browse";s:4:"list";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:3:"log";s:9:"logAction";s:4:"bind";s:4:"bind";s:10:"chooseDept";s:10:"chooseDept";}s:7:"contact";a:0:{}s:18:"programstakeholder";a:0:{}s:12:"researchplan";a:0:{}s:14:"workestimation";a:0:{}s:11:"gapanalysis";a:0:{}s:13:"executionview";a:0:{}s:11:"managespace";a:0:{}s:10:"systemteam";a:0:{}s:14:"systemschedule";a:0:{}s:12:"systemeffort";a:0:{}s:13:"systemdynamic";a:0:{}s:13:"systemcompany";a:0:{}s:8:"pipeline";a:0:{}s:13:"devopssetting";a:0:{}s:13:"featureswitch";a:0:{}s:10:"importdata";a:0:{}s:13:"systemsetting";a:0:{}s:11:"staffmanage";a:0:{}s:11:"modelconfig";a:0:{}s:13:"featureconfig";a:0:{}s:11:"doctemplate";a:0:{}s:13:"notifysetting";a:0:{}s:8:"bidesign";a:0:{}s:16:"personalsettings";a:0:{}s:15:"projectsettings";a:0:{}s:10:"dataaccess";a:0:{}s:14:"executiongantt";a:0:{}s:15:"executionkanban";a:0:{}s:13:"executionburn";a:0:{}s:12:"executioncfd";a:0:{}s:14:"executionstory";a:0:{}s:11:"executionqa";a:0:{}s:17:"executionsettings";a:0:{}s:14:"generalcomment";a:0:{}s:11:"generalping";a:0:{}s:15:"generaltemplate";a:0:{}s:13:"generaleffort";a:0:{}s:15:"productsettings";a:0:{}s:13:"projectreview";a:0:{}s:12:"projecttrack";a:0:{}s:9:"projectqa";a:0:{}s:7:"setting";a:1:{s:8:"xuanxuan";s:8:"xuanxuan";}s:2:"im";a:2:{s:18:"downloadXxdPackage";s:11:"downloadXXD";s:5:"debug";s:5:"debug";}s:6:"client";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}}';
$views['open']['lite'] = 'a:8:{i:0;s:2:"my";i:1;s:7:"project";i:2;s:9:"execution";i:3;s:6:"kanban";i:4;s:3:"doc";i:5;s:6:"system";i:6;s:5:"admin";i:7;s:9:"menuOrder";}';
$resources['open']['lite'] = 'a:32:{s:5:"index";a:1:{s:5:"index";s:5:"index";}s:2:"my";a:19:{s:5:"index";s:11:"indexAction";s:4:"todo";s:4:"todo";s:4:"work";s:10:"workAction";s:10:"contribute";s:16:"contributeAction";s:7:"project";s:7:"project";s:7:"profile";s:13:"profileAction";s:12:"uploadAvatar";s:12:"uploadAvatar";s:7:"dynamic";s:13:"dynamicAction";s:11:"editProfile";s:11:"editProfile";s:14:"changePassword";s:14:"changePassword";s:14:"manageContacts";s:14:"manageContacts";s:14:"deleteContacts";s:14:"deleteContacts";s:5:"score";s:5:"score";s:4:"team";s:4:"team";s:9:"execution";s:9:"execution";s:5:"story";s:5:"story";s:4:"task";s:4:"task";s:3:"doc";s:3:"doc";s:8:"calendar";s:14:"calendarAction";}s:4:"todo";a:16:{s:6:"create";s:6:"create";s:11:"createcycle";s:11:"createCycle";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:6:"export";s:6:"export";s:5:"start";s:5:"start";s:6:"finish";s:6:"finish";s:11:"batchFinish";s:11:"batchFinish";s:12:"import2Today";s:12:"import2Today";s:8:"assignTo";s:12:"assignAction";s:8:"activate";s:8:"activate";s:5:"close";s:5:"close";s:10:"batchClose";s:10:"batchClose";}s:9:"personnel";a:4:{s:10:"accessible";s:10:"accessible";s:6:"invest";s:6:"invest";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";}s:5:"story";a:26:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"edit";s:10:"editAction";s:6:"export";s:12:"exportAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:6:"change";s:12:"changeAction";s:6:"review";s:12:"reviewAction";s:12:"submitReview";s:12:"submitReview";s:11:"batchReview";s:11:"batchReview";s:6:"recall";s:6:"recall";s:5:"close";s:11:"closeAction";s:10:"batchClose";s:10:"batchClose";s:15:"batchChangePlan";s:15:"batchChangePlan";s:16:"batchChangeStage";s:16:"batchChangeStage";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:8:"activate";s:14:"activateAction";s:5:"tasks";s:5:"tasks";s:6:"report";s:12:"reportAction";s:9:"linkStory";s:9:"linkStory";s:17:"batchChangeBranch";s:17:"batchChangeBranch";s:17:"batchChangeModule";s:17:"batchChangeModule";s:11:"batchToTask";s:11:"batchToTask";s:18:"processStoryChange";s:18:"processStoryChange";s:9:"batchEdit";s:9:"batchEdit";}s:7:"project";a:28:{s:5:"index";s:5:"index";s:6:"browse";s:6:"browse";s:6:"kanban";s:6:"kanban";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:9:"batchEdit";s:9:"batchEdit";s:5:"group";s:5:"group";s:11:"createGroup";s:11:"createGroup";s:10:"managePriv";s:10:"managePriv";s:13:"manageMembers";s:13:"manageMembers";s:17:"manageGroupMember";s:17:"manageGroupMember";s:9:"copyGroup";s:9:"copyGroup";s:9:"editGroup";s:9:"editGroup";s:5:"start";s:5:"start";s:7:"suspend";s:7:"suspend";s:5:"close";s:5:"close";s:8:"activate";s:8:"activate";s:11:"updateOrder";s:11:"updateOrder";s:6:"delete";s:6:"delete";s:4:"view";s:4:"view";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";s:15:"unbindWhitelist";s:15:"unbindWhitelist";s:7:"dynamic";s:7:"dynamic";s:9:"execution";s:9:"execution";s:6:"export";s:6:"export";s:12:"unlinkMember";s:12:"unlinkMember";s:4:"team";s:10:"teamAction";}s:12:"projectstory";a:5:{s:5:"story";s:5:"story";s:4:"view";s:4:"view";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";}s:9:"execution";a:35:{s:4:"view";s:4:"view";s:6:"create";s:10:"createExec";s:4:"edit";s:10:"editAction";s:9:"batchedit";s:15:"batchEditAction";s:5:"start";s:11:"startAction";s:8:"activate";s:14:"activateAction";s:6:"putoff";s:11:"delayAction";s:7:"suspend";s:13:"suspendAction";s:5:"close";s:11:"closeAction";s:6:"delete";s:8:"deleteAB";s:4:"task";s:4:"task";s:9:"grouptask";s:9:"groupTask";s:10:"importtask";s:10:"importTask";s:5:"story";s:5:"story";s:4:"team";s:10:"teamAction";s:7:"dynamic";s:7:"dynamic";s:13:"manageMembers";s:13:"manageMembers";s:12:"unlinkMember";s:12:"unlinkMember";s:9:"linkStory";s:9:"linkStory";s:11:"unlinkStory";s:11:"unlinkStory";s:16:"batchUnlinkStory";s:16:"batchUnlinkStory";s:11:"updateOrder";s:11:"updateOrder";s:10:"taskKanban";s:10:"taskKanban";s:4:"tree";s:10:"treeAction";s:8:"treeTask";s:12:"treeViewTask";s:9:"treeStory";s:13:"treeViewStory";s:3:"all";s:14:"allExecutionAB";s:6:"export";s:12:"exportAction";s:11:"storyKanban";s:11:"storyKanban";s:9:"storySort";s:9:"storySort";s:9:"whitelist";s:9:"whitelist";s:12:"addWhitelist";s:12:"addWhitelist";s:15:"unbindWhitelist";s:15:"unbindWhitelist";s:6:"kanban";s:6:"kanban";s:3:"doc";s:3:"doc";}s:4:"task";a:24:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:9:"batchEdit";s:9:"batchEdit";s:4:"edit";s:4:"edit";s:8:"assignTo";s:12:"assignAction";s:13:"batchAssignTo";s:13:"batchAssignTo";s:5:"start";s:11:"startAction";s:5:"pause";s:11:"pauseAction";s:7:"restart";s:13:"restartAction";s:6:"finish";s:12:"finishAction";s:6:"cancel";s:12:"cancelAction";s:5:"close";s:11:"closeAction";s:10:"batchClose";s:10:"batchClose";s:8:"activate";s:14:"activateAction";s:6:"delete";s:12:"deleteAction";s:4:"view";s:4:"view";s:6:"export";s:12:"exportAction";s:18:"confirmStoryChange";s:18:"confirmStoryChange";s:14:"recordEstimate";s:20:"recordEstimateAction";s:12:"editEstimate";s:12:"editEstimate";s:14:"deleteEstimate";s:14:"deleteEstimate";s:6:"report";s:11:"reportChart";s:17:"batchChangeModule";s:17:"batchChangeModule";s:11:"batchCancel";s:11:"batchCancel";}s:3:"doc";a:20:{s:5:"index";s:5:"index";s:7:"mySpace";s:7:"mySpace";s:6:"myView";s:6:"myView";s:12:"myCollection";s:12:"myCollection";s:10:"myCreation";s:10:"myCreation";s:9:"createLib";s:9:"createLib";s:7:"editLib";s:7:"editLib";s:9:"deleteLib";s:9:"deleteLib";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:6:"delete";s:6:"delete";s:10:"deleteFile";s:10:"deleteFile";s:7:"collect";s:13:"collectAction";s:12:"projectSpace";s:12:"projectSpace";s:9:"showFiles";s:9:"showFiles";s:10:"addCatalog";s:10:"addCatalog";s:11:"editCatalog";s:11:"editCatalog";s:13:"deleteCatalog";s:13:"deleteCatalog";s:14:"displaySetting";s:14:"displaySetting";}s:7:"company";a:4:{s:6:"browse";s:6:"browse";s:4:"edit";s:4:"edit";s:7:"dynamic";s:7:"dynamic";s:4:"view";s:4:"view";}s:4:"dept";a:5:{s:6:"browse";s:6:"browse";s:11:"updateOrder";s:11:"updateOrder";s:11:"manageChild";s:11:"manageChild";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:5:"group";a:9:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"copy";s:4:"copy";s:6:"delete";s:6:"delete";s:10:"managePriv";s:10:"managePriv";s:12:"manageMember";s:12:"manageMember";s:18:"manageProjectAdmin";s:18:"manageProjectAdmin";s:10:"manageView";s:10:"manageView";}s:4:"user";a:15:{s:6:"create";s:6:"create";s:11:"batchCreate";s:11:"batchCreate";s:4:"view";s:4:"view";s:4:"edit";s:4:"edit";s:6:"unlock";s:6:"unlock";s:6:"delete";s:6:"delete";s:4:"todo";s:4:"todo";s:4:"task";s:4:"task";s:7:"dynamic";s:7:"dynamic";s:7:"profile";s:7:"profile";s:9:"batchEdit";s:9:"batchEdit";s:6:"unbind";s:6:"unbind";s:17:"setPublicTemplate";s:17:"setPublicTemplate";s:5:"story";s:5:"story";s:9:"execution";s:9:"execution";}s:5:"admin";a:6:{s:5:"index";s:5:"index";s:9:"checkWeak";s:9:"checkWeak";s:3:"sso";s:9:"ssoAction";s:8:"register";s:8:"register";s:8:"xuanxuan";s:8:"xuanxuan";s:4:"safe";s:9:"safeIndex";}s:9:"extension";a:10:{s:6:"browse";s:12:"browseAction";s:6:"obtain";s:6:"obtain";s:9:"structure";s:15:"structureAction";s:7:"install";s:7:"install";s:9:"uninstall";s:15:"uninstallAction";s:8:"activate";s:14:"activateAction";s:10:"deactivate";s:16:"deactivateAction";s:6:"upload";s:6:"upload";s:5:"erase";s:11:"eraseAction";s:7:"upgrade";s:7:"upgrade";}s:6:"custom";a:12:{s:3:"set";s:3:"set";s:7:"product";s:11:"productName";s:9:"execution";s:15:"executionCommon";s:8:"required";s:8:"required";s:7:"restore";s:7:"restore";s:4:"flow";s:4:"flow";s:8:"timezone";s:8:"timezone";s:15:"setStoryConcept";s:15:"setStoryConcept";s:16:"editStoryConcept";s:16:"editStoryConcept";s:18:"browseStoryConcept";s:18:"browseStoryConcept";s:17:"setDefaultConcept";s:17:"setDefaultConcept";s:18:"deleteStoryConcept";s:18:"deleteStoryConcept";}s:6:"action";a:6:{s:5:"trash";s:5:"trash";s:8:"undelete";s:14:"undeleteAction";s:7:"hideOne";s:13:"hideOneAction";s:7:"hideAll";s:7:"hideAll";s:7:"comment";s:7:"comment";s:11:"editComment";s:11:"editComment";}s:6:"search";a:7:{s:9:"buildForm";s:9:"buildForm";s:10:"buildQuery";s:10:"buildQuery";s:9:"saveQuery";s:9:"saveQuery";s:11:"deleteQuery";s:11:"deleteQuery";s:6:"select";s:6:"select";s:5:"index";s:5:"index";s:10:"buildIndex";s:10:"buildIndex";}s:4:"tree";a:7:{s:6:"browse";s:6:"browse";s:10:"browseTask";s:10:"browseTask";s:11:"updateOrder";s:11:"updateOrder";s:11:"manageChild";s:11:"manageChild";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:3:"fix";s:3:"fix";}s:4:"file";a:5:{s:8:"download";s:8:"download";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:12:"uploadImages";s:12:"uploadImages";s:9:"setPublic";s:9:"setPublic";}s:4:"misc";a:1:{s:4:"ping";s:4:"ping";}s:6:"backup";a:7:{s:5:"index";s:5:"index";s:6:"backup";s:6:"backup";s:7:"restore";s:7:"restore";s:6:"delete";s:6:"delete";s:7:"setting";s:13:"settingAction";s:11:"rmPHPHeader";s:11:"rmPHPHeader";s:6:"change";s:6:"change";}s:4:"cron";a:7:{s:5:"index";s:5:"index";s:6:"turnon";s:6:"turnon";s:6:"create";s:12:"createAction";s:4:"edit";s:4:"edit";s:6:"toggle";s:6:"toggle";s:6:"delete";s:6:"delete";s:11:"openProcess";s:7:"restart";}s:7:"message";a:3:{s:5:"index";s:5:"index";s:7:"browser";s:7:"browser";s:7:"setting";s:7:"setting";}s:2:"im";a:2:{s:18:"downloadXxdPackage";s:11:"downloadXXD";s:5:"debug";s:5:"debug";}s:6:"client";a:4:{s:6:"browse";s:6:"browse";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";}s:7:"setting";a:1:{s:8:"xuanxuan";s:8:"xuanxuan";}s:6:"kanban";a:41:{s:5:"space";s:11:"spaceCommon";s:11:"createSpace";s:11:"createSpace";s:9:"editSpace";s:9:"editSpace";s:10:"closeSpace";s:10:"closeSpace";s:11:"deleteSpace";s:11:"deleteSpace";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:4:"view";s:4:"view";s:5:"close";s:5:"close";s:6:"delete";s:6:"delete";s:12:"createRegion";s:12:"createRegion";s:10:"editRegion";s:10:"editRegion";s:10:"sortRegion";s:10:"sortRegion";s:9:"sortGroup";s:9:"sortGroup";s:12:"deleteRegion";s:12:"deleteRegion";s:10:"createLane";s:10:"createLane";s:8:"sortLane";s:8:"sortLane";s:10:"deleteLane";s:10:"deleteLane";s:12:"createColumn";s:12:"createColumn";s:9:"setColumn";s:9:"setColumn";s:6:"setWIP";s:6:"setWIP";s:10:"sortColumn";s:10:"sortColumn";s:12:"deleteColumn";s:12:"deleteColumn";s:10:"createCard";s:10:"createCard";s:8:"editCard";s:8:"editCard";s:8:"viewCard";s:8:"viewCard";s:8:"sortCard";s:8:"sortCard";s:10:"deleteCard";s:10:"deleteCard";s:12:"assigntoCard";s:12:"assigntoCard";s:8:"moveCard";s:8:"moveCard";s:12:"setCardColor";s:12:"setCardColor";s:18:"viewArchivedColumn";s:18:"viewArchivedColumn";s:15:"batchCreateCard";s:15:"batchCreateCard";s:12:"editLaneName";s:12:"editLaneName";s:13:"editLaneColor";s:13:"editLaneColor";s:11:"splitColumn";s:11:"splitColumn";s:13:"archiveColumn";s:13:"archiveColumn";s:13:"restoreColumn";s:13:"restoreColumn";s:11:"archiveCard";s:11:"archiveCard";s:16:"viewArchivedCard";s:16:"viewArchivedCard";s:11:"restoreCard";s:11:"restoreCard";}s:4:"mail";a:10:{s:5:"index";s:5:"index";s:6:"detect";s:12:"detectAction";s:4:"edit";s:4:"edit";s:4:"save";s:10:"saveAction";s:4:"test";s:4:"test";s:5:"reset";s:11:"resetAction";s:6:"browse";s:6:"browse";s:6:"delete";s:6:"delete";s:11:"batchDelete";s:11:"batchDelete";s:6:"resend";s:12:"resendAction";}s:9:"datatable";a:1:{s:9:"setGlobal";s:9:"setGlobal";}s:7:"webhook";a:7:{s:6:"browse";s:4:"list";s:6:"create";s:6:"create";s:4:"edit";s:4:"edit";s:6:"delete";s:6:"delete";s:3:"log";s:9:"logAction";s:4:"bind";s:4:"bind";s:10:"chooseDept";s:10:"chooseDept";}}';
