#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中是否存在feelec_delete字段
"""

import pymysql

def check_delete_field():
    """检查feelec_delete字段是否存在"""
    
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='**********',
            user='qyuser',
            password='C~w9d4kaWS',
            database='ticket',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 检查feelec_project表结构
        print("🔍 检查feelec_project表结构...")
        cursor.execute("DESCRIBE feelec_project")
        project_columns = cursor.fetchall()
        
        project_has_delete = False
        for column in project_columns:
            if 'delete' in column[0].lower():
                print(f"   ✅ 找到删除字段: {column[0]} ({column[1]})")
                project_has_delete = True
        
        if not project_has_delete:
            print("   ❌ feelec_project表中没有找到删除字段")
        
        # 检查feelec_ticket表结构
        print("\n🔍 检查feelec_ticket表结构...")
        cursor.execute("DESCRIBE feelec_ticket")
        ticket_columns = cursor.fetchall()
        
        ticket_has_delete = False
        for column in ticket_columns:
            if 'delete' in column[0].lower():
                print(f"   ✅ 找到删除字段: {column[0]} ({column[1]})")
                ticket_has_delete = True
        
        if not ticket_has_delete:
            print("   ❌ feelec_ticket表中没有找到删除字段")
        
        # 检查feelec_ticket_status表结构
        print("\n🔍 检查feelec_ticket_status表结构...")
        cursor.execute("DESCRIBE feelec_ticket_status")
        status_columns = cursor.fetchall()
        
        status_has_delete = False
        for column in status_columns:
            if 'delete' in column[0].lower():
                print(f"   ✅ 找到删除字段: {column[0]} ({column[1]})")
                status_has_delete = True
        
        if not status_has_delete:
            print("   ❌ feelec_ticket_status表中没有找到删除字段")
        
        # 测试查询不带删除条件
        print("\n🔍 测试查询项目数据...")
        cursor.execute("SELECT COUNT(*) FROM feelec_project")
        project_count = cursor.fetchone()[0]
        print(f"   📊 项目总数: {project_count}")
        
        print("\n🔍 测试查询工单数据...")
        cursor.execute("SELECT COUNT(*) FROM feelec_ticket")
        ticket_count = cursor.fetchone()[0]
        print(f"   📊 工单总数: {ticket_count}")
        
        # 测试查询状态数据
        print("\n🔍 测试查询状态数据...")
        cursor.execute("SELECT COUNT(*) FROM feelec_ticket_status")
        status_count = cursor.fetchone()[0]
        print(f"   📊 状态总数: {status_count}")
        
        cursor.close()
        connection.close()
        
        return {
            'project_has_delete': project_has_delete,
            'ticket_has_delete': ticket_has_delete,
            'status_has_delete': status_has_delete,
            'project_count': project_count,
            'ticket_count': ticket_count,
            'status_count': status_count
        }
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None

if __name__ == "__main__":
    print("🔍 检查数据库删除字段")
    print("=" * 50)
    
    result = check_delete_field()
    
    if result:
        print("\n📊 检查结果:")
        print(f"  项目表有删除字段: {'是' if result['project_has_delete'] else '否'}")
        print(f"  工单表有删除字段: {'是' if result['ticket_has_delete'] else '否'}")
        print(f"  状态表有删除字段: {'是' if result['status_has_delete'] else '否'}")
        print(f"  项目总数: {result['project_count']}")
        print(f"  工单总数: {result['ticket_count']}")
        print(f"  状态总数: {result['status_count']}")
        
        if not any([result['project_has_delete'], result['ticket_has_delete'], result['status_has_delete']]):
            print("\n💡 建议: 移除SQL查询中的feelec_delete条件")
