#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查阿里云数据库的真实结构和数据
"""

import pymysql

def check_aliyun_database():
    """检查阿里云数据库"""
    print("🔍 检查阿里云数据库...")
    
    # 阿里云数据库配置
    db_config = {
        'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
        'port': 3306,
        'user': 'cyh',
        'password': 'Qq188788',
        'database': 'kanban2',
        'charset': 'utf8mb4'
    }
    
    print(f"数据库配置:")
    print(f"   host: {db_config['host']}")
    print(f"   port: {db_config['port']}")
    print(f"   user: {db_config['user']}")
    print(f"   password: {'*' * len(db_config['password'])}")
    print(f"   database: {db_config['database']}")
    
    try:
        print("\n🔗 连接阿里云数据库...")
        connection = pymysql.connect(**db_config)
        print("✅ 阿里云数据库连接成功")
        
        with connection.cursor() as cursor:
            # 显示MySQL版本和当前数据库
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"   MySQL版本: {version}")
            
            cursor.execute("SELECT DATABASE()")
            database = cursor.fetchone()[0]
            print(f"   当前数据库: {database}")
            
            # 显示所有表
            print(f"\n📋 数据库中的所有表:")
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            print(f"共有 {len(tables)} 个表:")
            for table in tables:
                table_name = table[0]
                print(f"   📝 {table_name}")
                
                # 检查表的数据量
                cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"      数据量: {count} 条")
            
            # 检查督办相关表
            print(f"\n🔍 检查督办相关表:")
            supervision_tables = [
                'supervision_items', 'companies', 'company_progress', 
                'company_supervision_status', 'supervision_status_history'
            ]
            
            existing_supervision_tables = []
            for table_name in supervision_tables:
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                if cursor.fetchone():
                    existing_supervision_tables.append(table_name)
                    print(f"✅ {table_name} 表存在")
                    
                    # 显示表结构
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = cursor.fetchall()
                    print(f"   表结构:")
                    for column in columns:
                        print(f"      {column[0]} ({column[1]}) - NULL:{column[2]} - Default:{column[4]}")
                    
                    # 显示数据量
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"   数据量: {count} 条")
                    
                    # 显示前几条数据
                    if count > 0:
                        cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                        rows = cursor.fetchall()
                        print(f"   前3条数据:")
                        for i, row in enumerate(rows):
                            print(f"      {i+1}. {row}")
                    
                    print()
                else:
                    print(f"❌ {table_name} 表不存在")
            
            # 查找可能存在的督办相关表
            print(f"\n📊 查找可能相关的表:")
            supervision_related = []
            for table in tables:
                table_name = table[0]
                if any(keyword in table_name.lower() for keyword in ['supervision', 'company', 'progress', 'item']):
                    supervision_related.append(table_name)
            
            if supervision_related:
                print(f"找到可能相关的表:")
                for table_name in supervision_related:
                    print(f"   📝 {table_name}")
                    
                    # 检查表结构
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = cursor.fetchall()
                    print(f"      字段: {[col[0] for col in columns]}")
                    
                    # 检查数据量
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"      数据量: {count} 条")
                    
                    if count > 0 and count <= 10:
                        cursor.execute(f"SELECT * FROM {table_name}")
                        rows = cursor.fetchall()
                        print(f"      所有数据:")
                        for i, row in enumerate(rows):
                            print(f"         {i+1}. {row}")
                    
                    print()
            else:
                print("没有找到督办相关的表")
            
            # 检查是否有其他可能的督办数据表
            print(f"\n🔍 检查其他可能的督办数据表:")
            for table in tables:
                table_name = table[0]
                if 'supervision' in table_name.lower() or '督办' in table_name:
                    print(f"   📝 发现督办相关表: {table_name}")
                    
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = cursor.fetchall()
                    print(f"      表结构:")
                    for column in columns:
                        print(f"         {column[0]} ({column[1]})")
                    
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"      数据量: {count} 条")
                    
                    if count > 0:
                        cursor.execute(f"SELECT * FROM {table_name} LIMIT 5")
                        rows = cursor.fetchall()
                        print(f"      前5条数据:")
                        for i, row in enumerate(rows):
                            print(f"         {i+1}. {row}")
                    print()
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 阿里云数据库检查失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 检查阿里云数据库的真实结构和数据")
    print("=" * 60)
    
    success = check_aliyun_database()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 阿里云数据库检查完成")
        print("\n📋 下一步:")
        print("   1. 根据实际的数据库结构调整API")
        print("   2. 如果督办表不存在，在阿里云数据库中创建")
        print("   3. 如果表存在但结构不对，修改表结构")
        print("   4. 插入测试数据")
        print("   5. 重启后端服务")
        print("   6. 测试API功能")
    else:
        print("❌ 阿里云数据库检查失败")
    
    print("🏁 检查完成")

if __name__ == "__main__":
    main()
