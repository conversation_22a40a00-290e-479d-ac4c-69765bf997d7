title: table zt_review
desc: "项目评审"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: project
    note: "所属项目"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: title
    note: "评审标题"
    range: 1-10000
    prefix: "这里一个评审标题"
    postfix: ""
    loop: 0
    format: ""
  - field: object
    note: "评审对象ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: template
    note: "模板ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: "评审状态"
    range: draft,wait,reviewing,pass,fail,auditing,done
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: reviewedBy
    note: "由谁评审"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: auditedBy
    note: "由谁审计"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: "由谁创建"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdDate
    note: "创建日期"
    range: "(M)-(w)"
    type: timestamp
    format: "YYYY-MM-DD"
  - field: deadline
    note: "创建日期"
    range: "(M)-(w)"
    type: timestamp
    format: "YYYY-MM-DD"
  - field: lastReviewedBy
    note: "最后评审者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: lastAuditedBy
    note: "最后审计者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: lastEditedBy
    note: "最后编辑者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: result
    note: "评审结果"
    range: pass,fail,needfix
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: auditResult
    note: "审计结果"
    range: pass,fail,needfix
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
