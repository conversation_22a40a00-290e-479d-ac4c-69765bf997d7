title: table zt_reviewissue
desc: "评审问题"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: project
    note: "所属项目"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: review
    note: "所属评审"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: injection
    note: "注入阶段"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: identify
    note: ""
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "问题类型"
    range: review
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: listID
    note: "检查清单ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: title
    note: "问题标题"
    range: 1-10000
    prefix: "这是问题标题"
    postfix: ""
    loop: 0
    format: ""
  - field: opinion
    note: "问题意见"
    range: 1-10000
    prefix: "这里问题意见"
    postfix: ""
    loop: 0
    format: ""
  - field: opinionDate
    note: "意见日期"
    from: common.date.v1.yaml
    use: dateB
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: "状态"
    range: active,resolved,closed
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: resolution
    note: "解决方案"
    range: bydesign,duplicate,external,fixed,notrepro,postponed,willnotfix
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: resolutionBy
    note: "解决者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: resolutionDate
    note: "解决日期"
    from: common.date.v1.yaml
    use: dateB
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdBy
    note: "创建者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdDate
    note: "创建日期"
    from: common.date.v1.yaml
    use: dateB
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
