#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import requests
import json

def main():
    print("🚀 简单API测试")
    
    # 测试API
    try:
        print("📋 测试新督办管理API...")
        response = requests.get("http://localhost:8000/api/v1/new-supervision/items")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            
            if 'data' in data and 'companies' in data:
                items = data['data']
                companies = data['companies']
                
                print(f"   📊 督办事项数量: {len(items)}")
                print(f"   🏢 公司数量: {len(companies)}")
                
                # 显示前3个督办事项
                print(f"   📝 前3个督办事项:")
                for i, item in enumerate(items[:3]):
                    print(f"      {item.get('sequence_number', 'N/A')}. {item.get('work_theme', 'N/A')}")
                    print(f"         维度: {item.get('work_dimension', 'N/A')}")
                    print(f"         考核指标: {item.get('is_annual_assessment', 'N/A')}")
                    print(f"         进度: {item.get('overall_progress', 'N/A')}")
                
                print("🎉 督办管理功能完全可用！")
                print("\n🌐 访问地址: http://localhost:3000/new-supervision")
                
            else:
                print("❌ API返回数据格式错误")
                print(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ API调用失败")
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    main()
