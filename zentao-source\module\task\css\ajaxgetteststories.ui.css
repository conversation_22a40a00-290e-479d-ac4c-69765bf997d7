#testTaskTable {table-layout: fixed;}
#testTaskTable tr {border: none;}
#testTaskTable tr > th:not(.c-actions) {border: 1px solid var(--form-control-border);}
#testTaskTable tr > th {background-color: #FFF; font-weight: unset;}
#testTaskTable tr > td {padding: 0; border-color: var(--form-control-border);}
#testTaskTable .form-control {border-radius: 0;}
#testTaskTable .c-estimate {padding-right: 0.5px;}
