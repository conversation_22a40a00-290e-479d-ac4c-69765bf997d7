.execute-info {position: relative; top: 5px;}
#type_chosen .chosen-drop {width: 150px;}
.table-footer .pager {margin: 0px; height: 30px; padding-top: 10px;}
.table-footer .pager .icon-play.icon-flip-horizontal:before {content:"\e314";}
.table-footer .pager .icon-play:before {content:"\e315";}
.modal.with-titlebar .modal-header, .no-stash {margin-bottom: 10px;}
.modal.with-titlebar .modal-header .modal-title {display: block;}

.list {color: #333; background-color: #fff; border: solid #DDD 1px; border-radius: 3px;}
.list > header
{
  padding: 8px 15px;
  border-bottom: solid #DDD 1px;
  font-weight: inherit;
  font-size: inherit;
  background-color: #F5F5F5;
  color: #333;
  background-image: -moz-linear-gradient(top,#fbfbfb 0%,#f2f2f2 100%);
  background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0%,#fbfbfb),color-stop(100%,#f2f2f2));
  background-image: -webkit-linear-gradient(top,#fbfbfb 0%,#f2f2f2 100%);
  background-image: -o-linear-gradient(top,#fbfbfb 0%,#f2f2f2 100%);
  background-image: linear-gradient(top,#fbfbfb 0%,#f2f2f2 100%);
}
.list > header h2 {font-size: 14px; font-weight: bold; margin: 0; padding: 0; line-height: 20px;}
.list-condensed > .items > .item {padding: 10px 15px; border-bottom: 1px solid #e5e5e5; transition: all .5s cubic-bezier(.175,.885,.32,1);}
.items .item .text-muted {color: #757575;}
.items .item-heading h4 {margin-top: 5px; font-size: 14px; line-height: 1.5; font-weight: normal;}
.item:hover {background-color: #f5f5f5;}

.modal-iframe .modal-content .modal-header {margin: 0px;}
.modal-iframe .modal-content .modal-header .modal-title {position: absolute; right: 45px; background: #F8FAFE; height: 45px; padding: 8px 0px; width: 200px; text-align: right;}
.modal-iframe .modal-content .modal-header .modal-title .icon-eye-open {display: none;}
.modal-iframe .modal-content .modal-header .modal-title a.btn {border-color: transparent; background: none; padding: 0px; border-radius: 2px; color: #036; padding: 4px 7px;}
.modal-iframe .modal-content .modal-header .modal-title a.btn .icon-eye-open {display: inline;}
.modal-iframe .modal-header .close {top: 17px !important;}
.input-group-btn.goback {padding-left: 10px;}
.input-group-btn.select {min-width: 60px; margin-left: -2px;}
.input-group .chosen-container {min-width: 250px;}
#words {border-right-width: 0px;}
#submit {border: 0px;}
