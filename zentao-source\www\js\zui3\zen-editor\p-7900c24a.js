var e=Object.defineProperty,n={},t=e=>"object"==(e=typeof e)||"function"===e;function l(e){var n,t,l;return null!=(l=null==(t=null==(n=e.head)?void 0:n.querySelector('meta[name="csp-nonce"]'))?void 0:t.getAttribute("content"))?l:void 0}((n,t)=>{for(var l in t)e(n,l,{get:t[l],enumerable:!0})})({},{err:()=>s,map:()=>r,ok:()=>o,unwrap:()=>u,unwrapErr:()=>a});var o=e=>({isOk:!0,isErr:!1,value:e}),s=e=>({isOk:!1,isErr:!0,value:e});function r(e,n){if(e.isOk){const t=n(e.value);return t instanceof Promise?t.then((e=>o(e))):o(t)}if(e.isErr)return s(e.value);throw"should never get here"}var i,c,u=e=>{if(e.isOk)return e.value;throw e.value},a=e=>{if(e.isErr)return e.value;throw e.value},f="slot-fb{display:contents}slot-fb[hidden]{display:none}",d="http://www.w3.org/1999/xlink",p=["formAssociatedCallback","formResetCallback","formDisabledCallback","formStateRestoreCallback"],v=(e,n,...l)=>{let o=null,s=null,r=!1,i=!1;const c=[],u=n=>{for(let l=0;l<n.length;l++)o=n[l],Array.isArray(o)?u(o):null!=o&&"boolean"!=typeof o&&((r="function"!=typeof e&&!t(o))&&(o+=""),r&&i?c[c.length-1].t+=o:c.push(r?m(null,o):o),i=r)};if(u(l),n){n.key&&(s=n.key);{const e=n.className||n.class;e&&(n.class="object"!=typeof e?e:Object.keys(e).filter((n=>e[n])).join(" "))}}if("function"==typeof e)return e(null===n?{}:n,c,h);const a=m(e,null);return a.l=n,c.length>0&&(a.o=c),a.i=s,a},m=(e,n)=>({u:0,p:e,t:n,v:null,o:null,l:null,i:null}),y={},h={forEach:(e,n)=>e.map(b).forEach(n),map:(e,n)=>e.map(b).map(n).map($)},b=e=>({vattrs:e.l,vchildren:e.o,vkey:e.i,vname:e.m,vtag:e.p,vtext:e.t}),$=e=>{if("function"==typeof e.vtag){const n={...e.vattrs};return e.vkey&&(n.key=e.vkey),e.vname&&(n.name=e.vname),v(e.vtag,n,...e.vchildren||[])}const n=m(e.vtag,e.vtext);return n.l=e.vattrs,n.o=e.vchildren,n.i=e.vkey,n.m=e.vname,n},w=e=>ie(e).$hostElement$,k=(e,n,t)=>{const l=w(e);return{emit:e=>g(l,n,{bubbles:!!(4&t),composed:!!(2&t),cancelable:!!(1&t),detail:e})}},g=(e,n,t)=>{const l=he.ce(n,t);return e.dispatchEvent(l),l},j=new WeakMap,O=e=>"sc-"+e.h,S=(e,n,l,o,s,r)=>{if(l!==o){let i=ae(e,n),c=n.toLowerCase();if("class"===n){const n=e.classList,t=E(l),s=E(o);n.remove(...t.filter((e=>e&&!s.includes(e)))),n.add(...s.filter((e=>e&&!t.includes(e))))}else if("style"===n){for(const n in l)o&&null!=o[n]||(n.includes("-")?e.style.removeProperty(n):e.style[n]="");for(const n in o)l&&o[n]===l[n]||(n.includes("-")?e.style.setProperty(n,o[n]):e.style[n]=o[n])}else if("key"===n);else if("ref"===n)o&&o(e);else if(i||"o"!==n[0]||"n"!==n[1]){const u=t(o);if((i||u&&null!==o)&&!s)try{if(e.tagName.includes("-"))e[n]=o;else{const t=null==o?"":o;"list"===n?i=!1:null!=l&&e[n]==t||(e[n]=t)}}catch(e){}let a=!1;c!==(c=c.replace(/^xlink\:?/,""))&&(n=c,a=!0),null==o||!1===o?!1===o&&""!==e.getAttribute(n)||(a?e.removeAttributeNS(d,n):e.removeAttribute(n)):(!i||4&r||s)&&!u&&(o=!0===o?"":o,a?e.setAttributeNS(d,n,o):e.setAttribute(n,o))}else if(n="-"===n[2]?n.slice(3):ae(me,c)?c.slice(2):c[2]+n.slice(3),l||o){const t=n.endsWith(M);n=n.replace(x,""),l&&he.rel(e,n,l,t),o&&he.ael(e,n,o,t)}}},C=/\s/,E=e=>e?e.split(C):[],M="Capture",x=RegExp(M+"$"),P=(e,t,l)=>{const o=11===t.v.nodeType&&t.v.host?t.v.host:t.v,s=e&&e.l||n,r=t.l||n;for(const e of R(Object.keys(s)))e in r||S(o,e,s[e],void 0,l,t.u);for(const e of R(Object.keys(r)))S(o,e,s[e],r[e],l,t.u)};function R(e){return e.includes("ref")?[...e.filter((e=>"ref"!==e)),"ref"]:e}var A=!1,D=(e,n,t)=>{var l,o,s;const r=n.o[t];let u,a,f=0;if(null!==r.t)u=r.v=ye.createTextNode(r.t);else{u=r.v=ye.createElement(r.p),P(null,r,A),null!=i&&u["s-si"]!==i&&u.classList.add(u["s-si"]=i);{const e=(null==(l=n.v)?void 0:l["s-rsc"])||(null==(o=n.v)?void 0:o["s-si"])||(null==(s=n.v)?void 0:s["s-sc"]);e&&(u["s-rsc"]=e,!u.classList.contains(e)&&u.classList.add(e))}if(r.o)for(f=0;f<r.o.length;++f)a=D(e,r,f),a&&u.appendChild(a)}return u["s-hn"]=c,u},L=(e,n,t,l,o,s)=>{let r,i=e;for(i.shadowRoot&&i.tagName===c&&(i=i.shadowRoot);o<=s;++o)l[o]&&(r=D(null,t,o),r&&(l[o].v=r,i.insertBefore(r,n)))},F=(e,n,t)=>{for(let l=n;l<=t;++l){const n=e[l];if(n){const e=n.v;U(n),e&&e.remove()}}},N=(e,n,t=!1)=>e.p===n.p&&(!!t||e.i===n.i),T=(e,n,t=!1)=>{const l=n.v=e.v,o=e.o,s=n.o,r=n.t;null===r?(P(e,n,A),null!==o&&null!==s?((e,n,t,l,o=!1)=>{let s,r,i=0,c=0,u=0,a=0,f=n.length-1,d=n[0],p=n[f],v=l.length-1,m=l[0],y=l[v];for(;i<=f&&c<=v;)if(null==d)d=n[++i];else if(null==p)p=n[--f];else if(null==m)m=l[++c];else if(null==y)y=l[--v];else if(N(d,m,o))T(d,m,o),d=n[++i],m=l[++c];else if(N(p,y,o))T(p,y,o),p=n[--f],y=l[--v];else if(N(d,y,o))T(d,y,o),e.insertBefore(d.v,p.v.nextSibling),d=n[++i],y=l[--v];else if(N(p,m,o))T(p,m,o),e.insertBefore(p.v,d.v),p=n[--f],m=l[++c];else{for(u=-1,a=i;a<=f;++a)if(n[a]&&null!==n[a].i&&n[a].i===m.i){u=a;break}u>=0?(r=n[u],r.p!==m.p?s=D(n&&n[c],t,u):(T(r,m,o),n[u]=void 0,s=r.v),m=l[++c]):(s=D(n&&n[c],t,c),m=l[++c]),s&&d.v.parentNode.insertBefore(s,d.v)}i>f?L(e,null==l[v+1]?null:l[v+1].v,t,l,c,v):c>v&&F(n,i,f)})(l,o,n,s,t):null!==s?(null!==e.t&&(l.textContent=""),L(l,null,n,s,0,s.length-1)):null!==o&&F(o,0,o.length-1)):e.t!==r&&(l.data=r)},U=e=>{e.l&&e.l.ref&&e.l.ref(null),e.o&&e.o.map(U)},W=(e,n)=>{n&&!e.$&&n["s-p"]&&n["s-p"].push(new Promise((n=>e.$=n)))},H=(e,n)=>{if(e.u|=16,!(4&e.u))return W(e,e.k),Ee((()=>q(e,n)));e.u|=512},q=(e,n)=>{const t=e.j;let l;return n&&(e.u|=256,e.O&&(e.O.map((([e,n])=>K(t,e,n))),e.O=void 0),l=K(t,"componentWillLoad")),z(l,(()=>_(e,t,n)))},z=(e,n)=>V(e)?e.then(n):n(),V=e=>e instanceof Promise||e&&e.then&&"function"==typeof e.then,_=async(e,n,t)=>{var o;const s=e.$hostElement$,r=s["s-rc"];t&&(e=>{const n=e.S,t=e.$hostElement$,o=n.u,s=((e,n)=>{var t;const o=O(n),s=ve.get(o);if(e=11===e.nodeType?e:ye,s)if("string"==typeof s){let r,i=j.get(e=e.head||e);if(i||j.set(e,i=new Set),!i.has(o)){{r=ye.createElement("style"),r.innerHTML=s;const n=null!=(t=he.C)?t:l(ye);null!=n&&r.setAttribute("nonce",n),e.insertBefore(r,e.querySelector("link"))}4&n.u&&(r.innerHTML+=f),i&&i.add(o)}}else e.adoptedStyleSheets.includes(s)||(e.adoptedStyleSheets=[...e.adoptedStyleSheets,s]);return o})(t.shadowRoot?t.shadowRoot:t.getRootNode(),n);10&o&&(t["s-sc"]=s,t.classList.add(s+"-h"),2&o&&t.classList.add(s+"-s"))})(e);B(e,n,s,t),r&&(r.map((e=>e())),s["s-rc"]=void 0);{const n=null!=(o=s["s-p"])?o:[],t=()=>G(e);0===n.length?t():(Promise.all(n).then(t),e.u|=4,n.length=0)}},B=(e,n,t,l)=>{try{n=n.render(),e.u&=-17,e.u|=2,((e,n,t=!1)=>{const l=e.$hostElement$,o=e.M||m(null,null),s=(e=>e&&e.p===y)(n)?n:v(null,null,n);if(c=l.tagName,t&&s.l)for(const e of Object.keys(s.l))l.hasAttribute(e)&&!["key","ref","style","class"].includes(e)&&(s.l[e]=l[e]);s.p=null,s.u|=4,e.M=s,s.v=o.v=l.shadowRoot||l,i=l["s-sc"],T(o,s,t)})(e,n,l)}catch(n){fe(n,e.$hostElement$)}return null},G=e=>{const n=e.$hostElement$,t=e.j,l=e.k;K(t,"componentDidRender"),64&e.u?K(t,"componentDidUpdate"):(e.u|=64,Q(n),K(t,"componentDidLoad"),e.P(n),l||J()),e.R(n),e.$&&(e.$(),e.$=void 0),512&e.u&&Ce((()=>H(e,!1))),e.u&=-517},I=e=>{{const n=ie(e),t=n.$hostElement$.isConnected;return t&&2==(18&n.u)&&H(n,!1),t}},J=()=>{Q(ye.documentElement),Ce((()=>g(me,"appload",{detail:{namespace:"zen-editor"}})))},K=(e,n,t)=>{if(e&&e[n])try{return e[n](t)}catch(e){fe(e)}},Q=e=>e.classList.add("hydrated"),X=(e,n,l)=>{var o;const s=e.prototype;if(64&n.u&&1&l&&p.forEach((e=>Object.defineProperty(s,e,{value(...n){const t=ie(this),l=t.j;if(l){const t=l[e];"function"==typeof t&&t.call(l,...n)}else t.A.then((t=>{const l=t[e];"function"==typeof l&&l.call(t,...n)}))}}))),n.D){e.watchers&&(n.L=e.watchers);const r=Object.entries(n.D);if(r.map((([e,[o]])=>{31&o||2&l&&32&o?Object.defineProperty(s,e,{get(){return((e,n)=>ie(this).F.get(n))(0,e)},set(l){((e,n,l,o)=>{const s=ie(e),r=s.$hostElement$,i=s.F.get(n),c=s.u,u=s.j;if(l=((e,n)=>null==e||t(e)?e:4&n?"false"!==e&&(""===e||!!e):2&n?parseFloat(e):1&n?e+"":e)(l,o.D[n][0]),(!(8&c)||void 0===i)&&l!==i&&(!Number.isNaN(i)||!Number.isNaN(l))&&(s.F.set(n,l),u)){if(o.L&&128&c){const e=o.L[n];e&&e.map((e=>{try{u[e](l,i,n)}catch(e){fe(e,r)}}))}2==(18&c)&&H(s,!1)}})(this,e,l,n)},configurable:!0,enumerable:!0}):1&l&&64&o&&Object.defineProperty(s,e,{value(...n){var t;const l=ie(this);return null==(t=null==l?void 0:l.N)?void 0:t.then((()=>{var t;return null==(t=l.j)?void 0:t[e](...n)}))}})})),1&l){const t=new Map;s.attributeChangedCallback=function(e,l,o){he.jmp((()=>{var r;const i=t.get(e);if(this.hasOwnProperty(i))o=this[i],delete this[i];else{if(s.hasOwnProperty(i)&&"number"==typeof this[i]&&this[i]==o)return;if(null==i){const t=ie(this),s=null==t?void 0:t.u;if(s&&!(8&s)&&128&s&&o!==l){const s=t.j,i=null==(r=n.L)?void 0:r[e];null==i||i.forEach((n=>{null!=s[n]&&s[n].call(s,o,l,e)}))}return}}this[i]=(null!==o||"boolean"!=typeof this[i])&&o}))},e.observedAttributes=Array.from(new Set([...Object.keys(null!=(o=n.L)?o:{}),...r.filter((([e,n])=>15&n[0])).map((([e,n])=>{const l=n[1]||e;return t.set(l,e),l}))]))}}return e},Y=e=>{K(e,"connectedCallback")},Z=e=>{K(e,"disconnectedCallback")},ee=(e,n={})=>{var t;const o=[],s=n.exclude||[],r=me.customElements,i=ye.head,c=i.querySelector("meta[charset]"),u=ye.createElement("style"),a=[];let d,p=!0;Object.assign(he,n),he.T=new URL(n.resourcesUrl||"./",ye.baseURI).href;let v=!1;if(e.map((e=>{e[1].map((n=>{var t;const l={u:n[0],h:n[1],D:n[2],U:n[3]};4&l.u&&(v=!0),l.D=n[2],l.U=n[3],l.L=null!=(t=n[4])?t:{};const i=l.h,c=class extends HTMLElement{constructor(e){super(e),ue(e=this,l),1&l.u&&e.attachShadow({mode:"open"})}connectedCallback(){d&&(clearTimeout(d),d=null),p?a.push(this):he.jmp((()=>(e=>{if(!(1&he.u)){const n=ie(e),t=n.S,l=()=>{};if(1&n.u)te(e,n,t.U),(null==n?void 0:n.j)?Y(n.j):(null==n?void 0:n.A)&&n.A.then((()=>Y(n.j)));else{n.u|=1;{let t=e;for(;t=t.parentNode||t.host;)if(t["s-p"]){W(n,n.k=t);break}}t.D&&Object.entries(t.D).map((([n,[t]])=>{if(31&t&&e.hasOwnProperty(n)){const t=e[n];delete e[n],e[n]=t}})),(async(e,n,t)=>{let l;if(!(32&n.u)){if(n.u|=32,t.W){if(l=pe(t),l.then){const e=()=>{};l=await l,e()}l.isProxied||(t.L=l.watchers,X(l,t,2),l.isProxied=!0);const e=()=>{};n.u|=8;try{new l(n)}catch(e){fe(e)}n.u&=-9,n.u|=128,e(),Y(n.j)}else l=e.constructor,customElements.whenDefined(t.h).then((()=>n.u|=128));if(l.style){let e=l.style;const n=O(t);if(!ve.has(n)){const l=()=>{};((e,n,t)=>{let l=ve.get(e);$e&&t?(l=l||new CSSStyleSheet,"string"==typeof l?l=n:l.replaceSync(n)):l=n,ve.set(e,l)})(n,e,!!(1&t.u)),l()}}}const o=n.k,s=()=>H(n,!0);o&&o["s-rc"]?o["s-rc"].push(s):s()})(e,n,t)}l()}})(this)))}disconnectedCallback(){he.jmp((()=>(async()=>{if(!(1&he.u)){const e=ie(this);e.H&&(e.H.map((e=>e())),e.H=void 0),(null==e?void 0:e.j)?Z(e.j):(null==e?void 0:e.A)&&e.A.then((()=>Z(e.j)))}})()))}componentOnReady(){return ie(this).A}};64&l.u&&(c.formAssociated=!0),l.W=e[0],s.includes(i)||r.get(i)||(o.push(i),r.define(i,X(c,l,1)))}))})),o.length>0&&(v&&(u.textContent+=f),u.textContent+=o+"{visibility:hidden}.hydrated{visibility:inherit}",u.innerHTML.length)){u.setAttribute("data-styles","");const e=null!=(t=he.C)?t:l(ye);null!=e&&u.setAttribute("nonce",e),i.insertBefore(u,c?c.nextSibling:i.firstChild)}p=!1,a.length?a.map((e=>e.connectedCallback())):he.jmp((()=>d=setTimeout(J,30)))},ne=(e,n)=>n,te=(e,n,t)=>{t&&t.map((([t,l,o])=>{const s=e,r=le(n,o),i=oe(t);he.ael(s,l,r,i),(n.H=n.H||[]).push((()=>he.rel(s,l,r,i)))}))},le=(e,n)=>t=>{try{256&e.u?e.j[n](t):(e.O=e.O||[]).push([n,t])}catch(e){fe(e)}},oe=e=>({passive:!!(1&e),capture:!!(2&e)}),se=e=>he.C=e,re=new WeakMap,ie=e=>re.get(e),ce=(e,n)=>re.set(n.j=e,n),ue=(e,n)=>{const t={u:0,$hostElement$:e,S:n,F:new Map};return t.N=new Promise((e=>t.R=e)),t.A=new Promise((e=>t.P=e)),e["s-p"]=[],e["s-rc"]=[],te(e,t,n.U),re.set(e,t)},ae=(e,n)=>n in e,fe=(e,n)=>(0,console.error)(e,n),de=new Map,pe=e=>{const n=e.h.replace(/-/g,"_"),t=e.W,l=de.get(t);return l?l[n]:import(`./${t}.entry.js`).then((e=>(de.set(t,e),e[n])),fe)
/*!__STENCIL_STATIC_IMPORT_SWITCH__*/},ve=new Map,me="undefined"!=typeof window?window:{},ye=me.document||{head:{}},he={u:0,T:"",jmp:e=>e(),raf:e=>requestAnimationFrame(e),ael:(e,n,t,l)=>e.addEventListener(n,t,l),rel:(e,n,t,l)=>e.removeEventListener(n,t,l),ce:(e,n)=>new CustomEvent(e,n)},be=e=>Promise.resolve(e),$e=(()=>{try{return new CSSStyleSheet,"function"==typeof(new CSSStyleSheet).replaceSync}catch(e){}return!1})(),we=!1,ke=[],ge=[],je=(e,n)=>t=>{e.push(t),we||(we=!0,n&&4&he.u?Ce(Se):he.raf(Se))},Oe=e=>{for(let n=0;n<e.length;n++)try{e[n](performance.now())}catch(e){fe(e)}e.length=0},Se=()=>{Oe(ke),Oe(ge),(we=ke.length>0)&&he.raf(Se)},Ce=e=>be().then(e),Ee=je(ge,!0);export{ne as F,y as H,ee as b,k as c,I as f,w as g,v as h,be as p,ce as r,se as s}