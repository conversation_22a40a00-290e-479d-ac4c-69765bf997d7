import{d as $,q as L,N as V,O as H,Q as z,P as g,R as F,c as d,w as e,r as n,o as a,e as t,m as T,t as E,L as I,f as o,h as O,A as G,F as R,p as C,U as q,V as J,W as Q,u as X,z as A,X as j,Y as S,Z as Y,$ as Z,y as ee,a0 as x,a1 as B,a2 as D,a3 as te,a4 as oe,J as ne,a5 as ae,x as le,a6 as se,a7 as ce,a8 as _e,M as de}from"./index.js";import{i as M}from"./icon-bb3d09e7.js";import{_ as re}from"./index-ba3ca6b4.js";import"./index-053cb545.js";const ie=$({__name:"index",props:{show:Boolean},emits:["close"],setup(c,{emit:l}){const{FishIcon:i,CloseIcon:s}=M.ionicons5,{StoreIcon:r,ObjectStorageIcon:u}=M.carbon,p=L(!1),v=l,f=c,m=V([{title:H("project.new_project"),key:z.CHART_HOME_NAME,icon:i,disabled:!1},{title:H("project.my_templete"),key:g.BASE_HOME_TEMPLATE_NAME,icon:u,disabled:!0},{title:H("project.template_market"),key:g.BASE_HOME_TEMPLATE_MARKET_NAME,icon:r,disabled:!0}]);F(f,h=>{p.value=h.show});const _=()=>{v("close",!1)},y=h=>{_();const k=q(),b=J(z.CHART_HOME_NAME,"href");Q(b,[k],void 0,!0)};return(h,k)=>{const b=n("n-text"),N=n("n-icon"),K=n("n-button"),P=n("n-space"),W=n("n-card"),U=n("n-modal");return a(),d(U,{show:p.value,"onUpdate:show":k[0]||(k[0]=w=>p.value=w),class:"go-create-modal",onAfterLeave:_},{default:e(()=>[t(P,{size:"large"},{default:e(()=>[t(W,{class:"card-box",hoverable:""},{header:e(()=>[t(b,{class:"card-box-tite"},{default:e(()=>[T(E(h.$t("project.create_tip")),1)]),_:1})]),"header-extra":e(()=>[t(b,{onClick:_},{default:e(()=>[t(N,{size:"20"},{default:e(()=>[(a(),d(I(o(s))))]),_:1})]),_:1})]),action:e(()=>[]),default:e(()=>[t(P,{class:"card-box-content",justify:"center"},{default:e(()=>[(a(!0),O(R,null,G(m.value,w=>(a(),d(K,{size:"large",disabled:w.disabled,key:w.key,onClick:y},{icon:e(()=>[t(N,{size:"18"},{default:e(()=>[(a(),d(I(w.icon)))]),_:2},1024)]),default:e(()=>[(a(),d(I(w.title)))]),_:2},1032,["disabled"]))),128))]),_:1})]),_:1})]),_:1})]),_:1},8,["show"])}}});var ue=C(ie,[["__scopeId","data-v-ea013926"]]);const pe=$({__name:"index",props:{collapsed:Boolean},setup(c){const{DuplicateIcon:l,DuplicateOutlineIcon:i}=M.ionicons5,s=X(),r=L(!1),u=()=>{r.value=!0},p=()=>{r.value=!1};return(v,f)=>{const m=n("n-icon"),_=n("n-button"),y=n("n-tooltip");return a(),O(R,null,[A("div",{onClick:u},[c.collapsed?(a(),d(y,{key:0,placement:"right",trigger:"hover"},{trigger:e(()=>[t(_,{ghost:"",type:"primary",size:"small"},{icon:e(()=>[t(m,null,{default:e(()=>[j(t(o(i),null,null,512),[[S,o(s).getDarkTheme]]),j(t(o(l),null,null,512),[[S,!o(s).getDarkTheme]])]),_:1})]),_:1})]),default:e(()=>[A("span",null,E(v.$t("project.create_btn")),1)]),_:1})):(a(),d(_,{key:1,ghost:"",type:"primary"},{icon:e(()=>[t(m,null,{default:e(()=>[j(t(o(i),null,null,512),[[S,o(s).getDarkTheme]]),j(t(o(l),null,null,512),[[S,!o(s).getDarkTheme]])]),_:1})]),default:e(()=>[A("span",null,E(v.$t("project.create_btn")),1)]),_:1}))]),t(o(ue),{show:r.value,onClose:p},null,8,["show"])],64)}}});const me={class:"go-aside-footer"},fe=$({__name:"index",props:{collapsed:Boolean},setup(c){const{DocumentTextIcon:l,CodeSlashIcon:i}=M.ionicons5,s=()=>{Y()},r=()=>{Z()};return(u,p)=>{const v=n("n-divider"),f=n("n-icon"),m=n("n-button"),_=n("n-text"),y=n("n-tooltip"),h=n("n-space");return a(),O("div",me,[t(v,{class:"go-mt-0"}),t(h,{justify:"space-around"},{default:e(()=>[c.collapsed?(a(),d(y,{key:0,placement:"right",trigger:"hover"},{trigger:e(()=>[t(m,{secondary:"",onClick:s},{icon:e(()=>[t(f,{size:"18"},{default:e(()=>[t(o(l))]),_:1})]),_:1})]),default:e(()=>[t(_,null,{default:e(()=>[T(E(u.$t("global.doc")),1)]),_:1})]),_:1})):(a(),d(m,{key:1,secondary:"",onClick:s},{icon:e(()=>[t(f,{size:"18"},{default:e(()=>[t(o(l))]),_:1})]),default:e(()=>[t(_,null,{default:e(()=>[T(E(u.$t("global.doc")),1)]),_:1})]),_:1})),c.collapsed?(a(),d(y,{key:2,placement:"right",trigger:"hover"},{trigger:e(()=>[t(m,{secondary:"",onClick:s},{icon:e(()=>[t(f,{size:"18"},{default:e(()=>[t(o(i))]),_:1})]),_:1})]),default:e(()=>[t(_,null,{default:e(()=>[T(E(u.$t("global.code_addr")),1)]),_:1})]),_:1})):(a(),d(m,{key:3,secondary:"",onClick:r},{icon:e(()=>[t(f,{size:"18"},{default:e(()=>[t(o(i))]),_:1})]),default:e(()=>[j(t(_,null,{default:e(()=>[T(E(u.$t("global.code_addr")),1)]),_:1},512),[[S,!c.collapsed]])]),_:1}))]),_:1})])}}});var ve=C(fe,[["__scopeId","data-v-9719eb6a"]]);const{GridIcon:He,TvOutlineIcon:ye}=M.ionicons5,{StoreIcon:he,ObjectStorageIcon:ge,DevicesIcon:Ee}=M.carbon,ke=()=>["all-project"],be=()=>{const c=window.$t;return ee([{key:"divider-1",type:"divider"},{label:()=>x("span",null,{default:()=>c("project.project")}),key:"all-project",icon:B(Ee),children:[{type:"group",label:()=>x("span",null,{default:()=>c("project.my")}),key:"my-project",children:[{label:()=>x(D,{to:{name:g.BASE_HOME_ITEMS_NAME}},{default:()=>c("project.all_project")}),key:g.BASE_HOME_ITEMS_NAME,icon:B(ye)},{label:()=>x(D,{to:{name:g.BASE_HOME_TEMPLATE_NAME}},{default:()=>c("project.my_templete")}),key:g.BASE_HOME_TEMPLATE_NAME,icon:B(ge)}]}]},{key:"divider-2",type:"divider"},{label:()=>x(D,{to:{name:g.BASE_HOME_TEMPLATE_MARKET_NAME}},{default:()=>c("project.template_market")}),key:g.BASE_HOME_TEMPLATE_MARKET_NAME,icon:B(he)}])};const we={class:"go-project-sider-flex"},Ae={class:"sider-bottom"},Me=$({__name:"index",setup(c){const l=L(!1),{getAsideCollapsedWidth:i}=te(oe()),s=ne(),r=ae(()=>s.name),u=L(r),p=be(),v=ke(),f=()=>{document.body.clientWidth<=950?l.value=!0:l.value=!1};return le(()=>{window.addEventListener("resize",f)}),se(()=>{window.removeEventListener("resize",f)}),(m,_)=>{const y=n("n-space"),h=n("n-menu"),k=n("n-layout-sider");return a(),d(k,{class:"go-project-sider",bordered:"","collapse-mode":"width","show-trigger":"bar",collapsed:l.value,"native-scrollbar":!1,"collapsed-width":o(i),width:o(ce),onCollapse:_[0]||(_[0]=b=>l.value=!0),onExpand:_[1]||(_[1]=b=>l.value=!1)},{default:e(()=>[A("div",we,[A("aside",null,[t(y,{vertical:"",class:"go-project-sider-top"},{default:e(()=>[t(o(pe),{collapsed:l.value},null,8,["collapsed"])]),_:1}),t(h,{value:u.value,options:o(p),"collapsed-width":o(i),"collapsed-icon-size":22,"default-expanded-keys":o(v)},null,8,["value","options","collapsed-width","default-expanded-keys"])]),A("div",Ae,[t(o(ve),{collapsed:l.value},null,8,["collapsed"])])])]),_:1},8,["collapsed","collapsed-width","width"])}}});var xe=C(Me,[["__scopeId","data-v-31f754a3"]]);const Te={};function je(c,l){const i=n("router-view");return a(),d(i,null,{default:e(({Component:s,route:r})=>[t(_e,{name:"fade",mode:"out-in",appear:""},{default:e(()=>[r.noKeepAlive?(a(),d(I(s),{key:r.fullPath})):(a(),d(de,{key:1},[(a(),d(I(s),{key:r.fullPath}))],1024))]),_:2},1024)]),_:1})}var Se=C(Te,[["render",je]]);const Ie={class:"go-project"},$e=$({__name:"index",setup(c){return(l,i)=>{const s=n("n-space"),r=n("router-view"),u=n("n-layout-content"),p=n("n-layout");return a(),O("div",Ie,[t(p,{"has-sider":"",position:"absolute"},{default:e(()=>[t(s,{vertical:""},{default:e(()=>[t(o(xe))]),_:1}),t(p,null,{default:e(()=>[t(o(re)),t(p,{id:"go-project-content-top",class:"content-top",position:"absolute","native-scrollbar":!1},{default:e(()=>[t(u,null,{default:e(()=>[t(o(Se),null,{default:e(()=>[t(r)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})])}}});var De=C($e,[["__scopeId","data-v-d9a83266"]]);export{De as default};
