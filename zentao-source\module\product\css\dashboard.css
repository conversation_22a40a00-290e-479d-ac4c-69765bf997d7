.block-release .panel-body, .block-dynamic .panel-body {height: 240px; overflow-x: hidden; overflow-y: auto; position: relative;}
.block-release .panel-body {padding-bottom: 50px;}
.block-release .panel-body > .btn.pull-right {position: absolute; right: 20px; bottom: 20px;}
.release-line li .title {overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}

.timeline > li:before {left: -26px;}
.timeline > li + li:after {left: -23px;}
.timeline-text {margin-left: -18px;}
.block-dynamic .label-action {padding: 0 6px;}
.block-dynamic .label-action + a {padding-left: 6px;}
.timeline > li.active:before {left: -30px;}
.timeline > li > div:after {left: -27px;}
.timeline .timeline-text {display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}
.timeline > li > div > .timeline-tag, .timeline > li > div > .timeline-text > .label-action {color: #838A9D;}
.timeline > li > div > .timeline-text > a {color: #313C52;}
