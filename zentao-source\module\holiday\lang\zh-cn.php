<?php
if(!isset($lang->holiday)) $lang->holiday = new stdclass();
$lang->holiday->common = '节假日';
$lang->holiday->browse = '浏览';
$lang->holiday->create = '新建';
$lang->holiday->edit   = '编辑';
$lang->holiday->delete = '删除';

$lang->holiday->createAction = '创建节假日';
$lang->holiday->editAction   = '编辑节假日';
$lang->holiday->deleteAction = '删除节假日';
$lang->holiday->importAction = '导入节假日';

$lang->holiday->id    = '编号';
$lang->holiday->name  = '名称';
$lang->holiday->desc  = '描述';
$lang->holiday->type  = '类型';
$lang->holiday->begin = '开始日期';
$lang->holiday->end   = '结束日期';
$lang->holiday->all   = '所有';

$lang->holiday->holiday   = '假期';
$lang->holiday->checkYear = '年份选择';

$lang->holiday->typeList['holiday'] = '假期';
$lang->holiday->typeList['working'] = '补班';

$lang->holiday->emptyTip      = '暂时没有节假日。';
$lang->holiday->confirmDelete = '确认删除节假日？';
$lang->holiday->importTip     = '可以在此 %s国家法定节假日。';
