var Ht=Object.defineProperty,Pt=Object.defineProperties;var Kt=Object.getOwnPropertyDescriptors;var vt=Object.getOwnPropertySymbols;var Nt=Object.prototype.hasOwnProperty,Yt=Object.prototype.propertyIsEnumerable;var ht=(e,n,t)=>n in e?Ht(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,G=(e,n)=>{for(var t in n||(n={}))Nt.call(n,t)&&ht(e,t,n[t]);if(vt)for(var t of vt(n))Yt.call(n,t)&&ht(e,t,n[t]);return e},Ee=(e,n)=>Pt(e,Kt(n));var Fe=(e,n,t)=>new Promise((s,o)=>{var f=m=>{try{E(t.next(m))}catch(h){o(h)}},l=m=>{try{E(t.throw(m))}catch(h){o(h)}},E=m=>m.done?s(m.value):Promise.resolve(m.value).then(f,l);E((t=t.apply(e,n)).next())});import{u as ce,r as le,t as re,y as Xe,z as Gt,A as Xt,B as Wt,D as ke,x as zt,G as jt,H as we,d as At,I as Ue,k as qt,C as Jt,J as $,K as Ie,a as Vt,b as kt,i as Zt,O as Qt,g as en,e as tn,f as nn,h as on,F as sn,N as an,M as _t,o as rn,j as un}from"./useSync.hook-14394fcc.js";import{x as ut,a6 as bt,aJ as ln,ah as F,ci as q,cj as J,ck as gt,cg as cn,cl as et,U as dn,cm as V,c6 as We,ai as pn,d as ie,bI as be,u as Le,a5 as P,o as S,h as z,j as lt,F as ge,A as me,ad as ve,aR as se,f as c,a9 as pe,z as U,p as de,c as H,w,L as Se,bV as tt,bW as He,bX as nt,bY as Lt,a4 as ct,y as Ke,aj as mt,R as he,r as B,cn as fn,a3 as Ne,q as Z,co as Ct,H as it,I as dt,e as D,m as _e,t as ue,aI as vn,aU as hn,cp as Ce,cq as Pe,cr as _n,ch as gn,cs as mn,ak as Cn,ct as yn,J as Rt,Q as yt,c0 as ze,X as je,Y as qe,aY as En,V as Sn,cu as wn,cv as Tn,W as xn,aP as Fn,S as Be,c1 as Je}from"./index.js";import{u as pt,d as Dn}from"./index-26dff32f.js";import{a as An,b as Et,l as kn,g as bn}from"./plugin-37914809.js";import{C as Ln}from"./index-cc1ab172.js";import{i as Te}from"./icon-bb3d09e7.js";import"./tables_list-f613fa36.js";import"./index-ba3ca6b4.js";import"./index-053cb545.js";const De=ce(),Rn=()=>{ut(()=>{De.setEditCanvas(le.EDIT_LAYOUT_DOM,document.getElementById("go-chart-edit-layout")),De.setEditCanvas(le.EDIT_CONTENT_DOM,document.getElementById("go-chart-edit-content"));const e=De.listenerScale();bt(()=>{De.setEditCanvas(le.EDIT_LAYOUT_DOM,null),De.setEditCanvas(le.EDIT_CONTENT_DOM,null),e()})})};var Ot={exports:{}};(function(e){(function(n){var t,s={},o={16:!1,18:!1,17:!1,91:!1},f="all",l={"\u21E7":16,shift:16,"\u2325":18,alt:18,option:18,"\u2303":17,ctrl:17,control:17,"\u2318":91,command:91},E={backspace:8,tab:9,clear:12,enter:13,return:13,esc:27,escape:27,space:32,left:37,up:38,right:39,down:40,del:46,delete:46,home:36,end:35,pageup:33,pagedown:34,",":188,".":190,"/":191,"`":192,"-":189,"=":187,";":186,"'":222,"[":219,"]":221,"\\":220},m=function(u){return E[u]||u.toUpperCase().charCodeAt(0)},h=[];for(t=1;t<20;t++)E["f"+t]=111+t;function g(u,p){for(var A=u.length;A--;)if(u[A]===p)return A;return-1}function d(u,p){if(u.length!=p.length)return!1;for(var A=0;A<u.length;A++)if(u[A]!==p[A])return!1;return!0}var r={16:"shiftKey",18:"altKey",17:"ctrlKey",91:"metaKey"};function a(u){for(t in o)o[t]=u[r[t]]}function _(u){var p,A,k,W,Y,fe;if(p=u.keyCode,g(h,p)==-1&&h.push(p),(p==93||p==224)&&(p=91),p in o){o[p]=!0;for(k in l)l[k]==p&&(C[k]=!0);return}if(a(u),!!C.filter.call(this,u)&&p in s){for(fe=j(),W=0;W<s[p].length;W++)if(A=s[p][W],A.scope==fe||A.scope=="all"){Y=A.mods.length>0;for(k in o)(!o[k]&&g(A.mods,+k)>-1||o[k]&&g(A.mods,+k)==-1)&&(Y=!1);(A.mods.length==0&&!o[16]&&!o[18]&&!o[17]&&!o[91]||Y)&&A.method(u,A)===!1&&(u.preventDefault?u.preventDefault():u.returnValue=!1,u.stopPropagation&&u.stopPropagation(),u.cancelBubble&&(u.cancelBubble=!0))}}}function v(u){var p=u.keyCode,A,k=g(h,p);if(k>=0&&h.splice(k,1),(p==93||p==224)&&(p=91),p in o){o[p]=!1;for(A in l)l[A]==p&&(C[A]=!1)}}function y(){for(t in o)o[t]=!1;for(t in l)C[t]=!1}function C(u,p,A){var k,W;k=i(u),A===void 0&&(A=p,p="all");for(var Y=0;Y<k.length;Y++)W=[],u=k[Y].split("+"),u.length>1&&(W=L(u),u=[u[u.length-1]]),u=u[0],u=m(u),u in s||(s[u]=[]),s[u].push({shortcut:k[Y],scope:p,method:A,key:k[Y],mods:W})}function M(u,p){var A,k,W=[],Y,fe,xe;for(A=i(u),fe=0;fe<A.length;fe++){if(k=A[fe].split("+"),k.length>1&&(W=L(k),u=k[k.length-1]),u=m(u),p===void 0&&(p=j()),!s[u])return;for(Y=0;Y<s[u].length;Y++)xe=s[u][Y],xe.scope===p&&d(xe.mods,W)&&(s[u][Y]={})}}function R(u){return typeof u=="string"&&(u=m(u)),g(h,u)!=-1}function b(){return h.slice(0)}function K(u){var p=(u.target||u.srcElement).tagName;return!(p=="INPUT"||p=="SELECT"||p=="TEXTAREA")}for(t in l)C[t]=!1;function X(u){f=u||"all"}function j(){return f||"all"}function I(u){var p,A,k;for(p in s)for(A=s[p],k=0;k<A.length;)A[k].scope===u?A.splice(k,1):k++}function i(u){var p;return u=u.replace(/\s/g,""),p=u.split(","),p[p.length-1]==""&&(p[p.length-2]+=","),p}function L(u){for(var p=u.slice(0,u.length-1),A=0;A<p.length;A++)p[A]=l[p[A]];return p}function N(u,p,A){u.addEventListener?u.addEventListener(p,A,!1):u.attachEvent&&u.attachEvent("on"+p,function(){A(window.event)})}N(document,"keydown",function(u){_(u)}),N(document,"keyup",v),N(window,"focus",y);var x=n.key;function O(){var u=n.key;return n.key=x,u}n.key=C,n.key.setScope=X,n.key.getScope=j,n.key.deleteScope=I,n.key.filter=K,n.key.isPressed=R,n.key.getPressedKeyCodes=b,n.key.noConflict=O,n.key.unbind=M,e.exports=C})(ln)})(Ot);var te=Ot.exports;const ne=ce(),ae=e=>`${q.CTRL}+${e}`,Me=e=>`${q.SHIFT}+${e}`,Q={[F.ARROW_UP]:ae("up"),[F.ARROW_RIGHT]:ae("right"),[F.ARROW_DOWN]:ae("down"),[F.ARROW_LEFT]:ae("left"),[F.COPY]:ae("c"),[F.CUT]:ae("x"),[F.PARSE]:ae("v"),[F.DELETE]:"delete",[F.BACK]:ae("z"),[F.FORWORD]:ae(Me("z")),[F.GROUP]:ae("g"),[F.UN_GROUP]:ae(Me("g")),[F.LOCK]:ae("l"),[F.UNLOCK]:ae(Me("l")),[F.HIDE]:ae("h"),[F.SHOW]:ae(Me("h"))},oe=e=>`${J.CTRL}+${e}`,$e=e=>`${J.SHIFT}+${e}`,ee={[F.ARROW_UP]:oe("arrowup"),[F.ARROW_RIGHT]:oe("arrowright"),[F.ARROW_DOWN]:oe("arrowdown"),[F.ARROW_LEFT]:oe("arrowleft"),[F.COPY]:oe("c"),[F.CUT]:oe("x"),[F.PARSE]:oe("v"),[F.DELETE]:oe("backspace"),[F.BACK]:oe("z"),[F.FORWORD]:oe($e("z")),[F.GROUP]:oe("g"),[F.UN_GROUP]:oe($e("g")),[F.LOCK]:oe("l"),[F.UNLOCK]:oe($e("l")),[F.HIDE]:oe("h"),[F.SHOW]:oe($e("h"))},On=[Q.up,Q.right,Q.down,Q.left,Q.delete,Q.copy,Q.cut,Q.parse,Q.back,Q.forward,Q.group,Q.unGroup,Q.lock,Q.unLock,Q.hide,Q.show],Bn=[ee.up,ee.right,ee.down,ee.left,ee.delete,ee.copy,ee.cut,ee.parse,ee.back,ee.forward,ee.group,ee.unGroup,ee.lock,ee.unLock,ee.hide,ee.show],Mn=()=>{window.$KeyboardActive={ctrl:!1,space:!1},document.onkeydown=e=>{const{keyCode:n}=e;if(n==32&&e.target==document.body&&e.preventDefault(),[17,32].includes(n)&&window.$KeyboardActive)switch(gt(e.keyCode),n){case 17:window.$KeyboardActive.ctrl=!0;break;case 32:window.$KeyboardActive.space=!0;break}},document.onkeyup=e=>{const{keyCode:n}=e;if(n==32&&e.target==document.body&&e.preventDefault(),[17,32].includes(n)&&window.$KeyboardActive)switch(gt(),n){case 17:window.$KeyboardActive.ctrl=!1;break;case 32:window.$KeyboardActive.space=!1;break}}},$n=()=>{const n=(t,s)=>{switch(s){case t.up:te(s,re(()=>(ne.setMove(F.ARROW_UP),!1),50));break;case t.right:te(s,re(()=>(ne.setMove(F.ARROW_RIGHT),!1),50));break;case t.down:te(s,re(()=>(ne.setMove(F.ARROW_DOWN),!1),50));break;case t.left:te(s,re(()=>(ne.setMove(F.ARROW_LEFT),!1),50));break;case t.delete:te(s,Xe(()=>(ne.removeComponentList(),!1),50));break;case t.copy:te(s,Xe(()=>(ne.setCopy(),!1),50));break;case t.cut:te(s,Xe(()=>(ne.setCut(),!1),50));break;case t.parse:te(s,re(()=>(ne.setParse(),!1),50));break;case t.back:te(s,re(()=>(ne.setBack(),!1),50));break;case t.forward:te(s,re(()=>(ne.setForward(),!1),50));break;case t.group:te(s,re(()=>(ne.setGroup(),!1),50));break;case t.unGroup:te(s,re(()=>(ne.setUnGroup(),!1),50));break;case t.lock:te(s,re(()=>(ne.setLock(),!1),50));break;case t.unLock:te(s,re(()=>(ne.setUnLock(),!1),50));break;case t.hide:te(s,re(()=>(ne.setHide(),!1),50));break;case t.show:te(s,re(()=>(ne.setShow(),!1),50));break}};On.forEach(t=>{n(Q,t)}),Bn.forEach(t=>{n(ee,t)}),Mn()},T=ce(),{onClickOutSide:In}=pt(),Un=e=>Fe(void 0,null,function*(){e.preventDefault();try{An();const n=e.dataTransfer.getData(cn.DRAG_KEY);if(!n){Et();return}T.setEditCanvas(le.IS_CREATE,!1);const t=et(n);let s,o;if(t.sourceID){let f=yield Gt(t.sourceID,t.category);if(!f){window.$message.warning("\u56FE\u8868\u6B63\u5728\u7814\u53D1\u4E2D, \u656C\u8BF7\u671F\u5F85...");return}s=f.chartConfig,Xt(f,"design");const l=Wt(f.chartConfig);o=new l.default(f.chartConfig),o=ke.exports.merge({},o,f),o.id=dn()}else o=yield zt(t);V(o,e.offsetX-o.attr.w/2,e.offsetY-o.attr.h/2),T.addComponentList(o,!1,!0),T.setTargetSelectChart(o.id),Et()}catch(n){kn(),window.$message.warning("\u56FE\u8868\u6B63\u5728\u7814\u53D1\u4E2D, \u656C\u8BF7\u671F\u5F85...")}}),St=e=>{e.preventDefault(),e.stopPropagation(),e.dataTransfer&&(e.dataTransfer.dropEffect="copy")},Bt=(e,n)=>{if(n){T.setTargetSelectChart(n.id);return}T.setTargetSelectChart(void 0)},Hn=(e,n)=>{var h;if(e.which==2||(h=window.$KeyboardActive)!=null&&h.space)return;Bt();const t=e.offsetX,s=e.offsetY,o=e.screenX,f=e.screenY,l=T.getEditCanvas.scale;T.setMousePosition(void 0,void 0,t,s);const E=ke.exports.throttle(g=>{T.setTargetSelectChart(),T.setEditCanvas(le.IS_SELECT,!0);const d=t+g.screenX-o,r=s+g.screenY-f;T.setMousePosition(d,r);const a={x1:0,y1:0,x2:0,y2:0};d>t&&r>s?(a.x1=t,a.y1=s,a.x2=Math.round(t+(g.screenX-o)/l),a.y2=Math.round(s+(g.screenY-f)/l)):d>t&&r<s?(a.x1=t,a.y1=Math.round(s-(f-g.screenY)/l),a.x2=Math.round(t+(g.screenX-o)/l),a.y2=s):d<t&&r>s?(a.x1=Math.round(t-(o-g.screenX)/l),a.y1=s,a.x2=t,a.y2=Math.round(s+(g.screenY-f)/l)):(a.x1=Math.round(t-(o-g.screenX)/l),a.y1=Math.round(s-(f-g.screenY)/l),a.x2=t,a.y2=s),T.getComponentList.forEach(_=>{if(!T.getTargetChart.selectId.includes(_.id)){const{x:v,y,w:C,h:M}=_.attr,R={x1:v,y1:y,x2:v+C,y2:y+M};R.x1-a.x1>=0&&R.y1-a.y1>=0&&R.x2-a.x2<=0&&R.y2-a.y2<=0&&!_.status.lock&&!_.status.hide&&T.setTargetSelectChart(_.id,!0)}})},30),m=()=>{E.cancel(),T.setEditCanvas(le.IS_SELECT,!1),T.setMousePosition(0,0,0,0),document.removeEventListener("mousemove",E),document.removeEventListener("mouseup",m)};document.addEventListener("mousemove",E),document.addEventListener("mouseup",m)},Mt=()=>({mouseClickHandle:(o,f)=>{var l;if(o.preventDefault(),o.stopPropagation(),!f.status.lock&&(l=window.$KeyboardActive)!=null&&l.ctrl)if(T.targetChart.selectId.includes(f.id)){const E=T.targetChart.selectId.filter(m=>m!==f.id);T.setTargetSelectChart(E)}else T.setTargetSelectChart(f.id,!0)},mousedownHandle:(o,f)=>{var y;if(o.preventDefault(),o.stopPropagation(),f.status.lock||(In(),o.buttons===We.LEFT&&((y=window.$KeyboardActive)==null?void 0:y.ctrl)))return;const l=T.getTargetChart.selectId;if(o.buttons===We.RIGHT&&l.length>1&&l.includes(f.id)||(T.setTargetSelectChart(f.id),o.buttons===We.RIGHT))return;const E=T.getEditCanvas.scale,m=T.getEditCanvasConfig.width,h=T.getEditCanvasConfig.height,g=new Map;T.getTargetChart.selectId.forEach(C=>{const M=T.fetchTargetIndex(C);if(M!==-1){const{x:R,y:b,w:K,h:X}=pn(T.getComponentList[M]).attr;g.set(C,{x:R,y:b,w:K,h:X})}});const d=o.screenX,r=o.screenY;let a=[];T.getTargetChart.selectId.forEach(C=>{if(!g.has(C))return;const M=T.fetchTargetIndex(C);a.push(ke.exports.cloneDeep(T.getComponentList[M]))}),T.setMousePosition(void 0,void 0,d,r);const _=ke.exports.throttle(C=>{T.setEditCanvas(le.IS_DRAG,!0),T.setMousePosition(C.screenX,C.screenY);let M=(C.screenX-d)/E,R=(C.screenY-r)/E;T.getTargetChart.selectId.forEach(b=>{if(!g.has(b))return;const K=T.fetchTargetIndex(b),{x:X,y:j,w:I,h:i}=g.get(b),L=T.getComponentList[K];let N=Math.round(X+M),x=Math.round(j+R);const O=50;N=N<-I+O?-I+O:N,x=x<-i+O?-i+O:x,N=N>m-O?m-O:N,x=x>h-O?h-O:x,L&&!Number.isNaN(N)&&!Number.isNaN(x)&&(L.attr=Object.assign(L.attr,{x:N,y:x}))})},20),v=()=>{try{T.setMousePosition(0,0,0,0),T.setEditCanvas(le.IS_DRAG,!1),a.length&&(T.getTargetChart.selectId.forEach(C=>{if(!g.has(C))return;const M=T.fetchTargetIndex(C),R=T.getComponentList[M];a.forEach(b=>{b.id===C&&(b.attr=Object.assign(b.attr,{offsetX:R.attr.x-b.attr.x,offsetY:R.attr.y-b.attr.y}))})}),T.moveComponentList(a)),document.removeEventListener("mousemove",_),document.removeEventListener("mouseup",v)}catch(C){console.log(C)}};document.addEventListener("mousemove",_),document.addEventListener("mouseup",v)},mouseenterHandle:(o,f)=>{o.preventDefault(),o.stopPropagation(),T.getEditCanvas.isSelect||T.setTargetHoverChart(f.id)},mouseleaveHandle:(o,f)=>{o.preventDefault(),o.stopPropagation(),T.setEditCanvas(le.IS_DRAG,!1),T.setTargetHoverChart(void 0)}}),Pn=(e,n,t)=>{e.stopPropagation(),e.preventDefault(),T.setEditCanvas(le.IS_DRAG,!0);const s=T.getEditCanvas.scale,o=t.x,f=t.y,l=t.w,E=t.h,m=t.lockScale,h=t.scaleRatio,g=t.minWidth,d=e.screenX,r=e.screenY,a=l-g;T.setMousePosition(d,r);const _=ke.exports.throttle(y=>{T.setMousePosition(y.screenX,y.screenY);let C=Math.round((y.screenX-d)/s),M=Math.round((y.screenY-r)/s);const R=/t/.test(n),b=/b/.test(n),K=/l/.test(n),X=/r/.test(n);let j=E+(R?-M:b?M:0),I=l+(K?-C:X?C:0);m&&((R||b)&&(I=Math.round(j*h)),(K||X)&&(j=Math.round(I/h))),K&&(I<g&&a>0&&(C=a),a===0&&C>0&&(C=0)),t.h=j>0?j:0,t.w=I>0?I:0,t.x=o+(K?C:0),t.y=f+(R?M:0)},50),v=()=>{T.setEditCanvas(le.IS_DRAG,!1),T.setMousePosition(0,0,0,0),document.removeEventListener("mousemove",_),document.removeEventListener("mouseup",v)};document.addEventListener("mousemove",_),document.addEventListener("mouseup",v)};const Kn=["onMousedown"],Nn=ie({__name:"index",props:{item:{type:Object,required:!0},hiddenPoint:{type:Boolean,required:!1}},setup(e){be(d=>({"026d39a0":l.value}));const n=e,t=Le(),s=ce(),o=["t","r","b","l","lt","rt","lb","rb"],f=["n","e","s","w","nw","ne","sw","se"],l=P(()=>t.getAppTheme),E=P(()=>n.item.status.lock?!1:n.item.id===s.getTargetChart.hoverId),m=P(()=>{const d=n.item.id;return n.item.status.lock?!1:s.getTargetChart.selectId.find(r=>r===d)}),h=P(()=>n.item.status.lock),g=P(()=>n.item.status.hide);return(d,r)=>(S(),z("div",{class:ve(["go-shape-box",{lock:h.value,hide:g.value}])},[lt(d.$slots,"default",{},void 0,!0),e.hiddenPoint?pe("",!0):(S(!0),z(ge,{key:0},me(m.value?o:[],(a,_)=>(S(),z("div",{class:ve(`shape-point ${a}`),key:_,style:se(c(jt)(a,_,e.item.attr,f)),onMousedown:v=>c(Pn)(v,a,e.item.attr)},null,46,Kn))),128)),U("div",{class:"shape-modal",style:se(c(we)(e.item.attr))},[U("div",{class:ve(["shape-modal-select",{active:m.value}])},null,2),U("div",{class:ve(["shape-modal-change",{selectActive:m.value,hoverActive:E.value}])},null,2)],4)],2))}});var ot=de(Nn,[["__scopeId","data-v-5f8854aa"]]);const Yn={class:"go-edit-group-box"},Gn=ie({__name:"index",props:{groupData:{type:Object,required:!0},groupIndex:{type:Number,required:!0}},setup(e){const n=ce(),{handleContextMenu:t}=pt(),{mouseenterHandle:s,mouseleaveHandle:o,mousedownHandle:f,mouseClickHandle:l}=Mt(),E=(g,d,r)=>{const a=_=>d.filter(v=>_.includes(v.key));if(n.getTargetChart.selectId.length>1)return a([F.GROUP,F.DELETE]);{const _=[];r.status.lock?_.push(F.LOCK):_.push(F.UNLOCK),r.status.hide?_.push(F.HIDE):_.push(F.SHOW);const v=[F.UN_GROUP];return[...a(v),Dn(),...g.filter(y=>!_.includes(y.key))]}},m=P(()=>{const g=n.getEditCanvasConfig.chartThemeColor;return At[g]}),h=P(()=>n.getEditCanvasConfig.chartThemeSetting);return(g,d)=>(S(),z("div",Yn,[(S(),H(c(ot),{key:e.groupData.id,"data-id":e.groupData.id,index:e.groupIndex,item:e.groupData,hiddenPoint:!0,class:ve(c(tt)(e.groupData.styles.animations)),style:se(G(G(G(G(G({},c(Ue)(e.groupData.attr,e.groupIndex)),c(we)(e.groupData.attr)),c(He)(e.groupData.styles)),c(nt)(e.groupData.styles)),c(Lt)(e.groupData.styles))),onClick:d[0]||(d[0]=r=>c(l)(r,e.groupData)),onMousedown:d[1]||(d[1]=r=>c(f)(r,e.groupData)),onMouseenter:d[2]||(d[2]=r=>c(s)(r,e.groupData)),onMouseleave:d[3]||(d[3]=r=>c(o)(r,e.groupData)),onContextmenu:d[4]||(d[4]=r=>c(t)(r,e.groupData,E))},{default:w(()=>[(S(!0),z(ge,null,me(e.groupData.groupList,r=>(S(),H(c(ot),{key:r.id,"data-id":r.id,index:e.groupIndex,item:r,hiddenPoint:!0,style:se(G({},c(Ue)(r.attr,e.groupIndex)))},{default:w(()=>[(S(),H(Se(r.chartConfig.chartKey),{class:ve(["edit-content-chart",c(tt)(r.styles.animations)]),chartConfig:r,themeSetting:h.value,themeColor:m.value,style:se(G(G(G({},c(we)(r.attr)),c(He)(r.styles)),c(nt)(r.styles)))},null,8,["class","chartConfig","themeSetting","themeColor","style"]))]),_:2},1032,["data-id","index","item","style"]))),128))]),_:1},8,["data-id","index","item","class","style"]))]))}});const Xn={class:"go-edit-align-line"},Wn=ie({__name:"index",setup(e){be(_=>({"32d4d33a":l.value}));const n=Le(),t=ce(),s=ct(),o=Ke({lineArr:["rowt","rowc","rowb","coll","colc","colr"],select:new Map,sorptioned:{x:!1,y:!1}}),f=_=>_?{left:`${_.x?_.x:0}px`,top:`${_.y?_.y:0}px`}:{},l=P(()=>n.getAppTheme),E=P(()=>s.getChartAlignRange),m=P(()=>t.getEditCanvas[le.IS_DRAG]),h=(_,v)=>Math.abs(_-v)<=E.value,g=P(()=>t.getTargetChart.selectId),d=P(()=>t.getComponentList[t.fetchTargetIndex()]),r=P(()=>{var _;return((_=d.value)==null?void 0:_.attr)||{}}),a=P(()=>({id:"0",attr:{w:mt(t.getEditCanvasConfig.width),h:mt(t.getEditCanvasConfig.height),x:0,y:0,offsetX:0,offsetY:0,zIndex:0,scaleRatio:1.5,lockScale:!1,disabledHeight:!1,minWidth:50}}));return he(()=>t.getMousePosition,re(()=>{try{if(!m.value||g.value.length!==1)return;const _=r.value.w,v=r.value.h,y=r.value.x,C=y+_/2,M=y+_,R=[y,C,M],b=r.value.y,K=b+v/2,X=b+v,j=[b,K,X];o.select.clear(),o.sorptioned.y=!1;const I=t.getComponentList.map(i=>({id:i.id,attr:i.attr}));I.push(a.value),o.lineArr.forEach(i=>{I.forEach(L=>{if(g.value[0]===L.id)return;const N=L.attr.w,x=L.attr.h,O=L.attr.x,u=O+N/2,p=O+N,A=[O,u,p],k=L.attr.y,W=k+x/2,Y=k+x,fe=[k,W,Y];i.includes("rowt")&&(h(b,k)&&(o.select.set(i,{y:k}),V(d.value,y,k)),h(b,W)&&(o.select.set(i,{y:W}),V(d.value,y,W)),h(b,Y)&&(o.select.set(i,{y:Y}),V(d.value,y,Y))),i.includes("rowc")&&(h(K,k)&&(o.select.set(i,{y:k}),V(d.value,y,k-v/2)),h(K,W)&&(o.select.set(i,{y:W}),V(d.value,y,W-v/2)),h(K,Y)&&(o.select.set(i,{y:Y}),V(d.value,y,Y-v/2))),i.includes("rowb")&&(h(X,k)&&(o.select.set(i,{y:k}),V(d.value,y,k-v)),h(X,W)&&(o.select.set(i,{y:W}),V(d.value,y,W-v)),h(X,Y)&&(o.select.set(i,{y:Y}),V(d.value,y,Y-v))),i.includes("coll")&&(h(y,O)&&(o.select.set(i,{x:O}),V(d.value,O,b)),h(y,u)&&(o.select.set(i,{x:u}),V(d.value,u,b)),h(y,p)&&(o.select.set(i,{x:p}),V(d.value,p,b))),i.includes("colc")&&(h(C,O)&&(o.select.set(i,{x:O}),V(d.value,O-_/2,b)),h(C,u)&&(o.select.set(i,{x:u}),V(d.value,u-_/2,b)),h(C,p)&&(o.select.set(i,{x:p}),V(d.value,p-_/2,b))),i.includes("colr")&&(h(M,O)&&(o.select.set(i,{x:O}),V(d.value,O-_,b)),h(M,u)&&(o.select.set(i,{x:u}),V(d.value,u-_,b)),h(M,p)&&(o.select.set(i,{x:p}),V(d.value,p-_,b)))})})}catch(_){console.log(_)}},200),{deep:!0}),he(()=>m.value,_=>{_||(o.select.clear(),o.sorptioned.y=!1)}),(_,v)=>(S(),z("div",Xn,[(S(!0),z(ge,null,me(o.lineArr,y=>(S(),z("div",{class:ve(["line",[y.includes("row")?"row":"col",o.select.has(y)&&"visible"]]),key:y,style:se(f(o.select.get(y)))},null,6))),128))]))}});var zn=de(Wn,[["__scopeId","data-v-41123ead"]]);const jn=ie({__name:"index",setup(e){return(n,t)=>{const s=B("n-watermark");return S(),H(s,{id:"go-edit-watermark",content:c(fn),cross:"",selectable:"","font-size":16,"line-height":16,width:500,height:150,"x-offset":12,"y-offset":80,rotate:-15},null,8,["content"])}}});var qn=de(jn,[["__scopeId","data-v-e5cd93da"]]);const $t=e=>(it("data-v-6aae2e54"),e=e(),dt(),e),Jn=$t(()=>U("div",{class:"select-background"},null,-1)),Vn=$t(()=>U("div",{class:"select-border"},null,-1)),Zn=[Jn,Vn],Qn=ie({__name:"index",setup(e){be(E=>({bb49f51e:f.value}));const n=Le(),t=ce(),{isSelect:s,scale:o}=Ne(t.getEditCanvas),f=P(()=>n.getAppTheme),l=Z();return he(()=>t.getMousePosition,E=>{if(!s.value)return;const{startX:m,startY:h,x:g,y:d}=E,r={zIndex:Ct,x:0,y:0,w:0,h:0,offsetX:0,offsetY:0,lockScale:!1,scaleRatio:1.5,disabledHeight:!1,minWidth:50};g>m&&d>h?(r.x=m,r.y=h,r.w=Math.round((g-m)/o.value),r.h=Math.round((d-h)/o.value)):g>m&&d<h?(r.x=m,r.w=Math.round((g-m)/o.value),r.h=Math.round((h-d)/o.value),r.y=h-r.h):g<m&&d>h?(r.y=h,r.w=Math.round((m-g)/o.value),r.h=Math.round((d-h)/o.value),r.x=m-r.w):(r.w=Math.round((m-g)/o.value),r.h=Math.round((h-d)/o.value),r.x=m-r.w,r.y=h-r.h),l.value=G(G({},Ue(r,Ct)),we(r))},{deep:!0}),(E,m)=>c(s)?(S(),z("div",{key:0,class:"go-edit-select",style:se(l.value)},Zn,4)):pe("",!0)}});var eo=de(Qn,[["__scopeId","data-v-6aae2e54"]]);const to=ie({__name:"index",setup(e){const n=ce(),{getEditCanvasConfig:t,getEditCanvas:s}=Ne(n),o=P(()=>({w:t.value.width,h:t.value.height})),f=P(()=>{const E={transform:`scale(${s.value.scale})`};return G(G({},we(o.value)),E)}),l=P(()=>{const E=s.value.isCreate&&{"z-index":99999};return G(G({},we(o.value)),E)});return(E,m)=>(S(),z("div",{class:"go-edit-range go-transition",style:se(f.value),onMousedown:m[0]||(m[0]=h=>c(Hn)(h,void 0))},[lt(E.$slots,"default",{},void 0,!0),D(c(qn)),D(c(zn)),D(c(eo)),U("div",{class:"go-edit-range-model",style:se(l.value)},null,4)],36))}});var no=de(to,[["__scopeId","data-v-52e445d0"]]),It=!!(typeof window!="undefined"&&window.document&&window.document.createElement),st=!1,at=!1;try{var Ve={get passive(){return st=!0},get once(){return at=st=!0}};It&&(window.addEventListener("test",Ve,Ve),window.removeEventListener("test",Ve,!0))}catch(e){}function oo(e,n,t,s){if(s&&typeof s!="boolean"&&!at){var o=s.once,f=s.capture,l=t;!at&&o&&(l=t.__once||function E(m){this.removeEventListener(n,E,f),t.call(this,m)},t.__once=l),e.addEventListener(n,l,st?s:f)}e.addEventListener(n,t,s)}function so(e,n,t,s){var o=s&&typeof s!="boolean"?s.capture:s;e.removeEventListener(n,t,o),t.__once&&e.removeEventListener(n,t.__once,o)}function wt(e,n,t,s){return oo(e,n,t,s),function(){so(e,n,t,s)}}var Tt=new Date().getTime();function ao(e){var n=new Date().getTime(),t=Math.max(0,16-(n-Tt)),s=setTimeout(e,t);return Tt=n,s}var ro=["","webkit","moz","o","ms"],xt=ao,Ft=function(n,t){return n+(n?t[0].toUpperCase()+t.substr(1):t)+"AnimationFrame"};It&&ro.some(function(e){var n=Ft(e,"request");return n in window&&(Ft(e,"cancel"),xt=function(s){return window[n](s)}),!!xt});Function.prototype.bind.call(Function.prototype.call,[].slice);Function.prototype.bind.call(Function.prototype.call,[].slice);const uo={class:"go-sketch-rule"},lo={key:1,class:"fix-edit-screens-block"},Ze=20,co=ie({__name:"index",setup(e){be(x=>({"0c493a2c":K.value,"57d4e729":d.value}));const n=ce(),t=qt(),s=Le();let o=[0,0],f=[0,0];const l=Z(),E=Z(!0),m=Z(),h=Z(),g=Z(!1),d=Z("auto"),{width:r,height:a}=Ne(n.getEditCanvasConfig),_=Z(0),v=Z(0),y=Ke({h:[],v:[]}),C=P(()=>n.getEditCanvas.scale),M=P(()=>r.value*2),R=P(()=>a.value*2),b=P(()=>s.getDarkTheme?{bgColor:"#18181c",longfgColor:"#4d4d4d",shortfgColor:"#4d4d4d",fontColor:"#4d4d4d",shadowColor:"#18181c",borderColor:"#18181c",cornerActiveColor:"#18181c"}:{}),K=P(()=>s.getAppTheme),X=x=>{if(x.ctrlKey||x.metaKey){x.preventDefault();let O=C.value;if(x.wheelDelta>=0&&C.value<2){O=C.value+.05,n.setScale(O);return}x.wheelDelta<0&&C.value>.1&&(O=C.value-.05,n.setScale(O))}},j=()=>{if(!l.value)return;const x=l.value.getBoundingClientRect(),O=m.value.getBoundingClientRect();_.value=(x.left+Ze-O.left)/C.value,v.value=(x.top+Ze-O.top)/C.value},I=x=>{var k,W;if(x.preventDefault(),x.stopPropagation(),x.which==2)g.value=!0;else if(!((k=window.$KeyboardActive)!=null&&k.space))return;(W=document.activeElement)==null||W.blur();const O=x.pageX,u=x.pageY,p=wt(window,"mousemove",Y=>{const fe=Y.pageX-O,xe=Y.pageY-u,[Ye,Re]=o,[Ge,Oe]=f;o=[Re,fe],f=[Oe,xe],l.value.scrollLeft-=Re>Ye?Math.abs(Re-Ye):-Math.abs(Re-Ye),l.value.scrollTop-=Oe>Ge?Math.abs(Oe-Ge):-Math.abs(Oe-Ge)}),A=wt(window,"mouseup",()=>{p(),A(),o=[0,0],f=[0,0],g.value=!1})},i=()=>{const x=document.getElementById("go-chart-edit-layout");return x?{height:x.clientHeight-25,width:x.clientWidth}:{width:r.value,height:a.value}},L=()=>{E.value=!1,setTimeout(()=>{E.value=!0},10)},N=()=>{const{width:x,height:O}=i();l.value.scrollLeft=M.value/2-x/2,l.value.scrollTop=R.value/2-O/2};return he(()=>M.value,()=>{N(),n.computedScale()}),he(()=>s.getDarkTheme,()=>{L()}),he(()=>C.value,(x,O)=>{O!==x?(t.setItemUnHandle(Jt.RE_POSITION_CANVAS,!1),j(),setTimeout(()=>{N(),L()},400)):re(L,20)}),he(()=>g.value,x=>{d.value=x?"grab":"auto"}),ut(()=>{l.value&&(l.value.addEventListener("wheel",X,{passive:!1}),N())}),bt(()=>{l.value&&l.value.removeEventListener("wheel",X)}),window.onKeySpacePressHold=x=>{g.value=x},(x,O)=>{const u=B("sketch-rule");return S(),z("div",uo,[E.value?(S(),H(u,{key:0,thick:Ze,scale:C.value,width:i().width,height:i().height,startX:_.value,startY:v.value,lines:y,palette:b.value},null,8,["scale","width","height","startX","startY","lines","palette"])):pe("",!0),U("div",{ref_key:"$app",ref:l,class:"edit-screens",onScroll:j},[U("div",{ref_key:"$container",ref:h,class:"edit-screen-container",style:se({width:M.value+"px",height:R.value+"px"})},[U("div",{ref_key:"refSketchRuleBox",ref:m,class:"canvas",onMousedown:I,style:se({marginLeft:"-"+(i().width/2-25)+"px"})},[U("div",{style:se({pointerEvents:g.value?"none":"auto"})},[lt(x.$slots,"default",{},void 0,!0)],4)],36)],4)],544),c(s).getDarkTheme?(S(),z("div",lo)):pe("",!0)])}}});var io=de(co,[["__scopeId","data-v-2f924d76"]]);const Qe={[$.ADD]:"\u65B0\u589E",[$.DELETE]:"\u5220\u9664",[$.UPDATE]:"\u66F4\u65B0",[$.MOVE]:"\u79FB\u52A8",[$.PASTE]:"\u7C98\u8D34",[$.COPY]:"\u590D\u5236",[$.CUT]:"\u526A\u5207",[$.TOP]:"\u7F6E\u9876",[$.BOTTOM]:"\u7F6E\u5E95",[$.UP]:"\u4E0A\u79FB",[$.DOWN]:"\u4E0B\u79FB",[$.GROUP]:"\u6210\u7EC4",[$.UN_GROUP]:"\u89E3\u7EC4",[$.LOCK]:"\u9501\u5B9A",[$.UNLOCK]:"\u89E3\u9501",[$.HIDE]:"\u9690\u85CF",[$.SHOW]:"\u663E\u793A",[Ie.CANVAS]:"\u753B\u5E03\u521D\u59CB\u5316"};var po=Array.prototype,fo=po.reverse;function vo(e){return e==null?e:fo.call(e)}var ho=vo;const Ut=e=>(it("data-v-5a4fd660"),e=e(),dt(),e),_o={class:"go-flex-items-center"},go=Ut(()=>U("span",{class:"btn-text"},"\u5386\u53F2\u8BB0\u5F55",-1)),mo={class:"history-list-box"},Co=["title"],yo=Ut(()=>U("div",{class:"popover-modal"},null,-1)),Eo=ie({__name:"index",setup(e){const{DesktopOutlineIcon:n,PencilIcon:t,TrashIcon:s,CopyIcon:o,LayersIcon:f,DuplicateIcon:l,HelpOutlineIcon:E,LockClosedOutlineIcon:m,LockOpenOutlineIcon:h,EyeOffOutlineIcon:g,EyeOutlineIcon:d}=Te.ionicons5,{StackedMoveIcon:r,Carbon3DCursorIcon:a,Carbon3DSoftwareIcon:_}=Te.carbon,v=Vt(),y=R=>{if(R.targetType===Ie.CANVAS)return n;switch(R.actionType){case $.UPDATE:return t;case $.DELETE:return s;case $.PASTE:return o;case $.TOP:return f;case $.BOTTOM:return f;case $.UP:return f;case $.DOWN:return f;case $.MOVE:return r;case $.ADD:return l;case $.GROUP:return a;case $.UN_GROUP:return _;case $.LOCK:return m;case $.UNLOCK:return h;case $.HIDE:return g;case $.SHOW:return d;default:return t}},C=R=>{if(R.targetType===Ie.CANVAS)return Qe[Ie.CANVAS];if(R.actionType===$.GROUP||R.actionType===$.UN_GROUP)return`${Qe[R.actionType]}`;if(R.historyData.length)return`${Qe[R.actionType]} - ${R.historyData[0].chartConfig.title}`},M=P(()=>{const b=v.getBackStack.map(K=>({label:C(K),icon:y(K)}));return ho(b.filter(K=>K.label))});return(R,b)=>{const K=B("n-button"),X=B("n-icon"),j=B("n-text"),I=B("n-scrollbar"),i=B("n-popover"),L=B("n-tooltip");return S(),z("div",_o,[D(i,{class:"edit-history-popover","show-arrow":!1,size:"small",trigger:"click",placement:"top-start"},{trigger:w(()=>[D(K,{class:"mr-10",secondary:"",size:"small",disabled:M.value.length===0},{default:w(()=>[go]),_:1},8,["disabled"])]),default:w(()=>[U("div",mo,[D(I,{style:{"max-height":"500px"}},{default:w(()=>[(S(!0),z(ge,null,me(M.value,(N,x)=>(S(),z("div",{class:"list-item go-flex-items-center go-ellipsis-1",key:x,title:N.label},[D(X,{class:"item-icon",size:"16",depth:2,component:N.icon},null,8,["component"]),D(j,{depth:"2"},{default:w(()=>[_e(ue(N.label),1)]),_:2},1024)],8,Co))),128))]),_:1}),yo])]),_:1}),D(L,{trigger:"hover"},{trigger:w(()=>[D(X,{size:"21",depth:3},{default:w(()=>[D(c(E))]),_:1})]),default:w(()=>[U("span",null,"\u6700\u591A\u53EA\u4FDD\u7559"+ue(c(vn))+"\u6761\u8BB0\u5F55",1)]),_:1})])}}});var So=de(Eo,[["__scopeId","data-v-5a4fd660"]]);const ft=e=>(it("data-v-3c7b6730"),e=e(),dt(),e),wo=ft(()=>U("th",null,"\u529F\u80FD",-1)),To=ft(()=>U("th",null,"Win \u5FEB\u6377\u952E",-1)),xo=ft(()=>U("span",null," Mac \u5FEB\u6377\u952E ",-1)),Fo={key:0},Do={key:1},Ao=ie({__name:"ShortcutKeyModal",props:{modelShow:Boolean},emits:["update:modelShow"],setup(e,{emit:n}){const{CloseIcon:t}=Te.ionicons5,s=Z(!1),o=n,f=e;he(()=>f.modelShow,m=>{s.value=m});const l=[{label:"\u62D6\u62FD\u753B\u5E03",win:`${q.SPACE.toUpperCase()} + \u{1F5B1}\uFE0F `,mac:`${J.SPACE.toUpperCase()} + \u{1F5B1}\uFE0F `,macSource:!0},{label:"\u5411 \u4E0A/\u53F3/\u4E0B/\u5DE6 \u79FB\u52A8",win:`${q.CTRL.toUpperCase()} + \u2191 \u6216 \u2192 \u6216 \u2193 \u6216 \u2190`,mac:`${J.CTRL.toUpperCase()} + \u2191 `},{label:"\u9501\u5B9A",win:`${q.CTRL.toUpperCase()} + L `,mac:`${J.CTRL.toUpperCase()} + L `},{label:"\u89E3\u9501",win:`${q.CTRL.toUpperCase()} + ${q.SHIFT.toUpperCase()}+ L `,mac:`${J.CTRL.toUpperCase()} + ${J.SHIFT.toUpperCase()} + L `},{label:"\u5C55\u793A",win:`${q.CTRL.toUpperCase()} + H `,mac:`${J.CTRL.toUpperCase()} + H `},{label:"\u9690\u85CF",win:`${q.CTRL.toUpperCase()} + ${q.SHIFT.toUpperCase()} + H `,mac:`${J.CTRL.toUpperCase()} + ${J.SHIFT.toUpperCase()} + H `},{label:"\u5220\u9664",win:"Delete".toUpperCase(),mac:`${J.CTRL.toUpperCase()} + Backspace `},{label:"\u590D\u5236",win:`${q.CTRL.toUpperCase()} + C `,mac:`${J.CTRL.toUpperCase()} + C `},{label:"\u526A\u5207",win:`${q.CTRL.toUpperCase()} + X `,mac:`${J.CTRL.toUpperCase()} + X `},{label:"\u7C98\u8D34",win:`${q.CTRL.toUpperCase()} + V `,mac:`${J.CTRL.toUpperCase()} + V `},{label:"\u540E\u9000",win:`${q.CTRL.toUpperCase()} + Z `,mac:`${J.CTRL.toUpperCase()} + Z `},{label:"\u524D\u8FDB",win:`${q.CTRL.toUpperCase()} + ${q.SHIFT.toUpperCase()} + Z `,mac:`${J.CTRL.toUpperCase()} + ${J.SHIFT.toUpperCase()} + Z `},{label:"\u591A\u9009",win:`${q.CTRL.toUpperCase()} + \u{1F5B1}\uFE0F `,mac:`${J.CTRL_SOURCE_KEY.toUpperCase()} + \u{1F5B1}\uFE0F `},{label:"\u521B\u5EFA\u5206\u7EC4",win:`${q.CTRL.toUpperCase()} + G / \u{1F5B1}\uFE0F `,mac:`${J.CTRL_SOURCE_KEY.toUpperCase()} + G / \u{1F5B1}\uFE0F`},{label:"\u89E3\u9664\u5206\u7EC4",win:`${q.CTRL.toUpperCase()} + ${q.SHIFT.toUpperCase()} + G `,mac:`${J.CTRL_SOURCE_KEY.toUpperCase()} + ${q.SHIFT.toUpperCase()} + G `}],E=()=>{o("update:modelShow",!1)};return(m,h)=>{const g=B("n-icon"),d=B("n-space"),r=B("n-gradient-text"),a=B("n-table"),_=B("n-modal");return S(),H(_,{show:s.value,"onUpdate:show":h[0]||(h[0]=v=>s.value=v),"mask-closable":!0,onAfterLeave:E},{default:w(()=>[D(a,{class:"model-content",bordered:!1,"single-line":!1},{default:w(()=>[U("thead",null,[U("tr",null,[wo,To,U("th",null,[D(d,{justify:"space-between"},{default:w(()=>[xo,D(g,{size:"20",class:"go-cursor-pointer",onClick:E},{default:w(()=>[D(c(t))]),_:1})]),_:1})])])]),U("tbody",null,[(S(),z(ge,null,me(l,(v,y)=>U("tr",{key:y},[U("td",null,ue(v.label),1),U("td",null,ue(v.win),1),v.macSource?(S(),z("td",Fo,ue(v.mac),1)):(S(),z("td",Do,[D(r,{size:22},{default:w(()=>[_e(ue(v.mac.substr(0,1)),1)]),_:2},1024),_e(" + "+ue(v.mac.substr(3)),1)]))])),64))])]),_:1})]),_:1},8,["show"])}}});var ko=de(Ao,[["__scopeId","data-v-3c7b6730"]]);const bo={class:"go-edit-shortcut"},Lo=ie({__name:"index",setup(e){const n=Z(!1);return(t,s)=>{const o=B("n-button");return S(),z("div",bo,[D(ko,{modelShow:n.value,"onUpdate:modelShow":s[0]||(s[0]=f=>n.value=f)},null,8,["modelShow"]),D(o,{class:"scale-btn",onClick:s[1]||(s[1]=f=>n.value=!0)},{default:w(()=>[_e(" \u5FEB\u6377\u952E")]),_:1})])}}});var Ro=de(Lo,[["__scopeId","data-v-2a5a05ce"]]);const Oo={class:"go-edit-bottom"},Bo=ie({__name:"index",setup(e){be(y=>({f3add9fc:o.value}));const{LockClosedOutlineIcon:n,LockOpenOutlineIcon:t}=Te.ionicons5,s=Le(),o=Z(s.getAppTheme),f=ce(),{lockScale:l,scale:E}=Ne(f.getEditCanvas);let m=[{label:"200%",value:200},{label:"150%",value:150},{label:"100%",value:100},{label:"50%",value:50},{label:"\u81EA\u9002\u5E94",value:0}];const h=Z(""),g=y=>{if(y===0){f.computedScale();return}f.setScale(y/100)},d=()=>{f.setEditCanvas(le.LOCK_SCALE,!l.value)},r=Z(100),a=y=>`${y}%`,_=y=>{f.setScale(y/100)},v=Ke({100:""});return hn(()=>{const y=(E.value*100).toFixed(0);h.value=`${y}%`,r.value=parseInt(y)}),(y,C)=>{const M=B("n-text"),R=B("n-space"),b=B("n-select"),K=B("n-icon"),X=B("n-button"),j=B("n-tooltip"),I=B("n-slider");return S(),z("div",Oo,[D(R,null,{default:w(()=>[D(c(So)),D(M,{id:"keyboard-dress-show",depth:"3"})]),_:1}),D(R,{class:"bottom-ri"},{default:w(()=>[D(Ro),D(b,{disabled:c(l),class:"scale-btn",value:h.value,"onUpdate:value":[C[0]||(C[0]=i=>h.value=i),g],size:"mini",options:c(m)},null,8,["disabled","value","options"]),D(j,{trigger:"hover"},{trigger:w(()=>[D(X,{onClick:d,text:""},{default:w(()=>[D(K,{class:ve(["lock-icon",{color:c(l)}]),size:"18",depth:2},{default:w(()=>[c(l)?(S(),H(c(n),{key:0})):(S(),H(c(t),{key:1}))]),_:1},8,["class"])]),_:1})]),default:w(()=>[U("span",null,ue(c(l)?"\u89E3\u9501":"\u9501\u5B9A")+"\u5F53\u524D\u6BD4\u4F8B",1)]),_:1}),D(I,{class:"scale-slider",value:r.value,"onUpdate:value":[C[1]||(C[1]=i=>r.value=i),_],"default-value":50,min:10,max:200,step:5,"format-tooltip":a,disabled:c(l),marks:v},null,8,["value","disabled","marks"])]),_:1})])}}});var Mo=de(Bo,[["__scopeId","data-v-99a534fc"]]);const $o=ie({__name:"index",props:{modelShow:Boolean},emits:["update:modelShow"],setup(e,{emit:n}){const t=e,s=n,{HelpOutlineIcon:o,CloseIcon:f}=Te.ionicons5,l=ct(),E=Z(!1),m=Ke([{key:Ce.ASIDE_ALL_COLLAPSED,value:l.getAsideAllCollapsed,type:"switch",name:"\u83DC\u5355\u6298\u53E0",desc:"\u9996\u9875\u83DC\u5355\u6298\u53E0\u65F6\u9690\u85CF\u81F3\u754C\u9762\u5916"},{key:Ce.HIDE_PACKAGE_ONE_CATEGORY,value:l.getHidePackageOneCategory,type:"switch",name:"\u9690\u85CF\u5206\u7C7B",desc:"\u5DE5\u4F5C\u7A7A\u95F4\u8868\u5355\u5206\u7C7B\u53EA\u6709\u5355\u9879\u65F6\u9690\u85CF"},{key:Ce.CHANGE_LANG_RELOAD,value:l.getChangeLangReload,type:"switch",name:"\u5207\u6362\u8BED\u8A00",desc:"\u5207\u6362\u8BED\u8A00\u91CD\u65B0\u52A0\u8F7D\u9875\u9762",tip:"\u82E5\u9047\u5230\u90E8\u5206\u533A\u57DF\u8BED\u8A00\u5207\u6362\u5931\u8D25\uFF0C\u5219\u5EFA\u8BAE\u5F00\u542F"},{key:"divider1",type:"divider",name:"",desc:"",value:""},{key:Ce.CHART_TOOLS_STATUS_HIDE,value:l.getChartToolsStatusHide,type:"switch",name:"\u9690\u85CF\u5DE5\u5177\u680F",desc:"\u9F20\u6807\u79FB\u5165\u65F6\uFF0C\u4F1A\u5C55\u793A\u5207\u6362\u5230\u5C55\u5F00\u6A21\u5F0F"},{key:Ce.CHART_TOOLS_STATUS,value:l.getChartToolsStatus,type:"select",name:"\u5DE5\u5177\u680F\u5C55\u793A",desc:"\u5DE5\u4F5C\u7A7A\u95F4\u5DE5\u5177\u680F\u5C55\u793A\u65B9\u5F0F",options:[{label:"\u4FA7\u8FB9\u680F",value:Pe.ASIDE},{label:"\u5E95\u90E8 Dock",value:Pe.DOCK}]},{key:"divider0",type:"divider",name:"",desc:"",value:""},{key:Ce.CHART_MOVE_DISTANCE,value:l.getChartMoveDistance,type:"number",name:"\u79FB\u52A8\u8DDD\u79BB",min:1,step:1,suffix:"px",desc:"\u5DE5\u4F5C\u7A7A\u95F4\u65B9\u5411\u952E\u63A7\u5236\u79FB\u52A8\u8DDD\u79BB"},{key:Ce.CHART_ALIGN_RANGE,value:l.getChartAlignRange,type:"number",name:"\u5438\u9644\u8DDD\u79BB",min:10,step:2,suffix:"px",desc:"\u5DE5\u4F5C\u7A7A\u95F4\u79FB\u52A8\u56FE\u8868\u65F6\u7684\u5438\u9644\u8DDD\u79BB"}]);he(()=>t.modelShow,d=>{E.value=d});const h=()=>{s("update:modelShow",!1)},g=(d,r)=>{l.setItem(r.key,r.value)};return(d,r)=>{const a=B("n-h3"),_=B("n-icon"),v=B("n-space"),y=B("n-divider"),C=B("n-text"),M=B("n-switch"),R=B("n-input-number"),b=B("n-select"),K=B("n-tooltip"),X=B("n-list-item"),j=B("n-list"),I=B("n-modal");return S(),H(I,{show:E.value,"onUpdate:show":r[0]||(r[0]=i=>E.value=i),onAfterLeave:h},{default:w(()=>[D(j,{bordered:"",class:"go-system-setting"},{header:w(()=>[D(v,{justify:"space-between"},{default:w(()=>[D(a,{class:"go-mb-0"},{default:w(()=>[_e("\u7CFB\u7EDF\u8BBE\u7F6E")]),_:1}),D(_,{size:"20",class:"go-cursor-pointer",onClick:h},{default:w(()=>[D(c(f))]),_:1})]),_:1})]),default:w(()=>[(S(!0),z(ge,null,me(m,i=>(S(),H(X,{key:i.key},{default:w(()=>[i.type==="divider"?(S(),H(y,{key:0,style:{margin:"0"}})):(S(),H(v,{key:1,size:40},{default:w(()=>[D(v,null,{default:w(()=>[D(C,{class:"item-left"},{default:w(()=>[_e(ue(i.name),1)]),_:2},1024),i.type==="switch"?(S(),H(M,{key:0,value:i.value,"onUpdate:value":[L=>i.value=L,L=>g(L,i)],size:"small"},null,8,["value","onUpdate:value"])):i.type==="number"?(S(),H(R,{key:1,value:i.value,"onUpdate:value":[L=>i.value=L,L=>g(L,i)],class:"input-num-width",size:"small",step:i.step||null,suffix:i.suffix||null,min:i.min||0},null,8,["value","onUpdate:value","step","suffix","min"])):i.type==="select"?(S(),H(b,{key:2,class:"select-min-width",value:i.value,"onUpdate:value":[L=>i.value=L,L=>g(L,i)],size:"small",options:i.options},null,8,["value","onUpdate:value","options"])):pe("",!0)]),_:2},1024),D(v,null,{default:w(()=>[D(C,{class:"item-right"},{default:w(()=>[_e(ue(i.desc),1)]),_:2},1024),i.tip?(S(),H(K,{key:0,trigger:"hover"},{trigger:w(()=>[D(_,{size:"21"},{default:w(()=>[D(c(o))]),_:1})]),default:w(()=>[U("span",null,ue(i.tip),1)]),_:2},1024)):pe("",!0)]),_:2},1024)]),_:2},1024))]),_:2},1024))),128))]),_:1})]),_:1},8,["show"])}}});var Io=de($o,[["__scopeId","data-v-7cea0aa5"]]);const Ae=ce(),Uo=()=>{Ae.setTargetSelectChart(void 0),_n(gn(Ae.getStorageInfo||[]),void 0,"json");const e=document.querySelector(".go-edit-range"),n=document.getElementById("go-edit-watermark");if(!e||!n){window.$message.error("\u5BFC\u51FA\u5931\u8D25\uFF01");return}const t=Ae.getEditCanvas.scale;Ae.setScale(1,!0),n.style.display="block",setTimeout(()=>{mn(e,()=>{n&&(n.style.display="none"),Ae.setScale(t,!0)})},600)};var rt=(e=>(e.TXT="text/plain",e.JSON="application/json",e.PNG="image/png",e.JPEG="image/jpeg",e.GIF="image/gif",e))(rt||{});const Ho=()=>{const e=Z(),{updateComponent:n}=kt();return{importUploadFileListRef:e,importBeforeUpload:({file:o})=>{e.value=[];const f=o.file.type;return f!==rt.JSON&&f!==rt.TXT?(window.$message.warning("\u4EC5\u652F\u6301\u4E0A\u4F20 \u3010JSON\u3011 \u683C\u5F0F\u6587\u4EF6\uFF0C\u8BF7\u91CD\u65B0\u4E0A\u4F20\uFF01"),!1):!0},importCustomRequest:o=>{const{file:f}=o;Cn(()=>{f.file?yn(f.file).then(l=>{bn({message:"\u8BF7\u9009\u62E9\u5BFC\u5165\u65B9\u5F0F:",positiveText:"\u65B0\u589E\uFF08\u53EF\u64A4\u56DE\uFF09",negativeText:"\u8986\u76D6\uFF08\u4E0D\u53EF\u64A4\u56DE\uFF09",negativeButtonProps:{type:"info",ghost:!1},onPositiveCallback:()=>Fe(void 0,null,function*(){try{l=et(l),yield n(l,!1,!0),window.$message.success("\u5BFC\u5165\u6210\u529F\uFF01")}catch(E){console.log(E),window.$message.error("\u7EC4\u4EF6\u5BFC\u5165\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u6587\u4EF6\u5B8C\u6574\u6027!")}}),onNegativeCallback:()=>Fe(void 0,null,function*(){try{l=et(l),yield n(l,!0,!0),window.$message.success("\u5BFC\u5165\u6210\u529F\uFF01")}catch(E){console.log(E),window.$message.error("\u7EC4\u4EF6\u5BFC\u5165\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u6587\u4EF6\u5B8C\u6574\u6027!")}})})}):window.$message.error("\u5BFC\u5165\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u6570\u636E\u6216\u8054\u7CFB\u7BA1\u7406\u5458\uFF01")})}}},{updateComponent:Po,dataSyncUpdate:Dt}=kt(),Ko=ce(),No=()=>{const e=f=>Po(f.detail,!0,!1),n=()=>Fe(void 0,null,function*(){Dt&&(yield Dt()),dispatchEvent(new CustomEvent(ze.CHART,{detail:Ko.getStorageInfo}))}),t=()=>{addEventListener("blur",n),addEventListener(ze.JSON,e)},s=()=>{removeEventListener(ze.JSON,e),removeEventListener("blur",n)};return(f,l)=>{l==yt.CHART_HOME_NAME&&s(),f==yt.CHART_HOME_NAME&&t()}},Yo=()=>{const e=Rt();he(()=>e.name,No(),{immediate:!0})};var ye=(e=>(e.BUTTON="button",e.IMPORTUPLOAD="importUpload",e))(ye||{});const Go={class:"btn-item"},Xo=ie({__name:"index",setup(e){const{DownloadIcon:n,ShareIcon:t,PawIcon:s,SettingsSharpIcon:o,CreateIcon:f}=Te.ionicons5,l=ct(),E=ce();Rt(),Yo();let m=null;const h=Z(!1),g=Z(!0),d=Z(!0),{importUploadFileListRef:r,importCustomRequest:a,importBeforeUpload:_}=Ho(),v=P(()=>l.getChartToolsStatus===Pe.ASIDE),y=P(()=>l.getChartToolsStatusHide),C=P(()=>g.value&&y.value),M=P(()=>{if(!v.value)return j;const I=[];return j.map(i=>{I.unshift(i)}),I}),R=()=>{m=setTimeout(()=>{g.value&&(g.value=!1,d.value=!0)},200),setTimeout(()=>{d.value=!1},400)},b=()=>{clearTimeout(m),g.value||(g.value=!0)},K=()=>{window.$message.warning("\u5C06\u5F00\u542F\u5931\u7126\u66F4\u65B0\uFF01"),setTimeout(()=>{const I=Sn(wn.CHART_EDIT_NAME,"href");if(!I)return;const i=Tn();X(i),xn(I,[i],void 0,!0)},1e3)},X=I=>{const i=E.getStorageInfo,L=Fn(Be.GO_CHART_STORAGE_LIST)||[];if(L!=null&&L.length){const N=L.findIndex(x=>x.id===I);N!==-1?(L.splice(N,1,Ee(G({},i),{id:I})),Je(Be.GO_CHART_STORAGE_LIST,L)):(L.push(Ee(G({},i),{id:I})),Je(Be.GO_CHART_STORAGE_LIST,L))}else Je(Be.GO_CHART_STORAGE_LIST,[Ee(G({},i),{id:I})])},j=[{key:"import",type:ye.IMPORTUPLOAD,name:"\u5BFC\u5165",icon:t},{key:"export",type:ye.BUTTON,name:"\u5BFC\u51FA",icon:n,handle:Uo},{key:"edit",type:ye.BUTTON,name:"\u7F16\u8F91",icon:f,handle:K},{key:"setting",type:ye.BUTTON,name:"\u8BBE\u7F6E",icon:o,handle:()=>{h.value=!0}}];return(I,i)=>{const L=B("n-icon"),N=B("n-text"),x=B("n-button"),O=B("n-upload"),u=B("n-tooltip");return S(),z(ge,null,[U("div",{class:ve(["go-chart-edit-tools",[c(l).getChartToolsStatus,C.value?"isMini":"unMini"]]),onClick:i[1]||(i[1]=p=>g.value&&(g.value=!1)),onMouseenter:R,onMouseleave:b},[je(D(L,{class:"asideLogo",size:"22"},{default:w(()=>[D(c(s))]),_:1},512),[[qe,c(l).getChartToolsStatus===c(Pe).ASIDE&&C.value]]),(S(!0),z(ge,null,me(M.value,(p,A)=>(S(),H(u,{key:p.key,disabled:!v.value||y.value&&d.value,trigger:"hover",placement:"left"},{trigger:w(()=>[U("div",Go,[p.type===c(ye).BUTTON?(S(),H(x,{key:0,circle:v.value,secondary:"",onClick:p.handle},{icon:w(()=>[v.value?(S(),H(L,{key:0,size:"22"},{default:w(()=>[(S(),H(Se(p.icon)))]),_:2},1024)):(S(),H(Se(p.icon),{key:1}))]),default:w(()=>[je(D(N,{depth:"3"},{default:w(()=>[_e(ue(p.name),1)]),_:2},1536),[[qe,!v.value]])]),_:2},1032,["circle","onClick"])):p.type===c(ye).IMPORTUPLOAD?(S(),H(O,{key:1,"file-list":c(r),"onUpdate:fileList":i[0]||(i[0]=k=>En(r)?r.value=k:null),"show-file-list":!1,customRequest:c(a),onBeforeUpload:c(_)},{default:w(()=>[D(x,{circle:v.value,secondary:""},{icon:w(()=>[v.value?(S(),H(L,{key:0,size:"22"},{default:w(()=>[(S(),H(Se(p.icon)))]),_:2},1024)):(S(),H(Se(p.icon),{key:1}))]),default:w(()=>[je(D(N,{depth:"3"},{default:w(()=>[_e(ue(p.name),1)]),_:2},1536),[[qe,!v.value]])]),_:2},1032,["circle"])]),_:2},1032,["file-list","customRequest","onBeforeUpload"])):pe("",!0)])]),default:w(()=>[U("span",null,ue(p.name),1)]),_:2},1032,["disabled"]))),128))],34),D(c(Io),{modelShow:h.value,"onUpdate:modelShow":i[2]||(i[2]=p=>h.value=p)},null,8,["modelShow"])],64)}}});var Wo=de(Xo,[["__scopeId","data-v-4c257f27"]]);const zo=ie({__name:"index",setup(e){const n=ce(),{handleContextMenu:t}=pt();Rn();const{mouseenterHandle:s,mouseleaveHandle:o,mousedownHandle:f,mouseClickHandle:l}=Mt(),E=(d,r,a)=>{if(n.getTargetChart.selectId.length>1)return r.filter(v=>[F.GROUP,F.DELETE].includes(v.key));const _=[];return a.status.lock?_.push(F.LOCK):_.push(F.UNLOCK),a.status.hide?_.push(F.HIDE):_.push(F.SHOW),d.filter(v=>!_.includes(v.key))},m=P(()=>n.getEditCanvasConfig.chartThemeSetting),h=P(()=>{const d=n.getEditCanvasConfig.chartThemeColor;return At[d]});P(()=>n.getEditCanvasConfig.filterShow);const g=P(()=>{const d=n.getEditCanvasConfig.background,r=n.getEditCanvasConfig.backgroundImage,v=n.getEditCanvasConfig.selectColor?{background:d||void 0}:{background:`url(${r}) no-repeat center center / cover !important`};return Ee(G({},v),{width:"inherit",height:"inherit"})});return ut(()=>{$n()}),(d,r)=>(S(),H(c(Ln),{id:"go-chart-edit-layout",flex:!0,showTop:!1,showBottom:!0,depth:1,xScroll:!0,disabledScroll:!0,onMousedown:c(Bt),onDrop:c(Un),onDragover:c(St),onDragenter:c(St)},{default:w(()=>[D(c(io),null,{default:w(()=>[U("div",{id:"go-chart-edit-content",onContextmenu:r[0]||(r[0]=(...a)=>c(t)&&c(t)(...a))},[D(c(no),null,{default:w(()=>[U("div",{style:se(G(G({},c(He)(c(n).getEditCanvasConfig)),g.value))},[(S(!0),z(ge,null,me(c(n).getComponentList,(a,_)=>(S(),z("div",{key:a.id},[a.isGroup?(S(),H(c(Gn),{key:0,groupData:a,groupIndex:_},null,8,["groupData","groupIndex"])):(S(),H(c(ot),{key:1,"data-id":a.id,index:_,style:se(G(G({},c(Ue)(a.attr,_)),c(Lt)(a.styles))),item:a,onClick:v=>c(l)(v,a),onMousedown:v=>c(f)(v,a),onMouseenter:v=>c(s)(v,a),onMouseleave:v=>c(o)(v,a),onContextmenu:v=>c(t)(v,a,E)},{default:w(()=>{var v,y,C,M;return[c(Zt)(a)&&!((v=a.option)!=null&&v.isDeleted)?(S(),H(c(Qt),{key:0,title:c(en)(a),attr:a.attr,titleSize:c(tn)(a),titleStyle:c(nn)(a)},null,8,["title","attr","titleSize","titleStyle"])):pe("",!0),c(on)(a)&&!((y=a.option)!=null&&y.isDeleted)?(S(),H(c(sn),{key:1,class:"filter-content",componentIdx:a.id,attr:a.attr,filters:a.chartConfig.filters,type:a.chartConfig.category},null,8,["componentIdx","attr","filters","type"])):pe("",!0),(C=a.option)!=null&&C.isDeleted?(S(),H(c(an),{key:2,item:a,style:se(Ee(G({},c(_t)(a.attr,0)),{lineHeight:a.attr.h+"px"}))},null,8,["item","style"])):pe("",!0),(M=a.option)!=null&&M.isDeleted?pe("",!0):(S(),H(Se(a.chartConfig.chartKey),{key:3,class:ve(["edit-content-chart",c(tt)(a.styles.animations)]),chartConfig:a,themeSetting:m.value,themeColor:h.value,style:se(G(G(G({},c(_t)(a.attr,c(rn)(a)+c(un)(a))),c(He)(a.styles)),c(nt)(a.styles)))},null,8,["class","chartConfig","themeSetting","themeColor","style"]))]}),_:2},1032,["data-id","index","style","item","onClick","onMousedown","onMouseenter","onMouseleave","onContextmenu"]))]))),128))],4)]),_:1})],32)]),_:1}),U("template",null,[D(c(Wo))]),U("template",null,[D(c(Mo))])]),_:1},8,["onMousedown","onDrop","onDragover","onDragenter"]))}});var ss=de(zo,[["__scopeId","data-v-1f1a2a96"]]);export{ss as default};
