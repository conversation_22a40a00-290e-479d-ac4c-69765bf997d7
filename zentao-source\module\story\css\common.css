.icon-exclamation-sign {color: #0075ff;}
.linkbox {height: 180px; overflow-y: auto;}
.tab-pane h6 {margin: 0px; padding: 10px;}
.tab-pane h6, .tab-pane table {border: 1px solid #ddd; border-top: none;}
.tab-pane .table-data.table-borderless {border: none !important;}

.input-group .control-product + .chosen-container-single .chosen-single {border-radius: 2px 0 0 2px; border-right-width: 1px; padding-right: 1px;}
.input-group .control-product + .chosen-container-active .chosen-single {border-right-width: 1px; padding-right: 0;}

#batchCreateForm .input-group, #batchEditForm .input-group,
#batchCreateForm .input-group .form-control, #batchEditForm .input-group .form-control {position: static;}
#batchCreateForm .input-group .colorpicker, #batchEditForm .input-group .colorpicker {z-index: 2;}
#batchCreateForm .input-group .colorpicker.open, #batchEditForm .input-group .colorpicker.open {z-index: 5;}
#branch_chosen .chosen-single span {word-break: break-all;}

.c-branch, .c-spec {width: 150px}
.c-source {width: 130px}
.c-note {width: 100px}
.c-category {width: 90px}

.icon-info {color: #ff9800; border-color: #ff9800;}
.text-active {text-decoration: underline;}
.text-cancel {text-decoration: underline;color:#838a9d;margin-left:10px;}
.popover {border:unset;}
.popover.bottom .arrow{border-bottom-color:#ffffff;}
.popover-icon {float:left;}
.popover .content {margin-left:25px;overflow: overlay;}
.twins > a.viewlink {padding:6px;white-space: nowrap;overflow: hidden;display: inline-flex;max-width: 70%;}
.twins > span {max-width: 60px;white-space: nowrap;overflow: hidden;}
.tooltip-inner {max-width: 500px;}
