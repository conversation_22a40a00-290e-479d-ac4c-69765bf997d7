#begin_chosen {width: 50% !important;}
.switchDate {border-left: none;}
#end_chosen .chosen-single {border-left: none;}
.cycleConfig .checkbox-primary {float: left; width: 100px;}
.cycleConfig .tab-pane {padding: 8px 0px;}
.cycleConfig .tab-pane .input-group {width: 380px;}
.nameBox {display: grid;}
.pri-selector > .btn {padding: 5px 12px;}
#assignedTo_chosen.chosen-container .chosen-results {max-height: 180px;}
.inputGroupWidth {padding-top: 5px;}
.nameBox + span {border-radius: 0px;}
