#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整功能测试脚本
"""

import requests
import os
import json

def test_all_functions():
    """测试所有功能"""
    base_url = "http://127.0.0.1:8001/api/v1/simple-supervision"
    
    print("🚀 开始完整功能测试...")
    
    # 1. 测试获取督办事项列表
    print("\n1️⃣ 测试获取督办事项列表...")
    try:
        response = requests.get(f"{base_url}/items")
        if response.status_code == 200:
            data = response.json()
            items = data.get('data', [])
            print(f"✅ 获取成功，共 {len(items)} 条督办事项")
            if items:
                print(f"   第一条: {items[0]['work_theme']}")
        else:
            print(f"❌ 获取失败: {response.text}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 2. 测试状态更新
    print("\n2️⃣ 测试状态更新...")
    try:
        response = requests.put(f"{base_url}/status", json={
            "work_theme": "核心系统升级改造",
            "company_name": "财险",
            "status": "√"
        })
        if response.status_code == 200:
            print("✅ 状态更新成功")
        else:
            print(f"❌ 状态更新失败: {response.text}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 3. 测试进度详情更新
    print("\n3️⃣ 测试进度详情更新...")
    try:
        response = requests.put(f"{base_url}/progress-detail", json={
            "work_theme": "核心系统升级改造",
            "company_name": "财险",
            "progress_description": "项目已完成90%，正在进行最后的测试验收工作。",
            "existing_problems": "部分功能模块需要进一步优化。",
            "next_plan": "计划在本月底完成全部测试并上线。"
        })
        if response.status_code == 200:
            print("✅ 进度详情更新成功")
        else:
            print(f"❌ 进度详情更新失败: {response.text}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 4. 测试获取进度详情
    print("\n4️⃣ 测试获取进度详情...")
    try:
        response = requests.get(f"{base_url}/progress-detail/核心系统升级改造/财险")
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取进度详情成功")
            print(f"   进度描述: {data['data']['progress_description'][:50]}...")
        else:
            print(f"❌ 获取进度详情失败: {response.text}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 5. 测试Excel导出
    print("\n5️⃣ 测试Excel导出...")
    try:
        response = requests.get(f"{base_url}/export", stream=True)
        if response.status_code == 200:
            filename = 'complete_test_export.xlsx'
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filename)
            print(f"✅ 导出成功！文件大小: {file_size} 字节")
        else:
            print(f"❌ 导出失败: {response.text}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 6. 测试Excel导入
    print("\n6️⃣ 测试Excel导入...")
    try:
        if os.path.exists('complete_test_export.xlsx'):
            with open('complete_test_export.xlsx', 'rb') as f:
                files = {'file': ('test_import.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
                response = requests.post(f"{base_url}/import", files=files)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 导入成功: {result.get('message', '无消息')}")
            else:
                print(f"❌ 导入失败: {response.text}")
        else:
            print("❌ 导出文件不存在，跳过导入测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 7. 最终验证数据
    print("\n7️⃣ 最终验证数据...")
    try:
        response = requests.get(f"{base_url}/items")
        if response.status_code == 200:
            data = response.json()
            items = data.get('data', [])
            print(f"✅ 最终验证成功，共 {len(items)} 条督办事项")
            
            # 检查状态更新是否生效
            for item in items:
                if item['work_theme'] == '核心系统升级改造':
                    print(f"   核心系统升级改造 - 财险状态: {item.get('财险_status', 'N/A')}")
                    break
        else:
            print(f"❌ 最终验证失败: {response.text}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n🎉 完整功能测试完成！")

if __name__ == "__main__":
    test_all_functions()
