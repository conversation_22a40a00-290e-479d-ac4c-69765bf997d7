/*!
 * Chart.js
 * http://chartjs.org/
 * Version: 1.0.2
 *
 * Copyright 2015 <PERSON>
 * Released under the MIT license
 * https://github.com/nnnick/Chart.js/blob/master/LICENSE.md
 */
window.Chart=function(t){function e(t,e,n){var a=e.steps*e.stepValue,i=t-e.graphMin,o=r(i/a,1,0);return n*e.steps*o}function n(t,e,n,a){function i(){var i=t.animation?r(l(u),null,0):1;p(a),t.scaleOverlay?(n(i),e()):(e(),n(i))}function o(){u+=s,i(),1>=u?M(o):"function"==typeof t.onAnimationComplete&&t.onAnimationComplete()}var s=t.animation?1/r(t.animationSteps,Number.MAX_VALUE,1):1,l=h[t.animationEasing],u=t.animation?0:1;"function"!=typeof e&&(e=function(){}),M(o)}function a(t,e,n,a,o,r){function s(t){return Math.floor(Math.log(t)/Math.LN10)}var l,u,c,h,f,d,p;for(d=a-o,p=s(d),l=Math.floor(o/(1*Math.pow(10,p)))*Math.pow(10,p),u=Math.ceil(a/(1*Math.pow(10,p)))*Math.pow(10,p),c=u-l,h=Math.pow(10,p),f=Math.round(c/h);n>f||f>e;)n>f?(h/=2,f=Math.round(c/h)):(h*=2,f=Math.round(c/h));var v=[];return i(r,v,f,l,h),{steps:f,stepValue:h,graphMin:l,labels:v}}function i(t,e,n,a,i){if(t)for(var o=1;n+1>o;o++)e.push(u(t,{value:(a+i*o).toFixed(s(i))}))}function o(t){return!isNaN(parseFloat(t))&&isFinite(t)}function r(t,e,n){return o(e)&&t>e?e:o(n)&&n>t?n:t}function s(t){return 0!=t%1?t.toString().split(".")[1].length:0}function l(t,e){var n={};for(var a in t)n[a]=t[a];for(var a in e)n[a]=e[a];return n}function u(t,e){var n=/\W/.test(t)?new Function("obj","var p=[],print=function(){p.push.apply(p,arguments);};with(obj){p.push('"+t.replace(/[\r\t\n]/g," ").split("<%").join("	").replace(/((^|%>)[^\t]*)'/g,"$1\r").replace(/\t=(.*?)%>/g,"',$1,'").split("	").join("');").split("%>").join("p.push('").split("\r").join("\\'")+"');}return p.join('');"):w[t]=w[t]||u(document.getElementById(t).innerHTML);return e?n(e):n}var c=this,h={linear:function(t){return t},easeInQuad:function(t){return t*t},easeOutQuad:function(t){return-1*t*(t-2)},easeInOutQuad:function(t){return(t/=.5)<1?.5*t*t:-0.5*(--t*(t-2)-1)},easeInCubic:function(t){return t*t*t},easeOutCubic:function(t){return 1*((t=t/1-1)*t*t+1)},easeInOutCubic:function(t){return(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},easeInQuart:function(t){return t*t*t*t},easeOutQuart:function(t){return-1*((t=t/1-1)*t*t*t-1)},easeInOutQuart:function(t){return(t/=.5)<1?.5*t*t*t*t:-0.5*((t-=2)*t*t*t-2)},easeInQuint:function(t){return 1*(t/=1)*t*t*t*t},easeOutQuint:function(t){return 1*((t=t/1-1)*t*t*t*t+1)},easeInOutQuint:function(t){return(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},easeInSine:function(t){return-1*Math.cos(t/1*(Math.PI/2))+1},easeOutSine:function(t){return 1*Math.sin(t/1*(Math.PI/2))},easeInOutSine:function(t){return-0.5*(Math.cos(Math.PI*t/1)-1)},easeInExpo:function(t){return 0==t?1:1*Math.pow(2,10*(t/1-1))},easeOutExpo:function(t){return 1==t?1:1*(-Math.pow(2,-10*t/1)+1)},easeInOutExpo:function(t){return 0==t?0:1==t?1:(t/=.5)<1?.5*Math.pow(2,10*(t-1)):.5*(-Math.pow(2,-10*--t)+2)},easeInCirc:function(t){return t>=1?t:-1*(Math.sqrt(1-(t/=1)*t)-1)},easeOutCirc:function(t){return 1*Math.sqrt(1-(t=t/1-1)*t)},easeInOutCirc:function(t){return(t/=.5)<1?-0.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},easeInElastic:function(t){var e=1.70158,n=0,a=1;if(0==t)return 0;if(1==(t/=1))return 1;if(n||(n=.3),a<Math.abs(1)){a=1;var e=n/4}else var e=n/(2*Math.PI)*Math.asin(1/a);return-(a*Math.pow(2,10*(t-=1))*Math.sin((1*t-e)*2*Math.PI/n))},easeOutElastic:function(t){var e=1.70158,n=0,a=1;if(0==t)return 0;if(1==(t/=1))return 1;if(n||(n=.3),a<Math.abs(1)){a=1;var e=n/4}else var e=n/(2*Math.PI)*Math.asin(1/a);return a*Math.pow(2,-10*t)*Math.sin((1*t-e)*2*Math.PI/n)+1},easeInOutElastic:function(t){var e=1.70158,n=0,a=1;if(0==t)return 0;if(2==(t/=.5))return 1;if(n||(n=1*.3*1.5),a<Math.abs(1)){a=1;var e=n/4}else var e=n/(2*Math.PI)*Math.asin(1/a);return 1>t?-.5*a*Math.pow(2,10*(t-=1))*Math.sin((1*t-e)*2*Math.PI/n):.5*a*Math.pow(2,-10*(t-=1))*Math.sin((1*t-e)*2*Math.PI/n)+1},easeInBack:function(t){var e=1.70158;return 1*(t/=1)*t*((e+1)*t-e)},easeOutBack:function(t){var e=1.70158;return 1*((t=t/1-1)*t*((e+1)*t+e)+1)},easeInOutBack:function(t){var e=1.70158;return(t/=.5)<1?.5*t*t*(((e*=1.525)+1)*t-e):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:function(t){return 1-h.easeOutBounce(1-t)},easeOutBounce:function(t){return(t/=1)<1/2.75?1*7.5625*t*t:2/2.75>t?1*(7.5625*(t-=1.5/2.75)*t+.75):2.5/2.75>t?1*(7.5625*(t-=2.25/2.75)*t+.9375):1*(7.5625*(t-=2.625/2.75)*t+.984375)},easeInOutBounce:function(t){return.5>t?.5*h.easeInBounce(2*t):.5*h.easeOutBounce(2*t-1)+.5}},f=t.canvas.width,d=t.canvas.height;window.devicePixelRatio&&(t.canvas.style.width=f+"px",t.canvas.style.height=d+"px",t.canvas.height=d*window.devicePixelRatio,t.canvas.width=f*window.devicePixelRatio,t.scale(window.devicePixelRatio,window.devicePixelRatio)),this.Line=function(e,n){c.Line.defaults={scaleOverlay:!1,scaleOverride:!1,scaleSteps:null,scaleStepWidth:null,scaleStartValue:null,scaleLineColor:"rgba(0,0,0,.1)",scaleLineWidth:1,scaleShowLabels:!1,scaleLabel:"<%=value%>",scaleFontFamily:"'Arial'",scaleFontSize:12,scaleFontStyle:"normal",scaleFontColor:"#666",scaleShowGridLines:!1,scaleGridLineColor:"rgba(0,0,0,.05)",scaleGridLineWidth:1,bezierCurve:!0,pointDot:!1,pointDotRadius:4,pointDotStrokeWidth:2,datasetStroke:!0,datasetStrokeWidth:1,datasetFill:!0,animation:!0,animationSteps:30,animationEasing:"easeOutQuart",onAnimationComplete:null};var a=n?l(c.Line.defaults,n):c.Line.defaults;return new v(e,a,t)};var p=function(t){t.clearRect(0,0,f,d)},v=function(t,o,r){function s(n){function a(a,i){return F-n*e(t.datasets[a].data[i],M,v)}function i(t){return O+b*t}for(var s=0;s<t.datasets.length;s++){r.strokeStyle=t.datasets[s].strokeColor,r.lineWidth=o.datasetStrokeWidth,r.beginPath(),r.moveTo(O,F-n*e(t.datasets[s].data[0],M,v));for(var l=1;l<t.datasets[s].data.length;l++)o.bezierCurve?r.bezierCurveTo(i(l-.5),a(s,l-1),i(l-.5),a(s,l),i(l),a(s,l)):r.lineTo(i(l),a(s,l));if(r.stroke(),o.datasetFill?(r.lineTo(O+b*(t.datasets[s].data.length-1),F),r.lineTo(O,F),r.closePath(),r.fillStyle=t.datasets[s].fillColor,r.fill()):r.closePath(),o.pointDot){r.fillStyle=t.datasets[s].pointColor,r.strokeStyle=t.datasets[s].pointStrokeColor,r.lineWidth=o.pointDotStrokeWidth;for(var u=0;u<t.datasets[s].data.length;u++)r.beginPath(),r.arc(O+b*u,F-n*e(t.datasets[s].data[u],M,v),o.pointDotRadius,0,2*Math.PI,!0),r.fill(),r.stroke()}}}function l(){r.lineWidth=o.scaleLineWidth,r.strokeStyle=o.scaleLineColor,r.beginPath(),r.moveTo(f-I/2+5,F),r.lineTo(f-I/2-L-5,F),r.stroke(),C>0?(r.save(),r.textAlign="right"):r.textAlign="center",r.fillStyle=o.scaleFontColor;for(var e=0;e<t.labels.length;e++)r.save(),r.beginPath(),r.moveTo(O+e*b,F+3),o.scaleShowGridLines&&e>0?(r.lineWidth=o.scaleGridLineWidth,r.strokeStyle=o.scaleGridLineColor,r.lineTo(O+e*b,5)):r.lineTo(O+e*b,F+3),r.stroke();r.lineWidth=o.scaleLineWidth,r.strokeStyle=o.scaleLineColor,r.textAlign="right",r.textBaseline="middle";for(var n=0;n<M.steps;n++)r.beginPath(),r.moveTo(O-3,F-(n+1)*v),o.scaleShowGridLines?(r.lineWidth=o.scaleGridLineWidth,r.strokeStyle=o.scaleGridLineColor,r.lineTo(O+L+5,F-(n+1)*v)):r.lineTo(O-.5,F-(n+1)*v),r.stroke(),o.scaleShowLabels&&r.fillText(M.labels[n],O-8,F-(n+1)*v)}function u(){L=f,b=L/(t.labels.length-1),O=0,F=d}function c(){p=d,r.font=o.scaleFontStyle+" "+o.scaleFontSize+"px "+o.scaleFontFamily,I=1;for(var e=0;e<t.labels.length;e++){var n=0;I=n>I?n:I}f/t.labels.length<I?(C=45,f/t.labels.length<Math.cos(C)*I?(C=90,p-=I):p-=Math.sin(C)*I):p-=o.scaleFontSize,w=o.scaleFontSize,p=d,m=p}function h(){for(var e=Number.MIN_VALUE,n=Number.MAX_VALUE,a=0;a<t.datasets.length;a++)for(var i=0;i<t.datasets[a].data.length;i++)t.datasets[a].data[i]>e&&(e=t.datasets[a].data[i]),t.datasets[a].data[i]<n&&(n=t.datasets[a].data[i]);var o=Math.floor(m/(.66*w)),r=Math.floor(.5*(m/w));return{maxValue:e,minValue:n,maxSteps:o,minSteps:r}}var p,v,M,w,m,S,g,b,I,L,O,F,C=0;c(),S=h(),g=o.scaleShowLabels?o.scaleLabel:"",o.scaleOverride?(M={steps:o.scaleSteps,stepValue:o.scaleStepWidth,graphMin:o.scaleStartValue,labels:[]},i(g,M.labels,M.steps,o.scaleStartValue,o.scaleStepWidth)):M=a(m,S.maxSteps,S.minSteps,S.maxValue,S.minValue,g),v=Math.floor(m/M.steps),u(),n(o,l,s,r)},M=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}(),w={}};
