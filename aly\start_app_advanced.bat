@echo off
chcp 65001 >nul
title MySQL 数据库管理工具

REM 检查Python是否可用
where python >nul 2>&1
if %errorlevel% neq 0 (
    echo Python未找到！请确保已安装Python并添加到PATH环境变量中。
    pause
    exit /b 1
)

:menu
cls
echo ===================================
echo      MySQL 数据库管理工具启动器
echo ===================================
echo.
echo  1. 正常模式启动
echo  2. 调试模式启动
echo  3. 安装/更新依赖
echo  4. 退出
echo.
echo ===================================
set /p choice=请选择操作 (1-4): 

if "%choice%"=="1" goto normal_mode
if "%choice%"=="2" goto debug_mode
if "%choice%"=="3" goto install_deps
if "%choice%"=="4" goto end

echo 无效选择，请重新输入！
timeout /t 2 >nul
goto menu

:normal_mode
cls
echo 正在正常模式下启动应用...
echo.
REM 使用setx永久设置环境变量可能需要管理员权限，这里使用set临时设置
set "HOST=0.0.0.0"
set "PORT=5000"
set "FLASK_DEBUG=False"
echo 应用将在 http://localhost:5000 运行
echo 按 Ctrl+C 可以停止应用
echo.
REM 使用call确保批处理文件在python执行完后继续执行
call python app.py
echo.
echo 应用已停止运行
pause
goto menu

:debug_mode
cls
echo 正在调试模式下启动应用...
echo.
set "HOST=0.0.0.0"
set "PORT=5000"
set "FLASK_DEBUG=True"
echo 应用将在 http://localhost:5000 运行（调试模式）
echo 按 Ctrl+C 可以停止应用
echo.
call python app.py
echo.
echo 应用已停止运行
pause
goto menu

:install_deps
cls
echo 正在检查管理员权限...
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: 未以管理员身份运行，安装依赖可能失败。
    echo 建议右键点击此批处理文件，选择"以管理员身份运行"。
    echo.
    echo 是否继续尝试安装依赖？(Y/N)
    set /p admin_choice=
    if /i not "%admin_choice%"=="Y" goto menu
)

echo 正在安装/更新依赖...
echo.
call pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
if %errorlevel% neq 0 (
    echo 安装依赖失败！请检查网络连接或尝试以管理员身份运行。
) else (
    echo 依赖安装/更新完成！
)
pause
goto menu

:end
cls
echo 感谢使用 MySQL 数据库管理工具
timeout /t 2 >nul
exit 