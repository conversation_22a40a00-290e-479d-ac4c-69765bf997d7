#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工单状态API测试脚本
测试按状态获取工单列表的API是否返回完整字段
"""

import requests
import json
import sys
from datetime import datetime

# API配置
BASE_URL = "http://localhost:8000"
API_ENDPOINTS = {
    "login": f"{BASE_URL}/auth/login",
    "tickets_by_status": f"{BASE_URL}/ticket-integration/tickets/by-status",
    "project_tickets": f"{BASE_URL}/ticket-integration/projects/{{project_id}}/tickets"
}

# 测试用户凭据
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

class TicketAPITester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        
    def login(self):
        """登录获取token"""
        print("🔐 正在登录...")
        try:
            response = self.session.post(API_ENDPOINTS["login"], json=TEST_USER)
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.token = data["data"]["access_token"]
                    self.session.headers.update({"Authorization": f"Bearer {self.token}"})
                    print("✅ 登录成功")
                    return True
                else:
                    print(f"❌ 登录失败: {data.get('message')}")
                    return False
            else:
                print(f"❌ 登录请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return False
    
    def test_tickets_by_status(self, status, limit=10):
        """测试按状态获取工单列表API"""
        print(f"\n📋 测试获取{status}工单列表...")
        try:
            params = {"status": status, "limit": limit}
            response = self.session.get(API_ENDPOINTS["tickets_by_status"], params=params)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    tickets = data["data"]["tickets"]
                    print(f"✅ 成功获取 {len(tickets)} 条工单")
                    
                    if tickets:
                        # 检查第一条工单的字段
                        first_ticket = tickets[0]
                        print(f"📊 第一条工单字段数量: {len(first_ticket)}")
                        
                        # 检查关键字段
                        required_fields = [
                            'feelec_ticket_id', 'feelec_ticket_no', 'feelec_title',
                            'feelec_project_id', 'project_name', 'publisher_name',
                            'processor_name', 'department_name', 'company_name',
                            'status_name', 'priority_text', 'template_name',
                            'create_time_formatted', 'complete_time_formatted',
                            'first_assign_time_formatted', 'first_process_time_formatted',
                            'deadline_formatted', 'process_duration_text',
                            'is_overdue', 'source_text'
                        ]
                        
                        missing_fields = []
                        for field in required_fields:
                            if field not in first_ticket:
                                missing_fields.append(field)
                        
                        if missing_fields:
                            print(f"⚠️  缺少字段: {missing_fields}")
                        else:
                            print("✅ 所有必需字段都存在")
                        
                        # 显示部分字段内容
                        print(f"🎫 工单编号: {first_ticket.get('feelec_ticket_no')}")
                        print(f"📝 工单标题: {first_ticket.get('feelec_title')}")
                        print(f"🏢 项目名称: {first_ticket.get('project_name', '无')}")
                        print(f"👤 发布人: {first_ticket.get('publisher_name', '无')}")
                        print(f"🏷️  状态: {first_ticket.get('status_name', '无')}")
                        print(f"⚡ 优先级: {first_ticket.get('priority_text', '无')}")
                        
                        return True, tickets
                    else:
                        print("⚠️  没有找到工单数据")
                        return True, []
                else:
                    print(f"❌ API返回失败: {data.get('message')}")
                    return False, None
            else:
                print(f"❌ 请求失败: {response.status_code}")
                return False, None
                
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
            return False, None
    
    def compare_with_project_tickets(self, project_id):
        """对比项目工单API的字段"""
        print(f"\n🔍 对比项目工单API字段...")
        try:
            url = API_ENDPOINTS["project_tickets"].format(project_id=project_id)
            response = self.session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data["data"]["tickets"]:
                    project_ticket = data["data"]["tickets"][0]
                    print(f"📊 项目工单字段数量: {len(project_ticket)}")
                    return project_ticket
                else:
                    print("⚠️  项目工单API没有返回数据")
                    return None
            else:
                print(f"❌ 项目工单API请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 对比异常: {str(e)}")
            return None
    
    def run_full_test(self):
        """运行完整测试"""
        print("🚀 开始工单状态API完整测试")
        print("=" * 50)
        
        # 1. 登录
        if not self.login():
            return False
        
        # 2. 测试三种状态
        test_statuses = ["all", "completed", "urgent"]
        results = {}
        
        for status in test_statuses:
            success, tickets = self.test_tickets_by_status(status, limit=5)
            results[status] = {"success": success, "tickets": tickets}
        
        # 3. 总结测试结果
        print("\n" + "=" * 50)
        print("📊 测试结果总结:")
        
        all_success = True
        for status, result in results.items():
            if result["success"]:
                count = len(result["tickets"]) if result["tickets"] else 0
                print(f"✅ {status}状态: 成功获取 {count} 条工单")
            else:
                print(f"❌ {status}状态: 测试失败")
                all_success = False
        
        if all_success:
            print("\n🎉 所有测试通过！API修改成功！")
        else:
            print("\n⚠️  部分测试失败，需要检查API实现")
        
        return all_success

def main():
    """主函数"""
    tester = TicketAPITester()
    success = tester.run_full_test()
    
    if success:
        print("\n✅ 测试完成，可以继续前端测试")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，请检查后端API")
        sys.exit(1)

if __name__ == "__main__":
    main()
