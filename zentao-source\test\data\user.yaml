title: table zt_user
desc: "用户信息"
author: sgm
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "内外部人员"
    range: inside{900},outside,inside{10000}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: dept
    note: "所属部门"
    range: 1-100{10}
  - field: account
    note: "用户名"
    fields:
    - field: account1
      range: admin,user{99},test{100},dev{100},pm{100},po{100},td{100},pd{100},qd{100},top{100},outside{100},others{100},a,bb,ccc,qwuiadsd?!2as@#%$aasd~aj1!@#1
    - field: account2
      range: "[],1-99,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100,[]{4}"
  - field: password
    note: "密码"
    range: 123Qwe!@#{346}
    format: md5
  - field: role
    note: "职位"
    range: qa{101},dev{200},pm{10},po{10},td{2},pd{2},qd{2},top{5},others{14}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: realname
    note: "真实姓名"
    fields:
    - field: realname1
      range: admin,用户{99},测试{100},开发{100},项目经理{100},产品经理{100},研发主管{100},产品主管{100},测试主管{100},高层管理{100},外用户{100},其他{100},A,BB,CCC,非常长非常长的用户名怎么这么长?
    - field: realname2
      range: "[],1-99,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100,[]{4}"
  - field: nickname
    note: "昵称"
  - field: commiter
    note: "源代码帐号"
    fields:
    - field: commiter1
      range: admin,user{99},test{100},dev{100},pm{100},po{100},td{100},pd{100},qd{100},top{100},outside{100},others{100},a,bb,ccc,qwuiadsd?!2as@#%$aasd~aj1!@#1
    - field: commiter2
      range: "[],1-99,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100,[]{4}"
  - field: avatar
    note: ""
    range: ""
    format: ""
  - field: birthday
    note: "生日"
    range: "(-30Y)-(-20Y):60D"
    type: timestamp
    format: "YYYY-MM-DD"
  - field: gender
    note: "性别"
    range: f,m
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: email
    note: "邮箱"
    fields:
    - field: email1
      range: 1000-9999:2
    - field: email2
      range: 1000-9999:2
    - field: email3
      range: "[@qq.com,@163.com,@gmail.com]"
  - field: skype
    note: "Skype"
    range: Skype
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: qq
    note: "QQ"
    range: QQ
    fields:
    - field: qq1
      range: 100-999:R
    - field: qq2
      range: 100-999:R
    - field: qq3
      range: 100-999:R
  - field: mobile
    note: "手机"
  - field: phone
    note: "电话"
    fields:
    - field: phone1
      range: 130-199:R
    - field: phone2
      range: 0000-9999:R
    - field: phone3
      range: 0000-9999:R
  - field: weixin
    note: "微信"
  - field: dingding
    note: "钉钉"
  - field: slack
    note: "Slack"
    range: slack
  - field: whatsapp
    note: "WhatsApp"
    range: whatsApp
  - field: address
    note: "通讯地址"
  - field: zipcode
    note: "邮编"
  - field: join
    note: "入职日期"
    range: "(M)-(w)"
    type: timestamp
    postfix: ""
    format: "YY/MM/DD hh:mm:ss"
  - field: visits
    note: "访问次数"
    range: 0-10000:R
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: visions
    note: "版本"
    range: rnd
    prefix: ""
    postfix: ",lite,or"
    loop: 0
    format: ""
  - field: ip
    note: "最后IP"
    format: ""
  - field: last
    note: "最后登录"
    range: "(-1M)-(+1w):60m"
    type: timestamp
  - field: fails
    note: "失败次数"
    range: 1-5
  - field: locked
    note: "锁定时间"
    range: "`2023-01-02 10:00:00`"
  - field: company
    note: "公司"
    range: 1
  - field: ranzhi
    note: "ZDOO账号"
    fields:
    - field: account1
      range: admin,user{99},test{100},dev{100},pm{100},po{100},td{100},pd{100},qd{100},top{100},outside{100},others{100},a,bb,ccc,qwuiadsd?!2as@#%$aasd~aj1!@#1
    - field: account2
      range: "[],1-99,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100,1-100,[]{4}"
  - field: score
    note: "积分"
    range: 1-10000:R
  - field: scoreLevel
    note: "积分等级"
    range: 0
  - field: deleted
    note: "是否删除"
    range: 0
