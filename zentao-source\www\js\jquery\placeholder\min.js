/*! http://mths.be/placeholder v2.0.7 by @mathias */
;(function(h,j,e){var a="placeholder" in j.createElement("input");var f="placeholder" in j.createElement("textarea");var k=e.fn;var d=e.valHooks;var b=e.propHooks;var m;var l;if(a&&f){l=k.placeholder=function(){return this};l.input=l.textarea=true}else{l=k.placeholder=function(){var o=this;o.filter((a?"textarea":":input")+"[placeholder]").not(".placeholder").bind({"focus.placeholder":c,"blur.placeholder":g}).data("placeholder-enabled",true).trigger("blur.placeholder");return o};l.input=a;l.textarea=f;m={get:function(p){var o=e(p);var q=o.data("placeholder-password");if(q){return q[0].value}return o.data("placeholder-enabled")&&o.hasClass("placeholder")?"":p.value},set:function(p,r){var o=e(p);var q=o.data("placeholder-password");if(q){return q[0].value=r}if(!o.data("placeholder-enabled")){return p.value=r}if(r==""){p.value=r;if(p!=n()){g.call(p)}}else{if(o.hasClass("placeholder")){c.call(p,true,r)||(p.value=r)}else{p.value=r}}return o}};if(!a){d.input=m;b.value=m}if(!f){d.textarea=m;b.value=m}e(function(){e(j).delegate("form","submit.placeholder clearplaceholder",function(){var o=e(".placeholder",this).each(c);setTimeout(function(){o.each(g)},100)})});e(h).bind("beforeunload.placeholder",function(){e(".placeholder").each(function(){this.value=""})})}function i(p){var o={};var q=/^jQuery\d+$/;e.each(p.attributes,function(s,r){if(r.specified&&!q.test(r.name)){o[r.name]=r.value}});return o}function c(p,q){var o=this;var r=e(o);if(o.value==r.attr("placeholder")&&r.hasClass("placeholder")){if(r.data("placeholder-password")){r=r.hide().next().show().attr("id",r.removeAttr("id").data("placeholder-id"));if(p===true){return r[0].value=q}r.focus()}else{o.value="";r.removeClass("placeholder");o==n()&&o.select()}}}function g(){var s;var o=this;var r=e(o);var q=this.id;if(o.value==""){if(o.type=="password"){if(!r.data("placeholder-textinput")){try{s=r.clone().attr({type:"text"})}catch(p){s=e("<input>").attr(e.extend(i(this),{type:"text"}))}s.removeAttr("name").data({"placeholder-password":r,"placeholder-id":q}).bind("focus.placeholder",c);r.data({"placeholder-textinput":s,"placeholder-id":q}).before(s)}r=r.removeAttr("id").hide().prev().attr("id",q).show()}r.addClass("placeholder");r[0].value=r.attr("placeholder")}else{r.removeClass("placeholder")}}function n(){try{return j.activeElement}catch(o){}}}(this,document,jQuery));
$(function(){$('[placeholder]:not(.disabled-ie-placeholder)').placeholder();});
