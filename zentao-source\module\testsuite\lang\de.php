<?php
/**
 * The testsuite module en file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     testsuite
 * @version     $Id: zh-cn.php 4490 2013-02-27 03:27:05Z <EMAIL> $
 * @link        https://www.zentao.net
 */
$lang->testsuite->create           = "Erstellen";
$lang->testsuite->delete           = "Löschen";
$lang->testsuite->view             = "Übersicht";
$lang->testsuite->edit             = "Bearbeiten";
$lang->testsuite->browse           = "Liste";
$lang->testsuite->linkCase         = "Fälle";
$lang->testsuite->linkVersion      = "Version";
$lang->testsuite->unlinkCase       = "Verknüpfung aufheben";
$lang->testsuite->unlinkCaseAction = "Unlink Case";
$lang->testsuite->batchUnlinkCases = "Mehrere Verknüpfungen aufheben";
$lang->testsuite->deleted          = 'gelöscht';
$lang->testsuite->successSaved     = 'Saved';

$lang->testsuite->id             = 'ID';
$lang->testsuite->pri            = 'Priority';
$lang->testsuite->common         = 'Test Suite';
$lang->testsuite->project        = $lang->projectCommon;
$lang->testsuite->product        = $lang->productCommon;
$lang->testsuite->name           = 'Suite Name';
$lang->testsuite->type           = 'Type';
$lang->testsuite->desc           = 'Beschreibung';
$lang->testsuite->mailto         = 'Mailto';
$lang->testsuite->author         = 'Zugriffskontrolle';
$lang->testsuite->addedBy        = 'Ersteller';
$lang->testsuite->addedDate      = 'Create Date';
$lang->testsuite->addedTime      = 'Create Time';
$lang->testsuite->lastEditedBy   = 'Last Edited By';
$lang->testsuite->lastEditedDate = 'Last Edited Date';

$lang->testsuite->legendDesc      = 'Beschreibung';
$lang->testsuite->legendBasicInfo = 'Basis Info';

$lang->testsuite->unlinkedCases = 'Unverknüpfte Fälle';

$lang->testsuite->confirmDelete     = 'Möchten Sie diese Suite löschen?';
$lang->testsuite->confirmUnlinkCase = 'Möchten Sie diese Verknüpfung aufheben?';
$lang->testsuite->noticeNone        = 'Sie haben noch keine Suite erstellt.';
$lang->testsuite->noModule          = '<div>Sie haben keine Module</div><div>Jetzt verwalten</div>';
$lang->testsuite->noTestsuite       = 'Keine Suite. ';
$lang->testsuite->summary           = "Total suites: <strong>%d</strong>, public: <strong>%d</strong>, private: <strong>%d</strong>.";

$lang->testsuite->lblCases      = 'Fälle';
$lang->testsuite->lblUnlinkCase = 'Unverknüpfte Fälle';

$lang->testsuite->authorList['private'] = 'Privat';
$lang->testsuite->authorList['public']  = 'Öffentlich';

$lang->testsuite->featureBar['browse']['all'] = 'All';
