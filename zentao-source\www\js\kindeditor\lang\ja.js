/*******************************************************************************
* KindEditor - WYSIWYG HTML Editor for Internet
* Copyright (C) 2006-2011 kindsoft.net
*
* <AUTHOR> <<EMAIL>>
* @site http://www.kindsoft.net/
* @licence http://www.kindsoft.net/license.php
*******************************************************************************/

KindEditor.lang({
	source : 'HTML コード',
	preview : 'プレビュー',
	undo : '戻る(Ctrl+Z)',
	redo : '次へ(Ctrl+Y)',
	cut : '切取り(Ctrl+X)',
	copy : 'コピー(Ctrl+C)',
	paste : '貼り付け(Ctrl+V)',
	plainpaste : 'テキストのみ保持',
	wordpaste : 'Wordから貼り付け',
	selectall : '全て選択(Ctrl+A)',
	justifyleft : '左揃え',
	justifycenter : '中央揃え',
	justifyright : '右揃え',
	justifyfull : '両端揃え',
	insertorderedlist : '番号',
	insertunorderedlist : '段落番号',
	indent : 'インデントを増やす',
	outdent : 'インデントを減らす',
	subscript : '下線',
	superscript : '上線',
	formatblock : '段落',
	fontname : 'フォント',
	fontsize : 'フォントサイズ',
	forecolor : 'フォント色',
	hilitecolor : '塗りつぶし色',
	bold : '太字(Ctrl+B)',
	italic : '斜体(Ctrl+I)',
	underline : '下線(Ctrl+U)',
	strikethrough : '取消線',
	removeformat : 'フォーマット削除',
	image : '画像',
	multiimage : '画像一括アップロード',
	flash : 'Flash',
	media : 'メディア',
	table : 'テーブル',
	tablecell : 'セル',
	hr : '横線挿入',
	emoticons : '絵文字挿入',
	link : 'ハイパーリンク',
	unlink : 'キャンセル',
	fullscreen : 'フルスクリーン',
	about : 'について',
	print : '印刷(Ctrl+P)',
	filemanager : 'ファイルスペース',
	code : 'ソースコード挿入',
	map : 'Googleマップ',
	baidumap : '百度マップ',
	lineheight : '行の高さ',
	clearhtml : 'HTMLクリア',
	pagebreak : '改ページの挿入',
	quickformat : 'クイックフォーマット',
	insertfile : 'ファイル挿入',
	template : 'テンプレート挿入',
	anchor : 'アンカー',
	yes : 'はい',
	no : 'キャンセル',
	close : 'クローズ',
	editImage : '画像プロパティ',
	deleteImage : '画像削除',
	editFlash : 'Flashプロパティ',
	deleteFlash : 'Flash削除',
	editMedia : 'メディアプロパティ',
	deleteMedia : 'メディア削除',
	editLink : 'ハイパーリンクプロパティ',
	deleteLink : 'キャンセル',
	editAnchor : 'アンカープロパティ',
	deleteAnchor : 'アンカー削除',
	tableprop : 'テーブルプロパティ',
	tablecellprop : 'セルプロパティ',
	tableinsert : 'テーブル挿入',
	tabledelete : 'テーブル削除',
	tablecolinsertleft : '列を左に挿入する',
	tablecolinsertright : '列を右に挿入する',
	tablerowinsertabove : '行を上に挿入する',
	tablerowinsertbelow : '行を下に挿入する',
	tablerowmerge : 'セルを結合して下揃え',
	tablecolmerge : 'セルを結合して右揃え',
	tablerowsplit : '行分割',
	tablecolsplit : '列分割',
	tablecoldelete : '列削除',
	tablerowdelete : '行削除',
	noColor : '色無し',
	pleaseSelectFile : 'ファイルを選択してください。',
	invalidImg : "有効なURLを入力してください。\n jpg,gif,bmp,png形式のみ選択可能。",
	invalidMedia : "有効なURLを入力してください。\nswf,flv,mp3,wav,wma,wmv,mid,avi,mpg,asf,rm,rmvb形式のみ選択可能。",
	invalidWidth : "幅は英数字のみ。",
	invalidHeight : "高さは英数字のみ。",
	invalidBorder : "外枠は英数字のみ。",
	invalidUrl : "有効なURLを入力してください。",
	invalidRows : '行数は必須項目の為、０以上の英数字を入力してください。',
	invalidCols : '列数は必須項目の為、０以上の英数字を入力してください。',
	invalidPadding : '余白は英数字のみ。',
	invalidSpacing : '行間隔は英数字のみ。',
	invalidJson : 'サーバーエラー。',
	uploadSuccess : 'アップロード成功しました。',
	cutError : 'お客様のブラウザはセキュリティの為、切取り操作は禁止されている為、ホットキーのCtrl＋Xを使ってください。',
	copyError : 'ブラウザのセキュリティ設定はコピー操作が許可されていません。ショートカットキー（Ctrl+C）を使ってください。',
	pasteError : 'ブラウザのセキュリティ設定は貼り付け操作を使用できません。ショートカットキー（Ctrl+V）を使ってください。',
	ajaxLoading : '転送中、お待ちください...',
	uploadLoading : 'アップロード中、お待ちください...',
	uploadError : 'アップロードエラー',
	'plainpaste.comment' : 'ホットキー(Ctrl+X)を使用し、選択された部分をしたのセルに貼り付けてください。',
	'wordpaste.comment' : 'ショートカットキー（Ctrl+V）を使って、内容を下のブロックに貼り付けてください。',
	'code.pleaseInput' : 'ソースコード挿入。',
	'link.url' : 'URL',
	'link.linkType' : 'タイプ',
	'link.newWindow' : '新しいウィンドウ',
	'link.selfWindow' : '現在のウィンドウ',
	'flash.url' : 'URL',
	'flash.width' : '幅',
	'flash.height' : '高さ',
	'flash.upload' : 'アップロード',
	'flash.viewServer' : 'ファイルスペース',
	'media.url' : 'URL',
	'media.urlTip': '複数のURLはコンマで区切られます',
	'media.width' : '幅',
	'media.height' : '高さ',
	'media.autostart' : '自動放送',
	'media.upload' : 'アップロード',
	'media.viewServer' : 'ファイルスペース',
	'media.controls': '再生コントロール',
	'image.remoteImage' : 'オンライン画像',
	'image.localImage' : 'ローカルアップロード',
	'image.remoteUrl' : '画像アドレス',
	'image.localUrl' : 'ファイルアップロード',
	'image.size' : '画像サイズ',
	'image.width' : '幅',
	'image.height' : '高さ',
	'image.resetSize' : 'サイズリセット',
	'image.align' : '配置',
	'image.defaultAlign' : 'デフォルト',
	'image.leftAlign' : '左揃え',
	'image.rightAlign' : '右揃え',
	'image.imgTitle' : '画像説明',
	'image.upload' : '参照...',
	'image.viewServer' : '画像スペース',
	'multiimage.uploadDesc' : 'ユーザが同時に<%=uploadLimit%>枚の画像をアップロードできます，1枚画像のサイズは<%=sizeLimit%>以下です',
	'multiimage.startUpload' : 'アップロード開始',
	'multiimage.clearAll' : '全て消去',
	'multiimage.insertAll' : '全て挿入',
	'multiimage.queueLimitExceeded' : 'ファイル数が制限を超えました。',
	'multiimage.fileExceedsSizeLimit' : 'ファイルサイズが制限を超えました。',
	'multiimage.zeroByteFile' : '空きファイルのアップロードができません。',
	'multiimage.invalidFiletype' : '無効なファイルタイプ。',
	'multiimage.unknownError' : '不明なエラーが発生、アップロードができません。',
	'multiimage.pending' : 'アップロード待ち',
	'multiimage.uploadError' : 'アップロード失敗',
	'filemanager.emptyFolder' : '空きフォルダ',
	'filemanager.moveup' : '前のフォルダーに移動する',
	'filemanager.viewType' : 'ビュータイプ：',
	'filemanager.viewImage' : 'サムネイル画像',
	'filemanager.listImage' : '詳細情報',
	'filemanager.orderType' : '並べ替え：',
	'filemanager.fileName' : '名前',
	'filemanager.fileSize' : 'サイズ',
	'filemanager.fileType' : 'タイプ',
	'insertfile.url' : 'URL',
	'insertfile.title' : 'ファイル説明',
	'insertfile.upload' : 'アップロード',
	'insertfile.viewServer' : 'ファイルスペース',
	'table.cells' : 'セル数',
	'table.rows' : '行数',
	'table.cols' : '列数',
	'table.size' : 'サイズ',
	'table.width' : '幅',
	'table.height' : '高さ',
	'table.percent' : '%',
	'table.px' : 'px',
	'table.space' : '余白',
	'table.padding' : '埋め込み',
	'table.spacing' : '間隔',
	'table.align' : '並べ方',
	'table.textAlign' : '水平',
	'table.verticalAlign' : '垂直',
	'table.alignDefault' : 'デフォルト',
	'table.alignLeft' : '左揃え',
	'table.alignCenter' : '中央揃え',
	'table.alignRight' : '右揃え',
	'table.alignTop' : '上',
	'table.alignMiddle' : '中央',
	'table.alignBottom' : '下',
	'table.alignBaseline' : '基線',
	'table.border' : '外枠',
	'table.borderWidth' : '外枠',
	'table.borderColor' : '色',
	'table.backgroundColor' : '塗りつぶし色',
	'map.address' : 'アドレス: ',
	'map.search' : '検索',
	'baidumap.address' : 'アドレス: ',
	'baidumap.search' : '検索',
	'baidumap.insertDynamicMap' : 'マップ挿入',
	'anchor.name' : 'アンカー名前',
	'formatblock.formatBlock' : {
		h1 : 'タイトル 1',
		h2 : 'タイトル 2',
		h3 : 'タイトル 3',
		h4 : 'タイトル 4',
		p : '正文'
	},
	'fontname.fontName' : {
		'SimSun' : '宋体',
		'NSimSun' : '新宋体',
		'FangSong_GB2312' : '仿宋_GB2312',
		'KaiTi_GB2312' : '楷体_GB2312',
		'SimHei' : '黑体',
		'Source Han Sans': '思源黑体',
        'Source Han Serif': '思源宋体',
		'Microsoft YaHei' : '微软雅黑',
		'Arial' : 'Arial',
		'Arial Black' : 'Arial Black',
		'Times New Roman' : 'Times New Roman',
		'Courier New' : 'Courier New',
		'Tahoma' : 'Tahoma',
		'Verdana' : 'Verdana'
	},
	'lineheight.lineHeight' : [
		{'1' : '1行間'},
		{'1.5' : '1.5行間'},
		{'2' : '2行間'},
		{'2.5' : '2.5行間'},
		{'3' : '3行間'}
	],
	'template.selectTemplate' : 'テンプレート',
	'template.replaceContent' : '内容置き換え',
	'template.fileList' : {
		'1.html' : '画像と文字',
		'2.html' : 'テーブル',
		'3.html' : '段落番号'
	}
}, 'ja');

if (window.$ && $.zui && $.zui.lang) {
	$.zui.lang('ja', {
		'kindeditor.advanceTable': {
			name: 'テーブル',
			xRxC: '{0}行 × {1}列',
			headerRow: '見出し行',
			headerCol: '見出し列',
			tableStyle: 'テーブルスタイル',
			addHeaderRow: '見出し行を追加',
			stripedRows: 'ストライブ 行',
			hoverRows: 'ホバー行',
			autoChangeTableWidth: '自動変更幅',
			tableWidthFixed: 'テーブル文字に適応',
			tableWidthFull: 'ページ幅で適応',
			tableBorder: 'テーブル枠線',
			tableHead: '見出し',
			tableContent: 'コンテンツ',
			mergeCells: 'セルを結合',
			defaultColor: 'デフォルト色',
			color: '色',
			forecolor: 'フォントの色',
			backcolor: '塗りつぶしの色',
			invalidBoderWidth: '外枠のサイズは必ず数値です。'
		},
		'kindeditor.pasteimage': {
			notSupportMsg: '使用されているブラウザは画像の貼り付けがサポートされていません！',
            placeholder: 'エディターを使用して画像を貼り付けます。',
            failMsg: '画像を貼り付けませんでした、後でやり直してください。',
            uploadingHint: '画像をアップロード中、しばらくお待ちください...',
		}
	});
}
