<template>
  <div class="project-management-content">
    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="24">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon projects">
              <el-icon><FolderOpened /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ projectStats.total }}</div>
              <div class="stat-label">项目总数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon investment">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ projectStats.totalInvestment }}万</div>
              <div class="stat-label">总投资额</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon progress">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ projectStats.inProgress }}</div>
              <div class="stat-label">进行中项目</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon completed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ projectStats.completed }}</div>
              <div class="stat-label">已完成项目</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 项目列表 -->
    <div class="project-list-container">
      <ProjectListDialog
        :visible="true"
        :entity="''"
        :category="'全部'"
        :title="''"
        :highlight-type="''"
        :show-header="false"
        :show-close="false"
        @close="() => {}"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { FolderOpened, Money, Clock, Check } from '@element-plus/icons-vue'
import ProjectListDialog from '@/views/dashboard/components/ProjectListDialog.vue'

// 项目统计数据
const projectStats = ref({
  total: 0,
  totalInvestment: 0,
  inProgress: 0,
  completed: 0
})

// 加载项目统计数据
const loadProjectStats = async () => {
  try {
    // TODO: 调用API获取统计数据
    // 这里先使用模拟数据
    projectStats.value = {
      total: 50,
      totalInvestment: 2850.71,
      inProgress: 32,
      completed: 18
    }
  } catch (error) {
    console.error('加载项目统计数据失败:', error)
  }
}

onMounted(() => {
  loadProjectStats()
})
</script>

<style scoped>
.project-management-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.stats-cards {
  flex-shrink: 0;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.projects {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.investment {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.progress {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

.project-list-container {
  flex: 1;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 隐藏内部对话框的装饰 */
:deep(.el-dialog__wrapper) {
  position: static !important;
  background: transparent !important;
}

:deep(.el-dialog) {
  margin: 0 !important;
  width: 100% !important;
  height: 100% !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  background: transparent !important;
}

:deep(.el-dialog__header) {
  display: none !important;
}

:deep(.el-dialog__body) {
  padding: 0 !important;
  height: 100% !important;
  overflow: hidden !important;
}

/* 优化内部项目列表样式 */
:deep(.project-list-content) {
  height: 100% !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}
</style>
