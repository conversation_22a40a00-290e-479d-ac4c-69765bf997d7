body {background: #1183fb linear-gradient(-90deg, #0a48d1 0%, #1183fb 100%); background-color: #1183fb;}
#reset {max-width: 400px !important; margin: 0 auto; margin-top: 13%;}
#resetPanel {background: #fff; overflow: hidden; box-shadow: 0 0 20px 0 rgba(0,0,0,.1); border-radius: 3px;}
#title {font-size: 16px; font-weight: bold; text-align: center; margin: 20px 0; margin-bottom: 0px; line-height: 26px; padding: 0 50px;}
#resetForm {padding: 0 40px; margin-top: 12px;}
#resetForm label {margin-bottom: 8px; font-size: 13px; color: #3C4353;}
#resetForm .form-group + .form-group {margin-top: 18px;}
#resetForm .form-control {border-color: #B2B6BF; background: transparent; border-radius: 2px; height: 30px;}
#resetForm .form-control:focus {border-color: #2272eb; background: #fff;}
#resetForm .form-actions {padding-bottom: 20px;}
.required:after {top: 32px; right: -15px;}
#resetPanel .form-group .input-group {width: 100%}
#resetPanel .form-group .input-group.required:after {top: 6px;}
#resetPanel .form-actions > .btn-wide {min-width: 45%;}
#resetPanel .form-actions > a.btn-wide {margin-right: 0px;}
#resetForm  .form-group div.text-danger.help-text {word-break: break-all;}
