title: table zt_story
desc: "需求"
author: automated export
version: "1.0"
fields:
  - field: parent
    note: "父需求ID"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: product
    note: "所属产品"
    range: 1-1000{50}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: branch
    note: "分支/平台"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: module
    note: "所属模块"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: plan
    note: "所属计划"
    range: 1-1000{50}
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: source
    note: "需求来源"
    range: customer,user,po,market,service,operation,support,competitor,partner,dev,tester,bug,forum,other
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: sourceNote
    note: "来源备注"
    range: 1-100000
    prefix: "这里是需求来源备注"
    postfix: ""
    loop: 0
    format: ""
  - field: fromBug
    note: "来源Bug"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: title
    note: "需求名称"
    fields:
      - field: field1
        range: 软件需求
      - field: field2
        range: 1-1000000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: keywords
    note: "关键词"
    range: 1-100000
    prefix: "关键词"
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "需求类型"
    range: story
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: pri
    note: "优先级"
    range: 1-4
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: estimate
    note: "预计工时"
    range: 0-20:R
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: status
    note: "当前状态"
    range: active
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: subStatus
    note: "子状态"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: color
    note: "标题颜色"
    from: common.color.v1.yaml
    use: color
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: stage
    note: "所处阶段"
    range: [],wait,[],planned,[],projected,[],developing,[],developed,[],testing,[],tested,[],verified,[],released,[],closed
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: stagedBy
    note: "设置阶段者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: mailto
    note: "抄送给"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: openedBy
    note: "由谁创建"
    fields:
      - field: openedBy1
        range: admin,user,test,dev
      - field: openedBy2
        range: [],2-4
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: openedDate
    note: "创建日期"
    range: "(M)-(w)"
    type: timestamp
    postfix: ""
    format: "YY/MM/DD"
  - field: assignedTo
    note: "指派给"
    fields:
      - field: openedBy1
        range: admin,user,test,dev
      - field: openedBy2
        range: [],2-4
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: assignedDate
    note: "指派日期"
    range: "(M)-(w)"
    type: timestamp
    postfix: ""
    format: "YY/MM/DD"
  - field: lastEditedBy
    note: "最后修改者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: reviewedBy
    note: "评审者"
    fields:
      - field: reviewedBy1
        range: user,test,dev,admin
      - field: reviewedBy2
        range: 5-7,[]{10}
    prefix: ""
    postfix: ""
    format: ""
  - field: closedBy
    note: "关闭者"
    fields:
      - field: closedBy1
        range: test,dev
      - field: closedBy2
        range: 1-10,1-10
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: closedReason
    note: "关闭原因"
    range: done,subdivided,duplicate,postponed,willnotdo,cancel,bydesign
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: toBug
    note: "转Bug"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: linkStories
    note: "相关需求"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: duplicateStory
    note: "重复需求ID"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: version
    note: "版本号"
    range: 1
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: URChanged
    note: "用户需求变更"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: deleted
    note: "是否删除"
    range: 0
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
