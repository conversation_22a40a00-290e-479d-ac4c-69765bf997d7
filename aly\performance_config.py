# 性能配置文件

# 数据库连接池配置
DB_POOL_CONFIG = {
    'pool_size': 10,  # 连接池大小
    'pool_reset_session': True,
    'pool_recycle': 3600,  # 连接回收时间（秒）
}

# 分页配置
PAGINATION_CONFIG = {
    'default_per_page': 50,  # 默认每页记录数
    'max_per_page': 1000,    # 最大每页记录数
}

# 缓存配置
CACHE_CONFIG = {
    'database_list_ttl': 300,    # 数据库列表缓存时间（秒）
    'table_list_ttl': 300,       # 表列表缓存时间（秒）
    'max_cache_size': 128,       # 最大缓存条目数
}

# 批量操作配置
BATCH_CONFIG = {
    'insert_batch_size': 1000,   # 批量插入大小
    'update_batch_size': 500,    # 批量更新大小
    'delete_batch_size': 500,    # 批量删除大小
}

# 性能监控配置
PERFORMANCE_CONFIG = {
    'slow_query_threshold': 1.0,  # 慢查询阈值（秒）
    'enable_query_logging': True, # 是否启用查询日志
    'log_level': 'INFO',          # 日志级别
}

# 数据库查询优化配置
QUERY_CONFIG = {
    'use_prepared_statements': True,  # 使用预处理语句
    'enable_query_cache': True,       # 启用查询缓存
    'connection_timeout': 10,         # 连接超时时间
    'read_timeout': 30,               # 读取超时时间
}
