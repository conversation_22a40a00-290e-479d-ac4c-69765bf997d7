#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试工单集成更新
"""

import requests
import json

def test_with_auth():
    """使用认证测试API"""
    print("=== 测试工单集成更新 ===")
    
    # 模拟登录获取token
    login_url = "http://localhost:8000/api/v1/auth/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        # 尝试登录
        login_response = requests.post(login_url, json=login_data)
        if login_response.status_code == 200:
            login_result = login_response.json()
            if login_result.get("success"):
                token = login_result["data"]["access_token"]
                headers = {"Authorization": f"Bearer {token}"}
                
                # 测试获取项目
                projects_url = "http://localhost:8000/api/v1/ticket-integration/projects"
                projects_response = requests.get(projects_url, headers=headers, params={"limit": 1})
                
                if projects_response.status_code == 200:
                    projects_data = projects_response.json()
                    if projects_data.get("success"):
                        print("✓ API认证和项目获取正常")
                        return True
                    else:
                        print(f"✗ 项目API返回失败: {projects_data.get('message')}")
                else:
                    print(f"✗ 项目API请求失败: {projects_response.status_code}")
            else:
                print(f"✗ 登录失败: {login_result.get('message')}")
        else:
            print(f"✗ 登录请求失败: {login_response.status_code}")
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
    
    return False

def main():
    """主函数"""
    print("工单集成页面更新完成！")
    print("\n更新内容：")
    print("1. ✓ 将工单完整内容整合到工单详情对话框")
    print("2. ✓ 移除了独立的工单完整内容对话框")
    print("3. ✓ 添加了Excel导出功能")
    print("   - 单个工单详情导出")
    print("   - 项目工单列表导出")
    print("4. ✓ 修复了对话框变量冲突")
    print("5. ✓ 添加了必要的导入（XLSX.js, file-saver）")
    
    print("\n前端访问地址：")
    print("- http://localhost:3001")
    
    print("\n使用说明：")
    print("1. 在首页点击项目卡片，打开项目工单列表")
    print("2. 点击工单行，直接查看工单详情（包含完整内容）")
    print("3. 在工单详情对话框中点击'导出Excel'导出单个工单")
    print("4. 在项目工单列表中点击'导出Excel'导出整个列表")
    
    # 尝试测试API
    test_with_auth()
    
    print("\n🎉 更新完成！请在浏览器中测试功能。")

if __name__ == "__main__":
    main()
