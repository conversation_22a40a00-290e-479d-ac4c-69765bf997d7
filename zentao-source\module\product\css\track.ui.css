.switchBtn {background: transparent; box-shadow: none;}
.orderByIcon.with-popover-show{color: var(--color-primary-500);}

.card {min-height:80px;}
.card .card-heading{color:var(--color-gray-900)}
.card .card-heading a{color:var(--color-gray-900)}
.card .card-title{height:40px; width:100%;}
.card .card-content{flex-direction: row;justify-content: space-between;color:var(--color-gray-800)}

.kanban-item.parentTask { padding-bottom: 1px;}
.kanban-item.childTask { padding-top:0px; padding-bottom: 1px;}
.kanban-item.childTask .card {background-color: #f9f9f9;}

.kanban-header-title .as-title{font-size:14px;}
.kanban-header-lane-name, .kanban-lane-name {display:none;}
.kanban-lane-cols, .kanban-header-cols, .kanban-header-sub-cols { gap: calc(var(--kanban-cols-gap) / 2); }
.lane-col-shrink-with-above { position: relative; }
.lane-col-shrink-with-above:before {
    background-color: inherit;
    left: 0;
    position: absolute;
    right: 0;
    --tw-content: "";
    content: var(--tw-content);
    height: calc(var(--kanban-col-gap-top, var(--kanban-lanes-gap)) - 2px);
    top: calc(2px - var(--kanban-col-gap-top, var(--kanban-lanes-gap)));
}

.kanban-header-col-wrapper{
  --tw-bg-opacity-60: 0.6;
  background-color: rgba(var(--color-gray-100-rgb), var(--tw-bg-opacity-60));
}
.kanban-lane-col{background-color: rgba(var(--color-gray-100-rgb), var(--tw-bg-opacity));}
