body{padding:0px; margin: 0px;background-color: #fff;}
#header{display: none}
#footer{display: none}
#header+#main{min-width:auto}
#main{padding: 0px; top:0!important;}
#main .container{padding: 0px;}
#mainMenu{margin-top:0px;background-color: #efefef;}
#mainContent .cell{box-shadow:none; -webkit-box-shadow: none;}

.xuanxuan-card{padding-bottom:55px;}
.xuancard-actions{width: 100%;text-align: center; height: 35px; padding: 1px;}
.xuancard-actions.fixed{position: fixed; background: #fff; border-top: 1px solid #eee; bottom: 0; margin-bottom: 0; z-index: 999;}
