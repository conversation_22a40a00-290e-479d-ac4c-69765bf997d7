#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据库表问题
"""

import pymysql

def debug_tables():
    """调试数据库表"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        print("✅ 数据库连接成功")
        
        with connection.cursor() as cursor:
            # 显示所有表
            print("\n📋 数据库中的所有表:")
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            for table in tables:
                print(f"   📝 {table[0]}")
            
            # 检查督办相关表
            print(f"\n🔍 检查督办相关表:")
            supervision_tables = ['supervision_items', 'companies', 'company_progress']
            
            for table in supervision_tables:
                cursor.execute(f"SHOW TABLES LIKE '{table}'")
                result = cursor.fetchone()
                if result:
                    print(f"✅ {table} 存在")
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"   📊 数据量: {count}")
                    
                    # 显示表结构
                    cursor.execute(f"DESCRIBE {table}")
                    columns = cursor.fetchall()
                    print(f"   📋 表结构:")
                    for col in columns:
                        print(f"      {col[0]} ({col[1]})")
                else:
                    print(f"❌ {table} 不存在")
                    
                    # 尝试创建表
                    if table == 'company_progress':
                        print(f"🔧 尝试创建 {table} 表...")
                        try:
                            cursor.execute("""
                                CREATE TABLE company_progress (
                                    id INT AUTO_INCREMENT PRIMARY KEY,
                                    supervision_item_id INT NOT NULL,
                                    company_id INT NOT NULL,
                                    status VARCHAR(50) NOT NULL DEFAULT '未开始',
                                    progress_description TEXT,
                                    existing_problems TEXT,
                                    next_plan TEXT,
                                    updated_by VARCHAR(50),
                                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                    UNIQUE KEY uk_item_company (supervision_item_id, company_id)
                                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                            """)
                            connection.commit()
                            print(f"✅ {table} 表创建成功")
                            
                            # 插入测试数据
                            cursor.execute("SELECT id FROM supervision_items LIMIT 1")
                            item = cursor.fetchone()
                            cursor.execute("SELECT id FROM companies LIMIT 1")
                            company = cursor.fetchone()
                            
                            if item and company:
                                cursor.execute("""
                                    INSERT INTO company_progress (supervision_item_id, company_id, status)
                                    VALUES (%s, %s, '未开始')
                                """, (item[0], company[0]))
                                connection.commit()
                                print("✅ 插入测试数据成功")
                                
                        except Exception as e:
                            print(f"❌ 创建 {table} 表失败: {str(e)}")
            
            # 测试查询
            print(f"\n🧪 测试督办管理查询:")
            try:
                cursor.execute("""
                    SELECT cp.supervision_item_id, c.company_code, cp.status
                    FROM company_progress cp
                    JOIN companies c ON cp.company_id = c.id
                    WHERE c.is_active = TRUE
                    LIMIT 5
                """)
                results = cursor.fetchall()
                print(f"✅ 查询成功，返回 {len(results)} 条记录")
                for result in results:
                    print(f"   📊 督办事项ID: {result[0]}, 公司: {result[1]}, 状态: {result[2]}")
            except Exception as e:
                print(f"❌ 查询失败: {str(e)}")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_tables()
