---
title: zt_task
author: qixinzhi
version: "1.0"
fields:
- field: id
  range: 1-10000
- field: execution
  range: 11-30{12}
- field: closedDate
  range: "20110101 000000-20210101 000000:10D"
  type: timestamp
  format: YYYY-MM-DD
- field: openedDate
  range: "20100101 000000-20210101 230000:10D"
  type: timestamp
  format: YYYY-MM-DD hh:mm:ss
- field: finishedDate
  range: "20100101 000000-20210101 230000:10D"
  type: timestamp
  format: YYYY-MM-DD hh:mm:ss
- field: deadline
  range: "20100201 000000-20210201 230000:10D"
  type: timestamp
  format: YYYY-MM-DD
- field: estimate
  range: 1{10},2{9},4{8},5{7},6{5},8{4},10{1},
- field: consumed
  range: 1{10},2{9},4{8},5{7},6{5},8{4},10{1},
- field: left
  range: 1{10},2{9},4{8},5{7},6{5},8{4},10{1},
- field: status
  range: wait{1},doing{1},done{1},pause{1},cancel{1},closed{1}
- field: type
  range: design{2},devel{10},request{1},test{2},study{1},discuss{1}
- field: assignedTo
  range: admin,user{5},test{3},dev{10},pm{10},po{8},closed{20},[]{40}
- field: deleted
  range: 0{6},1{6}
...
