{% extends "base.html" %}

{% block content %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>表格数据 - {{ table_name }}</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .table-responsive {
            overflow-x: auto;
        }
        .action-buttons {
            margin-bottom: 20px;
        }
        .action-buttons button {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <!-- 隐藏的数据元素，用于存储列名 -->
    <script type="application/json" id="column-names-data">
        {{ column_names | tojson }}
    </script>

    <div class="container mt-4">
        <h2>{{ db_name }} > {{ table_name }}</h2>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    {% if category == 'error' %}
                        <div class="alert alert-danger">
                            {{ message }}
                        </div>
                    {% elif category == 'warning' %}
                        <div class="alert alert-warning">
                            {{ message }}
                        </div>
                    {% elif category == 'success' %}
                        <div class="alert alert-success">
                            {{ message }}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            {{ message }}
                        </div>
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="mb-3">
            <a href="/database/{{ db_name }}" class="btn btn-secondary">返回数据库</a>
            <a href="/manage_fields/{{ db_name }}/{{ table_name }}" class="btn btn-primary">管理字段</a>
            <a href="/export_table_content/{{ db_name }}/{{ table_name }}" class="btn btn-success">导出为Excel</a>
            <a href="/delete_table/{{ db_name }}/{{ table_name }}" class="btn btn-danger" onclick="return confirm('确定要删除该表吗？')">删除表</a>
        </div>
        
        <!-- 索引信息 -->
        {% if indexes %}
        <div class="card mb-3">
            <div class="card-header">索引信息</div>
            <div class="card-body">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>索引名称</th>
                            <th>字段</th>
                            <th>类型</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for index in indexes %}
                        <tr>
                            <td>{{ index.name }}</td>
                            <td>{{ index.columns }}</td>
                            <td>{{ index.type }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
        
        <!-- 批量操作按钮 -->
        <div class="action-buttons">
            <form id="batch-form" action="/batch_delete_records/{{ db_name }}/{{ table_name }}" method="post">
                <button type="button" class="btn btn-danger" id="batch-delete-btn" disabled>批量删除</button>
                <button type="button" class="btn btn-secondary" id="select-all">全选</button>
                <button type="button" class="btn btn-secondary" id="deselect-all">取消全选</button>
                <span id="selected-count" class="ms-3">已选择: 0 条记录</span>
            </form>
        </div>
        
        <!-- 表格数据 -->
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead class="table-dark">
                    <tr>
                        <th><input type="checkbox" id="select-all-checkbox"></th>
                        {% for col in column_names %}
                        <th>{{ col }}</th>
                        {% endfor %}
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in rows %}
                    <tr>
                        <td>
                            <input type="checkbox" class="record-checkbox" name="record_ids" value="{{ row[column_names[0]] }}" form="batch-form">
                        </td>
                        {% for col in column_names %}
                        <td>{{ row[col] }}</td>
                        {% endfor %}
                        <td>
                            <button type="button" class="btn btn-sm btn-primary edit-btn" data-bs-toggle="modal" data-bs-target="#editModal" 
                                data-record-id="{{ row[column_names[0]] }}"
                                {% for col in column_names %}
                                data-{{ col }}="{{ row[col] }}"
                                {% endfor %}>
                                编辑
                            </button>
                            <form action="/delete_record/{{ db_name }}/{{ table_name }}" method="post" style="display:inline;">
                                <input type="hidden" name="record_id" value="{{ row[column_names[0]] }}">
                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('确定要删除这条记录吗？')">删除</button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 添加记录按钮 -->
        <button type="button" class="btn btn-success mt-3" data-bs-toggle="modal" data-bs-target="#addModal">
            添加记录
        </button>
        
        <!-- 添加记录模态框 -->
        <div class="modal fade" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="addModalLabel">添加记录</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form action="/add_record/{{ db_name }}/{{ table_name }}" method="post">
                            {% for col in column_names %}
                            <div class="mb-3">
                                <label for="add_{{ col }}" class="form-label">{{ col }}</label>
                                <input type="text" class="form-control" id="add_{{ col }}" name="{{ col }}">
                            </div>
                            {% endfor %}
                            <button type="submit" class="btn btn-primary">保存</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 编辑记录模态框 -->
        <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editModalLabel">编辑记录</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form action="/update_record/{{ db_name }}/{{ table_name }}" method="post" id="edit-form">
                            <input type="hidden" id="edit_record_id" name="record_id">
                            {% for col in column_names %}
                            <div class="mb-3">
                                <label for="edit_{{ col }}" class="form-label">{{ col }}</label>
                                <input type="text" class="form-control" id="edit_{{ col }}" name="{{ col }}">
                            </div>
                            {% endfor %}
                            <button type="submit" class="btn btn-primary">保存修改</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script>
        // 编辑按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            // 将列名存储在一个数据属性中
            const columnNamesData = document.getElementById('column-names-data');
            const columnNames = columnNamesData ? JSON.parse(columnNamesData.textContent) : [];
            
            const editButtons = document.querySelectorAll('.edit-btn');
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const recordId = this.getAttribute('data-record-id');
                    document.getElementById('edit_record_id').value = recordId;
                    
                    // 为每个列设置值
                    columnNames.forEach(col => {
                        const inputId = 'edit_' + col;
                        const dataAttr = 'data-' + col;
                        const value = this.getAttribute(dataAttr);
                        if (document.getElementById(inputId)) {
                            document.getElementById(inputId).value = value;
                        }
                    });
                });
            });
            
            // 全选/取消全选功能
            const selectAllCheckbox = document.getElementById('select-all-checkbox');
            const recordCheckboxes = document.querySelectorAll('.record-checkbox');
            const batchDeleteBtn = document.getElementById('batch-delete-btn');
            const selectedCountSpan = document.getElementById('selected-count');
            const selectAllBtn = document.getElementById('select-all');
            const deselectAllBtn = document.getElementById('deselect-all');
            const batchForm = document.getElementById('batch-form');
            
            // 更新选中计数和删除按钮状态
            function updateSelectedCount() {
                const selectedCount = document.querySelectorAll('.record-checkbox:checked').length;
                selectedCountSpan.textContent = `已选择: ${selectedCount} 条记录`;
                batchDeleteBtn.disabled = selectedCount === 0;
            }
            
            // 复选框变化事件
            recordCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateSelectedCount();
                    
                    // 检查是否所有复选框都被选中
                    const allChecked = Array.from(recordCheckboxes).every(cb => cb.checked);
                    selectAllCheckbox.checked = allChecked;
                });
            });
            
            // 全选复选框
            selectAllCheckbox.addEventListener('change', function() {
                recordCheckboxes.forEach(cb => {
                    cb.checked = this.checked;
                });
                updateSelectedCount();
            });
            
            // 全选按钮
            selectAllBtn.addEventListener('click', function() {
                recordCheckboxes.forEach(cb => {
                    cb.checked = true;
                });
                selectAllCheckbox.checked = true;
                updateSelectedCount();
            });
            
            // 取消全选按钮
            deselectAllBtn.addEventListener('click', function() {
                recordCheckboxes.forEach(cb => {
                    cb.checked = false;
                });
                selectAllCheckbox.checked = false;
                updateSelectedCount();
            });
            
            // 批量删除按钮
            batchDeleteBtn.addEventListener('click', function() {
                const selectedCount = document.querySelectorAll('.record-checkbox:checked').length;
                if (selectedCount > 0) {
                    if (confirm(`确定要删除选中的 ${selectedCount} 条记录吗？`)) {
                        batchForm.submit();
                    }
                }
            });
            
            // 初始化
            updateSelectedCount();
        });
    </script>
</body>
</html>
{% endblock %} 