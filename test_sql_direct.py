#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试SQL查询
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'pmo-backend'))

from app.core.database import TicketSystemConnector

def test_sql_query():
    """直接测试SQL查询"""
    print("🔍 直接测试SQL查询")
    print("=" * 60)
    
    connector = TicketSystemConnector()
    
    try:
        # 测试按状态获取工单的SQL
        test_sql = """
        SELECT
            t.feelec_ticket_id,
            t.feelec_ticket_no,
            t.feelec_title,
            t.feelec_project_id,
            s.feelec_name as status_name,
            u1.feelec_name as publisher_name,
            u2.feelec_name as processor_name,
            d.feelec_name as department_name,
            c.feelec_name as company_name,
            tt.feelec_name as template_name,
            p.feelec_name as project_name
        FROM feelec_ticket t
        LEFT JOIN feelec_ticket_status s ON t.feelec_status_id = s.feelec_status_id AND s.delete_time = 0
        LEFT JOIN feelec_user u1 ON t.feelec_publisher_id = u1.feelec_user_id AND u1.delete_time = 0
        LEFT JOIN feelec_user u2 ON t.feelec_processor_id = u2.feelec_user_id AND u2.delete_time = 0
        LEFT JOIN feelec_member_department d ON t.feelec_department_id = d.feelec_department_id AND d.delete_time = 0
        LEFT JOIN feelec_company c ON t.feelec_company_id = c.feelec_company_id AND c.delete_time = 0
        LEFT JOIN feelec_ticket_template tt ON t.feelec_template_id = tt.feelec_template_id
        LEFT JOIN feelec_project p ON t.feelec_project_id = p.feelec_project_id AND p.feelec_delete = 20
        WHERE t.feelec_delete = 20
        ORDER BY t.create_time DESC
        LIMIT 5
        """
        
        print("📋 执行测试SQL查询...")
        results = connector.execute_query(test_sql)
        
        if results:
            print(f"✅ 查询成功，返回 {len(results)} 条记录")
            
            for i, result in enumerate(results):
                print(f"\n📄 工单 {i+1}:")
                print(f"  工单ID: {result['feelec_ticket_id']}")
                print(f"  工单编号: {result['feelec_ticket_no']}")
                print(f"  工单标题: {result['feelec_title'][:30]}...")
                print(f"  项目ID: {result['feelec_project_id']} {'✅' if result['feelec_project_id'] else '❌'}")
                print(f"  项目名称: {result['project_name']} {'✅' if result['project_name'] else '❌'}")
                print(f"  状态: {result['status_name']}")
                print(f"  发布人: {result['publisher_name']}")
                print(f"  处理人: {result['processor_name']}")
                print(f"  部门: {result['department_name']}")
                print(f"  公司: {result['company_name']}")
                print(f"  模板: {result['template_name']}")
        else:
            print("❌ 查询返回空结果")
        
        # 测试特定的项目关联查询
        print(f"\n" + "=" * 60)
        print("🔗 测试项目关联查询")
        print("=" * 60)
        
        project_test_sql = """
        SELECT 
            t.feelec_ticket_id,
            t.feelec_ticket_no,
            t.feelec_project_id,
            p.feelec_project_id as p_project_id,
            p.feelec_name as project_name,
            p.feelec_delete as project_delete_status
        FROM feelec_ticket t
        LEFT JOIN feelec_project p ON t.feelec_project_id = p.feelec_project_id
        WHERE t.feelec_delete = 20 
        AND t.feelec_project_id IS NOT NULL 
        AND t.feelec_project_id != 0
        LIMIT 10
        """
        
        project_results = connector.execute_query(project_test_sql)
        
        if project_results:
            print(f"✅ 项目关联查询成功，返回 {len(project_results)} 条记录")
            
            active_projects = 0
            deleted_projects = 0
            
            for result in project_results:
                project_status = "正常" if result['project_delete_status'] == 20 else f"已删除({result['project_delete_status']})"
                if result['project_delete_status'] == 20:
                    active_projects += 1
                else:
                    deleted_projects += 1
                    
                print(f"  工单: {result['feelec_ticket_no']}")
                print(f"    工单项目ID: {result['feelec_project_id']}")
                print(f"    项目表项目ID: {result['p_project_id']}")
                print(f"    项目名称: {result['project_name'] or '❌ NULL'}")
                print(f"    项目状态: {project_status}")
                print("    ---")
            
            print(f"\n📊 项目状态统计:")
            print(f"  正常项目: {active_projects}")
            print(f"  已删除项目: {deleted_projects}")
            
            if deleted_projects > 0:
                print(f"⚠️  发现 {deleted_projects} 个工单关联的项目已被删除！")
                print("💡 这就是为什么项目名称显示为空的原因")
        else:
            print("❌ 项目关联查询返回空结果")
        
        # 测试修正后的查询（只关联正常项目）
        print(f"\n" + "=" * 60)
        print("✅ 测试修正后的查询（只关联正常项目）")
        print("=" * 60)
        
        fixed_sql = """
        SELECT 
            t.feelec_ticket_id,
            t.feelec_ticket_no,
            t.feelec_project_id,
            p.feelec_name as project_name
        FROM feelec_ticket t
        LEFT JOIN feelec_project p ON t.feelec_project_id = p.feelec_project_id AND p.feelec_delete = 20
        WHERE t.feelec_delete = 20 
        AND t.feelec_project_id IS NOT NULL 
        AND t.feelec_project_id != 0
        LIMIT 10
        """
        
        fixed_results = connector.execute_query(fixed_sql)
        
        if fixed_results:
            print(f"✅ 修正查询成功，返回 {len(fixed_results)} 条记录")
            
            has_project_name = 0
            no_project_name = 0
            
            for result in fixed_results:
                if result['project_name']:
                    has_project_name += 1
                    status = "✅"
                else:
                    no_project_name += 1
                    status = "❌"
                    
                print(f"  {status} 工单: {result['feelec_ticket_no']}")
                print(f"    项目ID: {result['feelec_project_id']}")
                print(f"    项目名称: {result['project_name'] or 'NULL'}")
            
            print(f"\n📊 修正后统计:")
            print(f"  有项目名称: {has_project_name}")
            print(f"  无项目名称: {no_project_name}")
            
            if has_project_name > 0:
                print("🎉 修正成功！现在可以正确获取项目名称了")
            else:
                print("⚠️  仍然无法获取项目名称，可能是数据问题")
        else:
            print("❌ 修正查询返回空结果")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        connector.close()

def main():
    """主函数"""
    print("🚀 开始直接测试SQL查询")
    test_sql_query()

if __name__ == "__main__":
    main()
