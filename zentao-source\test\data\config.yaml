title: table zt_config
desc: "系统配置"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field:  vision
    note: "界面"
    range: 'rnd'
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: owner
    note: "所有者"
    range: system
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: module
    note: "模块"
    range: common
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: section
    note: "附加部分"
    range: global
  - field: key
    note: "键"
    range: hourPoint,CRProduct,CRExecution,URSR,mode,version,sn
  - field: value
    note: "值"
    range: 0,1,1,2,ALM,10.0,f205720305272543052e3d689afdb5b8
