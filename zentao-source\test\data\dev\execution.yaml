title: table zt_execution
author: <PERSON>
version: "1.0"
fields:
  - field: id
    range: 20001-50000
  - field: name
    note: "名称"
    fields:
    - field: name1
      range: 迭代{9000},阶段{5000},看板{5000}
    - field: name2
      range: 1-100000
  - field: project
    range: 1001-20000
  - field: model
    range: []
  - field: type
    range: sprint{9000},stage{5000},kanban{5000}
  - field: budget
    range: 800000-1:100
  - field: status
    range: wait,doing
  - field: percent
    range: 0{30},10{30},0{30}
  - field: milestone
    range: 0{30},1{10},0{10},1{10},0{30}
  - field: auth
    range: "extend"
  - field: desc
    range: 1-100000
    prefix: "迭代描述"
  - field: begin
    range: "(-2M)-(+M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: end
    range: "(+1w)-(+2M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: realBegan
    range: "(-2M)-(+M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: realEnd
    range: "(+1w)-(+2M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: grade
    range: 1
  - field: parent
    range: 1001-10000,10001-15000,15001-20000
  - field: path
    fields:
      - field: path1
        prefix: ","
        range: 1001-20000
      - field: path2
        prefix: ","
        range: 20001-50000
        postfix: ","
  - field: acl
    range: open{4},private{4}
  - field: openedVersion
    range: "16.5"
  - field: whitelist
    froms:
      - from: common.user.v1.yaml
        use: empty{8}
      - from: common.user.v1.yaml
        use: empty{8}
        prefix: ","
      - from: common.user.v1.yaml
        use: one{8}
        prefix: ","
      - from: common.user.v1.yaml
        use: two{8}
        prefix: ","
      - from: common.user.v1.yaml
        use: three{8}
        prefix: ","
