import mysql.connector

# 数据库配置
config = {
    'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'cyh',
    'password': 'Qq188788',
    'database': 'kanban'
}

try:
    # 建立连接
    conn = mysql.connector.connect(**config)
    cursor = conn.cursor()
    
    # 1. 删除重复的索引
    print("正在删除重复的索引...")
    try:
        cursor.execute("ALTER TABLE weekly_reports DROP INDEX idx_create_time")
        print("成功删除重复索引 idx_create_time")
    except Exception as e:
        print(f"删除索引时出错: {str(e)}")
    
    # 2. 修改id为自增主键
    print("\n正在设置id为自增主键...")
    try:
        # 先删除主键
        cursor.execute("ALTER TABLE weekly_reports DROP PRIMARY KEY")
        # 然后重新设置为自增主键
        cursor.execute("ALTER TABLE weekly_reports MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY")
        print("成功将id设置为自增主键")
    except Exception as e:
        print(f"设置自增主键时出错: {str(e)}")
    
    # 提交更改
    conn.commit()
    print("\n所有操作已完成！")
    
    # 显示当前的索引信息
    print("\n当前的索引信息：")
    cursor.execute("""
        SELECT 
            index_name,
            GROUP_CONCAT(column_name ORDER BY seq_in_index) as columns,
            index_type,
            non_unique
        FROM information_schema.statistics
        WHERE table_schema = 'kanban'
        AND table_name = 'weekly_reports'
        GROUP BY index_name, index_type, non_unique;
    """)
    indexes = cursor.fetchall()
    for idx in indexes:
        unique_str = "唯一索引" if idx[3] == 0 else "非唯一索引"
        print(f"\n索引名称: {idx[0]}")
        print(f"包含列: {idx[1]}")
        print(f"索引类型: {idx[2]}")
        print(f"索引属性: {unique_str}")
    
except Exception as e:
    print(f"发生错误: {str(e)}")
finally:
    if 'cursor' in locals():
        cursor.close()
    if 'conn' in locals():
        conn.close() 