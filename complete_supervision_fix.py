#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的督办管理修复和测试脚本
"""

import pymysql
import requests
import json
import time

def step1_check_database():
    """步骤1：检查数据库连接和表结构"""
    print("🔍 步骤1：检查数据库连接和表结构")
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        print("✅ 数据库连接成功")
        
        with connection.cursor() as cursor:
            # 检查supervision_items表是否存在
            cursor.execute("SHOW TABLES LIKE 'supervision_items'")
            if cursor.fetchone():
                print("✅ supervision_items 表存在")
                
                # 检查表结构
                cursor.execute("DESCRIBE supervision_items")
                columns = cursor.fetchall()
                column_names = [col[0] for col in columns]
                
                required_fields = ['is_annual_assessment', 'progress_description', 'overall_progress']
                missing_fields = [field for field in required_fields if field not in column_names]
                
                if missing_fields:
                    print(f"❌ 缺少字段: {missing_fields}")
                    return False
                else:
                    print("✅ 所有必需字段都存在")
                    return True
            else:
                print("❌ supervision_items 表不存在")
                return False
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {str(e)}")
        return False

def step2_recreate_tables():
    """步骤2：重新创建表"""
    print("\n🔧 步骤2：重新创建督办管理表")
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            print("🗑️  删除旧表...")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            cursor.execute("DROP TABLE IF EXISTS supervision_status_history")
            cursor.execute("DROP TABLE IF EXISTS company_supervision_status")
            cursor.execute("DROP TABLE IF EXISTS supervision_items")
            cursor.execute("DROP TABLE IF EXISTS companies")
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
            
            print("✅ 旧表删除完成")
            
            # 创建督办事项表
            cursor.execute("""
                CREATE TABLE supervision_items (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    sequence_number INT NOT NULL COMMENT '序号',
                    work_dimension VARCHAR(100) NOT NULL COMMENT '工作维度',
                    work_theme VARCHAR(200) NOT NULL COMMENT '工作主题',
                    supervision_source VARCHAR(100) NOT NULL COMMENT '督办来源',
                    work_content TEXT NOT NULL COMMENT '工作内容和完成标志',
                    is_annual_assessment ENUM('是', '否') DEFAULT '否' COMMENT '是否年度绩效考核指标',
                    completion_deadline VARCHAR(50) NOT NULL COMMENT '完成时限',
                    progress_description TEXT COMMENT '7月28日进度情况',
                    overall_progress ENUM('X 未启动', 'O进行中', '！逾期', '√ 已完成', '— 不需要执行') DEFAULT 'X 未启动' COMMENT '整体进度',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='督办事项表'
            """)
            print("✅ supervision_items 表创建成功")
            
            # 创建公司表
            cursor.execute("""
                CREATE TABLE companies (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    company_code VARCHAR(50) NOT NULL UNIQUE COMMENT '公司代码',
                    company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
                    display_order INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
                    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司表'
            """)
            print("✅ companies 表创建成功")
            
            # 创建公司督办状态表
            cursor.execute("""
                CREATE TABLE company_supervision_status (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    supervision_item_id INT NOT NULL COMMENT '督办事项ID',
                    company_id INT NOT NULL COMMENT '公司ID',
                    status ENUM('√', 'O', '！', 'X', '—') DEFAULT 'X' COMMENT '状态：√已完成 O进行中 ！逾期 X未启动 —不需要执行',
                    progress_description TEXT COMMENT '当前进展情况',
                    existing_problems TEXT COMMENT '存在问题',
                    next_plan TEXT COMMENT '下一步计划',
                    updated_by VARCHAR(50) COMMENT '更新人',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY uk_item_company (supervision_item_id, company_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司督办状态表'
            """)
            print("✅ company_supervision_status 表创建成功")
            
            # 创建督办状态变更历史表
            cursor.execute("""
                CREATE TABLE supervision_status_history (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    supervision_item_id INT NOT NULL COMMENT '督办事项ID',
                    company_id INT NOT NULL COMMENT '公司ID',
                    old_status ENUM('√', 'O', '！', 'X', '—') COMMENT '原状态',
                    new_status ENUM('√', 'O', '！', 'X', '—') COMMENT '新状态',
                    old_progress_description TEXT COMMENT '原进展情况',
                    new_progress_description TEXT COMMENT '新进展情况',
                    old_existing_problems TEXT COMMENT '原存在问题',
                    new_existing_problems TEXT COMMENT '新存在问题',
                    old_next_plan TEXT COMMENT '原下一步计划',
                    new_next_plan TEXT COMMENT '新下一步计划',
                    change_reason VARCHAR(500) COMMENT '变更原因',
                    updated_by VARCHAR(50) COMMENT '更新人',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='督办状态变更历史表'
            """)
            print("✅ supervision_status_history 表创建成功")
            
            connection.commit()
            print("✅ 所有表创建完成")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 表创建失败: {str(e)}")
        return False

def step3_insert_data():
    """步骤3：插入测试数据"""
    print("\n📊 步骤3：插入测试数据")
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 插入公司数据
            companies_data = [
                ('CXBX', '财险', 1),
                ('SXBX', '寿险', 2),
                ('JINZU', '金租', 3),
                ('ZICHAN', '资管', 4),
                ('GUANGZU', '广租', 5),
                ('TONGSHENG', '通盛', 6),
                ('DANBAO', '担保', 7),
                ('XIAODAI', '小贷', 8),
                ('BAOLI', '保理', 9),
                ('BUDONGCHAN', '不动产', 10),
                ('ZHENGXIN', '征信', 11),
                ('JINFU', '金服', 12),
                ('BENBU', '本部', 13)
            ]
            
            for company_code, company_name, display_order in companies_data:
                cursor.execute("""
                    INSERT INTO companies (company_code, company_name, display_order)
                    VALUES (%s, %s, %s)
                """, (company_code, company_name, display_order))
            
            print("✅ 公司数据插入成功")
            
            # 插入督办事项数据
            items_data = [
                (1, '一、监管和制度', '建立条线ITBP团队管理办法', '3月科技例会', 
                 '各单位及部门明确对接人，以条线形式建立ITBP服务团队。', 
                 '否', '5月末', '各部门及子公司已制定ITBP对接人。', '√ 已完成'),
                (2, '一、监管和制度', '建立项目红绿灯管理办法', '3月科技例会',
                 '开展重点项目周跟踪机制，推行"亮黄牌"机制。',
                 '否', '4月末', '已建立公共台账实时反应项目红绿灯。', '√ 已完成'),
                (3, '一、监管和制度', '印发8个信息化管理制度', '5月科技例会',
                 '各子公司参照集团印发的信息化管理制度。',
                 '否', '8月末', '本部制度印发已较为完整。', 'O进行中'),
                (4, '二、数据治理和系统覆盖', '第一批次数据治理', '3月科技例会',
                 '持续开展数据治理工作，准确率、完整率、及时率达到要求。',
                 '是', '4月末', '已完成第一批次数据治理。', '√ 已完成'),
                (5, '二、数据治理和系统覆盖', '第二批次数据治理', '5月科技例会',
                 '涉及经营、风控、人力的152项数据治理。',
                 '是', '11月末', '正在进行中。', 'O进行中')
            ]
            
            for item_data in items_data:
                cursor.execute("""
                    INSERT INTO supervision_items 
                    (sequence_number, work_dimension, work_theme, supervision_source, work_content, 
                     is_annual_assessment, completion_deadline, progress_description, overall_progress)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, item_data)
            
            print("✅ 督办事项数据插入成功")
            
            # 为每个督办事项创建公司状态记录
            cursor.execute("SELECT id FROM supervision_items")
            items = cursor.fetchall()
            
            cursor.execute("SELECT id FROM companies WHERE is_active = 1")
            companies = cursor.fetchall()
            
            for item in items:
                for company in companies:
                    cursor.execute("""
                        INSERT INTO company_supervision_status (supervision_item_id, company_id, status)
                        VALUES (%s, %s, 'X')
                    """, (item[0], company[0]))
            
            print("✅ 公司状态记录创建成功")
            
            connection.commit()
            
            # 验证数据
            cursor.execute("SELECT COUNT(*) FROM supervision_items")
            items_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM companies")
            companies_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM company_supervision_status")
            status_count = cursor.fetchone()[0]
            
            print(f"📊 数据统计:")
            print(f"   督办事项: {items_count} 条")
            print(f"   公司: {companies_count} 个")
            print(f"   状态记录: {status_count} 条")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据插入失败: {str(e)}")
        return False

def step4_test_database_query():
    """步骤4：测试数据库查询"""
    print("\n🧪 步骤4：测试数据库查询")
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 测试API中使用的查询
            cursor.execute("""
                SELECT id, sequence_number, work_dimension, work_theme, supervision_source,
                       work_content, is_annual_assessment, completion_deadline, 
                       progress_description, overall_progress
                FROM supervision_items 
                ORDER BY sequence_number
            """)
            items = cursor.fetchall()
            
            print(f"✅ 督办事项查询成功，返回 {len(items)} 条记录")
            
            # 测试公司查询
            cursor.execute("""
                SELECT id, company_code, company_name 
                FROM companies 
                WHERE is_active = TRUE 
                ORDER BY display_order
            """)
            companies = cursor.fetchall()
            
            print(f"✅ 公司查询成功，返回 {len(companies)} 条记录")
            
            # 测试状态查询
            cursor.execute("""
                SELECT css.supervision_item_id, c.company_code, css.status
                FROM company_supervision_status css
                JOIN companies c ON css.company_id = c.id
                WHERE c.is_active = TRUE
                LIMIT 5
            """)
            statuses = cursor.fetchall()
            
            print(f"✅ 状态查询成功，返回 {len(statuses)} 条记录")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库查询测试失败: {str(e)}")
        return False

def step5_restart_backend():
    """步骤5：重启后端服务"""
    print("\n🔄 步骤5：重启后端服务")
    print("⚠️  请手动重启后端服务，然后按回车继续...")
    input("按回车键继续...")
    return True

def step6_test_api():
    """步骤6：测试API"""
    print("\n🌐 步骤6：测试API")
    try:
        base_url = "http://localhost:8000/api/v1"
        
        # 登录获取token
        print("🔐 登录获取token...")
        login_response = requests.post(f"{base_url}/auth/login", json={
            "username": "admin",
            "password": "admin123"
        })
        
        if login_response.status_code == 200:
            token = login_response.json().get('access_token')
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        # 测试新督办管理API
        print("📋 测试新督办管理API...")
        response = requests.get(f"{base_url}/new-supervision/items", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            print(f"   督办事项数量: {len(data.get('data', []))}")
            print(f"   公司数量: {len(data.get('companies', []))}")
            return True
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 督办管理完整修复和测试")
    print("=" * 60)
    
    # 步骤1：检查数据库
    if not step1_check_database():
        print("\n需要重建表...")
        
        # 步骤2：重新创建表
        if not step2_recreate_tables():
            print("❌ 表创建失败，退出")
            return
        
        # 步骤3：插入数据
        if not step3_insert_data():
            print("❌ 数据插入失败，退出")
            return
    else:
        print("✅ 数据库表结构正确")
    
    # 步骤4：测试数据库查询
    if not step4_test_database_query():
        print("❌ 数据库查询测试失败，退出")
        return
    
    # 步骤5：重启后端服务
    step5_restart_backend()
    
    # 步骤6：测试API
    if step6_test_api():
        print("\n🎉 督办管理功能修复完成！")
        print("✅ 数据库表结构正确")
        print("✅ 测试数据完整")
        print("✅ API调用成功")
        print("\n🌐 现在可以访问: http://localhost:3000/new-supervision")
    else:
        print("\n❌ API测试失败，请检查后端服务")
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
