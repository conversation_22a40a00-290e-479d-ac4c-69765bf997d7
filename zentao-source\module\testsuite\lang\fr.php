<?php
/**
 * The testsuite module en file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     testsuite
 * @version     $Id: zh-cn.php 4490 2013-02-27 03:27:05Z <EMAIL> $
 * @link        https://www.zentao.net
 */
$lang->testsuite->create           = "Créer un Cahier";
$lang->testsuite->delete           = "Supprimer un Cahier";
$lang->testsuite->view             = "Détail du Cahier";
$lang->testsuite->edit             = "Editer Cahier";
$lang->testsuite->browse           = "Liste des Cahiers de Recette";
$lang->testsuite->linkCase         = "Incorporer CasTest";
$lang->testsuite->linkVersion      = "Version";
$lang->testsuite->unlinkCase       = "Retirer";
$lang->testsuite->unlinkCaseAction = "Retirer CasTest";
$lang->testsuite->batchUnlinkCases = "Retirer par Lot";
$lang->testsuite->deleted          = 'Supprimé';
$lang->testsuite->successSaved     = 'Sauvé';

$lang->testsuite->id             = 'ID';
$lang->testsuite->pri            = 'Priority';
$lang->testsuite->common         = 'Cahier de Recette';
$lang->testsuite->project        = $lang->projectCommon;
$lang->testsuite->product        = $lang->productCommon;
$lang->testsuite->name           = 'Suite Nom';
$lang->testsuite->type           = 'Type';
$lang->testsuite->desc           = 'Description';
$lang->testsuite->mailto         = 'Mailto';
$lang->testsuite->author         = "Contrôle d'Accès";
$lang->testsuite->addedBy        = 'Créé par';
$lang->testsuite->addedDate      = 'Créé le';
$lang->testsuite->addedTime      = 'Create Time';
$lang->testsuite->lastEditedBy   = 'LastEditedBy';
$lang->testsuite->lastEditedDate = 'LastEditedDate';

$lang->testsuite->legendDesc      = 'Description';
$lang->testsuite->legendBasicInfo = 'Infos de Base';

$lang->testsuite->unlinkedCases = 'CasTests non incorporés';

$lang->testsuite->confirmDelete     = 'Voulez-vous supprimer ce cahier de recette ?';
$lang->testsuite->confirmUnlinkCase = 'Voulez-vous retirer ce CasTest ?';
$lang->testsuite->noticeNone        = "Vous n'avez pas encore créé de Cahier de Recette pour l'instant.";
$lang->testsuite->noModule          = '<div>Vous avez aucun Module.</div><div>Gérer les Modules maintenant.</div>';
$lang->testsuite->noTestsuite       = 'Aucun Cahier de Recette.';
$lang->testsuite->summary           = "Total suites: <strong>%d</strong>, public: <strong>%d</strong>, private: <strong>%d</strong>.";

$lang->testsuite->lblCases      = 'CasTests';
$lang->testsuite->lblUnlinkCase = 'Retirer CasTest';

$lang->testsuite->authorList['private'] = 'Privé';
$lang->testsuite->authorList['public']  = 'Public';

$lang->testsuite->featureBar['browse']['all'] = 'All';
