#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的导入导出功能
"""

import requests
import json

def test_import_data_api():
    """测试新的导入数据API"""
    try:
        print("🔍 测试新的导入数据API...")
        
        # 模拟前端解析后的数据
        test_data = {
            "items": [
                {
                    "sequence_number": 1,
                    "work_dimension": "测试维度",
                    "work_theme": "测试主题1",
                    "supervision_source": "测试来源",
                    "work_content": "测试内容1",
                    "is_annual_assessment": "是",
                    "completion_deadline": "2024-12-31",
                    "overall_progress": "√ 已完成",
                    "财险_status": "√",
                    "寿险_status": "○",
                    "金租_status": "X",
                    "资管_status": "√",
                    "广租_status": "○",
                    "通盛_status": "X",
                    "担保_status": "√",
                    "小贷_status": "○",
                    "保理_status": "X",
                    "不动产_status": "√",
                    "征信_status": "○",
                    "金服_status": "X",
                    "本部_status": "√"
                },
                {
                    "sequence_number": 2,
                    "work_dimension": "测试维度",
                    "work_theme": "测试主题2",
                    "supervision_source": "测试来源",
                    "work_content": "测试内容2",
                    "is_annual_assessment": "否",
                    "completion_deadline": "2024-11-30",
                    "overall_progress": "○ 进行中",
                    "财险_status": "○",
                    "寿险_status": "√",
                    "金租_status": "X",
                    "资管_status": "○",
                    "广租_status": "√",
                    "通盛_status": "X",
                    "担保_status": "○",
                    "小贷_status": "√",
                    "保理_status": "X",
                    "不动产_status": "○",
                    "征信_status": "√",
                    "金服_status": "X",
                    "本部_status": "○"
                }
            ]
        }
        
        response = requests.post(
            'http://127.0.0.1:8001/api/v1/simple-supervision/import-data',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 导入成功: {result.get('message', '无消息')}")
        else:
            print(f"❌ 导入失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_get_items():
    """测试获取督办事项"""
    try:
        print("\n🔍 测试获取督办事项...")
        
        response = requests.get('http://127.0.0.1:8001/api/v1/simple-supervision/items')
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            items = result.get('data', [])
            print(f"✅ 获取成功，共 {len(items)} 条督办事项")
            
            if items:
                print("前两条数据:")
                for i, item in enumerate(items[:2]):
                    print(f"  {i+1}. {item['work_theme']} - 财险状态: {item.get('财险_status', 'N/A')}")
        else:
            print(f"❌ 获取失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始测试新的导入导出功能...")
    test_import_data_api()
    test_get_items()
    print("\n🎉 测试完成！")
