import{p as V,d as w,a5 as D,a3 as u,q as i,y as H,a1 as z,aU as G,r as l,o as n,h as p,e as o,w as c,F as d,A as N,c as _,L as R,z as U,t as $,f as v}from"./index.js";import{i as q}from"./icon-bb3d09e7.js";import{u as O,k as f,C as m,E as T}from"./useSync.hook-14394fcc.js";import"./plugin-37914809.js";import"./tables_list-f613fa36.js";const Y=w({__name:"index",setup(j){const a=O();D(()=>a.canLinkGlobalFilter());const{lockScale:y,scale:b}=u(a.getEditCanvas),{setItem:k}=f(),{getLayers:E}=u(f()),{LayersIcon:C}=q.ionicons5,L=i(100),S=e=>{k(e.key,!e.select)},x=e=>e.key===m.DETAILS?e.select?"":"primary":e.select?"primary":"",h=H([{key:m.LAYERS,select:E,title:"\u56FE\u5C42",icon:z(C)}]);let g=[{label:"200%",value:200},{label:"150%",value:150},{label:"100%",value:100},{label:"50%",value:50},{label:"\u81EA\u9002\u5E94",value:0}];const s=i(""),F=e=>{if(e===0){a.computedScale();return}a.setScale(e/100)};return G(()=>{const e=(b.value*100).toFixed(0);s.value=`${e}%`,L.value=parseInt(e)}),(e,r)=>{const I=l("n-button"),A=l("n-space"),B=l("n-select");return n(),p(d,null,[o(T,{batchLink:!0}),o(A,{class:"btnList"},{default:c(()=>[(n(!0),p(d,null,N(h,t=>(n(),_(I,{class:"btn",type:x(t),key:t.key,ghost:"",onClick:J=>S(t)},{icon:c(()=>[(n(),_(R(t.icon)))]),default:c(()=>[U("span",null,$(t.title),1)]),_:2},1032,["type","onClick"]))),128))]),_:1}),o(B,{disabled:v(y),class:"scale-btn",value:s.value,"onUpdate:value":[r[0]||(r[0]=t=>s.value=t),F],size:"mini",options:v(g)},null,8,["disabled","value","options"])],64)}}});var X=V(Y,[["__scopeId","data-v-2c7f30bf"]]);export{X as default};
