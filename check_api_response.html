<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检查API响应数据</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background-color: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #337ecc;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border-left: 4px solid #409EFF;
            max-height: 400px;
            overflow-y: auto;
        }
        .json-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .field-check {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .field-present {
            color: #38a169;
            font-weight: bold;
        }
        .field-missing {
            color: #e53e3e;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 检查API响应数据</h1>
        <p>直接检查按状态获取工单API的响应数据，特别是项目ID和项目名称字段</p>
        
        <button class="btn" onclick="checkAPI('all')">检查全部工单API</button>
        <button class="btn" onclick="checkAPI('completed')">检查已完成工单API</button>
        <button class="btn" onclick="checkAPI('urgent')">检查紧急工单API</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        async function checkAPI(status) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `<p>正在检查 ${status} 状态的工单API...</p>`;
            
            try {
                // 从当前页面获取认证信息
                const token = localStorage.getItem('token') || sessionStorage.getItem('token');
                
                const headers = {
                    'Content-Type': 'application/json'
                };
                
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }
                
                const url = `/api/v1/ticket-integration/tickets/by-status?status=${status}&limit=3`;
                const response = await fetch(url, { headers });
                
                let html = `<h3>📡 API响应检查 - ${status}</h3>`;
                html += `<p><strong>HTTP状态码:</strong> ${response.status}</p>`;
                
                if (response.ok) {
                    const data = await response.json();
                    
                    html += `<div class="field-check">`;
                    html += `<p><strong>响应成功:</strong> ${data.success ? '✅ 是' : '❌ 否'}</p>`;
                    
                    if (data.success && data.data && data.data.tickets) {
                        const tickets = data.data.tickets;
                        html += `<p><strong>工单数量:</strong> ${tickets.length}</p>`;
                        
                        if (tickets.length > 0) {
                            const firstTicket = tickets[0];
                            const fieldCount = Object.keys(firstTicket).length;
                            html += `<p><strong>字段数量:</strong> ${fieldCount}</p>`;
                            
                            // 检查关键字段
                            const keyFields = [
                                'feelec_ticket_id',
                                'feelec_ticket_no', 
                                'feelec_title',
                                'feelec_project_id',
                                'project_name',
                                'publisher_name',
                                'processor_name',
                                'status_name',
                                'priority_text',
                                'template_name'
                            ];
                            
                            html += `<h4>🔍 关键字段检查:</h4>`;
                            keyFields.forEach(field => {
                                const value = firstTicket[field];
                                const hasValue = value !== null && value !== undefined && value !== '';
                                const status = hasValue ? 'field-present' : 'field-missing';
                                const icon = hasValue ? '✅' : '❌';
                                html += `<p class="${status}">${icon} ${field}: ${JSON.stringify(value)}</p>`;
                            });
                            
                            // 显示第一个工单的完整数据
                            html += `<h4>📄 第一个工单的完整数据:</h4>`;
                            html += `<div class="json-display">${JSON.stringify(firstTicket, null, 2)}</div>`;
                            
                            // 特别检查项目字段
                            html += `<h4>🎯 项目字段特别检查:</h4>`;
                            tickets.forEach((ticket, index) => {
                                html += `<div class="field-check">`;
                                html += `<p><strong>工单 ${index + 1}:</strong> ${ticket.feelec_ticket_no}</p>`;
                                html += `<p>项目ID: ${ticket.feelec_project_id || '❌ 空'}</p>`;
                                html += `<p>项目名称: ${ticket.project_name || '❌ 空'}</p>`;
                                html += `</div>`;
                            });
                        }
                    } else {
                        html += `<p class="field-missing">❌ 数据格式不正确</p>`;
                        html += `<div class="json-display">${JSON.stringify(data, null, 2)}</div>`;
                    }
                    html += `</div>`;
                    
                } else {
                    const errorText = await response.text();
                    html += `<p class="field-missing">❌ 请求失败</p>`;
                    html += `<div class="json-display">${errorText}</div>`;
                }
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>❌ 检查失败</h3>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                `;
            }
        }
        
        // 页面加载完成后的提示
        window.onload = function() {
            console.log('🔍 API响应检查页面已加载');
            console.log('当前页面URL:', window.location.href);
            
            // 检查是否有认证token
            const token = localStorage.getItem('token') || sessionStorage.getItem('token');
            if (token) {
                console.log('✅ 找到认证token');
            } else {
                console.log('⚠️ 未找到认证token，可能需要先登录');
            }
        };
    </script>
</body>
</html>
