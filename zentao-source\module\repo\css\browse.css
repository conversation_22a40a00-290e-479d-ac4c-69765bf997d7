#mainMenu > .btn-toolbar {height: 35px; line-height: 35px;}
#mainMenu > .pull-right {padding-right: 1%;}
#sidebar>.sidebar-toggle>.icon {right: -2px; left: auto;}
.main-row {width: 99%;}
.btn-toolbar > .last-sync-time {display: inline-block; float: left; margin-right: 10px;}
.btn-toolbar .page-title {line-height: 32px;}
.dropdown-menu > li.selected > a:after {content: '';}
.pull-left li > a.selected {color: #e9f2fb; background-color: #16a8f8;}
.pull-left li > a {line-height: 20px; border-radius: 5px;}
.tree li.has-list.open:before {border-left: 0px;}
#mainMenu > div.btn-toolbar.pull-left > div.btn-group.open {line-height: 20px;}
#dropMenuRepo > div.table-row > div > div > ul > li {padding: 0}
#dropMenuRepo > div.table-row > div > div > ul > li > div {padding-left: 20px;}
#dropMenuRepo > div.table-row > div > div > ul > li > ul {padding-left: 10px;}
#repoList .hide-in-search .hidden {display: block !important; visibility: inherit !important;}
#mainMenu .btn-limit {max-width: unset !important;}

#branchList {line-height: 25px;}
#branchList .hide-in-search, #branchList .tree ul {padding-left: 20px;}
.tree li.has-list.open:before {border-left: none;}
#branchList .tree li {padding: 0;}
.branch-or-tag {border-radius: 4px;}

.c-name {min-width: 150px;}
.c-checkbox {width: 40px;}
.c-version {width: 80px;}
.c-commit {width: 70px;}
.c-committer {width: 100px;}
.c-icon {width: 30px;}

/* Fix bug #21893. */
.in #sidebar>.sidebar-toggle>.icon-angle-left:before {content: "\e315";}
.hide-sidebar #sidebar>.sidebar-toggle>.icon-angle-left:before {content: "\e314";}

.download-popover {width: 400px; max-width: none;}
.popover-content {padding: 0;}
.download-popover .table {margin-bottom: 0;}
.download-popover .table td {padding-right: 0;}
.download-popover .popover-title {padding: 8px;}

#mainMenu #swapper {padding: 0;}
#mainMenu #swapper #currentItem {color: #313c52;}
#mainMenu #swapper #currentItem:hover {color: #0b0f18; background-color: rgba(0, 0, 0, 0.075);}
