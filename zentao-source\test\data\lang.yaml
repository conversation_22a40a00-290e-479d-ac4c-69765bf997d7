title: table zt_lang
desc: "语言配置项"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: lang
    note: "所属语言"
    range: "all,de,en,fr,vi,zh-cn,zh-tw"
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: module
    note: "模块"
    range: process,stage,bug,task
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: section
    note: "选项"
    range: 1-10000
    prefix: "section"
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: key
    note: "键"
    range: 1-10000
    prefix: "key"
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: value
    note: "值"
    range: 1-10000
    prefix: "value"
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
  - field: system
    note: "系统预设"
    range: 0,1
    prefix: ""
    postfix: ""
    loop: 0
    loopfix: ""
    format: ""
