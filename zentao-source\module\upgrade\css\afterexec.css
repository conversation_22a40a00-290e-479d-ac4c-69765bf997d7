.adbox {margin-bottom: 0px; border: 0px; box-shadow: none;}
.adbox .panel-body {padding-bottom: 0px; border: 0px;}

.col-md-6 {width: 100% !important;}
.row .divider {display: block; width: 2px; height: 450px; background-color: rgba(0,0,0,.05); float: left;}

.row .col-md-6 .message {padding-left: 50px; padding-top: 50px;}
.row .col-md-6 .message #tohome {margin-top: 50px;}
.row .col-md-6 .message p {font-size: 16px;}
.row .col-md-6 .message .icon-check-circle {font-size: 85px; color: green;}

.card {
  position: relative;
  display: block;
  padding: 0;
  width: 280px;
  margin: 0 auto;
  overflow: hidden;
  border: 1px solid #ddd;
  border-radius: 0;
  -webkit-box-shadow: 0 1px 2px rgba(0,0,0,.075);
  box-shadow: 0 1px 2px rgba(0,0,0,.075);
  -webkit-transition: all .5s cubic-bezier(.175,.885,.32,1);
  -o-transition: all .5s cubic-bezier(.175,.885,.32,1);
  transition: all .5s cubic-bezier(.175,.885,.32,1);
}
.card + .card {margin-top: 10px;}

.card .card-heading {
  display: block;
  padding: 10px;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card .card-content
{
  padding: 0 10px 10px 10px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card > .card-reveal {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f1f1f1;
  -webkit-transition: all .8s cubic-bezier(.175,.885,.32,1);
  -o-transition: all .8s cubic-bezier(.175,.885,.32,1);
  transition: all .8s cubic-bezier(.175,.885,.32,1);
}

.card>.card-reveal>.card-heading {padding: 20px 10px;}
.card:hover>.card-reveal {top: 0;}

.card.ad > .img-wrapper {
  height: 70px;
  line-height: 70px;
  text-align: center;
  font-size: 25px;
  width: 100%;
}

.card.ad > .card-heading {text-align: center;}
.card.ad > .card-reveal > .card-heading {padding: 12px 10px 10px 15px;}
.card.ad > .card-reveal {color: #333;}
.card.ad > .card-reveal li {line-height: 1.5; font-size: 12px;}

.result-box {min-height: 400px; position: fixed; left: 50%; top: 50%; margin-left: -400px; margin-top: -200px;}
