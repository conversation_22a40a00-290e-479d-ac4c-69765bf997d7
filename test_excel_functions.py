#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel导出导入功能
"""

import requests
import json
import time

def test_excel_export():
    """测试Excel导出功能"""
    print("📤 测试Excel导出功能...")
    
    try:
        # 先登录获取token
        print("🔐 登录获取token...")
        login_response = requests.post("http://localhost:8000/api/v1/auth/login", json={
            "username": "admin",
            "password": "admin123"
        })
        
        if login_response.status_code == 200:
            token = login_response.json().get('access_token')
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        # 测试Excel导出
        print("📋 测试Excel导出...")
        export_response = requests.get(
            "http://localhost:8000/api/v1/new-supervision/export-excel",
            headers=headers,
            timeout=30
        )
        
        print(f"导出API状态码: {export_response.status_code}")
        
        if export_response.status_code == 200:
            # 检查响应头
            content_type = export_response.headers.get('content-type', '')
            content_disposition = export_response.headers.get('content-disposition', '')
            
            print(f"Content-Type: {content_type}")
            print(f"Content-Disposition: {content_disposition}")
            
            if 'spreadsheetml' in content_type and 'attachment' in content_disposition:
                print("✅ Excel导出功能正常")
                
                # 保存文件进行验证
                with open('test_export.xlsx', 'wb') as f:
                    f.write(export_response.content)
                print("✅ Excel文件已保存为 test_export.xlsx")
                
                return True
            else:
                print("❌ Excel导出响应格式错误")
                return False
        else:
            print(f"❌ Excel导出失败: {export_response.status_code}")
            print(f"错误响应: {export_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Excel导出测试异常: {str(e)}")
        return False

def test_excel_import():
    """测试Excel导入功能"""
    print("\n📥 测试Excel导入功能...")
    
    try:
        # 先登录获取token
        print("🔐 登录获取token...")
        login_response = requests.post("http://localhost:8000/api/v1/auth/login", json={
            "username": "admin",
            "password": "admin123"
        })
        
        if login_response.status_code == 200:
            token = login_response.json().get('access_token')
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        # 检查是否有导出的文件可以用于测试导入
        try:
            with open('test_export.xlsx', 'rb') as f:
                file_content = f.read()
            print("✅ 找到测试Excel文件")
        except FileNotFoundError:
            print("❌ 未找到测试Excel文件，请先运行导出测试")
            return False
        
        # 测试Excel导入
        print("📋 测试Excel导入...")
        files = {'file': ('test_import.xlsx', file_content, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
        
        import_response = requests.post(
            "http://localhost:8000/api/v1/new-supervision/import-excel",
            headers=headers,
            files=files,
            timeout=60
        )
        
        print(f"导入API状态码: {import_response.status_code}")
        
        if import_response.status_code == 200:
            response_data = import_response.json()
            print("✅ Excel导入功能正常")
            print(f"导入结果: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            return True
        else:
            print(f"❌ Excel导入失败: {import_response.status_code}")
            print(f"错误响应: {import_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Excel导入测试异常: {str(e)}")
        return False

def test_api_endpoints():
    """测试API端点是否正常"""
    print("\n🔍 测试API端点...")
    
    try:
        # 测试督办事项列表API
        response = requests.get("http://localhost:8000/api/v1/new-supervision/items", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            items_count = len(data.get('data', []))
            companies_count = len(data.get('companies', []))
            
            print(f"✅ 督办事项API正常")
            print(f"   督办事项数量: {items_count}")
            print(f"   公司数量: {companies_count}")
            return True
        else:
            print(f"❌ 督办事项API异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API端点测试异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 Excel导出导入功能测试")
    print("=" * 60)
    
    # 等待后端服务完全启动
    print("⏳ 等待后端服务完全启动...")
    time.sleep(3)
    
    # 测试API端点
    api_success = test_api_endpoints()
    
    if not api_success:
        print("❌ API端点测试失败，无法继续测试Excel功能")
        return
    
    # 测试Excel导出
    export_success = test_excel_export()
    
    # 测试Excel导入
    import_success = test_excel_import()
    
    print("\n" + "=" * 60)
    print("📋 Excel功能测试结果:")
    
    if export_success and import_success:
        print("🎉 Excel导出导入功能测试通过！")
        
        print("\n✅ 功能特色:")
        print("   📤 Excel导出功能:")
        print("      • 导出完整的督办管理表格")
        print("      • 包含29个督办事项和13家公司状态")
        print("      • 格式与原督办表一致")
        print("      • 自动生成带时间戳的文件名")
        
        print("\n   📥 Excel导入功能:")
        print("      • 支持上传Excel文件(.xlsx/.xls)")
        print("      • 自动识别并更新数据库记录")
        print("      • 支持新增和修改督办事项")
        print("      • 批量更新公司状态")
        print("      • 文件大小限制10MB")
        
        print("\n🌐 使用方法:")
        print("   1. 登录督办管理系统")
        print("   2. 点击'导出Excel'按钮下载当前数据")
        print("   3. 编辑Excel文件后点击'导入Excel'上传")
        print("   4. 系统自动更新数据库记录")
        
        print("\n🎯 应用场景:")
        print("   • 批量更新督办事项状态")
        print("   • 离线编辑督办数据")
        print("   • 数据备份和恢复")
        print("   • 与其他系统数据交换")
        
    else:
        print("❌ Excel功能测试失败")
        if not export_success:
            print("   • Excel导出功能异常")
        if not import_success:
            print("   • Excel导入功能异常")
        
        print("\n🔧 可能的解决方案:")
        print("   1. 检查后端服务是否正常运行")
        print("   2. 检查pandas和openpyxl库是否正确安装")
        print("   3. 检查API路由是否正确配置")
        print("   4. 检查文件上传权限")
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
