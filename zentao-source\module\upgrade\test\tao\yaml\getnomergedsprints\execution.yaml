title: table zt_execution
author: <PERSON>
version: "1.0"
fields:
  - field: id
    range: 101-700
  - field: name
    note: "名称"
    fields:
    - field: name1
      range: 迭代{30},阶段{30},看板{30}
    - field: name2
      range: 1-10000
  - field: project
    range: '0{10},11-100'
  - field: model
    range: []
  - field: type
    range: sprint{30},stage{30},kanban{30}
  - field: budget
    range: 800000-1:100
  - field: status
    range: wait,doing
  - field: percent
    range: 0{30},10{30},0{30}
  - field: milestone
    range: 0{30},1{10},0{10},1{10},0{30}
  - field: auth
    range: "extend"
  - field: desc
    range: 1-10000
    prefix: "迭代描述"
  - field: begin
    range: "(-2M)-(+M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: end
    range: "(+1w)-(+2M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: grade
    range: 1
  - field: parent
    range: 11-40,41-70,71-100
  - field: path
    fields:
      - field: path1
        prefix: ","
        range: 11-100
      - field: path2
        prefix: ","
        range: 101-700
        postfix: ","
  - field: acl
    range: open{4},private{4}
  - field: openedVersion
    range: "16.5"
  - field: whitelist
    froms:
      - from: common.user.v1.yaml
        use: empty{8}
      - from: common.user.v1.yaml
        use: empty{8}
        prefix: ","
      - from: common.user.v1.yaml
        use: one{8}
        prefix: ","
      - from: common.user.v1.yaml
        use: two{8}
        prefix: ","
      - from: common.user.v1.yaml
        use: three{8}
        prefix: ","
