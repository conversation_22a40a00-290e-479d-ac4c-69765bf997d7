title: table zt_report
desc: "报表"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: code
    note: "报表代号"
    range: 1-10000
    prefix: "code"
    postfix: ""
    loop: 0
    format: ""
  - field: name
    note: "报表名称"
    range: 1-10000
    prefix: "这个报表名称json"
    postfix: ""
    loop: 0
    format: ""
  - field: module
    note: "所属模块"
    range: project,test
    prefix: ","
    postfix: ""
    loop: 0
    format: ""
  - field: sql
    note: "SQL语句"
    range: 1-10000
    prefix: "sql"
    postfix: ""
    loop: 0
    format: ""
  - field: vars
    note: "预设字段"
    range: 1-10000
    prefix: "vars-json"
    postfix: ""
    loop: 0
    format: ""
  - field: langs
    note: "语言"
    range: 1-10000
    prefix: "lang-json"
    postfix: ""
    loop: 0
    format: ""
  - field: params
    note: "参数"
    range: 1-10000
    prefix: "params-json"
    postfix: ""
    loop: 0
    format: ""
  - field: step
    note: "步数"
    range: 2
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: desc
    note: "描述"
    range: 1-10000
    prefix: "desc-json"
    postfix: ""
    loop: 0
    format: ""
  - field: addedBy
    note: "添加者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: addedDate
    note: "添加日期"
    from: common.date.v1.yaml
    use: dateA
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
