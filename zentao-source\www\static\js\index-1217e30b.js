import{p as _,r as l,o as e,c as n,w as f,L as r,M as i,d as p,f as u}from"./index.js";const m={};function d(s,c){const t=l("router-view");return e(),n(t,null,{default:f(({Component:o,route:a})=>[a.noKeepAlive?(e(),n(r(o),{key:a.fullPath})):(e(),n(i,{key:1},[(e(),n(r(o),{key:a.fullPath}))],1024))]),_:1})}var v=_(m,[["render",d]]);const h=p({__name:"index",setup(s){return(c,t)=>(e(),n(u(v)))}});export{h as default};
