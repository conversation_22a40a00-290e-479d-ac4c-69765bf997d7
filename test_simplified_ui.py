#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化后的AI更新用户界面
验证界面是否按要求简化并使用正确的选项
"""

import asyncio
import aiohttp
import json
import os

# 设置测试模式环境变量
os.environ["PMO_TEST_MODE"] = "true"

BASE_URL = "http://localhost:8000"
TEST_PROJECT_CODE = "C202500012"

async def test_simplified_ui():
    """测试简化后的用户界面"""
    async with aiohttp.ClientSession() as session:
        print("🎯 测试简化后的AI更新用户界面")
        print("=" * 60)
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test_token"
        }
        
        # 触发AI更新分析
        print("📤 触发AI更新分析...")
        async with session.post(f"{BASE_URL}/api/v1/project/{TEST_PROJECT_CODE}/ai-update", 
                               headers=headers) as response:
            if response.status == 200:
                result = await response.json()
                print("✅ AI更新分析成功")
                
                if result.get("code") == 200:
                    data = result.get("data", {})
                    steps = data.get("steps", {})
                    
                    # 检查步骤6的数据
                    step6 = steps.get("step6_user_confirm", {})
                    if step6.get("status") == "ready":
                        print("\n🎨 简化后的用户界面特性验证:")
                        
                        # 解析AI建议的字段
                        ai_response = step6.get("data", {}).get("ai_response_markdown", "")
                        ai_fields = parse_ai_response(ai_response)
                        
                        print(f"✅ 1. 界面简化:")
                        print(f"   - 移除了'当前值 vs 建议值'对比显示")
                        print(f"   - 只显示最终更新值编辑框")
                        print(f"   - 节约了界面空间")
                        
                        print(f"\n✅ 2. 字段选项验证:")
                        select_fields = get_select_fields()
                        print(f"   - 选择字段数量: {len(select_fields)}")
                        print(f"   - 使用项目管理页面相同的选项获取逻辑")
                        
                        for field in select_fields:
                            if any(ai_field['field_key'] == field for ai_field in ai_fields):
                                print(f"   - {field}: 使用getFieldOptions()获取选项")
                        
                        print(f"\n✅ 3. AI建议字段显示:")
                        print(f"   - AI建议字段数: {len(ai_fields)}")
                        print(f"   - 每个AI建议字段显示原因")
                        print(f"   - 带有'AI建议'绿色标签")
                        
                        print(f"\n✅ 4. 编辑控件类型:")
                        field_types = analyze_field_types(ai_fields)
                        for field_type, count in field_types.items():
                            print(f"   - {field_type}: {count}个字段")
                        
                        print(f"\n🎯 用户体验改进:")
                        print(f"   ✅ 界面更简洁，减少视觉干扰")
                        print(f"   ✅ 编辑区域更紧凑，节约空间")
                        print(f"   ✅ 选项数据与项目管理页面一致")
                        print(f"   ✅ AI建议原因仍然可见")
                        print(f"   ✅ 支持所有字段类型的编辑")
                        
                        return True
                    else:
                        print("❌ 步骤6未准备就绪")
                        return False
                else:
                    print(f"❌ AI更新失败: {result.get('message')}")
                    return False
            else:
                print(f"❌ 请求失败: HTTP {response.status}")
                return False

def parse_ai_response(ai_response):
    """解析AI回答中的字段建议"""
    fields = []
    lines = ai_response.split('\n')
    
    for line in lines:
        if line.startswith('|') and line.endswith('|'):
            cells = line.split('|')[1:-1]  # 去掉首尾空元素
            cells = [cell.strip() for cell in cells]
            
            if len(cells) >= 4 and cells[0] and cells[1] and cells[2]:
                # 跳过表头和分隔行
                if not cells[0].startswith('-') and '字段名称' not in cells[0]:
                    fields.append({
                        'field_key': cells[0],
                        'current_value': cells[1],
                        'suggested_value': cells[2],
                        'reason': cells[3] if len(cells) > 3 else ''
                    })
    
    return fields

def get_select_fields():
    """获取选择字段列表（与项目管理页面一致）"""
    return [
        'excellence_level', 'project_category', 'investment_type', 
        'is_hardware', 'is_non_indigenous_innovation', 'current_progress',
        'investment_entity', 'responsible_department'
    ]

def analyze_field_types(ai_fields):
    """分析字段类型分布"""
    field_types = {
        '日期字段': 0,
        '选择字段': 0,
        '数字字段': 0,
        '文本字段': 0,
        '文本域字段': 0
    }
    
    date_fields = ['start_time', 'acceptance_time', 'project_establishment_time', 'project_implementation_time']
    select_fields = get_select_fields()
    number_fields = ['annual_investment_plan', 'budget', 'project_planned_total_investment']
    textarea_fields = ['construction_content', 'project_overview', 'next_steps', 'remarks']
    
    for field in ai_fields:
        field_key = field['field_key']
        if field_key in date_fields:
            field_types['日期字段'] += 1
        elif field_key in select_fields:
            field_types['选择字段'] += 1
        elif field_key in number_fields:
            field_types['数字字段'] += 1
        elif field_key in textarea_fields:
            field_types['文本域字段'] += 1
        else:
            field_types['文本字段'] += 1
    
    return field_types

async def main():
    """主函数"""
    success = await test_simplified_ui()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 简化后的AI更新界面验证成功！")
        print("\n📋 界面改进总结:")
        print("1. ✅ 移除了冗余的'当前值 vs 建议值'对比显示")
        print("2. ✅ 只保留最终更新值编辑区域，节约空间")
        print("3. ✅ 使用与项目管理页面一致的选项获取逻辑")
        print("4. ✅ AI建议原因仍然清晰可见")
        print("5. ✅ 支持所有字段类型的智能编辑控件")
        print("\n🌐 访问地址: http://localhost:3000")
        print("📝 使用方法: 项目管理 → 点击'AI更新' → 查看步骤6的简化编辑界面")
    else:
        print("\n❌ 简化后的AI更新界面验证失败")

if __name__ == "__main__":
    asyncio.run(main())
