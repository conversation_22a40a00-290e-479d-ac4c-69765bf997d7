#projectTableList > tr.drop-not-allowed {opacity: 0.1 !important;}
#projectList .c-actions {overflow: visible;}
#projectTableList > tr:last-child .c-actions .dropdown-menu {top: auto; bottom: 100%; margin-bottom: -5px;}
#projectList .c-budget {width: 100px; padding-right: 20px;}
#projectTableList .c-budget {width: 100px; padding-right: 20px;}

.c-date {width: 80px;}
.label-warning, .label-info {width: 60px !important;}
.project-type-label.label-outline {width: 50px; min-width: 50px;}
.c-date {width: 120px;}

#projectList td.c-name {padding-right: 0px;}
#projectList td.c-name .project-name {display: flex; align-items: center;}
#projectList td.c-name .project-name > span {flex: none; margin-left: 5px;}
#projectList td.c-name .project-name > a {overflow: hidden; display: inline-block; max-width: calc(100% - 50px);}
#projectList td.c-name .project-name.has-suffix > a {padding-right: 5px;}
