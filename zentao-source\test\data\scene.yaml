title: table zt_scene
desc: "测试场景"
author: <PERSON>
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-117
  - field: product
    note: "产品ID"
    range: 1
  - field: branch
    note: "分支ID"
    range: 0{39},1{39},2{39}
  - field: module
    note: "所属模块"
    range: 0
  - field: title
    note: "场景名称"
    range: "1-117"
    prefix: "这个是测试场景"
  - field: sort
    note: "排序"
    range: 1-117
  - field: openedBy
    note: "由谁创建"
    range: admin
  - field: openedDate
    note: "创建日期"
    range: "-:600"
    type: timestamp
    format: "YYYY-MM-DD hh:mm:ss"
  - field: lastEditedBy
    note: "最后修改者"
    range: admin
  - field: lastEditedDate
    note: "修改日期"
    range: "-:300"
    type: timestamp
    format: "YYYY-MM-DD hh:mm:ss"
  - field: deleted
    note: "是否删除"
    range: 0
  - field: parent
    note: "父场景"
    range: 0{3},1{3},2{3},3{3},4{3},5{3},6{3},7{3},8{3},9{3},10{3},11{3},12{3},0{3},40{3},41{3},42{3},43{3},44{3},45{3},46{3},47{3},48{3},49{3},50{3},51{3},0{3},79{3},80{3},81{3},82{3},83{3},84{3},85{3},86{3},87{3},88{3},89{3},90{3}
