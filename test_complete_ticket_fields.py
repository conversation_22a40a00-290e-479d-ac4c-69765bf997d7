#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的工单字段
"""

import requests
import json

def test_complete_ticket_fields():
    """测试完整的工单字段"""
    print("🚀 开始测试完整的工单字段...")
    try:
        # 获取项目列表
        projects_url = "http://localhost:8000/api/v1/ticket-integration/projects"
        headers = {"Authorization": "Bearer test_token"}
        
        print("🔍 获取项目列表...")
        projects_response = requests.get(projects_url, headers=headers, timeout=10)
        
        if projects_response.status_code != 200:
            print(f"❌ 获取项目列表失败: {projects_response.status_code}")
            return
            
        projects_data = projects_response.json()
        if not projects_data.get('success') or not projects_data.get('data'):
            print("❌ 项目列表为空")
            return
            
        # 获取第一个项目的工单
        project_id = projects_data['data'][0]['feelec_project_id']
        print(f"📋 测试项目ID: {project_id}")
        
        tickets_url = f"http://localhost:8000/api/v1/ticket-integration/projects/{project_id}/tickets"
        tickets_response = requests.get(tickets_url, headers=headers, timeout=10)
        
        if tickets_response.status_code != 200:
            print(f"❌ 获取工单列表失败: {tickets_response.status_code}")
            return
            
        tickets_data = tickets_response.json()
        if not tickets_data.get('success') or not tickets_data.get('data') or not tickets_data['data'].get('tickets'):
            print("❌ 工单列表为空")
            return
            
        # 获取第一个工单的完整内容
        ticket_id = tickets_data['data']['tickets'][0]['feelec_ticket_id']
        print(f"🎫 测试工单ID: {ticket_id}")
        
        content_url = f"http://localhost:8000/api/v1/ticket-integration/tickets/{ticket_id}/full-content"
        content_response = requests.get(content_url, headers=headers, timeout=10)
        
        if content_response.status_code == 200:
            data = content_response.json()
            if data.get('success'):
                ticket = data['data']
                print("✅ 工单完整内容获取成功")
                
                # 基础信息
                print(f"\n📋 基础信息:")
                print(f"   工单ID: {ticket.get('feelec_ticket_id')}")
                print(f"   工单编号: {ticket.get('feelec_ticket_no')}")
                print(f"   工单标题: {ticket.get('feelec_title')}")
                print(f"   工单类型: {ticket.get('type_text')} (原值: {ticket.get('feelec_type')})")
                print(f"   工单分类: {ticket.get('feelec_category')}")
                print(f"   子分类: {ticket.get('feelec_subcategory')}")
                
                # 人员信息
                print(f"\n👥 人员信息:")
                print(f"   发布人: {ticket.get('publisher_name')} (ID: {ticket.get('feelec_publisher_id')})")
                print(f"   处理人: {ticket.get('processor_name')} (ID: {ticket.get('feelec_processor_id')})")
                print(f"   指派人: {ticket.get('assignee_name')} (ID: {ticket.get('feelec_assignee_id')})")
                print(f"   创建人: {ticket.get('creator_name')} (ID: {ticket.get('feelec_creator_id')})")
                print(f"   修改人: {ticket.get('modifier_name')} (ID: {ticket.get('feelec_modifier_id')})")
                
                # 组织信息
                print(f"\n🏢 组织信息:")
                print(f"   所属部门: {ticket.get('department_name')} (ID: {ticket.get('feelec_department_id')})")
                print(f"   主体公司: {ticket.get('company_name')} (ID: {ticket.get('feelec_company_id')})")
                print(f"   关联项目: {ticket.get('project_name')} (ID: {ticket.get('feelec_project_id')})")
                
                # 状态和优先级
                print(f"\n📊 状态和优先级:")
                print(f"   当前状态: {ticket.get('status_name')} (ID: {ticket.get('feelec_status_id')})")
                print(f"   优先级: {ticket.get('priority_text')} (原值: {ticket.get('feelec_priority')})")
                print(f"   紧急程度: {ticket.get('urgency_text')} (原值: {ticket.get('feelec_urgency')})")
                print(f"   影响程度: {ticket.get('impact_text')} (原值: {ticket.get('feelec_impact')})")
                print(f"   级别: {ticket.get('level_text')} (原值: {ticket.get('feelec_level')})")
                
                # 模板信息
                print(f"\n📝 模板信息:")
                print(f"   工单模板: {ticket.get('template_name')} (ID: {ticket.get('feelec_template_id')})")
                print(f"   模板类型: {ticket.get('template_type_name')} (ID: {ticket.get('feelec_template_type_id')})")
                
                # 时间信息
                print(f"\n⏰ 时间信息:")
                print(f"   创建时间: {ticket.get('create_time_formatted')}")
                print(f"   修改时间: {ticket.get('modify_time_formatted')}")
                print(f"   首次分配: {ticket.get('first_assign_time_formatted')}")
                print(f"   首次处理: {ticket.get('first_process_time_formatted')}")
                print(f"   完成时间: {ticket.get('complete_time_formatted')}")
                print(f"   关闭时间: {ticket.get('feelec_close_time_formatted')}")
                print(f"   重开时间: {ticket.get('feelec_reopen_time_formatted')}")
                print(f"   截止时间: {ticket.get('deadline_formatted')}")
                
                # 工时信息
                print(f"\n⏱️ 工时信息:")
                print(f"   预估工时: {ticket.get('estimate_hours_text')}")
                print(f"   实际工时: {ticket.get('actual_hours_text')}")
                print(f"   处理时长: {ticket.get('process_duration_text')}")
                
                # 其他信息
                print(f"\n📌 其他信息:")
                print(f"   工单来源: {ticket.get('source_text')} (原值: {ticket.get('feelec_source')})")
                print(f"   归档状态: {ticket.get('archive_text')} (原值: {ticket.get('feelec_archive')})")
                print(f"   删除状态: {ticket.get('feelec_delete')}")
                print(f"   是否完成: {ticket.get('is_completed')}")
                print(f"   是否逾期: {ticket.get('is_overdue')}")
                print(f"   客户反馈: {ticket.get('feedback_score_text')}")
                
                # 内容信息
                print(f"\n📄 内容信息:")
                print(f"   工单内容: {ticket.get('feelec_content', '无')[:100]}...")
                print(f"   工单描述: {ticket.get('feelec_description', '无')[:100]}...")
                print(f"   解决方案: {ticket.get('feelec_resolution', '无')[:100]}...")
                print(f"   反馈评论: {ticket.get('feelec_feedback_comment', '无')[:100]}...")
                
            else:
                print(f"❌ API返回错误: {data.get('message')}")
        else:
            print(f"❌ 工单内容获取失败: {content_response.status_code}")
            print(f"错误信息: {content_response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_complete_ticket_fields()
