title: table zt_project
desc: '项目、执行'
author: lanzongjun
version: '1.0'
fields:
  - field: id
    range: 1-10
  - field: project
    range: 1
  - field: model
    range: waterfall,[]{9}
  - field: name
    note: "名称"
    fields:
      - field: name1
        range: 项目,执行{9}
      - field: name2
        range: 1,[1-9]
  - field: type
    range: project,stage{4},scrum{3},kanban{2}
  - field: status
    range: wait{2},doing{4},suspended,closed
  - field: lifetime
    range:
  - field: budget
    range: 900000-1:100
  - field: budgetUnit
    range: CNY,USD
  - field: attribute
    note: "Only stage has attribute"
    range:
  - field: percent
    range: 1-100:R
  - field: milestone
    note: "Is it milestone"
    range: 0
  - field: output
    note: "Output document"
    range:
  - field: auth
    note: "Only project has auth"
    range: "extend"
  - field: parent
    range: 1-10
  - field: path
    fields:
      - field: path1
        prefix: ","
        range: 1
      - field: path2
        prefix: ","
        range: 2-10
        postfix: ","
  - field: grade
    range: "2"
  - field: code
    range: 1-10000
    prefix: "project"
  - field: begin
    range: "(-3M)-(+M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: end
    range: "(+5w)-(+2M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: PO
    range:
  - field: PM
    range:
  - field: QD
    range:
  - field: RD
    range:
  - field: team
    range:
  - field: acl
    range: open,private,program
  - field: order
    range: 5-10000:5
  - field: openedVersion
    range:
  - field: openedDate
    range: "(-3M)-(+M):1D"
    type: timestamp
    format: "YY/MM/DD"
    postfix: "\t"
  - field: deleted
    range: 0
