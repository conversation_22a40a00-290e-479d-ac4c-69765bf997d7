<?php
$lang->gitlab->common            = 'GitLab';
$lang->gitlab->browse            = '瀏覽GitLab';
$lang->gitlab->search            = '搜索';
$lang->gitlab->create            = '添加GitLab';
$lang->gitlab->edit              = '編輯GitLab';
$lang->gitlab->view              = '查看GitLab';
$lang->gitlab->bindUser          = '綁定用戶';
$lang->gitlab->webhook           = 'Webhook';
$lang->gitlab->bindProduct       = '關聯產品';
$lang->gitlab->importIssue       = '關聯issue';
$lang->gitlab->delete            = '刪除GitLab';
$lang->gitlab->confirmDelete     = '確認刪除該GitLab嗎？';
$lang->gitlab->gitlabAccount     = 'GitLab用戶';
$lang->gitlab->zentaoAccount     = '禪道用戶';
$lang->gitlab->accountDesc       = '(系統會將相同郵箱地址的用戶自動匹配)';
$lang->gitlab->bindingStatus     = '綁定狀態';
$lang->gitlab->all               = '全部';
$lang->gitlab->binded            = '已綁定';
$lang->gitlab->bindedError       = '綁定的用戶已刪除或者已修改，請重新綁定';
$lang->gitlab->serverFail        = '連接GitLab伺服器異常，請檢查GitLab伺服器。';
$lang->gitlab->lastUpdate        = '最後更新';
$lang->gitlab->confirmAddWebhook = '您確定創建Webhook嗎？';
$lang->gitlab->addWebhookSuccess = 'Webhook創建成功';
$lang->gitlab->failCreateWebhook = 'Webhook創建失敗，請查看日誌';
$lang->gitlab->placeholderSearch = '請輸入項目名稱';

$lang->gitlab->browseAction         = 'GitLab列表';
$lang->gitlab->deleteAction         = '刪除GitLab';
$lang->gitlab->gitlabProject        = "{$lang->gitlab->common}項目";
$lang->gitlab->browseProject        = "{$lang->gitlab->common}項目列表";
$lang->gitlab->browseUser           = "用戶列表";
$lang->gitlab->browseGroup          = "群組列表";
$lang->gitlab->browseBranch         = "GitLab分支列表";
$lang->gitlab->browseTag            = "GitLab標籤列表";
$lang->gitlab->gitlabIssue          = "{$lang->gitlab->common} issue";
$lang->gitlab->zentaoProduct        = '禪道產品';
$lang->gitlab->objectType           = '類型'; // task, bug, story
$lang->gitlab->******************** = '項目成員管理';
$lang->gitlab->createProject        = '添加GitLab項目';
$lang->gitlab->editProject          = '編輯GitLab項目';
$lang->gitlab->deleteProject        = '刪除GitLab項目';
$lang->gitlab->createGroup          = '添加群組';
$lang->gitlab->editGroup            = '編輯群組';
$lang->gitlab->deleteGroup          = '刪除群組';
$lang->gitlab->createUser           = '添加用戶';
$lang->gitlab->editUser             = '編輯用戶';
$lang->gitlab->deleteUser           = '刪除用戶';
$lang->gitlab->createBranch         = '创建分支';
$lang->gitlab->manageGroupMembers   = '群組成員管理';
$lang->gitlab->createWebhook        = '創建Webhook';
$lang->gitlab->browseBranchPriv     = '分支保護管理';
$lang->gitlab->createBranchPriv     = '創建分支保護';
$lang->gitlab->editBranchPriv       = '編輯分支保護';
$lang->gitlab->deleteBranchPriv     = '刪除分支保護';
$lang->gitlab->createTag            = '創建標籤';
$lang->gitlab->deleteTag            = '刪除標籤';
$lang->gitlab->createTagPriv        = '創建標簽保護';
$lang->gitlab->editTagPriv          = '編輯標簽保護';

$lang->gitlab->id             = 'ID';
$lang->gitlab->name           = "{$lang->gitlab->common}名稱";
$lang->gitlab->url            = '服務地址';
$lang->gitlab->token          = 'Token';
$lang->gitlab->defaultProject = '預設項目';
$lang->gitlab->private        = 'MD5驗證';

$lang->gitlab->lblCreate     = '添加GitLab伺服器';
$lang->gitlab->desc          = '描述';
$lang->gitlab->tokenFirst    = 'Token不為空時，優先使用Token。';
$lang->gitlab->tips          = '使用密碼時，請在GitLab全局安全設置中禁用"防止跨站點請求偽造"選項。';
$lang->gitlab->emptyError    = "不能為空";
$lang->gitlab->createSuccess = "創建成功";

$lang->gitlab->placeholder = new stdclass;
$lang->gitlab->placeholder->name        = '';
$lang->gitlab->placeholder->url         = "請填寫GitLab Server首頁的訪問地址，如：https://gitlab.zentao.net。";
$lang->gitlab->placeholder->token       = "請填寫具有admin權限賬戶的access token";
$lang->gitlab->placeholder->projectPath = "項目標識串只能包含字母、數字、“_”、“-”和“.”。不能以“-”開頭，以.git或者.atom結尾";

$lang->gitlab->noImportableIssues = "目前沒有可供導入的issue。";
$lang->gitlab->tokenError         = "當前token非管理員權限。";
$lang->gitlab->tokenLimit         = "GitLab Token權限不足。請更換為有管理員權限的GitLab Token。";
$lang->gitlab->hostError          = "無效的GitLab服務地址。";
$lang->gitlab->bindUserError      = "不能重複綁定用戶 %s";
$lang->gitlab->importIssueError   = "未選擇該issue所屬的執行。";
$lang->gitlab->importIssueWarn    = "存在導入失敗的issue，可再次嘗試導入。";

$lang->gitlab->accessLevels[10] = 'Guest';
$lang->gitlab->accessLevels[20] = 'Reporter';
$lang->gitlab->accessLevels[30] = 'Developer';
$lang->gitlab->accessLevels[40] = 'Maintainer';
$lang->gitlab->accessLevels[50] = 'Owner';

$lang->gitlab->apiError[0] = 'internal is not allowed in a private group.';
$lang->gitlab->apiError[1] = 'public is not allowed in a private group.';
$lang->gitlab->apiError[2] = 'is too short (minimum is 8 characters)';
$lang->gitlab->apiError[3] = "can contain only letters, digits, '_', '-' and '.'. Cannot start with '-', end in '.git' or end in '.atom'";
$lang->gitlab->apiError[4] = 'Branch already exists';
$lang->gitlab->apiError[5] = 'Failed to save group {:path=>["has already been taken"]}';
$lang->gitlab->apiError[6] = 'Failed to save group {:path=>["已经被使用"]}';
$lang->gitlab->apiError[7] = '403 Forbidden';
$lang->gitlab->apiError[8] = 'is invalid';
$lang->gitlab->apiError[9] = 'admin is a reserved name';

$lang->gitlab->errorLang[0] = '私有分組的項目，可見性級別不能設為內部。';
$lang->gitlab->errorLang[1] = '私有分組的項目，可見性級別不能設為公開。';
$lang->gitlab->errorLang[2] = '密碼太短（最少8個字元）';
$lang->gitlab->errorLang[3] = "只能包含字母、數字、'.'-'和'.'。不能以'-'開頭、以'.git'結尾或以'.atom'結尾。";
$lang->gitlab->errorLang[4] = '分支名已存在。';
$lang->gitlab->errorLang[5] = '保存失敗，群組URL路徑已經被使用。';
$lang->gitlab->errorLang[6] = '保存失敗，群組URL路徑已經被使用。';
$lang->gitlab->errorLang[7] = $lang->gitlab->noAccess;
$lang->gitlab->errorLang[8] = '格式错误';
$lang->gitlab->errorLang[9] = 'admin是保留名';

$lang->gitlab->errorResonse['Email has already been taken']    = '邮箱已存在';
$lang->gitlab->errorResonse['Username has already been taken'] = '用户名已存在';

$lang->gitlab->project = new stdclass;
$lang->gitlab->project->id                         = "項目ID";
$lang->gitlab->project->name                       = "項目名稱";
$lang->gitlab->project->create                     = "添加GitLab項目";
$lang->gitlab->project->edit                       = "編輯GitLab項目";
$lang->gitlab->project->url                        = "項目 URL";
$lang->gitlab->project->path                       = "項目標識串";
$lang->gitlab->project->description                = "項目描述";
$lang->gitlab->project->visibility                 = "可見性級別";
$lang->gitlab->project->visibilityList['private']  = "私有(項目訪問必須明確授予每個用戶。 如果此項目是在一個群組中，群組成員將會獲得訪問權限)";
$lang->gitlab->project->visibilityList['internal'] = "內部(除外部用戶外，任何登錄用戶均可訪問該項目)";
$lang->gitlab->project->visibilityList['public']   = "公開(該項目允許任何人訪問)";
$lang->gitlab->project->star                       = "星標";
$lang->gitlab->project->fork                       = "派生";
$lang->gitlab->project->mergeRequests              = "合併請求";
$lang->gitlab->project->issues                     = "議題";
$lang->gitlab->project->tagList                    = "主題";
$lang->gitlab->project->tagListTips                = "用逗號分隔主題。";
$lang->gitlab->project->emptyNameError             = "項目名稱不能為空";
$lang->gitlab->project->emptyPathError             = "項目標識串不能為空";
$lang->gitlab->project->confirmDelete              = '確認刪除該GitLab項目嗎？';
$lang->gitlab->project->notbindedError             = '還沒綁定GitLab用戶，無法修改權限！';

$lang->gitlab->user = new stdclass;
$lang->gitlab->user->id             = "用戶ID";
$lang->gitlab->user->name           = "名稱";
$lang->gitlab->user->username       = "用戶名";
$lang->gitlab->user->email          = "郵箱";
$lang->gitlab->user->password       = "密碼";
$lang->gitlab->user->passwordRepeat = "請重複密碼";
$lang->gitlab->user->projectsLimit  = "限制項目";
$lang->gitlab->user->canCreateGroup = "可創建組";
$lang->gitlab->user->external       = "外部人員";
$lang->gitlab->user->externalTip    = "除非明確授予訪問權限，否則外部用戶無法查看內部或私有項目。另外，外部用戶無法創建項目，群組或個人代碼片段。";
$lang->gitlab->user->bind           = "禪道用戶";
$lang->gitlab->user->avatar         = "頭像";
$lang->gitlab->user->skype          = "Skype";
$lang->gitlab->user->linkedin       = "Linkedin";
$lang->gitlab->user->twitter        = "Twitter";
$lang->gitlab->user->websiteUrl     = "網站地址";
$lang->gitlab->user->note           = "備註";
$lang->gitlab->user->createOn       = "創建於";
$lang->gitlab->user->lastActivity   = "上次活動";
$lang->gitlab->user->create         = "添加GitLab用戶";
$lang->gitlab->user->edit           = "編輯GitLab用戶";
$lang->gitlab->user->emptyError     = "不能為空";
$lang->gitlab->user->passwordError  = "二次密碼不一致！";
$lang->gitlab->user->bindError      = "該用戶已經被綁定！";
$lang->gitlab->user->confirmDelete  = '確認刪除該GitLab用戶嗎？';

$lang->gitlab->group = new stdclass;
$lang->gitlab->group->id                                      = "群組ID";
$lang->gitlab->group->name                                    = "群組名稱";
$lang->gitlab->group->path                                    = "群組URL";
$lang->gitlab->group->pathTip                                 = "更改群組URL可能會有意想不到的副作用。";
$lang->gitlab->group->description                             = "群組描述";
$lang->gitlab->group->avatar                                  = "群組頭像";
$lang->gitlab->group->avatarTip                               = '檔案最大支持200k.';
$lang->gitlab->group->visibility                              = "可見性級別";
$lang->gitlab->group->visibilityList['private']               = "私有(群組及其項目只能由成員查看)";
$lang->gitlab->group->visibilityList['internal']              = "內部(除外部用戶外，任何登錄用戶均可查看該組和任何內部項目)";
$lang->gitlab->group->visibilityList['public']                = "公開(群組和任何公共項目可以在沒有任何身份驗證的情況下查看)";
$lang->gitlab->group->permission                              = '許可';
$lang->gitlab->group->requestAccessEnabledTip                 = "允許用戶請求訪問(如果可見性是公開或內部的)";
$lang->gitlab->group->lfsEnabled                              = '大檔案存儲';
$lang->gitlab->group->lfsEnabledTip                           = "允許該組內的項目使用 Git LFS(可以在每個項目中覆蓋此設置)";
$lang->gitlab->group->********************                    = "創建項目權限";
$lang->gitlab->group->********************List['noone']       = "禁止";
$lang->gitlab->group->********************List['maintainer']  = "維護者";
$lang->gitlab->group->********************List['developer']   = "開發者 + 維護者";
$lang->gitlab->group->subgroupCreationLevel                   = "創建子群組權限";
$lang->gitlab->group->subgroupCreationLevelList['owner']      = "所有者";
$lang->gitlab->group->subgroupCreationLevelList['maintainer'] = "維護者";
$lang->gitlab->group->create                                  = "添加群組";
$lang->gitlab->group->edit                                    = "編輯群組";
$lang->gitlab->group->createOn                                = "創建於";
$lang->gitlab->group->members                                 = "群組成員";
$lang->gitlab->group->confirmDelete                           = '確認刪除該GitLab群組嗎？';
$lang->gitlab->group->emptyError                              = "不能為空";
$lang->gitlab->group->manageMembers                           = '群組成員管理';
$lang->gitlab->group->memberName                              = '賬號';
$lang->gitlab->group->memberAccessLevel                       = '角色權限';
$lang->gitlab->group->memberExpiresAt                         = '過期時間';
$lang->gitlab->group->repeatError                             = "群組成員不能重複添加";

$lang->gitlab->branch = new stdclass();
$lang->gitlab->branch->name                        = '分支名';
$lang->gitlab->branch->from                        = '創建自';
$lang->gitlab->branch->create                      = '創建';
$lang->gitlab->branch->lastCommitter               = '最後提交';
$lang->gitlab->branch->lastCommittedDate           = '最後修改時間';
$lang->gitlab->branch->accessLevel                 = "分支保護列表";
$lang->gitlab->branch->mergeAllowed                = "允許合併到";
$lang->gitlab->branch->pushAllowed                 = "允許推送到";
$lang->gitlab->branch->placeholderSearch           = "請輸入分支名稱";
$lang->gitlab->branch->placeholderSelect           = "請選擇分支";
$lang->gitlab->branch->confirmDelete               = '確定刪除分支保護？';
$lang->gitlab->branch->branchCreationLevelList[40] = "維護者";
$lang->gitlab->branch->branchCreationLevelList[30] = "開發者 + 維護者";
$lang->gitlab->branch->branchCreationLevelList[0]  = "禁止";
$lang->gitlab->branch->emptyPrivNameError          = "分支不能為空";
$lang->gitlab->branch->issetPrivNameError          = "已存在該保護分支";

$lang->gitlab->tag = new stdclass();
$lang->gitlab->tag->name               = '標籤名';
$lang->gitlab->tag->ref                = '創建自';
$lang->gitlab->tag->lastCommitter      = '最後提交';
$lang->gitlab->tag->lastCommittedDate  = '最後修改時間';
$lang->gitlab->tag->placeholderSearch  = "請輸入標籤名稱";
$lang->gitlab->tag->message            = '信息';
$lang->gitlab->tag->emptyNameError     = "標籤名不能為空";
$lang->gitlab->tag->emptyRefError      = "創建自不能為空";
$lang->gitlab->tag->issetNameError     = "已存在該標籤";
$lang->gitlab->tag->confirmDelete      = '確認刪除該GitLab標籤嗎？';
$lang->gitlab->tag->protected          = '受保護';
$lang->gitlab->tag->accessLevel        = '允許創建';
$lang->gitlab->tag->emptyPrivNameError = '標簽不能爲空';
$lang->gitlab->tag->issetPrivNameError = '已存在該保護標簽';

$lang->gitlab->featureBar['binduser']['all']     = $lang->gitlab->all;
$lang->gitlab->featureBar['binduser']['notBind'] = $lang->gitlab->notBind;
$lang->gitlab->featureBar['binduser']['binded']  = $lang->gitlab->binded;
