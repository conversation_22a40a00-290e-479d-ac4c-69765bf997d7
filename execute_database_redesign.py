#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行数据库重设计
基于Excel督办表重新设计数据库结构
"""

import pymysql
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

# 数据库配置
DB_CONFIG = {
    'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'cyh',
    'password': 'Qq188788',
    'database': 'kanban2',
    'charset': 'utf8mb4'
}

def execute_sql(connection, sql, description=""):
    """执行SQL语句"""
    try:
        cursor = connection.cursor()
        cursor.execute(sql)
        connection.commit()
        cursor.close()
        if description:
            logging.info(f"✅ {description}")
        return True
    except Exception as e:
        logging.error(f"❌ {description} 失败: {e}")
        return False

def execute_database_redesign():
    """执行数据库重设计"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        logging.info("🚀 开始执行数据库重设计")
        logging.info("=" * 80)
        
        # 1. 备份现有数据
        logging.info("步骤1: 备份现有数据")
        logging.info("-" * 40)
        
        backup_sqls = [
            ("CREATE TABLE supervision_items_backup_20250731 AS SELECT * FROM supervision_items", "备份督办事项表"),
            ("CREATE TABLE company_progress_backup_20250731 AS SELECT * FROM company_progress", "备份公司进度表"),
            ("CREATE TABLE company_supervision_status_backup_20250731 AS SELECT * FROM company_supervision_status", "备份公司督办状态表")
        ]
        
        for sql, desc in backup_sqls:
            execute_sql(connection, sql, desc)
        
        # 2. 创建新的督办事项表
        logging.info("\n步骤2: 创建新的督办事项表")
        logging.info("-" * 40)
        
        create_supervision_items_sql = """
        CREATE TABLE supervision_items_new (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            sequence_number INT NOT NULL COMMENT '序号',
            work_dimension VARCHAR(100) NOT NULL COMMENT '工作维度',
            work_theme VARCHAR(200) NOT NULL COMMENT '工作主题', 
            supervision_source VARCHAR(100) NOT NULL COMMENT '督办来源',
            work_content TEXT NOT NULL COMMENT '工作内容和完成标志',
            is_annual_assessment ENUM('是','否') DEFAULT '否' COMMENT '是否年度绩效考核指标',
            completion_deadline DATE COMMENT '完成时限',
            progress_description TEXT COMMENT '进度情况描述',
            completion_date DATE COMMENT '实际完成日期',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            deleted_at TIMESTAMP NULL COMMENT '删除时间',
            
            UNIQUE KEY uk_sequence_number (sequence_number) COMMENT '序号唯一索引',
            KEY idx_work_dimension (work_dimension) COMMENT '工作维度索引',
            KEY idx_completion_deadline (completion_deadline) COMMENT '完成时限索引',
            KEY idx_is_annual_assessment (is_annual_assessment) COMMENT '年度考核索引',
            KEY idx_deleted_at (deleted_at) COMMENT '删除时间索引'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='督办事项表'
        """
        
        execute_sql(connection, "DROP TABLE IF EXISTS supervision_items_new", "删除旧的新表")
        execute_sql(connection, create_supervision_items_sql, "创建新督办事项表")
        
        # 3. 重新设置公司数据
        logging.info("\n步骤3: 重新设置公司数据")
        logging.info("-" * 40)
        
        execute_sql(connection, "DELETE FROM companies", "清空公司表")
        
        companies_data = [
            ('caixian', '财险', 1),
            ('shouxian', '寿险', 2),
            ('jinzu', '金租', 3),
            ('ziguan', '资管', 4),
            ('guangzu', '广租', 5),
            ('tongsheng', '通盛', 6),
            ('danbao', '担保', 7),
            ('xiaodai', '小贷', 8),
            ('baoli', '保理', 9),
            ('budongchan', '不动产', 10),
            ('zhengxin', '征信', 11),
            ('jinfu', '金服', 12),
            ('benbu', '本部', 13)
        ]
        
        cursor = connection.cursor()
        insert_company_sql = "INSERT INTO companies (company_code, company_name, display_order, is_active) VALUES (%s, %s, %s, 1)"
        cursor.executemany(insert_company_sql, companies_data)
        connection.commit()
        cursor.close()
        logging.info("✅ 插入13家公司数据")
        
        # 4. 创建统一进度表
        logging.info("\n步骤4: 创建统一进度表")
        logging.info("-" * 40)
        
        create_progress_table_sql = """
        CREATE TABLE company_supervision_progress (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            supervision_item_id INT NOT NULL COMMENT '督办事项ID',
            company_id INT NOT NULL COMMENT '公司ID',
            status ENUM('√', 'O', '！', 'X', '—') NOT NULL DEFAULT 'X' COMMENT '进度状态: √已完成 O进行中 ！逾期 X未启动 —不需要执行',
            progress_description TEXT COMMENT '进度描述',
            existing_problems TEXT COMMENT '存在问题',
            next_plan TEXT COMMENT '下一步计划',
            completion_date DATE COMMENT '完成日期',
            updated_by VARCHAR(50) DEFAULT 'system' COMMENT '更新人',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            
            UNIQUE KEY uk_supervision_company (supervision_item_id, company_id) COMMENT '督办事项-公司唯一索引',
            KEY idx_supervision_item (supervision_item_id) COMMENT '督办事项索引',
            KEY idx_company (company_id) COMMENT '公司索引',
            KEY idx_status (status) COMMENT '状态索引',
            KEY idx_updated_at (updated_at) COMMENT '更新时间索引'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司督办进度表'
        """
        
        execute_sql(connection, "DROP TABLE IF EXISTS company_supervision_progress", "删除旧进度表")
        execute_sql(connection, create_progress_table_sql, "创建统一进度表")
        
        connection.close()
        logging.info("=" * 80)
        logging.info("🎯 数据库结构重设计完成！")
        return True
        
    except Exception as e:
        logging.error(f"❌ 数据库重设计失败: {e}")
        return False

def main():
    """主函数"""
    logging.info("🚀 开始数据库重设计")
    
    if execute_database_redesign():
        logging.info("🎯 数据库重设计成功！")
    else:
        logging.error("❌ 数据库重设计失败")

if __name__ == "__main__":
    main()
