#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的公司列管理功能测试
"""

import requests

def test_companies():
    """测试公司列管理功能"""
    print("🚀 测试公司列管理功能...")
    
    # 1. 获取现有公司列表
    print("\n1. 获取现有公司列表...")
    try:
        response = requests.get('http://127.0.0.1:8001/api/v1/supervision/companies')
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {result}")
            if result.get('success'):
                companies = result.get('data', [])
                print(f"✅ 当前有 {len(companies)} 个公司:")
                for company in companies:
                    print(f"   - {company['company_name']}")
            else:
                print(f"❌ 失败: {result}")
        else:
            print(f"❌ 请求失败: {response.text}")
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
    
    # 2. 测试添加新公司
    print("\n2. 测试添加新公司...")
    try:
        response = requests.post('http://127.0.0.1:8001/api/v1/supervision/companies', 
                               json={'company_name': '测试公司'})
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {result}")
        else:
            print(f"❌ 请求失败: {response.text}")
    except Exception as e:
        print(f"❌ 异常: {str(e)}")

if __name__ == "__main__":
    test_companies()
