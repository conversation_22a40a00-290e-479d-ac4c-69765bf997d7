#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析工单系统中的用户、部门、主体相关表结构
"""

import mysql.connector
from decimal import Decimal

class TicketDatabaseAnalyzer:
    """工单数据库分析器"""
    
    def __init__(self):
        self.config = {
            'host': '**********',
            'user': 'qyuser', 
            'password': 'C~w9d4kaWS',
            'database': 'ticket',
            'autocommit': True
        }
        self.connection = None
    
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = mysql.connector.connect(**self.config)
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False
    
    def execute_query(self, sql: str, params: tuple = None):
        """执行查询SQL"""
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return []
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(sql, params or ())
            result = cursor.fetchall()
            cursor.close()
            
            # 处理Decimal类型
            for row in result:
                for key, value in row.items():
                    if isinstance(value, Decimal):
                        row[key] = float(value)
            
            return result
        except Exception as e:
            print(f"SQL执行失败: {e}")
            return []
    
    def analyze_user_tables(self):
        """分析用户相关表"""
        print("👤 分析用户相关表...")
        print("=" * 60)
        
        # 查找用户相关表
        user_tables = self.execute_query("""
            SELECT TABLE_NAME as table_name, TABLE_COMMENT as table_comment
            FROM information_schema.tables
            WHERE table_schema = 'ticket'
            AND (TABLE_NAME LIKE '%user%' OR TABLE_NAME LIKE '%member%' OR TABLE_NAME LIKE '%employee%')
            ORDER BY TABLE_NAME
        """)
        
        print(f"📋 找到 {len(user_tables)} 个用户相关表:")
        for table in user_tables:
            print(f"   📄 {table['table_name']}: {table['table_comment'] or '无注释'}")
        
        # 分析feelec_user表结构
        if any(t['table_name'] == 'feelec_user' for t in user_tables):
            print(f"\n🔍 分析 feelec_user 表结构:")
            user_columns = self.execute_query("""
                SELECT COLUMN_NAME as column_name, COLUMN_TYPE as column_type, COLUMN_COMMENT as column_comment, IS_NULLABLE as is_nullable
                FROM information_schema.columns
                WHERE table_schema = 'ticket' AND table_name = 'feelec_user'
                ORDER BY ordinal_position
            """)
            
            for col in user_columns:
                print(f"   📝 {col['column_name']}: {col['column_type']} - {col['column_comment'] or '无注释'}")
            
            # 查看用户数据样例
            print(f"\n📊 feelec_user 数据样例:")
            user_samples = self.execute_query("""
                SELECT feelec_user_id, feelec_name, feelec_mobile, feelec_email, feelec_department_id, feelec_company_id
                FROM feelec_user 
                WHERE feelec_delete = 20 
                LIMIT 5
            """)
            
            for user in user_samples:
                print(f"   👤 ID:{user['feelec_user_id']} 姓名:{user['feelec_name']} 部门:{user['feelec_department_id']} 公司:{user['feelec_company_id']}")
    
    def analyze_department_tables(self):
        """分析部门相关表"""
        print(f"\n🏢 分析部门相关表...")
        print("=" * 60)
        
        # 查找部门相关表
        dept_tables = self.execute_query("""
            SELECT TABLE_NAME as table_name, TABLE_COMMENT as table_comment
            FROM information_schema.tables
            WHERE table_schema = 'ticket'
            AND (TABLE_NAME LIKE '%department%' OR TABLE_NAME LIKE '%dept%' OR TABLE_NAME LIKE '%organization%')
            ORDER BY TABLE_NAME
        """)
        
        print(f"📋 找到 {len(dept_tables)} 个部门相关表:")
        for table in dept_tables:
            print(f"   📄 {table['table_name']}: {table['table_comment'] or '无注释'}")
        
        # 分析feelec_member_department表
        if any(t['table_name'] == 'feelec_member_department' for t in dept_tables):
            print(f"\n🔍 分析 feelec_member_department 表结构:")
            dept_columns = self.execute_query("""
                SELECT COLUMN_NAME as column_name, COLUMN_TYPE as column_type, COLUMN_COMMENT as column_comment, IS_NULLABLE as is_nullable
                FROM information_schema.columns
                WHERE table_schema = 'ticket' AND table_name = 'feelec_member_department'
                ORDER BY ordinal_position
            """)
            
            for col in dept_columns:
                print(f"   📝 {col['column_name']}: {col['column_type']} - {col['column_comment'] or '无注释'}")
            
            # 查看部门数据样例
            print(f"\n📊 feelec_member_department 数据样例:")
            dept_samples = self.execute_query("""
                SELECT feelec_department_id, feelec_name, feelec_parent_id, feelec_company_id
                FROM feelec_member_department 
                WHERE feelec_delete = 20 
                LIMIT 10
            """)
            
            for dept in dept_samples:
                print(f"   🏢 ID:{dept['feelec_department_id']} 名称:{dept['feelec_name']} 父级:{dept['feelec_parent_id']} 公司:{dept['feelec_company_id']}")
    
    def analyze_company_tables(self):
        """分析公司/主体相关表"""
        print(f"\n🏛️ 分析公司/主体相关表...")
        print("=" * 60)
        
        # 查找公司相关表
        company_tables = self.execute_query("""
            SELECT TABLE_NAME as table_name, TABLE_COMMENT as table_comment
            FROM information_schema.tables
            WHERE table_schema = 'ticket'
            AND (TABLE_NAME LIKE '%company%' OR TABLE_NAME LIKE '%organization%' OR TABLE_NAME LIKE '%entity%')
            ORDER BY TABLE_NAME
        """)
        
        print(f"📋 找到 {len(company_tables)} 个公司相关表:")
        for table in company_tables:
            print(f"   📄 {table['table_name']}: {table['table_comment'] or '无注释'}")
        
        # 分析feelec_company表
        if any(t['table_name'] == 'feelec_company' for t in company_tables):
            print(f"\n🔍 分析 feelec_company 表结构:")
            company_columns = self.execute_query("""
                SELECT COLUMN_NAME as column_name, COLUMN_TYPE as column_type, COLUMN_COMMENT as column_comment, IS_NULLABLE as is_nullable
                FROM information_schema.columns
                WHERE table_schema = 'ticket' AND table_name = 'feelec_company'
                ORDER BY ordinal_position
            """)
            
            for col in company_columns:
                print(f"   📝 {col['column_name']}: {col['column_type']} - {col['column_comment'] or '无注释'}")
            
            # 查看公司数据样例
            print(f"\n📊 feelec_company 数据样例:")
            company_samples = self.execute_query("""
                SELECT feelec_company_id, feelec_name, feelec_short_name, feelec_type
                FROM feelec_company 
                WHERE feelec_delete = 20 
                LIMIT 10
            """)
            
            for company in company_samples:
                print(f"   🏛️ ID:{company['feelec_company_id']} 名称:{company['feelec_name']} 简称:{company['feelec_short_name']} 类型:{company['feelec_type']}")
    
    def analyze_ticket_relations(self):
        """分析工单与用户、部门、公司的关联关系"""
        print(f"\n🔗 分析工单关联关系...")
        print("=" * 60)
        
        # 分析工单表中的关联字段
        print("📋 工单表中的关联字段:")
        ticket_columns = self.execute_query("""
            SELECT COLUMN_NAME as column_name, COLUMN_TYPE as column_type, COLUMN_COMMENT as column_comment
            FROM information_schema.columns
            WHERE table_schema = 'ticket' AND table_name = 'feelec_ticket'
            AND (COLUMN_NAME LIKE '%user%' OR COLUMN_NAME LIKE '%department%' OR COLUMN_NAME LIKE '%company%'
                 OR COLUMN_NAME LIKE '%manager%' OR COLUMN_NAME LIKE '%publisher%' OR COLUMN_NAME LIKE '%processor%')
            ORDER BY ordinal_position
        """)
        
        for col in ticket_columns:
            print(f"   🔗 {col['column_name']}: {col['column_type']} - {col['column_comment'] or '无注释'}")
        
        # 查看实际的关联数据
        print(f"\n📊 工单关联数据样例:")
        ticket_relations = self.execute_query("""
            SELECT 
                feelec_ticket_id,
                feelec_title,
                feelec_publisher_id,
                feelec_processor_id,
                feelec_manager_id,
                feelec_department_id,
                feelec_company_id
            FROM feelec_ticket 
            WHERE feelec_delete = 20 
            LIMIT 5
        """)
        
        for ticket in ticket_relations:
            print(f"   🎫 工单:{ticket['feelec_ticket_id']} 标题:{ticket['feelec_title'][:20]}...")
            print(f"      发布人:{ticket['feelec_publisher_id']} 处理人:{ticket['feelec_processor_id']} 经理:{ticket['feelec_manager_id']}")
            print(f"      部门:{ticket['feelec_department_id']} 公司:{ticket['feelec_company_id']}")
    
    def test_join_queries(self):
        """测试关联查询"""
        print(f"\n🧪 测试关联查询...")
        print("=" * 60)
        
        # 测试工单与用户关联
        print("📋 测试工单与用户关联:")
        user_ticket_query = """
            SELECT 
                t.feelec_ticket_id,
                t.feelec_title,
                u1.feelec_name as publisher_name,
                u2.feelec_name as processor_name,
                u3.feelec_name as manager_name
            FROM feelec_ticket t
            LEFT JOIN feelec_user u1 ON t.feelec_publisher_id = u1.feelec_user_id AND u1.feelec_delete = 20
            LEFT JOIN feelec_user u2 ON t.feelec_processor_id = u2.feelec_user_id AND u2.feelec_delete = 20
            LEFT JOIN feelec_user u3 ON t.feelec_manager_id = u3.feelec_user_id AND u3.feelec_delete = 20
            WHERE t.feelec_delete = 20
            LIMIT 3
        """
        
        user_tickets = self.execute_query(user_ticket_query)
        for ticket in user_tickets:
            print(f"   🎫 {ticket['feelec_ticket_id']}: {ticket['feelec_title'][:30]}...")
            print(f"      发布人:{ticket['publisher_name']} 处理人:{ticket['processor_name']} 经理:{ticket['manager_name']}")
        
        # 测试工单与部门关联
        print(f"\n📋 测试工单与部门关联:")
        dept_ticket_query = """
            SELECT 
                t.feelec_ticket_id,
                t.feelec_title,
                d.feelec_name as department_name
            FROM feelec_ticket t
            LEFT JOIN feelec_member_department d ON t.feelec_department_id = d.feelec_department_id AND d.feelec_delete = 20
            WHERE t.feelec_delete = 20
            LIMIT 3
        """
        
        dept_tickets = self.execute_query(dept_ticket_query)
        for ticket in dept_tickets:
            print(f"   🎫 {ticket['feelec_ticket_id']}: {ticket['feelec_title'][:30]}...")
            print(f"      部门:{ticket['department_name']}")
        
        # 测试工单与公司关联
        print(f"\n📋 测试工单与公司关联:")
        company_ticket_query = """
            SELECT 
                t.feelec_ticket_id,
                t.feelec_title,
                c.feelec_name as company_name
            FROM feelec_ticket t
            LEFT JOIN feelec_company c ON t.feelec_company_id = c.feelec_company_id AND c.feelec_delete = 20
            WHERE t.feelec_delete = 20
            LIMIT 3
        """
        
        company_tickets = self.execute_query(company_ticket_query)
        for ticket in company_tickets:
            print(f"   🎫 {ticket['feelec_ticket_id']}: {ticket['feelec_title'][:30]}...")
            print(f"      公司:{ticket['company_name']}")
    
    def close(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()

def main():
    """主函数"""
    print("🔍 工单系统用户、部门、主体数据库分析")
    print("=" * 80)
    
    analyzer = TicketDatabaseAnalyzer()
    
    try:
        if analyzer.connect():
            analyzer.analyze_user_tables()
            analyzer.analyze_department_tables()
            analyzer.analyze_company_tables()
            analyzer.analyze_ticket_relations()
            analyzer.test_join_queries()
        else:
            print("❌ 数据库连接失败")
    finally:
        analyzer.close()
    
    print("\n" + "=" * 80)
    print("🎉 数据库分析完成！")

if __name__ == "__main__":
    main()
