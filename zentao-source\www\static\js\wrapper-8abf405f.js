var ie=Object.defineProperty,re=Object.defineProperties;var ae=Object.getOwnPropertyDescriptors;var D=Object.getOwnPropertySymbols;var ce=Object.prototype.hasOwnProperty,de=Object.prototype.propertyIsEnumerable;var N=(e,t,n)=>t in e?ie(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,u=(e,t)=>{for(var n in t||(t={}))ce.call(t,n)&&N(e,n,t[n]);if(D)for(var n of D(t))de.call(t,n)&&N(e,n,t[n]);return e},b=(e,t)=>re(e,ae(t));var $=(e,t,n)=>new Promise((s,i)=>{var c=d=>{try{l(n.next(d))}catch(o){i(o)}},r=d=>{try{l(n.throw(d))}catch(o){i(o)}},l=d=>d.done?s(d.value):Promise.resolve(d.value).then(c,r);l((n=n.apply(e,t)).next())});import{aG as R,bT as le,bU as ue,S as G,d as z,h as w,A as U,F as T,o as h,ad as k,f as a,bV as q,aR as S,bW as O,bX as X,bY as Y,c as p,bZ as K,b_ as J,L as Z,p as W,q as g,a5 as F,x as P,a9 as m,z as f,b$ as v,a6 as x,e as L,al as he,c0 as M,c1 as we}from"./index.js";import{t as E,u as Q,c as pe,d as ge,i as ve,g as ye,e as _e,f as fe,O as Se,h as Ce,F as Re,N as me,o as xe,j as Fe,b as ze,l as Ee}from"./useSync.hook-14394fcc.js";import"./plugin-37914809.js";import"./icon-bb3d09e7.js";import"./tables_list-f613fa36.js";const be=(e,t,n,s)=>{const i=e,c=t,r={width:1,height:1},l=parseFloat((i/c).toFixed(5)),d=()=>{const C=parseFloat((window.innerWidth/window.innerHeight).toFixed(5));n&&(C>l?(r.width=parseFloat((window.innerHeight*l/i).toFixed(5)),r.height=parseFloat((window.innerHeight/c).toFixed(5)),n.style.transform=`scale(${r.width}, ${r.height})`):(r.height=parseFloat((window.innerWidth/l/c).toFixed(5)),r.width=parseFloat((window.innerWidth/i).toFixed(5)),n.style.transform=`scale(${r.width}, ${r.height})`),s&&s(r))},o=E(()=>{d()},200);return{calcRate:d,windowResize:()=>{window.addEventListener("resize",o)},unWindowResize:()=>{window.removeEventListener("resize",o)}}},$e=(e,t,n,s)=>{const i=e,c=t,r={width:1,height:1},l=parseFloat((i/c).toFixed(5)),d=()=>{n&&(r.height=parseFloat((window.innerWidth/l/c).toFixed(5)),r.width=parseFloat((window.innerWidth/i).toFixed(5)),n.style.transform=`scale(${r.width}, ${r.height})`,s&&s(r))},o=E(()=>{d()},200);return{calcRate:d,windowResize:()=>{window.addEventListener("resize",o)},unWindowResize:()=>{window.removeEventListener("resize",o)}}},Le=(e,t,n,s)=>{const i=e,c=t,r={height:1,width:1},l=parseFloat((i/c).toFixed(5)),d=()=>{n&&(r.width=parseFloat((window.innerHeight*l/i).toFixed(5)),r.height=parseFloat((window.innerHeight/c).toFixed(5)),n.style.transform=`scale(${r.width}, ${r.height})`,s&&s(r))},o=E(()=>{d()},200);return{calcRate:d,windowResize:()=>{window.addEventListener("resize",o)},unWindowResize:()=>{window.removeEventListener("resize",o)}}},Te=(e,t,n,s)=>{const i={width:1,height:1},c=()=>{n&&(i.width=parseFloat((window.innerWidth/e).toFixed(5)),i.height=parseFloat((window.innerHeight/t).toFixed(5)),n.style.transform=`scale(${i.width}, ${i.height})`,s&&s(i))},r=E(()=>{c()},200);return{calcRate:c,windowResize:()=>{window.addEventListener("resize",r)},unWindowResize:()=>{window.removeEventListener("resize",r)}}},ee={},A={echarts:le},te=e=>{if(!e.events)return{};const t={};for(const i in e.events.baseEvent){const c=e.events.baseEvent[i];c&&(t[i]=ke(c))}const n=e.events.advancedEvents||{},s={[R.VNODE_BEFORE_MOUNT](i){ee[e.id]=i.component;const c=(n[R.VNODE_BEFORE_MOUNT]||"").trim();j(c,i)},[R.VNODE_MOUNTED](i){const c=(n[R.VNODE_MOUNTED]||"").trim();j(c,i)}};return u(u({},t),s)};function ke(e){try{return new Function(`
      return (
        async function(mouseEvent){
          ${e}
        }
      )`)()}catch(t){console.error(t)}}function j(e,t){try{Function(`
      "use strict";
      return (
        async function(e, components, node_modules){
          const {${Object.keys(A).join()}} = node_modules;
          ${e}
        }
      )`)().bind(t==null?void 0:t.component)(t,ee,A)}catch(n){console.error(n)}}const ne=(e,t)=>({zIndex:t+1,left:`${e.x}px`,top:`${e.y}px`}),Oe=(e,t)=>({width:`${t?t*e.w:e.w}px`,height:`${t?t*e.h:e.h}px`}),B=(e,t,n)=>e?{width:`${n?n*e.w:e.w}px`,height:`${n?n*e.h-t:e.h-t}px`}:{},oe=e=>({display:e.hide?"none":"block"}),se=e=>{const t={};return e&&e.overFlowHidden&&(t.overflow="hidden"),t},We=e=>{const t=e.selectColor?{background:e.background}:{background:`url(${e.backgroundImage}) center center / cover no-repeat !important`};return u({position:"relative",width:e.width?`${e.width||100}px`:"100%",height:e.height?`${e.height}px`:"100%"},t)},Pe=()=>{if(window.chartData)return window.chartData;const t=document.location.hash.split("/"),n=t&&t[t.length-1],s=ue(G.GO_CHART_STORAGE_LIST);if(!!s){for(let i=0;i<s.length;i++)if(n.toString()===s[i].id)return s[i]}};const He=z({__name:"index",props:{groupData:{type:Object,required:!0},themeSetting:{type:Object,required:!0},themeColor:{type:Object,required:!0},groupIndex:{type:Number,required:!0}},setup(e){return(t,n)=>(h(!0),w(T,null,U(e.groupData.groupList,s=>(h(),w("div",{class:k(["chart-item",a(q)(s.styles.animations)]),key:s.id,style:S(u(u(u(u(u(u({},a(ne)(s.attr,e.groupIndex)),a(O)(s.styles)),a(X)(s.styles)),a(oe)(s.status)),a(se)(s.preview)),a(Y)(s.styles)))},[(h(),p(Z(s.chartConfig.chartKey),K({id:s.id,chartConfig:s,themeSetting:e.themeSetting,themeColor:e.themeColor,style:u({},a(Oe)(s.attr))},J(a(te)(s))),null,16,["id","chartConfig","themeSetting","themeColor","style"]))],6))),128))}});var Ie=W(He,[["__scopeId","data-v-3933053a"]]);const De={key:1},Ne=z({__name:"index",setup(e){const t=g(!1),{initDataPond:n,clearMittDataPondMap:s}=pe(),i=Q(),c=F(()=>i.editCanvasConfig.chartThemeSetting),r=F(()=>{const l=i.editCanvasConfig.chartThemeColor;return ge[l]});return s(),P(()=>{n(i.requestGlobalConfig)}),(l,d)=>(h(!0),w(T,null,U(a(i).componentList,(o,y)=>{var _,C,H,I;return h(),w("div",{key:o.id,class:"chart-item",style:S(u(u(u(u(u(u({},a(ne)(o.attr,y)),a(O)(o.styles)),a(X)(o.styles)),a(oe)(o.status)),a(se)(o.preview)),a(Y)(o.styles)))},[a(ve)(o)&&!((_=o.option)!=null&&_.isDeleted)?(h(),p(a(Se),{key:0,title:a(ye)(o),attr:o.attr,titleSize:a(_e)(o),titleStyle:a(fe)(o)},null,8,["title","attr","titleSize","titleStyle"])):m("",!0),a(Ce)(o)&&!((C=o.option)!=null&&C.isDeleted)?(h(),p(a(Re),{key:1,class:"filter-content",componentIdx:o.id,attr:o.attr,filters:o.chartConfig.filters,type:o.chartConfig.category},null,8,["componentIdx","attr","filters","type"])):m("",!0),f("div",{class:k(a(q)(o.styles.animations))},[o.isGroup?(h(),p(a(Ie),{key:0,groupData:o,groupIndex:y,themeSetting:c.value,themeColor:r.value},null,8,["groupData","groupIndex","themeSetting","themeColor"])):(h(),w("div",De,[(H=o.option)!=null&&H.isDeleted?(h(),p(a(me),{key:0,item:o,style:S(b(u({},a(B)(o.attr,0)),{lineHeight:o.attr.h+"px"}))},null,8,["item","style"])):m("",!0),(I=o.option)!=null&&I.isDeleted?m("",!0):(h(),p(Z(o.chartConfig.chartKey),K({key:1,id:o.id,chartConfig:o,themeSetting:c.value,themeColor:r.value,style:u({},a(B)(o.attr,a(xe)(o)+a(Fe)(o))),scrollMode:t.value?"manual":"auto"},J(a(te)(o))),null,16,["id","chartConfig","themeSetting","themeColor","style","scrollMode"]))]))],2)],4)}),128))}});var V=W(Ne,[["__scopeId","data-v-64f2dc65"]]);const Me=e=>{const t=g(),n=g(),s=g(e.editCanvasConfig.width),i=g(e.editCanvasConfig.height);return P(()=>{switch(e.editCanvasConfig.previewScaleType){case v.FIT:(()=>{const{calcRate:c,windowResize:r,unWindowResize:l}=be(s.value,i.value,n.value);c(),r(),x(()=>{l()})})();break;case v.SCROLL_Y:(()=>{const{calcRate:c,windowResize:r,unWindowResize:l}=$e(s.value,i.value,n.value,d=>{const o=t.value;o.style.width=`${s.value*d.width}px`,o.style.height=`${i.value*d.height}px`});c(),r(),x(()=>{l()})})();break;case v.SCROLL_X:(()=>{const{calcRate:c,windowResize:r,unWindowResize:l}=Le(s.value,i.value,n.value,d=>{const o=t.value;o.style.width=`${s.value*d.width}px`,o.style.height=`${i.value*d.height}px`});c(),r(),x(()=>{l()})})();break;case v.FULL:(()=>{const{calcRate:c,windowResize:r,unWindowResize:l}=Te(s.value,i.value,n.value);c(),r(),x(()=>{l()})})();break}}),{entityRef:t,previewRef:n}};const Ae=z({__name:"index",setup(e){const{dataSyncFetch:t}=ze(),n=Q(),s=Ee(()=>he(()=>import("./index-aa1cb6be.js"),["static/js/index-aa1cb6be.js","static/js/useSync.hook-14394fcc.js","static/css/useSync.hook-b8fcaf56.css","static/js/index.js","static/css/index-d2bdc124.css","static/js/plugin-37914809.js","static/js/icon-bb3d09e7.js","static/js/tables_list-f613fa36.js"])),i=F(()=>u(u({},We(n.editCanvasConfig)),O(n.editCanvasConfig))),c=F(()=>{const d=n.editCanvasConfig.previewScaleType;return d===v.SCROLL_Y||d===v.SCROLL_X}),{entityRef:r,previewRef:l}=Me(n.getStorageInfo);return P(()=>$(this,null,function*(){yield t("preview")})),(d,o)=>(h(),w(T,null,[f("div",{class:k(`go-preview ${a(n).editCanvasConfig.previewScaleType}`)},[c.value?(h(),w("div",{key:0,ref_key:"entityRef",ref:r,class:"go-preview-entity"},[f("div",{ref_key:"previewRef",ref:l,class:"go-preview-scale"},[f("div",{style:S(i.value)},[L(a(V))],4)],512)],512)):(h(),w("div",{key:1,ref_key:"previewRef",ref:l,class:"go-preview-scale"},[f("div",{style:S(i.value)},[L(a(V))],4)],512))],2),L(a(s))],64))}});var je=W(Ae,[["__scopeId","data-v-187c9882"]]);const Ye=z({__name:"wrapper",setup(e){let t=g(Date.now());return[M.JSON,M.CHART].forEach(n=>{!window.opener||window.opener.addEventListener(n,s=>$(this,null,function*(){const i=yield Pe();we(G.GO_CHART_STORAGE_LIST,[b(u({},s.detail),{id:i.id})]),t.value=Date.now()}))}),(n,s)=>(h(),p(je,{key:a(t)}))}});export{Ye as default};
