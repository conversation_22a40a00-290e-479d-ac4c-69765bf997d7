#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入功能修复
"""

import requests
import json

def test_import_data():
    """测试导入数据功能"""
    try:
        print("🔍 测试导入数据功能...")
        
        # 模拟前端发送的数据（包含Excel日期序列号）
        test_data = {
            "items": [
                {
                    "sequence_number": 999,
                    "work_dimension": "测试维度",
                    "work_theme": "测试导入功能",
                    "supervision_source": "测试来源",
                    "work_content": "测试导入Excel日期格式转换功能",
                    "is_annual_assessment": "否",
                    "completion_deadline": "45807",  # Excel日期序列号
                    "overall_progress": "X 未启动",
                    "财险_status": "X",
                    "寿险_status": "X",
                    "金租_status": "X",
                    "资管_status": "X",
                    "广租_status": "X",
                    "通盛_status": "X",
                    "担保_status": "X",
                    "小贷_status": "X",
                    "保理_status": "X",
                    "不动产_status": "X",
                    "征信_status": "X",
                    "金服_status": "X",
                    "本部_status": "X"
                }
            ]
        }
        
        response = requests.post(
            'http://127.0.0.1:8001/api/v1/simple-supervision/import-data',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 导入成功: {result.get('message', '无消息')}")
            
            # 验证导入的数据
            print("\n🔍 验证导入的数据...")
            get_response = requests.get('http://127.0.0.1:8001/api/v1/simple-supervision/items')
            
            if get_response.status_code == 200:
                items = get_response.json().get('data', [])
                test_item = None
                
                for item in items:
                    if item['work_theme'] == '测试导入功能':
                        test_item = item
                        break
                
                if test_item:
                    print(f"✅ 找到测试数据:")
                    print(f"   工作主题: {test_item['work_theme']}")
                    print(f"   完成时限: {test_item['completion_deadline']} (应该是2025-05-30)")
                    print(f"   序号: {test_item['sequence_number']}")
                else:
                    print("❌ 未找到测试数据")
            else:
                print(f"❌ 获取数据失败: {get_response.text}")
                
        else:
            print(f"❌ 导入失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始测试导入功能修复...")
    test_import_data()
    print("\n🎉 测试完成！")
