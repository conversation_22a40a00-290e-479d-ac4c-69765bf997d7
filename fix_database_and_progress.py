#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复数据库结构并实现新的进度状态系统
"""

import pymysql
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

# 数据库配置
DB_CONFIG = {
    'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'cyh',
    'password': 'Qq188788',
    'database': 'kanban2',
    'charset': 'utf8mb4'
}

def fix_database_structure():
    """修复数据库结构"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        logging.info("🔧 开始修复数据库结构")
        logging.info("=" * 60)
        
        # 1. 添加 overall_progress 字段到 supervision_items 表
        logging.info("步骤1: 添加 overall_progress 字段")
        try:
            alter_query = """
            ALTER TABLE supervision_items 
            ADD COLUMN overall_progress VARCHAR(50) DEFAULT 'X 未启动' COMMENT '整体进度状态'
            """
            cursor.execute(alter_query)
            logging.info("✅ 成功添加 overall_progress 字段")
        except Exception as e:
            if "Duplicate column name" in str(e):
                logging.info("ℹ️  overall_progress 字段已存在")
            else:
                raise e
        
        # 2. 修改 company_progress 表的状态枚举
        logging.info("步骤2: 更新公司进度状态枚举")
        try:
            modify_enum_query = """
            ALTER TABLE company_progress 
            MODIFY COLUMN status ENUM(
                '已完成',      
                '进行中',      
                '逾期',        
                '未启动',      
                '不需要执行'   
            ) DEFAULT '未启动' COMMENT '执行状态'
            """
            cursor.execute(modify_enum_query)
            logging.info("✅ 成功更新状态枚举")
        except Exception as e:
            logging.warning(f"⚠️  更新状态枚举失败: {e}")
        
        # 3. 将现有的"未开始"状态更新为"未启动"
        logging.info("步骤3: 更新现有数据状态")
        update_status_query = """
        UPDATE company_progress 
        SET status = '未启动' 
        WHERE status = '未开始'
        """
        cursor.execute(update_status_query)
        updated_rows = cursor.rowcount
        logging.info(f"✅ 更新了 {updated_rows} 条记录的状态")
        
        # 4. 添加整体进度计算相关字段
        logging.info("步骤4: 添加进度计算字段")
        try:
            add_progress_fields = """
            ALTER TABLE supervision_items 
            ADD COLUMN completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率百分比',
            ADD COLUMN progress_status ENUM('已完成', '进行中', '逾期', '未启动', '不需要执行') 
                DEFAULT '未启动' COMMENT '整体进度状态'
            """
            cursor.execute(add_progress_fields)
            logging.info("✅ 成功添加进度计算字段")
        except Exception as e:
            if "Duplicate column name" in str(e):
                logging.info("ℹ️  进度计算字段已存在")
            else:
                logging.warning(f"⚠️  添加进度计算字段失败: {e}")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        logging.info("=" * 60)
        logging.info("🎉 数据库结构修复完成")
        return True
        
    except Exception as e:
        logging.error(f"❌ 修复数据库结构失败: {e}")
        return False

def calculate_overall_progress(supervision_item_id, cursor):
    """计算督办事项的整体进度"""
    try:
        # 获取该督办事项的所有公司进度
        progress_query = """
        SELECT cp.status, c.company_name
        FROM company_progress cp
        JOIN companies c ON cp.company_id = c.id
        WHERE cp.supervision_item_id = %s AND c.is_active = 1
        """
        cursor.execute(progress_query, (supervision_item_id,))
        progress_data = cursor.fetchall()
        
        if not progress_data:
            return '未启动', 0.0, 'X 未启动'
        
        # 统计各状态数量
        statuses = [row[0] for row in progress_data]
        total_companies = len(statuses)
        
        # 排除"不需要执行"的公司
        valid_statuses = [s for s in statuses if s != '不需要执行']
        valid_count = len(valid_statuses)
        
        if valid_count == 0:
            return '不需要执行', 100.0, '— 不需要执行'
        
        # 统计各状态
        completed = valid_statuses.count('已完成')
        in_progress = valid_statuses.count('进行中')
        overdue = valid_statuses.count('逾期')
        not_started = valid_statuses.count('未启动')
        
        # 计算完成率
        completion_rate = (completed / valid_count) * 100
        
        # 判定整体状态
        if overdue > 0:
            overall_status = '逾期'
            display_status = '！逾期'
        elif completed == valid_count:
            overall_status = '已完成'
            display_status = '√ 已完成'
        elif in_progress > 0:
            overall_status = '进行中'
            display_status = 'O 进行中'
        else:
            overall_status = '未启动'
            display_status = 'X 未启动'
        
        return overall_status, completion_rate, display_status
        
    except Exception as e:
        logging.error(f"计算进度失败: {e}")
        return '未启动', 0.0, 'X 未启动'

def update_all_progress():
    """更新所有督办事项的整体进度"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        logging.info("🔄 开始更新所有督办事项的整体进度")
        logging.info("=" * 60)
        
        # 获取所有督办事项
        items_query = "SELECT id, work_theme FROM supervision_items WHERE deleted_at IS NULL"
        cursor.execute(items_query)
        items = cursor.fetchall()
        
        updated_count = 0
        for item in items:
            item_id, work_theme = item
            
            # 计算整体进度
            overall_status, completion_rate, display_status = calculate_overall_progress(item_id, cursor)
            
            # 更新数据库
            update_query = """
            UPDATE supervision_items 
            SET overall_progress = %s, 
                completion_rate = %s, 
                progress_status = %s,
                updated_at = NOW()
            WHERE id = %s
            """
            cursor.execute(update_query, (display_status, completion_rate, overall_status, item_id))
            
            logging.info(f"✅ 更新: {work_theme} → {display_status} ({completion_rate:.1f}%)")
            updated_count += 1
        
        connection.commit()
        cursor.close()
        connection.close()
        
        logging.info("=" * 60)
        logging.info(f"🎉 成功更新了 {updated_count} 个督办事项的进度")
        return True
        
    except Exception as e:
        logging.error(f"❌ 更新整体进度失败: {e}")
        return False

def detect_and_mark_overdue():
    """检测并标记逾期状态"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        logging.info("⏰ 开始检测并标记逾期状态")
        logging.info("=" * 60)
        
        today = datetime.now().date()
        
        # 查找所有已逾期的督办事项
        overdue_query = """
        SELECT id, work_theme, completion_deadline
        FROM supervision_items 
        WHERE completion_deadline < %s AND deleted_at IS NULL
        """
        cursor.execute(overdue_query, (today,))
        overdue_items = cursor.fetchall()
        
        logging.info(f"📊 发现 {len(overdue_items)} 个逾期督办事项")
        
        overdue_count = 0
        for item in overdue_items:
            item_id, work_theme, deadline = item
            
            # 将该督办事项下所有"未启动"和"进行中"的公司状态改为"逾期"
            update_overdue_query = """
            UPDATE company_progress 
            SET status = '逾期', updated_at = NOW()
            WHERE supervision_item_id = %s 
            AND status IN ('未启动', '进行中')
            """
            cursor.execute(update_overdue_query, (item_id,))
            affected_rows = cursor.rowcount
            
            if affected_rows > 0:
                logging.info(f"⚠️  {work_theme} (截止:{deadline}) → 标记 {affected_rows} 家公司为逾期")
                overdue_count += affected_rows
        
        connection.commit()
        cursor.close()
        connection.close()
        
        logging.info("=" * 60)
        logging.info(f"🎯 共标记了 {overdue_count} 条记录为逾期状态")
        return True
        
    except Exception as e:
        logging.error(f"❌ 检测逾期状态失败: {e}")
        return False

def main():
    """主函数"""
    logging.info("🚀 开始修复数据库并实现新的进度状态系统")
    logging.info("=" * 80)
    
    # 1. 修复数据库结构
    if not fix_database_structure():
        logging.error("❌ 数据库结构修复失败，停止执行")
        return
    
    # 2. 检测并标记逾期状态
    if not detect_and_mark_overdue():
        logging.error("❌ 逾期状态检测失败")
        return
    
    # 3. 更新所有督办事项的整体进度
    if not update_all_progress():
        logging.error("❌ 整体进度更新失败")
        return
    
    logging.info("=" * 80)
    logging.info("🎉 所有修复工作完成！")
    logging.info("💡 现在可以重新启动后端服务测试功能")

if __name__ == "__main__":
    main()
