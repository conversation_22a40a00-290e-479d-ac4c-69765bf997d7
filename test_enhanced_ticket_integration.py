#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版工单集成功能
验证完整的工单内容显示，包括子公司需求信息
"""

import requests
import json
from datetime import datetime

class TicketIntegrationTester:
    """工单集成功能测试器"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000/api/v1/ticket-integration"
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token'  # 测试用token
        }
    
    def test_dashboard_stats(self):
        """测试仪表盘统计"""
        print("\n🎯 测试仪表盘统计...")
        
        try:
            response = requests.get(f"{self.base_url}/dashboard-stats", headers=self.headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ 仪表盘统计获取成功")
                
                if 'data' in data:
                    stats = data['data']
                    print(f"      📊 项目统计: {stats.get('project_stats', {})}")
                    print(f"      📊 工单统计: {stats.get('ticket_stats', {})}")
                    return True
                else:
                    print(f"   ❌ 响应数据格式错误: {data}")
                    return False
            else:
                print(f"   ❌ 请求失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            return False
    
    def test_projects_list(self):
        """测试项目列表"""
        print("\n🎯 测试项目列表...")
        
        try:
            response = requests.get(f"{self.base_url}/projects", headers=self.headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ 项目列表获取成功")
                
                if 'data' in data and isinstance(data['data'], list):
                    projects = data['data']
                    print(f"      📋 找到 {len(projects)} 个项目")
                    
                    if projects:
                        # 显示前3个项目
                        for i, project in enumerate(projects[:3], 1):
                            print(f"        {i}. {project.get('feelec_name')} (ID: {project.get('feelec_project_id')})")
                        
                        return projects[0]['feelec_project_id'] if projects else None
                    else:
                        print(f"      ⚠️  没有找到项目")
                        return None
                else:
                    print(f"   ❌ 响应数据格式错误: {data}")
                    return None
            else:
                print(f"   ❌ 请求失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            return None
    
    def test_project_tickets(self, project_id):
        """测试项目工单列表"""
        print(f"\n🎯 测试项目 {project_id} 的工单列表...")
        
        try:
            response = requests.get(f"{self.base_url}/projects/{project_id}/tickets", headers=self.headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ 项目工单列表获取成功")
                
                if 'data' in data and isinstance(data['data'], list):
                    tickets = data['data']
                    print(f"      📋 找到 {len(tickets)} 个工单")
                    
                    if tickets:
                        # 显示前3个工单
                        for i, ticket in enumerate(tickets[:3], 1):
                            print(f"        {i}. {ticket.get('feelec_title')} (ID: {ticket.get('feelec_ticket_id')})")
                        
                        return tickets[0]['feelec_ticket_id'] if tickets else None
                    else:
                        print(f"      ⚠️  该项目没有工单")
                        return None
                else:
                    print(f"   ❌ 响应数据格式错误: {data}")
                    return None
            else:
                print(f"   ❌ 请求失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            return None
    
    def test_ticket_full_content(self, ticket_id):
        """测试工单完整内容"""
        print(f"\n🎯 测试工单 {ticket_id} 的完整内容...")
        
        try:
            response = requests.get(f"{self.base_url}/tickets/{ticket_id}/full-content", headers=self.headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ 工单完整内容获取成功")
                
                if 'data' in data:
                    ticket = data['data']
                    
                    # 显示基本信息
                    print(f"      📋 工单基本信息:")
                    print(f"        编号: {ticket.get('feelec_ticket_no')}")
                    print(f"        标题: {ticket.get('feelec_title')}")
                    print(f"        状态: {ticket.get('status_name')}")
                    print(f"        优先级: {ticket.get('priority_text')}")
                    
                    # 显示发布人信息
                    if ticket.get('publisher_info'):
                        publisher = ticket['publisher_info']
                        print(f"      👤 发布人信息:")
                        print(f"        姓名: {publisher.get('feelec_name')}")
                        print(f"        电话: {publisher.get('feelec_mobile', '无')}")
                        print(f"        邮箱: {publisher.get('feelec_email', '无')}")
                        
                        if publisher.get('company_info'):
                            company = publisher['company_info']
                            print(f"        公司: {company.get('company_name')}")
                    
                    # 显示工单内容
                    if ticket.get('ticket_content'):
                        content = ticket['ticket_content']
                        print(f"      📄 工单详细内容:")
                        
                        if content.get('title'):
                            print(f"        标题: {content['title']}")
                        
                        if content.get('content'):
                            content_text = content['content']
                            # 移除HTML标签显示纯文本
                            import re
                            clean_content = re.sub(r'<[^>]+>', '', content_text)
                            if len(clean_content) > 100:
                                clean_content = clean_content[:100] + "..."
                            print(f"        内容: {clean_content}")
                        
                        # 显示其他字段
                        for key, value in content.items():
                            if key not in ['title', 'content'] and value:
                                print(f"        {key}: {value}")
                    
                    # 显示处理记录
                    if ticket.get('process_records'):
                        records = ticket['process_records']
                        print(f"      📊 处理记录: {len(records)} 条")
                    
                    return True
                else:
                    print(f"   ❌ 响应数据格式错误: {data}")
                    return False
            else:
                print(f"   ❌ 请求失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            return False
    
    def run_full_test(self):
        """运行完整测试"""
        print("🎫 工单集成功能完整测试")
        print("=" * 60)
        
        results = {
            'dashboard_stats': False,
            'projects_list': False,
            'project_tickets': False,
            'ticket_full_content': False
        }
        
        # 1. 测试仪表盘统计
        results['dashboard_stats'] = self.test_dashboard_stats()
        
        # 2. 测试项目列表
        project_id = self.test_projects_list()
        if project_id:
            results['projects_list'] = True
            
            # 3. 测试项目工单列表
            ticket_id = self.test_project_tickets(project_id)
            if ticket_id:
                results['project_tickets'] = True
                
                # 4. 测试工单完整内容
                results['ticket_full_content'] = self.test_ticket_full_content(ticket_id)
        
        # 显示测试结果
        print(f"\n🎉 测试完成！")
        print("=" * 60)
        print("📊 测试结果:")
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        success_count = sum(results.values())
        total_count = len(results)
        success_rate = (success_count / total_count) * 100
        
        print(f"\n总体成功率: {success_rate:.1f}% ({success_count}/{total_count})")
        
        if success_rate == 100:
            print("🎉 所有测试通过！工单集成功能完全可用！")
        elif success_rate >= 75:
            print("⚠️  大部分功能正常，有少量问题需要修复")
        else:
            print("❌ 存在较多问题，需要进一步调试")
        
        return results

def main():
    """主函数"""
    print("🎫 工单集成功能增强版测试")
    print("=" * 60)
    print("⚠️  请确保后端服务已启动 (http://localhost:8000)")
    print("=" * 60)
    
    tester = TicketIntegrationTester()
    results = tester.run_full_test()
    
    return results

if __name__ == "__main__":
    main()
