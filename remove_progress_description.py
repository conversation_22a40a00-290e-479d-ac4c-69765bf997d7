#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除progress_description字段并录入督办表完整数据
"""

import pymysql

def remove_progress_description_and_import_data():
    """删除progress_description字段并录入完整数据"""
    print("🔧 删除progress_description字段并录入督办表完整数据...")
    
    try:
        # 连接阿里云数据库
        connection = pymysql.connect(
            host='rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
            port=3306,
            user='cyh',
            password='Qq188788',
            database='kanban2',
            charset='utf8mb4',
            connect_timeout=30
        )
        
        print("✅ 阿里云数据库连接成功")
        
        with connection.cursor() as cursor:
            # 1. 删除progress_description字段
            print("\n🗑️  删除progress_description字段...")
            try:
                cursor.execute("ALTER TABLE supervision_items DROP COLUMN progress_description")
                print("✅ progress_description字段删除成功")
            except Exception as e:
                if "doesn't exist" in str(e):
                    print("✅ progress_description字段不存在，无需删除")
                else:
                    print(f"❌ 删除progress_description字段失败: {str(e)}")
            
            # 2. 清空现有数据
            print("\n🗑️  清空现有督办数据...")
            cursor.execute("DELETE FROM company_supervision_status")
            cursor.execute("DELETE FROM supervision_items")
            print("✅ 现有数据清空完成")
            
            # 3. 录入督办表完整数据
            print("\n📋 录入督办表完整数据...")
            
            # 督办事项数据（从督办表提取）
            items_data = [
                (1, '一、监管和制度', '建立条线ITBP团队管理办法', '3月科技例会', 
                 '各单位及部门明确对接人，以条线形式建立ITBP服务团队。完成标志：各单位提供科技工作人员分工和花名册信息，变更科技人员须备案科委办。', 
                 '否', '5月末', '√ 已完成'),
                
                (2, '一、监管和制度', '建立项目红绿灯管理办法', '3月科技例会',
                 '开展重点项目周跟踪机制，推行"亮黄牌"机制，明确子公司数字化应用覆盖要求。完成标志：各单位参考集团建立科技项目周跟踪机制，确保提前识别项目风险并解决，至年底所有重点项目均按计划上线。',
                 '否', '4月末', '√ 已完成'),
                
                (3, '一、监管和制度', '印发8个信息化管理制度', '5月科技例会',
                 '各子公司参照集团印发的信息化管理制度，印发包括科技风险、项目管理等8个制度，并落地执行。完成标志：各单位对照集团信息化制度，检查和印发自身信息化管理制度清单。',
                 '否', '8月末', 'O进行中'),
                
                (4, '一、监管和制度', '印发非信创采购管理制度', '7月科技例会',
                 '科委办及各单位参照广投集团印发非信创采购管理制度，含含非信创设备评审流程，并落地执行。完成标志：科委办及各单位对照广投制度，印发非信创制度。',
                 '否', '10月末', 'X 未启动'),
                
                (5, '二、数据治理和系统覆盖', '第一批次数据治理', '3月科技例会',
                 '持续开展数据治理工作，准确率、完整率、及时率达到要求。完成标志：各单位配合完成集团下达的财务，风控和经营18项指标数据治理，三率（准确、及时、T+1）90%。',
                 '是', '4月末', '√ 已完成'),
                
                (6, '二、数据治理和系统覆盖', '第二批次数据治理', '5月科技例会',
                 '持续开展数据治理工作，准确率、完整率、及时率达到要求。完成标志：涉及经营、风控、人力的152项数据治理。三率（准确、及时、T+1）90%。',
                 '是', '11月末', 'O进行中'),
                
                (7, '二、数据治理和系统覆盖', '100%落实集团本部各管理条线要求', '3月科技例会',
                 '以集团条线要求为基准，梳理本部各条线系统使用情况，各单位落实集团管理条线的覆盖度达到100%。完成标志：集团管理条线提出覆盖要求，科委办统筹推广，各单位负责落地，确保本部条线管理的系统在各单位100%覆盖使用。',
                 '是', '10月末', 'O进行中'),
                
                (8, '二、数据治理和系统覆盖', '业务中台接入', '5月科技例会',
                 '重点租赁、通盛租赁、广西租赁、金控资管接入业务中台的事项需在6月启动接入工作，与科委办确认对接方案、实施路径、时间计划等，并纳入年终考核。完成标志：四家公司业务系统接入业务中台，统一集团的信用评级等风险管理。',
                 '是', '11月末', 'O进行中'),
                
                (9, '三、网络和数据安全', '完成数据防泄漏方案并实施', '4月科技例会',
                 '制定和实施DLP数据防泄漏方案。完成标志：各单位配合DLP数据防泄漏方案的评审和实施。',
                 '否', '6月末', '！逾期'),
                
                (10, '三、网络和数据安全', '集团金投云方案的意见征集', '4月科技例会',
                 '建立"统一云平台"、"统一灾备体系"、"统一网络安全"完成标志：科委办统筹建设，各单位配合完成金投云方案的确定，完成第一轮意见征集。',
                 '是', '5月末', '√ 已完成'),
                
                (11, '三、网络和数据安全', '集团金投云方案完成上线', '5月科技例会',
                 '建立"统一云平台"、"统一灾备体系"、"统一网络安全"完成标志：科委办统筹建设，各单位配合完成金投云方案的确定，并最终顺利上线。',
                 '是', '12月末', 'O进行中'),
                
                (12, '三、网络和数据安全', '信创改造计划第一轮摸底', '4月科技例会',
                 '各单位梳理自身所有系统，确定信创紧急重要程度、环境要求、改造成本和改造时间计划，报科委办审批并备案。完成标志：完成集团信创改造信息收集表。',
                 '否', '5月末', '√ 已完成'),
                
                (13, '三、网络和数据安全', '明确信创改造计划，完成2025年度改造计划', '5月科技例会',
                 '各单位梳理自身所有系统，确定信创紧急重要程度、环境要求、改造成本和改造时间计划，报科委办审批并备案。完成标志：完成集团信创方案，结合金投云确定2025年改造任务。',
                 '否', '6月末', '√ 已完成'),
                
                (14, '三、网络和数据安全', '建立异地数据备份', '4月科技例会',
                 '通盛、广租两家租赁公司的三级等保系统被审计指出异地数据备份的问题，其他公司进行排查并制定改进方案。完成标志：通盛、广租的异地数据灾备备份完成审计和通过，其他单位也同步推动完成相关工作。',
                 '否', '9月末', 'O进行中'),
                
                (15, '三、网络和数据安全', '补例外采购备案流程', '7月科技例会',
                 '2024年以来的设备采购如涉及非国产电脑，各企业都需要补例外采购备案流程。完成标志：2024年以来的设备非国产电脑采购按广投集团要求完成备案流程。',
                 '否', '8月初', 'O进行中'),
                
                (16, '三、网络和数据安全', '信创电脑卸载Windows系统', '7月科技例会',
                 '2024年以来采购的信创电脑如安装了Windows系统的，均需要卸载。完成标志：2024年以来采购信创电脑完成Windows卸载。',
                 '否', '8月初', 'O进行中'),
                
                (17, '三、网络和数据安全', '信创设备采购', '7月科技例会',
                 '2025年的信创设备采购专项工作需要在11月底完成，各企业需紧盯进度。完成标志：完成2025年度的信创设备采购。',
                 '否', '9月末', 'X 未启动'),
                
                (18, '三、网络和数据安全', '信创正式报告', '7月科技例会',
                 '夯实集团信创改造计划，形式正式报告，上报广投。完成标志：完成信创报告的上报。',
                 '否', '8月末', 'O进行中'),
                
                (19, '四、项目建设', '诉讼案件导入', '3月科技例会',
                 '推动集团所有案件信息都已完成导入诉讼系统。',
                 '否', '5月末', '√ 已完成'),
                
                (20, '四、项目建设', '业财一体', '4月科技例会',
                 '推动解决子公司业财数据不一致的问题，消除业财数据差异。完成标志：系统改造部分，各单位牵头与数字金服完成技术改造上线；业财数据核对方面，财务部牵头与各单位完成数据核对，消除差异。',
                 '是', '6月末', '！逾期'),
                
                (21, '四、项目建设', '集团资产管理专项项目', '5月科技例会',
                 '从投资与运营入手，覆盖资产类型全周期、内外部场景及企业不同阶段，助力集团资产高效管理。完成标志：保全部牵头，科委办统筹，不动产与各单位配合，数字金服实施，确保系统按计划推进立项、研发、上线。',
                 '是', '12月末', 'O进行中'),
                
                (22, '四、项目建设', '广投司库对接项目', '7月科技例会',
                 '对接广投司库，实现账户自动化和智能化管理、资金集中专业化运营管理、资金预算精细化管理等。完成标志：财务部牵头，科委办统筹，各单位配合，数字金服实施，确保系统按计划推进立项、研发、上线。',
                 '是', '12月末', 'O进行中'),
                
                (23, '四、项目建设', '集团档案管理系统', '7月科技例会',
                 '建设统一集团信贷、诉讼、法规等档案管理系统，提升集团内部各部门的运营效率。完成标志：办公室牵头，科委办统筹，各单位配合，数字金服实施，确保系统按计划推进立项、研发、上线。',
                 '是', '12月末', 'O进行中'),
                
                (24, '五、条线管理', '制定集团数智规划', '3月科技例会',
                 '制定集团制定数字化建设规划。完成标志：印发集团数字化建设规划。',
                 '否', '7月末', '√ 已完成'),
                
                (25, '五、条线管理', '加强外包管理，完成自查', '5月科技例会',
                 '涉及外包的子公司排查是否建立外包管理办法，对外包人员系统操作和数据查看的权限是否控制在合理的范围内。完成标志：各单位排查是否建立制度，确保都已完成制度印发，同时梳理外包权限，确保无过度授权，并在科技月度例会汇报。',
                 '否', '6月末', '√ 已完成'),
                
                (26, '五、条线管理', '规范招标采购流程，完成自查', '5月科技例会',
                 '各单位排查招标采购过程，严禁出现因串标、围标的问题。完成标志：各单位排查采购过程，确保无串标、围标等采购问题，并在科技月度例会汇报。',
                 '否', '6月末', '√ 已完成'),
                
                (27, '五、条线管理', '提高系统应用程度，完成自查', '5月科技例会',
                 '排查存在使用效率较低的系统，并提出改进意见并执行。完成标志：各单位排查自身上线的系统并分析现状、建议，确保无使用效率较低的系统，并在科技月度例会汇报。',
                 '否', '6月末', '√ 已完成'),
                
                (28, '五、条线管理', '按授权要求进行项目备案，完成自查', '5月科技例会',
                 '子公司按照集团授权要求排查达到授权要求的项目，均需要向科委办进行OA备案。完成标志：各单位排查项目立项过程，在集团授权方案下确保达到授权金额下的项目都按授权要求进行备案等，并在科技月度例会汇报。',
                 '否', '6月末', '√ 已完成'),
                
                (29, '五、条线管理', '集团科技项目投资计划调整', '5月科技例会',
                 '各单位在下周梳理完成对取消、修改投资额等调整的项目进行梳理，并标识原因。完成标志：8月30日完成金投集团投资计划的调整，并按广投的时间要求上报广投。',
                 '否', '8月末', 'O进行中')
            ]
            
            # 插入督办事项数据
            for item_data in items_data:
                cursor.execute("""
                    INSERT INTO supervision_items 
                    (sequence_number, work_dimension, work_theme, supervision_source, work_content, 
                     is_annual_assessment, completion_deadline, overall_progress)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, item_data)
            
            print(f"✅ 插入了 {len(items_data)} 条督办事项数据")
            
            # 4. 创建公司状态记录（根据督办表中的状态）
            print("\n📊 创建公司状态记录...")
            
            # 公司代码映射
            company_mapping = {
                '财险': 'CXBX', '寿险': 'SXBX', '金租': 'JINZU', '资管': 'ZICHAN',
                '广租': 'GUANGZU', '通盛': 'TONGSHENG', '担保': 'DANBAO', '小贷': 'XIAODAI',
                '保理': 'BAOLI', '不动产': 'BUDONGCHAN', '征信': 'ZHENGXIN', '金服': 'JINFU', '本部': 'BENBU'
            }
            
            # 从督办表提取的状态数据
            status_data = [
                # 序号1: 建立条线ITBP团队管理办法
                (1, {'财险': '√', '寿险': '√', '金租': '√', '资管': '√', '广租': '√', '通盛': '√', '担保': '√', '小贷': '√', '保理': '√', '不动产': '√', '征信': '√', '金服': '√', '本部': '√'}),
                # 序号2: 建立项目红绿灯管理办法
                (2, {'财险': '√', '寿险': '√', '金租': '√', '资管': '√', '广租': '√', '通盛': '√', '担保': '√', '小贷': '√', '保理': '√', '不动产': '√', '征信': '√', '金服': '√', '本部': '√'}),
                # 序号3: 印发8个信息化管理制度
                (3, {'财险': 'O', '寿险': 'O', '金租': 'O', '资管': 'O', '广租': 'O', '通盛': 'O', '担保': 'O', '小贷': 'O', '保理': 'O', '不动产': 'O', '征信': 'O', '金服': 'O', '本部': '√'}),
                # 序号4: 印发非信创采购管理制度
                (4, {'财险': 'X', '寿险': 'X', '金租': 'X', '资管': 'X', '广租': 'X', '通盛': 'X', '担保': 'X', '小贷': 'X', '保理': 'X', '不动产': 'X', '征信': 'X', '金服': 'X', '本部': 'X'}),
                # 序号5: 第一批次数据治理
                (5, {'财险': '√', '寿险': '—', '金租': '√', '资管': '√', '广租': '√', '通盛': '√', '担保': '√', '小贷': '√', '保理': '√', '不动产': '√', '征信': '√', '金服': '√', '本部': '√'}),
                # 序号6: 第二批次数据治理
                (6, {'财险': 'O', '寿险': '—', '金租': 'O', '资管': 'O', '广租': 'O', '通盛': 'O', '担保': 'O', '小贷': 'O', '保理': 'O', '不动产': 'O', '征信': 'O', '金服': 'O', '本部': 'O'}),
                # 序号7: 100%落实集团本部各管理条线要求
                (7, {'财险': 'O', '寿险': '—', '金租': 'O', '资管': 'O', '广租': 'O', '通盛': 'O', '担保': 'O', '小贷': 'O', '保理': 'O', '不动产': 'O', '征信': 'O', '金服': 'O', '本部': 'O'}),
                # 序号8: 业务中台接入
                (8, {'财险': '—', '寿险': '—', '金租': 'O', '资管': 'O', '广租': 'O', '通盛': 'O', '担保': '—', '小贷': '—', '保理': '—', '不动产': '—', '征信': '—', '金服': '—', '本部': '—'}),
                # 序号9: 完成数据防泄漏方案并实施
                (9, {'财险': 'O', '寿险': 'O', '金租': 'O', '资管': 'O', '广租': 'O', '通盛': 'O', '担保': 'O', '小贷': 'O', '保理': 'O', '不动产': 'O', '征信': 'O', '金服': 'O', '本部': '！'}),
                # 序号10: 集团金投云方案的意见征集
                (10, {'财险': '√', '寿险': '√', '金租': '√', '资管': '√', '广租': '√', '通盛': '√', '担保': '√', '小贷': '√', '保理': '√', '不动产': '√', '征信': '√', '金服': '√', '本部': '√'}),
                # 序号11: 集团金投云方案完成上线
                (11, {'财险': 'O', '寿险': 'O', '金租': 'O', '资管': 'O', '广租': 'O', '通盛': 'O', '担保': 'O', '小贷': 'O', '保理': 'O', '不动产': 'O', '征信': 'O', '金服': 'O', '本部': 'O'}),
                # 序号12: 信创改造计划第一轮摸底
                (12, {'财险': '√', '寿险': '√', '金租': '√', '资管': '√', '广租': '√', '通盛': '√', '担保': '√', '小贷': '√', '保理': '√', '不动产': '√', '征信': '√', '金服': '√', '本部': '√'}),
                # 序号13: 明确信创改造计划，完成2025年度改造计划
                (13, {'财险': '√', '寿险': '√', '金租': '√', '资管': '√', '广租': '√', '通盛': '√', '担保': '√', '小贷': '√', '保理': '√', '不动产': '√', '征信': '√', '金服': '√', '本部': '√'}),
                # 序号14: 建立异地数据备份
                (14, {'财险': '√', '寿险': '√', '金租': 'O', '资管': '√', '广租': 'O', '通盛': 'O', '担保': '√', '小贷': '√', '保理': '√', '不动产': '√', '征信': 'O', '金服': '√', '本部': '√'}),
                # 序号15: 补例外采购备案流程
                (15, {'财险': 'O', '寿险': 'O', '金租': 'O', '资管': 'O', '广租': 'O', '通盛': 'O', '担保': 'O', '小贷': 'O', '保理': 'O', '不动产': 'O', '征信': 'O', '金服': 'O', '本部': 'O'}),
                # 序号16: 信创电脑卸载Windows系统
                (16, {'财险': 'O', '寿险': 'O', '金租': 'O', '资管': 'O', '广租': 'O', '通盛': 'O', '担保': 'O', '小贷': 'O', '保理': 'O', '不动产': 'O', '征信': 'O', '金服': 'O', '本部': 'O'}),
                # 序号17: 信创设备采购
                (17, {'财险': 'X', '寿险': 'X', '金租': 'X', '资管': 'X', '广租': 'X', '通盛': 'X', '担保': 'X', '小贷': 'X', '保理': 'X', '不动产': 'X', '征信': 'X', '金服': 'X', '本部': 'X'}),
                # 序号18: 信创正式报告
                (18, {'财险': 'O', '寿险': 'O', '金租': 'O', '资管': 'O', '广租': 'O', '通盛': 'O', '担保': 'O', '小贷': 'O', '保理': 'O', '不动产': 'O', '征信': 'O', '金服': 'O', '本部': 'O'}),
                # 序号19: 诉讼案件导入
                (19, {'财险': '—', '寿险': '—', '金租': '√', '资管': '√', '广租': '√', '通盛': '√', '担保': '√', '小贷': '√', '保理': '√', '不动产': '√', '征信': '√', '金服': '√', '本部': '√'}),
                # 序号20: 业财一体
                (20, {'财险': '—', '寿险': '—', '金租': '√', '资管': '√', '广租': '√', '通盛': '√', '担保': '√', '小贷': '！', '保理': '√', '不动产': '√', '征信': '—', '金服': '—', '本部': '—'}),
                # 序号21: 集团资产管理专项项目
                (21, {'财险': '—', '寿险': '—', '金租': '—', '资管': 'O', '广租': 'O', '通盛': 'O', '担保': 'O', '小贷': 'O', '保理': 'O', '不动产': 'O', '征信': 'O', '金服': 'O', '本部': 'O'}),
                # 序号22: 广投司库对接项目
                (22, {'财险': '—', '寿险': '—', '金租': '—', '资管': 'O', '广租': 'O', '通盛': 'O', '担保': 'O', '小贷': 'O', '保理': 'O', '不动产': 'O', '征信': 'O', '金服': 'O', '本部': 'O'}),
                # 序号23: 集团档案管理系统
                (23, {'财险': '—', '寿险': '—', '金租': '—', '资管': 'O', '广租': 'O', '通盛': 'O', '担保': 'O', '小贷': 'O', '保理': 'O', '不动产': 'O', '征信': 'O', '金服': 'O', '本部': 'O'}),
                # 序号24: 制定集团数智规划
                (24, {'财险': '√', '寿险': '√', '金租': '√', '资管': '√', '广租': '√', '通盛': '√', '担保': '√', '小贷': '√', '保理': '√', '不动产': '√', '征信': '√', '金服': '√', '本部': '√'}),
                # 序号25: 加强外包管理，完成自查
                (25, {'财险': '√', '寿险': '√', '金租': '√', '资管': '√', '广租': '√', '通盛': '√', '担保': '√', '小贷': '√', '保理': '√', '不动产': '√', '征信': '√', '金服': '√', '本部': '√'}),
                # 序号26: 规范招标采购流程，完成自查
                (26, {'财险': '√', '寿险': '√', '金租': '√', '资管': '√', '广租': '√', '通盛': '√', '担保': '√', '小贷': '√', '保理': '√', '不动产': '√', '征信': '√', '金服': '√', '本部': '√'}),
                # 序号27: 提高系统应用程度，完成自查
                (27, {'财险': '√', '寿险': '√', '金租': '√', '资管': '√', '广租': '√', '通盛': '√', '担保': '√', '小贷': '√', '保理': '√', '不动产': '√', '征信': '√', '金服': '√', '本部': '√'}),
                # 序号28: 按授权要求进行项目备案，完成自查
                (28, {'财险': '√', '寿险': '√', '金租': '√', '资管': '√', '广租': '√', '通盛': '√', '担保': '√', '小贷': '√', '保理': '√', '不动产': '√', '征信': '√', '金服': '√', '本部': '√'}),
                # 序号29: 集团科技项目投资计划调整
                (29, {'财险': '√', '寿险': '√', '金租': '√', '资管': '√', '广租': '√', '通盛': '√', '担保': '√', '小贷': '√', '保理': '√', '不动产': '√', '征信': '√', '金服': '√', '本部': 'O'})
            ]
            
            # 获取公司ID映射
            cursor.execute("SELECT id, company_code FROM companies")
            companies = cursor.fetchall()
            company_id_mapping = {row[1]: row[0] for row in companies}
            
            # 插入状态数据
            status_count = 0
            for sequence_number, company_statuses in status_data:
                for company_name, status in company_statuses.items():
                    company_code = company_mapping.get(company_name)
                    if company_code and company_code in company_id_mapping:
                        company_id = company_id_mapping[company_code]
                        cursor.execute("""
                            INSERT INTO company_supervision_status 
                            (supervision_item_id, company_id, status)
                            VALUES (%s, %s, %s)
                        """, (sequence_number, company_id, status))
                        status_count += 1
            
            print(f"✅ 插入了 {status_count} 条公司状态记录")
            
            connection.commit()
            
            # 验证结果
            print(f"\n📊 验证结果:")
            cursor.execute("SELECT COUNT(*) FROM supervision_items")
            items_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM companies")
            companies_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM company_supervision_status")
            status_count = cursor.fetchone()[0]
            
            print(f"   督办事项: {items_count} 条")
            print(f"   公司: {companies_count} 个")
            print(f"   状态记录: {status_count} 条")
        
        connection.close()
        print("\n🎉 督办表数据录入完成！")
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    remove_progress_description_and_import_data()
