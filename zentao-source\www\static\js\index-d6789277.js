import{d as a,J as c,K as r,c as _,w as e,r as t,o as p,e as m,m as i}from"./index.js";const B=a({__name:"index",setup(l){c();const o=r(),n=()=>{o.replace({path:"/"})};return(d,x)=>{const s=t("n-button"),u=t("n-empty");return p(),_(u,{description:"\u4F60\u4EC0\u4E48\u4E5F\u627E\u4E0D\u5230"},{extra:e(()=>[m(s,{size:"small",onClick:n},{default:e(()=>[i("\u770B\u770B\u522B\u7684")]),_:1})]),_:1})}}});export{B as default};
