var J=Object.defineProperty;var L=Object.getOwnPropertySymbols;var K=Object.prototype.hasOwnProperty,Q=Object.prototype.propertyIsEnumerable;var U=(r,t,e)=>t in r?J(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,q=(r,t)=>{for(var e in t||(t={}))K.call(t,e)&&U(r,e,t[e]);if(L)for(var e of L(t))Q.call(t,e)&&U(r,e,t[e]);return r};var w=(r,t,e)=>new Promise((i,f)=>{var y=d=>{try{x(e.next(d))}catch(g){f(g)}},m=d=>{try{x(e.throw(d))}catch(g){f(g)}},x=d=>d.done?i(d.value):Promise.resolve(d.value).then(y,m);x((e=e.apply(r,t)).next())});import{u as A,T as B,U as E,_ as W}from"./useSync.hook-14394fcc.js";import{d as S,r as u,o as c,c as v,w as l,z as O,j as D,e as a,h as b,m as $,t as Y,aR as Z,p as R,a3 as ee,q as te,a5 as ne,x as ae,R as le,f as s,a9 as F,F as T,A as N}from"./index.js";import{i as oe}from"./icon-bb3d09e7.js";import"./plugin-37914809.js";import"./tables_list-f613fa36.js";const re=S({__name:"index",props:{name:{type:String,required:!0},expanded:{type:Boolean,required:!0,default:!1}},setup(r){const t=r,e=i=>{i.preventDefault(),i.stopPropagation()};return(i,f)=>{const y=u("n-collapse-item"),m=u("n-collapse");return c(),v(m,{"arrow-placement":"right","expanded-names":t.expanded?t.name:null,accordion:""},{"header-extra":l(()=>[O("div",{onClick:e},[D(i.$slots,"header")])]),default:l(()=>[a(y,{title:t.name,name:t.name},{default:l(()=>[D(i.$slots,"default")]),_:3},8,["title","name"])]),_:3},8,["expanded-names"])}}});const ue={class:"go-config-item-box"},se=S({__name:"index",props:{name:{type:String,required:!1},alone:{type:Boolean,default:!1,required:!1},itemRightStyle:{type:Object,default:()=>{},required:!1}},setup(r){return(t,e)=>{const i=u("n-text");return c(),b("div",ue,[a(i,{class:"item-left",depth:"2"},{default:l(()=>[$(Y(r.name)+" ",1),D(t.$slots,"name",{},void 0,!0)]),_:3}),O("div",{class:"item-right",style:Z(q({gridTemplateColumns:r.alone?"1fr":"1fr 1fr"},r.itemRightStyle))},[D(t.$slots,"default",{},void 0,!0)],4)])}}});var ie=R(se,[["__scopeId","data-v-61259120"]]);const de=S({__name:"index",props:{filter:{type:Object,required:!0},remove:{type:Function},idx:{type:Number,required:!0}},setup(r){const{RemoveIcon:t}=oe.ionicons5,e=r,{remove:i}=ee(e),f=te([]),y=()=>w(this,null,function*(){if(window.selectOptionApi)try{let _=new FormData;_.append("field",e.filter.field);const p=yield(yield fetch(window.selectOptionApi,{method:"POST",body:_})).json();f.value=p}catch(_){console.error(_)}else f.value=[]}),m={margin:"12px 0"},x=_=>!e.filter.name||e.filter.name.length==0?"\u7B5B\u9009\u5668 - \u672A\u547D\u540D"+_:"\u7B5B\u9009\u5668 - "+e.filter.name,d=A(),g=ne(()=>d.getComponentList.filter(p=>p.chartConfig.sourceID&&p.chartConfig.fields).map(p=>p.chartConfig).map(p=>{const C=[];for(const z in p.fields){const h=p.fields[z];C.push({value:h.field,label:h.name})}const k="diagram-"+p.sourceID;return{key:k,name:p.title,fields:C,expanded:e.filter.diagramIds.includes(k)}}));return ae(()=>{e.filter.type==B.SELECT&&y(),e.filter.name.length==0&&(e.filter.name="\u672A\u547D\u540D"+e.idx)}),le(()=>e.filter.field,(_,o)=>w(this,null,function*(){yield y()})),(_,o)=>{const p=u("n-icon"),C=u("n-button"),k=u("n-select"),z=u("n-tree-select"),h=u("n-input"),I=u("n-date-picker"),j=u("n-checkbox"),V=u("n-gi"),H=u("n-grid"),M=u("n-checkbox-group"),P=u("n-space"),X=u("n-card");return c(),v(X,{title:x(e.idx),size:"small","header-style":{fontSize:"14px"}},{"header-extra":l(()=>[a(C,{quaternary:"",title:"\u79FB\u9664",onClick:s(i)},{default:l(()=>[a(p,{size:"20"},{default:l(()=>[a(s(t))]),_:1})]),_:1},8,["onClick"])]),default:l(()=>[a(s(E),{name:"\u7C7B\u578B",alone:!0,itemBoxStyle:m},{default:l(()=>[a(k,{value:e.filter.type,"onUpdate:value":o[0]||(o[0]=n=>e.filter.type=n),options:e.filter.typeOptions,size:"small"},null,8,["value","options"])]),_:1}),e.filter.type===s(B).SELECT?(c(),v(s(E),{key:0,name:"\u7B5B\u9009\u503C",alone:!0,itemBoxStyle:m},{default:l(()=>[a(z,{options:e.filter.fieldOptions,value:e.filter.field,"onUpdate:value":o[1]||(o[1]=n=>e.filter.field=n),size:"small"},null,8,["options","value"])]),_:1})):F("",!0),a(s(E),{name:"\u540D\u79F0",alone:!0,itemBoxStyle:m},{default:l(()=>[a(h,{value:e.filter.name,"onUpdate:value":o[2]||(o[2]=n=>e.filter.name=n),size:"small",maxlength:"6",minlength:"1","show-count":"",placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["value"])]),_:1}),a(s(E),{name:"\u9ED8\u8BA4\u503C",alone:!0,itemBoxStyle:m},{default:l(()=>[e.filter.type===s(B).TEXT?(c(),v(h,{key:0,value:e.filter.defaultValue,"onUpdate:value":o[3]||(o[3]=n=>e.filter.defaultValue=n),size:"small"},null,8,["value"])):F("",!0),e.filter.type===s(B).SELECT?(c(),v(k,{key:1,value:e.filter.defaultValue,"onUpdate:value":o[4]||(o[4]=n=>e.filter.defaultValue=n),options:f.value,size:"small"},null,8,["value","options"])):F("",!0),e.filter.type===s(B).DATE?(c(),v(I,{key:2,value:e.filter.defaultValue,"onUpdate:value":o[5]||(o[5]=n=>e.filter.defaultValue=n),type:"daterange","start-placeholder":"\u5F00\u59CB","end-placeholder":"\u7ED3\u675F",clearable:"",size:"small"},null,8,["value"])):F("",!0),e.filter.type===s(B).DATETIME?(c(),v(I,{key:3,value:e.filter.defaultValue,"onUpdate:value":o[6]||(o[6]=n=>e.filter.defaultValue=n),type:"datetimerange","start-placeholder":"\u5F00\u59CB","end-placeholder":"\u7ED3\u675F",clearable:"",size:"small"},null,8,["value"])):F("",!0)]),_:1}),a(s(W),{name:"\u5173\u8054\u56FE\u8868",expanded:!0},{default:l(()=>[a(P,{vertical:"",size:[0,10]},{default:l(()=>[a(M,{value:e.filter.diagramIds,"onUpdate:value":o[7]||(o[7]=n=>e.filter.diagramIds=n)},{default:l(()=>[(c(!0),b(T,null,N(g.value,(n,ce)=>(c(),v(H,{key:n.key,"x-gap":"12",cols:12},{default:l(()=>[a(V,{span:1},{default:l(()=>[a(j,{value:n.key},null,8,["value"])]),_:2},1024),a(V,{span:11},{default:l(()=>[a(s(re),{name:n.name,expanded:e.filter.diagramIds.includes(n.key)},{default:l(()=>[a(s(ie),{name:"\u5173\u8054\u5B57\u6BB5",alone:!0},{default:l(()=>[a(k,{value:e.filter.diagramFields[n.key],"onUpdate:value":G=>e.filter.diagramFields[n.key]=G,options:n.fields,size:"small"},null,8,["value","onUpdate:value","options"])]),_:2},1024)]),_:2},1032,["name","expanded"])]),_:2},1024)]),_:2},1024))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1},8,["title"])}}});const pe=S({__name:"index",setup(r){const t=A(),e=()=>{t.addFilterList()},i=f=>{t.removeFilter(f)};return(f,y)=>{const m=u("n-button"),x=u("n-space");return c(),b(T,null,[a(m,{class:"btn-add",type:"info",onClick:e},{default:l(()=>[$(" + \u6DFB\u52A0 ")]),_:1}),a(x,{size:[0,10]},{default:l(()=>[(c(!0),b(T,null,N(s(t).getFilters,(d,g)=>(c(),v(s(de),{key:d.key,idx:g+1,filter:d,remove:()=>i(d.key)},null,8,["idx","filter","remove"]))),128))]),_:1})],64)}}});var ge=R(pe,[["__scopeId","data-v-4274ce7e"]]);export{ge as default};
