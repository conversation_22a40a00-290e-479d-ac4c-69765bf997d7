.table-children {border-left: 2px solid #cbd0db; border-right: 2px solid #cbd0db;}
.table tbody > tr.table-children.table-child-top {border-top: 2px solid #cbd0db;}
.table tbody > tr.table-children.table-child-bottom {border-bottom: 2px solid #cbd0db;}
.table td.has-child > a:not(.task-toggle) {max-width: 90%; max-width: calc(100% - 30px); display: inline-block; overflow: hidden; white-space: nowrap;}
.table td.has-child > .task-toggle {color: #838a9d; position: relative; top: 1px; line-height: 16px;}
.table td.has-child > .task-toggle:hover {color: #006af1; cursor: pointer;}
.table td.has-child > .task-toggle > .icon {font-size: 16px; display: inline-block; transition: transform .2s; -ms-transform: rotate(-90deg); -moz-transform: rotate(-90deg); -o-transform: rotate(-90deg); -webkit-transform: rotate(-90deg); transform: rotate(-90deg);}
.table td.has-child > .task-toggle > .icon:before {text-align: left;}
.table td.has-child > .task-toggle.collapsed {top: 0;}
.table td.has-child > .task-toggle.collapsed > .icon {-ms-transform: rotate(90deg); -moz-transform: rotate(90deg); -o-transform: rotate(90deg); -webkit-transform: rotate(90deg); transform: rotate(90deg);}
.table td.c-hours {padding-right: 12px;}
.main-table tbody > tr.table-children > td:first-child::before {width: 3px;}
@-moz-document url-prefix() {.main-table tbody > tr.table-children > td:first-child::before {width: 4px;};}

.c-hours {width: 50px;}
.c-status, .c-date, .c-user-short {width: 70px;}
.c-project {width: 130px;}
#taskTable .assigned-title{width: 110px; padding-left: 29px;}

html[lang="en"] .c-date {width: 80px;}
html[lang="en"] .c-user-short {width: 88px;}
[lang^=en] #taskTable .c-project {width: 100px}
[lang^=en] #taskTable .assigned-title {width: 120px !important;}
[lang^=de] #taskTable .c-name {width: 100px;}
[lang^=de] #taskTable .c-project {width: 80px;}
[lang^=de] #taskTable .c-date {width: 78px;}
[lang^=de] #taskTable .assigned-title {width: 123px !important;}
[lang^=de] #taskTable .c-user {width: 118px;}
[lang^=de] #taskTable .estimate, [lang^=de] #taskTable .consumed {width: 86px;}
[lang^=de] #taskTable .c-user-short {width: 75px;}
[lang^=fr] #taskTable .c-name {width: 120px;}
[lang^=fr] #taskTable .c-project {width: 100px;}
[lang^=fr] #taskTable .c-date {width: 91px;}
[lang^=fr] #taskTable .assigned-title {width: 130px !important;}
[lang^=fr] #taskTable .c-user-short {width: 75px;}
[lang^=fr] #taskTable .c-status {width: 71px;}
#myTaskForm table tbody tr td.c-actions .dividing-line {width: 1px; height: 16px; display: inline-block; vertical-align: middle; background: #F4F5F7; margin: 0 4px 0 0;}
