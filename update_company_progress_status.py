#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新公司进度状态枚举值
"""

import pymysql
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

# 数据库配置
DB_CONFIG = {
    'host': 'rm-7xvsn8jphl87541xueo.mysql.rds.aliyuncs.com',
    'port': 3306,
    'user': 'cyh',
    'password': 'Qq188788',
    'database': 'kanban2',
    'charset': 'utf8mb4'
}

def update_status_enum():
    """更新公司进度状态枚举值"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        logging.info("🔧 开始更新公司进度状态枚举值")
        logging.info("=" * 60)
        
        # 1. 先查看当前状态分布
        logging.info("步骤1: 查看当前状态分布")
        cursor.execute("SELECT status, COUNT(*) as count FROM company_progress GROUP BY status")
        current_statuses = cursor.fetchall()
        for status, count in current_statuses:
            logging.info(f"   - '{status}': {count} 条记录")
        
        # 2. 更新现有的"未开始"状态为"未启动"
        logging.info("步骤2: 更新现有状态名称")
        update_query = "UPDATE company_progress SET status = '未启动' WHERE status = '未开始'"
        cursor.execute(update_query)
        updated_rows = cursor.rowcount
        logging.info(f"✅ 更新了 {updated_rows} 条记录：'未开始' → '未启动'")
        
        # 3. 修改枚举值定义
        logging.info("步骤3: 修改状态枚举定义")
        try:
            alter_enum_query = """
            ALTER TABLE company_progress 
            MODIFY COLUMN status ENUM(
                '已完成',      
                '进行中',      
                '逾期',        
                '未启动',      
                '不需要执行'   
            ) DEFAULT '未启动' COMMENT '执行状态：√已完成 O进行中 ！逾期 X未启动 —不需要执行'
            """
            cursor.execute(alter_enum_query)
            logging.info("✅ 成功更新状态枚举定义")
        except Exception as e:
            logging.warning(f"⚠️  更新枚举定义失败: {e}")
            # 如果修改枚举失败，可能是因为有不兼容的数据，我们继续执行
        
        # 4. 验证更新结果
        logging.info("步骤4: 验证更新结果")
        cursor.execute("SELECT status, COUNT(*) as count FROM company_progress GROUP BY status")
        new_statuses = cursor.fetchall()
        logging.info("更新后的状态分布:")
        for status, count in new_statuses:
            logging.info(f"   - '{status}': {count} 条记录")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        logging.info("=" * 60)
        logging.info("🎉 公司进度状态枚举值更新完成")
        return True
        
    except Exception as e:
        logging.error(f"❌ 更新状态枚举值失败: {e}")
        return False

def main():
    """主函数"""
    logging.info("🚀 开始更新公司进度状态枚举值")
    logging.info("=" * 80)
    
    if update_status_enum():
        logging.info("=" * 80)
        logging.info("🎯 状态枚举值更新完成！")
        logging.info("💡 现在可以重新启动后端服务测试功能")
    else:
        logging.error("❌ 状态枚举值更新失败")

if __name__ == "__main__":
    main()
