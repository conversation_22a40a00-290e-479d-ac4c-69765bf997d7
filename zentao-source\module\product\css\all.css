@media screen and (min-width: 2048px) {.container {max-width: 2000px!important;}}
@media screen and (min-width: 2560px) {.container {max-width: 2380px!important;}}

.side-col .detail-content {padding-left: 10px; margin-top: 0px;}
.tree li.has-list.open:before {border-left: none;}
#programTree li {padding: 0 0 0 8px;}
#programTree li.has-list {padding-left: 20px;}
#programTree li > a {display: block; padding: 5px 0; border-radius: 2px; padding-left: 6px; height: 30px;}
#programTree li > a > span {display: inline-block;}
#programTree li > a > span + span {margin-left: 8px;}
#programTree li.selected > a {background-color: #E8F3FC;}
#programTree li.selected > a > span.title {color: #006AF1;}
#programTree .label-id {border-color: #cbd0db; color: #7d8599;}
#programTree .label.label-type {background: #fff; border: 1px solid #7d8599; color: #7d8599;}

#programTree li > a > span.title {color: #3C4353; white-space: nowrap; max-width: 60%; overflow: hidden; text-overflow: ellipsis; vertical-align: middle;}
#programTree li > a > span.user {color: #838a9d;}
#programTree li > a > span.user > .icon-person {font-size: 14px; position: relative; top: -1px; color: #a6aab8;}

#programTree li > .list-toggle {transform: rotate(0deg); width: 16px; height: 16px; border: 4px solid #a6aab8; border-radius: 2px; top: 7px;}
#programTree li > .list-toggle:before {content: ' '; display: block; position: absolute; border: 1px solid #a6aab8; top: 2px; left: -3px; right: -3px; bottom: 2px; min-width: 0; transition: all .2s;}
#programTree li > .list-toggle:hover:before,
#programTree li > .list-toggle:hover {border-color: #006AF1;}
#programTree li.open > .list-toggle {width: 12px; height: 12px; top: 9px; background-color: #a6aab8; border-width: 3px; left: 3px;}
#programTree li.open > .list-toggle:before {border: none; height: 2px; width: 6px; left: 0; top: 2px; background: #fff;}
#programTree li.open > .list-toggle:hover {background: #006AF1;}

#programTree ul > li:after {display: block; position: absolute; content: ' '; border-top: 2px solid #cbd0db; top: 14px; left: -12px; z-index: 1; width: 10px;}
#programTree ul > li:before,
#programTree ul > li.has-list:before {background: none; content: ' '; display: block; position: absolute; width: auto; height: auto; border: none; border-left: 2px solid #cbd0db; top: -13px; bottom: 12px; left: -12px;}
#programTree ul > li:last-child:before {bottom: auto; height: 29px;}
#programTree ul > li:first-child:before {top: -9px;}
#programTree ul > li.has-list:first-child:before {top: -13px;}
#programTree ul > li.tree-single-item:before {height: 23px;}
#programTree ul > li.has-list:after {width: 14px;}
#programTree ul > li.item-meas a.selected {color: #0c64eb;}
.side-col {padding-right: 20px; width: 18%;}
#sidebar > .cell {width: 100%;}

th.c-name {width: 400px;}
th.c-PO {width: 100px;}
th.c-story {width: 290px;}
th.c-bug {width: 150px;}
th.c-plan {width: 45px;}
th.c-release {width: 50px;}
th.c-actions {width: 50px;}
.c-name {border-left: none !important;}
.icon-cards-view {color: #888fa1;}
[lang='en'] .en-wrap-text {white-space: normal; height: 24px; line-height: 1; font-size: 12px;}

#productListForm thead > tr > th,
#productListForm tbody > tr > td {text-align: center; text-overflow: unset!important}
#productListForm thead > tr > th {padding: 0; line-height: 24px;}
#productListForm thead > tr:first-child > th {border-left: 1px solid #ddd;}
#productListForm thead > tr > th.c-checkbox {border-left: none; padding-left: 15px; width: 30px;}
#productListForm tbody > tr > td {padding: 2px 4px;}
#productListForm tbody > tr > td:first-child {text-align: left; padding-left: 15px;}
#productListForm tbody > tr > td.table-nest-title {padding-left: 16px; display: flex; align-items: center;}
#productListForm th.table-nest-title .nest-has-checkbox {margin-top: 7px; margin-left: 35px;}
#productListForm th.table-nest-title .nest-none-checkbox {margin-top: 8px; margin-left: 5px;}
#productListForm th.table-nest-title .header {margin-left: 5px;}
#productListForm th.table-nest-title .sort-up {padding-left: 5px;}
#productListForm th.table-nest-title .sort-down {padding-left: 5px;}
#productListForm th.table-nest-title .table-nest-toggle {top: 14px; left: auto; right: 10px; opacity: .6;}
#productListForm th.table-nest-title .table-nest-toggle::before {position: absolute; top: 0 !important; right: 0; bottom: 0; left: 0;}
#productListForm th.table-nest-title .table-nest-toggle:hover {opacity: 1;}
#productListForm .table.has-sort-head thead > tr > th > a:after,
#productListForm .table.has-sort-head thead > tr > th > a:before {top: -3px;}
#productListForm .icon-product:before {content: '\e98f'; width: 22px; height: 22px; background: none; color: rgb(166, 170, 184); top: 0; line-height: 22px; margin-right: 2px; font-size: 14px;}
#productTableList td.table-nest-title > span.icon.table-nest-toggle {vertical-align: middle;}
#productTableList > tr[data-nest-parent] {background: #f8f8f8;}
#productList > thead th.table-nest-title.c-name {position: relative; padding-left: 16px !important;}

@media screen and (max-width: 1460px)
{
    th.c-name {width: auto;}
    [lang^='zh-'] th.c-requirement {width: 260px;}
    [lang^='zh-'] th.c-story {width: 260px;}
}
@media screen and (max-width: 1300px)
{
    [lang^='zh-'] th.c-requirement {width: 240px;}
    [lang^='zh-'] th.c-story {width: 240px;}
}

@media screen and (max-width: 1250px)
{
    #productListForm thead > tr > th {font-size: 12px;}
    th.c-requirement {width: 265px;}
    th.c-story {width: 265px;}
    [lang^='zh-'] th.c-requirement {width: 220px;}
    [lang^='zh-'] th.c-story {width: 220px;}
}

#productTableList .c-name > span.table-nest-icon {width: 0; visibility: hidden;}
#productTableList > tr.row-line.is-nest-child > td.text-left.table-nest-title > span.table-nest-icon.icon:before{content: '\e6f2'; background-color: white; display: inline;}
#productTableList > tr.row-line.no-nest > td.text-left.table-nest-title > span.table-nest-icon.icon:before{content: '\e6f2'; background-color: white; display: inline;}
.statistic {margin-left: 10px; color: #838a9d; float: left; position: relative; line-height: 28px;}
.table-footer.fixed-footer .statistic {color: #fff;}
#productTableList .c-manager {padding-left: 14px;}
#productTableList .c-manager a {top: 7px; left: 22px;}
.checkbox-primary {margin-right: 10px;}
.table-nest-title > a, .table-nest-title > span {padding-left: 2px;}
.table-nest-icon {font-size: 14px; top: 50%;}
