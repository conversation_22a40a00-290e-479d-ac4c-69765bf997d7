#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析工单表的完整字段结构
"""

import pymysql
import json

class TicketFieldAnalyzer:
    def __init__(self):
        self.connection = None
        
    def connect(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host='**********',
                user='qyuser',
                password='C~w9d4kaWS',
                database='ticket',
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def execute_query(self, sql, params=None):
        """执行SQL查询"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            print(f"❌ SQL执行失败: {e}")
            return []
    
    def analyze_ticket_table(self):
        """分析工单表结构"""
        print("\n🔍 分析 feelec_ticket 表结构...")
        
        # 获取表结构
        structure_sql = "DESCRIBE feelec_ticket"
        fields = self.execute_query(structure_sql)
        
        print(f"\n📋 feelec_ticket 表字段 ({len(fields)} 个):")
        for field in fields:
            print(f"   {field['Field']:<30} {field['Type']:<20} {field['Null']:<5} {field['Key']:<5} {field['Default']}")
        
        # 获取一条样本数据
        sample_sql = "SELECT * FROM feelec_ticket WHERE feelec_delete = 20 LIMIT 1"
        sample_data = self.execute_query(sample_sql)
        
        if sample_data:
            print(f"\n📄 样本数据:")
            sample = sample_data[0]
            for key, value in sample.items():
                print(f"   {key:<30}: {value}")
        
        return fields
    
    def analyze_related_tables(self):
        """分析相关表结构"""
        related_tables = [
            'feelec_ticket_detail',
            'feelec_ticket_process', 
            'feelec_ticket_status',
            'feelec_ticket_template',
            'feelec_ticket_template_field',
            'feelec_ticket_action',
            'feelec_sub_ticket'
        ]
        
        for table in related_tables:
            print(f"\n🔍 分析 {table} 表结构...")
            
            # 检查表是否存在
            check_sql = f"SHOW TABLES LIKE '{table}'"
            exists = self.execute_query(check_sql)
            
            if not exists:
                print(f"   ❌ 表 {table} 不存在")
                continue
                
            # 获取表结构
            structure_sql = f"DESCRIBE {table}"
            fields = self.execute_query(structure_sql)
            
            print(f"   📋 字段 ({len(fields)} 个):")
            for field in fields:
                print(f"      {field['Field']:<25} {field['Type']:<15} {field['Null']:<5}")
            
            # 获取记录数
            count_sql = f"SELECT COUNT(*) as count FROM {table}"
            count_result = self.execute_query(count_sql)
            if count_result:
                print(f"   📊 记录数: {count_result[0]['count']}")
    
    def find_missing_fields(self):
        """查找可能遗漏的字段"""
        print(f"\n🔍 查找可能遗漏的字段...")
        
        # 检查是否有其他以feelec_ticket开头的表
        tables_sql = "SHOW TABLES LIKE 'feelec_ticket%'"
        ticket_tables = self.execute_query(tables_sql)
        
        print(f"📋 工单相关表:")
        for table in ticket_tables:
            table_name = list(table.values())[0]
            print(f"   {table_name}")
        
        # 检查feelec_ticket表中可能的扩展字段
        print(f"\n🔍 检查可能的扩展字段...")
        
        # 查看所有字段的实际使用情况
        fields_sql = """
        SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            IS_NULLABLE,
            COLUMN_DEFAULT,
            COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'ticket' 
        AND TABLE_NAME = 'feelec_ticket'
        ORDER BY ORDINAL_POSITION
        """
        
        detailed_fields = self.execute_query(fields_sql)
        
        print(f"📋 详细字段信息:")
        for field in detailed_fields:
            comment = field['COLUMN_COMMENT'] or '无注释'
            print(f"   {field['COLUMN_NAME']:<30} {field['DATA_TYPE']:<15} {comment}")
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()

def main():
    analyzer = TicketFieldAnalyzer()
    
    if not analyzer.connect():
        return
    
    try:
        # 分析主表
        analyzer.analyze_ticket_table()
        
        # 分析相关表
        analyzer.analyze_related_tables()
        
        # 查找遗漏字段
        analyzer.find_missing_fields()
        
    finally:
        analyzer.close()

if __name__ == "__main__":
    main()
