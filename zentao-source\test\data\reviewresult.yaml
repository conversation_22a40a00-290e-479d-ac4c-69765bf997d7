title: table zt_reviewresult
desc: "评审结果"
author: automated export
version: "1.0"
fields:
  - field: id
    note: "ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: review
    note: "评审ID"
    range: 1-10000
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: type
    note: "类型"
    range: audit,review
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: result
    note: "结果"
    range: pass,needfix
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: opinion
    note: "意见"
    range: 1-10000
    prefix: "这里是意见"
    postfix: ""
    loop: 0
    format: ""
  - field: reviewer
    note: "评审者"
    from: common.user.v1.yaml
    use: user
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: createdDate
    note: "创建日期"
    from: common.date.v1.yaml
    use: dateB
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
  - field: consumed
    note: "消耗工时"
    range: 1-10:R
    prefix: ""
    postfix: ""
    loop: 0
    format: ""
