#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据库问题
"""

import pymysql

def debug_database():
    """调试数据库"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        print("✅ 数据库连接成功")
        
        with connection.cursor() as cursor:
            # 检查所有表
            print("\n📋 数据库中的所有表:")
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            for table in tables:
                print(f"   📝 {table[0]}")
            
            # 检查supervision_items表结构
            print(f"\n🔍 检查 supervision_items 表结构:")
            try:
                cursor.execute("DESCRIBE supervision_items")
                columns = cursor.fetchall()
                
                print("   表结构:")
                for column in columns:
                    print(f"      {column[0]} ({column[1]}) - {column[2] if column[2] else 'NULL'}")
                
                # 检查数据
                cursor.execute("SELECT COUNT(*) FROM supervision_items")
                count = cursor.fetchone()[0]
                print(f"   📊 数据量: {count} 条")
                
                if count > 0:
                    cursor.execute("SELECT * FROM supervision_items LIMIT 3")
                    rows = cursor.fetchall()
                    print(f"   📝 前3条数据:")
                    for i, row in enumerate(rows):
                        print(f"      {i+1}. {row}")
                
            except Exception as e:
                print(f"   ❌ supervision_items 表检查失败: {str(e)}")
            
            # 检查companies表
            print(f"\n🔍 检查 companies 表:")
            try:
                cursor.execute("SELECT COUNT(*) FROM companies")
                count = cursor.fetchone()[0]
                print(f"   📊 公司数量: {count}")
                
                if count > 0:
                    cursor.execute("SELECT company_code, company_name FROM companies LIMIT 5")
                    companies = cursor.fetchall()
                    print(f"   🏢 前5个公司:")
                    for company in companies:
                        print(f"      {company[0]}: {company[1]}")
                
            except Exception as e:
                print(f"   ❌ companies 表检查失败: {str(e)}")
            
            # 检查company_supervision_status表
            print(f"\n🔍 检查 company_supervision_status 表:")
            try:
                cursor.execute("SELECT COUNT(*) FROM company_supervision_status")
                count = cursor.fetchone()[0]
                print(f"   📊 状态记录数量: {count}")
                
                if count > 0:
                    cursor.execute("""
                        SELECT css.supervision_item_id, c.company_code, css.status 
                        FROM company_supervision_status css
                        JOIN companies c ON css.company_id = c.id
                        LIMIT 5
                    """)
                    statuses = cursor.fetchall()
                    print(f"   📊 前5个状态记录:")
                    for status in statuses:
                        print(f"      督办事项{status[0]} - {status[1]}: {status[2]}")
                
            except Exception as e:
                print(f"   ❌ company_supervision_status 表检查失败: {str(e)}")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 数据库调试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_simple_query():
    """测试简单查询"""
    try:
        print("\n🧪 测试简单查询...")
        
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='kanban2',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 测试基本查询
            cursor.execute("SELECT id, sequence_number, work_theme FROM supervision_items LIMIT 3")
            rows = cursor.fetchall()
            
            print("✅ 基本查询成功:")
            for row in rows:
                print(f"   ID:{row[0]}, 序号:{row[1]}, 主题:{row[2]}")
            
            # 测试带is_annual_assessment字段的查询
            try:
                cursor.execute("SELECT id, sequence_number, work_theme, is_annual_assessment FROM supervision_items LIMIT 3")
                rows = cursor.fetchall()
                
                print("✅ 包含is_annual_assessment字段的查询成功:")
                for row in rows:
                    print(f"   ID:{row[0]}, 序号:{row[1]}, 主题:{row[2]}, 考核指标:{row[3]}")
                    
            except Exception as e:
                print(f"❌ 包含is_annual_assessment字段的查询失败: {str(e)}")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 简单查询测试失败: {str(e)}")

def main():
    print("🚀 数据库调试")
    print("=" * 60)
    
    # 调试数据库
    debug_database()
    
    # 测试简单查询
    test_simple_query()
    
    print("\n" + "=" * 60)
    print("🏁 调试完成")

if __name__ == "__main__":
    main()
