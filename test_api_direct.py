#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试API
"""

import requests
import json

def test_api():
    """测试API"""
    print("🧪 直接测试新督办管理API...")
    
    base_url = "http://localhost:8000/api/v1"
    
    # 先登录获取token
    try:
        print("🔐 登录获取token...")
        login_response = requests.post(f"{base_url}/auth/login", json={
            "username": "admin",
            "password": "admin123"
        })
        
        if login_response.status_code == 200:
            token = login_response.json().get('access_token')
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            print(f"响应: {login_response.text}")
            return
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        return
    
    # 测试新督办管理API
    try:
        print("\n📋 测试新督办管理API...")
        response = requests.get(f"{base_url}/new-supervision/items", headers=headers)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            print(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ API调用失败")
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ API测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api()
