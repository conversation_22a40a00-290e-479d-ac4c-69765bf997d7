function setStory(reason)
{
    if(reason == 'duplicate')
    {
        $('#duplicateStoryBox').show();
        $('#childStoriesBox').hide();
    }
    else if(reason == 'subdivided')
    {
        $('#duplicateStoryBox').hide();
        $('#childStoriesBox').show();
    }
    else
    {
        $('#duplicateStoryBox').hide();
        $('#childStoriesBox').hide();
    }
}

$(function()
{
    $('#duplicateStory').picker(
    {
        disableEmptySearch : true,
        dropWidth : 'auto'
    });
    
    $('[data-toggle="popover"]').popover();
});
