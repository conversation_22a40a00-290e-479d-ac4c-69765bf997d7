<?php
declare(strict_types=1);
/**
 * The ajaxGetUserTasks view file of task module of ZenTaoPMS.
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.zentao.net)
 * @license     ZPL(https://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR>
 * @package     task
 * @link        https://www.zentao.net
 */
namespace zin;

$filedName = $id ? "tasks[$id]" : 'task';
control
(
    set::name($filedName),
    set::items($tasks),
    set::required(true)
);

render();
