var e;ShadowRoot.prototype.createRange=document.createRange.bind(document),document.head.insertAdjacentHTML("beforeend","<style> body > div.monaco-aria-container {display: none;} </style>");const o=null===(e=performance.getEntriesByType("resource").find((e=>e.name.includes("zen-editor.esm.js"))))||void 0===e?void 0:e.name;void 0!==o&&document.head.insertAdjacentHTML("beforeend",`<link href="${o.replace(/(.*\/).*?$/,"$1")}codicon.css?v=0.13.3" rel="stylesheet" media="print" onload="this.media='all'" />`);const n=()=>{};export{n as g}