#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工单详情修复
"""

import requests
import json

def test_ticket_detail():
    """测试工单详情"""
    try:
        print("🚀 测试工单详情修复...")
        
        # 直接测试一个已知的工单ID
        ticket_id = "72282405343465472"
        content_url = f"http://localhost:8000/api/v1/ticket-integration/tickets/{ticket_id}/full-content"
        headers = {}
        
        print(f"🎫 测试工单ID: {ticket_id}")
        
        response = requests.get(content_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                ticket = data['data']
                print("✅ 工单详情获取成功")
                
                # 基础信息
                print(f"\n📋 基础信息:")
                print(f"   工单ID: {ticket.get('feelec_ticket_id')}")
                print(f"   工单编号: {ticket.get('feelec_ticket_no')}")
                print(f"   工单标题: {ticket.get('feelec_title')}")
                
                # 人员信息
                print(f"\n👥 人员信息:")
                print(f"   发布人: {ticket.get('publisher_name')} (ID: {ticket.get('feelec_publisher_id')})")
                print(f"   处理人: {ticket.get('processor_name')} (ID: {ticket.get('feelec_processor_id')})")
                
                # 组织信息
                print(f"\n🏢 组织信息:")
                print(f"   所属部门: {ticket.get('department_name')} (ID: {ticket.get('feelec_department_id')})")
                print(f"   主体公司: {ticket.get('company_name')} (ID: {ticket.get('feelec_company_id')})")
                print(f"   关联项目: {ticket.get('project_name')} (ID: {ticket.get('feelec_project_id')})")
                
                # 状态和优先级
                print(f"\n📊 状态和优先级:")
                print(f"   当前状态: {ticket.get('status_name')} (ID: {ticket.get('feelec_status_id')})")
                print(f"   优先级: {ticket.get('priority_text')} (原值: {ticket.get('feelec_priority')})")
                
                # 模板信息
                print(f"\n📝 模板信息:")
                print(f"   工单模板: {ticket.get('template_name')} (ID: {ticket.get('feelec_template_id')})")
                
                # 时间信息
                print(f"\n⏰ 时间信息:")
                print(f"   创建时间: {ticket.get('create_time_formatted')}")
                print(f"   首次分配: {ticket.get('first_assign_time_formatted')}")
                print(f"   首次处理: {ticket.get('first_process_time_formatted')}")
                print(f"   完成时间: {ticket.get('complete_time_formatted')}")
                print(f"   截止时间: {ticket.get('deadline_formatted')}")
                
                # 其他信息
                print(f"\n📌 其他信息:")
                print(f"   工单来源: {ticket.get('source_text')} (原值: {ticket.get('feelec_source')})")
                print(f"   删除状态: {ticket.get('feelec_delete')}")
                print(f"   是否完成: {ticket.get('is_completed')}")
                print(f"   是否逾期: {ticket.get('is_overdue')}")
                
                # 详细字段
                detail_fields = ticket.get('detail_fields', {})
                if detail_fields:
                    print(f"\n📄 详细字段:")
                    for field_name, field_value in detail_fields.items():
                        print(f"   {field_name}: {field_value[:100]}..." if len(str(field_value)) > 100 else f"   {field_name}: {field_value}")
                else:
                    print(f"\n📄 详细字段: 无")
                
                print(f"\n🎉 测试完成！工单详情显示正常")
                
            else:
                print(f"❌ API返回错误: {data.get('message')}")
        else:
            print(f"❌ 工单内容获取失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ticket_detail()
