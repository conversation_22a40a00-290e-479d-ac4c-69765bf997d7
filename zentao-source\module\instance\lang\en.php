<?PHP
$lang->instance = new stdclass;
$lang->instance->common           = 'Services';
$lang->instance->manage           = 'Manage Services';
$lang->instance->view             = 'Service Detail';
$lang->instance->ajaxStatus       = 'Get Service Status';
$lang->instance->ajaxStart        = 'Start Service';
$lang->instance->ajaxStop         = 'Stop Service';
$lang->instance->ajaxUninstall    = 'Uninstall Service';
$lang->instance->name             = 'Service Name';
$lang->instance->customName       = 'Name';
$lang->instance->appName          = 'Service type';
$lang->instance->version          = 'version';
$lang->instance->status           = 'Status';
$lang->instance->cpu              = 'CPU';
$lang->instance->mem              = 'Memory';
$lang->instance->space            = 'space';
$lang->instance->domain           = 'Domain name';
$lang->instance->visitIP          = 'Access Method';
$lang->instance->dbType           = 'Database';
$lang->instance->advanceOption    = 'Advanced Options';
$lang->instance->baseInfo         = 'Basic Information';
$lang->instance->backupAndRestore = 'Backup';
$lang->instance->advance         = 'Advanced';
$lang->instance->enableLDAP       = 'Enable LDAP';
$lang->instance->linkLDAP         = 'Integrated LDAP';
$lang->instance->enableSMTP       = 'Enable SMTP';
$lang->instance->customSetting    = 'Custom Configuration';
$lang->instance->updateDomain     = 'Update Domain Name';
$lang->instance->updateLog        = 'Update Log';
$lang->instance->start            = 'Start';
$lang->instance->restart          = 'Restart';
$lang->instance->stop             = 'Close';
$lang->instance->install          = 'Install';
$lang->instance->update           = 'Update';
$lang->instance->upgrade          = 'Upgrade';
$lang->instance->customInstall    = 'Custom Install';
$lang->instance->uninstall        = 'delete';
$lang->instance->visit            = 'Visit';
$lang->instance->editName         = 'Modify Name';
$lang->instance->cpuCore          = 'Core';
$lang->instance->scalable         = 'Service level expansion';
$lang->instance->change           = 'Modify';
$lang->instance->browseProject    = "Project List";
$lang->instance->url              = "Server URL";

$lang->instance->systemLDAPInactive = 'System LDAP not enabled';
$lang->instance->toSystemLDAP       = 'Deactivate';

$lang->instance->enableSMTP         = 'Enable SMTP';
$lang->instance->systemSMTPInactive = 'System SMTP not enabled';
$lang->instance->toSystemSMTP       = 'Deactivate';

$lang->instance->serviceInfo      = 'Service Information';
$lang->instance->appTemplate      = 'Application Template';
$lang->instance->source           = 'Source';
$lang->instance->installBy        = 'Creator';
$lang->instance->installAt        = 'Creation time';
$lang->instance->runDuration      = 'Running';
$lang->instance->defaultAccount   = 'Default User';
$lang->instance->defaultPassword  = 'Default Password';
$lang->instance->account          = 'User';
$lang->instance->password         = 'Password';
$lang->instance->token            = 'Token';
$lang->instance->copied           = 'Copy successful';
$lang->instance->operationLog     = 'Operation Record';
$lang->instance->installedService = 'Installed Service';
$lang->instance->runningService   = 'Running Service';
$lang->instance->installApp       = 'Install App';
$lang->instance->cpuUsage         = 'CPU usage';
$lang->instance->memUsage         = 'Memory usage';
$lang->instance->memTotal         = '(Total %s)';
$lang->instance->volUsage         = 'Volume usage';
$lang->instance->volTotal         = '(Total %s)';
$lang->instance->currentMemory    = 'Current Memory';
$lang->instance->adjustMem        = 'Memory';
$lang->instance->adjustCPU        = 'CPU';
$lang->instance->adjustVol        = 'Disk Space';
$lang->instance->setting          = 'Setting';
$lang->instance->saveSetting      = 'Save Settings';
$lang->instance->leftTime         = 'Remaining';
$lang->instance->switchTo         = 'Upgrade to';
$lang->instance->or               = 'or';
$lang->instance->hasRead          = 'Read';
$lang->instance->stopInstanceTips = 'You can only upgrade to the advanced version after shutting down the service!';

$lang->instance->dbTypes  =  array();
$lang->instance->dbTypes['sharedDB']    =  'Shared Database';
$lang->instance->dbTypes['unsharedDB']  =  'Exclusive Database';

$lang->instance->backup  =  new stdclass;
$lang->instance->backup->common              = 'backup';
$lang->instance->backup->date                = 'backup time';
$lang->instance->backup->operator            = 'Backup person';
$lang->instance->backup->type                = 'Backup type';
$lang->instance->backup->backupStatus        = 'Backup Status';
$lang->instance->backup->restoreStatus       = 'Restore Status';
$lang->instance->backup->restoreOperator     = 'Restore by';
$lang->instance->backup->restoreTime         = 'Restore time';
$lang->instance->backup->action              = 'Operation';
$lang->instance->backup->restore             = 'rollback';
$lang->instance->backup->restoreInfo         = 'Restore Information';
$lang->instance->backup->delete              = 'Delete';
$lang->instance->backup->rebackup            = 'Retrying backup';
$lang->instance->backup->create              = 'Create backup';
$lang->instance->backup->database            = 'Database';
$lang->instance->backup->dbType              = 'Type';
$lang->instance->backup->dbName              = 'name';
$lang->instance->backup->dbStatus            =  'Status';
$lang->instance->backup->dbSpentSeconds      =  'Time taken (seconds)';
$lang->instance->backup->dbSize              =  'Size';
$lang->instance->backup->volumne              = 'Data volume';
$lang->instance->backup->volName             = 'name';
$lang->instance->backup->volMountName        = 'Mount directory';
$lang->instance->backup->volStatus           = 'Status';
$lang->instance->backup->volSpentSeconds     = 'Time taken (seconds)';
$lang->instance->backup->volSize             = 'Size';
$lang->instance->backup->lastRestore         = 'Last rollback';
$lang->instance->backup->restoreDate         = 'Restore time';
$lang->instance->backup->latestBackupAt      = 'Last backup time';
$lang->instance->backup->backupBeforeRestore = 'We recommend that you backup before rolling back!';
$lang->instance->backup->enableAutoBackup    = 'Enable automatic backup';
$lang->instance->backup->autoBackup          = 'Automatic backup';
$lang->instance->backup->cycleDays           = 'backup cycle';
$lang->instance->backup->backupTime          = 'backup time';
$lang->instance->backup->keepDays            = 'retention days';
$lang->instance->backup->keepDayRange        = 'Please enter an integer between 1 and 30';
$lang->instance->backup->firstStartTime      = '%s The first backup will be performed on %s';
$lang->instance->backup->invalidTime         = 'Invalid time';
$lang->instance->backup->disableAutoBackup   = 'Automatic backup has been turned off';
$lang->instance->backup->keepBackupBySystem  = 'The system will only delete expired automatic backup data when there is more than one backup data.';
$lang->instance->backup->backupSize          = 'Size';
$lang->instance->backup->confirmTip          = 'Are you sure you want to back up?';
$lang->instance->backup->cronRemark          = 'DevOps service automatic backup task';
$lang->instance->backup->backupSettingsTip   = 'The system will delete backups that exceed the set number of days, but will retain at least one valid backup.';
$lang->instance->backup->confirmDeleteTip    = 'Are you sure you want to delete the backup?';

$lang->instance->backup->cycleList[1] = 'Daily';

$lang->instance->backup->operators = array();
$lang->instance->backup->operators['auto']     = 'Automatic backup';
$lang->instance->backup->operators['manual']   = 'Manual backup';
$lang->instance->backup->operators['settings'] = 'Backup Settings';

$lang->instance->backup->statusList = array();
$lang->instance->backup->statusList['success']        = 'successful';
$lang->instance->backup->statusList['failed']         = 'failed';
$lang->instance->backup->statusList['pending']        = 'Waiting';
$lang->instance->backup->statusList['processing']     = 'Backup in progress';
$lang->instance->backup->statusList['inprogress']     = 'Backup in progress';
$lang->instance->backup->statusList['completed']      = 'completed';
$lang->instance->backup->statusList['executedFailed'] = 'Failed';
$lang->instance->backup->statusList['uploading']      = 'Uploading';
$lang->instance->backup->statusList['deleting']       = 'Deleting';
$lang->instance->backup->statusList['uploadFailed']   = 'Upload failed';
$lang->instance->backup->statusList['downloading']    = 'Downloading';
$lang->instance->backup->statusList['downloadFailed'] = 'Download failed';

$lang->instance->backup->autoRestoreOptions[0] = '关闭';
$lang->instance->backup->autoRestoreOptions[1] = '开启';

$lang->instance->restore  =  new stdclass;
$lang->instance->restore->common             = 'Restore';
$lang->instance->restore->autoRestore        = 'Auto Restore';
$lang->instance->restore->enableAutoRestore  = 'Automatic backup';
$lang->instance->restore->cycleDays          = 'Backup cycle';
$lang->instance->restore->restoreTime        = 'Backup Time';
$lang->instance->restore->invalidTime        = 'Invalid time';
$lang->instance->restore->disableAutoRestore = 'Automatic restore has been turned off';
$lang->instance->restore->firstStartTime     = '%s The first automatic restore will be executed on %s';

$lang->instance->restore->cycleList[1] = 'Daily';

$lang->instance->restore->statusList = array();
$lang->instance->restore->statusList['pending']    = 'waiting';
$lang->instance->restore->statusList['inprogress'] = 'In progress';
$lang->instance->restore->statusList['completed']  = 'completed';
$lang->instance->restore->statusList['failed']     = 'failed';

$lang->instance->dbList     = 'Database';
$lang->instance->dbName     = 'Name';
$lang->instance->dbStatus   = 'Status';
$lang->instance->dbType     = 'Type';
$lang->instance->action     = 'Action';
$lang->instance->management = 'Management';
$lang->instance->dbReady    = 'Normal';
$lang->instance->dbWaiting  = 'Waiting for readiness';

$lang->instance->log = new stdclass;
$lang->instance->log->date    = 'date';
$lang->instance->log->message = 'Content';

$lang->instance->log->viewButton  = 'Logs';
$lang->instance->log->title       = 'Watch Logs';
$lang->instance->log->button      = 'View Now';
$lang->instance->log->autoRefresh = 'Auto Refresh';
$lang->instance->log->tips        = 'The logs are refreshed automatically by default, and you can switch the button to switch';
$lang->instance->log->noLog       = 'No logs yet';


$lang->instance->component         = 'Component';
$lang->instance->pod               = 'Pod';
$lang->instance->previous          = 'View exited containers';
$lang->instance->isPreviousList[0] = 'Current container';
$lang->instance->isPreviousList[1] = 'Previous container';

$lang->instance->event = new stdClass;
$lang->instance->event->viewButton = 'Events';
$lang->instance->event->title      = 'Watch Events';
$lang->instance->event->button     = 'View Now';
$lang->instance->event->noEvents   = 'No events yet';

$lang->instance->actionList  =  array();
$lang->instance->actionList['install']                  = '%s installed';
$lang->instance->actionList['uninstall']               = '%s deleted';
$lang->instance->actionList['start']                   = 'Started %s';
$lang->instance->actionList['stop']                    = '%s closed';
$lang->instance->actionList['editname']                = 'Name modified';
$lang->instance->actionList['upgrade']                 = 'upgraded %s';
$lang->instance->actionList['backup']                  = 'Backed up %s';
$lang->instance->actionList['restore']                 = 'Rolled back %s';
$lang->instance->actionList['adjustmemory']            = 'Adjust the memory of %s to %s';
$lang->instance->actionList['adjustreplicas']          = 'Adjust the number of replicas of %s to %s';
$lang->instance->actionList['enableldap']              = 'LDAP enabled';
$lang->instance->actionList['disableldap']             = 'LDAP disabled';
$lang->instance->actionList['enablesmtp']              = 'enabled SMTP';
$lang->instance->actionList['disablesmtp']             = 'SMTP disabled';
$lang->instance->actionList['updatecustom']            = 'Modified custom configuration';
$lang->instance->actionList['saveautobackupsettings']  = 'Modified automatic backup settings';
$lang->instance->actionList['saveautorestoresettings'] = 'Modified automatic restore settings';
$lang->instance->actionList['autobackup']              = 'The system has performed an automatic backup';
$lang->instance->actionList['autorestore']             = 'The system has performed an automatic restore';
$lang->instance->actionList['deleteexpiredbackup']     = 'The system has deleted expired automatic backups';

$lang->instance->sourceList = array();
$lang->instance->sourceList['cloud'] = 'Store';
$lang->instance->sourceList['local'] = 'Local Market';
$lang->instance->sourceList['user']  = 'User';

$lang->instance->channelList = array();
$lang->instance->channelList['test']   = 'Test version';
$lang->instance->channelList['stable'] = 'stable version';

$lang->instance->statusList = array();
$lang->instance->statusList['installationFail'] = 'Installation failed';
$lang->instance->statusList['creating']         = 'Creating';
$lang->instance->statusList['initializing']     = 'initializing';
$lang->instance->statusList['pulling']          = 'Downloading';
$lang->instance->statusList['startup']          = 'Starting';
$lang->instance->statusList['starting']         = 'Starting';
$lang->instance->statusList['running']          = 'Running';
$lang->instance->statusList['suspending']       = 'Suspending';
$lang->instance->statusList['suspended']        = 'paused';
$lang->instance->statusList['installing']       = 'Installing';
$lang->instance->statusList['uninstalling']     = 'Removing';
$lang->instance->statusList['stopping']         = 'Closing';
$lang->instance->statusList['stopped']          = 'Closed';
$lang->instance->statusList['destroying']       = 'Destroying';
$lang->instance->statusList['destroyed']        = 'Destroyed';
$lang->instance->statusList['abnormal']         = 'Abnormal';
$lang->instance->statusList['upgrading']        = 'Updating';
$lang->instance->statusList['unknown']          = 'unknown';
$lang->instance->statusList['scheduling']       = 'Scheduling';

$lang->instance->htmlStatusesClass = array();
$lang->instance->htmlStatusesClass['running']          = 'success';
$lang->instance->htmlStatusesClass['stopped']          = 'default';
$lang->instance->htmlStatusesClass['abnormal']         = 'danger';
$lang->instance->htmlStatusesClass['installationFail'] = 'danger';
$lang->instance->htmlStatusesClass['busy']             = "warning";

$lang->instance->memOptions  =  array();
$lang->instance->memOptions[128 * 1024]   = '128MB';
$lang->instance->memOptions[256 * 1024]   = '256MB';
$lang->instance->memOptions[512 * 1024]   = '512MB';
$lang->instance->memOptions[1024 * 1024]  = '1GB';
$lang->instance->memOptions[2048 * 1024]  = '2GB';
$lang->instance->memOptions[4096 * 1024]  = '4GB';
$lang->instance->memOptions[8192 * 1024]  = '8GB';
$lang->instance->memOptions[16384 * 1024] = '16GB';
$lang->instance->memOptions[32768 * 1024] = '32GB';

$lang->instance->cpuOptions = array();
$lang->instance->cpuOptions[1]  = '1 core';
$lang->instance->cpuOptions[2]  = '2 core';
$lang->instance->cpuOptions[4]  = '4 core';
$lang->instance->cpuOptions[8]  = '8 core';
$lang->instance->cpuOptions[16] = '16 core';
$lang->instance->cpuOptions[64] = 'No limit';

$lang->instance->componentFields = array();
$lang->instance->componentFields['replicas']  = 'Number of replicas';
$lang->instance->componentFields['cpu_limit'] = 'CPU';
$lang->instance->componentFields['mem_limit'] = 'Memory';

$lang->instance->notices = array();
$lang->instance->notices['success']                  = 'Success';
$lang->instance->notices['fail']                     = 'failed';
$lang->instance->notices['error']                    = 'error';
$lang->instance->notices['confirmStart']             = 'Are you sure to start the application?';
$lang->instance->notices['confirmStop']              = 'Are you sure to close this application?';
$lang->instance->notices['confirmUninstall']         = 'Are you sure to delete this application?';
$lang->instance->notices['confirmUninstallStoreApp'] = 'Are you sure you delete it? Apps and data cannot be recovered after deletion!';
$lang->instance->notices['startSuccess']             = 'Successfully started';
$lang->instance->notices['startFail']                = 'Start failed';
$lang->instance->notices['stopSuccess']              = 'Close successfully';
$lang->instance->notices['stopFail']                 = 'Close failed';
$lang->instance->notices['uninstallSuccess']         = 'Successfully deleted';
$lang->instance->notices['uninstallFail']            = 'Delete failed';
$lang->instance->notices['installSuccess']           = 'Installation successful';
$lang->instance->notices['installFail']              = 'Installation failed';
$lang->instance->notices['upgradeSuccess']           = 'Upgrade successful';
$lang->instance->notices['upgradeFail']              = 'Upgrade failed';
$lang->instance->notices['backupSuccess']            = 'Backup task submitted';
$lang->instance->notices['backupFail']               = 'Backup failed';
$lang->instance->notices['cleanBackupsSuccess']      = 'Backup cleanup successfuls';
$lang->instance->notices['restoreSuccess']           = 'The rollback task has been submitted';
$lang->instance->notices['restoreFail']              = 'Rollback failed';
$lang->instance->notices['deleteSuccess']            = 'The deletion task has been submitted';
$lang->instance->notices['deleteFail']               = 'Delete failed';
$lang->instance->notices['starting']                 = 'Starting, please wait...';
$lang->instance->notices['stopping']                 = 'Closing, please wait...';
$lang->instance->notices['installing']               = 'Installing, please wait...';
$lang->instance->notices['uninstalling']             = 'Deleting, please wait...';
$lang->instance->notices['upgrading']                = 'Upgrading, please wait...';
$lang->instance->notices['backuping']                = 'Backing up, please wait...';
$lang->instance->notices['restoring']                = 'Rolling back, please wait...';
$lang->instance->notices['deleting']                 = 'Deleting, please wait...';
$lang->instance->notices['adjusting']                = 'Adjusting, please wait...';
$lang->instance->notices['switching']                = 'Submitting, please wait...';
$lang->instance->notices['setting']                  = 'Submitting, please wait...';
$lang->instance->notices['confirmInstall']           = 'Are you sure you want to install (%s)?';
$lang->instance->notices['confirmUpgrade']           = 'Are you sure you want to upgrade to the latest version?';
$lang->instance->notices['confirmBackup']            = 'Are you sure you want to backup?';
$lang->instance->notices['confirmRestore']           = 'This operation will overwrite the current data with the backed up data. Are you sure you want to rollback?';
$lang->instance->notices['confirmDelete']            = 'Are you sure you want to delete the backup data?';
$lang->instance->notices['adjustMemory']             = 'Are you sure you want to adjust the middle memory?';
$lang->instance->notices['switchLDAP']               = 'Modifying LDAP will restart the service. Are you sure you want to modify LDAP?';
$lang->instance->notices['enableLDAPFailed']         = 'Enable LDAP failed';
$lang->instance->notices['disableLDAPFailed']        = 'Disable LDAP failed';
$lang->instance->notices['enableLDAPSuccess']        = 'Enable LDAP successfully';
$lang->instance->notices['disableLDAPSuccess']       = 'Disable LDAP successfully';
$lang->instance->notices['switchSMTP']               = 'Modifying SMTP will restart the service. Are you sure you want to modify SMTP?';
$lang->instance->notices['enableSMTPFailed']         = 'Enable SMTP failed';
$lang->instance->notices['disableSMTPFailed']        = 'Disable SMTP failed';
$lang->instance->notices['enableSMTPSuccess']        = 'Enable SMTP successfully';
$lang->instance->notices['disableSMTPSuccess']       = 'Disable SMTP successfully';
$lang->instance->notices['confirmCustom']            = 'After modifying the custom configuration, the service will automatically restart to make the configuration effective.';
$lang->instance->notices['required']                 = 'cannot be empty';
$lang->instance->notices['notEnoughResource']        = 'Insufficient platform resources. Do you want to continue installing?';
$lang->instance->notices['NoCleanBackupFiles']       = 'There are currently no backup files to clean.';
$lang->instance->notices['cleanBackupSuccess']       = 'Backup cleanup succeeded.';

$lang->instance->nameChangeTo      = '%s is modified to %s.';
$lang->instance->versionChangeTo   = 'Upgrade %s to %s.';
$lang->instance->adjustMemorySize  = 'It is recommended to adjust the memory to %s.';
$lang->instance->enableAutoBackup  = 'Enable automatic backup';
$lang->instance->disableAutoBackup = 'Turn off automatic backup';

$lang->instance->instanceNotExists  = 'Service does not exist';
$lang->instance->caplicasTooSmall   = 'The number of replicas cannot be less than 1';
$lang->instance->empty              = 'Currently no service available';
$lang->instance->noComponent        = 'No component, click';
$lang->instance->noHigherVersion    = 'No higher version found!';
$lang->instance->backupOnlyRunning  = 'Only running status can be backed up';
$lang->instance->restoreOnlyRunning = 'Only running status can be rolled back';
$lang->instance->howToSelectDB      =  'How to select';
$lang->instance->appLifeTip         = 'The application installed on the demo account has a 30 minute limit and will be automatically deleted after 30 minutes.';
$lang->instance->serialDiff         = 'View version differences';
$lang->instance->descOfSwitchSerial = 'You are currently using<strong>%s</strong>. If you want to experience more advanced features, you can upgrade to %s.';

$lang->instance->errors = new stdclass;
$lang->instance->errors->domainLength         = 'The domain name length must be between 2 and 20 characters';
$lang->instance->errors->domainExists         = 'The domain name is already in use, please use a different domain name.';
$lang->instance->errors->wrongDomainCharacter = 'Domain name can only be lowercase English alphabet and numbers';
$lang->instance->errors->noAppInfo            = 'Failed to obtain application data, please try again later.';
$lang->instance->errors->notEnoughResource    = 'Insufficient cluster resources';
$lang->instance->errors->notEnoughMemory      = "The '%s' application requires %s memory, currently available memory is %s, and %s is still needed. Please expand memory resources or remove other services and try again";
$lang->instance->errors->restoreRunning       = 'The current rollback is in progress, please wait for the current rollback to complete.';
$lang->instance->errors->noBackup             = 'No backup data,';
$lang->instance->errors->wrongRequestData     = 'The submitted data is incorrect. Please refresh the page and try again.';
$lang->instance->errors->noDBList             = 'No database or inaccessible';
$lang->instance->errors->notFoundDB           = 'The database cannot be found';
$lang->instance->errors->dbNameIsEmpty        = 'Database name is empty';
$lang->instance->errors->failToAdjustMemory   = 'Failed to adjust memory';
$lang->instance->errors->failToAdjustCPU      = 'Failed to adjust CPU';
$lang->instance->errors->failToAdjustVol      = 'Failed to adjust volume';
$lang->instance->errors->switchLDAPFailed     = 'Modifying LDAP settings failed';
$lang->instance->errors->switchSMTPFailed     = 'Modifying SMTP settings failed';
$lang->instance->errors->updateCustomFailed   = 'Modifying custom configuration failed';
$lang->instance->errors->failedToUpdateDomain = 'Failed to update domain name';
$lang->instance->errors->invalidDiskSize      = 'The disk space size must be an integer between %s and %s';

$lang->instance->tips = new stdclass;
$lang->instance->tips->resizeDisk         = 'Fill in an integer from %s to %s with unit GB';
$lang->instance->tips->resizeDiskDisabled = 'The disk space not adjustable';
$lang->instance->tips->fSettingsAttention = 'Changing the Settings of CPU, memory, disk, etc. will restart the application, and the disk space size can only be increased, not reduced, please be careful!';
$lang->instance->tips->pSettingsAttention = 'Changing the CPU, memory, and other Settings will restart the application, please be careful!';
$lang->instance->tips->diskAdjusting      = 'The disk cannot be adjusted during disk expansion';
