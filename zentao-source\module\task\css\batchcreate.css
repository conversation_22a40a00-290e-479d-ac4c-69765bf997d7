.chosen-container[id^="story"] {width: 200px; max-width: 150px;}
select[id^="story"] + .picker-single {width: 130px; max-width: 130px;}
[id^=name] {min-width: 165px;}
#mainContent .main-header h2 {max-width: 300px;}

#batchCreateForm .input-group,
#batchCreateForm .input-group .form-control {position: static;}
#batchCreateForm .input-group .colorpicker {z-index: 2;}
#batchCreateForm .input-group .colorpicker.open {z-index: 5;}

.main-header .pull-left {max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}

#zeroTaskStory {margin: 0 0 0 20px; height: 32px; padding: 5px 5px 5px 5px; border-radius: 3px; border: 2px solid transparent;}
[lang^='en'] #zeroTaskStory {max-width: 210px !important;}
[lang^='de'] #zeroTaskStory {max-width: 225px !important;}
[lang^='fr'] #zeroTaskStory {max-width: 265px !important;}
#zeroTaskStory > label:before {top: 7px; left: 5px;}
#zeroTaskStory > label:after {top: 7px; left: 5px;}
#zeroTaskStory.checked {border-color: #00a9fc; background: #E9F2FB;}

.chosen-results > li.has-task,
.chosen-results > li.has-new-task {position: relative; color: #388E3C;}
.chosen-results > li.has-task.highlighted,
.chosen-results > li.has-new-task.highlighted {color: #fff;}

#importLinesModal .modal-dialog {width: 80%;}

.c-id {width: 30px;}
.c-name, .c-desc {width: 150px;}
.c-story {width: 200px;}
.c-assigned {width: 130px;}
.c-date {width: 120px;}
.c-pri {width: 80px;}
.c-actions {width: 60px;}
