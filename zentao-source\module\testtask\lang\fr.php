<?php
/**
 * The testtask module English file of ZenTaoPMS.
 *
 * @copyright   Copyright 2009-2023 禅道软件（青岛）有限公司(ZenTao Software (Qingdao) Co., Ltd. www.cnezsoft.com)
 * @license     ZPL(http://zpl.pub/page/zplv12.html) or AGPL(https://www.gnu.org/licenses/agpl-3.0.en.html)
 * <AUTHOR> <<EMAIL>>
 * @package     testtask
 * @version     $Id: en.php 4490 2013-02-27 03:27:05Z <EMAIL> $
 * @link        https://www.zentao.net
 */
$lang->testtask->index            = "Accueil Recette";
$lang->testtask->create           = "Initier Campagne";
$lang->testtask->reportChart      = 'Rapport';
$lang->testtask->delete           = "Supprimer Campagne";
$lang->testtask->importUnitResult = "Import T.U. Résult";
$lang->testtask->importUnit       = "Import Unit Result"; //Fix bug custom required testtask.
$lang->testtask->browseUnits      = "Liste des tests unitaires";
$lang->testtask->unitCases        = "Browse Unit Cases List";
$lang->testtask->view             = "Détail Campagne";
$lang->testtask->edit             = "Editer Campagne";
$lang->testtask->browse           = "Tester Campagne";
$lang->testtask->linkCase         = "Inclure CasTests";
$lang->testtask->selectVersion    = "Sélectioner Version";
$lang->testtask->unlinkCase       = "Exclure";
$lang->testtask->batchUnlinkCases = "Exclure CasTests par Lot";
$lang->testtask->batchAssign      = "Affecter par Lot";
$lang->testtask->runCase          = "Jouer";
$lang->testtask->running          = ", running.";
$lang->testtask->runningLog       = "Execution Log";
$lang->testtask->runNode          = "Executed by %s,Execute on node %s %s";
$lang->testtask->batchRun         = "Jouer par Lot";
$lang->testtask->results          = "Résultats";
$lang->testtask->createBug        = "Bug(+)";
$lang->testtask->assign           = 'Affecter';
$lang->testtask->cases            = 'Liste CasTests';
$lang->testtask->groupCase        = "Revu par Groupe";
$lang->testtask->pre              = 'Préc.';
$lang->testtask->next             = 'Suiv';
$lang->testtask->start            = "Commencer";
$lang->testtask->close            = "Clôturer";
$lang->testtask->wait             = "En Attente";
$lang->testtask->block            = "Bloquer";
$lang->testtask->activate         = "Activer";
$lang->testtask->testing          = "En Déroulement";
$lang->testtask->blocked          = "Bloquée";
$lang->testtask->done             = "Jouée";
$lang->testtask->totalStatus      = "Toutes";
$lang->testtask->all              = 'Toutes';
$lang->testtask->allTasks         = 'Toutes Recettes';
$lang->testtask->collapseAll      = 'Collapse';
$lang->testtask->expandAll        = 'Expand';
$lang->testtask->auto             = 'Test Automation Tasks';
$lang->testtask->task             = 'Test Task';
$lang->testtask->run              = 'Test Run ID';
$lang->testtask->job              = 'Job';
$lang->testtask->compile          = 'Compile';
$lang->testtask->duration         = 'Duration';
$lang->testtask->myInvolved       = 'Involved';

$lang->testtask->viewAction     = "View Request";
$lang->testtask->casesAction    = 'Browse Cases List';
$lang->testtask->activateAction = "Activer Campagne";
$lang->testtask->blockAction    = "Bloquer Campagne";
$lang->testtask->closeAction    = "Clôturer Campagne";
$lang->testtask->startAction    = "Démarrer Campagne";
$lang->testtask->resultsAction  = "Résultats CasTest";
$lang->testtask->reportAction   = 'Rapport CasTests';

$lang->testtask->id                = 'ID';
$lang->testtask->common            = 'Recette';
$lang->testtask->product           = $lang->productCommon;
$lang->testtask->project           = $lang->projectCommon;;
$lang->testtask->execution         = $lang->execution->common;
$lang->testtask->type              = 'Type';
$lang->testtask->build             = 'Build';
$lang->testtask->owner             = 'Owner';
$lang->testtask->members           = 'Participant';
$lang->testtask->executor          = 'Executeur';
$lang->testtask->execTime          = 'Durée Exec';
$lang->testtask->pri               = 'Priorité';
$lang->testtask->name              = 'Nom Campagne';
$lang->testtask->unitName          = 'Name Of Unit Test';
$lang->testtask->begin             = 'Début';
$lang->testtask->end               = 'Fin';
$lang->testtask->realBegan         = 'Actual Started Date';
$lang->testtask->realFinishedDate  = 'Actual Finished Date';
$lang->testtask->desc              = 'Description';
$lang->testtask->mailto            = 'Mailto';
$lang->testtask->status            = 'Statut';
$lang->testtask->statusAB          = 'Status';
$lang->testtask->subStatus         = 'Sous-statut';
$lang->testtask->testreport        = 'Test Report';
$lang->testtask->assignCase        = 'Assign Testcase';
$lang->testtask->assignedTo        = 'Affecté';
$lang->testtask->linkVersion       = 'Build';
$lang->testtask->lastRunAccount    = 'Jouée par';
$lang->testtask->lastRunTime       = 'Dernier Run';
$lang->testtask->lastRunResult     = 'Résultat';
$lang->testtask->reportField       = 'Rapport';
$lang->testtask->files             = 'Upload';
$lang->testtask->case              = 'Liste CasTests';
$lang->testtask->version           = 'Version';
$lang->testtask->caseResult        = 'Résultat Test';
$lang->testtask->stepResults       = 'Etape Résultat';
$lang->testtask->lastRunner        = 'Joué par';
$lang->testtask->lastRunDate       = 'Dernier Run';
$lang->testtask->createdBy         = 'Created By';
$lang->testtask->createdDate       = 'Created Date';
$lang->testtask->date              = 'Testé sur';;
$lang->testtask->deleted           = "Supprimé";
$lang->testtask->resultFile        = "Fichier Résultats";
$lang->testtask->caseCount         = 'Compteur CasTest';
$lang->testtask->passCount         = 'Pass';
$lang->testtask->failCount         = 'Fail';
$lang->testtask->skipChangedCases  = 'The testcases that are not to be confirmed are ignored.';
$lang->testtask->summary           = '%s CasTest, %s échecs, %s heures.';
$lang->testtask->stepSummary       = 'Total %s steps, %s passes, %s failures.';
$lang->testtask->unitSummary       = 'Total %s unit test results.';
$lang->testtask->pageSummary       = 'Total testtasks: <strong>%s</strong>.';
$lang->testtask->mySummary         = 'Total testtasks: <strong>%s</strong>, Wait: <strong>%s</strong>, Testing: <strong>%s</strong>, Blocked: <strong>%s</strong>.';
$lang->testtask->allSummary        = 'Total testtasks: <strong>%s</strong>, Wait: <strong>%s</strong>, Testing: <strong>%s</strong>, Blocked: <strong>%s</strong>, Done: <strong>%s</strong>.';
$lang->testtask->checkedAllSummary = 'Seleted: <strong>%total%</strong>, Wait: <strong>%wait%</strong>, Testing: <strong>%testing%</strong>, Blocked: <strong>%blocked%</strong>.';
$lang->testtask->emptyCases        = 'Cases %s have no steps, filter to show.';
$lang->testtask->caseEmpty         = 'Selected cases have no steps or do not meet the execution conditions, and have been ignored.';

$lang->testtask->beginAndEnd = 'Durée';
$lang->testtask->to          = 'à';

$lang->testtask->legendDesc      = 'Description';
$lang->testtask->legendReport    = 'Rapport';
$lang->testtask->legendBasicInfo = 'Infos de Base';

$lang->testtask->statusList['wait']    = 'En Attente';
$lang->testtask->statusList['doing']   = 'En Déroulement';
$lang->testtask->statusList['done']    = 'Jouée';
$lang->testtask->statusList['blocked'] = 'Bloquée';

$lang->testtask->priList[1] = '1';
$lang->testtask->priList[2] = '2';
$lang->testtask->priList[3] = '3';
$lang->testtask->priList[4] = '4';

$lang->testtask->unlinkedCases = 'CasTests Non rattachés';
$lang->testtask->linkByBuild   = 'Copier depuis build';
$lang->testtask->linkByStory   = 'Lier par Story';
$lang->testtask->linkByBug     = 'Lier par Bug';
$lang->testtask->linkBySuite   = 'Lier par Cahier Recette';
$lang->testtask->browseBySuite = 'Browse by Suite';
$lang->testtask->passAll       = 'Tout Réussir';
$lang->testtask->pass          = 'Réussite';
$lang->testtask->fail          = 'Echec';
$lang->testtask->showResult    = 'Jouée <label class="label primary-pale rounded-full h-3 px-1.5 mx-1">%s</label> fois';
$lang->testtask->showFail      = 'Echouée <label class="label danger-pale rounded-full h-3 px-1.5 mx-1">%s</label> fois';
$lang->testtask->runInTask     = ' <strong>%s</strong> by <strong>%s</strong>, the build is <strong>%s</strong> ';
$lang->testtask->runCaseResult = ', executed %s, the results is <span class="text-%s font-bold">%s</span>.';

$lang->testtask->confirmDelete     = 'Voulez-vous supprimer ce build ?';
$lang->testtask->confirmUnlinkCase = 'Voulez-vous détacher ce CasTest ?';
$lang->testtask->noticeNoOther     = "No test builds for this {$lang->productCommon}.";
$lang->testtask->noTesttask        = 'Pas de campagne. ';
$lang->testtask->checkLinked       = "Please check whether the {$lang->productCommon} that the test request is linked to has been linked to a {$lang->executionCommon}.";
$lang->testtask->noImportData      = "Le XML importé ne parse pas les données.";
$lang->testtask->unitXMLFormat     = 'Veuillez sélectionner un fichier au format XML JUnit.';
$lang->testtask->titleOfAuto       = "%s tests automatisés";
$lang->testtask->cannotBeParsed    = 'The content of the imported XML file is in the wrong format and cannot be parsed.';
$lang->testtask->finishedDateLess  = 'Actual Finished Date cannot be <= Begin Date %s';
$lang->testtask->finishedDateMore  = 'Actual Finished Date cannot be > Today';
$lang->testtask->emptyUnitTip      = 'No unit test results.';

$lang->testtask->assignedToMe  = 'Affecté à Moi';
$lang->testtask->allCases      = 'Tous les CasTests';

$lang->testtask->lblCases      = 'CasTest';
$lang->testtask->lblUnlinkCase = 'Exclure CasTest';
$lang->testtask->lblRunCase    = 'Jouer le CasTest';
$lang->testtask->lblResults    = 'Résultats';

$lang->testtask->placeholder = new stdclass();
$lang->testtask->placeholder->begin = 'Début';
$lang->testtask->placeholder->end   = 'Fin';

$lang->testtask->mail = new stdclass();
$lang->testtask->mail->create = new stdclass();
$lang->testtask->mail->edit   = new stdclass();
$lang->testtask->mail->close  = new stdclass();
$lang->testtask->mail->create->title = "%s a créé campagne de recette #%s:%s";
$lang->testtask->mail->edit->title   = "%s a modifié campagne de recette #%s:%s";
$lang->testtask->mail->close->title  = "%s a clôturé campagne de recette #%s:%s";

$lang->testtask->action = new stdclass();
$lang->testtask->action->testtaskopened  = '$date,  <strong>$actor</strong> a soumis campagne de recette <strong>$extra</strong>.' . "\n";
$lang->testtask->action->testtaskstarted = '$date,  <strong>$actor</strong> a démarré campagne de recette <strong>$extra</strong>.' . "\n";
$lang->testtask->action->testtaskclosed  = '$date,  <strong>$actor</strong> a terminé campagne de recette <strong>$extra</strong>.' . "\n";

$lang->testtask->unexecuted = 'En Attente';

/* Statistical statement. */
$lang->testtask->report = new stdclass();
$lang->testtask->report->common = 'Rapport';
$lang->testtask->report->select = 'Sélect Type de Rapport';
$lang->testtask->report->create = 'Créer Rapport';

$lang->testtask->report->testTaskPerRunResultTip = 'There are %s usecase, including %s passed,%s not executed, and %s failed';

$lang->testtask->report->charts['testTaskPerRunResult'] = 'Résultat des CasTests';
$lang->testtask->report->charts['testTaskPerType']      = 'Type de CasTests';
$lang->testtask->report->charts['testTaskPerModule']    = 'Module CasTests';
$lang->testtask->report->charts['testTaskPerRunner']    = 'CasTests joués par';

$lang->testtask->featureBar['browse']['totalStatus'] = $lang->testtask->totalStatus;
$lang->testtask->featureBar['browse']['myinvolved']  = $lang->testtask->myInvolved;
$lang->testtask->featureBar['browse']['wait']        = $lang->testtask->wait;
$lang->testtask->featureBar['browse']['doing']       = $lang->testtask->testing;
$lang->testtask->featureBar['browse']['blocked']     = $lang->testtask->blocked;
$lang->testtask->featureBar['browse']['done']        = $lang->testtask->done;

$lang->testtask->featureBar['cases']['all']          = $lang->testtask->allCases;
$lang->testtask->featureBar['cases']['assignedtome'] = $lang->testtask->assignedToMe;

$lang->testtask->featureBar['groupcase']['all']          = $lang->testtask->allCases;
$lang->testtask->featureBar['groupcase']['assignedtome'] = $lang->testtask->assignedToMe;

$lang->testtask->featureBar['linkcase']['all']     = $lang->all;
$lang->testtask->featureBar['linkcase']['bystory'] = $lang->testtask->linkByStory;
$lang->testtask->featureBar['linkcase']['bysuite'] = $lang->testtask->linkBySuite;
$lang->testtask->featureBar['linkcase']['bybuild'] = $lang->testtask->linkByBuild;
$lang->testtask->featureBar['linkcase']['bybug']   = $lang->testtask->linkByBug;

$lang->testtask->featureBar['browseunits']['all']       = 'All';
$lang->testtask->featureBar['browseunits']['newest']    = 'Recently';
$lang->testtask->featureBar['browseunits']['thisWeek']  = 'This week';
$lang->testtask->featureBar['browseunits']['lastWeek']  = 'Last week';
$lang->testtask->featureBar['browseunits']['thisMonth'] = 'This month';
$lang->testtask->featureBar['browseunits']['lastMonth'] = 'Last month';

$lang->testtask->typeList['integrate']   = 'Integrate';
$lang->testtask->typeList['system']      = 'System';
$lang->testtask->typeList['acceptance']  = 'Acceptance';
$lang->testtask->typeList['performance'] = 'Performance';
$lang->testtask->typeList['safety']      = 'Safety';
