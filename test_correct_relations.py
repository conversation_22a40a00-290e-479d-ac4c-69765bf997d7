#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试正确的关联查询
"""

import mysql.connector
from decimal import Decimal

class CorrectRelationTester:
    """正确关联测试器"""
    
    def __init__(self):
        self.config = {
            'host': '**********',
            'user': 'qyuser', 
            'password': 'C~w9d4kaWS',
            'database': 'ticket',
            'autocommit': True
        }
        self.connection = None
    
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = mysql.connector.connect(**self.config)
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False
    
    def execute_query(self, sql: str, params: tuple = None):
        """执行查询SQL"""
        if not self.connection or not self.connection.is_connected():
            if not self.connect():
                return []
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(sql, params or ())
            result = cursor.fetchall()
            cursor.close()
            
            # 处理Decimal类型
            for row in result:
                for key, value in row.items():
                    if isinstance(value, Decimal):
                        row[key] = float(value)
            
            return result
        except Exception as e:
            print(f"SQL执行失败: {e}")
            return []
    
    def test_user_data(self):
        """测试用户数据"""
        print("👤 测试用户数据...")
        print("=" * 50)
        
        # 查看用户表实际字段
        user_samples = self.execute_query("""
            SELECT feelec_user_id, feelec_name, feelec_mobile, feelec_email, feelec_company_id
            FROM feelec_user 
            WHERE delete_time = 0 
            LIMIT 5
        """)
        
        print("📊 用户数据样例:")
        for user in user_samples:
            print(f"   👤 ID:{user['feelec_user_id']} 姓名:{user['feelec_name']} 公司:{user['feelec_company_id']}")
    
    def test_department_data(self):
        """测试部门数据"""
        print(f"\n🏢 测试部门数据...")
        print("=" * 50)
        
        # 查看部门表实际数据
        dept_samples = self.execute_query("""
            SELECT feelec_department_id, feelec_name, feelec_parent_id, feelec_company_id
            FROM feelec_member_department 
            WHERE delete_time = 0 
            LIMIT 10
        """)
        
        print("📊 部门数据样例:")
        for dept in dept_samples:
            print(f"   🏢 ID:{dept['feelec_department_id']} 名称:{dept['feelec_name']} 父级:{dept['feelec_parent_id']} 公司:{dept['feelec_company_id']}")
    
    def test_company_data(self):
        """测试公司数据"""
        print(f"\n🏛️ 测试公司数据...")
        print("=" * 50)
        
        # 查看公司表实际数据
        company_samples = self.execute_query("""
            SELECT feelec_company_id, feelec_name, feelec_contact, feelec_phone
            FROM feelec_company 
            WHERE delete_time = 0 
            LIMIT 10
        """)
        
        print("📊 公司数据样例:")
        for company in company_samples:
            print(f"   🏛️ ID:{company['feelec_company_id']} 名称:{company['feelec_name']} 联系人:{company['feelec_contact']}")
    
    def test_ticket_relations(self):
        """测试工单关联"""
        print(f"\n🔗 测试工单关联...")
        print("=" * 50)
        
        # 测试工单与用户关联
        print("📋 测试工单与用户关联:")
        user_ticket_query = """
            SELECT 
                t.feelec_ticket_id,
                t.feelec_title,
                t.feelec_publisher_id,
                t.feelec_processor_id,
                u1.feelec_name as publisher_name,
                u2.feelec_name as processor_name
            FROM feelec_ticket t
            LEFT JOIN feelec_user u1 ON t.feelec_publisher_id = u1.feelec_user_id AND u1.delete_time = 0
            LEFT JOIN feelec_user u2 ON t.feelec_processor_id = u2.feelec_user_id AND u2.delete_time = 0
            WHERE t.feelec_delete = 20
            LIMIT 5
        """
        
        user_tickets = self.execute_query(user_ticket_query)
        for ticket in user_tickets:
            print(f"   🎫 {ticket['feelec_ticket_id']}: {ticket['feelec_title'][:30]}...")
            print(f"      发布人:{ticket['publisher_name']} (ID:{ticket['feelec_publisher_id']})")
            print(f"      处理人:{ticket['processor_name']} (ID:{ticket['feelec_processor_id']})")
        
        # 测试工单与部门关联
        print(f"\n📋 测试工单与部门关联:")
        dept_ticket_query = """
            SELECT 
                t.feelec_ticket_id,
                t.feelec_title,
                t.feelec_department_id,
                d.feelec_name as department_name
            FROM feelec_ticket t
            LEFT JOIN feelec_member_department d ON t.feelec_department_id = d.feelec_department_id AND d.delete_time = 0
            WHERE t.feelec_delete = 20
            LIMIT 5
        """
        
        dept_tickets = self.execute_query(dept_ticket_query)
        for ticket in dept_tickets:
            print(f"   🎫 {ticket['feelec_ticket_id']}: {ticket['feelec_title'][:30]}...")
            print(f"      部门:{ticket['department_name']} (ID:{ticket['feelec_department_id']})")
        
        # 测试工单与公司关联
        print(f"\n📋 测试工单与公司关联:")
        company_ticket_query = """
            SELECT 
                t.feelec_ticket_id,
                t.feelec_title,
                t.feelec_company_id,
                c.feelec_name as company_name
            FROM feelec_ticket t
            LEFT JOIN feelec_company c ON t.feelec_company_id = c.feelec_company_id AND c.delete_time = 0
            WHERE t.feelec_delete = 20
            LIMIT 5
        """
        
        company_tickets = self.execute_query(company_ticket_query)
        for ticket in company_tickets:
            print(f"   🎫 {ticket['feelec_ticket_id']}: {ticket['feelec_title'][:30]}...")
            print(f"      公司:{ticket['company_name']} (ID:{ticket['feelec_company_id']})")
    
    def test_statistics_queries(self):
        """测试统计查询"""
        print(f"\n📊 测试统计查询...")
        print("=" * 50)
        
        # 按状态统计工单
        print("📋 按状态统计工单:")
        status_stats = self.execute_query("""
            SELECT 
                s.feelec_name as status_name,
                COUNT(t.feelec_ticket_id) as ticket_count
            FROM feelec_ticket t
            LEFT JOIN feelec_ticket_status s ON t.feelec_status_id = s.feelec_status_id AND s.delete_time = 0
            WHERE t.feelec_delete = 20
            GROUP BY t.feelec_status_id, s.feelec_name
            ORDER BY ticket_count DESC
        """)
        
        for stat in status_stats:
            print(f"   📊 {stat['status_name']}: {stat['ticket_count']} 个工单")
        
        # 按用户统计工单
        print(f"\n📋 按用户统计工单:")
        user_stats = self.execute_query("""
            SELECT 
                u.feelec_name as user_name,
                COUNT(t.feelec_ticket_id) as ticket_count
            FROM feelec_ticket t
            LEFT JOIN feelec_user u ON t.feelec_processor_id = u.feelec_user_id AND u.delete_time = 0
            WHERE t.feelec_delete = 20 AND t.feelec_processor_id IS NOT NULL
            GROUP BY t.feelec_processor_id, u.feelec_name
            ORDER BY ticket_count DESC
            LIMIT 10
        """)
        
        for stat in user_stats:
            print(f"   👤 {stat['user_name']}: {stat['ticket_count']} 个工单")
        
        # 按公司统计工单
        print(f"\n📋 按公司统计工单:")
        company_stats = self.execute_query("""
            SELECT 
                c.feelec_name as company_name,
                COUNT(t.feelec_ticket_id) as ticket_count
            FROM feelec_ticket t
            LEFT JOIN feelec_company c ON t.feelec_company_id = c.feelec_company_id AND c.delete_time = 0
            WHERE t.feelec_delete = 20
            GROUP BY t.feelec_company_id, c.feelec_name
            ORDER BY ticket_count DESC
            LIMIT 10
        """)
        
        for stat in company_stats:
            print(f"   🏛️ {stat['company_name']}: {stat['ticket_count']} 个工单")
    
    def close(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()

def main():
    """主函数"""
    print("🔍 测试正确的关联查询")
    print("=" * 80)
    
    tester = CorrectRelationTester()
    
    try:
        if tester.connect():
            tester.test_user_data()
            tester.test_department_data()
            tester.test_company_data()
            tester.test_ticket_relations()
            tester.test_statistics_queries()
        else:
            print("❌ 数据库连接失败")
    finally:
        tester.close()
    
    print("\n" + "=" * 80)
    print("🎉 关联查询测试完成！")

if __name__ == "__main__":
    main()
