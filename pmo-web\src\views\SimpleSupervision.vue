<template>
  <div class="simple-supervision">
    <el-card class="supervision-card">
      <template #header>
        <div class="card-header">
          <span>督办管理</span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="refreshData">刷新</el-button>
            <el-button type="success" size="small" @click="addNewItem">新增督办事项</el-button>
            <el-button type="info" size="small" @click="showColumnManager">管理公司列</el-button>
            <el-button type="warning" size="small" @click="exportToExcel" :loading="exporting">导出Excel</el-button>
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :on-change="handleFileChange"
              :show-file-list="false"
              accept=".xlsx,.xls"
            >
              <el-button type="danger" size="small" :loading="importing">导入Excel</el-button>
            </el-upload>
          </div>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchText"
          placeholder="搜索工作主题、维度或来源..."
          size="small"
          style="width: 300px"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 数据表格 -->
      <el-table
        :key="tableKey"
        :data="filteredItems"
        :loading="loading"
        border
        stripe
        size="small"
        style="width: 100%"
        :header-cell-style="{ padding: '6px 0', fontSize: '13px' }"
        :cell-style="{ padding: '4px 8px' }"
        :scroll-x="true"
        max-height="600"
      >
        <!-- 基本信息列 -->
        <el-table-column prop="sequence_number" label="序号" width="60" />
        
        <el-table-column prop="work_dimension" label="工作维度" width="120">
          <template #default="scope">
            <div v-if="scope.row.isEditing">
              <el-input v-model="scope.row.work_dimension" size="small" />
            </div>
            <div v-else @dblclick="enableEditing(scope.row)">
              {{ scope.row.work_dimension }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="work_theme" label="工作主题" width="200">
          <template #default="scope">
            <div v-if="scope.row.isEditing">
              <el-input v-model="scope.row.work_theme" size="small" />
            </div>
            <div v-else @dblclick="enableEditing(scope.row)">
              {{ scope.row.work_theme }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="supervision_source" label="督办来源" width="120">
          <template #default="scope">
            <div v-if="scope.row.isEditing">
              <el-input v-model="scope.row.supervision_source" size="small" />
            </div>
            <div v-else @dblclick="enableEditing(scope.row)">
              {{ scope.row.supervision_source }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="completion_deadline" label="完成时限" width="120">
          <template #default="scope">
            <div v-if="scope.row.isEditing">
              <el-date-picker
                v-model="scope.row.completion_deadline"
                type="date"
                size="small"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="选择日期"
              />
            </div>
            <div v-else @dblclick="enableEditing(scope.row)">
              {{ formatDate(scope.row.completion_deadline) }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="overall_progress" label="整体进度" width="100">
          <template #default="scope">
            <div v-if="scope.row.isEditing">
              <el-select v-model="scope.row.overall_progress" size="small">
                <el-option label="X 未启动" value="X 未启动" />
                <el-option label="O 进行中" value="O 进行中" />
                <el-option label="！ 延期" value="！ 延期" />
                <el-option label="√ 已完成" value="√ 已完成" />
              </el-select>
            </div>
            <div v-else @dblclick="enableEditing(scope.row)">
              <el-tag :type="getProgressTagType(scope.row.overall_progress)" size="small">
                {{ scope.row.overall_progress }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <!-- 动态公司状态列 -->
        <el-table-column
          v-for="company in visibleCompanies"
          :key="company"
          :label="company"
          width="80"
          align="center"
        >
          <template #default="scope">
            <div
              class="status-cell"
              :class="getStatusClass(scope.row[`${company}_status`])"
              @dblclick="openProgressDialog(scope.row, company)"
              :title="`双击编辑详细进度`"
            >
              {{ scope.row[`${company}_status`] || 'X' }}
            </div>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <div v-if="scope.row.isEditing" class="compact-buttons">
              <el-button size="small" type="primary" @click="saveItem(scope.row)">保存</el-button>
              <el-button size="small" @click="cancelEdit(scope.row)">取消</el-button>
            </div>
            <div v-else>
              <el-button size="small" type="danger" @click="confirmDelete(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 进度详情编辑对话框 -->
    <el-dialog
      v-model="progressDialogVisible"
      :title="`${currentItem?.work_theme} - ${currentCompany}公司进度详情`"
      width="600px"
    >
      <el-form :model="progressForm" label-width="120px">
        <el-form-item label="状态">
          <el-select v-model="progressForm.status">
            <el-option label="X 未启动" value="X" />
            <el-option label="O 进行中" value="O" />
            <el-option label="！ 延期" value="！" />
            <el-option label="√ 已完成" value="√" />
            <el-option label="— 不适用" value="—" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="项目进度情况">
          <el-input
            v-model="progressForm.progress_description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述当前项目的进度情况、完成程度、关键节点等..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="存在问题">
          <el-input
            v-model="progressForm.existing_problems"
            type="textarea"
            :rows="3"
            placeholder="请描述项目执行过程中遇到的问题和困难..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="下一步计划">
          <el-input
            v-model="progressForm.next_plan"
            type="textarea"
            :rows="3"
            placeholder="请描述下一步的工作计划和安排..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="progressDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveProgress" :loading="saving">保存</el-button>
      </template>
    </el-dialog>

    <!-- 公司列管理组件 -->
    <CompanyManager
      v-model="companyManagerVisible"
      @refresh="handleCompanyRefresh"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import request from '@/utils/request'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import CompanyManager from '@/components/CompanyManager.vue'

// 数据和状态
const loading = ref(false)
const saving = ref(false)
const exporting = ref(false)
const importing = ref(false)
const items = ref([])
const originalItems = ref([])
const searchText = ref('')
const tableKey = ref(0)  // 用于强制重新渲染表格

// 上传配置
const uploadRef = ref()

// 对话框状态
const progressDialogVisible = ref(false)
const companyManagerVisible = ref(false)

// 当前编辑的项目和公司
const currentItem = ref(null)
const currentCompany = ref('')

// 进度表单
const progressForm = reactive({
  status: 'X',
  progress_description: '',
  existing_problems: '',
  next_plan: ''
})

// 公司列表配置
const allCompanies = ref([])
const visibleCompanies = ref([])

// 计算属性
const filteredItems = computed(() => {
  if (!searchText.value) return items.value
  
  const keyword = searchText.value.toLowerCase()
  return items.value.filter(item => 
    item.work_theme?.toLowerCase().includes(keyword) ||
    item.work_dimension?.toLowerCase().includes(keyword) ||
    item.supervision_source?.toLowerCase().includes(keyword)
  )
})

// 方法
const loadCompanies = async () => {
  try {
    const response = await request({
      url: '/supervision/companies',
      method: 'get'
    })

    if (response.success) {
      const companies = response.data.map(item => item.company_name)
      allCompanies.value = companies

      // 更新可见公司列表，保留用户的选择但添加新公司
      const savedColumns = localStorage.getItem('supervision-visible-companies')
      if (savedColumns) {
        const savedCompanies = JSON.parse(savedColumns)
        // 合并保存的公司和新的公司，去重
        const mergedCompanies = [...new Set([...savedCompanies, ...companies])]
        // 只保留实际存在的公司
        visibleCompanies.value = mergedCompanies.filter(company => companies.includes(company))
      } else {
        // 如果没有保存的设置，显示所有公司
        visibleCompanies.value = [...companies]
      }

      // 更新本地存储
      localStorage.setItem('supervision-visible-companies', JSON.stringify(visibleCompanies.value))
    }
  } catch (error) {
    console.error('获取公司列表失败:', error)
  }
}

const refreshData = async () => {
  loading.value = true
  try {
    // 先加载公司列表
    await loadCompanies()

    const response = await request({
      url: '/supervision/items',
      method: 'get'
    })
    
    if (response.success) {
      items.value = response.data.map(item => ({
        ...item,
        isEditing: false,
        isNew: false
      }))
      originalItems.value = JSON.parse(JSON.stringify(items.value))
    } else {
      ElMessage.error('获取数据失败: ' + response.message)
    }
  } catch (error) {
    console.error('获取督办数据失败:', error)
    ElMessage.error('获取督办数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const enableEditing = (row) => {
  // 保存原始数据
  const originalIndex = originalItems.value.findIndex(item => item.id === row.id)
  if (originalIndex === -1) {
    originalItems.value.push(JSON.parse(JSON.stringify(row)))
  }
  
  row.isEditing = true
}

const cancelEdit = (row) => {
  if (row.isNew) {
    items.value = items.value.filter(item => item !== row)
  } else {
    const originalItem = originalItems.value.find(item => item.id === row.id)
    if (originalItem) {
      Object.assign(row, JSON.parse(JSON.stringify(originalItem)))
    }
    row.isEditing = false
  }
}

const saveItem = async (row) => {
  try {
    saving.value = true
    
    const url = row.isNew ? '/supervision/items' : `/supervision/items/${row.id}`
    const method = row.isNew ? 'post' : 'put'
    
    const response = await request({
      url,
      method,
      data: row
    })
    
    if (response.success) {
      ElMessage.success(row.isNew ? '新增成功' : '更新成功')
      row.isEditing = false
      row.isNew = false
      
      // 更新原始数据
      const index = originalItems.value.findIndex(item => item.id === row.id)
      if (index !== -1) {
        originalItems.value[index] = JSON.parse(JSON.stringify(row))
      } else {
        originalItems.value.push(JSON.parse(JSON.stringify(row)))
      }
      
      if (row.isNew) {
        await refreshData()
      }
    } else {
      ElMessage.error('保存失败: ' + response.message)
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

const addNewItem = () => {
  const newItem = {
    id: null,
    sequence_number: items.value.length + 1,
    work_dimension: '',
    work_theme: '',
    supervision_source: '',
    work_content: '',
    is_annual_assessment: '否',
    completion_deadline: '',
    overall_progress: 'X 未启动',
    isEditing: true,
    isNew: true
  }
  
  // 为所有公司添加默认状态
  allCompanies.value.forEach(company => {
    newItem[`${company}_status`] = 'X'
  })
  
  items.value.unshift(newItem)
}

const confirmDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除督办事项"${row.work_theme}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await request({
      url: `/supervision/items/${row.id}`,
      method: 'delete'
    })
    
    if (response.success) {
      ElMessage.success('删除成功')
      await refreshData()
    } else {
      ElMessage.error('删除失败: ' + response.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}


const openProgressDialog = async (item, company) => {
  currentItem.value = item
  currentCompany.value = company
  
  // 重置表单
  progressForm.status = item[`${company}_status`] || 'X'
  progressForm.progress_description = ''
  progressForm.existing_problems = ''
  progressForm.next_plan = ''
  
  try {
    // 获取现有的进度详情
    const response = await request({
      url: `/supervision/progress-detail/${item.work_theme}/${company}`,
      method: 'get'
    })
    
    if (response.success && response.data) {
      progressForm.progress_description = response.data.progress_description || ''
      progressForm.existing_problems = response.data.existing_problems || ''
      progressForm.next_plan = response.data.next_plan || ''
    }
  } catch (error) {
    console.error('获取进度详情失败:', error)
  }
  
  progressDialogVisible.value = true
}

const saveProgress = async () => {
  try {
    saving.value = true

    // 使用新的进度更新API，一次性更新状态和详细信息
    await request({
      url: '/supervision/progress',
      method: 'put',
      data: {
        work_theme: currentItem.value.work_theme,
        company_name: currentCompany.value,
        status: progressForm.status,
        progress_description: progressForm.progress_description,
        existing_problems: progressForm.existing_problems,
        next_plan: progressForm.next_plan
      }
    })

    // 更新本地数据
    currentItem.value[`${currentCompany.value}_status`] = progressForm.status

    // 刷新数据以获取最新的整体进度
    await refreshData()

    ElMessage.success('保存成功')
    progressDialogVisible.value = false
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

const showColumnManager = () => {
  companyManagerVisible.value = true
}

const handleCompanyRefresh = async () => {
  // 强制重新加载公司列表和数据
  loading.value = true

  try {
    // 清除本地缓存，强制从后端重新获取
    localStorage.removeItem('supervision-visible-companies')

    // 重新加载公司列表
    await loadCompanies()

    // 重新加载数据
    const response = await request({
      url: '/supervision/items',
      method: 'get'
    })

    if (response.success) {
      items.value = response.data.map(item => ({
        ...item,
        isEditing: false,
        isNew: false
      }))
      originalItems.value = JSON.parse(JSON.stringify(items.value))

      // 强制重新渲染表格
      tableKey.value += 1

      ElMessage.success('页面已刷新，公司列已更新')
    } else {
      ElMessage.error('刷新数据失败: ' + response.message)
    }
  } catch (error) {
    console.error('刷新失败:', error)
    ElMessage.error('刷新失败，请重试')
  } finally {
    loading.value = false
  }
}

// 导出Excel
const exportToExcel = async () => {
  try {
    exporting.value = true

    // 创建工作表数据
    const worksheet = XLSX.utils.json_to_sheet(
      items.value.map(item => {
        const row = {
          '序号': item.sequence_number,
          '工作维度': item.work_dimension,
          '工作主题': item.work_theme,
          '督办来源': item.supervision_source,
          '工作内容': item.work_content,
          '是否年度考核': item.is_annual_assessment,
          '完成时限': item.completion_deadline,
          '整体进度': item.overall_progress
        }

        // 添加公司状态列
        visibleCompanies.value.forEach(company => {
          row[company] = item[`${company}_status`] || 'X'
        })

        return row
      })
    )

    // 创建工作簿
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, '督办事项')

    // 生成Excel文件
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
    const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })

    // 下载文件
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_')
    const fileName = `督办事项_${timestamp}.xlsx`
    saveAs(data, fileName)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败: ' + error.message)
  } finally {
    exporting.value = false
  }
}

// 处理文件选择
const handleFileChange = async (file) => {
  if (!file.raw) return

  const isExcel = file.raw.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.raw.type === 'application/vnd.ms-excel' ||
                  file.name.endsWith('.xlsx') ||
                  file.name.endsWith('.xls')

  if (!isExcel) {
    ElMessage.error('只能上传Excel文件(.xlsx或.xls)')
    return
  }

  const isLt10M = file.raw.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB')
    return
  }

  try {
    importing.value = true

    // 读取Excel文件
    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        const data = new Uint8Array(e.target.result)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)

        if (!jsonData || jsonData.length === 0) {
          ElMessage.error('Excel文件为空或格式不正确')
          return
        }

        // 转换数据格式
        const importData = jsonData.map((row, index) => {
          const item = {
            sequence_number: row['序号'] || index + 1,
            work_dimension: row['工作维度'] || '',
            work_theme: row['工作主题'] || '',
            supervision_source: row['督办来源'] || '',
            work_content: row['工作内容'] || '',
            is_annual_assessment: row['是否年度考核'] || '否',
            completion_deadline: row['完成时限'] || '',
            overall_progress: row['整体进度'] || 'X 未启动'
          }

          // 动态添加公司状态字段
          allCompanies.value.forEach(company => {
            item[`${company}_status`] = row[company] || 'X'
          })

          return item
        })

        // 调用后端API导入数据
        await importDataToBackend(importData)

      } catch (error) {
        console.error('解析Excel文件失败:', error)
        ElMessage.error('解析Excel文件失败: ' + error.message)
      } finally {
        importing.value = false
      }
    }

    reader.readAsArrayBuffer(file.raw)

  } catch (error) {
    importing.value = false
    console.error('读取文件失败:', error)
    ElMessage.error('读取文件失败: ' + error.message)
  }
}

// 导入数据到后端
const importDataToBackend = async (importData) => {
  try {
    const response = await request({
      url: '/supervision/import-data',
      method: 'post',
      data: { items: importData }
    })

    if (response.success) {
      ElMessage.success(`导入成功，共导入 ${importData.length} 条数据`)
      await refreshData() // 刷新数据
    } else {
      ElMessage.error(response.message || '导入失败')
    }
  } catch (error) {
    console.error('导入数据失败:', error)
    ElMessage.error('导入数据失败: ' + error.message)
  }
}

// 工具方法
const getProgressTagType = (progress) => {
  if (progress?.includes('已完成')) return 'success'
  if (progress?.includes('进行中')) return 'primary'
  if (progress?.includes('延期')) return 'danger'
  return 'info'
}

const getStatusClass = (status) => {
  switch (status) {
    case '√': return 'status-completed'
    case 'O': return 'status-progress'
    case '！': return 'status-delayed'
    case 'X': return 'status-not-started'
    case '—': return 'status-not-applicable'
    default: return 'status-not-started'
  }
}

// 格式化日期显示
const formatDate = (dateStr) => {
  if (!dateStr) return ''

  // 如果已经是YYYY-MM-DD格式，直接返回
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
    return dateStr
  }

  // 如果是YYYY-MM-DD HH:mm:ss格式，只取日期部分
  if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(dateStr)) {
    return dateStr.split(' ')[0]
  }

  // 尝试解析其他格式
  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) return dateStr

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  } catch (error) {
    return dateStr
  }
}

// 生命周期
onMounted(async () => {
  // 先加载公司列表（内部会处理本地存储的列设置），再加载数据
  await loadCompanies()
  refreshData()
})
</script>

<style scoped>
.simple-supervision {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.supervision-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.search-bar {
  margin-bottom: 16px;
}

.status-cell {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  margin: 0 auto;
  transition: all 0.3s;
}

.status-cell:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.status-completed {
  background-color: #67c23a;
  color: white;
}

.status-progress {
  background-color: #409eff;
  color: white;
}

.status-delayed {
  background-color: #f56c6c;
  color: white;
}

.status-not-started {
  background-color: #909399;
  color: white;
}

.status-not-applicable {
  background-color: #e6a23c;
  color: white;
}

.compact-buttons {
  display: flex;
  gap: 4px;
}

.column-manager {
  padding: 16px 0;
}

.column-manager p {
  margin-bottom: 16px;
  font-weight: bold;
}

.header-actions .el-upload {
  display: inline-block;
}

.header-actions .el-upload .el-button {
  margin-left: 0;
}
</style>
