.actions-menu {bottom: 12px;}
.maxh-72{max-height:18rem;}
.actions-menu.no-divider .divider{display: none;}

.flex-col {flex-direction: column;}
.chart > div {margin: auto;}
.pie-chart {position: relative;}
.pie-chart .pie-chart-title {position: relative; top: -60%; left: 2%; height: 1rem;}

.member-list > .center-y {width: 12.5%}
.memberBox .panel-heading, .otherInfoBox .panel-heading{background-color: var(--color-gray-100); color: var(--color-gray-900)}
#mainContainer{margin-bottom: 60px;}

#mainContent .desc-box {max-height: 6rem;}
