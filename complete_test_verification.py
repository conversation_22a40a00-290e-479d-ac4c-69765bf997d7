#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的测试验证脚本 - 验证项目ID和项目名称字段修复
"""

import requests
import json
from datetime import datetime

# API配置
BASE_URL = "http://localhost:8000"

def get_auth_token():
    """获取认证token（模拟登录）"""
    # 这里应该使用真实的登录凭据
    login_data = {
        "username": "test_user",  # 替换为真实用户名
        "password": "test_pass"   # 替换为真实密码
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/v1/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            return data.get("access_token")
    except:
        pass
    
    return None

def test_api_with_auth(token=None):
    """使用认证测试API"""
    print("🔐 使用认证测试API")
    print("=" * 60)
    
    headers = {'Content-Type': 'application/json'}
    if token:
        headers['Authorization'] = f'Bearer {token}'
        print("✅ 使用认证token")
    else:
        print("⚠️ 未使用认证token")
    
    # 测试三种状态的API
    test_cases = [
        {"status": "all", "name": "全部工单"},
        {"status": "completed", "name": "已完成工单"},
        {"status": "urgent", "name": "紧急工单"}
    ]
    
    results = {}
    
    for case in test_cases:
        status = case["status"]
        name = case["name"]
        
        print(f"\n📋 测试 {name}...")
        
        try:
            url = f"{BASE_URL}/api/v1/ticket-integration/tickets/by-status"
            params = {"status": status, "limit": 3}
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            
            print(f"📡 HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    if data.get("success") and "data" in data and "tickets" in data["data"]:
                        tickets = data["data"]["tickets"]
                        print(f"🎫 工单数量: {len(tickets)}")
                        
                        if tickets:
                            # 检查项目字段
                            project_id_count = 0
                            project_name_count = 0
                            
                            for i, ticket in enumerate(tickets):
                                print(f"\n   工单 {i+1}:")
                                print(f"     编号: {ticket.get('feelec_ticket_no', 'N/A')}")
                                print(f"     标题: {ticket.get('feelec_title', 'N/A')[:30]}...")
                                
                                # 检查项目ID
                                project_id = ticket.get('feelec_project_id')
                                if project_id and project_id not in [None, '', 0]:
                                    print(f"     项目ID: ✅ {project_id}")
                                    project_id_count += 1
                                else:
                                    print(f"     项目ID: ❌ 空或无效 ({project_id})")
                                
                                # 检查项目名称
                                project_name = ticket.get('project_name')
                                if project_name and project_name not in [None, '']:
                                    print(f"     项目名称: ✅ {project_name}")
                                    project_name_count += 1
                                else:
                                    print(f"     项目名称: ❌ 空 ({project_name})")
                                
                                print(f"     状态: {ticket.get('status_name', 'N/A')}")
                                print(f"     优先级: {ticket.get('priority_text', 'N/A')}")
                            
                            # 统计结果
                            total_tickets = len(tickets)
                            print(f"\n   📊 项目字段统计:")
                            print(f"     有项目ID的工单: {project_id_count}/{total_tickets}")
                            print(f"     有项目名称的工单: {project_name_count}/{total_tickets}")
                            
                            results[status] = {
                                "success": True,
                                "total": total_tickets,
                                "project_id_count": project_id_count,
                                "project_name_count": project_name_count,
                                "sample_ticket": tickets[0] if tickets else None
                            }
                        else:
                            print("   没有工单数据")
                            results[status] = {"success": True, "total": 0}
                    else:
                        print("❌ 数据格式不正确")
                        print(f"   响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                        results[status] = {"success": False, "error": "数据格式错误"}
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    results[status] = {"success": False, "error": "JSON解析失败"}
                    
            elif response.status_code == 401:
                print("🔐 需要认证")
                results[status] = {"success": False, "error": "需要认证"}
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"   响应: {response.text[:200]}...")
                results[status] = {"success": False, "error": f"HTTP {response.status_code}"}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误: {str(e)}")
            results[status] = {"success": False, "error": f"网络错误: {str(e)}"}
        except Exception as e:
            print(f"❌ 未知错误: {str(e)}")
            results[status] = {"success": False, "error": f"未知错误: {str(e)}"}
    
    return results

def compare_with_project_tickets():
    """对比项目工单列表API"""
    print(f"\n" + "=" * 60)
    print("🔍 对比项目工单列表API")
    print("=" * 60)
    
    # 这里可以添加对比项目工单列表API的逻辑
    print("💡 建议手动对比:")
    print("1. 在前端点击'总项目数' -> '项目清单' -> 选择一个项目")
    print("2. 查看项目工单列表中的项目ID和项目名称字段")
    print("3. 然后点击'总工单数量'，对比字段显示是否一致")

def generate_test_report(results):
    """生成测试报告"""
    print(f"\n" + "=" * 60)
    print("📊 测试报告")
    print("=" * 60)
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results.values() if r.get("success"))
    
    print(f"📈 总体情况:")
    print(f"   测试API数量: {total_tests}")
    print(f"   成功响应数量: {successful_tests}")
    print(f"   成功率: {successful_tests/total_tests*100:.1f}%")
    
    for status, result in results.items():
        print(f"\n🔍 {status} 状态:")
        if result.get("success"):
            total = result.get("total", 0)
            project_id_count = result.get("project_id_count", 0)
            project_name_count = result.get("project_name_count", 0)
            
            print(f"   ✅ API响应成功")
            print(f"   📊 工单总数: {total}")
            if total > 0:
                print(f"   🏷️  项目ID覆盖率: {project_id_count}/{total} ({project_id_count/total*100:.1f}%)")
                print(f"   📝 项目名称覆盖率: {project_name_count}/{total} ({project_name_count/total*100:.1f}%)")
                
                if project_id_count == project_name_count == total:
                    print(f"   🎉 所有工单都有完整的项目信息！")
                elif project_id_count > 0 or project_name_count > 0:
                    print(f"   ⚠️  部分工单缺少项目信息（可能是业务正常情况）")
                else:
                    print(f"   ❌ 所有工单都缺少项目信息！")
        else:
            print(f"   ❌ API响应失败: {result.get('error', '未知错误')}")
    
    print(f"\n💡 建议:")
    if successful_tests == total_tests:
        print("✅ 后端API修复成功！")
        print("🚀 现在可以在前端测试完整功能")
        print("📝 测试步骤:")
        print("   1. 刷新前端页面")
        print("   2. 点击统计卡片查看工单列表")
        print("   3. 检查项目ID和项目名称字段显示")
        print("   4. 测试Excel导出功能")
    else:
        print("❌ 部分API仍有问题，需要进一步检查")

def main():
    """主函数"""
    print("🚀 开始完整的测试验证")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 尝试获取认证token
    token = get_auth_token()
    
    # 测试API
    results = test_api_with_auth(token)
    
    # 对比分析
    compare_with_project_tickets()
    
    # 生成报告
    generate_test_report(results)
    
    return results

if __name__ == "__main__":
    results = main()
    
    # 根据结果设置退出码
    success_count = sum(1 for r in results.values() if r.get("success"))
    if success_count == len(results):
        print(f"\n🎉 所有测试通过！")
        exit(0)
    else:
        print(f"\n⚠️  部分测试失败")
        exit(1)
