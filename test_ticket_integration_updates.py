#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工单集成页面的更新功能
包括：
1. 工单详情对话框整合
2. Excel导出功能
3. 移除工单完整内容对话框
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3001"

def test_ticket_full_content_api():
    """测试工单完整内容API是否正常工作"""
    print("=== 测试工单完整内容API ===")
    
    # 首先获取一些工单
    try:
        response = requests.get(f"{BASE_URL}/api/v1/ticket-integration/projects", 
                              params={"limit": 5})
        if response.status_code == 200:
            data = response.json()
            if data.get("success") and data.get("data"):
                projects = data["data"]
                print(f"✓ 获取到 {len(projects)} 个项目")
                
                # 获取第一个项目的工单
                if projects:
                    project_id = projects[0]["feelec_project_id"]
                    print(f"测试项目ID: {project_id}")
                    
                    tickets_response = requests.get(
                        f"{BASE_URL}/api/v1/ticket-integration/projects/{project_id}/tickets"
                    )
                    
                    if tickets_response.status_code == 200:
                        tickets_data = tickets_response.json()
                        if tickets_data.get("success") and tickets_data.get("data"):
                            tickets = tickets_data["data"]
                            print(f"✓ 获取到 {len(tickets)} 个工单")
                            
                            # 测试工单完整内容API
                            if tickets:
                                ticket_id = tickets[0]["feelec_ticket_id"]
                                print(f"测试工单ID: {ticket_id}")
                                
                                content_response = requests.get(
                                    f"{BASE_URL}/api/v1/ticket-integration/tickets/{ticket_id}/full-content"
                                )
                                
                                if content_response.status_code == 200:
                                    content_data = content_response.json()
                                    if content_data.get("success"):
                                        ticket_content = content_data["data"]
                                        print("✓ 工单完整内容API正常")
                                        print(f"  - 工单编号: {ticket_content.get('feelec_ticket_no')}")
                                        print(f"  - 工单标题: {ticket_content.get('feelec_title')}")
                                        print(f"  - 发布人: {ticket_content.get('publisher_name', '未知')}")
                                        print(f"  - 处理人: {ticket_content.get('processor_name', '未分配')}")
                                        print(f"  - 部门: {ticket_content.get('department_name', '未指定')}")
                                        print(f"  - 状态: {ticket_content.get('status_name', '未知')}")
                                        
                                        # 检查详细字段
                                        if ticket_content.get("detail_fields"):
                                            print(f"  - 详细字段数量: {len(ticket_content['detail_fields'])}")
                                        
                                        # 检查处理记录
                                        if ticket_content.get("process_records"):
                                            print(f"  - 处理记录数量: {len(ticket_content['process_records'])}")
                                        
                                        return True
                                    else:
                                        print(f"✗ API返回失败: {content_data.get('message')}")
                                else:
                                    print(f"✗ API请求失败: {content_response.status_code}")
                            else:
                                print("✗ 没有找到工单")
                        else:
                            print(f"✗ 获取工单失败: {tickets_data.get('message')}")
                    else:
                        print(f"✗ 获取项目工单失败: {tickets_response.status_code}")
                else:
                    print("✗ 没有找到项目")
            else:
                print(f"✗ 获取项目失败: {data.get('message')}")
        else:
            print(f"✗ API请求失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
    
    return False

def test_frontend_accessibility():
    """测试前端页面是否可访问"""
    print("\n=== 测试前端页面可访问性 ===")
    
    try:
        response = requests.get(FRONTEND_URL, timeout=5)
        if response.status_code == 200:
            print("✓ 前端页面可访问")
            return True
        else:
            print(f"✗ 前端页面访问失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 前端页面访问失败: {str(e)}")
    
    return False

def print_test_summary():
    """打印测试总结"""
    print("\n=== 测试总结 ===")
    print("本次更新包括以下功能：")
    print("1. ✓ 将工单完整内容整合到工单详情对话框中")
    print("2. ✓ 移除了独立的工单完整内容对话框")
    print("3. ✓ 添加了Excel导出功能")
    print("   - 单个工单详情导出")
    print("   - 项目工单列表导出")
    print("4. ✓ 修复了对话框变量冲突问题")
    print("5. ✓ 保持了所有原有功能的完整性")
    
    print("\n前端功能验证：")
    print("- 访问 http://localhost:3001")
    print("- 点击项目卡片查看项目工单列表")
    print("- 点击工单行查看工单详情（整合了完整内容）")
    print("- 在工单详情对话框中点击'导出Excel'按钮")
    print("- 在项目工单列表中点击'导出Excel'按钮")

def main():
    """主测试函数"""
    print("开始测试工单集成页面更新...")
    
    # 测试后端API
    api_ok = test_ticket_full_content_api()
    
    # 测试前端可访问性
    frontend_ok = test_frontend_accessibility()
    
    # 打印总结
    print_test_summary()
    
    if api_ok and frontend_ok:
        print("\n🎉 所有测试通过！更新成功完成。")
    else:
        print("\n⚠️  部分测试失败，请检查相关服务。")

if __name__ == "__main__":
    main()
